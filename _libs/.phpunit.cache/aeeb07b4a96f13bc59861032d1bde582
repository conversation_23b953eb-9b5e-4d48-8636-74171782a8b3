a:6:{s:9:"classesIn";a:1:{s:31:"Nzoom\Export\Entity\ExportValue";a:6:{s:4:"name";s:11:"ExportValue";s:14:"namespacedName";s:31:"Nzoom\Export\Entity\ExportValue";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:10;s:7:"endLine";i:255;s:7:"methods";a:12:{s:13:"getValidTypes";a:6:{s:10:"methodName";s:13:"getValidTypes";s:9:"signature";s:22:"getValidTypes(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:58;s:7:"endLine";i:61;s:3:"ccn";i:1;}s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:51:"__construct($value, ?string $type, ?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:70;s:7:"endLine";i:75;s:3:"ccn";i:1;}s:8:"getValue";a:6:{s:10:"methodName";s:8:"getValue";s:9:"signature";s:10:"getValue()";s:10:"visibility";s:6:"public";s:9:"startLine";i:82;s:7:"endLine";i:85;s:3:"ccn";i:1;}s:8:"setValue";a:6:{s:10:"methodName";s:8:"setValue";s:9:"signature";s:22:"setValue($value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:92;s:7:"endLine";i:95;s:3:"ccn";i:1;}s:7:"getType";a:6:{s:10:"methodName";s:7:"getType";s:9:"signature";s:18:"getType(): ?string";s:10:"visibility";s:6:"public";s:9:"startLine";i:102;s:7:"endLine";i:105;s:3:"ccn";i:1;}s:7:"setType";a:6:{s:10:"methodName";s:7:"setType";s:9:"signature";s:28:"setType(?string $type): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:113;s:7:"endLine";i:124;s:3:"ccn";i:3;}s:9:"getFormat";a:6:{s:10:"methodName";s:9:"getFormat";s:9:"signature";s:20:"getFormat(): ?string";s:10:"visibility";s:6:"public";s:9:"startLine";i:131;s:7:"endLine";i:134;s:3:"ccn";i:1;}s:9:"setFormat";a:6:{s:10:"methodName";s:9:"setFormat";s:9:"signature";s:26:"setFormat(?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:142;s:7:"endLine";i:146;s:3:"ccn";i:1;}s:6:"isNull";a:6:{s:10:"methodName";s:6:"isNull";s:9:"signature";s:14:"isNull(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:153;s:7:"endLine";i:156;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:16:"validate(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:163;s:7:"endLine";i:189;s:3:"ccn";i:27;}s:17:"getFormattedValue";a:6:{s:10:"methodName";s:17:"getFormattedValue";s:9:"signature";s:19:"getFormattedValue()";s:10:"visibility";s:6:"public";s:9:"startLine";i:196;s:7:"endLine";i:226;s:3:"ccn";i:15;}s:10:"__toString";a:6:{s:10:"methodName";s:10:"__toString";s:9:"signature";s:12:"__toString()";s:10:"visibility";s:6:"public";s:9:"startLine";i:233;s:7:"endLine";i:254;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:256;s:18:"commentLinesOfCode";i:86;s:21:"nonCommentLinesOfCode";i:170;}s:15:"ignoredLinesFor";a:1:{i:0;i:10;}s:17:"executableLinesIn";a:70:{i:60;i:12;i:72;i:13;i:73;i:14;i:74;i:15;i:84;i:16;i:94;i:17;i:104;i:18;i:115;i:19;i:120;i:21;i:116;i:21;i:117;i:21;i:118;i:21;i:119;i:21;i:123;i:22;i:133;i:23;i:144;i:24;i:145;i:25;i:155;i:26;i:165;i:27;i:166;i:28;i:169;i:29;i:170;i:30;i:171;i:31;i:172;i:32;i:173;i:33;i:174;i:34;i:175;i:35;i:176;i:36;i:177;i:37;i:178;i:37;i:179;i:37;i:180;i:38;i:181;i:39;i:182;i:40;i:183;i:40;i:184;i:41;i:185;i:42;i:187;i:43;i:198;i:44;i:199;i:45;i:202;i:46;i:203;i:47;i:206;i:48;i:207;i:49;i:208;i:50;i:209;i:51;i:210;i:52;i:211;i:53;i:212;i:54;i:214;i:55;i:215;i:56;i:216;i:57;i:217;i:58;i:218;i:59;i:219;i:60;i:220;i:61;i:222;i:62;i:225;i:63;i:235;i:64;i:236;i:65;i:239;i:66;i:240;i:67;i:243;i:68;i:244;i:69;i:245;i:70;i:246;i:71;i:247;i:72;i:248;i:73;i:250;i:74;i:253;i:75;}}