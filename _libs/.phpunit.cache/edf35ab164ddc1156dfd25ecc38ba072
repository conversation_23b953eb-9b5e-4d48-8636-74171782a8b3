a:6:{s:9:"classesIn";a:1:{s:35:"Nzoom\Export\Streamer\StreamHeaders";a:6:{s:4:"name";s:13:"StreamHeaders";s:14:"namespacedName";s:35:"Nzoom\Export\Streamer\StreamHeaders";s:9:"namespace";s:21:"Nzoom\Export\Streamer";s:9:"startLine";i:11;s:7:"endLine";i:245;s:7:"methods";a:14:{s:9:"addHeader";a:6:{s:10:"methodName";s:9:"addHeader";s:9:"signature";s:44:"addHeader(string $name, string $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:25;s:7:"endLine";i:28;s:3:"ccn";i:1;}s:10:"setHeaders";a:6:{s:10:"methodName";s:10:"setHeaders";s:9:"signature";s:32:"setHeaders(array $headers): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:36;s:7:"endLine";i:39;s:3:"ccn";i:1;}s:9:"getHeader";a:6:{s:10:"methodName";s:9:"getHeader";s:9:"signature";s:32:"getHeader(string $name): ?string";s:10:"visibility";s:6:"public";s:9:"startLine";i:47;s:7:"endLine";i:50;s:3:"ccn";i:1;}s:6:"getAll";a:6:{s:10:"methodName";s:6:"getAll";s:9:"signature";s:15:"getAll(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:57;s:7:"endLine";i:60;s:3:"ccn";i:1;}s:5:"clear";a:6:{s:10:"methodName";s:5:"clear";s:9:"signature";s:13:"clear(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:67;s:7:"endLine";i:70;s:3:"ccn";i:1;}s:9:"hasHeader";a:6:{s:10:"methodName";s:9:"hasHeader";s:9:"signature";s:29:"hasHeader(string $name): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:78;s:7:"endLine";i:81;s:3:"ccn";i:1;}s:12:"removeHeader";a:6:{s:10:"methodName";s:12:"removeHeader";s:9:"signature";s:32:"removeHeader(string $name): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:89;s:7:"endLine";i:92;s:3:"ccn";i:1;}s:21:"setFileContentHeaders";a:6:{s:10:"methodName";s:21:"setFileContentHeaders";s:9:"signature";s:63:"setFileContentHeaders(string $mimeType, string $filename): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:101;s:7:"endLine";i:109;s:3:"ccn";i:1;}s:16:"sanitizeFilename";a:6:{s:10:"methodName";s:16:"sanitizeFilename";s:9:"signature";s:42:"sanitizeFilename(string $filename): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:117;s:7:"endLine";i:128;s:3:"ccn";i:2;}s:19:"prepareCacheHeaders";a:6:{s:10:"methodName";s:19:"prepareCacheHeaders";s:9:"signature";s:79:"prepareCacheHeaders(int $cacheExpires, ?string $etag, ?int $lastModified): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:138;s:7:"endLine";i:161;s:3:"ccn";i:4;}s:19:"sendPreparedHeaders";a:6:{s:10:"methodName";s:19:"sendPreparedHeaders";s:9:"signature";s:27:"sendPreparedHeaders(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:168;s:7:"endLine";i:182;s:3:"ccn";i:3;}s:12:"flushHeaders";a:6:{s:10:"methodName";s:12:"flushHeaders";s:9:"signature";s:20:"flushHeaders(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:189;s:7:"endLine";i:194;s:3:"ccn";i:2;}s:18:"send304NotModified";a:6:{s:10:"methodName";s:18:"send304NotModified";s:9:"signature";s:78:"send304NotModified(int $cacheExpires, ?string $etag, ?int $lastModified): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:204;s:7:"endLine";i:213;s:3:"ccn";i:1;}s:21:"handleCacheValidation";a:6:{s:10:"methodName";s:21:"handleCacheValidation";s:9:"signature";s:81:"handleCacheValidation(int $cacheExpires, ?string $etag, ?int $lastModified): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:223;s:7:"endLine";i:244;s:3:"ccn";i:7;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:246;s:18:"commentLinesOfCode";i:112;s:21:"nonCommentLinesOfCode";i:134;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:46:{i:27;i:2;i:38;i:3;i:49;i:4;i:59;i:5;i:69;i:6;i:80;i:7;i:91;i:8;i:104;i:9;i:107;i:10;i:108;i:11;i:120;i:12;i:123;i:13;i:124;i:14;i:127;i:15;i:140;i:16;i:142;i:17;i:143;i:18;i:144;i:19;i:147;i:20;i:148;i:21;i:149;i:22;i:153;i:23;i:154;i:24;i:158;i:25;i:159;i:26;i:171;i:27;i:172;i:28;i:176;i:29;i:177;i:30;i:181;i:31;i:191;i:32;i:192;i:33;i:206;i:34;i:209;i:35;i:212;i:36;i:226;i:37;i:227;i:38;i:228;i:39;i:229;i:40;i:230;i:41;i:235;i:42;i:236;i:43;i:237;i:44;i:238;i:45;i:239;i:46;i:243;i:47;}}