a:6:{s:9:"classesIn";a:1:{s:32:"Nzoom\Export\Entity\ExportHeader";a:6:{s:4:"name";s:12:"ExportHeader";s:14:"namespacedName";s:32:"Nzoom\Export\Entity\ExportHeader";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:11;s:7:"endLine";i:333;s:7:"methods";a:22:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:51:"__construct(string $backgroundColor, array $styles)";s:10:"visibility";s:6:"public";s:9:"startLine";i:44;s:7:"endLine";i:49;s:3:"ccn";i:1;}s:9:"addColumn";a:6:{s:10:"methodName";s:9:"addColumn";s:9:"signature";s:51:"addColumn(Nzoom\Export\Entity\ExportColumn $column)";s:10:"visibility";s:6:"public";s:9:"startLine";i:57;s:7:"endLine";i:71;s:3:"ccn";i:2;}s:9:"hasColumn";a:6:{s:10:"methodName";s:9:"hasColumn";s:9:"signature";s:32:"hasColumn(string $varName): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:79;s:7:"endLine";i:82;s:3:"ccn";i:1;}s:10:"getColumns";a:6:{s:10:"methodName";s:10:"getColumns";s:9:"signature";s:19:"getColumns(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:89;s:7:"endLine";i:92;s:3:"ccn";i:1;}s:11:"getColumnAt";a:6:{s:10:"methodName";s:11:"getColumnAt";s:9:"signature";s:58:"getColumnAt(int $index): ?Nzoom\Export\Entity\ExportColumn";s:10:"visibility";s:6:"public";s:9:"startLine";i:100;s:7:"endLine";i:103;s:3:"ccn";i:1;}s:18:"getColumnByVarName";a:6:{s:10:"methodName";s:18:"getColumnByVarName";s:9:"signature";s:70:"getColumnByVarName(string $varName): ?Nzoom\Export\Entity\ExportColumn";s:10:"visibility";s:6:"public";s:9:"startLine";i:111;s:7:"endLine";i:118;s:3:"ccn";i:2;}s:18:"getBackgroundColor";a:6:{s:10:"methodName";s:18:"getBackgroundColor";s:9:"signature";s:28:"getBackgroundColor(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:125;s:7:"endLine";i:128;s:3:"ccn";i:1;}s:18:"setBackgroundColor";a:6:{s:10:"methodName";s:18:"setBackgroundColor";s:9:"signature";s:43:"setBackgroundColor(string $backgroundColor)";s:10:"visibility";s:6:"public";s:9:"startLine";i:135;s:7:"endLine";i:138;s:3:"ccn";i:1;}s:9:"getStyles";a:6:{s:10:"methodName";s:9:"getStyles";s:9:"signature";s:18:"getStyles(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:145;s:7:"endLine";i:148;s:3:"ccn";i:1;}s:9:"setStyles";a:6:{s:10:"methodName";s:9:"setStyles";s:9:"signature";s:24:"setStyles(array $styles)";s:10:"visibility";s:6:"public";s:9:"startLine";i:155;s:7:"endLine";i:158;s:3:"ccn";i:1;}s:8:"addStyle";a:6:{s:10:"methodName";s:8:"addStyle";s:9:"signature";s:30:"addStyle(string $name, $value)";s:10:"visibility";s:6:"public";s:9:"startLine";i:166;s:7:"endLine";i:169;s:3:"ccn";i:1;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:176;s:7:"endLine";i:179;s:3:"ccn";i:1;}s:9:"getLabels";a:6:{s:10:"methodName";s:9:"getLabels";s:9:"signature";s:18:"getLabels(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:186;s:7:"endLine";i:191;s:3:"ccn";i:1;}s:11:"getVarNames";a:6:{s:10:"methodName";s:11:"getVarNames";s:9:"signature";s:20:"getVarNames(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:198;s:7:"endLine";i:203;s:3:"ccn";i:1;}s:8:"getTypes";a:6:{s:10:"methodName";s:8:"getTypes";s:9:"signature";s:17:"getTypes(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:210;s:7:"endLine";i:215;s:3:"ccn";i:1;}s:14:"reorderColumns";a:6:{s:10:"methodName";s:14:"reorderColumns";s:9:"signature";s:28:"reorderColumns(array $order)";s:10:"visibility";s:6:"public";s:9:"startLine";i:223;s:7:"endLine";i:256;s:3:"ccn";i:6;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:62:"validateRecord(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:264;s:7:"endLine";i:284;s:3:"ccn";i:5;}s:6:"rewind";a:6:{s:10:"methodName";s:6:"rewind";s:9:"signature";s:14:"rewind(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:291;s:7:"endLine";i:294;s:3:"ccn";i:1;}s:7:"current";a:6:{s:10:"methodName";s:7:"current";s:9:"signature";s:43:"current(): Nzoom\Export\Entity\ExportColumn";s:10:"visibility";s:6:"public";s:9:"startLine";i:301;s:7:"endLine";i:304;s:3:"ccn";i:1;}s:3:"key";a:6:{s:10:"methodName";s:3:"key";s:9:"signature";s:10:"key(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:311;s:7:"endLine";i:314;s:3:"ccn";i:1;}s:4:"next";a:6:{s:10:"methodName";s:4:"next";s:9:"signature";s:12:"next(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:319;s:7:"endLine";i:322;s:3:"ccn";i:1;}s:5:"valid";a:6:{s:10:"methodName";s:5:"valid";s:9:"signature";s:13:"valid(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:329;s:7:"endLine";i:332;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:334;s:18:"commentLinesOfCode";i:140;s:21:"nonCommentLinesOfCode";i:194;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:67:{i:44;i:6;i:46;i:7;i:47;i:8;i:48;i:9;i:59;i:10;i:62;i:11;i:66;i:13;i:63;i:13;i:64;i:13;i:65;i:13;i:69;i:14;i:70;i:15;i:81;i:16;i:91;i:17;i:102;i:18;i:113;i:19;i:114;i:20;i:117;i:21;i:127;i:22;i:137;i:23;i:147;i:24;i:157;i:25;i:168;i:26;i:178;i:27;i:188;i:28;i:190;i:28;i:189;i:29;i:200;i:30;i:202;i:30;i:201;i:31;i:212;i:32;i:214;i:32;i:213;i:33;i:225;i:34;i:226;i:35;i:229;i:36;i:230;i:37;i:232;i:38;i:233;i:39;i:237;i:41;i:234;i:41;i:235;i:41;i:236;i:41;i:240;i:42;i:241;i:43;i:242;i:44;i:246;i:45;i:247;i:46;i:248;i:47;i:249;i:48;i:250;i:49;i:254;i:50;i:255;i:51;i:267;i:52;i:268;i:53;i:272;i:54;i:273;i:55;i:274;i:56;i:275;i:57;i:278;i:58;i:279;i:59;i:283;i:60;i:293;i:61;i:303;i:62;i:313;i:63;i:321;i:64;i:331;i:65;}}