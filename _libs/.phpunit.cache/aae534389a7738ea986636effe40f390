a:6:{s:9:"classesIn";a:1:{s:26:"Nzoom\Export\ExportService";a:6:{s:4:"name";s:13:"ExportService";s:14:"namespacedName";s:26:"Nzoom\Export\ExportService";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:13;s:7:"endLine";i:352;s:7:"methods";a:16:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:81:"__construct(Registry $registry, string $module, string $controller, string $type)";s:10:"visibility";s:6:"public";s:9:"startLine";i:69;s:7:"endLine";i:76;s:3:"ccn";i:1;}s:12:"setModelName";a:6:{s:10:"methodName";s:12:"setModelName";s:9:"signature";s:31:"setModelName(string $modelName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:84;s:7:"endLine";i:88;s:3:"ccn";i:1;}s:19:"setModelFactoryName";a:6:{s:10:"methodName";s:19:"setModelFactoryName";s:9:"signature";s:45:"setModelFactoryName(string $modelFactoryName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:96;s:7:"endLine";i:100;s:3:"ccn";i:1;}s:18:"createExportAction";a:6:{s:10:"methodName";s:18:"createExportAction";s:9:"signature";s:83:"createExportAction(string $module_check, array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:110;s:7:"endLine";i:124;s:3:"ccn";i:1;}s:16:"createExportData";a:6:{s:10:"methodName";s:16:"createExportData";s:9:"signature";s:113:"createExportData(Outlook $outlook, array $filters, string $modelClass, $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:127;s:7:"endLine";i:131;s:3:"ccn";i:1;}s:27:"createGeneratorFileStreamer";a:6:{s:10:"methodName";s:27:"createGeneratorFileStreamer";s:9:"signature";s:154:"createGeneratorFileStreamer(callable $generatorFunction, string $filename, string $mimeType, ?int $totalSize): Nzoom\Export\Streamer\GeneratorFileStreamer";s:10:"visibility";s:6:"public";s:9:"startLine";i:142;s:7:"endLine";i:145;s:3:"ccn";i:1;}s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:84:"export(string $filename, Nzoom\Export\Entity\ExportData $data, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:156;s:7:"endLine";i:173;s:3:"ccn";i:3;}s:16:"createTempStream";a:6:{s:10:"methodName";s:16:"createTempStream";s:9:"signature";s:18:"createTempStream()";s:10:"visibility";s:7:"private";s:9:"startLine";i:181;s:7:"endLine";i:190;s:3:"ccn";i:2;}s:15:"streamToBrowser";a:6:{s:10:"methodName";s:15:"streamToBrowser";s:9:"signature";s:74:"streamToBrowser($stream, string $downloadFilename, string $mimeType): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:201;s:7:"endLine";i:224;s:3:"ccn";i:3;}s:16:"getFormatFactory";a:6:{s:10:"methodName";s:16:"getFormatFactory";s:9:"signature";s:60:"getFormatFactory(): Nzoom\Export\Factory\ExportFormatFactory";s:10:"visibility";s:6:"public";s:9:"startLine";i:232;s:7:"endLine";i:239;s:3:"ccn";i:2;}s:16:"setFormatFactory";a:6:{s:10:"methodName";s:16:"setFormatFactory";s:9:"signature";s:79:"setFormatFactory(Nzoom\Export\Factory\ExportFormatFactory $formatFactory): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:247;s:7:"endLine";i:251;s:3:"ccn";i:1;}s:10:"getAdapter";a:6:{s:10:"methodName";s:10:"getAdapter";s:9:"signature";s:63:"getAdapter(): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:259;s:7:"endLine";i:266;s:3:"ccn";i:2;}s:19:"getSupportedFormats";a:6:{s:10:"methodName";s:19:"getSupportedFormats";s:9:"signature";s:28:"getSupportedFormats(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:273;s:7:"endLine";i:276;s:3:"ccn";i:1;}s:17:"isFormatSupported";a:6:{s:10:"methodName";s:17:"isFormatSupported";s:9:"signature";s:39:"isFormatSupported(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:284;s:7:"endLine";i:287;s:3:"ccn";i:1;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:45:"getExportFilename($prefix, string $extension)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:296;s:7:"endLine";i:316;s:3:"ccn";i:4;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:40:"handleExportError($message, $statusCode)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:326;s:7:"endLine";i:351;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:353;s:18:"commentLinesOfCode";i:150;s:21:"nonCommentLinesOfCode";i:203;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:75:{i:71;i:11;i:72;i:12;i:73;i:13;i:74;i:14;i:75;i:15;i:86;i:16;i:87;i:17;i:98;i:18;i:99;i:19;i:113;i:20;i:114;i:20;i:115;i:20;i:116;i:20;i:117;i:20;i:118;i:20;i:119;i:20;i:120;i:20;i:123;i:21;i:129;i:22;i:130;i:23;i:144;i:24;i:156;i:25;i:161;i:26;i:163;i:27;i:164;i:28;i:167;i:29;i:168;i:30;i:169;i:31;i:170;i:32;i:171;i:33;i:183;i:34;i:185;i:35;i:186;i:36;i:189;i:37;i:203;i:38;i:204;i:39;i:209;i:40;i:210;i:40;i:211;i:40;i:212;i:40;i:213;i:40;i:216;i:41;i:220;i:42;i:221;i:43;i:234;i:44;i:235;i:45;i:238;i:46;i:249;i:47;i:250;i:48;i:261;i:49;i:262;i:50;i:265;i:51;i:275;i:52;i:286;i:53;i:299;i:54;i:302;i:55;i:303;i:56;i:306;i:57;i:307;i:58;i:311;i:59;i:312;i:60;i:315;i:61;i:329;i:62;i:330;i:63;i:334;i:64;i:335;i:64;i:336;i:64;i:337;i:64;i:338;i:64;i:341;i:65;i:342;i:66;i:344;i:67;i:345;i:68;i:346;i:69;i:347;i:70;}}