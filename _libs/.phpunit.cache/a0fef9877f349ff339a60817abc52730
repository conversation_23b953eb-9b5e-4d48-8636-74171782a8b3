a:6:{s:9:"classesIn";a:1:{s:32:"Nzoom\Export\Entity\ExportRecord";a:6:{s:4:"name";s:12:"ExportRecord";s:14:"namespacedName";s:32:"Nzoom\Export\Entity\ExportRecord";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:11;s:7:"endLine";i:293;s:7:"methods";a:21:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:28:"__construct(array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:38;s:7:"endLine";i:42;s:3:"ccn";i:1;}s:8:"addValue";a:6:{s:10:"methodName";s:8:"addValue";s:9:"signature";s:68:"addValue(string $columnName, $value, ?string $type, ?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:52;s:7:"endLine";i:60;s:3:"ccn";i:2;}s:10:"setValueAt";a:6:{s:10:"methodName";s:10:"setValueAt";s:9:"signature";s:62:"setValueAt(int $index, $value, ?string $type, ?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:71;s:7:"endLine";i:86;s:3:"ccn";i:4;}s:20:"setValueByColumnName";a:6:{s:10:"methodName";s:20:"setValueByColumnName";s:9:"signature";s:80:"setValueByColumnName(string $columnName, $value, ?string $type, ?string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:98;s:7:"endLine";i:109;s:3:"ccn";i:2;}s:9:"getValues";a:6:{s:10:"methodName";s:9:"getValues";s:9:"signature";s:18:"getValues(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:116;s:7:"endLine";i:119;s:3:"ccn";i:1;}s:10:"getValueAt";a:6:{s:10:"methodName";s:10:"getValueAt";s:9:"signature";s:22:"getValueAt(int $index)";s:10:"visibility";s:6:"public";s:9:"startLine";i:127;s:7:"endLine";i:130;s:3:"ccn";i:1;}s:20:"getValueByColumnName";a:6:{s:10:"methodName";s:20:"getValueByColumnName";s:9:"signature";s:40:"getValueByColumnName(string $columnName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:138;s:7:"endLine";i:145;s:3:"ccn";i:2;}s:8:"hasValue";a:6:{s:10:"methodName";s:8:"hasValue";s:9:"signature";s:34:"hasValue(string $columnName): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:153;s:7:"endLine";i:156;s:3:"ccn";i:1;}s:12:"getRawValues";a:6:{s:10:"methodName";s:12:"getRawValues";s:9:"signature";s:21:"getRawValues(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:163;s:7:"endLine";i:168;s:3:"ccn";i:1;}s:18:"getFormattedValues";a:6:{s:10:"methodName";s:18:"getFormattedValues";s:9:"signature";s:27:"getFormattedValues(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:175;s:7:"endLine";i:180;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:187;s:7:"endLine";i:190;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:28:"setMetadata(array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:197;s:7:"endLine";i:200;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:209;s:7:"endLine";i:212;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:37:"setMetadataValue(string $key, $value)";s:10:"visibility";s:6:"public";s:9:"startLine";i:220;s:7:"endLine";i:223;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:56:"validate(Nzoom\Export\Entity\ExportHeader $header): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:231;s:7:"endLine";i:234;s:3:"ccn";i:1;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:241;s:7:"endLine";i:244;s:3:"ccn";i:1;}s:6:"rewind";a:6:{s:10:"methodName";s:6:"rewind";s:9:"signature";s:14:"rewind(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:251;s:7:"endLine";i:254;s:3:"ccn";i:1;}s:7:"current";a:6:{s:10:"methodName";s:7:"current";s:9:"signature";s:42:"current(): Nzoom\Export\Entity\ExportValue";s:10:"visibility";s:6:"public";s:9:"startLine";i:261;s:7:"endLine";i:264;s:3:"ccn";i:1;}s:3:"key";a:6:{s:10:"methodName";s:3:"key";s:9:"signature";s:10:"key(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:271;s:7:"endLine";i:274;s:3:"ccn";i:1;}s:4:"next";a:6:{s:10:"methodName";s:4:"next";s:9:"signature";s:12:"next(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:279;s:7:"endLine";i:282;s:3:"ccn";i:1;}s:5:"valid";a:6:{s:10:"methodName";s:5:"valid";s:9:"signature";s:13:"valid(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:289;s:7:"endLine";i:292;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:294;s:18:"commentLinesOfCode";i:139;s:21:"nonCommentLinesOfCode";i:155;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:48:{i:38;i:5;i:40;i:6;i:41;i:7;i:54;i:8;i:55;i:9;i:56;i:10;i:58;i:11;i:59;i:12;i:73;i:13;i:78;i:15;i:74;i:15;i:75;i:15;i:76;i:15;i:77;i:15;i:81;i:16;i:82;i:17;i:83;i:18;i:85;i:19;i:100;i:20;i:104;i:22;i:101;i:22;i:102;i:22;i:103;i:22;i:107;i:23;i:108;i:24;i:118;i:25;i:129;i:26;i:140;i:27;i:141;i:28;i:144;i:29;i:155;i:30;i:165;i:31;i:167;i:31;i:166;i:32;i:177;i:33;i:179;i:33;i:178;i:34;i:189;i:35;i:199;i:36;i:211;i:37;i:222;i:38;i:233;i:39;i:243;i:40;i:253;i:41;i:263;i:42;i:273;i:43;i:281;i:44;i:291;i:45;}}