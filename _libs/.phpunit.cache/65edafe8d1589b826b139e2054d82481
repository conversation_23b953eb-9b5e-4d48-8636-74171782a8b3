a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Factory\ExportFormatFactory";a:6:{s:4:"name";s:19:"ExportFormatFactory";s:14:"namespacedName";s:40:"Nzoom\Export\Factory\ExportFormatFactory";s:9:"namespace";s:20:"Nzoom\Export\Factory";s:9:"startLine";i:13;s:7:"endLine";i:237;s:7:"methods";a:9:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:67:"__construct(Registry $registry, string $module, string $controller)";s:10:"visibility";s:6:"public";s:9:"startLine";i:44;s:7:"endLine";i:50;s:3:"ccn";i:1;}s:13:"createAdapter";a:6:{s:10:"methodName";s:13:"createAdapter";s:9:"signature";s:96:"createAdapter(string $format, array $options): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:60;s:7:"endLine";i:85;s:3:"ccn";i:3;}s:15:"getAdapterClass";a:6:{s:10:"methodName";s:15:"getAdapterClass";s:9:"signature";s:39:"getAdapterClass(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:94;s:7:"endLine";i:105;s:3:"ccn";i:3;}s:16:"discoverAdapters";a:6:{s:10:"methodName";s:16:"discoverAdapters";s:9:"signature";s:25:"discoverAdapters(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:112;s:7:"endLine";i:140;s:3:"ccn";i:6;}s:20:"getClassNameFromFile";a:6:{s:10:"methodName";s:20:"getClassNameFromFile";s:9:"signature";s:47:"getClassNameFromFile(string $filePath): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:148;s:7:"endLine";i:163;s:3:"ccn";i:4;}s:14:"isValidAdapter";a:6:{s:10:"methodName";s:14:"isValidAdapter";s:9:"signature";s:39:"isValidAdapter(string $className): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:171;s:7:"endLine";i:179;s:3:"ccn";i:2;}s:19:"getSupportedFormats";a:6:{s:10:"methodName";s:19:"getSupportedFormats";s:9:"signature";s:28:"getSupportedFormats(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:186;s:7:"endLine";i:205;s:3:"ccn";i:3;}s:17:"isFormatSupported";a:6:{s:10:"methodName";s:17:"isFormatSupported";s:9:"signature";s:39:"isFormatSupported(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:213;s:7:"endLine";i:217;s:3:"ccn";i:1;}s:25:"createAdapterFromFilename";a:6:{s:10:"methodName";s:25:"createAdapterFromFilename";s:9:"signature";s:110:"createAdapterFromFilename(string $filename, array $options): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:227;s:7:"endLine";i:236;s:3:"ccn";i:2;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:238;s:18:"commentLinesOfCode";i:82;s:21:"nonCommentLinesOfCode";i:156;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:57:{i:46;i:9;i:47;i:10;i:48;i:11;i:49;i:12;i:60;i:13;i:62;i:14;i:65;i:15;i:66;i:16;i:67;i:17;i:71;i:18;i:74;i:19;i:77;i:20;i:78;i:21;i:82;i:22;i:84;i:23;i:96;i:24;i:98;i:25;i:99;i:26;i:100;i:27;i:104;i:28;i:115;i:29;i:116;i:30;i:119;i:31;i:121;i:32;i:122;i:33;i:126;i:34;i:128;i:35;i:129;i:36;i:131;i:37;i:132;i:38;i:137;i:39;i:139;i:40;i:150;i:41;i:153;i:42;i:154;i:43;i:158;i:44;i:159;i:45;i:160;i:46;i:162;i:47;i:173;i:48;i:174;i:49;i:178;i:50;i:189;i:51;i:190;i:52;i:193;i:53;i:194;i:54;i:196;i:55;i:198;i:56;i:202;i:57;i:204;i:58;i:215;i:59;i:216;i:60;i:227;i:61;i:229;i:62;i:231;i:63;i:232;i:64;i:235;i:65;}}