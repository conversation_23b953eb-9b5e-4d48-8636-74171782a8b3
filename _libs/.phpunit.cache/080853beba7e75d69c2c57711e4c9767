a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:16;s:7:"endLine";i:294;s:7:"methods";a:9:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:33;s:7:"endLine";i:36;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:45;s:7:"endLine";i:63;s:3:"ccn";i:2;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:71;s:7:"endLine";i:92;s:3:"ccn";i:4;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:101;s:7:"endLine";i:106;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:115;s:7:"endLine";i:141;s:3:"ccn";i:4;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:149;s:7:"endLine";i:171;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:28:"setChunkSize(int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:178;s:7:"endLine";i:181;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:192;s:7:"endLine";i:227;s:3:"ccn";i:2;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:143:"createCursorStreaming(string $modelClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:239;s:7:"endLine";i:293;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:295;s:18:"commentLinesOfCode";i:97;s:21:"nonCommentLinesOfCode";i:198;}s:15:"ignoredLinesFor";a:1:{i:0;i:16;}s:17:"executableLinesIn";a:102:{i:35;i:3;i:48;i:4;i:51;i:5;i:52;i:5;i:53;i:5;i:54;i:5;i:57;i:6;i:58;i:7;i:59;i:8;i:62;i:9;i:73;i:10;i:74;i:11;i:76;i:12;i:77;i:13;i:78;i:14;i:79;i:15;i:81;i:16;i:82;i:16;i:83;i:16;i:84;i:16;i:85;i:16;i:86;i:16;i:87;i:16;i:91;i:17;i:103;i:18;i:104;i:19;i:117;i:20;i:119;i:21;i:120;i:22;i:123;i:23;i:126;i:24;i:127;i:25;i:130;i:26;i:131;i:27;i:132;i:28;i:133;i:29;i:137;i:30;i:140;i:31;i:151;i:32;i:152;i:32;i:153;i:32;i:154;i:32;i:155;i:32;i:156;i:32;i:157;i:32;i:158;i:32;i:159;i:32;i:160;i:32;i:161;i:32;i:162;i:32;i:163;i:32;i:164;i:32;i:165;i:32;i:166;i:32;i:167;i:32;i:168;i:32;i:170;i:33;i:180;i:34;i:195;i:35;i:198;i:36;i:199;i:36;i:200;i:36;i:201;i:36;i:204;i:37;i:221;i:37;i:206;i:38;i:207;i:38;i:208;i:38;i:209;i:38;i:212;i:39;i:215;i:40;i:216;i:41;i:217;i:42;i:220;i:43;i:224;i:44;i:226;i:45;i:242;i:46;i:245;i:47;i:246;i:47;i:247;i:47;i:248;i:47;i:251;i:48;i:254;i:49;i:287;i:49;i:256;i:50;i:259;i:51;i:260;i:52;i:261;i:53;i:262;i:54;i:266;i:55;i:267;i:56;i:270;i:57;i:272;i:58;i:273;i:59;i:277;i:60;i:278;i:61;i:281;i:62;i:282;i:63;i:283;i:64;i:286;i:65;i:290;i:66;i:292;i:67;}}