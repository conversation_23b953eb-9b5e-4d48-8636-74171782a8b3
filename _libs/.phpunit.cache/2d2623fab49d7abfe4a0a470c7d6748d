a:6:{s:9:"classesIn";a:0:{}s:8:"traitsIn";a:1:{s:25:"Nzoom\I18n\I18nAwareTrait";a:6:{s:4:"name";s:14:"I18nAwareTrait";s:14:"namespacedName";s:25:"Nzoom\I18n\I18nAwareTrait";s:9:"namespace";s:10:"Nzoom\I18n";s:9:"startLine";i:10;s:7:"endLine";i:49;s:7:"methods";a:3:{s:13:"getTranslator";a:6:{s:10:"methodName";s:13:"getTranslator";s:9:"signature";s:32:"getTranslator(): Nzoom\I18n\I18n";s:10:"visibility";s:6:"public";s:9:"startLine";i:22;s:7:"endLine";i:25;s:3:"ccn";i:1;}s:13:"setTranslator";a:6:{s:10:"methodName";s:13:"setTranslator";s:9:"signature";s:42:"setTranslator(Nzoom\I18n\I18n $translator)";s:10:"visibility";s:6:"public";s:9:"startLine";i:33;s:7:"endLine";i:37;s:3:"ccn";i:1;}s:4:"i18n";a:6:{s:10:"methodName";s:4:"i18n";s:9:"signature";s:25:"i18n(string $key): string";s:10:"visibility";s:9:"protected";s:9:"startLine";i:45;s:7:"endLine";i:48;s:3:"ccn";i:1;}}}}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:50;s:18:"commentLinesOfCode";i:25;s:21:"nonCommentLinesOfCode";i:25;}s:15:"ignoredLinesFor";a:1:{i:0;i:10;}s:17:"executableLinesIn";a:4:{i:24;i:2;i:35;i:3;i:36;i:4;i:47;i:5;}}