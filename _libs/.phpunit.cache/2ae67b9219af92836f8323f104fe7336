a:6:{s:9:"classesIn";a:1:{s:32:"Nzoom\Export\Entity\ExportColumn";a:6:{s:4:"name";s:12:"ExportColumn";s:14:"namespacedName";s:32:"Nzoom\Export\Entity\ExportColumn";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:10;s:7:"endLine";i:239;s:7:"methods";a:16:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:101:"__construct(string $varName, string $label, string $type, string $format, ?int $width, array $styles)";s:10:"visibility";s:6:"public";s:9:"startLine";i:53;s:7:"endLine";i:67;s:3:"ccn";i:1;}s:10:"getVarName";a:6:{s:10:"methodName";s:10:"getVarName";s:9:"signature";s:20:"getVarName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:74;s:7:"endLine";i:77;s:3:"ccn";i:1;}s:10:"setVarName";a:6:{s:10:"methodName";s:10:"setVarName";s:9:"signature";s:27:"setVarName(string $varName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:86;s:7:"endLine";i:93;s:3:"ccn";i:2;}s:8:"getLabel";a:6:{s:10:"methodName";s:8:"getLabel";s:9:"signature";s:18:"getLabel(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:100;s:7:"endLine";i:103;s:3:"ccn";i:1;}s:8:"setLabel";a:6:{s:10:"methodName";s:8:"setLabel";s:9:"signature";s:23:"setLabel(string $label)";s:10:"visibility";s:6:"public";s:9:"startLine";i:110;s:7:"endLine";i:113;s:3:"ccn";i:1;}s:7:"getType";a:6:{s:10:"methodName";s:7:"getType";s:9:"signature";s:17:"getType(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:120;s:7:"endLine";i:123;s:3:"ccn";i:1;}s:7:"setType";a:6:{s:10:"methodName";s:7:"setType";s:9:"signature";s:21:"setType(string $type)";s:10:"visibility";s:6:"public";s:9:"startLine";i:131;s:7:"endLine";i:143;s:3:"ccn";i:2;}s:9:"getFormat";a:6:{s:10:"methodName";s:9:"getFormat";s:9:"signature";s:19:"getFormat(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:150;s:7:"endLine";i:153;s:3:"ccn";i:1;}s:9:"setFormat";a:6:{s:10:"methodName";s:9:"setFormat";s:9:"signature";s:25:"setFormat(string $format)";s:10:"visibility";s:6:"public";s:9:"startLine";i:161;s:7:"endLine";i:164;s:3:"ccn";i:1;}s:8:"getWidth";a:6:{s:10:"methodName";s:8:"getWidth";s:9:"signature";s:16:"getWidth(): ?int";s:10:"visibility";s:6:"public";s:9:"startLine";i:171;s:7:"endLine";i:174;s:3:"ccn";i:1;}s:8:"setWidth";a:6:{s:10:"methodName";s:8:"setWidth";s:9:"signature";s:21:"setWidth(?int $width)";s:10:"visibility";s:6:"public";s:9:"startLine";i:181;s:7:"endLine";i:184;s:3:"ccn";i:1;}s:9:"getStyles";a:6:{s:10:"methodName";s:9:"getStyles";s:9:"signature";s:18:"getStyles(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:191;s:7:"endLine";i:194;s:3:"ccn";i:1;}s:9:"setStyles";a:6:{s:10:"methodName";s:9:"setStyles";s:9:"signature";s:24:"setStyles(array $styles)";s:10:"visibility";s:6:"public";s:9:"startLine";i:201;s:7:"endLine";i:204;s:3:"ccn";i:1;}s:8:"addStyle";a:6:{s:10:"methodName";s:8:"addStyle";s:9:"signature";s:30:"addStyle(string $name, $value)";s:10:"visibility";s:6:"public";s:9:"startLine";i:212;s:7:"endLine";i:215;s:3:"ccn";i:1;}s:13:"validateValue";a:6:{s:10:"methodName";s:13:"validateValue";s:9:"signature";s:27:"validateValue($value): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:223;s:7:"endLine";i:227;s:3:"ccn";i:1;}s:11:"createValue";a:6:{s:10:"methodName";s:11:"createValue";s:9:"signature";s:52:"createValue($value): Nzoom\Export\Entity\ExportValue";s:10:"visibility";s:6:"public";s:9:"startLine";i:235;s:7:"endLine";i:238;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:240;s:18:"commentLinesOfCode";i:117;s:21:"nonCommentLinesOfCode";i:123;}s:15:"ignoredLinesFor";a:1:{i:0;i:10;}s:17:"executableLinesIn";a:32:{i:56;i:7;i:59;i:8;i:61;i:9;i:62;i:10;i:63;i:11;i:64;i:12;i:65;i:13;i:66;i:14;i:76;i:15;i:88;i:16;i:89;i:17;i:92;i:18;i:102;i:19;i:112;i:20;i:122;i:21;i:134;i:22;i:139;i:24;i:135;i:24;i:136;i:24;i:137;i:24;i:138;i:24;i:142;i:25;i:152;i:26;i:163;i:27;i:173;i:28;i:183;i:29;i:193;i:30;i:203;i:31;i:214;i:32;i:225;i:33;i:226;i:34;i:237;i:35;}}