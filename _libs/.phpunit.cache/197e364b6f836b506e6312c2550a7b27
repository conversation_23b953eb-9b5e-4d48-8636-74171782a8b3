a:6:{s:9:"classesIn";a:1:{s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";a:6:{s:4:"name";s:24:"ExcelExportFormatAdapter";s:14:"namespacedName";s:45:"Nzoom\Export\Adapter\ExcelExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:25;s:7:"endLine";i:1055;s:7:"methods";a:32:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:54;s:7:"endLine";i:96;s:3:"ccn";i:5;}s:20:"extractSizingOptions";a:6:{s:10:"methodName";s:20:"extractSizingOptions";s:9:"signature";s:42:"extractSizingOptions(array $options): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:104;s:7:"endLine";i:128;s:3:"ccn";i:11;}s:17:"createSpreadsheet";a:6:{s:10:"methodName";s:17:"createSpreadsheet";s:9:"signature";s:99:"createSpreadsheet(Nzoom\Export\Entity\ExportData $exportData): PhpOffice\PhpSpreadsheet\Spreadsheet";s:10:"visibility";s:7:"private";s:9:"startLine";i:136;s:7:"endLine";i:158;s:3:"ccn";i:1;}s:20:"setSpreadsheetLocale";a:6:{s:10:"methodName";s:20:"setSpreadsheetLocale";s:9:"signature";s:28:"setSpreadsheetLocale(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:165;s:7:"endLine";i:181;s:3:"ccn";i:4;}s:20:"getApplicationLocale";a:6:{s:10:"methodName";s:20:"getApplicationLocale";s:9:"signature";s:31:"getApplicationLocale(): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:188;s:7:"endLine";i:230;s:3:"ccn";i:4;}s:21:"setDocumentProperties";a:6:{s:10:"methodName";s:21:"setDocumentProperties";s:9:"signature";s:96:"setDocumentProperties(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:239;s:7:"endLine";i:251;s:3:"ccn";i:2;}s:17:"processExportData";a:6:{s:10:"methodName";s:17:"processExportData";s:9:"signature";s:120:"processExportData(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:260;s:7:"endLine";i:278;s:3:"ccn";i:1;}s:10:"addHeaders";a:6:{s:10:"methodName";s:10:"addHeaders";s:9:"signature";s:85:"addHeaders(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:287;s:7:"endLine";i:293;s:3:"ccn";i:2;}s:14:"styleHeaderRow";a:6:{s:10:"methodName";s:14:"styleHeaderRow";s:9:"signature";s:91:"styleHeaderRow(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, int $headerCount): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:302;s:7:"endLine";i:309;s:3:"ccn";i:1;}s:24:"processExportDataRecords";a:6:{s:10:"methodName";s:24:"processExportDataRecords";s:9:"signature";s:127:"processExportDataRecords(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportData $exportData): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:318;s:7:"endLine";i:357;s:3:"ccn";i:8;}s:19:"processExportRecord";a:6:{s:10:"methodName";s:19:"processExportRecord";s:9:"signature";s:130:"processExportRecord(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, Nzoom\Export\Entity\ExportRecord $record, int $row): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:367;s:7:"endLine";i:382;s:3:"ccn";i:3;}s:26:"setCellValueWithFormatting";a:6:{s:10:"methodName";s:26:"setCellValueWithFormatting";s:9:"signature";s:152:"setCellValueWithFormatting(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, Nzoom\Export\Entity\ExportValue $exportValue): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:392;s:7:"endLine";i:442;s:3:"ccn";i:17;}s:16:"setCellDateValue";a:6:{s:10:"methodName";s:16:"setCellDateValue";s:9:"signature";s:118:"setCellDateValue(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $cellAddress, $value, string $type): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:453;s:7:"endLine";i:473;s:3:"ccn";i:5;}s:29:"getExcelFormatFromExportValue";a:6:{s:10:"methodName";s:29:"getExcelFormatFromExportValue";s:9:"signature";s:83:"getExcelFormatFromExportValue(Nzoom\Export\Entity\ExportValue $exportValue): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:481;s:7:"endLine";i:524;s:3:"ccn";i:15;}s:25:"convertCustomNumberFormat";a:6:{s:10:"methodName";s:25:"convertCustomNumberFormat";s:9:"signature";s:49:"convertCustomNumberFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:532;s:7:"endLine";i:547;s:3:"ccn";i:3;}s:23:"convertCustomDateFormat";a:6:{s:10:"methodName";s:23:"convertCustomDateFormat";s:9:"signature";s:47:"convertCustomDateFormat(string $format): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:555;s:7:"endLine";i:573;s:3:"ccn";i:1;}s:27:"handleExportRecordCellError";a:6:{s:10:"methodName";s:27:"handleExportRecordCellError";s:9:"signature";s:186:"handleExportRecordCellError(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, string $colLetter, int $row, Exception $e, int $colIndex, Nzoom\Export\Entity\ExportRecord $record): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:586;s:7:"endLine";i:595;s:3:"ccn";i:2;}s:25:"finalizeExportDataColumns";a:6:{s:10:"methodName";s:25:"finalizeExportDataColumns";s:9:"signature";s:100:"finalizeExportDataColumns(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet, array $headers): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:604;s:7:"endLine";i:620;s:3:"ccn";i:2;}s:27:"applyColumnWidthConstraints";a:6:{s:10:"methodName";s:27:"applyColumnWidthConstraints";s:9:"signature";s:86:"applyColumnWidthConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:628;s:7:"endLine";i:654;s:3:"ccn";i:5;}s:25:"applyRowHeightConstraints";a:6:{s:10:"methodName";s:25:"applyRowHeightConstraints";s:9:"signature";s:84:"applyRowHeightConstraints(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:665;s:7:"endLine";i:714;s:3:"ccn";i:8;}s:22:"applyVerticalAlignment";a:6:{s:10:"methodName";s:22:"applyVerticalAlignment";s:9:"signature";s:81:"applyVerticalAlignment(PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:722;s:7:"endLine";i:732;s:3:"ccn";i:3;}s:12:"createWriter";a:6:{s:10:"methodName";s:12:"createWriter";s:9:"signature";s:123:"createWriter(PhpOffice\PhpSpreadsheet\Spreadsheet $spreadsheet, string $extension): PhpOffice\PhpSpreadsheet\Writer\IWriter";s:10:"visibility";s:7:"private";s:9:"startLine";i:742;s:7:"endLine";i:754;s:3:"ccn";i:4;}s:22:"handleSpreadsheetError";a:6:{s:10:"methodName";s:22:"handleSpreadsheetError";s:9:"signature";s:92:"handleSpreadsheetError(PhpOffice\PhpSpreadsheet\Writer\Exception $e, string $filename): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:763;s:7:"endLine";i:785;s:3:"ccn";i:4;}s:18:"getExcelFormatting";a:6:{s:10:"methodName";s:18:"getExcelFormatting";s:9:"signature";s:43:"getExcelFormatting(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:793;s:7:"endLine";i:888;s:3:"ccn";i:83;}s:23:"optimizeMemoryForExport";a:6:{s:10:"methodName";s:23:"optimizeMemoryForExport";s:9:"signature";s:31:"optimizeMemoryForExport(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:895;s:7:"endLine";i:910;s:3:"ccn";i:3;}s:14:"convertToBytes";a:6:{s:10:"methodName";s:14:"convertToBytes";s:9:"signature";s:40:"convertToBytes(string $memoryLimit): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:918;s:7:"endLine";i:936;s:3:"ccn";i:4;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:941;s:7:"endLine";i:944;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:949;s:7:"endLine";i:961;s:3:"ccn";i:4;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:966;s:7:"endLine";i:969;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:974;s:7:"endLine";i:977;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:982;s:7:"endLine";i:985;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:990;s:7:"endLine";i:1054;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:1056;s:18:"commentLinesOfCode";i:304;s:21:"nonCommentLinesOfCode";i:752;}s:15:"ignoredLinesFor";a:1:{i:0;i:25;}s:17:"executableLinesIn";a:445:{i:54;i:4;i:58;i:5;i:59;i:6;i:63;i:7;i:66;i:8;i:67;i:9;i:68;i:10;i:72;i:11;i:75;i:12;i:78;i:13;i:81;i:14;i:84;i:15;i:85;i:16;i:87;i:17;i:89;i:18;i:90;i:19;i:94;i:20;i:106;i:21;i:107;i:22;i:110;i:23;i:111;i:24;i:114;i:25;i:115;i:26;i:119;i:27;i:121;i:28;i:122;i:29;i:123;i:30;i:124;i:31;i:139;i:32;i:142;i:33;i:145;i:34;i:148;i:35;i:151;i:36;i:152;i:37;i:155;i:38;i:157;i:39;i:169;i:40;i:171;i:41;i:173;i:42;i:175;i:43;i:177;i:44;i:178;i:45;i:191;i:46;i:192;i:47;i:195;i:48;i:196;i:48;i:197;i:48;i:198;i:48;i:199;i:48;i:200;i:48;i:201;i:48;i:202;i:48;i:203;i:48;i:204;i:48;i:205;i:48;i:206;i:48;i:207;i:48;i:208;i:48;i:209;i:48;i:210;i:48;i:211;i:48;i:212;i:48;i:213;i:48;i:215;i:49;i:219;i:50;i:220;i:51;i:224;i:52;i:225;i:53;i:229;i:54;i:241;i:55;i:242;i:56;i:243;i:57;i:246;i:58;i:247;i:58;i:248;i:58;i:249;i:58;i:250;i:58;i:263;i:59;i:264;i:60;i:267;i:61;i:270;i:62;i:273;i:63;i:276;i:64;i:289;i:65;i:290;i:66;i:291;i:67;i:304;i:68;i:305;i:69;i:306;i:70;i:307;i:70;i:308;i:70;i:320;i:71;i:321;i:72;i:322;i:73;i:325;i:74;i:327;i:75;i:329;i:76;i:330;i:77;i:333;i:78;i:334;i:79;i:337;i:80;i:338;i:81;i:342;i:82;i:343;i:83;i:349;i:84;i:350;i:85;i:354;i:86;i:355;i:87;i:369;i:88;i:371;i:89;i:372;i:90;i:373;i:91;i:377;i:92;i:378;i:93;i:379;i:94;i:394;i:95;i:395;i:96;i:398;i:97;i:399;i:98;i:400;i:99;i:405;i:100;i:406;i:101;i:407;i:102;i:408;i:103;i:410;i:104;i:411;i:105;i:412;i:106;i:413;i:107;i:414;i:108;i:416;i:109;i:417;i:110;i:418;i:111;i:420;i:112;i:421;i:113;i:422;i:114;i:423;i:115;i:424;i:116;i:425;i:117;i:429;i:118;i:430;i:119;i:432;i:120;i:434;i:121;i:438;i:122;i:439;i:123;i:440;i:124;i:456;i:125;i:458;i:126;i:459;i:127;i:460;i:128;i:462;i:129;i:463;i:130;i:464;i:131;i:467;i:132;i:469;i:133;i:471;i:134;i:483;i:135;i:484;i:136;i:487;i:137;i:488;i:138;i:489;i:139;i:491;i:140;i:492;i:141;i:493;i:142;i:495;i:143;i:497;i:144;i:498;i:145;i:500;i:146;i:502;i:147;i:503;i:148;i:505;i:149;i:506;i:150;i:508;i:151;i:510;i:152;i:511;i:153;i:513;i:154;i:514;i:155;i:516;i:156;i:518;i:157;i:519;i:158;i:522;i:159;i:535;i:160;i:537;i:161;i:538;i:162;i:539;i:163;i:541;i:164;i:546;i:165;i:558;i:166;i:559;i:166;i:560;i:166;i:561;i:166;i:562;i:166;i:563;i:166;i:564;i:166;i:565;i:166;i:566;i:166;i:567;i:166;i:568;i:166;i:569;i:166;i:572;i:167;i:589;i:168;i:590;i:169;i:591;i:169;i:594;i:170;i:607;i:171;i:608;i:172;i:609;i:173;i:613;i:174;i:616;i:175;i:619;i:176;i:631;i:177;i:634;i:178;i:635;i:179;i:636;i:180;i:639;i:181;i:640;i:182;i:642;i:183;i:643;i:184;i:646;i:185;i:647;i:186;i:648;i:187;i:649;i:188;i:650;i:189;i:667;i:190;i:668;i:191;i:669;i:192;i:672;i:193;i:673;i:194;i:676;i:195;i:678;i:196;i:679;i:197;i:680;i:198;i:683;i:199;i:684;i:200;i:685;i:201;i:688;i:202;i:689;i:203;i:693;i:204;i:694;i:205;i:695;i:206;i:697;i:207;i:698;i:208;i:699;i:209;i:700;i:210;i:705;i:211;i:707;i:212;i:709;i:213;i:711;i:214;i:724;i:215;i:725;i:216;i:727;i:217;i:729;i:218;i:730;i:219;i:744;i:220;i:747;i:221;i:748;i:222;i:749;i:223;i:750;i:224;i:752;i:225;i:766;i:226;i:767;i:227;i:771;i:228;i:772;i:229;i:775;i:230;i:777;i:231;i:778;i:231;i:779;i:231;i:780;i:231;i:781;i:232;i:796;i:233;i:797;i:234;i:798;i:235;i:799;i:236;i:800;i:237;i:801;i:238;i:802;i:239;i:803;i:240;i:804;i:241;i:805;i:242;i:806;i:243;i:807;i:244;i:808;i:245;i:809;i:246;i:810;i:247;i:811;i:248;i:812;i:249;i:813;i:250;i:814;i:251;i:815;i:252;i:816;i:253;i:817;i:254;i:818;i:255;i:819;i:256;i:820;i:257;i:821;i:258;i:822;i:259;i:823;i:260;i:824;i:261;i:825;i:262;i:826;i:263;i:827;i:264;i:828;i:265;i:829;i:266;i:830;i:267;i:831;i:268;i:832;i:269;i:833;i:270;i:834;i:271;i:835;i:272;i:836;i:273;i:837;i:274;i:838;i:275;i:839;i:276;i:840;i:277;i:841;i:278;i:842;i:279;i:843;i:280;i:844;i:281;i:845;i:282;i:846;i:283;i:847;i:284;i:848;i:285;i:849;i:286;i:850;i:287;i:851;i:288;i:852;i:289;i:853;i:290;i:854;i:291;i:855;i:292;i:856;i:293;i:857;i:294;i:858;i:295;i:859;i:296;i:860;i:297;i:861;i:298;i:863;i:299;i:864;i:300;i:865;i:301;i:866;i:302;i:867;i:303;i:868;i:304;i:869;i:305;i:870;i:306;i:872;i:307;i:873;i:308;i:874;i:309;i:875;i:310;i:876;i:311;i:877;i:312;i:878;i:313;i:879;i:314;i:880;i:315;i:881;i:316;i:882;i:317;i:883;i:318;i:884;i:319;i:887;i:320;i:898;i:321;i:899;i:322;i:903;i:323;i:904;i:324;i:907;i:325;i:908;i:326;i:920;i:327;i:921;i:328;i:922;i:329;i:925;i:330;i:926;i:331;i:928;i:332;i:929;i:333;i:931;i:334;i:932;i:335;i:935;i:336;i:943;i:337;i:951;i:338;i:953;i:339;i:954;i:340;i:955;i:341;i:956;i:342;i:957;i:343;i:959;i:344;i:968;i:345;i:976;i:346;i:984;i:347;i:992;i:348;i:993;i:348;i:994;i:348;i:995;i:348;i:996;i:348;i:997;i:348;i:998;i:348;i:999;i:348;i:1000;i:348;i:1001;i:348;i:1002;i:348;i:1003;i:348;i:1004;i:348;i:1005;i:348;i:1006;i:348;i:1007;i:348;i:1008;i:348;i:1009;i:348;i:1010;i:348;i:1011;i:348;i:1012;i:348;i:1013;i:348;i:1014;i:348;i:1015;i:348;i:1016;i:348;i:1017;i:348;i:1018;i:348;i:1019;i:348;i:1020;i:348;i:1021;i:348;i:1022;i:348;i:1023;i:348;i:1024;i:348;i:1025;i:348;i:1026;i:348;i:1027;i:348;i:1028;i:348;i:1029;i:348;i:1030;i:348;i:1031;i:348;i:1032;i:348;i:1033;i:348;i:1034;i:348;i:1035;i:348;i:1036;i:348;i:1037;i:348;i:1038;i:348;i:1039;i:348;i:1040;i:348;i:1041;i:348;i:1042;i:348;i:1043;i:348;i:1044;i:348;i:1045;i:348;i:1046;i:348;i:1047;i:348;i:1048;i:348;i:1049;i:348;i:1050;i:348;i:1051;i:348;i:1052;i:348;i:1053;i:348;}}