a:6:{s:9:"classesIn";a:1:{s:30:"Nzoom\Export\Entity\ExportData";a:6:{s:4:"name";s:10:"ExportData";s:14:"namespacedName";s:30:"Nzoom\Export\Entity\ExportData";s:9:"namespace";s:19:"Nzoom\Export\Entity";s:9:"startLine";i:12;s:7:"endLine";i:424;s:7:"methods";a:23:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:70:"__construct(Nzoom\Export\Entity\ExportHeader $header, array $metadata)";s:10:"visibility";s:6:"public";s:9:"startLine";i:50;s:7:"endLine";i:54;s:3:"ccn";i:1;}s:17:"setRecordProvider";a:6:{s:10:"methodName";s:17:"setRecordProvider";s:9:"signature";s:64:"setRecordProvider(callable $recordProvider, int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:64;s:7:"endLine";i:74;s:3:"ccn";i:1;}s:6:"isLazy";a:6:{s:10:"methodName";s:6:"isLazy";s:9:"signature";s:14:"isLazy(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:81;s:7:"endLine";i:84;s:3:"ccn";i:1;}s:11:"getPageSize";a:6:{s:10:"methodName";s:11:"getPageSize";s:9:"signature";s:18:"getPageSize(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:91;s:7:"endLine";i:94;s:3:"ccn";i:1;}s:11:"setPageSize";a:6:{s:10:"methodName";s:11:"setPageSize";s:9:"signature";s:32:"setPageSize(int $pageSize): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:102;s:7:"endLine";i:106;s:3:"ccn";i:1;}s:9:"getHeader";a:6:{s:10:"methodName";s:9:"getHeader";s:9:"signature";s:45:"getHeader(): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:6:"public";s:9:"startLine";i:113;s:7:"endLine";i:116;s:3:"ccn";i:1;}s:9:"setHeader";a:6:{s:10:"methodName";s:9:"setHeader";s:9:"signature";s:57:"setHeader(Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:123;s:7:"endLine";i:126;s:3:"ccn";i:1;}s:11:"getMetadata";a:6:{s:10:"methodName";s:11:"getMetadata";s:9:"signature";s:20:"getMetadata(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:133;s:7:"endLine";i:136;s:3:"ccn";i:1;}s:11:"setMetadata";a:6:{s:10:"methodName";s:11:"setMetadata";s:9:"signature";s:34:"setMetadata(array $metadata): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:143;s:7:"endLine";i:146;s:3:"ccn";i:1;}s:16:"getMetadataValue";a:6:{s:10:"methodName";s:16:"getMetadataValue";s:9:"signature";s:39:"getMetadataValue(string $key, $default)";s:10:"visibility";s:6:"public";s:9:"startLine";i:155;s:7:"endLine";i:158;s:3:"ccn";i:1;}s:16:"setMetadataValue";a:6:{s:10:"methodName";s:16:"setMetadataValue";s:9:"signature";s:43:"setMetadataValue(string $key, $value): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:166;s:7:"endLine";i:169;s:3:"ccn";i:1;}s:9:"addRecord";a:6:{s:10:"methodName";s:9:"addRecord";s:9:"signature";s:73:"addRecord(Nzoom\Export\Entity\ExportRecord $record, bool $validate): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:180;s:7:"endLine";i:191;s:3:"ccn";i:4;}s:10:"getRecords";a:6:{s:10:"methodName";s:10:"getRecords";s:9:"signature";s:19:"getRecords(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:199;s:7:"endLine";i:211;s:3:"ccn";i:3;}s:11:"getRecordAt";a:6:{s:10:"methodName";s:11:"getRecordAt";s:9:"signature";s:58:"getRecordAt(int $index): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:220;s:7:"endLine";i:235;s:3:"ccn";i:4;}s:5:"count";a:6:{s:10:"methodName";s:5:"count";s:9:"signature";s:12:"count(): int";s:10:"visibility";s:6:"public";s:9:"startLine";i:242;s:7:"endLine";i:249;s:3:"ccn";i:2;}s:12:"createRecord";a:6:{s:10:"methodName";s:12:"createRecord";s:9:"signature";s:63:"createRecord(array $metadata): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:6:"public";s:9:"startLine";i:259;s:7:"endLine";i:268;s:3:"ccn";i:2;}s:7:"isEmpty";a:6:{s:10:"methodName";s:7:"isEmpty";s:9:"signature";s:15:"isEmpty(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:275;s:7:"endLine";i:282;s:3:"ccn";i:2;}s:12:"sortByColumn";a:6:{s:10:"methodName";s:12:"sortByColumn";s:9:"signature";s:55:"sortByColumn(string $columnName, bool $ascending): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:291;s:7:"endLine";i:332;s:3:"ccn";i:15;}s:6:"filter";a:6:{s:10:"methodName";s:6:"filter";s:9:"signature";s:32:"filter(callable $callback): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:339;s:7:"endLine";i:343;s:3:"ccn";i:1;}s:8:"validate";a:6:{s:10:"methodName";s:8:"validate";s:9:"signature";s:16:"validate(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:350;s:7:"endLine";i:359;s:3:"ccn";i:3;}s:7:"toArray";a:6:{s:10:"methodName";s:7:"toArray";s:9:"signature";s:31:"toArray(bool $formatted): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:368;s:7:"endLine";i:381;s:3:"ccn";i:3;}s:11:"getIterator";a:6:{s:10:"methodName";s:11:"getIterator";s:9:"signature";s:26:"getIterator(): Traversable";s:10:"visibility";s:6:"public";s:9:"startLine";i:388;s:7:"endLine";i:395;s:3:"ccn";i:2;}s:15:"getLazyIterator";a:6:{s:10:"methodName";s:15:"getLazyIterator";s:9:"signature";s:28:"getLazyIterator(): Generator";s:10:"visibility";s:7:"private";s:9:"startLine";i:402;s:7:"endLine";i:423;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:425;s:18:"commentLinesOfCode";i:168;s:21:"nonCommentLinesOfCode";i:257;}s:15:"ignoredLinesFor";a:1:{i:0;i:12;}s:17:"executableLinesIn";a:99:{i:50;i:7;i:52;i:8;i:53;i:9;i:66;i:10;i:67;i:11;i:68;i:12;i:71;i:13;i:73;i:14;i:83;i:15;i:93;i:16;i:104;i:17;i:105;i:18;i:115;i:19;i:125;i:20;i:135;i:21;i:145;i:22;i:157;i:23;i:168;i:24;i:182;i:25;i:183;i:26;i:186;i:27;i:187;i:28;i:190;i:29;i:201;i:30;i:203;i:31;i:204;i:32;i:205;i:33;i:207;i:34;i:210;i:35;i:222;i:36;i:224;i:37;i:225;i:38;i:226;i:39;i:227;i:40;i:229;i:41;i:231;i:42;i:234;i:43;i:244;i:44;i:245;i:45;i:248;i:46;i:259;i:47;i:261;i:48;i:262;i:49;i:265;i:50;i:266;i:51;i:267;i:52;i:277;i:53;i:278;i:54;i:281;i:55;i:293;i:56;i:297;i:58;i:294;i:58;i:295;i:58;i:296;i:58;i:300;i:59;i:331;i:59;i:301;i:60;i:302;i:61;i:304;i:62;i:305;i:63;i:308;i:64;i:309;i:65;i:312;i:66;i:313;i:67;i:316;i:68;i:317;i:69;i:318;i:70;i:320;i:71;i:321;i:72;i:322;i:73;i:323;i:74;i:324;i:75;i:325;i:76;i:327;i:77;i:330;i:78;i:341;i:79;i:342;i:80;i:352;i:81;i:353;i:82;i:354;i:83;i:358;i:84;i:370;i:85;i:373;i:86;i:376;i:87;i:377;i:88;i:380;i:89;i:390;i:90;i:391;i:91;i:394;i:92;i:404;i:93;i:405;i:94;i:408;i:95;i:422;i:96;i:410;i:97;i:412;i:98;i:413;i:99;i:416;i:100;i:417;i:101;i:420;i:102;}}