a:6:{s:9:"classesIn";a:1:{s:34:"Nzoom\Export\Streamer\FileStreamer";a:6:{s:4:"name";s:12:"FileStreamer";s:14:"namespacedName";s:34:"Nzoom\Export\Streamer\FileStreamer";s:9:"namespace";s:21:"Nzoom\Export\Streamer";s:9:"startLine";i:12;s:7:"endLine";i:292;s:7:"methods";a:17:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:47:"__construct(string $filename, string $mimeType)";s:10:"visibility";s:6:"public";s:9:"startLine";i:60;s:7:"endLine";i:64;s:3:"ccn";i:1;}s:10:"getHeaders";a:6:{s:10:"methodName";s:10:"getHeaders";s:9:"signature";s:49:"getHeaders(): Nzoom\Export\Streamer\StreamHeaders";s:10:"visibility";s:6:"public";s:9:"startLine";i:71;s:7:"endLine";i:77;s:3:"ccn";i:2;}s:10:"setHeaders";a:6:{s:10:"methodName";s:10:"setHeaders";s:9:"signature";s:62:"setHeaders(Nzoom\Export\Streamer\StreamHeaders $headers): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:85;s:7:"endLine";i:88;s:3:"ccn";i:1;}s:16:"setTimeIncrement";a:6:{s:10:"methodName";s:16:"setTimeIncrement";s:9:"signature";s:36:"setTimeIncrement(int $seconds): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:96;s:7:"endLine";i:99;s:3:"ccn";i:1;}s:15:"setCacheExpires";a:6:{s:10:"methodName";s:15:"setCacheExpires";s:9:"signature";s:35:"setCacheExpires(int $seconds): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:107;s:7:"endLine";i:110;s:3:"ccn";i:1;}s:7:"setETag";a:6:{s:10:"methodName";s:7:"setETag";s:9:"signature";s:28:"setETag(?string $etag): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:118;s:7:"endLine";i:121;s:3:"ccn";i:1;}s:15:"setLastModified";a:6:{s:10:"methodName";s:15:"setLastModified";s:9:"signature";s:38:"setLastModified(?int $timestamp): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:129;s:7:"endLine";i:132;s:3:"ccn";i:1;}s:6:"stream";a:6:{s:10:"methodName";s:6:"stream";s:9:"signature";s:14:"stream(): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:142;s:7:"endLine";i:158;s:3:"ccn";i:2;}s:16:"performStreaming";a:6:{s:10:"methodName";s:16:"performStreaming";s:9:"signature";s:24:"performStreaming(): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:168;s:7:"endLine";i:168;s:3:"ccn";i:0;}s:19:"prepareForStreaming";a:6:{s:10:"methodName";s:19:"prepareForStreaming";s:9:"signature";s:27:"prepareForStreaming(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:175;s:7:"endLine";i:190;s:3:"ccn";i:2;}s:31:"setStreamingOptimizationHeaders";a:6:{s:10:"methodName";s:31:"setStreamingOptimizationHeaders";s:9:"signature";s:39:"setStreamingOptimizationHeaders(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:200;s:7:"endLine";i:213;s:3:"ccn";i:2;}s:11:"outputChunk";a:6:{s:10:"methodName";s:11:"outputChunk";s:9:"signature";s:32:"outputChunk(string $chunk): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:225;s:7:"endLine";i:236;s:3:"ccn";i:2;}s:17:"isClientConnected";a:6:{s:10:"methodName";s:17:"isClientConnected";s:9:"signature";s:25:"isClientConnected(): bool";s:10:"visibility";s:9:"protected";s:9:"startLine";i:243;s:7:"endLine";i:246;s:3:"ccn";i:1;}s:21:"increaseExecutionTime";a:6:{s:10:"methodName";s:21:"increaseExecutionTime";s:9:"signature";s:29:"increaseExecutionTime(): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:253;s:7:"endLine";i:258;s:3:"ccn";i:2;}s:7:"cleanup";a:6:{s:10:"methodName";s:7:"cleanup";s:9:"signature";s:15:"cleanup(): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:265;s:7:"endLine";i:271;s:3:"ccn";i:2;}s:11:"getFilename";a:6:{s:10:"methodName";s:11:"getFilename";s:9:"signature";s:21:"getFilename(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:278;s:7:"endLine";i:281;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:21:"getMimeType(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:288;s:7:"endLine";i:291;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:293;s:18:"commentLinesOfCode";i:147;s:21:"nonCommentLinesOfCode";i:146;}s:15:"ignoredLinesFor";a:1:{i:0;i:12;}s:17:"executableLinesIn";a:38:{i:62;i:9;i:63;i:10;i:73;i:11;i:74;i:12;i:76;i:13;i:87;i:14;i:98;i:15;i:109;i:16;i:120;i:17;i:131;i:18;i:145;i:19;i:148;i:20;i:149;i:21;i:152;i:22;i:153;i:23;i:154;i:24;i:156;i:25;i:178;i:26;i:181;i:27;i:182;i:28;i:186;i:29;i:189;i:30;i:203;i:31;i:206;i:32;i:207;i:33;i:208;i:34;i:212;i:35;i:227;i:36;i:230;i:37;i:231;i:38;i:235;i:39;i:245;i:40;i:255;i:41;i:256;i:42;i:268;i:43;i:269;i:44;i:280;i:45;i:290;i:46;}}