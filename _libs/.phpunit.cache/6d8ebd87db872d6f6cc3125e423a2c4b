a:6:{s:9:"classesIn";a:1:{s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";a:6:{s:4:"name";s:23:"JsonExportFormatAdapter";s:14:"namespacedName";s:44:"Nzoom\Export\Adapter\JsonExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:13;s:7:"endLine";i:187;s:7:"methods";a:8:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:18;s:7:"endLine";i:81;s:3:"ccn";i:10;}s:14:"getJsonOptions";a:6:{s:10:"methodName";s:14:"getJsonOptions";s:9:"signature";s:35:"getJsonOptions(array $options): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:89;s:7:"endLine";i:121;s:3:"ccn";i:4;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:126;s:7:"endLine";i:129;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:134;s:7:"endLine";i:138;s:3:"ccn";i:1;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:143;s:7:"endLine";i:146;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:151;s:7:"endLine";i:154;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:159;s:7:"endLine";i:162;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:167;s:7:"endLine";i:186;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:188;s:18:"commentLinesOfCode";i:47;s:21:"nonCommentLinesOfCode";i:141;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:68:{i:18;i:1;i:21;i:2;i:24;i:3;i:25;i:4;i:26;i:5;i:31;i:6;i:34;i:7;i:35;i:8;i:37;i:9;i:39;i:10;i:40;i:11;i:43;i:12;i:44;i:13;i:47;i:14;i:51;i:15;i:54;i:16;i:55;i:17;i:56;i:18;i:60;i:19;i:61;i:20;i:62;i:21;i:63;i:22;i:66;i:23;i:67;i:24;i:68;i:25;i:72;i:26;i:74;i:27;i:75;i:28;i:79;i:29;i:91;i:30;i:94;i:31;i:95;i:31;i:96;i:33;i:98;i:34;i:99;i:35;i:103;i:36;i:104;i:36;i:105;i:38;i:107;i:39;i:108;i:40;i:112;i:41;i:113;i:41;i:114;i:43;i:116;i:44;i:117;i:45;i:120;i:46;i:128;i:47;i:137;i:48;i:145;i:49;i:153;i:50;i:161;i:51;i:169;i:52;i:170;i:52;i:171;i:52;i:172;i:52;i:173;i:52;i:174;i:52;i:175;i:52;i:176;i:52;i:177;i:52;i:178;i:52;i:179;i:52;i:180;i:52;i:181;i:52;i:182;i:52;i:183;i:52;i:184;i:52;i:185;i:52;}}