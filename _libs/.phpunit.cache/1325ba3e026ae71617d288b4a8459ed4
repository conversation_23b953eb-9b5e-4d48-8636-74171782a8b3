a:6:{s:9:"classesIn";a:1:{s:43:"Nzoom\Export\Adapter\CsvExportFormatAdapter";a:6:{s:4:"name";s:22:"CsvExportFormatAdapter";s:14:"namespacedName";s:43:"Nzoom\Export\Adapter\CsvExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:13;s:7:"endLine";i:192;s:7:"methods";a:9:{s:6:"export";a:6:{s:10:"methodName";s:6:"export";s:9:"signature";s:93:"export($file, string $type, Nzoom\Export\Entity\ExportData $exportData, array $options): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:23;s:7:"endLine";i:85;s:3:"ccn";i:10;}s:12:"getDelimiter";a:6:{s:10:"methodName";s:12:"getDelimiter";s:9:"signature";s:36:"getDelimiter(array $options): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:93;s:7:"endLine";i:112;s:3:"ccn";i:4;}s:18:"normalizeDelimiter";a:6:{s:10:"methodName";s:18:"normalizeDelimiter";s:9:"signature";s:45:"normalizeDelimiter(string $delimiter): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:120;s:7:"endLine";i:131;s:3:"ccn";i:5;}s:22:"getSupportedExtensions";a:6:{s:10:"methodName";s:22:"getSupportedExtensions";s:9:"signature";s:31:"getSupportedExtensions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:136;s:7:"endLine";i:139;s:3:"ccn";i:1;}s:11:"getMimeType";a:6:{s:10:"methodName";s:11:"getMimeType";s:9:"signature";s:35:"getMimeType(string $format): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:144;s:7:"endLine";i:148;s:3:"ccn";i:1;}s:19:"getDefaultExtension";a:6:{s:10:"methodName";s:19:"getDefaultExtension";s:9:"signature";s:29:"getDefaultExtension(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:153;s:7:"endLine";i:156;s:3:"ccn";i:1;}s:14:"supportsFormat";a:6:{s:10:"methodName";s:14:"supportsFormat";s:9:"signature";s:36:"supportsFormat(string $format): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:161;s:7:"endLine";i:164;s:3:"ccn";i:1;}s:13:"getFormatName";a:6:{s:10:"methodName";s:13:"getFormatName";s:9:"signature";s:23:"getFormatName(): string";s:10:"visibility";s:6:"public";s:9:"startLine";i:169;s:7:"endLine";i:172;s:3:"ccn";i:1;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:177;s:7:"endLine";i:191;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:193;s:18:"commentLinesOfCode";i:58;s:21:"nonCommentLinesOfCode";i:135;}s:15:"ignoredLinesFor";a:1:{i:0;i:13;}s:17:"executableLinesIn";a:59:{i:23;i:2;i:26;i:3;i:29;i:4;i:32;i:5;i:33;i:6;i:34;i:7;i:39;i:8;i:40;i:9;i:41;i:10;i:42;i:11;i:45;i:12;i:49;i:13;i:50;i:14;i:53;i:15;i:56;i:16;i:58;i:17;i:60;i:18;i:61;i:19;i:63;i:20;i:66;i:21;i:67;i:22;i:72;i:23;i:73;i:24;i:76;i:25;i:78;i:26;i:79;i:27;i:83;i:28;i:96;i:29;i:97;i:30;i:101;i:31;i:102;i:32;i:106;i:33;i:107;i:34;i:108;i:35;i:111;i:36;i:122;i:37;i:123;i:38;i:124;i:39;i:125;i:40;i:126;i:41;i:127;i:42;i:129;i:43;i:138;i:44;i:147;i:45;i:155;i:46;i:163;i:47;i:171;i:48;i:179;i:49;i:180;i:49;i:181;i:49;i:182;i:49;i:183;i:49;i:184;i:49;i:185;i:49;i:186;i:49;i:187;i:49;i:188;i:49;i:189;i:49;i:190;i:49;}}