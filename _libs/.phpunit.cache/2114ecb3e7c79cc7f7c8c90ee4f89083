a:6:{s:9:"classesIn";a:1:{s:48:"Nzoom\Export\Adapter\AbstractExportFormatAdapter";a:6:{s:4:"name";s:27:"AbstractExportFormatAdapter";s:14:"namespacedName";s:48:"Nzoom\Export\Adapter\AbstractExportFormatAdapter";s:9:"namespace";s:20:"Nzoom\Export\Adapter";s:9:"startLine";i:12;s:7:"endLine";i:316;s:7:"methods";a:12:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:67:"__construct(Registry $registry, string $module, string $controller)";s:10:"visibility";s:6:"public";s:9:"startLine";i:43;s:7:"endLine";i:52;s:3:"ccn";i:2;}s:16:"setConfiguration";a:6:{s:10:"methodName";s:16:"setConfiguration";s:9:"signature";s:82:"setConfiguration(array $config): Nzoom\Export\Adapter\ExportFormatAdapterInterface";s:10:"visibility";s:6:"public";s:9:"startLine";i:57;s:7:"endLine";i:61;s:3:"ccn";i:1;}s:17:"getExportFilename";a:6:{s:10:"methodName";s:17:"getExportFilename";s:9:"signature";s:53:"getExportFilename($prefix, string $extension): string";s:10:"visibility";s:9:"protected";s:9:"startLine";i:70;s:7:"endLine";i:91;s:3:"ccn";i:5;}s:11:"sendHeaders";a:6:{s:10:"methodName";s:11:"sendHeaders";s:9:"signature";s:56:"sendHeaders(string $filename, string $contentType): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:100;s:7:"endLine";i:118;s:3:"ccn";i:3;}s:17:"handleExportError";a:6:{s:10:"methodName";s:17:"handleExportError";s:9:"signature";s:57:"handleExportError(string $message, int $statusCode): void";s:10:"visibility";s:9:"protected";s:9:"startLine";i:127;s:7:"endLine";i:153;s:3:"ccn";i:4;}s:16:"getRecordHeaders";a:6:{s:10:"methodName";s:16:"getRecordHeaders";s:9:"signature";s:32:"getRecordHeaders($record): array";s:10:"visibility";s:9:"protected";s:9:"startLine";i:161;s:7:"endLine";i:176;s:3:"ccn";i:5;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:25:"getFormatOptions(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:181;s:7:"endLine";i:184;s:3:"ccn";i:1;}s:28:"validateAndPrepareSaveTarget";a:6:{s:10:"methodName";s:28:"validateAndPrepareSaveTarget";s:9:"signature";s:35:"validateAndPrepareSaveTarget($file)";s:10:"visibility";s:9:"protected";s:9:"startLine";i:193;s:7:"endLine";i:204;s:3:"ccn";i:3;}s:20:"validateStringTarget";a:6:{s:10:"methodName";s:20:"validateStringTarget";s:9:"signature";s:44:"validateStringTarget(string $target): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:213;s:7:"endLine";i:222;s:3:"ccn";i:2;}s:24:"validatePhpStreamWrapper";a:6:{s:10:"methodName";s:24:"validatePhpStreamWrapper";s:9:"signature";s:49:"validatePhpStreamWrapper(string $wrapper): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:231;s:7:"endLine";i:264;s:3:"ccn";i:5;}s:16:"validateFilePath";a:6:{s:10:"methodName";s:16:"validateFilePath";s:9:"signature";s:42:"validateFilePath(string $filePath): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:273;s:7:"endLine";i:286;s:3:"ccn";i:3;}s:19:"validateFilePointer";a:6:{s:10:"methodName";s:19:"validateFilePointer";s:9:"signature";s:33:"validateFilePointer($filePointer)";s:10:"visibility";s:7:"private";s:9:"startLine";i:295;s:7:"endLine";i:315;s:3:"ccn";i:4;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:317;s:18:"commentLinesOfCode";i:111;s:21:"nonCommentLinesOfCode";i:206;}s:15:"ignoredLinesFor";a:1:{i:0;i:12;}s:17:"executableLinesIn";a:92:{i:45;i:6;i:46;i:7;i:47;i:8;i:49;i:9;i:50;i:10;i:59;i:11;i:60;i:12;i:72;i:13;i:73;i:14;i:77;i:15;i:78;i:16;i:81;i:17;i:82;i:18;i:86;i:19;i:87;i:20;i:90;i:21;i:102;i:22;i:103;i:23;i:107;i:24;i:108;i:25;i:112;i:26;i:113;i:27;i:114;i:28;i:115;i:29;i:116;i:30;i:117;i:31;i:130;i:32;i:131;i:33;i:135;i:34;i:136;i:34;i:137;i:34;i:138;i:34;i:139;i:34;i:140;i:34;i:143;i:35;i:144;i:36;i:146;i:37;i:147;i:38;i:148;i:39;i:149;i:40;i:163;i:41;i:165;i:42;i:166;i:43;i:167;i:44;i:168;i:45;i:169;i:46;i:170;i:47;i:171;i:48;i:172;i:49;i:175;i:50;i:183;i:51;i:195;i:52;i:197;i:53;i:198;i:54;i:200;i:55;i:202;i:56;i:216;i:57;i:217;i:58;i:220;i:59;i:233;i:60;i:236;i:61;i:237;i:61;i:238;i:61;i:239;i:61;i:240;i:61;i:241;i:61;i:242;i:61;i:245;i:62;i:246;i:63;i:250;i:64;i:251;i:65;i:255;i:66;i:256;i:67;i:258;i:68;i:260;i:69;i:263;i:70;i:275;i:71;i:277;i:72;i:278;i:73;i:281;i:74;i:282;i:75;i:285;i:76;i:297;i:77;i:298;i:78;i:301;i:79;i:302;i:80;i:303;i:81;i:307;i:82;i:308;i:83;i:310;i:84;i:311;i:85;i:314;i:86;}}