a:6:{s:9:"classesIn";a:1:{s:32:"Nzoom\Export\ExportActionFactory";a:6:{s:4:"name";s:19:"ExportActionFactory";s:14:"namespacedName";s:32:"Nzoom\Export\ExportActionFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:11;s:7:"endLine";i:426;s:7:"methods";a:13:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:143:"__construct(Registry $registry, string $module, string $controller, ?string $modelName, ?string $modelFactoryName, Nzoom\I18n\I18n $translator)";s:10:"visibility";s:6:"public";s:9:"startLine";i:55;s:7:"endLine";i:77;s:3:"ccn";i:3;}s:12:"setModelName";a:6:{s:10:"methodName";s:12:"setModelName";s:9:"signature";s:31:"setModelName(string $modelName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:85;s:7:"endLine";i:89;s:3:"ccn";i:1;}s:19:"setModelFactoryName";a:6:{s:10:"methodName";s:19:"setModelFactoryName";s:9:"signature";s:45:"setModelFactoryName(string $modelFactoryName)";s:10:"visibility";s:6:"public";s:9:"startLine";i:97;s:7:"endLine";i:101;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:51:"__invoke(array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:110;s:7:"endLine";i:113;s:3:"ccn";i:1;}s:18:"createExportAction";a:6:{s:10:"methodName";s:18:"createExportAction";s:9:"signature";s:61:"createExportAction(array $types, array $typeSections): ?array";s:10:"visibility";s:6:"public";s:9:"startLine";i:122;s:7:"endLine";i:129;s:3:"ccn";i:1;}s:20:"prepareExportOptions";a:6:{s:10:"methodName";s:20:"prepareExportOptions";s:9:"signature";s:78:"prepareExportOptions(array $types, array $sections, array $filtersHide): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:141;s:7:"endLine";i:169;s:3:"ccn";i:1;}s:26:"initializeFilterVisibility";a:6:{s:10:"methodName";s:26:"initializeFilterVisibility";s:9:"signature";s:53:"initializeFilterVisibility(array $filtersHide): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:177;s:7:"endLine";i:186;s:3:"ccn";i:3;}s:11:"firstOrZero";a:6:{s:10:"methodName";s:11:"firstOrZero";s:9:"signature";s:32:"firstOrZero(array $subject): int";s:10:"visibility";s:7:"private";s:9:"startLine";i:194;s:7:"endLine";i:200;s:3:"ccn";i:2;}s:16:"getPluginOptions";a:6:{s:10:"methodName";s:16:"getPluginOptions";s:9:"signature";s:64:"getPluginOptions(int $selectedType, int $selectedSection): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:209;s:7:"endLine";i:270;s:3:"ccn";i:4;}s:22:"buildBaseExportOptions";a:6:{s:10:"methodName";s:22:"buildBaseExportOptions";s:9:"signature";s:85:"buildBaseExportOptions(array $filtersHide, array $types, int $selectedSection): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:280;s:7:"endLine";i:321;s:3:"ccn";i:4;}s:16:"getFormatOptions";a:6:{s:10:"methodName";s:16:"getFormatOptions";s:9:"signature";s:43:"getFormatOptions(array $filtersHide): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:329;s:7:"endLine";i:357;s:3:"ccn";i:2;}s:21:"getGroupTablesOptions";a:6:{s:10:"methodName";s:21:"getGroupTablesOptions";s:9:"signature";s:30:"getGroupTablesOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:364;s:7:"endLine";i:389;s:3:"ccn";i:1;}s:19:"getDelimiterOptions";a:6:{s:10:"methodName";s:19:"getDelimiterOptions";s:9:"signature";s:46:"getDelimiterOptions(array $filtersHide): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:397;s:7:"endLine";i:425;s:3:"ccn";i:2;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:427;s:18:"commentLinesOfCode";i:122;s:21:"nonCommentLinesOfCode";i:305;}s:15:"ignoredLinesFor";a:1:{i:0;i:11;}s:17:"executableLinesIn";a:196:{i:64;i:8;i:65;i:9;i:66;i:10;i:68;i:11;i:69;i:12;i:72;i:13;i:73;i:14;i:76;i:15;i:87;i:16;i:88;i:17;i:99;i:18;i:100;i:19;i:112;i:20;i:124;i:21;i:125;i:21;i:126;i:21;i:127;i:21;i:128;i:21;i:142;i:22;i:143;i:23;i:144;i:24;i:148;i:25;i:150;i:26;i:151;i:27;i:153;i:28;i:155;i:29;i:156;i:29;i:157;i:29;i:161;i:30;i:162;i:30;i:163;i:30;i:164;i:30;i:165;i:30;i:166;i:30;i:168;i:31;i:179;i:32;i:180;i:33;i:181;i:34;i:185;i:35;i:196;i:36;i:197;i:37;i:199;i:38;i:212;i:39;i:213;i:39;i:214;i:39;i:215;i:39;i:216;i:39;i:219;i:40;i:221;i:41;i:222;i:42;i:225;i:43;i:226;i:44;i:227;i:44;i:228;i:44;i:229;i:44;i:230;i:44;i:231;i:44;i:233;i:45;i:236;i:46;i:237;i:46;i:238;i:46;i:239;i:46;i:242;i:47;i:243;i:48;i:244;i:48;i:245;i:48;i:246;i:48;i:250;i:49;i:251;i:49;i:252;i:49;i:253;i:49;i:254;i:49;i:255;i:49;i:256;i:49;i:257;i:49;i:258;i:49;i:259;i:49;i:260;i:49;i:261;i:49;i:262;i:49;i:263;i:49;i:264;i:49;i:265;i:49;i:266;i:49;i:267;i:49;i:268;i:49;i:269;i:49;i:283;i:50;i:284;i:50;i:285;i:50;i:286;i:50;i:287;i:50;i:288;i:50;i:289;i:50;i:290;i:50;i:291;i:50;i:292;i:50;i:293;i:50;i:294;i:50;i:295;i:50;i:296;i:50;i:297;i:50;i:298;i:50;i:299;i:50;i:300;i:50;i:301;i:50;i:304;i:51;i:305;i:52;i:306;i:52;i:307;i:52;i:308;i:52;i:309;i:52;i:310;i:52;i:311;i:53;i:312;i:54;i:313;i:54;i:314;i:54;i:315;i:54;i:316;i:54;i:317;i:54;i:320;i:55;i:331;i:56;i:332;i:56;i:333;i:56;i:334;i:56;i:335;i:56;i:336;i:56;i:337;i:56;i:338;i:56;i:339;i:56;i:340;i:56;i:341;i:56;i:342;i:56;i:343;i:56;i:344;i:56;i:345;i:56;i:346;i:56;i:347;i:56;i:348;i:56;i:349;i:56;i:350;i:56;i:351;i:56;i:352;i:56;i:353;i:56;i:354;i:56;i:355;i:56;i:356;i:56;i:366;i:57;i:367;i:57;i:368;i:57;i:369;i:57;i:370;i:57;i:371;i:57;i:372;i:57;i:373;i:57;i:374;i:57;i:375;i:57;i:376;i:57;i:377;i:57;i:378;i:57;i:379;i:57;i:380;i:57;i:381;i:57;i:382;i:57;i:383;i:57;i:384;i:57;i:385;i:57;i:386;i:57;i:387;i:57;i:388;i:57;i:399;i:58;i:400;i:58;i:401;i:58;i:402;i:58;i:403;i:58;i:404;i:58;i:405;i:58;i:406;i:58;i:407;i:58;i:408;i:58;i:409;i:58;i:410;i:58;i:411;i:58;i:412;i:58;i:413;i:58;i:414;i:58;i:415;i:58;i:416;i:58;i:417;i:58;i:418;i:58;i:419;i:58;i:420;i:58;i:421;i:58;i:422;i:58;i:423;i:58;i:424;i:58;}}