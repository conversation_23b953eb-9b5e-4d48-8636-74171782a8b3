<?php

/**
 * @method static Model[] search(\Registry $registry, array $filters) Search for models with the given filters
 */
Class Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName;

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Table alias to be used in search methods
     */
    public static $alias;

    /**
     * Creates new model out of specified params and class name
     *
     * @param object $registry - the main registry object
     * @param array $params - array of params to be stored in model object
     * @param string $className - the class name
     * @return Model $model - a model object
     */
    public static function newModel(&$registry, $params, $className) {
        return new $className($registry, $params);
    }

    /**
     * Creates new model out of specified params and class name
     *
     * @param object $registry - the main registry object
     * @param array $records - array of model data records usually fetched from the DB
     * @param string $modelName - the class name of the model
     * @return array $models - array of model objects
     */
    public static function createModels(&$registry, $records = array(), $modelName, $sanitize = false) {
        $models = array();

        if (is_array($records)) {
            foreach ($records as $record) {
                $model = self::newModel($registry, $record, $modelName);
                if ($model->valid) {
                    if (!empty($registry['prepareModels'])) {
                        $model->prepare();
                    }
                    if ($sanitize) {
                        $models[] = $model->sanitize();
                    } else {
                        $models[] = $model;
                    }
                }
            }
        }

        return $models;
    }

    /**
     * Creates new model out of request (post and get)
     *
     * @param object $registry - the main registry object
     * @param string $modelName - the class name of the model
     * @param array $allowedRequestParams - contains the allowed parameters to be passed from request
                                            when it is empty the entire request is passed to the model
     * @return Model - a model object
     */
    public static function buildFromRequest(&$registry, $modelName, $allowedRequestParams = array()) {
        $data = array();

        $request = $registry['request']->getAll();
        if (!empty($allowedRequestParams)) {
            foreach ($allowedRequestParams as $param) {
                if (isset($request[$param])) {
                    $data[$param] = $request[$param];
                }
            }
        } else {
            $data = $request;
        }
        $data['_origin'] = 'request';
        $model = self::newModel($registry, $data, $modelName);

        return $model;
    }

    /**
     * Creates new model out of request (post and get)
     *
     * @param object $registry - the main registry object
     * @param string $modelName - the class name of the model
     * @param string $index - index of post (get) array
     * @return Model - a model object
     */
    public static function buildFromRequestIndex(&$registry, $modelName, $index) {

        $data = $registry['request']->getAll();
        foreach ($data as $key => $val) {
            if (is_array($val)) {
                $data[$key] = @$val[$index];
            }
        }
        $data['_origin'] = 'request';
        $model = self::newModel($registry, $data, $modelName);

        return $model;
    }

    /**
     * Clears all redundant data from a model or list of model objects
     *
     * @param mixed $models - array of model objects or single model object
     * @return mixed - sanitized(cleared) model(s)
     */
    public static function sanitizeModels($models) {
        $list = array();

        if (is_array($models)) {
            foreach ($models as $model) {
                $list[] = $model->sanitize();
            }
        } elseif (is_object($models)) {
            return $models->sanitize();
        }

        return $list;
    }

    /**
     * Calculate GT2 rows
     *
     * @param object $registry - the main registry object
     * @param array $values - data from GT2 rows
     * @param float $total_vat_rate - VAT rate to use for calculations
     * @return array - GT2 rows after processing
     */
    public static function calculateGT2Rows(&$registry, $values = array(), $total_vat_rate = 20) {
        $prec = $registry['config']->getSectionParams('precision');
        $prec_formats = array();
        foreach ($prec as $item => $format) {
            $prec_formats[$item] = '%.' . $format . 'F';
        }
        foreach ($values as $row => $vals) {

            if (empty($vals) || !empty($vals['deleted'])) {
                continue;
            }

            // check if we have discount or surplus and if it is provided as percent or absolute value
            if (isset($vals['discount_surplus_field'])) {
                if ($vals['discount_surplus_field'] == 'discount_percentage') {
                    $vals['discount_value'] = ($vals["price"] * $vals['discount_percentage']) / 100;
                    $vals['surplus_value'] = $vals['surplus_percentage'] = 0;
                } elseif ($vals['discount_surplus_field'] == 'discount_value') {
                    $vals['discount_percentage'] = ($vals['discount_value'] / $vals["price"]) * 100;
                    $vals['surplus_value'] = $vals['surplus_percentage'] = 0;
                } elseif ($vals['discount_surplus_field'] == 'surplus_percentage') {
                    $vals['surplus_value'] = ($vals["price"] * $vals['surplus_percentage']) / 100;
                    $vals['discount_value'] = $vals['discount_percentage'] = 0;
                } elseif ($vals['discount_surplus_field'] == 'surplus_value') {
                    $vals['surplus_percentage'] = ($vals['surplus_value'] / $vals["price"]) * 100;
                    $vals['discount_value'] = $vals['discount_percentage'] = 0;
                }
                if (bccomp($vals['discount_value'], 0, 6) == 0 && bccomp($vals['surplus_value'], 0, 6) == 0 &&
                    bccomp($vals['discount_percentage'], 0, 6) == 0 && bccomp($vals['surplus_percentage'], 0, 6) == 0) {
                        // we have nothing
                        $vals['discount_surplus_field'] = 'none';
                }
            } else {
                $vals['discount_surplus_field'] = 'none';
            }
            if (!isset($vals['discount_value'])) {
                $vals['discount_value'] = 0;
            }
            if (!isset($vals['discount_percentage'])) {
                $vals['discount_percentage'] = 0;
            }
            if (!isset($vals['surplus_value'])) {
                $vals['surplus_value'] = 0;
            }
            if (!isset($vals['surplus_percentage'])) {
                $vals['surplus_percentage'] = 0;
            }

            if (!isset($vals["average_weighted_delivery_price"])) {
                $vals["average_weighted_delivery_price"] = 0;
            }

            //calculate row values
            $vals['price'] = sprintf($prec_formats['gt2_rows'], round($vals["price"], $prec['gt2_rows']));
            $vals['quantity'] = sprintf($prec_formats['gt2_quantity'], round($vals["quantity"], $prec['gt2_quantity']));
            $vals['discount_value'] = sprintf($prec_formats['gt2_rows'], round($vals['discount_value'], $prec['gt2_rows']));
            $vals['discount_percentage'] = sprintf($prec_formats['gt2_rows'], round($vals['discount_percentage'], $prec['gt2_rows']));
            $vals['surplus_value'] = sprintf($prec_formats['gt2_rows'], round($vals['surplus_value'], $prec['gt2_rows']));
            $vals['surplus_percentage'] = sprintf($prec_formats['gt2_rows'], round($vals['surplus_percentage'], $prec['gt2_rows']));
            $vals['price_with_discount'] = sprintf($prec_formats['gt2_rows'], round(($vals["price"] - $vals['discount_value'] + $vals['surplus_value']), $prec['gt2_rows']));
            $vals['vat_value'] = sprintf($prec_formats['gt2_rows'], round(($vals["price"] * $total_vat_rate / 100), $prec['gt2_rows']));
            $vals['profit_no_final_discount'] = sprintf($prec_formats['gt2_rows'], round(($vals["price"] - $vals['discount_value'] + $vals['surplus_value'] - $vals["average_weighted_delivery_price"]), $prec['gt2_rows']));
            $vals['subtotal'] = sprintf($prec_formats['gt2_rows'], round(($vals["price"] * $vals["quantity"]), $prec['gt2_rows']));
            $vals['subtotal_profit_no_final_discount'] = sprintf($prec_formats['gt2_rows'], round((($vals["price"] - $vals['discount_value'] + $vals['surplus_value'] - $vals["average_weighted_delivery_price"]) * $vals["quantity"]), $prec['gt2_rows']));
            $vals['subtotal_discount_value'] = sprintf($prec_formats['gt2_rows'], round((($vals['discount_value'] - $vals['surplus_value']) * $vals["quantity"]), $prec['gt2_rows']));
            $vals['subtotal_with_discount'] = sprintf($prec_formats['gt2_rows'], round((($vals["price"] - $vals['discount_value'] + $vals['surplus_value']) * $vals["quantity"]), $prec['gt2_rows']));
            $vals['subtotal_with_vat'] = sprintf($prec_formats['gt2_rows'], round(($vals['price'] * $vals['quantity'] * (100 + $total_vat_rate) / 100), $prec['gt2_rows']));
            $vals['subtotal_with_vat_with_discount'] = sprintf($prec_formats['gt2_rows'], round((($vals['price'] - $vals['discount_value'] + $vals['surplus_value']) * $vals['quantity'] * (100 + $total_vat_rate) / 100), $prec['gt2_rows']));

            $values[$row] = $vals;
        }

        return $values;
    }

    /**
     * Merges search filters from session, request and custom filters
     * into one array and saves them in session
     *
     * @param object $registry - the main registry object
     * @param string $sessionParam - session section index to save the filters into
     * @param array $filters - custom filters
     * @return array $search - array of search params
     */
    public static function saveSearchFilters(&$registry, $sessionParam, $filters = array()) {

        $params = array();
        $search_fields = array();
        $filters_to_save = array ();
        $session_param = '';
        require_once PH_MODULES_DIR . 'filters/models/filters.factory.php';

        if ($search_module = $registry['request']->get('search_module')) {
            if ($search_controller = $registry['request']->get('search_controller')) {
            } else {
                $search_controller = $search_module;
            }
        } else {
            $search_module = $registry->get('module');
            $search_controller = $registry->get('controller');
        }

        // exception for filter for contact persons
        if ($search_module == 'customers' && $search_module == $search_controller && $sessionParam == 'filter_customers_contactperson') {
            $search_controller = 'contactpersons';
        }

        //save filters if requested - AJAX
        if ($registry['request']->get('filters_action') == 'savefilter') {
            if ($registry['request']->get('save_filter_as')) {

                //save filter
                $saved_filter = Filters::buildModel($registry);
                $saved_filter->save();

                $models = Filters::search($registry, array('where' => array ('f.module = \'' . $search_module . '\'',
                                                                             'f.controller = \'' . $search_controller . '\'',
                                                                             'f.user_defined = 1',
                                                                             'f.added_by = ' . $registry['currentUser']->get('id') . ' OR',
                                                                             'f.added_by = 1'),
                                                           'sanitize' => 1));
                $saved_filters = array();
                foreach ($models as $model) {
                    $saved_filters[] = array('option_value' => $model->get('id'),
                                             'label' => $model->get('name'));
                }
                echo 'result = ' . json_encode($saved_filters) . '; selected_filter = ' . ($saved_filter->get('id') ?: '0');exit;
            } else {
                echo 'result =\'' . $registry['translater']->translate('error_no_filter_name_specified') . '\'';exit;
            }
        }

        //delete filter if requested - AJAX
        if ($registry['request']->get('filters_action') == 'delfilter') {
            if ($registry['request']->get('filter_name')) {
                //deletion of saved filter
                Filters::purge($registry, array('id' => $registry['request']->get('filter_name')));

                $models = Filters::search($registry, array('where' => array ('f.module = \'' . $search_module . '\'',
                                                                             'f.controller = \'' . $search_controller . '\'',
                                                                             'f.user_defined = 1',
                                                                             'f.added_by = ' . $registry['currentUser']->get('id') . ' OR',
                                                                             'f.added_by = 1'),
                                                           'sanitize' => 1));
                $saved_filters = array();
                foreach ($models as $model) {
                    $saved_filters[] = array('option_value' => $model->get('id'),
                                            'label' => $model->get('name'));
                }
                echo 'result = ' . json_encode($saved_filters) . '; selected_filter = \'\''; exit;
            } else {
                echo 'result = \'' . $registry['translater']->translate('error_no_filter_name_specified') . '\'';exit;
            }
        }

        if ($registry['request']['filters_action'] == 'loadfilter' && $registry['request']['filter_name']) {
            //search is initialized
            //so clear selected items for the current session param
            $registry['session']->remove($sessionParam, 'selected_items');

            //load filters from the database if requested
            $filter_id = $registry['request']['filter_name'];
            $saved_filter = Filters::searchOne($registry, array('where' => array ('f.id = ' . $filter_id),
                                                                'sanitize' => 1));
            if (!empty($saved_filter)) {
                $saved_filter = General::slashesStrip($saved_filter->getParams());
            } else {
                $saved_filter = array('search_fields' => array(), 'sort' => array());
            }

            //get search definitions for the current module and controller,
            //check which are requested and prepare search filters from them
            $params = array();

            //iterate through the array and get all the definitions from the REQUEST
            $search_fields = $saved_filter['search_fields'];
            if (!$search_fields) {
                return array();
            }
            ksort($search_fields, SORT_NUMERIC);

            $type_sections_counter = 0;
            $additional_hidden_filters = array();
            foreach ($search_fields as $key => $field) {
                if ($field && isset($saved_filter['values'][$key])) {
                    if (!empty($saved_filter['values'][$key])) {
                        // escape the value for the SQL search query
                        $saved_filter['values'][$key] = General::slashesEscape($saved_filter['values'][$key]);
                        if (preg_match('#DATE_(SUB|ADD)[^%]*%d %s#', $saved_filter['compare_options'][$key])) {
                            //relative date comparison (period)
                            $condition = preg_split('#\s+AND\s+#', $saved_filter['compare_options'][$key]);
                            $condition[0] = sprintf($condition[0], $saved_filter['values'][$key], $saved_filter['date_period'][$key]);
                            $condition = implode(' AND ', $condition);
                        } elseif (preg_match('/%s/', $saved_filter['compare_options'][$key])) {
                            if (!preg_match('#deleted$#', $field)) {
                                $condition = sprintf($saved_filter['compare_options'][$key], $saved_filter['values'][$key]);
                            } else {
                                $condition = $saved_filter['values'][$key];
                            }
                        } else {
                            if (!preg_match('#deleted$#', $field)) {
                                $condition = $saved_filter['compare_options'][$key] . ' ' . $saved_filter['values'][$key];
                            } else {
                                $condition = $saved_filter['values'][$key];
                            }
                        }
                        // strip the value for the saved session filters
                        $saved_filter['values'][$key] = General::slashesStrip($saved_filter['values'][$key]);
                    } else {
                        if (preg_match('#DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'[^\']*\'\s*\)#', $saved_filter['compare_options'][$key])) {
                            //relative date comparison(current mont/week/year)
                            $date_format = preg_replace('#DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'([^\']*)\'\s*\)#', '$1', $saved_filter['compare_options'][$key]);
                        }
                        if (preg_match('/%s/', $saved_filter['compare_options'][$key])) {
                            if (!preg_match('#deleted$#', $field)) {
                                $condition = sprintf($saved_filter['compare_options'][$key], $saved_filter['values'][$key]);
                            } else {
                                $condition = $saved_filter['values'][$key];
                            }
                        } else {
                            if (!preg_match('#deleted$#', $field)) {
                                $condition = $saved_filter['compare_options'][$key] . ' ' . $saved_filter['values'][$key];
                            } else {
                                $condition = $saved_filter['values'][$key];
                            }
                        }
                    }
                    if (preg_match('#\[week_day\]#', $condition)) {
                        $condition = preg_replace('#\[week_day\]#', '', $condition);
                        $params['where'][$key] = 'DATE_FORMAT(' .
                                     preg_replace('#DATE_FORMAT\s*\(\s*(.*)\s*,.*#', '$1', $field) . ', \'%w\')';
                    } elseif (preg_match('#\[(day|month|year)\]#', $condition, $m)) {
                        //%d - day, %m - month, %Y - year
                        $format = substr($m[1] == 'year' ? strtoupper($m[1]) : strtolower($m[1]), 0, 1);
                        $condition = preg_replace('#\[' . $m[1] . '\]#', '', $condition);
                        $params['where'][$key] = 'DATE_FORMAT(' .
                                     preg_replace('#DATE_FORMAT\s*\(\s*(.*)\s*,.*#', '$1', $field) . ', \'%' . $format . '\')';
                    } else {
                        $params['where'][$key] = $field;
                    }
                    $params['where'][$key] .= ' ' . $condition;
                    $params['where'][$key] .= isset($saved_filter['logical_operator'][$key]) ? ' ' . $saved_filter['logical_operator'][$key] : ' AND';

                    //prepare filters to be saved in the session
                    $filters_to_save['search_fields'][$key] = $field;
                    $filters_to_save['compare_options'][$key] = $saved_filter['compare_options'][$key];
                    $filters_to_save['values'][$key] = $saved_filter['values'][$key];
                    $filters_to_save['date_period'][$key] = @$saved_filter['date_period'][$key];
                    $filters_to_save['logical_operator'][$key] = isset($saved_filter['logical_operator'][$key]) ? $saved_filter['logical_operator'][$key] : 'AND';

                    //for autocompleters
                    if (isset($saved_filter['values_autocomplete'][$key])) {
                        $filters_to_save['values_autocomplete'][$key] = $saved_filter['values_autocomplete'][$key];
                    }
                    if (!empty($saved_filter['date_period'][$key])) {
                        $filters_to_save['date_period'][$key] = $saved_filter['date_period'][$key];
                    }

                    //check if there are type section/type/category criteria saved
                    if (preg_match('#^[^\.]*\.(type_section|type|category)#', $field)) {
                        $filter = preg_replace('#^[^\.]*\.(type_section|type|category)#', '$1', $saved_filter['search_fields'][$key]);
                        $additional_hidden_filters[] = array('hidden_' . $filter => $saved_filter['values'][$key]);
                        // increments counter if the current filter matches search
                        $type_sections_counter++;
                    }
                } elseif ($field && !isset($saved_filter['values'][$key])) {
                    if (preg_match('#\s*=\s*DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'[^\']*\'\s*\)#',
                                   $saved_filter['compare_options'][$key])) {
                        //relative date comparison(current month/week/year)
                        $date_format = preg_replace('#\s*=\s*DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'([^\']*)\'\s*\)#',
                                                    '$1', $saved_filter['compare_options'][$key]);
                        $params['where'][$key] = 'DATE_FORMAT(';
                        $params['where'][$key] .= preg_replace('#DATE_FORMAT\s*\(\s*(.*)\s*,.*#', '$1', $field);
                        $params['where'][$key] .= ', \'' . $date_format . '\')';
                    } else {
                        $params['where'][$key] = $field;
                    }
                    $params['where'][$key] .= ' ' . $saved_filter['compare_options'][$key];
                    $params['where'][$key] .= isset($saved_filter['logical_operator'][$key]) ? ' ' . $saved_filter['logical_operator'][$key] : ' AND';

                    //prepare filters to be saved in the session
                    $filters_to_save['search_fields'][$key] = $field;
                    $filters_to_save['compare_options'][$key] = $saved_filter['compare_options'][$key];
                    $filters_to_save['logical_operator'][$key] = isset($saved_filter['logical_operator'][$key]) ? $saved_filter['logical_operator'][$key] : 'AND';
                }
            }
            //if there is only one type section/type/category parameter, set it as a simple search parameter as well
            if ($type_sections_counter == 1) {
                $filters_to_save = array_merge($filters_to_save, $additional_hidden_filters[0]);
            }

            //prepare sort, order and pagination as they are a little bit special
            $sort = array();
            if ($saved_filter['sort']) {
                $sort_fields = $saved_filter['sort'];
                ksort($sort_fields, SORT_NUMERIC);
                foreach ($sort_fields as $key => $sort_field) {
                    if (isset($saved_filter['order'][$key])) {
                        $saved_filter['order'][$key] = in_array(strtoupper($saved_filter['order'][$key]), ['ASC', 'DESC']) ?
                            strtoupper($saved_filter['order'][$key]) : 'ASC';
                        $sort[$key] = $sort_field . ' ' . $saved_filter['order'][$key];
                    } else {
                        $sort[$key] = $sort_field . ' ASC';
                        $saved_filter['order'][$key] = 'ASC';
                    }
                }
                $filters_to_save['sort'] = $saved_filter['sort'];
                $filters_to_save['order'] = $saved_filter['order'];
            }
            $params['sort'] = $sort;


            if (isset($order_def[$key])) {
                $order_def[$key] = in_array(strtoupper($order_def[$key]), ['ASC', 'DESC']) ?
                    strtoupper($order_def[$key]) : 'ASC';
                $sort[$key] = $sort_field . ' ' . $order_def[$key];
            } else {
                $sort[$key] = $sort_field . ' ASC';
                $order_def[$key] = 'ASC';
            }


            //prepare pagination
            $var_name = 'list_' . $registry['module'];
            if ($registry['module'] != $registry['controller']) {
                $var_name = '_' . $registry['controller'];
            }
            if (preg_match('#comments|communications#', $var_name) ||
                @preg_match('#comments|communications#', $sessionParam) ||
                @preg_match('#comments|communications#', $session_param) ||
                $registry['action'] == 'comments' || $registry['action'] == 'communications') {
                $var_name = 'list_communications';
            }
            if (preg_match('#timesheets#', $var_name) || @preg_match('#timesheets#', $sessionParam) ||
                @preg_match('#timesheets#', $session_param) || $registry['action'] == 'timesheets') {
                $var_name = 'list_timesheets';
            }
            $default_rpp = $registry['currentUser']->getPersonalSettings('interface', $var_name);
            if (isset($saved_filter['display'])) {
                $params['display'] = $saved_filter['display'];
            } elseif ($default_rpp) {
                $params['display'] = $default_rpp;
            } else {
                $params['display'] = 10;
            }
            $filters_to_save['display'] = $params['display'];
        } else {

            //get search definitions for the current module and controller,
            //check which are requested and prepare search filters from them

            //first check for simple search in REQUEST
            if ($registry['request']->isRequested('key')) {
                $filters_to_save['key'] = $registry['request']['key'];
                $filters_to_save['field'] = $registry['request']['field'];
                // escape the value for the SQL search query
                $params['key'] = General::slashesEscape($registry['request']['key']);
                $params['field'] = $registry['request']['field'];

                //check if type/section/category is required
                if ($registry['request']['hidden_type']) {
                    $params['hidden_type'] = $registry['request']['hidden_type'];
                    $filters_to_save['hidden_type'] = $registry['request']['hidden_type'];
                } elseif ($registry['request']['hidden_type_section']) {
                    $params['hidden_type_section'] = $registry['request']['hidden_type_section'];
                    $filters_to_save['hidden_type_section'] = $registry['request']['hidden_type_section'];
                } elseif ($registry['request']['hidden_category']) {
                    $params['hidden_category'] = $registry['request']['hidden_category'];
                    $filters_to_save['hidden_category'] = $registry['request']['hidden_category'];
                }

                $sort_def = isset($search_defs['sort']) ? $search_defs['sort'] : array();
                $order_def = isset($search_defs['order']) ? $search_defs['order'] : array();
                if ($registry['request']->isRequested('sort')) {
                    $sort_def = is_array($registry['request']->get('sort')) ?
                        $registry['request']->get('sort') :
                        array($registry['request']->get('sort'));
                }
                if ($registry['request']->isRequested('order')) {
                    $order_def = is_array($registry['request']->get('order')) ?
                        $registry['request']->get('order') :
                        array($registry['request']->get('order'));
                }
                $display_def = ($registry['request']->isRequested('display')) ? $registry['request']->get('display') : 10;
            }

            if ($registry['request']->isRequested('search_fields')) {
                //else get advanced definitions from the REQUEST
                $search_fields = $registry['request']['search_fields'];
                ksort($search_fields, SORT_NUMERIC);
                $values = $registry['request']['values']?:[];
                $compare_options = $registry['request']['compare_options']?:[];
                $logical_operator = $registry['request']['logical_operator']?:[];

                // Replace a placeholder value for empty string. (EJ2 can't handle empty string as a value,
                // so I need to send something else, that will represent an actual empty string)
                $emptyStrReplacer = function (&$value, $key) {
                    $value = str_replace('<emptystr>', '', $value);
                };
                array_walk($search_fields, $emptyStrReplacer);
                array_walk($values, $emptyStrReplacer);
                array_walk($compare_options, $emptyStrReplacer);
                array_walk($logical_operator, $emptyStrReplacer);
                //for auto completers
                if ($registry['request']->isRequested('values_autocomplete')) {
                    $values_autocomplete = $registry['request']->get('values_autocomplete')?:[];
                    array_walk($values_autocomplete, $emptyStrReplacer);
                }
                // for relative date search
                if ($registry['request']->isRequested('date_period')) {
                    $values_date_period = $registry['request']->get('date_period');
                }

                $sort_def = isset($search_defs['sort']) ? $search_defs['sort'] : array();
                $order_def = isset($search_defs['order']) ? $search_defs['order'] : array();
                if ($registry['request']->isRequested('sort')) {
                    $sort_def = is_array($registry['request']->get('sort')) ?
                        $registry['request']->get('sort') :
                        array($registry['request']->get('sort'));
                }
                if ($registry['request']->isRequested('order')) {
                    $order_def = is_array($registry['request']->get('order')) ?
                        $registry['request']->get('order') :
                        array($registry['request']->get('order'));
                }
                $display_def = ($registry['request']->isRequested('display')) ? $registry['request']->get('display') : array();

                //if there is only one type section/type/category parameter, set it as a simple search parameter as well
                $simple_param = '';
                $found = array();
                foreach ($search_fields as $key => $value) {
                    //check if type/section/category is required
                    if (preg_match('#^[^\.]*\.(type_section|type|category)#', $value, $matches)) {
                        $simple_param = $matches[1];
                        $found[] = $key;
                    }
                }
                if (count($found) == 1) {
                    $params['hidden_' . $simple_param] = $values[$found[0]];
                    $filters_to_save['hidden_' . $simple_param] = $values[$found[0]];
                }
            } elseif (!$registry['request']->isRequested('key') && ($registry['session']->get($sessionParam) || $registry['request']->get('session_param_prefix'))) {
                //else get definitions from the SESSION
                if ($registry['request']->get('session_param_prefix')) {
                    if ($registry['module'] == $registry['controller']) {
                        $model_name = General::plural2singular($registry['module']);
                    } else {
                        $model_name = $registry['module'] . '_' . General::plural2singular($registry['controller']);
                    }
                    $session_param = strtolower($registry['request']->get('session_param_prefix') . '_' . $model_name);
                    $search_defs = $registry['session']->get($session_param);
                } else {
                    $search_defs = $registry['session']->get($sessionParam);
                }

                if (isset($search_defs['search_fields']) && !isset($filters['where']) && !(isset($search_defs['key']))) {
                    //get advanced definitions
                    $search_fields = $search_defs['search_fields'];
                    $values = isset($search_defs['values']) ? $search_defs['values'] : array();
                    if (isset($search_defs['values_autocomplete'])) {
                        $values_autocomplete = $search_defs['values_autocomplete'];
                    }

                    $values_date_period = isset($search_defs['date_period']) ? $search_defs['date_period'] : array();
                    $compare_options = isset($search_defs['compare_options']) ? $search_defs['compare_options'] : array();
                    $logical_operator = isset($search_defs['logical_operator']) ? $search_defs['logical_operator'] : array();
                } elseif (isset($search_defs['key'])) {
                    //get simple definitions
                    $filters_to_save['key'] = $search_defs['key'];
                    $filters_to_save['field'] = $search_defs['field'];
                    $params['key'] = $search_defs['key'];
                    $params['field'] = $search_defs['field'];

                    // get advanced search definitions for type/section/category
                    if (isset($search_defs['search_fields'])) {
                        $search_fields = $search_defs['search_fields'];
                        $values = isset($search_defs['values']) ? $search_defs['values'] : array();
                        $compare_options = isset($search_defs['compare_options']) ? $search_defs['compare_options'] : array();
                        $logical_operator = isset($search_defs['logical_operator']) ? $search_defs['logical_operator'] : array();
                    } else {
                        $params['key'] = General::slashesEscape($search_defs['key']);
                    }
                }

                if (isset($search_defs['hidden_type'])) {
                    $params['hidden_type'] = $search_defs['hidden_type'];
                    $filters_to_save['hidden_type'] = $search_defs['hidden_type'];
                }
                if (isset($search_defs['hidden_type_section'])) {
                    $params['hidden_type_section'] = $search_defs['hidden_type_section'];
                    $filters_to_save['hidden_type_section'] = $search_defs['hidden_type_section'];
                }
                if (isset($search_defs['hidden_category'])) {
                    $params['hidden_category'] = $search_defs['hidden_category'];
                    $filters_to_save['hidden_category'] = $search_defs['hidden_category'];
                }

                //if column sort is requested - sort and order are strings OR
                //if filters form is requested with no enabled filters - sort and order are arrays
                $sort_def = isset($search_defs['sort']) ? $search_defs['sort'] : array();
                $order_def = isset($search_defs['order']) ? $search_defs['order'] : array();
                if ($registry['request']->isRequested('sort')) {
                    $sort_def = is_array($registry['request']->get('sort')) ?
                        $registry['request']->get('sort') :
                        array($registry['request']->get('sort'));
                }
                if ($registry['request']->isRequested('order')) {
                    $order_def = is_array($registry['request']->get('order')) ?
                        $registry['request']->get('order') :
                        array($registry['request']->get('order'));
                }
                $display_def = ($registry['request']->isRequested('display')) ? $registry['request']->get('display') : $search_defs['display']??'';
            } elseif (!$registry['request']->isRequested('key') && $registry['request']->get('category')) {
                //keep category for events
                $params['hidden_category'] = $registry['request']['category'];
                $filters_to_save['hidden_category'] = $registry['request']['category'];
                if ($registry['request']->isRequested('display')) {
                    $display_def = $registry['request']['display'];
                }
            }
            if ($search_fields) {
                ksort($search_fields, SORT_NUMERIC);
                foreach ($search_fields as $key => $field) {
                    if ($field && isset($values[$key])) {
                        if (isset($values[$key])) {
                            // escape the value for the SQL search query
                            $values[$key] = General::slashesEscape($values[$key]);
                            if (preg_match('#DATE_(SUB|ADD)[^%]*%d %s#', $compare_options[$key])) {
                                //relative date comparison (period)
                                $condition = preg_split('#\s+AND\s+#', $compare_options[$key]);
                                $condition[0] = sprintf($condition[0], $values[$key], $values_date_period[$key]);
                                $condition = implode(' AND ', $condition);
                            } elseif (preg_match('/\.deleted/', $search_fields[$key])) {
                                if ($values[$key] == 'IS NOT NULL') {
                                    $condition = 'IS NOT NULL';
                                } elseif ($values[$key] == ' > 0') {
                                    $condition = '!= \'0000-00-00 00:00:00\'';
                                }
                            } elseif (preg_match('/%s/', $compare_options[$key])) {
                                $condition = sprintf($compare_options[$key], $values[$key]);
                            } else {
                                $condition = $compare_options[$key] . ' ' . $values[$key];
                            }
                            // strip the value for the session search filters
                            $values[$key] = General::slashesStrip($values[$key]);
                        } else {
                            if (preg_match('#DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'[^\']*\'\s*\)#', $compare_options[$key])) {
                                //relative date comparison(current month/week/year)
                                $date_format = preg_replace('#DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'([^\']*)\'\s*\)#', '$1', $compare_options[$key]);
                            }
                            if (preg_match('/%s/', $compare_options[$key])) {
                                if (!preg_match('#deleted$#', $field)) {
                                    $condition = sprintf($compare_options[$key], $values[$key]);
                                } else {
                                    $condition = $values[$key];
                                }
                            } else {
                                if (!preg_match('#deleted$#', $field)) {
                                    $condition = $compare_options[$key] . ' ' . $values[$key];
                                } else {
                                    $condition = $values[$key];
                                }
                            }
                        }

                        if (preg_match('#\[week_day\]#', $condition)) {
                            $condition = preg_replace('#\[week_day\]#', '', $condition);
                            $params['where'][$key] = 'DATE_FORMAT(' .
                                         preg_replace('#DATE_FORMAT\s*\(\s*(.*)\s*,.*#', '$1', $field) . ', \'%w\')';
                        } elseif (preg_match('#\[(day|month|year)\]#', $condition, $m)) {
                            //%d - day, %m - month, %Y - year
                            $format = substr($m[1] == 'year' ? strtoupper($m[1]) : strtolower($m[1]), 0, 1);
                            $condition = preg_replace('#\[' . $m[1] . '\]#', '', $condition);
                            $params['where'][$key] = 'DATE_FORMAT(' .
                                         preg_replace('#DATE_FORMAT\s*\(\s*(.*)\s*,.*#', '$1', $field) . ', \'%' . $format . '\')';
                        } else {
                            $params['where'][$key] = $field;
                        }
                        $params['where'][$key] .= ' ' . $condition;
                        $params['where'][$key] .= isset($logical_operator[$key]) ? ' ' . $logical_operator[$key] : ' AND';

                        //prepare filters to be saved in the session
                        $filters_to_save['search_fields'][$key] = $field;
                        $filters_to_save['compare_options'][$key] = $compare_options[$key];
                        $filters_to_save['values'][$key] = $values[$key];
                        $filters_to_save['date_period'][$key] = @$values_date_period[$key];
                        $filters_to_save['logical_operator'][$key] = isset($logical_operator[$key]) ? $logical_operator[$key] : 'AND';

                        //for autocompleters
                        if (isset($values_autocomplete[$key])) {
                            $filters_to_save['values_autocomplete'][$key] = $values_autocomplete[$key];
                        }
                        if (!empty($values_date_period[$key])) {
                            $filters_to_save['date_period'][$key] = $values_date_period[$key];
                        }
                    } elseif ($field && !isset($values[$key])) {
                        if (preg_match('#\s*=\s*DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'[^\']*\'\s*\)#',
                                       $compare_options[$key])) {
                            //relative date comparison(current month/week/year)
                            $date_format = preg_replace('#\s*=\s*DATE_FORMAT\s*\(\s*CURDATE\s*\(\)\s*,\s*\'([^\']*)\'\s*\)#',
                                                        '$1', $compare_options[$key]);
                            $params['where'][$key] = 'DATE_FORMAT(';
                            $params['where'][$key] .= preg_replace('#DATE_FORMAT\s*\(\s*(.*)\s*,.*#', '$1', $field);
                            $params['where'][$key] .= ', \'' . $date_format . '\')';
                        } else {
                            $params['where'][$key] = $field;
                        }
                        $params['where'][$key] .= ' ' . $compare_options[$key];
                        $params['where'][$key] .= isset($logical_operator[$key]) ? ' ' . $logical_operator[$key] : ' AND';

                        //prepare filters to be saved in the session
                        $filters_to_save['search_fields'][$key] = $field;
                        $filters_to_save['compare_options'][$key] = $compare_options[$key];
                        $filters_to_save['logical_operator'][$key] = isset($logical_operator[$key]) ? $logical_operator[$key] : 'AND';
                    }
                }
            }

            //prepare sort, order and pagination as they are a little bit special
            $sort = array();
            if (isset($sort_def)) {
                ksort($sort_def, SORT_NUMERIC);
                foreach ($sort_def as $key => $sort_field) {
                    if ($sort_field) {
                        if (isset($order_def[$key])) {
                            $order_def[$key] = in_array(strtoupper($order_def[$key]), ['ASC', 'DESC']) ?
                                strtoupper($order_def[$key]) : 'ASC';
                            $sort[$key] = $sort_field . ' ' . $order_def[$key];
                        } else {
                            $sort[$key] = $sort_field . ' ASC';
                            $order_def[$key] = 'ASC';
                        }
                    }
                }
                $filters_to_save['sort'] = $sort_def;
                $filters_to_save['order'] = $order_def;
            }
            $params['sort'] = $sort;

            //prepare pagination
            $var_name = 'list_' . $registry['module'];
            if ($registry['module'] != $registry['controller']) {
                $var_name .= '_' . $registry['controller'];
            }
            if (preg_match('#comments|communications#', $var_name) ||
                @preg_match('#comments|communications#', $sessionParam) ||
                @preg_match('#comments|communications#', $session_param) ||
                $registry['action'] == 'comments' || $registry['action'] == 'communications') {
                $var_name = 'list_communications';
            }
            if (preg_match('#timesheets#', $var_name) || @preg_match('#timesheets#', $sessionParam) ||
                @preg_match('#timesheets#', $session_param) || $registry['action'] == 'timesheets') {
                $var_name = 'list_timesheets';
            }
            if (preg_match('#reminds#', $var_name) || @preg_match('#reminds#', $sessionParam) ||
                @preg_match('#reminds#', $session_param) || $registry['action'] == 'remind') {
                $var_name = 'list_reminders';
            }
            $default_rpp = $registry['currentUser']->getPersonalSettings('interface', $var_name);
            if (!empty($display_def)) {
                $params['display'] = $display_def;
            } elseif (!empty($filters['display'])) {
                $params['display'] = $filters['display'];
            } elseif ($default_rpp) {
                $params['display'] = $default_rpp;
            } else {
                $params['display'] = 10;
            }
            //display should be a string
            $filters_to_save['display'] = $params['display'] . '';
        }

        //initialize page
        if ($registry['request']->isRequested('page')) {
            $params['page'] = $registry['request']['page'];
        } else {
            $params['page'] = 1;
        }

        //custom filters somewhere in the code
        //ignore when loading saved filter
        if (isset($filters['where']) && !($registry['request']['filters_action'] == 'loadfilter' && $registry['request']['filter_name'])) {
            if (isset($params['where'])) {
                $params['where'] = array_merge($params['where'], $filters['where']);
                $params['where'] = array_unique($params['where']);
            } else {
                $params['where'] = $filters['where'];
                $params_where_stripped = General::slashesStrip($params['where']);
                foreach ($params_where_stripped as $idx => $filter) {

                    list($field, $operator, $value, $logical) = self::parseFilter($filter);
                    $filters_to_save['search_fields'][$idx] = $field;
                    $filters_to_save['compare_options'][$idx] = $operator;
                    $filters_to_save['values'][$idx] = $value;
                    $filters_to_save['logical_operator'][$idx] = $logical;
                }

                $search_fields = $filters_to_save['search_fields'];
                ksort($search_fields, SORT_NUMERIC);
                $values = $filters_to_save['values'];
                //if there is only one type section/type/category parameter, set it as a simple search parameter as well
                $simple_param = '';
                $found = array();
                foreach ($search_fields as $key => $value) {
                    //check if type/section/category is required
                    if (preg_match('#^[^\.]*\.(type_section|type|category)#', $value, $matches)) {
                        $simple_param = $matches[1];
                        $found[] = $key;
                    }
                }
                if (count($found) == 1) {
                    $params['hidden_' . $simple_param] = $values[$found[0]];
                    $filters_to_save['hidden_' . $simple_param] = $values[$found[0]];
                }
            }

            //added for sorting in filter window when opened from autocompleter in additional variables
            if (empty($params['sort']) && isset($filters['sort'])) {
                $params['sort'] = $filters['sort'];
                ksort($params['sort'], SORT_NUMERIC);
                foreach ($params['sort'] as $idx => $sort_def) {
                    preg_match('#ASC|DESC\s*$#ui', $sort_def, $order);
                    $order = !empty($order) ? $order[0] : 'ASC';
                    $sort_def = trim(preg_replace('#\sASC|DESC\s*$#ui', '', $sort_def));
                    $filters_to_save['sort'][$idx] = $sort_def;
                    $filters_to_save['order'][$idx] = $order;
                }
            }
        }

        //initialize the search params
        if ($registry['request'][$sessionParam]) {
            //search is initialized
            //so clear selected items for the current session param
            $registry['session']->remove($sessionParam, 'selected_items');

            //reset page number
            $params['page'] = ($registry['request']->isRequested('page')) ? $registry['request']->get('page') : 1;

            //init saved filter
            $params['from_filter'] = false;
        }

        // if there are no session filters, remove selected items with the same key
        // (this happens when switching between lists by type/section)
        if (!$registry['session'][$sessionParam]) {
            $registry['session']->remove($sessionParam, 'selected_items');
        }

        $params['session_param'] = $sessionParam;

        //store search params into the session
        if (!$registry['request']->isRequested('clear_flag')) {
            $registry['session']->set($sessionParam, $filters_to_save, '', true);
        } else {
            $registry['session']->remove($sessionParam);
            return array();
        }

        return $params;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @param string $className - factory class name
     * @return mixed - searched object model or false
     */
    public static function getOne(&$registry, $filters = array(), $className) {
        $filters['limit'] = '0, 1';
        /*//the active filter is set to empty string
        //so that all the models are get no matter what is written in the DB
        if (!isset($filters['active'])) {
            $filters['active'] = '';
        }
        //the deleted filter is set tp empty string
        //so that all the NON deleted models are derived
        if (!isset($filters['deleted'])) {
            $filters['deleted'] = 0;
        }*/
        $registry->set('getOneRequested', true, true);
        $result = $className::search($registry, $filters);
        $model = false;
        if (!empty($result)) {
            $model = array_shift($result);
        }
        $registry->remove('getOneRequested');
        return $model;
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @param string $className - factory class name
     * @param string $searchFunction - the name of the search function, default is 'search'
     * @return array - with all necessary data for pagination of models
     */
    public static function paginatedSearch(&$registry, &$filters = array(), $className, $searchFunction = 'search') {

        //paged search requires definition of page number and results per page

        //results per page
        $class_vars =  get_class_vars($className);

        $var_name = 'list_' . $registry['module'];
        if ($registry['module'] != $registry['controller']) {
            $var_name = '_' . $registry['controller'];
        }
        if (preg_match('#comments|communications#', $var_name) || $registry['action'] == 'comments' || $registry['action'] == 'communications' || preg_match('#comments#i', $className)) {
            $var_name = 'list_communications';
        }
        if (preg_match('#timesheets#', $var_name) || $registry['action'] == 'timesheets' || preg_match('#timesheets#i', $className)) {
            $var_name = 'list_timesheets';
        }
        $default_rpp = '';
        if ($registry['currentUser']) {
            $default_rpp = $registry['currentUser']->getPersonalSettings('interface', $var_name);
        }

        if (!empty($filters['display'])) {
           $resultsPerPage = $filters['display'];
        } elseif ($default_rpp) {
            $resultsPerPage = $default_rpp;
        } elseif (!empty($class_vars['itemsPerPage'])) {
            $resultsPerPage = $class_vars['itemsPerPage'];
        } else {
            $resultsPerPage = self::$itemsPerPage;
        }

        //page number (default is 1)
        $page = 1;
        if (isset($filters['page'])) {
            if ($filters['page'] == 'last') {
                //get last page if it is requested
                $page = $className::getIds($registry, $filters, $sql, true);
                $page = ceil($page / $resultsPerPage);
            } elseif ($filters['page'] > 0) {
                $page = intval($filters['page']);
            }
        }

        //calculate the offset and limit
        $offset = ($page - 1) * $resultsPerPage;
        // only for models where new pagination is applied
        if (preg_match('#^(Customers|Tasks|Events)$#', $className)) {
            $rpp = $resultsPerPage + 1;
        } else {
            $rpp = $resultsPerPage;
        }
        $limit = sprintf("%d, %d", $offset, $rpp);

        //redefine the filters with the pagination filters
        $filters['limit'] = $limit;

        //get the model records and the total number of records
        $filters['paginate'] = true;

        //when pagination is called always prepare the models
        $prepareModels = @intval($registry['prepareModels']);
        $registry->set('prepareModels', true, true);

        //TODO: IMPORTANT!!!   IMPORTANT!!!   IMPORTANT
        //we have to watch closely what will the system do
        //because the filters are not passed by address any more
        //TODO: IMPORTANT!!!   IMPORTANT!!!   IMPORTANT
        list($models, $total) = $className::$searchFunction($registry, $filters);

        if (!$prepareModels) {
            //restore the previous state of this switch in the registry
            $registry->remove('prepareModels');
        }

        if (!isset($filters['session_param'])) {
            $filters['session_param'] = '';
        }

        //number of models found
        $found = is_array($models) ? count($models) : 0;
        //if this is a report and results contain 'additional_options' key, decrement $found
        if ($registry->get('module') == 'reports' && is_array($models) && array_key_exists('additional_options', $models)) {
            $found--;
        }
        // last page of new pagination
        if ($rpp > $resultsPerPage && $found > 0 && $found < $rpp) {
            $total = $page*$resultsPerPage-$resultsPerPage+$found;
        }
        //prepare the stats
        $pagination = array(
            'page'  => (int) $page,                                   //current page
            'pages' => ceil($total/$resultsPerPage),            //total count of pages
            'found' => (int) $found,                                  //number of models found
            'total' => (int) $total,                                  //total count of models
            'rpp'   => (int) $resultsPerPage,                         //results shown per page
            'start' => (int) $offset,                                 //starting counter for items
            'session_param' => $filters['session_param'],       //the index of the filters in the session
        );

        //check if the specified page is invalid
        if ($page > 1 && $total > 0 && $pagination['found'] == 0) {
            //there is no results found for the specified page
            //set the last possible page
            $filters['page'] = $pagination['pages'];
            return self::paginatedSearch($registry, $filters, $className, $searchFunction);
        }

        // second part of this IF ($total==0) is dedicated to the reports which does not have pagination
        if (is_array($models) && count($models) > $resultsPerPage && !($registry->get('module') == 'reports' && is_array($models) && (array_key_exists('additional_options', $models) || $total == 0))) {
            array_pop($models);
        }

        if (!isset($filters['sanitize']) || !empty($filters['sanitize'])) {
            $models = self::sanitizeModels($models);
        }

        $results = array($models, $pagination);

        return $results;
    }

    /**
     * Deletes specified models by specified table, id, or where clause
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param mixed $list - array of ids or single id
     * @param string $dbTable - table to delete from
     * @param string $field - field name to be used as basic where clause
     * @param mixed $additionalWhere - string or array expected; additional where clause to filter more the results to delete
     * @return bool - result of the operations
     */
    public static function deleteMultiple(&$registry, $list, $dbTable = '', $field = 'id', $additionalWhere = '') {
        $db = $registry['db'];

        if (empty($list)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause($field, $list);
        if (!empty($additionalWhere)) {
            if (is_array($additionalWhere)) {
                $where = array_merge($where, $additionalWhere);
            } else {
                $where[] = '(' . $additionalWhere . ')';
            }
        }

        $set = array();
        $set['deleted']    = sprintf("deleted=now()");
        $set['deleted_by'] = sprintf("deleted_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE `' . $dbTable . '`' . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        $db->Execute($query);
        $result = $db->Affected_Rows();

        return $result;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: The purged models has no restore!
     *
     * @param object $registry - the main registry
     * @param mixed $list - array of ids or single id
     * @param string $dbTable - table to delete from
     * @param string $field - field name to be used as basic where clause
     * @param mixed $additionalWhere - string or array expected; additional where clause to filter more the results to restore
     * @return bool - result of the operations
     */
    public static function restoreMultiple(&$registry, $list, $dbTable, $field = 'id', $additionalWhere = '') {
        $db = $registry['db'];

        if (empty($list)) {
            return false;
        }

        $where = array();
        //ToDo: add check for deleted items
        //$where[] = 'deleted_by!=0 AND deleted IS NOT NULL AND deleted!=0 AND deleted!="0000-00-00 00:00:00"';
        $where[] = General::buildClause($field, $list);
        if (!empty($additionalWhere)) {
            if (is_array($additionalWhere)) {
                $where = array_merge($where, $additionalWhere);
            } else {
                $where[] = '(' . $additionalWhere . ')';
            }
        }

        $set = array();
        $set['deleted']    = sprintf("deleted=0");
        $set['deleted_by'] = sprintf("deleted_by=0");

        //query to insert into the main table
        $query = 'UPDATE `' . $dbTable . '`' . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        $result = $db->Execute($query);

        return $result;
    }

    /**
     * Deletes specified models by specified table, id, or where clause
     * ATTENTION: Deletion is real and has no restore!
     *
     * @param object $registry - the main registry
     * @param mixed $list - array of ids or single id
     * @param string $dbTable - table to delete from
     * @param string $field - field name to be used as basic where clause
     * @param string $additionalWhere - additional where clause to filter more the results to delete
     * @return bool - result of the operations
     */
    public static function purgeMultiple(&$registry, $list, $dbTable, $field = 'id', $additionalWhere = '') {
        $db = $registry['db'];

        if (empty($list)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause($field, $list);
        if (!empty($additionalWhere)) {
            $where[] = '(' . $additionalWhere . ')';
        }

        //query to insert into the main table
        $query = 'DELETE FROM `' . $dbTable . '`' . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        $result = $db->Execute($query);

        return $result;
    }

    /**
     * Checks permission for multiple list of models
     * If just one model is not accessible return false
     *
     * @param object $registry - the main registry
     * @param array|int $ids - array of ids or a single id
     * @param string $action - action to check for permissions
     * @return bool - true - accessible, false - inaccessible
     */
    public static function checkPermissions(&$registry, $ids, $action) {
        if (empty($ids)) {
            return true;
        }

        if (!is_array($ids)) {
            $ids = array($ids);
        }
        // factory class to search the models
        $factoryClass = get_called_class();

        //try to guess table alias for the filters
        $alias = $factoryClass::getAlias($registry->get('module'), $registry->get('controller'));

        $filters = array(
            'where' => array(
                $alias . '.id IN (' . implode(', ', $ids) . ')'
            ),
            'sanitize' => true
        );
        if ($action == 'restore') {
            $filters['where'][] = $alias . '.deleted IS NOT NULL';
        }

        $models = $factoryClass::search($registry, $filters);

        //search has not returned some of the models because permissions are checked in constructWhere()
        if (empty($models) || count($models) != count($ids)) {
            return false;
        }

        foreach ($models as $model) {
            if (!$model->checkPermissions($action)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param ADOConnection $db
     * @param string $dbTable
     * @return array
     */
    public static function getDateColumns(ADOConnection $db, string $dbTable):array {
        $metaData = $db->MetaColumns($dbTable);
        if (!empty(($metaData))) {
            return array_keys(
                array_change_key_case(
                    array_filter($metaData, function ($field) {
                        return $field->type == 'date';
                    }),
                    CASE_LOWER
                )
            );
        }

        return array();
    }

    /**
     * @param ADOConnection $db
     * @param string $dbTable
     * @return array
     */
    public static function getDateTimeColumns(ADOConnection $db, string $dbTable):array {
        $metaData = $db->MetaColumns($dbTable);
        if (!empty(($metaData))) {
            return array_keys(
                array_change_key_case(
                    array_filter($metaData, function ($field) {
                        return $field->type == 'datetime';
                    }),
                    CASE_LOWER
                )
            );
        }

        return array();
    }

    /**
     * Parses filter definition
     *
     * @param string $filter - filter definition
     * @return array/list - the parts of the filter
     */
    public static function parseFilter($filter) {

        preg_match('#\s+(AND|OR)$#iu', $filter, $matches);
        $filter = preg_replace('#\s+(AND|OR)$#iu', '', $filter);
        $logical = '';
        if ($matches) {
            $logical = trim($matches[0]);
        }
        $field = $operator = $value = '';
        $contain_quotes = false;
        // TODO: match BETWEEN
        if (preg_match("#(<>|<=|>=|!=|=|<|>|\s+NOT\s+LIKE|\s+LIKE|\s+IS\s+NOT\s+NULL|\s+IS\s+NULL|\s+NOT\s+IN|\s+IN|\s+REGEXP)#iu", $filter, $matches)) {
            $operator = trim($matches[0]);
            $filter = preg_split("#(<>|<=|>=|!=|=|<|>|\s+NOT\s+LIKE|\s+LIKE|\s+IS\s+NOT\s+NULL|\s+IS\s+NULL|\s+NOT\s+IN|\s+IN|\s+REGEXP)#iu", $filter, 2);
            $field = trim($filter[0]);
            if (preg_match('#\'#',trim($filter[1]))) {
                $value = preg_replace('#^\'(.*)\'$#s', '$1', trim($filter[1]));
                $contain_quotes = true;
            } else {
                $value = trim($filter[1]);
                $contain_quotes = false;
            }
            if (preg_match('#LIKE#ui', $operator)) {
                //double the % mark, because it is an escape string for the sprintf function
                $val = str_replace(str_replace('%', '', $value), '%s', str_replace('%', '%%', $value));
                if ($contain_quotes) {
                    $operator .= ' \'' . $val . '\'';
                } else {
                    $operator .= ' ' . $val;
                }
                $value = str_replace('%', '', $value);
            } elseif (preg_match('#IN#ui', $operator)) {
                $value = preg_replace('#\(([^\)].*)\)#', '$1', trim($filter[1]));
                $operator .= ' (%s)';
            } elseif (!preg_match('#IS\s+(NOT\s+)?NULL#iu', $operator)) {
                if ($contain_quotes) {
                    $operator .= ' \'%s\'';
                } else {
                    $operator .= ' %s';
                }
            }
        }

        $filter = array(trim($field), trim($operator), trim($value), trim($logical));

        return $filter;
    }

    /**
     * Adds additional conditions to WHERE clause based on current user's permissions for action
     *
     * @param Registry $registry - the main registry
     * @param string $filter - search filter
     * @param string $filter - prepared search filter
     */
    public static function prepareEmptyDateFilter(&$registry, $dateColumns, $dateTimeColumns, $filter) {
        list($field, $operator, $value, $logical) = self::parseFilter($filter);
        if (!empty($value)) {
            return $filter;
        }

        $alias = static::$alias;
        $field_name = preg_replace("#.*[^,\s\(]*{$alias}\.([^,\s\)]*).*#", '$1', $field);

        if (in_array(strtolower($field_name), $dateColumns)) {
            $filter = preg_replace('#(\"\"|\'\')#', '"0000-00-00"', $filter);
        } elseif (in_array(strtolower($field_name), $dateTimeColumns)) {
            $filter = preg_replace('#(\"\"|\'\')#', '"0000-00-00 00:00:00"', $filter);
        }

        return $filter;
    }

    /**
     * Gets labels for layouts for basic vars to be displayed in list/search/outlooks.
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - keyname as key, label text as value
     */
    public static function getBasicVarsLabels(&$registry, $filters = array()) {

        require_once PH_MODULES_DIR . 'layouts/models/layouts.factory.php';

        $basic_vars_labels = array();

        $layouts = Layouts::search($registry, $filters);
        $label_prefix = $registry['module'] . '_' .
                        ($registry['module'] != $registry['controller'] ? $registry['controller'] . '_' : '');
        foreach ($layouts as $layout) {
            $basic_vars_labels[$layout->get('keyname')] = $layout->get('name') ?
                $layout->get('name') :
                ($registry['translater']->translate($label_prefix . $layout->get('keyname')) ?:
                $registry['translater']->translate($layout->get('keyname')));
        }

        return $basic_vars_labels;
    }

    /**
     * Function to calculate and save formulas with global variables
     *
     * @param object $registry - the main registry
     * @return array - result of the operation (calculated and erred)
     */
    public static function saveGlobalVars(&$registry) {
        $force = $registry['request']->get('force');
        $db = &$registry['db'];

        $model = new Model($registry);
        $formulas = $model->getFormulas();
        $formula_vars = $model->getFormulaVars();

        $vars_array = array();
        foreach ($formula_vars as $fv) {
            $vars_array[] = $fv['name'];
        }
        $models = array();

        //prepare query to get ids of all the contracts with formulas
        $where1 = 'f.formula REGEXP \'\\\[(' . implode('|', $vars_array) . ')\\\]\'' . "\n";
        $where2 = 'f.formula REGEXP \'\\\[(' . implode('|', $vars_array) . ')\\\]\'' . "\n";
        $where3 = 'f.formula REGEXP \'\\\[(' . implode('|', $vars_array) . ')\\\]\'' . "\n";
        if (!$force) {
            $where1 .= ' AND c.date_sign IS NOT NULL AND c.date_sign NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND c.date_start IS NOT NULL AND c.date_start NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND c.date_validity IS NOT NULL AND c.date_validity NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND c.date_end IS NOT NULL AND c.date_end NOT IN(\'\', \'0000-00-00\')' . "\n";
            $where2 .= ' AND v.value IS NOT NULL AND v.value != \'\' AND v.value NOT LIKE \'0000-00-00%\'' . "\n";
            $where3 .= ' AND gt2i.price_formula IS NOT NULL AND gt2i.price_formula != \'\'' . "\n" .
                       ' AND gt2i.quantity_formula IS NOT NULL AND gt2i.quantity_formula != \'\'' . "\n" .
                       ' AND gt2i.discount_value_formula IS NOT NULL AND gt2i.discount_value_formula != \'\'' . "\n" .
                       ' AND gt2i.price_index_formula IS NOT NULL AND gt2i.price_index_formula NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND gt2i.quantity_index_formula IS NOT NULL AND gt2i.quantity_index_formula NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND gt2i.discount_value_index_formula IS NOT NULL AND gt2i.discount_value_index_formula NOT IN(\'\', \'0000-00-00\')' . "\n";
        }



        //get contracts
        $query = 'SELECT DISTINCT(c.id)' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                 'JOIN ' . DB_TABLE_FORMULAS . ' AS f' . "\n" .
                 '  ON f.id = c.date_sign_formula OR f.id = c.date_start_formula' . "\n" .
                 '  OR f.id = c.date_validity_formula OR f.id = c.date_end_formula' . "\n".
                 'WHERE ' . $where1 . "\n" .
                 'UNION' . "\n" .
                 'SELECT DISTINCT(c.id)' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                 'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                 '  ON fm.model = \'CONTRACT\' AND c.type = fm.model_type AND fm.gt2 = 0' . "\n" .
                 'JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS v' . "\n" .
                 '  ON v.model_id = c.id AND v.var_id = fm.id' . "\n" .
                 'JOIN ' . DB_TABLE_FORMULAS . ' AS f' . "\n" .
                 '  ON f.id = v.formula' . "\n" .
                 'WHERE ' . $where2 . "\n" .
                 'UNION' . "\n" .
                 'SELECT DISTINCT(c.id)' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
                 'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                 '  ON gt2.model = \'Contract\' AND gt2.model_id = c.id' . "\n" .
                 'JOIN ' . DB_TABLE_GT2_INDEXES . ' AS gt2i' . "\n" .
                 '  ON gt2.id = gt2i.parent_id' . "\n" .
                 'JOIN ' . DB_TABLE_FORMULAS . ' AS f' . "\n" .
                 '  ON f.id = gt2i.price_formula OR f.id = gt2i.price_index_formula' . "\n" .
                 '  OR f.id = gt2i.quantity_formula OR f.id = gt2i.quantity_index_formula' . "\n" .
                 '  OR f.id = gt2i.discount_value_formula OR f.id = gt2i.discount_value_index_formula' . "\n" .
                 'WHERE ' . $where3;

        $ids = $db->GetCol($query);

        if ($ids) {
            $filters = array('where' => array('co.id IN (' . implode(', ', $ids) . ')'),
                             'sanitize' => false);
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $models = array_merge($models, Contracts::search($registry, $filters));
        }

        //prepare query to get all the templates with formulas
        $where1 = 'f.formula REGEXP \'\\\[(' . implode('|', $vars_array) . ')\\\]\'' . "\n";
        $where3 = 'f.formula REGEXP \'\\\[(' . implode('|', $vars_array) . ')\\\]\'' . "\n";
        if (!$force) {
            $where1 .= ' AND t.periods_start_formula IS NOT NULL AND t.periods_start_formula NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND t.periods_end_formula IS NOT NULL AND t.periods_end_formula NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND t.issue_date_formula IS NOT NULL AND t.issue_date_formula NOT IN(\'\', \'0000-00-00\')' . "\n";

            $where3 .= ' AND gt2i.price_formula IS NOT NULL AND gt2i.price_formula != \'\'' . "\n" .
                       ' AND gt2i.quantity_formula IS NOT NULL AND gt2i.quantity_formula != \'\'' . "\n" .
                       ' AND gt2i.discount_value_formula IS NOT NULL AND gt2i.discount_value_formula != \'\'' . "\n" .
                       ' AND gt2i.price_index_formula IS NOT NULL AND gt2i.price_index_formula NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND gt2i.quantity_index_formula IS NOT NULL AND gt2i.quantity_index_formula NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND gt2i.discount_value_index_formula IS NOT NULL AND gt2i.discount_value_index_formula NOT IN(\'\', \'0000-00-00\')' . "\n";
        }



        //get templates
        $query = 'SELECT DISTINCT(t.id)' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS t' . "\n" .
                 'JOIN ' . DB_TABLE_FORMULAS . ' AS f' . "\n" .
                 '  ON f.id = t.periods_start_formula' . "\n" .
                 '  OR f.id = t.periods_end_formula OR f.id = t.issue_date_formula' . "\n".
                 'WHERE ' . $where1 . "\n" .
                 'UNION' . "\n" .
                 'SELECT DISTINCT(t.id)' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS t' . "\n" .
                 'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                 '  ON gt2.model = \'Contract\' AND gt2.model_id = t.id' . "\n" .
                 'JOIN ' . DB_TABLE_GT2_INDEXES . ' AS gt2i' . "\n" .
                 '  ON gt2.id = gt2i.parent_id' . "\n" .
                 'JOIN ' . DB_TABLE_FORMULAS . ' AS f' . "\n" .
                 '  ON f.id = gt2i.price_formula OR f.id = gt2i.price_index_formula' . "\n" .
                 '  OR f.id = gt2i.quantity_formula OR f.id = gt2i.quantity_index_formula' . "\n" .
                 '  OR f.id = gt2i.discount_value_formula OR f.id = gt2i.discount_value_index_formula' . "\n" .
                 'WHERE ' . $where3;

        $ids = $db->GetCol($query);

        if ($ids) {
            $filters = array('where' => array('fit.id IN (' . implode(', ', $ids) . ')'),
                             'sanitize' => false);
            require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
            $models = array_merge($models, Finance_Invoices_Templates::search($registry, $filters));
        }

        //prepare to get all the indexes with formulas
        $where1 = 'f.formula REGEXP \'\\\[(' . implode('|', $vars_array) . ')\\\]\'' . "\n";
        if (!$force) {
            $where1 .= ' AND id.from_formula IS NOT NULL AND id.from_formula NOT IN(\'\', \'0000-00-00\')' . "\n" .
                       ' AND id.to_formula IS NOT NULL AND id.to_formula NOT IN(\'\', \'0000-00-00\')' . "\n";
        }

        //get indexes
        $query = 'SELECT DISTINCT(id.parent_id)' . "\n" .
                 'FROM ' . DB_TABLE_INDEXES_DATA . ' AS id' . "\n" .
                 'JOIN ' . DB_TABLE_FORMULAS . ' AS f' . "\n" .
                 '  ON f.id = id.from_formula OR f.id = id.to_formula' . "\n" .
                 'WHERE ' . $where1 . "\n";

        $ids = $db->GetCol($query);

        if ($ids) {
            $filters = array('where' => array('i.id IN (' . implode(', ', $ids) . ')'),
                             'sanitize' => false);
            require_once PH_MODULES_DIR . 'indexes/models/indexes.factory.php';
            $models = array_merge($models, Indexes::search($registry, $filters));
        }

        $result = 0;
        foreach ($models as $model) {
            //calculate models
            $model->set('formulas', $formulas, true);
            $model->set('formula_vars', $formula_vars, true);
            if ($model->calculateFormulas(false, $force)) {
                $result++;
            }
        }
        //return the result of the operation
        return array('calculated' => $result, 'erred' => count($models) - $result);
    }

    /**
     * Function to calculate the value with an index applied
     * @param Registry $registry - the main registry
     * @param array $values - gt2 values
     * @params string $field - Field to be indexed
     *  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
     *  ! DO NOT CALL THIS FUNCTION WITH THIS ARGUMENT !
     *  !   IT IS USED ONLY FOR RECURSIVE OPERATION    !
     *  !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
     * @return mixed
     */
    public static function getIndexedValues(&$registry, $values, $field = '') {

        $indexed_fields = array('price', 'quantity', 'discount_percentage', 'discount_value', 'surplus_percentage', 'surplus_value');
        if (!$field) {
            $field = 'price';
        }

        $new_values = array();
        if (!$registry->isRegistered('indexes_custom_data')) {
            $registry->set('indexes_custom_data', array(), true);
        }
        foreach ($values as $key => $vals) {
            if (!empty($vals['row_indexed'])) {
                $new_values = array_merge($new_values, array($vals));
                continue;
            }
            $row_broken = false;
            //get $field index data
            //'field'_index_date must be before date_to
            if (!empty($vals[$field . '_index']) && !empty($vals['date_to']) && !empty($vals['date_from']) && $vals[$field . '_index_date'] <= $vals['date_to']) {
                $query = 'SELECT * FROM ' . DB_TABLE_INDEXES_DATA . "\n" .
                         'WHERE parent_id = ' . $vals[$field . '_index'] . "\n" .
                         '  AND `from` <= \'' . $vals['date_to'] . '\'' . "\n" .
                         '  AND `to` >= \'' . $vals[$field . '_index_date'] . '\'' . "\n" .
                         'ORDER BY `from`, `row_id`';
                $records = $registry['db']->GetAll($query);

                //get index settings
                $query = 'SELECT `settings` FROM ' . DB_TABLE_INDEXES . ' WHERE id = ' . $vals[$field . '_index'];
                $settings = $registry['db']->GetOne($query);
                $settings = preg_split('#\r\n|\r|\n#', $settings);
                foreach ($settings as $k => $v) {
                    if (!preg_match('#^\s*(\#|$)#', $v)) {
                        list($s, $v) = preg_split('#\s*:=\s*#', $v);
                        $settings[$s] = $v;
                    }
                    unset($settings[$k]);
                }

                $execute_plugin = false;
                if (!empty($settings['plugin']) && !empty($settings['method'])) {
                    $plugin = PH_MODULES_DIR . 'indexes/plugins/' . $settings['plugin'] . '/' .
                              $settings['plugin'] . '.indexes.factory.php';
                    if (file_exists($plugin)) {
                        include_once $plugin;
                        $plugin = ucfirst($settings['plugin']) . '_Indexes';
                        if (method_exists($plugin, $settings['method'])) {
                            $execute_plugin = true;
                            if (!empty($settings['behavior']) && (!count($records) ||
                            in_array($settings['behavior'], array('execute_before_indexing', 'cancel_indexing')))) {
                                $method = $settings['method'];
                                $vals = $plugin::$method($registry, $vals);
                                //set this index as used with a plugin
                                $vals['used_indexes'][$vals[$field . '_index']] = array(-1);
                            }
                        }
                    }
                }

                if (empty($settings['behavior']) || $settings['behavior'] != 'cancel_indexing') {
                    //get total days for the period of the row
                    $total_days = round((strtotime($vals['date_to']) - strtotime($vals['date_from'])) /(3600 * 24)) + 1;
                    //apply index values in function of the operation
                    //we will iterate this array with while
                    //because we need special behaviour down
                    $k = 0;
                    while (!empty($records[$k])) {
                        $iterate_again = false;
                        $record = $records[$k];
                        $tmp_vals = array();
                        if ($record['from'] <= $vals['date_from'] && $vals[$field . '_index_date'] <= $vals['date_from']) {
                            //index start before the period of the row

                            //calculate total value with index
                            if ($record['operation'] == '*') {
                                $vals[$field] = $vals[$field] * $record['value'];
                            } elseif ($record['operation'] == '+') {
                                $vals[$field] = $vals[$field] + $record['value'] * $vals['row_index'];
                            } elseif ($record['operation'] == '=') {
                                $vals[$field] = $record['value'] * $vals['row_index'];
                            } elseif ($record['operation'] == '%') {
                                $vals[$field] = $vals[$field] * ($record['value'] / 100);
                            }

                            if (empty($vals['used_indexes'][$record['parent_id']])) {
                                $vals['used_indexes'][$record['parent_id']] = array();
                            }
                            //mark usage of this index stair
                            $vals['used_indexes'][$record['parent_id']][] = $record['row_id'];

                            if ($record['to'] < $vals['date_from']) {
                                //index end before the period start
                                $k++;
                                continue;
                            }

                            if ($record['to'] > $vals['date_to']) {
                                //to be sure that we will calculate indexed days correctly
                                $record['to'] = $vals['date_to'];
                            }
                            //get number of the days to be indexed
                            $indexed_days = round((strtotime($record['to']) - strtotime($vals['date_from'])) / (3600 * 24)) + 1;
                        } else {
                            // !!! ONLY FIRST ITERATION WE HAVE TO ENTER HERE !!!
                            //index start after the period of the row
                            //we have to create a not indexed part

                            //get number of the days to be NOT indexed
                            if ($vals[$field . '_index_date'] > $vals['date_from']) {
                                //'field'_index_date after date_from
                                if ($vals[$field . '_index_date'] < $record['from']) {
                                    //'field'_index_date before start index
                                    $vals[$field . '_index_date'] = $record['from'];
                                }
                                $indexed_days = round((strtotime($vals[$field . '_index_date']) - strtotime($vals['date_from'])) / (3600 * 24));
                                $record['to'] = General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($vals[$field . '_index_date'])));
                            } elseif ($record['from'] > $vals['date_from']) {
                                //index starts after date_from
                                $indexed_days = round((strtotime($record['from']) - strtotime($vals['date_from'])) / (3600 * 24));
                                $record['to'] = General::strftime('%Y-%m-%d', strtotime('-1 day', strtotime($record['from'])));
                            }
                            $iterate_again = true;
                        }

                        //temp var to operate with the row without losing any row info (except for the dates)
                        $tmp_vals = $vals;

                        $percent = $indexed_days / $total_days;

                        //calculate the part of the field without index
                        $tmp_vals[$field] = $tmp_vals[$field] * $percent;
                        $tmp_vals['row_index'] *= $percent;
                        if ($field == 'discount_percentage') {
                            //we have to recalculate the discount value
                            $tmp_vals['discount_value'] = $tmp_vals['discount_percentage'] * $tmp_vals['price'] / 100;
                            $tmp_vals['discount_surplus_field'] = 'discount_percentage';
                        } elseif ($field == 'discount_value') {
                            //we have to recalculate the discount_percentage
                            $tmp_vals['price'] = $tmp_vals['price'] * $percent;
                            $tmp_vals['discount_percentage'] = $tmp_vals['discount_value'] * 100 / $tmp_vals['price'];
                            $tmp_vals['discount_surplus_field'] = 'discount_value';
                        } elseif ($field == 'surplus_percentage') {
                            //we have to recalculate the discount value
                            $tmp_vals['surplus_value'] = $tmp_vals['surplus_percentage'] * $tmp_vals['price'] / 100;
                            $tmp_vals['discount_surplus_field'] = 'surplus_percentage';
                        } elseif ($field == 'surplus_value') {
                            //we have to recalculate the discount_percentage
                            $tmp_vals['price'] = $tmp_vals['price'] * $percent;
                            $tmp_vals['surplus_percentage'] = $tmp_vals['surplus_value'] * 100 / $tmp_vals['price'];
                            $tmp_vals['discount_surplus_field'] = 'surplus_value';
                        } elseif ($field == 'price') {
                            if ($tmp_vals['discount_surplus_field'] == 'discount_value') {
                                $tmp_vals['discount_value'] = $tmp_vals['discount_value'] * $percent;
                                $tmp_vals['discount_percentage'] = $tmp_vals['discount_value'] * 100 / $tmp_vals['price'];
                            } elseif ($tmp_vals['discount_surplus_field'] == 'surplus_value') {
                                $tmp_vals['surplus_value'] = $tmp_vals['surplus_value'] * $percent;
                                $tmp_vals['surplus_percentage'] = $tmp_vals['surplus_percentage'] * 100 / $tmp_vals['price'];
                            } elseif ($tmp_vals['discount_surplus_field'] == 'surplus_percentage') {
                                $tmp_vals['surplus_value'] = $tmp_vals['surplus_percentage'] * $tmp_vals['price'] / 100;
                            } elseif ($tmp_vals['discount_surplus_field'] == 'discount_percentage') {
                                $tmp_vals['discount_value'] = $tmp_vals['discount_percentage'] * $tmp_vals['price'] / 100;
                            }
                        }

                        //set new row end period date
                        $tmp_vals['date_to'] = $record['to'];
                        //set new period start for the row
                        $vals['date_from'] = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($record['to'])));

                        //execute plugin after row indexing
                        if ($execute_plugin && (empty($settings['behavior']) || $settings['behavior'] == 'execute_after_indexing')) {
                            $method = $settings['method'];
                            $tmp_vals = $plugin::$method($registry, $tmp_vals);
                        }
                        $new_values[] = $tmp_vals;
                        $row_broken = true;

                        if (!$iterate_again) {
                            // iterate with the next index
                            $k++;
                        } else {
                            //iterate with the same index
                            //if index start date is after the period start date
                            //and we have to put part of the row not indexed in a new row
                        }
                    }
                }
            }

            if (!$row_broken) {
                //if rows are not changed (e.g. not added to $new_values)
                $new_values = array_merge($new_values, array($vals));
            }
        }

        //get index of the field in the array with indexes(fields)
        $field = array_search($field, $indexed_fields);

        //if we have more indexes
        //check the values recursively
        if (!empty($indexed_fields[$field + 1])) {
            $field = $indexed_fields[$field + 1];
            $new_values = self::getIndexedValues($registry, $new_values, $field);
        } else {
            //we are at the end of the recursion
            //so clear the registry from the indexes plugin tmp data
            $registry->remove('indexes_custom_data');
            //mark rows as indexed so prevent them from further indexing
            foreach ($new_values as $key => $vals) {
                $new_values[$key]['row_indexed'] = true;
            }
        }

        //return rows with indexed values
        return $new_values;
    }

    /**
     * Gets table alias by module and controller to be used in search methods.
     *
     * @param string $module - module
     * @param string $controller - controller
     * @return string - alias
     */
    public static function getAlias($module, $controller) {

        $module_alias_words = explode('_', $module);
        $module_alias = array();
        foreach ($module_alias_words as $word) {
            $module_alias[] = strtolower(substr($word, 0, 1));
        }
        $alias = implode('', $module_alias);

        if ($module != $controller) {
            $controller_alias_words = explode('_', $controller);
            $controller_alias = array();
            foreach ($controller_alias_words as $word) {
                $controller_alias[] = strtolower(substr($word, 0, 1));
            }
            $alias .= implode('', $controller_alias);
        }

        return $alias;
    }

    /**
     * Get related customer types for autocompleter for specified model and type.
     *
     * @param object $registry - the main registry
     * @param string $model - model of the record
     * @param int $type - model type of the record
     * @return array - related customer types
     */
    public static function getRelatedTypes($registry, $model, $type) {
        $query = 'SELECT model_type FROM ' . DB_TABLE_TYPES_RELATIONS . "\n" .
                 'WHERE model="Customer" AND relation="autocompleter"' . "\n" .
                 '  AND relate_to_model="' . $model. '" AND relate_to_model_type="' . $type . '"';
        $records = $registry['db']->GetCol($query);

        return $records;
    }

    /**
     * Checks if all selected models have the same company as the pattern
     *
     * @param object $registry      - the main registry
     * @param string $modelName     - the model name of models
     * @param int $pattern_company  - the id of the pattern`s company
     * @param array $models_ids     - the models ids
     * @return bool                 - the result from the operations
     */
    public static function checkModelsForCompany(&$registry, $modelName, $pattern_company, $models_ids) {
        // Get the table name depending on the current model name
        $table_name = constant('DB_TABLE_' . strtoupper(General::singular2plural($modelName)));

        // Count the models having company equal to the pattern`s company
        $query        = 'SELECT COUNT(*) AS `models_count` FROM `' . $table_name . '` WHERE `company` = \'' . $pattern_company . '\' AND `id` IN (\'' . implode('\', \'', $models_ids) . '\')';
        $models_count = $registry['db']->GetOne($query);

        // If all selected models have the same company as the pattern
        if (count($models_ids) == $models_count) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Get model type from bb row in db
     *
     * @param object $registry - the main registry
     * @param int $index - id of bb row
     * @return int - id of model type
     */
    public static function getBBModelType(&$registry, $index) {
        $query = 'SELECT model_type FROM ' . DB_TABLE_BB . "\n" .
                 'WHERE id=\'' . $index . '\'';
        $type = $registry['db']->GetOne($query);

        return $type;
    }

    /**
     * Get model type from configurator row in db
     *
     * @param object $registry - the main registry
     * @param int $index - id of configurator row
     * @return int - id of model type
     */
    public static function getConfiguratorModelType(&$registry, $index) {
        $query = 'SELECT model_type FROM ' . DB_TABLE_CONFIGURATOR . "\n" .
                 'WHERE id=\'' . $index . '\'';
        $type = $registry['db']->GetOne($query);

        return $type;
    }

    /**
     * Decrypt and process "items" data from request in order to define ids of
     * records for multiprint
     *
     * @param object $registry - the main registry
     * @param string $ids - encrypted ids and other info to get records by
     */
    public static function decryptIdsMultiprint(&$registry, &$ids) {

        $uncompressed = General::decrypt($ids, '', 'gzip');
        if (preg_match('#from_id#', $uncompressed)) {
            //the ids are passed as filters

            // parse the string into variables
            parse_str($uncompressed, $output);

            if (isset($output['from_id']) && $output['from_id'] > 0) {

                // get class name of model factory that method was called on
                $model_factory = get_called_class();
                if (strpos($model_factory, '_') !== false) {
                    list($module, $controller) = explode('_', strtolower($model_factory), 2);
                } else {
                    $module = $controller = strtolower($model_factory);
                }
                $alias = $model_factory::getAlias($module, $controller);

                $filters = array('where' => array($alias . '.id >= ' . $output['from_id']),
                                 'sort' => array($alias . '.id'));
                if (isset($output['to_id']) && $output['to_id'] > 0) {
                    $filters['where'][] = $alias . '.id <= ' . $output['to_id'];
                }
                if ($module == 'finance') {
                    $filters['where'][] = $alias . '.annulled_by = 0';
                    if (!empty($output['company'])) {
                        $filters['where'][] = $alias . '.company = \'' . $output['company'] . '\'';
                    }
                } elseif ($module == 'events') {
                    // avoid event access condition
                    $filters['where'][] = '(1 OR ' . $alias . 'a.access IS NOT NULL)';
                }
                if (!empty($output['type'])) {
                    $filters['where'][] = $alias . '.type = \'' . $output['type'] . '\'';
                }

                //get the ids from the DB
                $ids = $model_factory::getIds($registry, $filters);
            } else {
                //the filters are invalid
                $ids = array();
            }
        } else {
            //the ids are passed as a CSV list
            $ids = preg_split('#\s*,\s*#', $uncompressed);
        }
    }

    /**
     * This "magic function" recursively copies information from a table
     * to the same table with respect to some settings
     * As Iliya is an idiot he cannot figure out a clever method
     * to do this more intelligently
     *
     * $defs is an array with field definitions for different tables
     * ignore/set/copy:
     * if field is in "ignore" it will not be processed
     * if field is in "set" its value will be the given one
     * if field is in "copy" or "copy" = "all"(and field is not ignored and not in "set")
     *    we get the field value from the "select" statement
     * the priority is IGNORE, SET, COPY
     * table - the table we will copy from/to
     * dependences - recursive definitions for dependent tables
     * condition - the condition we will use to select data from the table(insert method) or to select the record to update
     * delete_condition - condition we will use to delete rows if such a method is requested
     * copy_condition - condition we will use to get values to use for update of the record(s) we will get with the "condition"
     * replace_condition - fields from the dependent table that will replace the "unique" fields from the master table for on-by-one operation
     * method - the method we will copy data with
     *   -insert - create new row(s) in the table by selecting row(s) with "condition"
     *   -insert-one - first select rows by fields specified in "unique". After that insert rows on-by-one.
     *      This is useful for dependent tables which have dependences too and those dependences use the autoincrement value of their master table
     *   -update - update row(s) selected by "condition" with the info from the row selected by "copy_condition"
     *   -delete-insert - first delete rows selected by "delete_condition" and after "insert" action is executed
     *   -delete-insert-one - same as above but the insert operation is executed row-by-row.
     *        dependent tables data are also deleted by "delete_condition" connecting master and dependenat table
     * IMPORTANT!!!! For more information check
     * @see Contracts::updateCurrentFromNew, @see Contract::saveOriginal
     *
     * @param mixed $registry - our registry
     * @param mixed $defs - definitions for what and how to be copied
     * @param array $links - !!! IMPORTANT !!! FOR INTERNAL USE ONLY !!! DO NOT PASS THIS PARAMETER
     * @return int/bool - last insert id(if any)
     */
    public static function copyTableData($registry, $defs, &$links = array()) {

        $db = &$registry['db'];

        $fields = array();
        $one_by_one = false;

        if (preg_match('#^delete(-one)?(-(.+))?#', $defs['method'], $method)) {
            //we have to delete records first
            if (isset($method[3])) {
                $defs['method'] = @$method[3];
            } else {
                $defs['method'] = "";
            }
            if (!empty($defs['dependences'])) {
                //create delete definitions for the dependences too
                foreach ($defs['dependences'] as $k => $dep) {
                    if (empty($dep['delete_condition'])) {
                        continue;
                    }
                    preg_match_all('#\<[^\>]+\>#', $dep['delete_condition'], $placeholders);
                    $placeholders = $placeholders[0];
                    $query = 'SELECT ' . preg_replace('#\<|\>#', '', implode(', ', $placeholders)) . ' FROM ' . $defs['table'] . ' WHERE ' . $defs['delete_condition'];
                    if ($method[1] == '-one') {
                        //TODO: Extend this part if you need more specific conditions
                    } else {
                        $rows = $db->GetCol($query);
                        // if there are any rows
                        if ($rows) {
                            $dep['delete_condition'] = str_replace($placeholders, implode(', ', $rows), $dep['delete_condition']);
                            if (empty($dep['method'])) {
                                $dep['method'] = 'delete';
                            }
                            self::copyTableData($registry, $dep);
                        }
                    }
                }
            }
            //delete the records
            $query = 'DELETE FROM ' . $defs['table'] . ' WHERE ' . $defs['delete_condition'];
            $db->Execute($query);
        }
        if (preg_match('#^([^-]+)-one#', $defs['method'], $method)) {
            //rows has to be inserted one-by-one(update must be one by one by DEFINITION)
            $defs['method'] = $method[1];
            $one_by_one = true;
        }
        if (empty($defs['method'])) {
            //only delete has been requested
            return true;
        }

        if ($defs['method'] == 'update' && !empty($defs['copy'])) {
            //update operation we need the old row data
            //this is because we can not make update-select in the same table
            //get data from the row we will copy
            if (is_array($defs['copy'])) {
                $sel_fields = '`' . implode('`, `', $defs['copy']) . '`';
            } else {
                $sel_fields = '*';
            }
            $query = 'SELECT ' . $sel_fields . ' FROM ' . $defs['table'] . ' WHERE ' . $defs['copy_condition'];
            $copy_data = $db->GetRow($query);
        }

        foreach ($db->MetaColumns($defs['table']) as $k => $v) {
            //preces table fields against the definitions we provide
            if (!empty($defs['ignore']) && in_array($v->name, $defs['ignore'])) {
                //field is ignored
                continue;
            }
            if (!empty($defs['set'][$v->name])) {
                //we have custom value for the field
                if (preg_match('#^\<([^\>]+)\>$#', $defs['set'][$v->name], $key) && isset($defs[$key[1]])) {
                    //a placeholder is requested(at the moment only <insert_id> will be processed correctly)
                    $fields[$v->name] = sprintf('"%s"', $defs[$key[1]]);
                } else {
                    $fields[$v->name] = $defs['set'][$v->name];
                }
                if ($defs['method'] == 'update') {
                    //set update definition to look GREAT
                    $fields[$v->name] = sprintf('`%s` = %s', $v->name, $fields[$v->name]);
                }
            } elseif (!empty($defs['copy']) && ($defs['copy'] == 'all' || in_array($v->name, $defs['copy']))) {
                //we have to copy field value from the other model
                if ($defs['method'] == 'update') {
                    $fields[$v->name] = sprintf('`%s` = "%s"', $v->name, $copy_data[$v->name]);
                } else {
                    $fields[$v->name] = sprintf('`%s`', $v->name);
                }
            }
        }

        if ($one_by_one && !empty($defs['unique'])) {
            //one-by-one operation is available only if unique key is provided for the table
            if (!is_array($defs['unique'])) {
                $defs['unique'] = array($defs['unique']);
            }
            //select unique data for the rows by the unique key
            $query = 'SELECT ' . implode(', ', $defs['unique']) . ' FROM ' . $defs['table'] . ' WHERE ' . $defs['condition'];
            $recs = $db->GetAll($query);

            $conditions = $recs_base = array();
            //create new conditions for each row selected
            //as well as we prepare unique key to create links between the old and new records
            //this is useful at the moment in our contracts module
            foreach ($recs as $idx => $rec) {
                foreach ($rec as $k => $v) {
                    $conditions[$idx][] = sprintf('%s = "%s"', $k, $v);
                    $recs_base[$idx][] = $v;
                }
                $conditions[$idx] = implode(' AND ', $conditions[$idx]);
                $recs_base[$idx] = implode('^^^', $recs_base[$idx]);
            }
        } else {
            $conditions = array($defs['condition']);
        }
        //get current links from the registry
        //as this function is recursive
        if (empty($links)) {
            $links = $registry->get('tableCopyLinks');
        }
        if (!$links) {
            $links = array();
        }
        // no data to copy from
        if (empty($conditions)) {
            return true;
        }
        //at last we are ready to execute the data copy procedure
        foreach ($conditions as $idx => $cond) {
            //copy data
            if ($defs['method'] == 'insert') {
                $query = 'INSERT INTO ' . $defs['table'] . ' (`' . implode('`, `', array_keys($fields)) . '`)' . "\n" .
                        '    SELECT ' . implode(', ', array_values($fields)) . ' FROM ' . $defs['table'] . "\n" .
                        '    WHERE ' . $cond;
                $db->Execute($query);
            } elseif ($defs['method'] == 'update') {
                //IMPORTANT !!! One-by-one is not supported for update at the moment
                $query = 'UPDATE ' . $defs['table'] . ' SET' . "\n" .
                         implode(",\n", $fields) . "\n" .
                         'WHERE ' . $cond;
                $db->Execute($query);

            }

            $insert_id = $db->Insert_Id();
            if ($one_by_one && $insert_id) {
                //we save the links between source and destination records
                //TODO: for the contracts $insert_id is enough to make the connection
                //please extend this part of the code if needed
                $links[$defs['table']][$recs_base[$idx]] = $insert_id;
            }
            if (!empty($defs['dependences'])) {
                //we have dependent tables that we have to process recursively
                foreach ($defs['dependences'] as $defs_dep) {
                    if (empty($defs_dep['method'])) {
                        //get parent method if not method has been provided for the dependency
                        $defs_dep['method'] = $defs['method'];
                    }
                    //set last insert id in the definitions as we may need it in the dependent table
                    $defs_dep['insert_id'] = $insert_id;
                    if (!empty($defs_dep['replace_condition'])) {
                        //replace unique fields from the master table condition with the dependent table fields
                        if (!is_array($defs_dep['replace_condition'])) {
                            $defs_dep['replace_condition'] = array($defs_dep['replace_condition']);
                        }
                        $defs_dep['condition'] = str_replace($defs['unique'], $defs_dep['replace_condition'], $cond);
                    }

                    //process definitions for the dependency
                    self::copyTableData($registry, $defs_dep, $links);
                }
            }
        }
        if (!empty($links)) {
            //save copy links if any
            $registry->set('tableCopyLinks', $links, true);
        }
        return $insert_id;
    }

    /**
     * Get values of fields from contract used for specifying contents of
     * invoicing period data in each row of invoices issued from contract
     * and set them into destination model or array
     *
     * @param mixed $from - array or subclass of Model to get values from
     * @param mixed $to - array or subclass of Model to set values into
     * @param string $from_prefix - string to prepend source keys with
     * @param string $to_prefix - string to prepend destination keys with
     */
    public static function getGT2InfoContractData(&$from, &$to, $from_prefix = '', $to_prefix = 'contract_') {
        // keys to get (extend when necessary)
        // IMPORTANT: only fields from `contracts` table expected
        $contract_fields = array('num', 'date_sign', 'date_start', 'date_validity');

        // destination could be a model or an array
        $to_is_model = false;
        if (is_object($to) && is_subclass_of($to, 'Model')) {
            $to_is_model = true;
        } elseif (!is_array($to)) {
            $to = array();
        }

        // source could be a model or an array
        if (is_object($from) && is_subclass_of($from, 'Model')) {
            foreach ($contract_fields as $var) {
                if ($to_is_model) {
                    $to->set($to_prefix . $var, $from->get($from_prefix . $var), true);
                } else {
                    $to[$to_prefix . $var] = $from->get($from_prefix . $var);
                }
            }
        } elseif (is_array($from)) {
            foreach ($contract_fields as $var) {
                $val = isset($from[$from_prefix . $var]) ? $from[$from_prefix . $var] : '';
                if ($to_is_model) {
                    $to->set($to_prefix . $var, $val, true);
                } else {
                    $to[$to_prefix . $var] = $val;
                }
            }
        }
    }

    /**
     * Creates text contents that specifies invoicing period of every row of invoice
     * and sets it into 'free_text5' (or other specified field) of rows.
     * Sets 'date_from', 'date_to' from params into GT2 rows, if not set yet.
     *
     * @param Registry $registry - the main registry
     * @param array $rows - GT2 rows of invoice
     * @param array $params - possible params are 'field', 'invoice_issue_auto_message',
     *     'contract_num', 'contract_date_sign', 'contract_date_start', 'contract_date_validity',
     *     'date_from', 'date_to', 'model_name', 'model_type', 'model_lang'
     */
    public static function createGT2InfoData($registry, &$rows, $params) {
        //prepare notes for the gt2 rows
        $extender = new Extender();
        if (empty($params['field'])) {
            $params['field'] = 'free_text5';
        }
        $extender->placeholders = array();

        // data from contract (all keys starting with 'contract_')
        foreach ($params as $k => $v) {
            if (strpos($k, 'contract_') === 0 && !empty($v) && $v != '0000-00-00') {
                // if value matches ISO date format, convert it to specified format
                if (preg_match('#^\d{4}-\d{2}-\d{2}$#', $v)) {
                    $v = date_create($v)->format('d.m.Y');
                }
                $extender->placeholders[$k] = $v;
            }
        }

        // prepare format of text
        $invoice_issue_auto_message = $registry['translater']->translate('invoice_issue_auto_messages');
        if (isset($params['invoice_issue_auto_message'])) {
            // if format has been specified as a parameter, use it directly
            if ($params['invoice_issue_auto_message']) {
                $invoice_issue_auto_message = $params['invoice_issue_auto_message'];
            }
        } elseif (!empty($params['model_name']) && !empty($params['model_type'])) {
            // search for format in db
            $table_name_types_i18n = 'DB_TABLE_' . strtoupper(General::singular2plural($params['model_name'])) . '_TYPES_I18N';
            if (defined($table_name_types_i18n)) {
                $query = 'SELECT *' . "\n" .
                         '  FROM `' . constant($table_name_types_i18n) . '`' . "\n" .
                         '  WHERE `parent_id` = \'' . $params['model_type'] . '\'' . "\n" .
                         '    AND `lang`      = \'' . (empty($params['model_lang']) ? $registry['lang'] : $params['model_lang']) . '\'';
                $type_i18n = $registry['db']->GetRow($query);
                if (!empty($type_i18n['invoice_issue_auto_message'])) {
                    $invoice_issue_auto_message = $type_i18n['invoice_issue_auto_message'];
                }
            }
        }

        foreach ($rows as $key => $values) {
            // get dates from fields in GT2 row or from input parameters, if missing
            if (empty($values['date_from']) && !empty($params['date_from'])) {
                $rows[$key]['date_from'] = $params['date_from'];
            }
            if (empty($values['date_to']) && !empty($params['date_to'])) {
                $rows[$key]['date_to'] = $params['date_to'];
            }

            if (empty($rows[$key]['date_from']) || empty($rows[$key]['date_to']) ||
            $rows[$key]['date_from'] == '0000-00-00' || $rows[$key]['date_to'] == '0000-00-00') {
                // field contents should be blank
                $values[$params['field']] = '';
            } elseif ($rows[$key]['date_from'] == $rows[$key]['date_to']) {
                // field contents should be just the date
                $values[$params['field']] = date_create($rows[$key]['date_from'])->format('d.m.Y');
            } else {
                // field contents should be in the specified format
                $extender->placeholders['date_from'] = date_create($rows[$key]['date_from'])->format('d.m.Y');
                $extender->placeholders['date_to'] = date_create($rows[$key]['date_to'])->format('d.m.Y');

                if (empty($values[$params['field']]) || !empty($params['force'])) {
                    $values[$params['field']] = $invoice_issue_auto_message;
                }
            }

            $rows[$key][$params['field']] = $extender->expand($values[$params['field']]);
        }
    }

    /**
     * Recalculates gt2 row's field in function of a specified from/to currencies
     * @param mixed $registry - main registry
     * @param array $row - row to be recalculated
     * @param string $field - field to be recalculated
     * @param string $from - source currency
     * @param string $to - destination currency
     */
    public static function changeGT2RowsCurrency($registry, $rows, $field, $from, $to, $date = '') {
        $rates = array();
        foreach ($rows as $r => $row) {
            $rate = 1;
            if ($from != $to && empty($row['current_currency'])) {
                //different source and destination currencies
                if (!empty($rates[$from.$to])) {
                    $rate = $rates[$from.$to];
                } else {
                    $rate = Finance_Currencies::getRate($registry, $from, $to, $date);
                    $rates[$from.$to] = $rate;
                }
            } elseif (!empty($row['current_currency']) && $row['current_currency'] != $to) {
                //different current row currency and destination currency
                if (!empty($rates[$row['current_currency'].$to])) {
                    $rate = $rates[$row['current_currency'].$to];
                } else {
                    $rate = Finance_Currencies::getRate($registry, $row['current_currency'], $to, $date);
                    $rates[$row['current_currency'].$to] = $rate;
                }
            }
            $row['current_currency'] = $to;
            $row[$field] *= $rate;
            if (isset($row['discount_surplus_field'])) {
                if ($row['discount_surplus_field'] == 'discount_value') {
                    $row['discount_value'] *= $rate;
                } elseif ($row['discount_surplus_field'] == 'surplus_value') {
                    $row['surplus_value'] *= $rate;
                }
            }
            $rows[$r] = $row;
        }
        return $rows;
    }

    /**
     * Add specified tag to selected models
     *
     * @param Registry $registry - the main registry
     * @param Controller $controller - controller object
     * @return boolean|int - result of operation - false on error, true if all were updated, N if some were updated, -1 if none were updated
     */
    public static function multiTag(Registry &$registry, Controller $controller) {
        $request = &$registry['request'];

        //get additional post values
        $tag = $request->get('tagsSelect');
        if (empty($tag)) {
            return false;
        }

        //IDs of models to tag
        $ids = $request->get('items');

        $db = &$registry['db'];
        $db->StartTrans();

        /**
         * @var Model_Factory $model_factory - get class name of model factory that method was called on
         */
        $model_factory = get_called_class();
        if (strpos($model_factory, '_') !== false) {
            list($m, $c) = explode('_', strtolower($model_factory), 2);
        } else {
            $m = $c = strtolower($model_factory);
        }
        $alias = $model_factory::getAlias($m, $c);

        $new_models = $old_models = array();
        foreach ($ids as $model_id) {
            $filters = array('where' => array($alias . '.id = ' . $model_id));
            /**
             * @var Model $model
             */
            $model = $model_factory::searchOne($registry, $filters);
            if (!$model) {
                continue;
            }

            $old_model = clone $model;
            $old_model->getModelTagsForAudit();
            $old_model->sanitize();

            if ($model->checkPermissions('tags_view') && $model->checkPermissions('tags_edit') &&
            $model->set('tags', array($tag), true) && $model->updateTags(array('skip_delete' => true))) {

                $filters = array(
                    'where' => array($alias . '.id = ' . $model_id),
                    'skip_assignments' => true,
                    'skip_permissions_check' => true
                );
                $new_model = $model_factory::searchOne($registry, $filters);
                $new_model->getModelTagsForAudit();
                $new_model->sendTagNotification($old_model->get('tag_names_for_audit'));
                $new_model->sanitize();

                $new_models[] = $new_model;
                $old_models[] = $old_model;

                // include history and audit class
                require_once PH_MODULES_DIR . $m . '/models/' . $m . ($c != $m ? '.' . $c : '') . '.history.php';
                require_once PH_MODULES_DIR . $m . '/models/' . $m . ($c != $m ? '.' . $c : '') . '.audit.php';
                $model_factory_history = $model_factory . '_History';
                $model_factory_history::saveData(
                    $registry,
                    array(
                        'model' => $model,
                        'action_type' => 'multitag',
                        'new_model' => $new_model,
                        'old_model' => $old_model
                    ));

            }
            unset($model);
        }

        //set models for automations
        $controller->old_model = $old_models;
        $controller->new_models = $new_models;

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result && count($new_models) < count($ids)) {
            $result = count($new_models) ?: -1;
        }

        return $result;
    }

    /**
     * Remove specified tag from selected models
     *
     * @param Registry $registry - the main registry
     * @param Controller $controller - controller object
     * @return boolean|int - result of operation - false on error, true if all were updated, N if some were updated, -1 if none were updated
     */
    public static function multiRemoveTag(Registry &$registry, Controller $controller) {
        $request = &$registry['request'];

        //get additional post values
        $tag = $request->get('tagsSelect');
        if (empty($tag)) {
            return false;
        }

        //IDs of models to tag
        $ids = $request->get('items');

        $db = &$registry['db'];
        $db->StartTrans();

        /**
         * @var Model_Factory $model_factory - get class name of model factory that method was called on
         */
        $model_factory = get_called_class();
        if (strpos($model_factory, '_') !== false) {
            list($m, $c) = explode('_', strtolower($model_factory), 2);
        } else {
            $m = $c = strtolower($model_factory);
        }
        $alias = $model_factory::getAlias($m, $c);

        $new_models = $old_models = array();
        foreach ($ids as $model_id) {
            $filters = array('where' => array($alias . '.id = ' . $model_id));
            /**
             * @var Model $model
             */
            $model = $model_factory::searchOne($registry, $filters);
            if (!$model) {
                continue;
            }

            $old_model = clone $model;
            $old_model->getModelTagsForAudit();
            $old_model->sanitize();

            if ($model->checkPermissions('tags_view') && $model->checkPermissions('tags_edit') &&
            $model->set('tags', array($tag), true) && $model->updateTags(array('skip_add' => true))) {

                $filters = array(
                    'where' => array($alias . '.id = ' . $model_id),
                    'skip_assignments' => true,
                    'skip_permissions_check' => true
                );
                $new_model = $model_factory::searchOne($registry, $filters);
                $new_model->getModelTagsForAudit();
                $new_model->sendTagNotification($old_model->get('tag_names_for_audit'));
                $new_model->sanitize();

                $new_models[] = $new_model;
                $old_models[] = $old_model;

                // include history and audit class
                require_once PH_MODULES_DIR . $m . '/models/' . $m . ($c != $m ? '.' . $c : '') . '.history.php';
                require_once PH_MODULES_DIR . $m . '/models/' . $m . ($c != $m ? '.' . $c : '') . '.audit.php';
                $model_factory_history = $model_factory . '_History';
                $model_factory_history::saveData(
                    $registry,
                    array(
                        'model' => $model,
                        'action_type' => 'multitag',
                        'new_model' => $new_model,
                        'old_model' => $old_model
                    ));
            }
            unset($model);
        }

        //set models for automations
        $controller->old_model = $old_models;
        $controller->new_models = $new_models;

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result && count($new_models) < count($ids)) {
            $result = count($new_models) ?: -1;
        }

        return $result;
    }

    /**
     * Performs a check whether module has types
     *
     * @param string $module_name - specified name of module(+controller)
     * @return boolean - result of check
     */
    public static function hasTypes($module_name = '') {
        if (!$module_name) {
            $module_name = General::singular2plural(self::$modelName);
        }
        return defined('DB_TABLE_' . strtoupper($module_name) . '_TYPES') ||
            preg_match('#^finance_((incomes|expenses)_reasons|warehouses_documents|annulments|invoices_templates)$#i', $module_name);
    }

    /**
     * Retrieves a list of model types from DB.
     *
     * @param string $module_name - specified name of module(+controller)
     * @return array - type to name array
     */
    public static function getTypes(Registry &$registry, string $module_name = '') {
        if (!self::hasTypes($module_name)) {
            return [];
        }

        if (!$module_name) {
            $module_name = General::singular2plural(self::$modelName);
        }

        $settings = [
            // mti is shorthand from Module Types I18n as we cannot have exact names.
            'mti' => constant('DB_TABLE_' . strtoupper($module_name) . '_TYPES_I18N'),
            'lang' => $registry->get('lang'),
        ];
        $query = <<<SQL
        SELECT mti.`parent_id`, mti.`name`
        FROM {$settings['mti']} mti
        WHERE mti.`lang` = '{$settings['lang']}'
        SQL;
        return $registry['db']->getAssoc($query);
    }


    /**
     * Add or remove current user's assignment as an observer to model
     *
     * @param object $registry - the main registry
     * @param object $model - model (Document, Task, Contract or financial document)
     * @param bool $remove - true to remove assignment, false to add assignment
     * @return bool - result of the operation
     */
    public static function observer(&$registry, $model, $remove) {

        // if model has specific validation, perform it first
        if ($remove && method_exists($model, 'validateAssign') && !$model->validateAssign()) {
            return false;
        }

        $db = $registry['db'];
        $db->StartTrans();
        $user_id = $registry['currentUser']->get('id');
        if (preg_match('#finance_#i', $model->modelName)) {
            $table = DB_TABLE_FINANCE_DOCUMENTS_ASSIGNMENTS;
            $wh = ' AND model="' . $model->modelName . '"';
            $model_str = ', model = "' . $model->modelName . '"';
        } else {
            $table = constant('DB_TABLE_' . strtoupper($model->getModule()) . '_ASSIGNMENTS');
            $model_str = $wh = '';
        }
        if ($remove) {
            $query = 'DELETE FROM ' . $table . "\n" .
                     ' WHERE parent_id=' . $model->get('id') . "\n" .
                     ' AND assignments_type=' . PH_ASSIGNMENTS_OBSERVER . "\n" .
                     ' AND assigned_to=' . $user_id . $wh;

        } else {
            $query = 'INSERT INTO ' . $table . ' SET' . "\n" .
                     'parent_id = ' . $model->get('id') . ', assigned_by = ' . $user_id . ', ' . "\n" .
                     'assigned_to = ' . $user_id . ', assigned = now(), assignments_type = ' . PH_ASSIGNMENTS_OBSERVER . "\n" .
                     $model_str;
        }
        $db->Execute($query);

        // update user_permissions field of model
        if (!$db->HasFailedTrans()) {
            $sanitize_after = false;
            if ($model->isSanitized()) {
                $model->unsanitize();
                $sanitize_after = true;
            }
            $model->updateUserPermissions();
            if ($sanitize_after) {
                $model->sanitize();
            }
        }

        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Check if model has observer assignment to current user
     *
     * @param object $registry - the main registry
     * @param object $model - model (Document, Task, Contract or financial document)
     * @return bool - true if found, otherwise false
     */
    public static function checkObserver(&$registry, $model) {

        $db = $registry['db'];
        $user_id = $registry['currentUser']->get('id');
        if (preg_match('#finance_#i', $model->modelName)) {
            $table = DB_TABLE_FINANCE_DOCUMENTS_ASSIGNMENTS;
            $wh = ' AND model="' . $model->modelName . '"';
        } else {
            $table = constant('DB_TABLE_' . strtoupper($model->getModule()) . '_ASSIGNMENTS');
            $wh = '';
        }
        $query = 'SELECT assigned_to FROM ' . $table . "\n" .
                 ' WHERE parent_id=' . $model->get('id') . "\n" .
                 ' AND assignments_type=' . PH_ASSIGNMENTS_OBSERVER . "\n" .
                 ' AND assigned_to=' . $user_id . $wh;
        $obsrv_assignments = $db->GetAll($query);

        if (count($obsrv_assignments)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param string $module The module of the model
     * @param string $controller The controller of the model
     * @return string The factory name for the given module and controller combination
     */
    public static function getModelFactory(string $module, string $controller)
    {
        return implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));
    }
}

?>
