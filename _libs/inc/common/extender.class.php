<?php

/**
 * This class handles all placeholders within a string.
 * It gets a string and expands all variables enclosed in '[' and ']' tags.
 * After you create a class, you need to call only the expand() method of the object.
 * It will search automatically for all variables, take their values and expand them.
 */
class Extender
{
    /**
     * Associative array of placeholders in format:
     * placeholder => replacement
     */
    public $placeholders = array();
    public $module = '';
    public $model_lang = '';
    private string $openTag = '[';
    private string $closeTag = ']';
    private string $modifierDelimiter = '|';


    /**
     * Constructor of the class flushes placeholders
     */
    public function __construct()
    {
        $this->flush();
    }

    /**
     * This is the main method of the class. It gets a text string with variables.
     * If there is not defined variable, it will be removed without placing any text on its place.
     * Every expanded variable is stored in an internal array to speed up work.
     *
     * @param string $value - the variable without the enclosing tags
     * @param boolean $ignore_not_found - the variable shows if the string in the enclosing tags will be
     * replaced if no placeholder for it exists
     * @return string $value - the value of the variable.
     */
    function expand($value, $ignore_not_found = true)
    {
        // the array with variable names
        $smatches = array();

        // the array where the processed placeholders are stored
        $data =& $this->placeholders;

        if (is_array($value)) {
            return $value;
        }

        /**
         * Use recursive subpatterns to get the contents of the outermost pair of brackets.
         * @see http://php.net/manual/en/regexp.reference.recursive.php
         */
        $vars = preg_match_all("#\[(((?>[^\[\]]+)|(?R))*)\]#", $value, $matches, PREG_PATTERN_ORDER);
        // all variable names are assigned to $smatches
        $smatches = $matches[1];
        $smatches_count = count($smatches);

        for ($i = 0; $i < $smatches_count; $i++) {
            $match = $smatches[$i];
            $match = strip_tags($match);

            //check for recursive variables and extract them
            $end_pos = strrpos($match, ']');
            if (!is_bool($end_pos) && $end_pos) {
                $start_pos = strpos($match, '[');
                $sub_match = substr($match, $start_pos, ($end_pos - $start_pos + 1));
                $smatches[] = preg_replace("#^\[(.*)\]$#", "$1", $sub_match);
            }

            $orig_match = $match;
            // check for modifiers and
            // split by pipe but not when it is within a nested variable
            $match_arr = preg_split('#\|(?![^\[]+\])#', $match);

            $match = $match_arr[0];

            //if (empty($data[$match]) || !trim($data[$match])) {
            if (empty($data[$match]) && !(isset($data[$match]) && $data[$match] === '0') || (is_string($data[$match]) && trim($data[$match]) === '')) {
                //if a placeholder is not set check to remove it or not
                if ($ignore_not_found) {
                    //if a placeholder with empty value is found, remove the placeholder
                    //to avoid showing of [placeholders] in the content
                    $data[$match] = '';
                }
            }



            if (isset($data[$match])) {
                if (is_array($data[$match])) {
                    $data[$match] = implode(', ', $data[$match]);
                }
                $data_val = $data[$match];
                $placeholder_name = $match_arr[0];
                // set default attribute for the salutation modifier
                if (preg_match('/salutation$/', $placeholder_name) && count($match_arr) == 1) {
                    $match_arr[] = 'simple';
                }
                // check for modifiers
                if (count($match_arr) > 1) {
                    for ($m = 1; $m < count($match_arr); $m++) {
                        $data_val = $this->modify($data_val, $match_arr[$m], $placeholder_name);
                    }
                }
                // expand the placeholder
                $value = str_replace("[$orig_match]", $data_val, $value);
            }
        }

        return $value;
    }

    /**
     * Modify value
     *
     * @param string $val - value to be modified
     * @param string $modifier - modifier
     * @param string $placeholder_name - placeholder name
     * @return string $val - modified value
     */
    function modify($val, $modifier, $placeholder_name)
    {
        // split by colon but not when it is within a nested variable
        $modifier_arr = preg_split('#:(?![^\[]+\])#', $modifier);
        $modifier = array_shift($modifier_arr);
        switch ($modifier) {
            case 'date_format':
                //check the format of system date and time
                switch ($placeholder_name) {
                    case 'system_date':
                        if (!preg_match('#^\d{4}-\d{2}-\d{2}#', $val)) {
                            $val = General::strftime('%Y-%m-%d');
                        }
                        break;
                    case 'system_datetime':
                        if (!preg_match('#^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}#', $val)) {
                            $val = General::strftime('%Y-%m-%d %T');
                        }
                        break;
                    case 'system_time':
                        if (!preg_match('#^\d{2}:\d{2}:\d{2}#', $val)) {
                            $val = General::strftime('%T');
                        }
                        break;
                }

                // do not format date if empty
                if (empty($val)) {
                    return $val;
                } elseif ($val == '0000-00-00' || $val == '0000-00-00 00:00:00') {
                    //empty dates should not be replaced with current date (as General::strftime() does)
                    return '';
                }

                //apply the modifier to all the placeholders within the value
                preg_match_all("#\[([^\[]*(\[([^\[]*)\])?)\]#", $val, $matches, PREG_PATTERN_ORDER);
                if (!empty($matches[0])) {
                    foreach ($matches[0] as $val_within) {
                        if (!preg_match('#\|date_format#', $val_within)) {
                            $modifier .= ':' . implode(':', $modifier_arr);
                            $val_within_modifier = str_replace(']', '|' . $modifier . ']', $val_within);
                            $val = str_replace($val_within, $val_within_modifier, $val);
                        }
                    }
                } else {
                    $modifier = implode(':', $modifier_arr);
                    $val = General::strftime($modifier, strtotime($val));
                }
                break;
            case 'inwords':
                if (!empty($val) && is_numeric($val)) {
                    // first expected modifier parameter is currency
                    if (array_key_exists(0, $modifier_arr)) {
                        if (preg_match('#\[(.*)\]#', $modifier_arr[0])) {
                            $currency = preg_replace('#\[(.*)\]$#', '$1', $modifier_arr[0]);
                            $currency = $this->placeholders[$currency];
                        } else {
                            // remove the quotation marks (first and last characters, must be the same)
                            $currency = preg_replace('#^(&quot;|&\#39;|"|\')(.*)\1$#', '$2', $modifier_arr[0]);
                        }
                    } else {
                        $currency = '';
                    }
                    // second expected modifier parameter is lang
                    $lang = '';
                    if (array_key_exists(1, $modifier_arr)) {
                        if (preg_match('#\[(.*)\]#', $modifier_arr[1])) {
                            $lang = preg_replace('#\[(.*)\]$#', '$1', $modifier_arr[1]);
                            $lang = $this->placeholders[$lang];
                        } else {
                            // remove the quotation marks (first and last characters, must be the same)
                            $lang = preg_replace('#^(&quot;|&\#39;|"|\')(.*)\1$#', '$2', $modifier_arr[1]);
                        }
                    }
                    if (empty($lang) ||
                        !in_array($lang, $GLOBALS['registry']['config']->getParamAsArray('i18n', 'supported_langs'))) {
                        $lang = $this->model_lang;
                    }
                    $val = General::digits2words($val, $currency, $lang);
                }
                break;
            case 'display':
                switch ($modifier_arr[0]) {
                    case 'configs':
                        $val = $this->placeholders[$placeholder_name . '_configs'];
                        unset($this->placeholders[$placeholder_name . '_configs']);
                        break;
                    case 'both':
                        $val .= '<br />' . $this->placeholders[$placeholder_name . '_configs'];
                        unset($this->placeholders[$placeholder_name . '_configs']);
                        break;
                    default:
                        unset($this->placeholders[$placeholder_name . '_configs']);
                        break;
                }
            case 'formal':
                if (preg_match('/salutation$/', $placeholder_name) && $val) {
                    $formal = $this->placeholders[$placeholder_name . '_formal'];
                    if ($formal) {
                        $val = $formal . ' ' . $val;
                    }
                }
                break;
            case 'simple':
                if (preg_match('/salutation$/', $placeholder_name)) {
                    $enc = mb_detect_encoding($val);
                    $val = mb_strtoupper(mb_substr($val, 0, 1, $enc), $enc) . mb_substr($val, 1, mb_strlen($val, $enc) - 1, $enc);
                }
                break;
            case 'default':
                if ($val === '' || $val === false) {
                    if (preg_match_all("#\[(((?>[^\[\]]+)|(?R))*)\]#", $modifier_arr[0], $all_inner_matches, PREG_PATTERN_ORDER)) {
                        // assign val to the whole replacement expession, then start replacing modifiers in it
                        $val = $modifier_arr[0];
                        foreach ($all_inner_matches[1] as $inner_match) {
                            $match_arr = explode('|', $inner_match);
                            $inner_val = isset($this->placeholders[preg_replace('#\[(.*)\]$#', '$1', $match_arr[0])]) ? $this->placeholders[preg_replace('#\[(.*)\]$#', '$1', $match_arr[0])] : '';
                            if (count($match_arr) > 1) {
                                for ($m = 1; $m < count($match_arr); $m++) {
                                    $inner_val = $this->modify($inner_val, $match_arr[$m], $match_arr[0]);
                                }
                            } else {
                                $inner_val = isset($this->placeholders[$inner_match]) ? $this->placeholders[$inner_match] : '';
                            }
                            if (is_array($inner_val)) {
                                $inner_val = implode(', ', $inner_val);
                            }
                            $val = str_replace("[$inner_match]", $inner_val, $val);
                        }
                    } else {
                        // remove the quotation marks (first and last characters, must be the same)
                        $val = preg_replace('#^(&quot;|&\#39;|"|\')(.*)\1$#', '$2', $modifier_arr[0]);
                    }
                }
                break;
            case 'max_width':
            case 'max_height':
                //DOCX image dimension correction

                //separate value and unit
                $v = 0;
                $units = 'px';
                if (!preg_match('#([0-9]+)\s*(.*)#', $modifier_arr[0], $matches)) {
                    //no value passed
                    //should be |max_width:100px or |max_height:10%
                    break;
                } else {
                    $v = $matches[1];
                }
                if (!empty($matches[2]) && in_array($matches[2], array('px', '%'))) {
                    $units = $matches[2];
                }

                //height or width
                $dimension = str_replace('max_', '', $modifier);

                if (preg_match('#<wp:extent\s+c(x|y)="([0-9]+)"\s+c(x|y)="([0-9]+)"/>#', $val, $d)) {
                    //DOCX
                    //x is for width, y is for height, the values are in EMUs
                    $dimensions[$d[1]] = $d[2];
                    $dimensions[$d[3]] = $d[4];
                    $width = $dimensions['x'];
                    $height = $dimensions['y'];

                    //separate value and unit
                    preg_match('#([0-9]+)\s*(.*)#', $modifier_arr[0], $matches);
                    if (!empty($matches)) {
                        $v = $matches[1];
                    }

                    //convert wanted units to EMU
                    switch ($units) {
                        case 'px':
                            //IMPORTANT: 92 is the default resolution
                            $v = intval($v * 914400 / 92);
                            break;
                        case '%':
                            $v = intval(($$dimension * $v) / 100);
                            break;
                    }

                    //$$dimension is the width in the DOCX
                    //$v is the value in the modifier
                    if ($$dimension <= $v) {
                        //no resize needed
                        break;
                    }

                    // proportion
                    $k = $v / $$dimension;
                    // keep the aspect ratio
                    $new_width = intval($k * $width);
                    $new_height = intval($k * $height);

                    $val = preg_replace('#(<wp:extent|<a:ext)\s+c(x|y)="([0-9]+)"\s+c(x|y)="([0-9]+)"/>#',
                        '\1 cx="' . $new_width . '" cy="' . $new_height . '"/>',
                        $val);
                } else {
                    //PDF
                    //IMPORTANT: works only for pixels!!!
                    if (preg_match('#max' . $dimension . '=([^&]*)#', $val)) {
                        $val = preg_replace('#max' . $dimension . '=([^&]*)#', 'max' . $dimension . '=' . $v . $units, $val);
                    } elseif (preg_match('#<img\s+src="([^\"]*)"([^>]*)>#', $val, $m)) {
                        $val = preg_replace('#<img\s+src="([^\"]*)"([^>]*)>#', '<img src="\1&max' . $dimension . '=' . $v . $units . '"\2>', $val);
                    }
                }
                break;
            default:
                if (function_exists($modifier)) {
                    array_unshift($modifier_arr, $val);
                    $val = call_user_func_array($modifier, $modifier_arr);
                }
                break;
        }
        return $val;
    }

    /**
     * Clears the entire array of placeholders or specific placeholder
     *
     * @param string $key - key of specific placeholder to be flushed
     * @return bool - result of the operation
     */
    function flush($key = '')
    {
        if (!empty($key)) {
            unset($this->placeholders[$key]);
        } else {
            $this->placeholders = array();
        }

        return true;
    }

    /**
     * Adds placeholder and its replacement.
     * This method allows you to override placeholder's replacement
     *
     * @param string $placeholder - the name of the placeholder (variable name to be expanded)
     * @param string $replacement - the value to replace the placeholder (the replacement)
     * @return bool - result of the operation
     */
    function add($placeholder, $replacement)
    {
        $this->placeholders[$placeholder] = $replacement;

        return true;
    }

    /**
     * Get a prepared placeholder value
     *
     * @param string $placeholder - the name of the placeholder (variable name to be expanded)
     */
    public function getPlaceholderValue(string $placeholder)
    {
        return $this->placeholders[$placeholder] ?? null;
    }

    /**
     * Add an array of placeholders and their replacements.
     * This method allows you to override placeholder's replacement
     *
     * @param array $placeholders - array of placeholders and their values
     * @return bool - result of the operation
     */
    function merge($placeholders)
    {
        if (is_array($placeholders)) {
            $this->placeholders = array_merge($this->placeholders, $placeholders);
            return true;
        } else {
            return false;
        }
    }

    /**
     * Gets some pre-defined modifers (the ones that have special cases for
     * processing in Extender::modify method)
     *
     * @return array - modifier names
     * @see Extender::modify
     */
    public static function getModifiers()
    {
        // some of these are mutually exclusive
        return array(
            'date_format',
            'inwords',
            'number_format',
            /*'display',*/ // not used, it is for Franky config
            'formal',
            'simple',
            'default',
        );
    }

    /**
     * @return string
     */
    public function getOpenTag(): string
    {
        return $this->openTag;
    }

    /**
     * @param string $openTag
     */
    public function setOpenTag(string $openTag): void
    {
        $this->openTag = $openTag;
    }

    /**
     * @return string
     */
    public function getCloseTag(): string
    {
        return $this->closeTag;
    }

    /**
     * @param string $closeTag
     */
    public function setCloseTag(string $closeTag): void
    {
        $this->closeTag = $closeTag;
    }

    /**
     * @return string
     */
    public function getModifierDelimiter(): string
    {
        return $this->modifierDelimiter;
    }

    /**
     * @param string $modifierDelimiter
     */
    public function setModifierDelimiter(string $modifierDelimiter): void
    {
        $this->modifierDelimiter = $modifierDelimiter;
    }

    /**
     * Replace placeholder with a value defined in the list of placeholders, applying all modifiers
     *
     * @param string $placeholderWithModifiers - the variable without the enclosing tags
     * @return string $value - the value of the variable.
     */
    public function replace(string $placeholderWithModifiers) : string
    {
        list($placeholder, $modifiers) = $this->splitPlaceholderModifiers($placeholderWithModifiers);

        $value = $this->getPlaceholderValue($placeholder);

        //nothing to process
        if (is_null($value)) {
            return '';
        }

        //no modifiers, just return value
        if (empty($modifiers)) {
            return is_array($value) ? implode(', ', $value) : $value;
        }

        //modifiers found, apply them
        foreach ($modifiers as $modifier) {
            $value = $this->modify($value, $modifier, $placeholder);
        }
        return $value;
    }

    private function splitPlaceholderModifiers(string $placeholderWithModifiers): array
    {
        $modifiers = preg_split($this->getModifiersRegex(), $placeholderWithModifiers);
        $placeholder = array_shift($modifiers);

        return [$placeholder, $modifiers];
    }

    public function getPlaceholder(string $placeholderWithModifiers): string
    {
        $modifiers = preg_split($this->getModifiersRegex(), $placeholderWithModifiers);
        $placeholder = array_shift($modifiers);

        return $placeholder;
    }

    public function getModifiersRegex(): string
    {
        $openTag = preg_quote($this->getOpenTag());
        $closeTag = preg_quote($this->getCloseTag());
        $modifierSplitTag = preg_quote($this->getModifierDelimiter());
        $regex = "#{$modifierSplitTag}(?![^{$openTag}]+{$closeTag})#";
        return $regex;
    }

    /**
     * Get the placeholders within the content
     *
     * @param string $value - the variable without the enclosing tags
     * replaced if no placeholder for it exists
     * @return string $value - the value of the variable.
     */
    function getPlaceholdersUsed($value)
    {
        // the array with placeholders
        $placeholders = array();

        if (is_array($value)) {
            return $value;
        }

        /**
         * Use recursive subpatterns to get the contents of the outermost pair of brackets.
         * @see http://php.net/manual/en/regexp.reference.recursive.php
         */
        $openTag = preg_quote($this->getOpenTag());
        $closeTag = preg_quote($this->getCloseTag());
        $regex = "#{$openTag}(((?>[^{$openTag}{$closeTag}]+)|(?R))*){$closeTag}#";
        preg_match_all($regex, $value, $matches, PREG_PATTERN_ORDER);
        // all variable names are assigned to $smatches
        $smatches = $matches[1];
        $smatches_count = count($smatches);

        for ($i = 0; $i < $smatches_count; $i++) {
            $match = $smatches[$i];
            $match = strip_tags($match);

            //check for recursive variables and extract them
            $end_pos = strrpos($match, ']');
            if (!is_bool($end_pos) && $end_pos) {
                $start_pos = strpos($match, '[');
                $sub_match = substr($match, $start_pos, ($end_pos - $start_pos + 1));
                $smatches[] = preg_replace("#^{$openTag}(.*){$closeTag}$#", "$1", $sub_match);
            }

            $orig_match = $match;
            // check for modifiers and
            // split by pipe but not when it is within a nested variable
            $modifiers = preg_split("#\|(?![^{$openTag}]+{$closeTag})#", $match);

            $match = array_shift($modifiers);

            $placeholders[$match] = array(
                'original' => $orig_match,
                'modifiers' => $modifiers,
            );
        }

        return $placeholders;
    }
}

?>
