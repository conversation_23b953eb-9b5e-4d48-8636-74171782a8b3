<?php
// $Header: /cvsroot/html2ps/encoding.cp866.inc.php,v 1.4 2007/01/24 18:55:53 Konstantin Exp $

$GLOBALS['g_cp866'] = array(
"\x00" => 0x0000,	//NULL
"\x01" => 0x0001,	//START OF HEADING
"\x02" => 0x0002,	//START OF TEXT
"\x03" => 0x0003,	//END OF TEXT
"\x04" => 0x0004,	//END OF TRANSMISSION
"\x05" => 0x0005,	//ENQUIRY
"\x06" => 0x0006,	//ACKNOWLEDGE
"\x07" => 0x0007,	//BELL
"\x08" => 0x0008,	//BACKSPACE
"\x09" => 0x0009,	//HORIZONTAL TABULATION
"\x0a" => 0x000a,	//LINE FEED
"\x0b" => 0x000b,	//VERTICAL TABULATION
"\x0c" => 0x000c,	//FORM FEED
"\x0d" => 0x000d,	//CARRIAGE RETURN
"\x0e" => 0x000e,	//SHIFT OUT
"\x0f" => 0x000f,	//SHIFT IN
"\x10" => 0x0010,	//DATA LINK ESCAPE
"\x11" => 0x0011,	//DEVICE CONTROL ONE
"\x12" => 0x0012,	//DEVICE CONTROL TWO
"\x13" => 0x0013,	//DEVICE CONTROL THREE
"\x14" => 0x0014,	//DEVICE CONTROL FOUR
"\x15" => 0x0015,	//NEGATIVE ACKNOWLEDGE
"\x16" => 0x0016,	//SYNCHRONOUS IDLE
"\x17" => 0x0017,	//END OF TRANSMISSION BLOCK
"\x18" => 0x0018,	//CANCEL
"\x19" => 0x0019,	//END OF MEDIUM
"\x1a" => 0x001a,	//SUBSTITUTE
"\x1b" => 0x001b,	//ESCAPE
"\x1c" => 0x001c,	//FILE SEPARATOR
"\x1d" => 0x001d,	//GROUP SEPARATOR
"\x1e" => 0x001e,	//RECORD SEPARATOR
"\x1f" => 0x001f,	//UNIT SEPARATOR
"\x20" => 0x0020,	//SPACE
"\x21" => 0x0021,	//EXCLAMATION MARK
"\x22" => 0x0022,	//QUOTATION MARK
"\x23" => 0x0023,	//NUMBER SIGN
"\x24" => 0x0024,	//DOLLAR SIGN
"\x25" => 0x0025,	//PERCENT SIGN
"\x26" => 0x0026,	//AMPERSAND
"\x27" => 0x0027,	//APOSTROPHE
"\x28" => 0x0028,	//LEFT PARENTHESIS
"\x29" => 0x0029,	//RIGHT PARENTHESIS
"\x2a" => 0x002a,	//ASTERISK
"\x2b" => 0x002b,	//PLUS SIGN
"\x2c" => 0x002c,	//COMMA
"\x2d" => 0x002d,	//HYPHEN-MINUS
"\x2e" => 0x002e,	//FULL STOP
"\x2f" => 0x002f,	//SOLIDUS
"\x30" => 0x0030,	//DIGIT ZERO
"\x31" => 0x0031,	//DIGIT ONE
"\x32" => 0x0032,	//DIGIT TWO
"\x33" => 0x0033,	//DIGIT THREE
"\x34" => 0x0034,	//DIGIT FOUR
"\x35" => 0x0035,	//DIGIT FIVE
"\x36" => 0x0036,	//DIGIT SIX
"\x37" => 0x0037,	//DIGIT SEVEN
"\x38" => 0x0038,	//DIGIT EIGHT
"\x39" => 0x0039,	//DIGIT NINE
"\x3a" => 0x003a,	//COLON
"\x3b" => 0x003b,	//SEMICOLON
"\x3c" => 0x003c,	//LESS-THAN SIGN
"\x3d" => 0x003d,	//EQUALS SIGN
"\x3e" => 0x003e,	//GREATER-THAN SIGN
"\x3f" => 0x003f,	//QUESTION MARK
"\x40" => 0x0040,	//COMMERCIAL AT
"\x41" => 0x0041,	//LATIN CAPITAL LETTER A
"\x42" => 0x0042,	//LATIN CAPITAL LETTER B
"\x43" => 0x0043,	//LATIN CAPITAL LETTER C
"\x44" => 0x0044,	//LATIN CAPITAL LETTER D
"\x45" => 0x0045,	//LATIN CAPITAL LETTER E
"\x46" => 0x0046,	//LATIN CAPITAL LETTER F
"\x47" => 0x0047,	//LATIN CAPITAL LETTER G
"\x48" => 0x0048,	//LATIN CAPITAL LETTER H
"\x49" => 0x0049,	//LATIN CAPITAL LETTER I
"\x4a" => 0x004a,	//LATIN CAPITAL LETTER J
"\x4b" => 0x004b,	//LATIN CAPITAL LETTER K
"\x4c" => 0x004c,	//LATIN CAPITAL LETTER L
"\x4d" => 0x004d,	//LATIN CAPITAL LETTER M
"\x4e" => 0x004e,	//LATIN CAPITAL LETTER N
"\x4f" => 0x004f,	//LATIN CAPITAL LETTER O
"\x50" => 0x0050,	//LATIN CAPITAL LETTER P
"\x51" => 0x0051,	//LATIN CAPITAL LETTER Q
"\x52" => 0x0052,	//LATIN CAPITAL LETTER R
"\x53" => 0x0053,	//LATIN CAPITAL LETTER S
"\x54" => 0x0054,	//LATIN CAPITAL LETTER T
"\x55" => 0x0055,	//LATIN CAPITAL LETTER U
"\x56" => 0x0056,	//LATIN CAPITAL LETTER V
"\x57" => 0x0057,	//LATIN CAPITAL LETTER W
"\x58" => 0x0058,	//LATIN CAPITAL LETTER X
"\x59" => 0x0059,	//LATIN CAPITAL LETTER Y
"\x5a" => 0x005a,	//LATIN CAPITAL LETTER Z
"\x5b" => 0x005b,	//LEFT SQUARE BRACKET
"\x5c" => 0x005c,	//REVERSE SOLIDUS
"\x5d" => 0x005d,	//RIGHT SQUARE BRACKET
"\x5e" => 0x005e,	//CIRCUMFLEX ACCENT
"\x5f" => 0x005f,	//LOW LINE
"\x60" => 0x0060,	//GRAVE ACCENT
"\x61" => 0x0061,	//LATIN SMALL LETTER A
"\x62" => 0x0062,	//LATIN SMALL LETTER B
"\x63" => 0x0063,	//LATIN SMALL LETTER C
"\x64" => 0x0064,	//LATIN SMALL LETTER D
"\x65" => 0x0065,	//LATIN SMALL LETTER E
"\x66" => 0x0066,	//LATIN SMALL LETTER F
"\x67" => 0x0067,	//LATIN SMALL LETTER G
"\x68" => 0x0068,	//LATIN SMALL LETTER H
"\x69" => 0x0069,	//LATIN SMALL LETTER I
"\x6a" => 0x006a,	//LATIN SMALL LETTER J
"\x6b" => 0x006b,	//LATIN SMALL LETTER K
"\x6c" => 0x006c,	//LATIN SMALL LETTER L
"\x6d" => 0x006d,	//LATIN SMALL LETTER M
"\x6e" => 0x006e,	//LATIN SMALL LETTER N
"\x6f" => 0x006f,	//LATIN SMALL LETTER O
"\x70" => 0x0070,	//LATIN SMALL LETTER P
"\x71" => 0x0071,	//LATIN SMALL LETTER Q
"\x72" => 0x0072,	//LATIN SMALL LETTER R
"\x73" => 0x0073,	//LATIN SMALL LETTER S
"\x74" => 0x0074,	//LATIN SMALL LETTER T
"\x75" => 0x0075,	//LATIN SMALL LETTER U
"\x76" => 0x0076,	//LATIN SMALL LETTER V
"\x77" => 0x0077,	//LATIN SMALL LETTER W
"\x78" => 0x0078,	//LATIN SMALL LETTER X
"\x79" => 0x0079,	//LATIN SMALL LETTER Y
"\x7a" => 0x007a,	//LATIN SMALL LETTER Z
"\x7b" => 0x007b,	//LEFT CURLY BRACKET
"\x7c" => 0x007c,	//VERTICAL LINE
"\x7d" => 0x007d,	//RIGHT CURLY BRACKET
"\x7e" => 0x007e,	//TILDE
"\x7f" => 0x007f,	//DELETE
"\x80" => 0x0410,	//CYRILLIC CAPITAL LETTER A
"\x81" => 0x0411,	//CYRILLIC CAPITAL LETTER BE
"\x82" => 0x0412,	//CYRILLIC CAPITAL LETTER VE
"\x83" => 0x0413,	//CYRILLIC CAPITAL LETTER GHE
"\x84" => 0x0414,	//CYRILLIC CAPITAL LETTER DE
"\x85" => 0x0415,	//CYRILLIC CAPITAL LETTER IE
"\x86" => 0x0416,	//CYRILLIC CAPITAL LETTER ZHE
"\x87" => 0x0417,	//CYRILLIC CAPITAL LETTER ZE
"\x88" => 0x0418,	//CYRILLIC CAPITAL LETTER I
"\x89" => 0x0419,	//CYRILLIC CAPITAL LETTER SHORT I
"\x8a" => 0x041a,	//CYRILLIC CAPITAL LETTER KA
"\x8b" => 0x041b,	//CYRILLIC CAPITAL LETTER EL
"\x8c" => 0x041c,	//CYRILLIC CAPITAL LETTER EM
"\x8d" => 0x041d,	//CYRILLIC CAPITAL LETTER EN
"\x8e" => 0x041e,	//CYRILLIC CAPITAL LETTER O
"\x8f" => 0x041f,	//CYRILLIC CAPITAL LETTER PE
"\x90" => 0x0420,	//CYRILLIC CAPITAL LETTER ER
"\x91" => 0x0421,	//CYRILLIC CAPITAL LETTER ES
"\x92" => 0x0422,	//CYRILLIC CAPITAL LETTER TE
"\x93" => 0x0423,	//CYRILLIC CAPITAL LETTER U
"\x94" => 0x0424,	//CYRILLIC CAPITAL LETTER EF
"\x95" => 0x0425,	//CYRILLIC CAPITAL LETTER HA
"\x96" => 0x0426,	//CYRILLIC CAPITAL LETTER TSE
"\x97" => 0x0427,	//CYRILLIC CAPITAL LETTER CHE
"\x98" => 0x0428,	//CYRILLIC CAPITAL LETTER SHA
"\x99" => 0x0429,	//CYRILLIC CAPITAL LETTER SHCHA
"\x9a" => 0x042a,	//CYRILLIC CAPITAL LETTER HARD SIGN
"\x9b" => 0x042b,	//CYRILLIC CAPITAL LETTER YERU
"\x9c" => 0x042c,	//CYRILLIC CAPITAL LETTER SOFT SIGN
"\x9d" => 0x042d,	//CYRILLIC CAPITAL LETTER E
"\x9e" => 0x042e,	//CYRILLIC CAPITAL LETTER YU
"\x9f" => 0x042f,	//CYRILLIC CAPITAL LETTER YA
"\xa0" => 0x0430,	//CYRILLIC SMALL LETTER A
"\xa1" => 0x0431,	//CYRILLIC SMALL LETTER BE
"\xa2" => 0x0432,	//CYRILLIC SMALL LETTER VE
"\xa3" => 0x0433,	//CYRILLIC SMALL LETTER GHE
"\xa4" => 0x0434,	//CYRILLIC SMALL LETTER DE
"\xa5" => 0x0435,	//CYRILLIC SMALL LETTER IE
"\xa6" => 0x0436,	//CYRILLIC SMALL LETTER ZHE
"\xa7" => 0x0437,	//CYRILLIC SMALL LETTER ZE
"\xa8" => 0x0438,	//CYRILLIC SMALL LETTER I
"\xa9" => 0x0439,	//CYRILLIC SMALL LETTER SHORT I
"\xaa" => 0x043a,	//CYRILLIC SMALL LETTER KA
"\xab" => 0x043b,	//CYRILLIC SMALL LETTER EL
"\xac" => 0x043c,	//CYRILLIC SMALL LETTER EM
"\xad" => 0x043d,	//CYRILLIC SMALL LETTER EN
"\xae" => 0x043e,	//CYRILLIC SMALL LETTER O
"\xaf" => 0x043f,	//CYRILLIC SMALL LETTER PE
"\xb0" => 0x2591,	//LIGHT SHADE
"\xb1" => 0x2592,	//MEDIUM SHADE
"\xb2" => 0x2593,	//DARK SHADE
"\xb3" => 0x2502,	//BOX DRAWINGS LIGHT VERTICAL
"\xb4" => 0x2524,	//BOX DRAWINGS LIGHT VERTICAL AND LEFT
"\xb5" => 0x2561,	//BOX DRAWINGS VERTICAL SINGLE AND LEFT DOUBLE
"\xb6" => 0x2562,	//BOX DRAWINGS VERTICAL DOUBLE AND LEFT SINGLE
"\xb7" => 0x2556,	//BOX DRAWINGS DOWN DOUBLE AND LEFT SINGLE
"\xb8" => 0x2555,	//BOX DRAWINGS DOWN SINGLE AND LEFT DOUBLE
"\xb9" => 0x2563,	//BOX DRAWINGS DOUBLE VERTICAL AND LEFT
"\xba" => 0x2551,	//BOX DRAWINGS DOUBLE VERTICAL
"\xbb" => 0x2557,	//BOX DRAWINGS DOUBLE DOWN AND LEFT
"\xbc" => 0x255d,	//BOX DRAWINGS DOUBLE UP AND LEFT
"\xbd" => 0x255c,	//BOX DRAWINGS UP DOUBLE AND LEFT SINGLE
"\xbe" => 0x255b,	//BOX DRAWINGS UP SINGLE AND LEFT DOUBLE
"\xbf" => 0x2510,	//BOX DRAWINGS LIGHT DOWN AND LEFT
"\xc0" => 0x2514,	//BOX DRAWINGS LIGHT UP AND RIGHT
"\xc1" => 0x2534,	//BOX DRAWINGS LIGHT UP AND HORIZONTAL
"\xc2" => 0x252c,	//BOX DRAWINGS LIGHT DOWN AND HORIZONTAL
"\xc3" => 0x251c,	//BOX DRAWINGS LIGHT VERTICAL AND RIGHT
"\xc4" => 0x2500,	//BOX DRAWINGS LIGHT HORIZONTAL
"\xc5" => 0x253c,	//BOX DRAWINGS LIGHT VERTICAL AND HORIZONTAL
"\xc6" => 0x255e,	//BOX DRAWINGS VERTICAL SINGLE AND RIGHT DOUBLE
"\xc7" => 0x255f,	//BOX DRAWINGS VERTICAL DOUBLE AND RIGHT SINGLE
"\xc8" => 0x255a,	//BOX DRAWINGS DOUBLE UP AND RIGHT
"\xc9" => 0x2554,	//BOX DRAWINGS DOUBLE DOWN AND RIGHT
"\xca" => 0x2569,	//BOX DRAWINGS DOUBLE UP AND HORIZONTAL
"\xcb" => 0x2566,	//BOX DRAWINGS DOUBLE DOWN AND HORIZONTAL
"\xcc" => 0x2560,	//BOX DRAWINGS DOUBLE VERTICAL AND RIGHT
"\xcd" => 0x2550,	//BOX DRAWINGS DOUBLE HORIZONTAL
"\xce" => 0x256c,	//BOX DRAWINGS DOUBLE VERTICAL AND HORIZONTAL
"\xcf" => 0x2567,	//BOX DRAWINGS UP SINGLE AND HORIZONTAL DOUBLE
"\xd0" => 0x2568,	//BOX DRAWINGS UP DOUBLE AND HORIZONTAL SINGLE
"\xd1" => 0x2564,	//BOX DRAWINGS DOWN SINGLE AND HORIZONTAL DOUBLE
"\xd2" => 0x2565,	//BOX DRAWINGS DOWN DOUBLE AND HORIZONTAL SINGLE
"\xd3" => 0x2559,	//BOX DRAWINGS UP DOUBLE AND RIGHT SINGLE
"\xd4" => 0x2558,	//BOX DRAWINGS UP SINGLE AND RIGHT DOUBLE
"\xd5" => 0x2552,	//BOX DRAWINGS DOWN SINGLE AND RIGHT DOUBLE
"\xd6" => 0x2553,	//BOX DRAWINGS DOWN DOUBLE AND RIGHT SINGLE
"\xd7" => 0x256b,	//BOX DRAWINGS VERTICAL DOUBLE AND HORIZONTAL SINGLE
"\xd8" => 0x256a,	//BOX DRAWINGS VERTICAL SINGLE AND HORIZONTAL DOUBLE
"\xd9" => 0x2518,	//BOX DRAWINGS LIGHT UP AND LEFT
"\xda" => 0x250c,	//BOX DRAWINGS LIGHT DOWN AND RIGHT
"\xdb" => 0x2588,	//FULL BLOCK
"\xdc" => 0x2584,	//LOWER HALF BLOCK
"\xdd" => 0x258c,	//LEFT HALF BLOCK
"\xde" => 0x2590,	//RIGHT HALF BLOCK
"\xdf" => 0x2580,	//UPPER HALF BLOCK
"\xe0" => 0x0440,	//CYRILLIC SMALL LETTER ER
"\xe1" => 0x0441,	//CYRILLIC SMALL LETTER ES
"\xe2" => 0x0442,	//CYRILLIC SMALL LETTER TE
"\xe3" => 0x0443,	//CYRILLIC SMALL LETTER U
"\xe4" => 0x0444,	//CYRILLIC SMALL LETTER EF
"\xe5" => 0x0445,	//CYRILLIC SMALL LETTER HA
"\xe6" => 0x0446,	//CYRILLIC SMALL LETTER TSE
"\xe7" => 0x0447,	//CYRILLIC SMALL LETTER CHE
"\xe8" => 0x0448,	//CYRILLIC SMALL LETTER SHA
"\xe9" => 0x0449,	//CYRILLIC SMALL LETTER SHCHA
"\xea" => 0x044a,	//CYRILLIC SMALL LETTER HARD SIGN
"\xeb" => 0x044b,	//CYRILLIC SMALL LETTER YERU
"\xec" => 0x044c,	//CYRILLIC SMALL LETTER SOFT SIGN
"\xed" => 0x044d,	//CYRILLIC SMALL LETTER E
"\xee" => 0x044e,	//CYRILLIC SMALL LETTER YU
"\xef" => 0x044f,	//CYRILLIC SMALL LETTER YA
"\xf0" => 0x0401,	//CYRILLIC CAPITAL LETTER IO
"\xf1" => 0x0451,	//CYRILLIC SMALL LETTER IO
"\xf2" => 0x0404,	//CYRILLIC CAPITAL LETTER UKRAINIAN IE
"\xf3" => 0x0454,	//CYRILLIC SMALL LETTER UKRAINIAN IE
"\xf4" => 0x0407,	//CYRILLIC CAPITAL LETTER YI
"\xf5" => 0x0457,	//CYRILLIC SMALL LETTER YI
"\xf6" => 0x040e,	//CYRILLIC CAPITAL LETTER SHORT U
"\xf7" => 0x045e,	//CYRILLIC SMALL LETTER SHORT U
"\xf8" => 0x00b0,	//DEGREE SIGN
"\xf9" => 0x2219,	//BULLET OPERATOR
"\xfa" => 0x00b7,	//MIDDLE DOT
"\xfb" => 0x221a,	//SQUARE ROOT
"\xfc" => 0x2116,	//NUMERO SIGN
"\xfd" => 0x00a4,	//CURRENCY SIGN
"\xfe" => 0x25a0,	//BLACK SQUARE
"\xff" => 0x00a0,	//NO-BREAK SPACE
);
?>