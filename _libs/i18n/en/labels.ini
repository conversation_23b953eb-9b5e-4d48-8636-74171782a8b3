image = Photo
images = Photos
main_thumb_image = Basic photo
thumb_image = Small photo
size = Size
sitename = nZoom
subheader = Administration part of nZoom
home = Home
name = Name
go = Go
delete = Delete
restore = Recovery
purge = Purge
list = List
remove = Remove
add = Add
addsubstage = Add substage
stageactivities = Stage activities
addevent = Add Event
multiadd = Add +
multiedit = Edit +
edit = Edit
edittopic = Subject
editclause = Terms
editfinance = Finance
relatives = Links
skip_relatives = Without link
help_skip_relatives = When the box is checked, no link will be created between the current entry and the new one.
payments = Balance
approve = Approve issue
approve_send = Approve issue & send
disapprove = Disapprove
issue_send = Issue & send
dependencies = Dependencies
history = History
history_activity = Activity
statistics = Statistics
history_item_title = Click here to view the modifications ([audits])
history_event_type = Event
history_event_text = Description
comments = Comments
emails = E-mails
add_email = Send e-mail
minitasks = Mini tasks
timesheets = Timesheets
statements = Timesheets
addtimesheet = Quick add timesheet
allocate = Allocate
permissions = Permissions
assignments = Assignments
menu = Menu
setstatus = Status
setstatus_unlock = Unlock of document
contactpersons = Contact Persons
cancel = Cancel
help_cancel = Cancel action
ok = OK
confirm = Confrim
skip_confirm_action = Don't ask for confirmation for this action.
profile = Profile
password = Password
view = View
viewtopic = Subject
viewclause = Terms
viewfinance = Finance
listinvoices = Invoices
listhandovers = Handovers
stages = Stages
phases = Phases
send = Send
save = Save
link = Link to
select = Select
close = Close
draggable = Move
change = Change
change_status = Change status
active_state = Active state
activate = Activate
deactivate = Deactivate
activated = Activated
deactivated = Deactivated
include_deactivated = Include deactivated
only_deactivated = Only deactivated
activated_deactivated = Activated and deactivated
is_portal = Portal
is_not_portal = Not Portal
update = Update
group = Group
ownership_group = Group belonging
assign = Assign
finish = Finish
finishStage = Finish Stage
translate = Translate
clone = Clone
transform = Transformation
multitransform = Transformation +
transformation = Transformation
transformations = Transformations
transfer = Transfer
generate = Generate
preview = Review
welcome = Hello
attachments = Files
calendars = Calendar
remind = Remind
custom_message = Text
addannex = Add agreement
manage_outlooks = Edit outlook
distribute = Distribute
allocate_costs = Allocate costs to deliveries
enter = Enter
control = Control
unsubscribed = Unsubscribed recipients
all = All
noone = None
mine = Mine
more = more
back = Back
next = Next
settings = Settings
settings_panel = Settings
settings_panel_legend = Settings panel of nZoom
search = Search
search_advanced = Advanced search
search_simple = Basic search
search_show_advanced = Show advanced search
search_show_simple = Show basic search
articles = Articles
compound = Compound Articles
services = Services
report = Report
reports = Reports
analyses = Analyses
automation = Automation
dashlet = Dashlet
outlooks = Outlooks
helps = Help sections
imports = Import
forward = Dispensation
display = Results
display_rpp = Results per page
sort = Sort by
order = Sort order
asc = Ascending
desc = Descending
key = Keyword
field = Search field
active = Active
deactive = Inactive
activation = Activation
copy = Copy
copy_all = Copy all
myassigned = Assigned
myresponsible = Supervise
myobserver = Inform
myrecords = My records
myfinished = Finished
myfailed = Failed
num = #
num_inwords = Num.
from = From
to = To
yes = yes
no = no
yes_allow_zero = yes, allow zero
suggest = suggest
by = by
or = or
and = and
on = on
of = of
for = for
seconds = seconds
please_select = please, select
no_select_records = no records
no_data = No data
undefined = unspecified
input = Please input
select_or_input = Please select or input
select_multiple = Multiple selection
placeholders = Placeholders
system_info = Information
help = Help
auto_generated = Automatic generate
date = Date
added_date = Date added
added_time = Added time
added = Added on
added_by = Added by
modified_date = Modified date
modified_time = Modified time
modified = Modified on
modified_by = Modified by
annulled_date = Date annulled
annulled_time = Annulled time
annulled = Annulled on
annulled_by = Annulled by
status_modified = Status changed on
status_modified_by = Status changed by
include_deleted = Include deleted
only_deleted = Only deleted
deleted_not_deleted = Deleted and not deleted
deleted = Deleted on
deleted_by = Deleted by
search_filters = Search filters
search_params = Display results settings
load_search = Load filter
save_search = Save filter
save_as_action = Include in menu
save_load_filters = Save or load search filters
timer_total = Execution time
timer_render = Patterns time
content_size = Contents size
options = Options
calculate = Calculate
language = Language
translations = Translations
available_translations = Made translations
posible_translations = Translations
make_translation = Make translation
save_filter = Save filter
filter = Filter
searchfromdoc = Search
saved_filter = Save search
expand = Expand...
expand_all = Show all
expand_all_layouts = Show all layouts
expand_next = Show next
collapse = Collapse...
collapse_all = Hide all
collapse_all_layouts = Hide all layouts
show_more = Show more...
show_other = More
system_settings = System settings
system_settings_show = Show/Hide system settings
link_title = Description of link
link_type = Link Type
link_url = Full url
available_after_actions = Next step
available_after_actions_show = Show/Hide next step
mynzoom = My nZoom
months = January, February, March, April, May, June, July, August, September, October, November, December
attach = Attach
attached_files = Attached files
upload = Upload
download = Download
open = Open
export = Export
import = Import
print = Print
printlist = Print list
rev = Version
tree = Tree
observe = Inform
stopobserve = Stop to inform
count_selected_items = Number of selected items
num_of_selected_items = of %d of selected
del_selected = Reset the selected items
category = Category
delimiter_semicolon     = Semicolon
delimiter_comma         = Comma
delimiter_tab           = Tab
add_comment = Add comment
edit_comment = Edit comment
full_comment = Show full comment
part_comment = Hide full comment
full_text = Show full text
part_text = Hide text
full_email = Show full e-mail
part_email = Hide full e-mail
content_email = Content
resend_email = Resend
full_content = Show full content
part_content = Hide full content
reply = Reply
reply_all = Reply All
choose_article  = Choose article...
when_edit = By edit
clear_filters = Clear filters
help_show = Show/Hide help text
help_information_panel = Information panel
create = Create
tags = Tags
tag = Tags
confirm_tags = Confirm tags
tags_change = Change tags
removed_tags = Tags removed from record: %s
added_tags = Tags added to record: %s
removed_and_added_tags = Tags removed from record: %s, tags added: %s
model_tag_notify = for modification of tags of %s
tags_no_section = no group
events = Events
total = Total
empty_contact_person = contact person
project_phase = phase
stopwatch = Stopwatch
stopwatch_started_prefix = Stopwatch for
stopwatch_started_suffix = was started at
start_watch = Start watch for record
stop_watch = Stop watch for record
add_row = Add new row
remove_row = Remove last row
load_defaults = Load Defaults
text = text
allday_events = All Day Events
related_records = Related records
finance = Finance
warehouse = Warehouse
contracts = Contracts
portal_users = Portal Users
normal_users = Users
portal_user = Portal User
normal_user = User
department = Department
office = Office
offices = Offices
employee = Employee
employees = Employees
data = Data
basic_data = Registration
additional_data = Data
position = Position
issue = Issue
quantity = Quantity
available = Available
reserve = Reserve
reserved = Reserved
previous_list = Previous list
previous_search = Previous search
record_changed_by_at = List of changes made by %s at %s
statusfailed = Cancel
communications = Communications
advanced_invoice = Advanced invoice
medial_invoice = Medial invoice
parties = Parties
annul = Annulment
current_date = Current date
fiscal = fiscal
rights = Rights
currency = currency
type = Type
status = Status
issue_date = Issue date
date_of_payment = Date of payment
date_sign = Date of signature
date_start = Start date
date_validity = Validity date
origin_inherited = Inherited
origin_cloned = Cloned
origin_transformed = Transformed
code = Code
kind = Kind
database_copy_date = Database copied on:
responsible = Responsible
no_number = [no number]

salutation_mr = Mr.
salutation_mrs = Mrs.
salutation_ms = Ms.
salutation_vocative_mr = Mr.
salutation_vocative_mrs = Mrs.
salutation_vocative_ms = Ms.
dear_m = Dear
dear_f = Dear

phone = tel.
address = address
fax = fax
email = email
url = www

minute = minute
minutes = minutes
minutes_short = min
hour = hour
hours = hours
hours_short = h
day = day
days = days
days_short = d
week = week
month = month
months_short = m
trimester = Trimester
year = year
years = years
year_short = y

signout = Sign Out
renew = Renew
enlarge = Enlarge

file = File
has_files = Contains files
file_name = Name of the exported file
file_format = Format of the exported file
separator = Separator
include_group_tables = Include table data
file_format_xls = xls
file_format_xlsx = xlsx
file_format_csv = csv

eq = equal
ne = not equal
lt = less
gt = greater
le = less or equal
ge = greater or equal
like = contains

unlock = Unlock
record_locked =  The record is locked and cannot be edited.
record_locked_no_access = At the moment you have no access to this record.
record_locked_by_me = The record is automatically locked.
record_locked_record = You have 1 locked record.
record_locked_records = You have %d locked records.
record_locked_records_view = If you want to review your locked records, please click the icon.
record_locked_records_text = You have locked records, which <strong>could not be accessed by other users</strong> until you unlock or save them. If you want to unlock all locked records, please click the icon to the left.

required_statuses_option_without_comment = Without comment
required_statuses_option_requires_comment = Required comment
required_statuses_option_optional_comment = Optional comment

locked_records = Locked records
locked_records_legend = List of all records locked by me with the possibility to be unlocked
lr_model = Record type
lr_lock_date = Date of locking
lr_lock_by = Locked by

lr_model_task = Task
lr_model_project = Project
lr_model_customer = Contractor
lr_model_customers_type = Contractor Type
lr_model_user = User
lr_model_role = Role
lr_model_group = Group
lr_model_employee = Employee
lr_model_office = Office
lr_model_email = Notification letter
lr_model_event = Event
lr_model_document = Document
lr_model_documents_type = Document type
lr_model_documents_media = Source of document
lr_model_documents_counter = Document counter
lr_model_stage = Stage
lr_model_pattern = Pattern
lr_model_patterns_part = Pattern Header/Footer
lr_model_file = File
lr_model_categories = Nomenclatures: Category
lr_model_layout = Section
lr_model_dashlet = Dashlet
lr_model_announcement = Announcement
lr_model_contract = Contract
lr_model_nomenclature = Nomenclature
lr_model_configuration = Configuration

outlooks_personal_settings = Personalize outlook
help_outlooks_personal_settings = The columns can be moved by drag and drop to the desired position

side_panels_personal_settings = Personalize related panels
help_side_panels_personal_settings = Panels can be moved by drag and drop to the desired position
side_panels_update_all_actions = Save for all actions
side_panels_restore_defaults = Restore defaults

record_reminder = You have 1 reminder record.
record_reminders = You have %d reminder records.
record_reminders_view = Review of reminder records.
record_reminders_text = You have reminder records.
remind_after = Remind me again in
remind_stop = Stop reminder

param = Parameter
value = Value
formula = Formula
index_for = Index for
filters = Filters
configurator_title = Templates
configurator_load_save = Load/Save template
configurator_delete = Delete template
configurator_save = Save template
configurator_reload = Load template
config_load = Load
configurator_saved = Templates

last_modification_date = Date of last edit
last_actualization = Last actualization by

no_image = No image
allowed_image_size = Maximum size
allowed_image_type = Allow image types
delete_picture = Delete picture
no_additional_vars = No additional data for this data type
no_additional_vals = Missing value for additional data

check_all = Select all
check_none = Deselect all
check_to_include = Mark, if you want include in list
check_to_include_all = Mark, if you want include all in list
check_to_set_default = Mark to set as default value
click_to_zoom = Click to zoom
click_to_delete = Click to delete

required = <span class="required">*</span>
no_items_found = No items found
no_files_found = No files found
items_found = Found items
no_changes_made = No changes were made
more_items_found = More items found

multitransform_count = Transformation
message_translatable_items = The fields marked in yellow are language-dependent

layout = Layout
layouts = Layouts
layout_index = Go to layout
permissions_index = Index of permissions
back_to_index = Back to index
go_to_end = Go to end

edit_by_columns = Simultaneous editing by columns

timesheets_configurator_title = Timesheet templates
timesheets_configurator_load_save = Load/Save timesheet template
timesheets_configurator_reload = Load timesheet template
timesheets_configurator_save = Save timesheet template
timesheets_configurator_delete = Delete timesheet template

subject = Subject
comment = Comment

add_new_element = Add new element

select_all = Select all
select_page = This page only
select_first_x = Select the first [x]
select_none = Deselect all

current_user = Current user
current_customer = Current customer
current_user_departments = Current user's departments
deleted_only = Deleted only
deleted_all = Deleted and not deleted
no_user_assigned = No user assigned
not_read_by_anyone = Not read by anyone
unread = Not read
nobody = Nobody

inactive_option = * An inactive option

export_what = What to be exported
export_all = All items
export_selected = Selected items
export_empty_file = Blank template

count_users_notified = E-mail %s has been sent to %d users!
names_users_notified = E-mail %s has been sent to %s

autocomplete_search_nomenclatures = Search/Choose nomenclatures
autocomplete_search_documents = Search/Choose documents
autocomplete_search_customers = Search/Choose customers
autocomplete_search_contactpersons = Search/Choose contact persons
autocomplete_search_projects = Search/Choose projects
autocomplete_search_tasks = Search/Choose tasks
autocomplete_search_contracts = Search/Choose contracts
autocomplete_search_users = Search/Choose users
autocomplete_search_departments = Search/Choose departments
autocomplete_search_report = Search/Choose via report

autocomplete_add_customers = Add customer
autocomplete_add_projects = Add project
autocomplete_add_nomenclatures = Add nomenclature

autocomplete_edit_customers = Edit customer
autocomplete_edit_nomenclatures = Edit nomenclature

autocomplete_refresh_nomenclatures = Refresh nomenclatures data
autocomplete_refresh_documents = Refresh documents data
autocomplete_refresh_customers = Refresh customers data
autocomplete_refresh_contactpersons = Refresh contact persons data
autocomplete_refresh_projects = Refresh projects data
autocomplete_refresh_tasks = Refresh tasks data
autocomplete_refresh_contracts = Refresh contracts data
autocomplete_refresh_users = Refresh users data
autocomplete_refresh_departments = Refresh departments data

autocomplete_clear_nomenclatures = Clear nomenclatures data
autocomplete_clear_documents = Clear documents data
autocomplete_clear_customers = Clear customers data
autocomplete_clear_contactpersons = Clear contact persons data
autocomplete_clear_projects = Clear projects data
autocomplete_clear_tasks = Clear tasks data
autocomplete_clear_contracts = Clear contracts data
autocomplete_clear_users = Clear users data
autocomplete_clear_departments = Clear departments data

autocomplete_refresh_items = Refresh
autocomplete_clear_items = Clear
autocomplete_search_items = Search
autocomplete_add_rows = Add rows

in_words = in words
displaysettings = Display settings
printsettings = Print settings
emailsettings = E-mail settings

addinvoice = Add invoice
addcorrect = Revaluation document
addcreditdebit = Credit/Debit note
addcredit = Credit note
adddebit = Debit note
addproformainvoice = Add proforma invoice
addhandover = Add handover
addhandover_incoming = Add receival record
addhandover_outgoing = Add handover record
printform = Print form
addpayment = Add payment
nopay = Without payment
advances = Advances
proformaadvances = Proforma advances
balance = Balance
revision = Revision

messages_expand_items = Show all %d
messages_collapse_items = Show only first %d
errors = errors
messages = messages
warnings = warnings

placeholders_document = Document data
placeholders_customer = Contractor data
placeholders_currentuser = Current user data
placeholders_user = User data
placeholders_system = System data
placeholders_project = Project data
placeholders_event = Event data
placeholders_task = Task data
placeholders_announcement = Announcement data
placeholders_finance = Finance data
placeholders_finance_company = Company data
placeholders_finance_incomes_reason = Revenue document data
placeholders_finance_expenses_reason = Expense document data
placeholders_finance_warehouses_document = Warehouse document data
placeholders_finance_annulment = Invoice cancellation data
placeholders_finance_payment = Payment data
placeholders_finance_budget = Budget data
placeholders_recipient = Recipient data
placeholders_report = Report data
placeholders_contract = Contract data
placeholders_emails_campaign = E-mail campaign data
placeholders_minitask = Mini task data
placeholders_records_list = Data of list of records
placeholders_automation = Automation data
placeholders_model = Record data
placeholders_additional_list = Additional data list
placeholders_label = Placeholder name
placeholders_varname = Variable Name
placeholders_default_value = Value

defined_by_data = defined by data
legend = Legend
legend_added_row = New row
legend_deleted_row = Deleted row
legend_old_value = Old value
legend_new_value = New value

commodities_reservation = Commodities reservation
vars_completed = Completed
vars_empty = Empty
period_start = beginning
period_end = end
period_issue = issue date
period_period = of the period
working_days = working
calendar_days = calendar
after_receive = after receive
after_issue = after issue
working_days_days = working days

archive_action = Action
archiving = Archiving
extracting = Extracting
archive_what = Records
archive_all = All records in the list
archive_selected = Selected records
archive_state = Archived
archive_state_all = Archived and not archived
archive_state_archive = Archived only
archive = Archiving
extract = Extracting
multiarchive = Archive
archived = Archive date
payment_type_cheque = card
moved_from = Moved from %s

annulled_state = Annulled
annulled_only = Annulled only
annulled_all = Annulled and not annulled

first_page = &lsaquo;&lsaquo; first
previous_page = &laquo; previous
next_page = next &rsaquo;
last_page = last &raquo;&raquo;

#first_page = &lt;&lt;
#previous_page = &lt;
#next_page = &gt;
#last_page = &gt;&gt;

automation_success = Successful
automation_failed = Unsuccessful

automation_messages = Messages
automation_errors = Errors
automation_warnings = Warnings

mail_status = Mail status
email_sent_received_comment_reason = Mail sent and received successfully!
email_received_comment_reason = Incoming mail!
email_sent_not_received_comment_reason = Mail sent, but not received!
email_not_sent_unsent_reason = Mail was not sent! Possible reason is stopped notifications for the installation.
email_not_sent_failed_reason = Mail sending failed! An error occurred.
email_not_sent_fake_mail_reason = Mail sending failed! Invalid e-mail address!
email_sent_received_comment_reason_address = SUCCESSFUL sending to %s! The e-mail was sent and received successfully!
email_not_sent_unsent_reason_address = FAILED sending to %s! Possible reason is that the notifications for the installation are stopped.
email_not_sent_failed_reason_address = FAILED sending to %s! E-mail is not sent because an error occurred!
email_not_sent_fake_mail_reason_address = FAILED sending to %s! Fake address!
email_pending_reason = Mail has not been sent yet.

announcement_hide_bar = Hide announcement bar
announcement_view_content = Click to view full content of announcement
commercial_announcements_off = Displaying of commercial announcements is stopped for the installation

dont_notify_edit = Don't notify

import_table = Import to table
