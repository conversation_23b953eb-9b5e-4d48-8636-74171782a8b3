<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.6/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         cacheResultFile=".phpunit.result.cache"
         executionOrder="depends,defects"
         forceCoversAnnotation="false"
         beStrictAboutCoversAnnotation="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         convertDeprecationsToExceptions="true"
         failOnRisky="true"
         failOnWarning="true"
         verbose="true">
    <testsuites>
        <testsuite name="Nzoom">
            <directory>tests</directory>
        </testsuite>
        <testsuite name="Export">
            <directory>tests/Nzoom/Export</directory>
        </testsuite>
    </testsuites>

    <coverage cacheDirectory=".phpunit.cache"
              processUncoveredFiles="false">
        <include>
            <directory suffix=".php">Nzoom/Export</directory>
        </include>
        <exclude>
            <directory>Nzoom/Export/Examples</directory>
            <directory>Nzoom/Export/docs</directory>
            <file>Nzoom/Export/Documents (12).xlsx</file>
        </exclude>
        <report>
            <html outputDirectory="coverage" lowUpperBound="35" highLowerBound="70"/>
            <text outputFile="coverage.txt" showUncoveredFiles="false"/>
        </report>
    </coverage>

    <php>
        <ini name="error_reporting" value="-1" />
        <ini name="memory_limit" value="-1" />
    </php>
</phpunit>
