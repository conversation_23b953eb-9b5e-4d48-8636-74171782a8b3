{* display layout index as a dropdown menu *}
{if $vars_index}
  <div {if $display eq 'abs_div'}class="abs_div" style="visibility: hidden;"{else}class="action_tabs layouts_index"{/if}>
    <a name="vars_index"></a>
    <ul id="vars_index" class="zpHideOnLoad">
      {include file="`$theme->templatesDir`_drop_menu_item.html" action_options=$vars_index}
    </ul>
    <script type="text/javascript">
      new Zapatec.Menu({ldelim}source: 'vars_index',
                        hideDelay: 100,
                        theme: 'nzoom'{rdelim});
    </script>
  </div>
{/if}