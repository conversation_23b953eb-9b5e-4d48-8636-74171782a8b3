{if $available_action.show_form}
  <form method="get" action="{$smarty.server.PHP_SELF}"{if $redirect_to_url && $update_target} onsubmit="ajaxUpdater({ldelim}link: this.action, target: '{$update_target}', parameters: Form.serialize(this) + '&use_ajax=1&redirect_to_url={$redirect_to_url|escape:'url'}'{rdelim}); lb.deactivate(); return false;"{/if}>
    <input type="hidden" name="{$available_action.module_param}" value="{$available_action.module}" />
    {if $available_action.controller_param}
      <input type="hidden" name="{$available_action.controller_param}" value="{$available_action.controller}" />
      <input type="hidden" name="{$available_action.controller}" value="{$available_action.action}" />
    {else}
      <input type="hidden" name="{$available_action.module}" value="{$available_action.action}" />
    {/if}
    {if $available_action.model_id}
      <input type="hidden" name="{$available_action.action}" value="{$available_action.model_id}" />
    {/if}
    {if $available_action.model_lang}
      <input type="hidden" name="model_lang" value="{$available_action.model_lang}" />
    {/if}
{/if}

  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td class="vtop">
        <table cellpadding="0" cellspacing="0" border="0" class="t_layout_table">
          <tr>
            {if !$available_action.show_form}
            <td class="labelbox"><a name="error_tags"><label{if $messages->getErrors('tags')} class="error"{/if}>{help label_content=$available_action.label|escape}</label></a></td>
            <td class="unrequired">&nbsp;</td>
            {/if}
            <td>
              {assign var='tags_mode' value=''}
              {if $model->checkPermissions('tags_view')}
                {capture assign='tags_mode'}{if $model->checkPermissions('tags_edit')}addedit{else}view{/if}{/capture}
                {include file=_tags.html mode=$tags_mode}
              {/if}
            </td>
          </tr>
          {if $model->get('available_tags_count') gt 0 && $tags_mode eq 'addedit'}
          <tr>
            <td{if !$available_action.show_form} colspan="3"{/if}>
              {if $action eq 'ajax_tag'}
              <button type="button" name="saveButton1" class="button" onclick="if(lb && lb.params && typeof lb.params.saveFunction == 'function') {ldelim} lb.params.saveFunction.apply(this,[lb]);{rdelim} else {ldelim} this.form.submit() {rdelim}">{$available_action.options.label|escape}</button>
              {else}
              <button type="submit" class="button" name="{$available_action.name}Go" id="{$available_action.name}Go" title="{$available_action.options.label}">{$available_action.options.label|escape}</button>
              {/if}
            </td>
          </tr>
          {/if}
        </table>
      </td>
    </tr>
  </table>

{if $available_action.show_form}
  </form>
{/if}
