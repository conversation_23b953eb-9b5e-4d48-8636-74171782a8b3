{**
 * Buttons panel for GT/GT2 variables
 * ALLOWED PARAMETERS:
 *
 * var - the GT/GT2 variable
 * values_count - number of rows of values
 * floating_buttons - whether regular static of floating buttons should be displayed
 *}
{if empty($values_count) || !is_numeric($values_count)}{assign var='values_count' value=0}{/if}
{capture assign='button_id_suffix'}{if !empty($floating_buttons)}_floating{/if}{/capture}

<div class="t_buttons{if $floating_buttons} t_floating_buttons invisible" onmouseover="toggleFloatingButtonsVisibility(this, 1);" onmouseout="toggleFloatingButtonsVisibility(this, 0);{/if}">
  {if empty($var.hide_multiple_rows_buttons) && !empty($var.show_import_button)}
    {json assign='query_params' encode=$var.show_import_button} 
    <div onclick="importTableShowForm('var_group_{$var.grouping}', {$query_params|escape});" {help label_content=#import# popup_only=1}>
      <div class="t_import" title="{#import_table#|escape}"></div>
    </div>
  {/if}
  {*if !empty($var.show_export_button)}
    <div onclick="exportTableShowForm('var_group_{$var.grouping}');" {help label_content=#export# popup_only=1}>
      <div class="t_export"></div>
    </div>
  {/if*}
  {if empty($var.hide_multiple_rows_buttons) && !empty($var.show_select_buttons)}
    {foreach from=$var.show_select_buttons item='button'}
      {if in_array($button, array('nomenclatures', 'customers', 'documents', 'projects', 'tasks', 'contracts', 'users', 'departments'))}
        {* ITEMS Search Button *}
        {capture assign='filter_items'}autocomplete_search_{$button}{/capture}
        <div id="{$button}_filterButton{$button_id_suffix}_{$var.grouping}" onclick="filterAutocompleteItems({ldelim}table: 'var_group_{$var.grouping}', type: '{$button}', source_field: '{$var.select_buttons_search_var}'{rdelim});" {help label_content=#autocomplete_add_rows# text_content=$smarty.config.$filter_items popup_only=1}><div class="t_filter_{$button}"></div></div>
      {/if}
    {/foreach}
  {/if}
  {if !empty($var.show_refresh_buttons)}
    {foreach from=$var.show_refresh_buttons item='button'}
      {if in_array($button, array('nomenclatures', 'customers', 'documents', 'projects', 'tasks', 'contracts', 'users', 'departments'))}
        {* ITEMS Refresh Button *}
        {capture assign='refresh_items'}autocomplete_refresh_{$button}{/capture}
        <div id="{$button}_refreshButton{$button_id_suffix}_{$var.grouping}" onclick="refreshAutocompleteItems({ldelim}table: 'var_group_{$var.grouping}', type: '{$button}'{rdelim});" {help label_content=#autocomplete_refresh_items# text_content=$smarty.config.$refresh_items popup_only=1}><div class="t_refresh_{$button}"></div></div>
      {/if}
    {/foreach}
  {/if}
  {if empty($var.hide_multiple_rows_buttons)}
    {strip}
      <div id="var_group_{$var.grouping}_plusButton{$button_id_suffix}"{if $values_count >= $smarty.const.PH_MAX_GROUP_ROWS} class="disabled"{/if} onclick="addField('var_group_{$var.grouping}'{if $var.type eq 'grouping' and $var.dont_copy_values},'','','{$var.dont_copy_values}'{/if}){if $var.upon_table_rows_update};{$var.upon_table_rows_update}{/if}" {help label_content=#add_row# popup_only=1}><div class="t_plus"></div></div>
      <div id="var_group_{$var.grouping}_minusButton{$button_id_suffix}"{if $values_count le 1} class="disabled"{/if} onclick="removeField('var_group_{$var.grouping}'){if $var.upon_table_rows_update};{$var.upon_table_rows_update}{/if}" {help label_content=#remove_row# popup_only=1}><div class="t_minus"></div></div>
    {/strip}
  {/if}
</div>
