#####################################################################
### SQL nZoom Specific Updates Veolia (http://veolia.n-zoom.com/) ###
#####################################################################

######################################################################################
# 2022-07-04 - Added new automation to update nomenclature indications

# Added new automation to update nomenclature inspections
# Document type 5, substatus 17
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизиране на показания (визуално отчитане)', 0, NULL, 1, 'documents', NULL, 'before_action', '5', 'nom_types := 19, 20, 21', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $this->registry->get(\'request\')->get(\'substatus\') == \'closed_17\' && \'[prev_b_substatus]\' != \'17\'', 'plugin := veolia\r\nmethod := updateIndications', 'cancel_action_on_fail := 1', 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%updateIndications%' AND start_model_type=5);
# Document type 6, substatus 15
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизиране на показания (дистанционно отчитане)', 0, NULL, 1, 'documents', NULL, 'before_action', '6', 'nom_types := 19, 20, 21', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $this->registry->get(\'request\')->get(\'substatus\') == \'closed_15\' && \'[prev_b_substatus]\' != \'15\'', 'plugin := veolia\r\nmethod := updateIndications', 'cancel_action_on_fail := 1', 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%updateIndications%' AND start_model_type=6);
# Document type 7, substatus 18
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизиране на показания (самоотчет)', 0, NULL, 1, 'documents', NULL, 'before_action', '7', 'nom_types := 19, 20, 21', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $this->registry->get(\'request\')->get(\'substatus\') == \'closed_18\' && \'[prev_b_substatus]\' != \'18\'', 'plugin := veolia\r\nmethod := updateIndications', 'cancel_action_on_fail := 1', 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%updateIndications%' AND start_model_type=7);
# Document type 8, substatus 12
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизиране на показания (главни уреди)', 0, NULL, 1, 'documents', NULL, 'before_action', '8', 'nom_types := 16, 17, 18', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $this->registry->get(\'request\')->get(\'substatus\') == \'closed_12\' && \'[prev_b_substatus]\' != \'12\'', 'plugin := veolia\r\nmethod := updateIndications', 'cancel_action_on_fail := 1', 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%updateIndications%' AND start_model_type=8);

######################################################################################
# 2022-07-06 - Added new automation to mark errors with control measurements
#            - Added new automation to update nomenclature building date
#            - Fixed nom_status for the exisings devices

# Added new automation to mark errors with control measurements
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Контролни точки - показания', 0, NULL, 1, 'documents', NULL, 'action', '6', 'use_updated_model := 1\r\n\r\noptions_priority := 3,2,4,5,1\r\n\r\nnom_device_types := 19,20,21,22\r\nnom_device_type_water := 21\r\nnom_device_type_heater := 19,20\r\n\r\nnom_var_indication_date := indication_date\r\n\r\nvar_grouping_table := indication_grp\r\nvar_device := device_id\r\nvar_file_device_num := device_serial_num\r\nvar_old_readings := indication_old\r\nvar_new_readings := indication_new\r\nvar_date_readings := indication_date\r\nvar_deviation := indication_deviation\r\n\r\n# completed by DEV only\r\nprocess_function_option_1 := _differenceMoreThan20\r\nprocess_function_option_2 := _devicesNumberDifference\r\nprocess_function_option_3 := _deviceMissing\r\nprocess_function_option_4 := _missingIndications\r\nprocess_function_option_5 := _indicationsMismatch', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'add\', \'edit\'))', 'plugin := veolia\r\nmethod := processControlMeasurements', '', 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%processControlMeasurements%' AND `start_model_type`=6);

# Added new automation to update nomenclature building date
# Document type 5, substatus 17
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Обновяване на датата на сградите (визуално отчитане)', 0, NULL, 1, 'documents', NULL, 'before_action', '5', 'nom_types := 19, 20, 21', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $this->registry->get(\'request\')->get(\'substatus\') == \'closed_17\' && \'[prev_b_substatus]\' != \'17\'', 'plugin := veolia\r\nmethod := updateBuildingIndicationDate', 'cancel_action_on_fail := 1', 2, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%updateBuildingIndicationDate%' AND start_model_type=5);
# Document type 6, substatus 15
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Обновяване на датата на сградите (дистанционно отчитане)', 0, NULL, 1, 'documents', NULL, 'before_action', '6', 'nom_types := 19, 20, 21', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && $this->registry->get(\'request\')->get(\'substatus\') == \'closed_15\' && \'[prev_b_substatus]\' != \'15\'', 'plugin := veolia\r\nmethod := updateBuildingIndicationDate', 'cancel_action_on_fail := 1', 2, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%updateBuildingIndicationDate%' AND start_model_type=6);

# Fixed nom_status for the exisings devices
INSERT IGNORE INTO nom_cstm
SELECT n.id AS model_id, fm.id AS var_id, 1 AS num, '1' AS value,
       NOW() AS added, -1 AS added_by, NOW() AS modified, -1 AS modified_by, '' AS lang
FROM _fields_meta fm
JOIN nom n
 ON fm.name='nom_status' and fm.model='Nomenclature' and fm.model_type=n.type;

################################################################################
# 2023-07-11 - Add import of "Main" and "Individual" device readings documents.

# Added import
INSERT IGNORE INTO `imports` SET
  `id` = 27,
  `type` = 'veolia_devices',
  `settings` = '
# ИД за Apator Powogaz
nom_apator_id := 2063

# ИД за Apator Powogaz
nom_eurocom_id := 2065

# ИД за Siemens
nom_siemens_id := 2067

# ИД за Techen=m
nom_techem_id := 2068

# ИД за Apator Powogaz
nom_kamstrup_id := 2066

# ИД за Apator Powogaz
nom_elvaco_id := 2064

# ИД за Apator Powogaz
nom_elvaco_five_id := 2069
# ИД за Apator Powogaz
tester := 2069

# Настройки за памет
memory_limit := 2048M',
  `visible` = 1;
INSERT IGNORE INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
    (27, 'Показания за главни и индивидуални уреди', '', 'bg'),
    (27, 'Main and individual device readings', '', 'en');

################################################################################
# 2023-07-12 - Editing veolia_devices import due to extra code

# Removed extra forgotten settings
UPDATE `imports`
SET `settings` = REPLACE(`settings`, '# ИД за Apator Powogaz
tester := 2069', '')
WHERE `type` = 'veolia_devices'
  AND `settings` LIKE '%# ИД за Apator Powogaz
tester := 2069%';

################################################################################
# 2023-08-23 - Added setting to skip adding empty rows where a nom was not found

# Removed extra forgotten settings
UPDATE `imports`
SET `settings` = CONCAT(`settings`, '\r\n\r\n# Настройка за запис на празни редове\r\nskipNotFound := 1')
WHERE `type` = 'veolia_devices'
  AND `settings` NOT LIKE '%skipNotFound := 1%';

################################################################################
# 2023-10-04 - Added new automation (upcomingDeviceMaintanance) which will send notifications to selected users

# Added new automation (upcomingDeviceMaintanance) which will send notifications to selected users
SET @email_id=(SELECT IF(MAX(`id`)>1000, MAX(`id`), 1000)+1 FROM `emails`);
INSERT INTO `emails` (`id`, `model`, `model_type`, `name`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`)
  SELECT @email_id, 'Nomenclature', 0, 'notify_device_maintanence', 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0
    WHERE NOT EXISTS (SELECT id FROM emails WHERE `name`='notify_device_maintanence') ;
SET  @email_id := (SELECT id FROM emails WHERE `name`='notify_device_maintanence');

INSERT IGNORE INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated` ) VALUES
(@email_id , 'Списък с уреди с предстояща метрология/демонтаж', '<table border=\"0\" cellpadding=\"1\" cellspacing=\"1\" style=\"width:100%\">\r\n	<tbody>\r\n		<tr>\r\n			<td><span style=\"font-size:18px\"><span style=\"font-family:opalcyr\">Здравейте,</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td><span style=\"font-size:18px\"><span style=\"font-family:opalcyr\">Следните уреди подлежат на предстояща метрология/демонтаж:</span></span></td>\r\n		</tr>\r\n		<tr>\r\n			<td>[detailed_devices_list]</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>\r\n			<table border=\"0\" cellpadding=\"1\" cellspacing=\"1\">\r\n				<tbody>\r\n					<tr>\r\n						<td><span style=\"font-size:16px\"><span style=\"font-family:opalcyr\"><strong>НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на фирма Уникен ЕООД.</span></span></td>\r\n					</tr>\r\n					<tr>\r\n						<td><span style=\"font-size:16px\"><em><span style=\"font-family:opalcyr\">При възникнали проблеми можете да изпратите e-mail на нашият екип.</span></em></span></td>\r\n					</tr>\r\n				</tbody>\r\n			</table>\r\n			</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n\r\n<p>&nbsp;</p>\r\n', '', 'bg', NOW());

INSERT INTO  `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Известяване за предстояща метрология/демонтаж на уреди', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', CONCAT('start_month_date := 1\r\nstart_time := 00:30\r\nstart_before := 02:00\r\n\r\nmonths_in_advance := 2,4\r\n\r\nprimary_devices := 16,17,18\r\nsecondary_devices := 19,20,21\r\nskip_statuses := 3,4\r\ndevice_var_date := metrological_suitability_date\r\ndevice_var_status := nom_status\r\ndevice_var_station := to_station_id\r\n\r\nemail_id := ', @email_id, '\r\nemail_receiver_users := 4,14,15,17'), 'condition := 1', 'plugin := veolia\r\nmethod := upcomingDeviceMaintanance', NULL, 1, 0, 1
    WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%upcomingDeviceMaintanance%' AND automation_type='crontab');

######################################################################################
# 2023-11-01 - Added new report 'veolia_device_measurements' for Veolia

# Initial insertion of report
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
    (462, 'veolia_device_measurements', 'nom_apator_id := 2063\r\nnom_siemens_id := 2067' , 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
                                                                    (462, 'Информация и стойности за уреди', 'bg'),
                                                                    (454, 'Device measurements', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
                                                                                          ('reports', 'generate_report', '462', '1'),
                                                                                          ('reports', 'export', '462', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module`     = 'reports'
  AND `action`     IN ('generate_report', 'export')
  AND `model_type` = '462';

################################################################################
# 2023-11-14 - Update settings from the ground up, possibly will be updated further for the report

# Brand new settings for the report
UPDATE `reports`
SET `settings` = '# Apator nomenclature id\r\nnom_apator_id := 2063\r\n# Siemens nomenclature id\r\nnom_siemens_id := 2067\r\n\r\n# Heat distributor nomenclature type\r\nnom_heatDistributor_type := 19\r\n\r\n# Radiator type\r\nnom_radiator_type := 22\r\n\r\n# Type categories by design\r\ndevice_types_main := 16,17,18\r\ndevice_types_individual := 19,20,21,22'
WHERE `type` = 'veolia_device_measurements'
  AND `settings` NOT LIKE '# Apator nomenclature id%';

################################################################################
# 2023-11-15 - Added more settings to further reduce hard-coding and options for metrology years

# Defining sever nom types
UPDATE `reports`
SET `settings` = CONCAT(`settings`, '\r\n\r\n# Extra nomenclature type definitions\r\nnom_object_type := 11\r\nnom_lot_type := 1\r\nnom_building_type := 9\r\n\r\n# Year metrology options\r\nyear_options_start := 2020\r\nyear_options_end := 2035')
WHERE `type` = 'veolia_device_measurements'
  AND `settings` NOT LIKE '%# Extra nomenclature type definitions%';

########################################################################
# 2023-11-15 - Added pattern plugin for merging gt2 column values

# Added pattern plugin for Daily Construction Status Reports
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
 (105, 'Document', 21, 'gt2', 'mergeGT2Columns', 'merge_if_empty_into_article_name := free_text1', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (105, 'Сливане на колони в GT2', '', 'bg', NOW()),
  (105, 'Merge columns in GT2', '', 'en', NOW());

# Set the plugin, header and content to the pattern
UPDATE patterns SET plugin=105 WHERE id=3;

########################################################################
# 2023-11-20 - Added pattern plugin for preparing custom data for Veolia

# Added pattern plugin for preparing custom data for Veolia
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (106, 'Document', 15, 'veolia', 'prepareData', '', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (106, 'Приготвяне на данни, специфични за Веолия', '', 'bg', NOW()),
  (106, 'Prepare custom data for Veolia', '', 'en', NOW());

# Set the plugin, header and content to the pattern
UPDATE patterns SET plugin=106 WHERE id=9;

################################################################################
# 2023-11-23 - Added new automation (syncProtocolsMeasurements) which will update the data for the devices when they are activated/deactivated

# Added new automation (syncProtocolsMeasurements) which will update the data for the devices when they are activated/deactivated
INSERT INTO  `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизиране на показания от протоколи', 0, NULL, 1, 'documents', NULL, 'action', '0', 'use_updated_model := 1\r\n\r\ndocument_var_device_id := device_id\r\ndocument_var_measurements := indication_new\r\ndocument_var_status := protocolaction_type\r\ndocument_var_status_activate := 1\r\ndocument_var_status_deactivate := 2\r\n\r\nnom_var_status_activate := 3\r\nnom_var_status_deactivate := 1\r\n\r\nnom_var_status := nom_status\r\nnom_var_indication_doc_name := indication_doc\r\nnom_var_indication_doc_id := indication_doc_id\r\nnom_var_indication_date := indication_date\r\nnom_var_indication_indication_new := indication_new\r\nnom_var_indication_indication_old := indication_old\r\n\r\nnom_metrology_activate_indication := metrological_back_to_work_values\r\nnom_metrology_activate_date := metrological_back_to_work_date\r\nnom_metrology_activate_doc_name := metrological_back_to_work_doc\r\nnom_metrology_activate_doc_id := metrological_back_to_work_doc_id\r\n\r\nnom_metrology_deactivate_indication := metrological_off_work_values\r\nnom_metrology_deactivate_date := metrological_off_work_date\r\nnom_metrology_deactivate_doc_name := metrological_off_work_doc\r\nnom_metrology_deactivate_doc_id := metrological_off_work_doc_id', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'add\', \'edit\'))\r\ncondition := in_array(\'[b_type]\', array(\'10\', \'11\', \'12\', \'13\', \'14\', \'18\',\'24\'))', 'plugin := veolia\r\nmethod := syncProtocolsMeasurements', '', 1, 0, 1
    WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND automation_type='action');

######################################################################################
# 2023-12-07 - Updated settings for syncProtocolsMeasurements automation

# Updated settings for syncProtocolsMeasurements automation
UPDATE `automations` SET `settings`='use_updated_model := 1\r\n \r\ndocument_var_device_id := device_id\r\ndocument_var_measurements := indication_new\r\ndocument_var_status := protocolaction_type\r\ndocument_var_status_activate := 2\r\ndocument_var_status_deactivate := 1\r\n \r\nnom_var_status_activate := 1\r\nnom_var_status_deactivate := 3\r\n \r\nnom_var_status := nom_status\r\nnom_var_indication_doc_name := indication_doc\r\nnom_var_indication_doc_id := indication_doc_id\r\nnom_var_indication_date := indication_date\r\nnom_var_indication_indication_new := indication_new\r\nnom_var_indication_indication_old := indication_old\r\nnom_var_indication_date_old := indication_old_date\r\nnom_var_indication_doc_id_old := indication_old_doc_id\r\nnom_var_indication_doc_name_old := indication_old_doc\r\nnom_var_prescription_doc_id := prescriptions_issued_id\r\nnom_var_prescription_doc_name := prescriptions_issued\r\n\r\n# въвеждане 2\r\nnom_metrology_activate_indication := metrological_back_to_work_values\r\nnom_metrology_activate_date := metrological_back_to_work_date\r\nnom_metrology_activate_doc_name := metrological_back_to_work_doc\r\nnom_metrology_activate_doc_id := metrological_back_to_work_doc_id\r\n\r\n# извеждане 1\r\nnom_metrology_deactivate_indication := metrological_off_work_values\r\nnom_metrology_deactivate_date := metrological_off_work_date\r\nnom_metrology_deactivate_doc_name := metrological_off_work_doc\r\nnom_metrology_deactivate_doc_id := metrological_off_work_doc_id' WHERE `method` LIKE '%syncProtocolsMeasurements%' AND `settings` NOT LIKE '%nom_var_indication_date_old%';

######################################################################################
# 2023-12-11 - Added new setting in 'syncProtocolsMeasurements' automation for documents types which will write prescriptions in the related nomenclature

# Added new setting in 'syncProtocolsMeasurements' automation for documents types which will write prescriptions in the related nomenclature
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nnom_var_prescription_doc_id :=', '\r\nnom_var_prescription_doc_types := 18\r\nnom_var_prescription_doc_id :=') WHERE `method` LIKE '%syncProtocolsMeasurements%' AND `settings` NOT LIKE '%nom_var_prescription_doc_types%';

####################################################################################
# 2023-12-11 - Added REST settings for Reporter

# Added initial settings for rest in veolia
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'allowed_rest_user_agents', 'veolia-reporter'),
    ('rest', 'filter_vars_customers_2', 'all'),
    ('rest', 'filter_vars_documents_18', 'all'),
    ('rest', 'filter_vars_documents_23', 'all'),
    ('rest', 'filter_vars_documents_5', 'all'),
    ('rest', 'filter_vars_documents_9', 'all'),
    ('rest', 'filter_vars_nomenclatures_1', 'all'),
    ('rest', 'filter_vars_nomenclatures_10', 'all'),
    ('rest', 'filter_vars_nomenclatures_11', 'all'),
    ('rest', 'filter_vars_nomenclatures_15', 'all'),
    ('rest', 'filter_vars_nomenclatures_19', 'all'),
    ('rest', 'filter_vars_nomenclatures_20', 'all'),
    ('rest', 'filter_vars_nomenclatures_21', 'all'),
    ('rest', 'filter_vars_nomenclatures_9', 'all'),
    ('rest', 'filter_vars_users', 'id, username, firstname, lastname, employee, employee_name, rights, role')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2023-12-11 - Added new report 'veolia_device_history' for Veolia

# Initial insertion of report
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
    (464, 'veolia_device_history', '' , 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
                                                                    (464, 'История на показанията', 'bg'),
                                                                    (464, 'Device history', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
                                                                                          ('reports', 'generate_report', '464', '1'),
                                                                                          ('reports', 'export', '464', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module`     = 'reports'
  AND `action`     IN ('generate_report', 'export')
  AND `model_type` = '464';

######################################################################################
# 2023-12-14 - Added two new automations that will validate and add new devices

# Added two new automations that will validate and add new devices
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Валидация на уредите преди добавяне', 0, NULL, 1, 'documents', NULL, 'before_action', '0', 'device_code := device_psiro_code\r\ndevice_types := 16,17,18', 'condition := \'[action]\' == \'add\' && in_array(\'[type]\', array(\'10\',\'11\',\'12\',\'13\')) && $request->get(\'protocolaction_type\') == \'2\' && $request->get(\'device_add\') == \'1\'', 'plugin := veolia\r\nmethod := validateDevice', 'cancel_action_on_fail := 1', 1, 0, 1
    WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%validateDevice%' AND automation_type='before_action');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Добавяне на измервателни уреди', 0, NULL, 1, 'documents', NULL, 'action', '0', 'use_updated_model := 1\r\n \r\ndocument_device_type := device_type\r\ndocument_nom_code := device_psiro_code\r\ndocument_station := to_station_id\r\ndocument_station_name := to_station\r\ndocument_device_brand := brands_id\r\ndocument_device_brand_name := brands\r\ndocument_device_model_name := models\r\ndocument_device_model := models_id\r\ndocument_production_year := prod_year\r\ndocument_nominal_cost := nominal_cost\r\n\r\nnomenclature_status := nom_status\r\nnomenclature_station := to_station_id\r\nnomenclature_station_name := to_station\r\nnomenclature_device_brand := brands_id\r\nnomenclature_device_brand_name := brands\r\nnomenclature_device_model_name := models\r\nnomenclature_device_model := models_id\r\nnomenclature_production_year := prod_year\r\nnomenclature_nominal_cost := nominal_cost\r\nnomenclature_nom_status_id := 1', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'add\', \'edit\'))\r\ncondition := in_array(\'[b_type]\', array(\'10\', \'11\', \'12\', \'13\'))\r\ncondition := \'[a_protocolaction_type]\' == \'2\'\r\ncondition := \'[a_device_add]\' == \'1\'', 'plugin := veolia\r\nmethod := addDevice', '', 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND automation_type='action');

######################################################################################
# 2023-12-21 - Added settings for device id and name vars in addDevice automation

# Added settings for device id and name vars in addDevice automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnomenclature_status :=', '\r\ndocument_device_id := device_id\r\ndocument_device_name := device\r\n\r\nnomenclature_status :=') WHERE `method` LIKE '%addDevice%' AND `settings` NOT LIKE '%document_device_id%';

######################################################################################
# 2024-02-02 - Added new report 'veolia_objects_hierarchy'

# Added new report 'veolia_objects_hierarchy'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (466, 'veolia_objects_hierarchy', 'tree_collapsed := 1\r\n\r\nnom_type_station := 15\r\nnom_type_building := 9\r\nnom_type_entrance := 10\r\nnom_type_object := 11\r\nnom_type_heatrouter := 19\r\nnom_type_heatmeter_apartment := 20\r\nnom_type_watermeter := 21\r\nnom_type_radiator := 22\r\nnom_type_heatmeter := 16\r\nnom_type_additional_watermeter := 18\r\ncontact_types := 2,3,6\r\ndoc_type_signal := 3\r\n\r\nstation_var_building := building_id\r\nstation_var_entrance := entrance_id\r\nstation_var_quarter := quarter_id\r\nstation_var_street := street_id\r\nstation_var_status := status_as\r\nstation_var_services := sevices\r\nstation_var_appliances := appliances\r\nstation_var_distribution_method := distribution_method\r\nstation_var_fdr := fdr_id\r\nstation_var_fts := fts_responsible_id\r\nstation_var_tpm := tpm_responsible_id\r\nstation_var_file := files_id\r\nstation_var_file_desc := files_description\r\n\r\nbuilding_var_quarter := quarter_id\r\nbuilding_var_street := street_id\r\nbuilding_var_indication_date := indication_date\r\nbuilding_var_indication_doc := indication_doc_id\r\nbuilding_var_visual_employee := visual_report_employee_id\r\nbuilding_var_remote_employee := remote_report_employee_id\r\nbuilding_var_tech_employee := tech_employee_id\r\n\r\nentrance_var_building := building_id\r\nentrance_var_address := full_address\r\n\r\nobject_var_building := building_id\r\nobject_var_entrance := entrance_id\r\nobject_var_address := full_address\r\nobject_var_titular := titular_id\r\nobject_var_lot := lot_id\r\nobject_var_service_type := service_type\r\nobject_var_living := people_living_count\r\nobject_var_owner := owner_id\r\nobject_var_tenant := tenant_id\r\nobject_var_tenant_from := tenant_from\r\nobject_var_tenant_to := tenant_to\r\nobject_var_premises := inv_name\r\n\r\ndevices_var_object := to_object_id\r\n\r\nparent_noms_to_object := to_station_id\r\nparent_noms_status := nom_status\r\n\r\ndevice_var_serial_num := serial_num_remote_module\r\ndevice_var_brand := brands_id\r\ndevice_var_model := models_id\r\ndevice_var_year := prod_year\r\ndevice_var_radiator := radiator\r\ndevice_var_indication_old := indication_old\r\ndevice_var_indication_new := indication_new\r\ndevice_var_indication_old_date := indication_old_date\r\ndevice_var_indication_new_date := indication_new_date\r\ndevice_var_indication_old_doc := indication_old_doc_id\r\ndevice_var_indication_new_doc := indication_new_doc_id\r\ndevice_var_status := nom_status\r\ndevice_var_report_type := appliances_report_type\r\ndevice_var_room := room_id\r\ndevice_var_prescriptions := prescriptions_issued_id\r\ndevice_var_metrology_date := metrological_suitability_date\r\ndevice_var_file := files_id\r\ndevice_var_file_desc := files_description', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (466, 'Йерархия на обекти', NULL, NULL, 'bg'),
  (466, 'Object\'s hierarchy', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '466', '1'),
  ('reports', '', 'export', '466', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports' AND `action` IN ('generate_report') AND `model_type` = '466';

######################################################################################
# 2024-02-06 - Updated settings for report 'veolia_objects_hierarchy'

# Updated settings for report 'veolia_objects_hierarchy'
UPDATE `reports` SET `settings`='tree_collapsed := 1\r\n\r\nnom_type_station := 15\r\nnom_type_building := 9\r\nnom_type_entrance := 10\r\nnom_type_object := 11\r\nnom_type_heatrouter := 19\r\nnom_type_heatmeter_apartment := 20\r\nnom_type_watermeter := 21\r\nnom_type_main_watermeter := 17\r\nnom_type_radiator := 22\r\nnom_type_heatmeter := 16\r\nnom_type_additional_watermeter := 18\r\ncontact_types := 2,3,6\r\ndoc_type_signal := 3\r\n\r\nstation_var_building := building_id\r\nstation_var_entrance := entrance_id\r\nstation_var_quarter := quarter_id\r\nstation_var_street := street_id\r\nstation_var_status := status_as\r\nstation_var_services := sevices\r\nstation_var_appliances := appliances\r\nstation_var_distribution_method := distribution_method\r\nstation_var_fdr := fdr_id\r\nstation_var_fts := fts_responsible_id\r\nstation_var_tpm := tpm_responsible_id\r\nstation_var_file := files_id\r\nstation_var_file_desc := files_description\r\nstation_var_date_last_indication := indication_date\r\n\r\nbuilding_var_quarter := quarter_id\r\nbuilding_var_street := street_id\r\nbuilding_var_indication_date := indication_date\r\nbuilding_var_indication_doc := indication_doc_id\r\nbuilding_var_visual_employee := visual_report_employee_id\r\nbuilding_var_remote_employee := remote_report_employee_id\r\nbuilding_var_tech_employee := tech_employee_id\r\n\r\nentrance_var_building := building_id\r\nentrance_var_address := full_address\r\n\r\nobject_var_building := building_id\r\nobject_var_entrance := entrance_id\r\nobject_var_address := full_address\r\nobject_var_titular := titular_id\r\nobject_var_lot := lot_id\r\nobject_var_service_type := service_type\r\nobject_var_living := people_living_count\r\nobject_var_owner := owner_id\r\nobject_var_tenant := tenant_id\r\nobject_var_tenant_from := tenant_from\r\nobject_var_tenant_to := tenant_to\r\nobject_var_premises := inv_name\r\n\r\ndevices_var_object := to_object_id\r\n\r\nparent_noms_to_object := to_station_id\r\nparent_noms_status := nom_status\r\n\r\ndevice_var_serial_num := serial_num_remote_module\r\ndevice_var_brand := brands_id\r\ndevice_var_model := models_id\r\ndevice_var_year := prod_year\r\ndevice_var_radiator := radiator\r\ndevice_var_indication_old := indication_old\r\ndevice_var_indication_new := indication_new\r\ndevice_var_indication_old_date := indication_old_date\r\ndevice_var_indication_new_date := indication_date\r\ndevice_var_indication_old_doc := indication_old_doc_id\r\ndevice_var_indication_new_doc := indication_doc_id\r\ndevice_var_status := nom_status\r\ndevice_var_report_type := appliances_report_type\r\ndevice_var_room := room_id\r\ndevice_var_prescriptions := prescriptions_issued_id\r\ndevice_var_metrology_date := metrological_suitability_date\r\ndevice_var_file := files_id\r\ndevice_var_file_desc := files_description' WHERE `type`="veolia_objects_hierarchy";

######################################################################################
# 2024-02-16 - Added new automations that will write the objects in the data of the related customers

# Added new automations that will write the objects in the data of the related customers
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Актуализиране на списък с обекти в досие на клиенти и лица за контакт', 0, NULL, 1, 'nomenclatures', NULL, 'action', '11', 'use_updated_model := 1\r\n\r\nnom_owner_var := titular_id\r\nnom_coowner_var := owner_id\r\nnom_tenant_var := tenant_id\r\n\r\ncustomers_included := 2,6\r\ncustomer_object_id_var := object_id\r\ncustomer_object_name_var := object_name\r\ncustomer_object_role_var := related_role\r\ncustomer_object_date_var := related_added\r\n\r\ncustomer_role_owner := 1\r\ncustomer_role_coowner := 2\r\ncustomer_role_tenant := 3', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'add\', \'edit\'))', 'plugin := veolia\r\nmethod := syncObjectsAndCustomers', '', 1, 0, 1
    WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncObjectsAndCustomers%' AND automation_type='action');

######################################################################################
# 2024-02-27 - Updated settings for rest

# Added more variables to user filter
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_users', 'id, username, firstname, lastname, employee, employee_name, rights, role, default_department, default_group')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2024-03-25 - Updated settings for report 'veolia_device_measurements'

# Updated settings for report 'veolia_device_measurements'
UPDATE `reports` SET `settings`='# Apator nomenclature id\r\nnom_apator_id := 2063\r\n# Siemens nomenclature id\r\nnom_siemens_id := 2067\r\n\r\n# Heat distributor nomenclature type\r\nnom_heatDistributor_type := 19\r\n\r\n# Type categories by design\r\ndevice_types_main := 16,17,18\r\ndevice_types_individual := 19,20,21,22\r\ndevice_type_main_heatmeter := 16\r\ndevice_type_main_watermeter_bgv := 17\r\ndevice_type_main_watermeter_additional := 18\r\ndevice_type_heatspreader := 19\r\ndevice_type_radiator := 22\r\n\r\n# Extra nomenclature type definitions\r\nnom_object_type := 11\r\nnom_lot_type := 1\r\nnom_building_type := 9\r\nnom_type_station := 15\r\n\r\n# Year metrology options\r\nyear_options_start := 2020\r\nyear_options_end := 2035\r\n\r\n# Some documents types which are used\r\ndoc_type_expense := 25\r\ndoc_type_protocols_exploitation := 10,12\r\ndoc_type_measurements := 8\r\n\r\n# Definition of winter and summer months\r\nsummer_months := 4,5,6,7,8\r\nwinter_months := 9,10,11,12,1,2,3' WHERE `type`="veolia_device_measurements";

######################################################################################
# 2024-03-26 - Added settings for rest, added new field

# Added more variables to user filter
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_customers_4', 'all'),
    ('rest', 'filter_vars_customers_6', 'all'),
    ('rest', 'filter_vars_customers_7', 'all'),
    ('rest', 'filter_vars_documents_4', 'all'),
    ('rest', 'filter_vars_documents_10', 'all'),
    ('rest', 'filter_vars_documents_11', 'all'),
    ('rest', 'filter_vars_documents_12', 'all'),
    ('rest', 'filter_vars_documents_13', 'all'),
    ('rest', 'filter_vars_documents_14', 'all'),
    ('rest', 'filter_vars_documents_15', 'all'),
    ('rest', 'filter_vars_documents_16', 'all'),
    ('rest', 'filter_vars_documents_17', 'all'),
    ('rest', 'filter_vars_documents_18', 'all'),
    ('rest', 'filter_vars_documents_19', 'all'),
    ('rest', 'filter_vars_documents_21', 'all'),
    ('rest', 'filter_vars_documents_22', 'all'),
    ('rest', 'filter_vars_documents_23', 'all'),
    ('rest', 'filter_vars_documents_24', 'all'),
    ('rest', 'filter_vars_nomenclatures_23', 'all')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

# Added automation plugin for creation of protocols for correction of an employment
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `source`, `validate`,
                            `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`,
                            `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
    (NULL, 'Document', 4, 'customer_type', 'text', '', 0, 1, '', 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3054, 30, '', NULL, '');

# Added label for the new var although hidden.
INSERT INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Тип на контрагент', 'bg')
ON DUPLICATE KEY UPDATE
    `content` = VALUES(`content`);

######################################################################################
# 2024-07-22 - Added new setting for syncProtocolsMeasurements automation to define if the status of the realted nomenclature will be changed

# Added new setting for syncProtocolsMeasurements automation to define if the status of the realted nomenclature will be changed
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nupdate_nom_status := 1') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND `start_model_type`='0' AND `settings` NOT LIKE '%update_nom_status%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nupdate_nom_status := 0') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND `start_model_type`='24' AND `settings` NOT LIKE '%update_nom_status%';

######################################################################################
# 2024-07-30 - Added new settings for syncProtocolsMeasurements automation to address the specific conditions for changing status of heat spreaders

# Added new settings for syncProtocolsMeasurements automation to address the specific conditions for changing status of heat spreaders
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n \r\n', '\r\n\r\n') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_var_status :=', '\r\nnom_var_status_heatspreader_deactivate := 4\r\n\r\nnom_type_heatspreader := 19\r\n\r\nnom_var_status :=') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND `settings` NOT LIKE '%nom_var_status_heatspreader_deactivate%';


######################################################################################
# 2024-09-04 - Added multiple automations regarding PSIRO synchronisation.
# Each is recognised by the "import_method" in the settings. The main automation method is the same.

# Automation to synchronise the company clients from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO - Фирми', 0, NULL, 1, 'customers', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getCompanyCustomerSettings\r\n\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\n\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 1, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getCompanyCustomerSettings%');

# Automation to synchronise the private clients from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Абонати', 0, NULL, 1, 'customers', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getAbonatCustomerSettings\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 2, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getAbonatCustomerSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Сгради', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getBuildingsSettings\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 3, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getBuildingsSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Входове', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getEntranceSettings\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 4, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getEntranceSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Станции', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getStationSettings\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 5, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getStationSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Партиди', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getLotSettings\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 6, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getLotSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Имоти', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getObektSettings\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 7, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getObektSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Стаи', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getRoomsSettings\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 8, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getRoomsSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Главни Уреди', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getMainDevicesSettings\r\n# Limit parameter\r\n# limit := 1000\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 9, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getMainDevicesSettings%');

# Automation to synchronise the companies from PSIRO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Синхронизация с PSIRO  - Индивидуални Уреди', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'psirodb_type := mssqlnative\r\npsirodb_host := ************\r\npsirodb_port := 1433\r\npsirodb_user := SSDO\r\npsirodb_pass := W74YHq5Vf+Q=\r\npsirodb_name := 1ADB\r\nimport_method := getClientDevicesSettings\r\n# Limit parameter\r\n# limit := 2000\r\n# Add this option to set the modified after date, by default it is 1 for initial synchronisation, empty or 0 to use last automation run.\r\nmodified_after := 1\r\nsend_to_email := <EMAIL>\r\nstart_before := 08:00\r\nstart_time := 07:00', 'condition := 1', 'plugin := veolia\r\nmethod := syncPsiroDb', '', 10, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'customers' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncPsiroDb%' AND settings LIKE '%getClientDevicesSettings%');

######################################################################################
# 2024-09-11 - Added new settings for 'veolia_objects_hierarchy' for lot nomenclature type and for object files
#            - Added new settings for 'veolia_objects_hierarchy' for file type for station files

# Added new settings for 'veolia_objects_hierarchy' for lot nomenclature type and for object files
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ncontact_types :=', '\r\nnom_type_lot := 1\r\ncontact_types :=') WHERE `type`='veolia_objects_hierarchy' AND `settings` NOT LIKE '%nom_type_lot%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndevices_var_object :=', '\r\nobject_var_file := files_id\r\nobject_var_file_desc := files_description\r\nobject_var_file_type := file_type_id\r\n\r\ndevices_var_object :=') WHERE `type`='veolia_objects_hierarchy' AND `settings` NOT LIKE '%object_var_file%';

# Added new settings for 'veolia_objects_hierarchy' for file type for station files
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nstation_var_date_last_indication :=', '\r\nstation_var_file_type := file_type_id\r\nstation_var_date_last_indication :=') WHERE `type`='veolia_objects_hierarchy' AND `settings` NOT LIKE '%station_var_file_type%';

######################################################################################
# 2024-09-12 - Fixed var name for radiator in 'veolia_objects_hierarchy' report settings

# Fixed var name for radiator in 'veolia_objects_hierarchy' report settings
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndevice_var_radiator := radiator\r\n', '\r\ndevice_var_radiator := radiator_id\r\n') WHERE `type`='veolia_objects_hierarchy' AND `settings` NOT LIKE '%device_var_radiator := radiator_id%';

######################################################################################
# 2024-10-02 - Added new automation to sync the files from the server to the nzoom files
#            - Added new bullet for syncing the server files in the VEOLIA installation

# Added new automation to sync the files from the server to the nzoom files
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Синхронизация файлов сървър -> nZoom', 0, NULL, 1, 'nomenclatures', NULL, 'crontab', '0', 'start_time := 02:00\r\nstart_before := 04:00\r\nsend_to_email := <EMAIL>\r\n\r\npath_archive := D:/xampp/htdocs/bgs/nzoom/1.7.0/resources/files/remote/архив/\r\npath_archive_data := D:/xampp/htdocs/bgs/nzoom/1.7.0/resources/files/remote/Архив-данни за АС/\r\npath_invoices := D:/xampp/htdocs/bgs/nzoom/1.7.0/resources/files/remote/Fakturi/\r\nuse_console_search := 0\r\nuse_time_matching := 0\r\n\r\nsubfolder_partners := Партньори\r\nsubfolder_other_clients := Други Клиенти\r\nsubfolder_institutions := Институции\r\n\r\ncustomer_other_client := 826\r\n\r\ncustomer_partners_ids := 4,5\r\ncustomer_institution_ids := 4\r\n\r\ninvoice_nomenclature_type := 11\r\ninvoice_nomenclature_lot := lot_id\r\n\r\nnomenclature_file_type := 14\r\nnomenclature_stations_type := 15\r\nnomenclature_object_type := 11\r\n\r\nnomenclature_var_file := files_id\r\nnomenclature_var_file_type_id := file_type_id\r\nnomenclature_var_file_type_name := file_type_name\r\nnomenclature_var_file_desc := files_description\r\n\r\nnomenclature_station_var_psiro := psiro_code\r\nnomenclature_station_var_entrance := entrance_id\r\nnomenclature_object_var_entrance := entrance_id', 'condition := 1', 'plugin := veolia\r\nmethod := syncFiles', '', 50, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'nomenclatures' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncFiles%' AND method LIKE '%veolia%');

# Added new bullet for syncing the server files in the VEOLIA installation
INSERT IGNORE INTO `bullets` (`bullet`, `description`, `revision`, `position`, `active`, `modified`, `fired`) VALUES
  ('syncServerFiles', 'Sync server files for the VEOLIA installation', 20115, 1, 1, NOW(), '0000-00-00 00:00:00');

######################################################################################
# 2024-10-03 - Updated settings for syncFiles automation

# Updated settings for syncFiles automation
UPDATE `automations` SET `settings`='start_time := 02:00\r\nstart_before := 20:00\r\nsend_to_email := <EMAIL>\r\n\r\nuse_console_search := 0\r\nuse_time_matching := 0\r\n\r\nsubfolder_partners := Партньори\r\nsubfolder_other_clients := Други Клиенти\r\nsubfolder_institutions := Институции\r\n\r\ncustomer_other_client := 826\r\n\r\ncustomer_partners_ids := 4,5\r\ncustomer_institution_ids := 4\r\n\r\ninvoice_nomenclature_type := 11\r\ninvoice_nomenclature_lot := lot_id\r\n\r\nnomenclature_file_type := 14\r\nnomenclature_stations_type := 15\r\nnomenclature_object_type := 11\r\n\r\nnomenclature_var_file := files_id\r\nnomenclature_var_file_type_id := file_type_id\r\nnomenclature_var_file_type_name := file_type_name\r\nnomenclature_var_file_desc := files_description\r\n\r\nnomenclature_station_var_psiro := psiro_code\r\nnomenclature_station_var_entrance := entrance_id\r\nnomenclature_object_var_entrance := entrance_id' WHERE module = 'nomenclatures' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncFiles%' AND method LIKE '%veolia%' AND `settings` LIKE '%path_archive_data%';
UPDATE `automations` SET `settings`='start_time := 02:00\r\nstart_before := 04:00\r\nsend_to_email := <EMAIL>\r\n\r\nuse_console_search := 0\r\nuse_time_matching := 0\r\n\r\nsubfolder_partners := Партньори\r\nsubfolder_other_clients := Други Клиенти\r\nsubfolder_institutions := Институции\r\n\r\ncustomer_other_client := 826\r\n\r\ncustomer_partners_ids := 4,5\r\ncustomer_institution_ids := 4\r\n\r\ninvoice_nomenclature_type := 11\r\ninvoice_nomenclature_lot := lot_id\r\n\r\nnomenclature_file_type := 14\r\nnomenclature_stations_type := 15\r\nnomenclature_object_type := 11\r\n\r\nnomenclature_var_file := files_id\r\nnomenclature_var_file_type_id := file_type_id\r\nnomenclature_var_file_type_name := file_type_name\r\nnomenclature_var_file_desc := files_description\r\n\r\nnomenclature_station_var_psiro := psiro_code\r\nnomenclature_station_var_entrance := entrance_id\r\nnomenclature_object_var_entrance := entrance_id' WHERE module = 'nomenclatures' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncFiles%' AND method LIKE '%veolia%' AND `settings` LIKE '%20:00%';

######################################################################################
# 2024-10-18 - The labels for 'veolia_objects_hierarchy' report are moved in the DB

# The labels for 'veolia_objects_hierarchy' report are moved in the DB
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_station', 'Абонатна станция'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_building', 'Сграда'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_entrance', 'Вход'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_object', 'Обект'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_contact', 'Контакт'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_address', 'Адрес'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_last_measurement', 'Дата на последно показание'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_status', 'Статус'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_building_services', 'Услуги в сградата'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_devices', 'Уреди'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_distribution_type', 'Начин на разпределение'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_fdr', 'ФДР'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_fts', 'Отговорник ФТС'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_tpm', 'Отговорник ТПМ'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_heatmeter', 'Топломер'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_watermeter', 'Водомер за БГВ'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_watermeter_add', 'Водомер за допълване'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_files', 'Файлове'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_responsible_visual', 'Отговорен отчетник (визуален отчет)'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_responsible_distant', 'Отговорен отчетник (дистанционен отчет)'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_technician', 'Отговорен техник'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_owners', 'Собственик'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_lot', 'Партида'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_type_service', 'Вид услуга'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_residents', 'Брой обитатели'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_tenants', 'Наематели'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_premises', 'Помещения'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_serial_num', 'Сериен номер'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_serial_num_dist', 'Сериен номер на дистанционен модул'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_measurement_type', 'Начин на отчет'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_premise', 'Помещение'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_brand_model', 'Марка, модел'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_production_year', 'Година на производство'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_radiator', 'Радиатор'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_prescriptions', 'Издадени предписания'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_metrology', 'Подлежи на метрология до'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_old_measurement', 'Старо показание'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_lbl_new_measurement', 'Ново показание'),
  ('reports', 'veolia_objects_hierarchy', 'bg', 'reports_btn_add_signal', 'Добавяне на сигнал');
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_station', 'Substation'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_building', 'Building'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_entrance', 'Entrance'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_object', 'Object'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_contact', 'Contact'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_address', 'Address'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_last_measurement', 'Date of last reading'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_status', 'Status'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_building_services', 'Services in the building'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_devices', 'Appliances'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_distribution_type', 'Method of distribution'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_fdr', 'FDR'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_fts', 'FTS manager'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_tpm', 'Responsible TPM'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_heatmeter', 'Thermometer'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_watermeter', 'Water meter for DHW'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_watermeter_add', 'Water meter for topping up'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_files', 'Files'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_responsible_visual', 'Responsible Reporter (Visual Report)'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_responsible_distant', 'Responsible Reporter (Remote Report)'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_technician', 'Responsible technician'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_owners', 'Owner'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_lot', 'Lot'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_type_service', 'Type of service'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_residents', 'Number of inhabitants'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_tenants', 'Tenants'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_premises', 'Premise'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_serial_num', 'Serial number'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_serial_num_dist', 'Remote module serial number'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_measurement_type', 'Method of report'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_premise', 'Premises'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_brand_model', 'Brand, model'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_production_year', 'Year of manufacture'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_radiator', 'Radiator'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_prescriptions', 'Prescriptions issued'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_metrology', 'Subject to metrology until'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_old_measurement', 'Old testimony'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_lbl_new_measurement', 'New reading'),
  ('reports', 'veolia_objects_hierarchy', 'en', 'reports_btn_add_signal', 'Add alert');

######################################################################################
# 2024-10-23 - Added additional settings for vars to be transferred to the newly added nomenclatures by addDevice automation

# Added additional settings for vars to be transferred to the newly added nomenclatures by addDevice automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnomenclature_status :=', '\r\ndocument_object_name := to_object\r\ndocument_object_id := to_object_id\r\ndocument_room_name := room\r\ndocument_room_id := room_id\r\n\r\nnomenclature_status :=') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `settings` NOT LIKE '%document_object_id%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_object_name := to_object\r\nnomenclature_object_id := to_object_id\r\nnomenclature_room_name := room\r\nnomenclature_room_id := room_id') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `settings` NOT LIKE '%nomenclature_object_id%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnomenclature_status :=', '\r\ndocument_appliances_report_type := appliances_report_type\r\n\r\nnomenclature_status :=') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `settings` NOT LIKE '%document_appliances_report_type%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_appliances_report_type := appliances_report_type') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `settings` NOT LIKE '%nomenclature_appliances_report_type%';

######################################################################################
# 2024-11-07 - Set default setting use_console_search equals to 1 in the syncFiles automation

# Set default setting use_console_search equals to 1 in the syncFiles automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'use_console_search := 0', 'use_console_search := 1') WHERE module = 'nomenclatures' AND start_model_type = 0 AND automation_type = 'crontab' AND method LIKE '%syncFiles%' AND method LIKE '%veolia%' AND `settings` LIKE '%use_console_search := 0%';

################################################################################
# 2023-11-15 - Added more settings to further reduce hard-coding and options for metrology years

# Defining sever nom types
UPDATE `reports`
SET `settings` = CONCAT(`settings`, '\r\n\r\n# Define whether to use nZoom ID or psiro_code in individual devices export\r\nuseNzoomIdForIndExport := 0')
WHERE `type` = 'veolia_device_measurements'
  AND `settings` NOT LIKE '%# Define whether to use nZoom ID%';

################################################################################
# 2024-12-17 - New setting for main devices export

# Added new  setting to chose whether to use nZoom ID or psiro_code in main devices export.
UPDATE `reports`
SET `settings` = CONCAT(`settings`, '\r\n\r\n# Define whether to use nZoom ID or psiro_code in main devices export\r\nuseNzoomIdForMainExport := 0')
WHERE `type` = 'veolia_device_measurements'
  AND `settings` NOT LIKE '%useNzoomIdForMainExport%';

######################################################################################
# 2025-01-07 - Added new setting for syncProtocolsMeasurements automation to define if the status of the related nomenclature will be changed
#            - Added new settings for addDevice automation with names for entrance vars

# Added new setting for syncProtocolsMeasurements automation to define if the status of the related nomenclature will be changed
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nupdate_nom_types_active_status :=') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND `start_model_type`='0' AND `settings` NOT LIKE '%update_nom_types_active_status%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nupdate_nom_types_active_status := 10,11,12,13') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND `start_model_type`='24' AND `settings` NOT LIKE '%update_nom_types_active_status%';

# Added new settings for addDevice automation with names for entrance vars
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnomenclature_status := nom_status', '\r\ndocument_entrance := entrance\r\n\r\nnomenclature_status := nom_status') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `start_model_type`='0' AND `settings` NOT LIKE '%document_entrance%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_entrance := entrance') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `start_model_type`='0' AND `settings` NOT LIKE '%nomenclature_entrance%';

######################################################################################
# 2025-01-09 - Added new for hollander vars for measurements documents and devices in 'addDevice' automation
#            - Fix settings for syncProtocolsMeasurements automation

# Added new for hollander vars for measurements documents and devices in 'addDevice' automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnomenclature_status :=', '\r\ndocument_hollander := hollander\r\n\r\nnomenclature_status :=') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `start_model_type`='0' AND `settings` NOT LIKE '%document_hollander%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_hollander := hollander_id\r\nnomenclature_hollander_name := hollander_name') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `start_model_type`='0' AND `settings` NOT LIKE '%nomenclature_hollander_name%';

# Fix settings for syncProtocolsMeasurements automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nupdate_nom_types_active_status :=', '\r\nupdate_nom_types_active_status := 10,11,12,13') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND `start_model_type`='0' AND `settings` LIKE '%\r\nupdate_nom_types_active_status :=';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nupdate_nom_types_active_status := 10,11,12,13', '\r\nupdate_nom_types_active_status :=') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%syncProtocolsMeasurements%' AND `start_model_type`='24' AND `settings` LIKE '%\r\nupdate_nom_types_active_status := 10,11,12,13';

######################################################################################
# 2025-03-19 - Brand new automation "addMissingMainDevicesToImport

# Adding the automation
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Добавяне на липсващи абонатни станции и уредите им след импорт', 0, NULL, 1, 'documents', NULL, 'action', '8', '\r\n', 'condition := $this->registry[\'request\']->get(\'import_type\') == \'veolia_devices\'', 'plugin := veolia\r\nmethod := addMissingMainDevicesToImport', NULL, 1, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 8 AND automation_type = 'action' AND method LIKE '%addMissingMainDevicesToImport%');

######################################################################################
# 2025-03-24 - Brand new automation "addRadiators"

# Adding the automation
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Добавяне на радиатори от опис на АОИ', 0, NULL, 1, 'documents', NULL, 'crontab', '15', '\r\nperiod := 2 hours', 'condition := 1', 'plugin := veolia\r\nmethod := addRadiatorsFromReport', NULL, 1, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 15 AND automation_type = 'crontab' AND method LIKE '%addRadiatorsFromReport%');

######################################################################################
# 2025-05-22 - Added new settings for seal_num vars for measurements documents and devices in 'addDevice' automation

# Added new settings for seal_num vars for measurements documents and devices in 'addDevice' automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nnomenclature_status :=', '\r\ndocument_seal_num := seal_num\r\n\r\nnomenclature_status :=') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `start_model_type`='0' AND `settings` NOT LIKE '%document_seal_num%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_seal_num := seal_num') WHERE `method` LIKE '%veolia%' AND `method` LIKE '%addDevice%' AND `start_model_type`='0' AND `settings` NOT LIKE '%nomenclature_seal_num%';

######################################################################################
# 2025-06-04 - Brand new automation "addRoomsToObject"

# Adding the automation
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Добавяне на помещения към обект от АОИ', 75, NULL, 1, 'documents', NULL, 'crontab', '15', '\r\nperiod := 2 hours', 'condition := 1', 'plugin := veolia\r\nmethod := addRoomsToObject', NULL, 1, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 15 AND automation_type = 'crontab' AND method LIKE '%addRoomsToObject%');
