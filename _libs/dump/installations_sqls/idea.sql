################################################################################
### SQL nZoom Specific Updates Идея Билдингс ЕООД (https://idea.n-zoom.com/) ###
################################################################################

######################################################################################
# 2024-04-10 - Added new report 'idea_objects_hierarchy'

# Added new report 'idea_objects_hierarchy'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (471, 'idea_objects_hierarchy', 'tags_occupancy := 2,3,4,5\r\ntags_include_empty := 2\r\n\r\nnom_type_buildings := 5\r\nnom_type_properties := 9,10,11,12\r\n\r\nfilter_transferred_land_options_source := warehouse_yard\r\n\r\n# Adding new id here requires:\r\n# 1. adding noms_[new_id]_parent in the settings below\r\n# 2. (optional) adding new method (by developer) in the reports controller for the new type\r\nnoms_tree_included := 5,9,10,11,12\r\n\r\nnoms_5_parent := \r\nnoms_9_parent := building_id\r\nnoms_10_parent := building_id\r\nnoms_11_parent := building_id\r\nnoms_12_parent := building_id\r\n\r\nfilter_fields_floor := floor\r\nfilter_fields_object_value := object_value\r\nfilter_fields_transferred_land := transferred_land', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (471, 'Йерархия на обекти', NULL, NULL, 'bg'),
  (471, 'Object\'s hierarchy', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '471', '1'),
  ('reports', '', 'export', '471', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports' AND `action` IN ('generate_report') AND `model_type` = '471';

######################################################################################
# 2024-04-18 - Added new automation to update the relation between buildings and properties inside them

# Added new automation to update the relation between buildings and properties inside them
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
 SELECT 'Свързване на номенклатури', 0, NULL, 1, 'nomenclatures', NULL, 'action', '9', 'building_var := building_id\r\nobjects_included_tags := 2,3,4,5\r\n\r\nbuilding_var_object := object_name\r\nbuilding_var_object_id := object_id\r\nbuilding_var_object_status := obj_stat', 'condition := in_array(\'[action]\', array(\'add\', \'edit\', \'activate\', \'deactivate\'))', 'plugin := idea\r\nmethod := updateRelatedNomenclatures', NULL, 1, 0, 1
  WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateRelatedNomenclatures%' AND `method` LIKE '%idea%' AND `start_model_type`='9');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
 SELECT 'Свързване на номенклатури', 0, NULL, 1, 'nomenclatures', NULL, 'action', '10', 'building_var := building_id\r\nobjects_included_tags := 2,3,4,5\r\n\r\nbuilding_var_object := object_name\r\nbuilding_var_object_id := object_id\r\nbuilding_var_object_status := obj_stat', 'condition := in_array(\'[action]\', array(\'add\', \'edit\', \'activate\', \'deactivate\'))', 'plugin := idea\r\nmethod := updateRelatedNomenclatures', NULL, 1, 0, 1
  WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateRelatedNomenclatures%' AND `method` LIKE '%idea%' AND `start_model_type`='10');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
 SELECT 'Свързване на номенклатури', 0, NULL, 1, 'nomenclatures', NULL, 'action', '11', 'building_var := building_id\r\nobjects_included_tags := 2,3,4,5\r\n\r\nbuilding_var_object := object_name\r\nbuilding_var_object_id := object_id\r\nbuilding_var_object_status := obj_stat', 'condition := in_array(\'[action]\', array(\'add\', \'edit\', \'activate\', \'deactivate\'))', 'plugin := idea\r\nmethod := updateRelatedNomenclatures', NULL, 1, 0, 1
  WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateRelatedNomenclatures%' AND `method` LIKE '%idea%' AND `start_model_type`='11');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
 SELECT 'Свързване на номенклатури', 0, NULL, 1, 'nomenclatures', NULL, 'action', '12', 'building_var := building_id\r\nobjects_included_tags := 2,3,4,5\r\n\r\nbuilding_var_object := object_name\r\nbuilding_var_object_id := object_id\r\nbuilding_var_object_status := obj_stat', 'condition := in_array(\'[action]\', array(\'add\', \'edit\', \'activate\', \'deactivate\'))', 'plugin := idea\r\nmethod := updateRelatedNomenclatures', NULL, 1, 0, 1
  WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateRelatedNomenclatures%' AND `method` LIKE '%idea%' AND `start_model_type`='12');

######################################################################################
# 2024-06-28 - Added new automation to auto distribute PKO payments
#            - Added new automation to auto distribute BP payments
#            - Added new automation to auto distribute payments made from an incomes reason

# Added new automation to auto distribute PKO payments
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'payments', 'action', 'PKO', 'company_methodology := idea\r\n\r\nschedule_type_id := 5\r\nschedule_status_active :=\r\nschedule_substatus_proceeding :=\r\n\r\nschedule_var_credit_currency := offer_currency\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_type := bank_cash\r\nschedule_var_distribute_date := date_payment_doc\r\nschedule_var_payment_date := true_payment_date\r\nschedule_var_principal := principal\r\n\r\npayment_tag_not_distributed_payment :=\r\npayment_tag_skip_auto_distribution :=\r\n\r\nincomes_reason_type_id := 101\r\nincomes_reason_check_types :=\r\n\r\nemail_payment_not_distributed_email_id =\r\nemail_payment_not_distributed_receive_users :=', 'condition := \'[b_status]\' == \'finished\' && \'[b_status]\' != \'[prev_b_status]\'', 'plugin := credits\r\nmethod := autoDistributePayment', NULL, 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%credits%' AND `method` LIKE '%autoDistributePayment%' AND start_model_type='PKO' AND `controller`='payments');

# Added new automation to auto distribute BP payments
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'payments', 'action', 'BP', 'company_methodology := idea\r\n\r\nschedule_type_id := 5\r\nschedule_status_active :=\r\nschedule_substatus_proceeding :=\r\n\r\nschedule_var_credit_currency := offer_currency\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_type := bank_cash\r\nschedule_var_distribute_date := date_payment_doc\r\nschedule_var_payment_date := true_payment_date\r\nschedule_var_principal := principal\r\n\r\npayment_tag_not_distributed_payment :=\r\npayment_tag_skip_auto_distribution :=\r\n\r\nincomes_reason_type_id := 101\r\nincomes_reason_check_types :=\r\n\r\nemail_payment_not_distributed_email_id =\r\nemail_payment_not_distributed_receive_users :=', 'condition := \'[b_status]\' == \'finished\' && \'[b_status]\' != \'[prev_b_status]\'', 'plugin := credits\r\nmethod := autoDistributePayment', NULL, 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%credits%' AND `method` LIKE '%autoDistributePayment%' AND start_model_type='BP' AND `controller`='payments');

# Added new automation to auto distribute payments made from an incomes reason
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '101', 'company_methodology := idea\r\n\r\nschedule_type_id := 5\r\nschedule_status_active :=\r\nschedule_substatus_proceeding :=\r\n\r\nschedule_var_credit_currency := offer_currency\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_type := bank_cash\r\nschedule_var_distribute_date := date_payment_doc\r\nschedule_var_payment_date := true_payment_date\r\nschedule_var_principal := principal\r\n\r\npayment_tag_not_distributed_payment :=\r\npayment_tag_skip_auto_distribution :=\r\n\r\nincomes_reason_type_id := 101\r\nincomes_reason_check_types :=\r\n\r\nemail_payment_not_distributed_email_id =\r\nemail_payment_not_distributed_receive_users :=', 'condition := \'[action]\' == \'addpayment\' && \'[request_is_post]\' == \'1\'', 'plugin := credits\r\nmethod := autoDistributePayment', NULL, 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%credits%' AND `method` LIKE '%autoDistributePayment%' AND start_model_type=101 AND `controller`='incomes_reasons');

######################################################################################
# 2024-07-18 - Added new automation to auto create an incomes reason when the contract is activated
#            - Deactivate the old automation which created incomes reasons
#            - Added additional settings for the autoDistributePayment automation

# Added new automation to auto create an incomes reason when the contract is activated
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Създаване на основание за приход към погасителен план', 0, NULL, 1, 'documents', NULL, 'action', '5', 'company_methodology := idea\r\n\r\nincomes_reason_type_id := 101\r\n\r\nschedule_var_credit_currency := currency\r\nschedule_var_building := building_id\r\n\r\nbuilding_company := addresscompany_id\r\n\r\nnom_contract_obligations_id := 39', 'condition := \"[prev_b_substatus]\" != \"22\"\r\ncondition := \"[b_substatus]\" == \"22\"', 'plugin := credits\r\nmethod := contractActivateCreateIncomesReason', NULL, 10, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%credits%' AND `method` LIKE '%contractActivateCreateIncomesReason%' AND start_model_type=5);

# Deactivate the old automation which created incomes reasons
UPDATE `automations` SET `active`=0 WHERE `module`='documents' AND `start_model_type`=5 AND `method` LIKE '%transform%' AND `conditions` LIKE '%[b_substatus]%' AND `active`=0;

# Added additional settings for the autoDistributePayment automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nnom_contract_obligations_id := 39') WHERE `method` LIKE '%credits%' AND `method` LIKE '%autoDistributePayment%' AND `settings` NOT LIKE '%nom_contract_obligations_id%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nschedule_var_principal := principal', '\r\nschedule_var_currency_rate := exchange_rate\r\nschedule_var_payment_currency := payment_currency\r\nschedule_var_principal := principal') WHERE `method` LIKE '%credits%' AND `method` LIKE '%autoDistributePayment%' AND `settings` NOT LIKE '%schedule_var_currency_rate%';

######################################################################################
# 2024-08-01 - Add new automation setCustomerRelatedObjects to write the relation between objects and customers when an object is updated
#            - Added settings for the setCustomerRelatedObjects if it already exists

# Add new automation setCustomerRelatedObjects to write the relation between objects and customers when an object is updated
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Попълване на свързани обекти към контрагенти', 0, NULL, 1, 'nomenclatures', NULL, 'action', '0', 'customers_types := 2', 'condition :=  \'[action]\' == \'add\' || \'[action]\' ==  \'edit\'\r\ncondition := in_array($new_model->get(\'type\'), array(9,10,11,12))', 'plugin := garitagepark\r\nmethod := setCustomerRelatedObjects', NULL, 2, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'nomenclatures' AND start_model_type = 0 AND automation_type = 'action' AND method LIKE '%setCustomerRelatedObjects%');

# Added settings for the setCustomerRelatedObjects if it already exists
UPDATE `automations` SET `settings`='customers_types := 2', `conditions`='condition :=  \'[action]\' == \'add\' || \'[action]\' ==  \'edit\'\r\ncondition := in_array($new_model->get(\'type\'), array(9,10,11,12))' WHERE `method` LIKE '%setCustomerRelatedObjects%' AND `method` LIKE '%garitagepark%' AND `settings` NOT LIKE '%customers_types%';

########################################################################
# 2024-08-06 - Added new plugin for distribution of undistributed payments

# Added new plugin for distribution of undistributed payments
INSERT INTO `dashlets_plugins` (`type`, `settings`, `is_portal`, `visible`)
  SELECT 'idea_distribute_payments', 'customer_type_client := 2\r\nincomes_reason_loan_contract_type := 101\r\nloan_contract_type := 5\r\nloan_contract_substatus_active := 22\r\nloan_contract_substatus_paid := 20', 0, 1
    WHERE NOT EXISTS(SELECT id FROM `dashlets_plugins` WHERE `type` = 'idea_distribute_payments');
SET @dashlet_id := (SELECT id FROM `dashlets_plugins` WHERE `type` = 'idea_distribute_payments');
INSERT IGNORE INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (@dashlet_id, 'Разпределяне на плащания', 'Панел за разпределяне на неразпределени плащания', 'bg'),
  (@dashlet_id, 'Payments distribution', 'Panel to distribute the undistributed payments', 'en');

######################################################################################
# 2024-08-07 - Add new automation updateBuildingStagesInRelatedDocuments to update the dates from the building in the related repayment plans

# Add new automation updateBuildingStagesInRelatedDocuments to update the dates from the building in the related repayment plans
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Актуализация дати на строителни етапи в документ Погасителен план', 0, NULL, 1, 'nomenclatures', NULL, 'action', '5', 'document_type_repayment_plan := 5\r\ndocument_substatus_active := 22\r\ndocument_way_of_payment := way_of_payment_id\r\ndocument_way_of_payment_building := 42\r\n\r\nbuilding_var_stage := stage_id\r\nbuilding_var_date := stage_end', 'condition := in_array(\'[action]\', array(\'add\', \'edit\')) && \'[request_is_post]\' == \'1\'', 'plugin := idea\r\nmethod := updateBuildingStagesInRelatedDocuments', NULL, 1, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'nomenclatures' AND start_model_type = 5 AND automation_type = 'action' AND method LIKE '%updateBuildingStagesInRelatedDocuments%');

######################################################################################
# 2024-08-15 - Add new automation validateActiveContractChange that will validate if the new sum is greater than the old sum od the repayment plan
#            - Add new automation updateRepaymentPlanSum that will handle the update of the sum of the repayment plan
#            - Add new automation markRepaymentPlanAsFinished that will handle setting the repayment plan as closed

# Add new automation validateActiveContractChange that will validate if the new sum is greater than the old sum od the repayment plan
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Да не позволява запис, ако общата сума по Активен погасителния план е по-малка от сумата преди редакция', 0, NULL, 1, 'documents', NULL, 'before_action', '5', '', 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'edit\' && \'[b_substatus]\' == \'22\'', 'plugin := idea\r\nmethod := validateActiveContractChange', 'cancel_action_on_fail := 1', 2, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'before_action' AND method LIKE '%validateActiveContractChange%');

# Add new automation updateRepaymentPlanSum that will handle the update of the sum of the repayment plan
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Обновяване на ОП към погасителния план след редакция на сумата', 0, NULL, 1, 'documents', NULL, 'action', '5', '', 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'edit\' && \'[b_substatus]\' == \'22\'', 'plugin := idea\r\nmethod := updateRepaymentPlanSum', '', 1, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'action' AND method LIKE '%updateRepaymentPlanSum%');

# Add new automation markRepaymentPlanAsFinished that will handle setting the repayment plan as closed
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Нулиране на погасителния план при преминаване от статус Активен към статус Прекратен', 0, NULL, 1, 'documents', NULL, 'action', '5', '', 'condition := \'[action]\' == \'setstatus\' && \'[prev_b_substatus]\' == \'22\' && \'[b_substatus]\' == \'39\'', 'plugin := idea\r\nmethod := markRepaymentPlanAsFinished', NULL, 1, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'action' AND method LIKE '%markRepaymentPlanAsFinished%');

# Add new automation changeRepaymentPlanCustomer that will handle changing the customer of a repayment plan
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Промяна на клиент по погасителен план', 0, NULL, 1, 'documents', NULL, 'action', '5', '', 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'edit\' && \'[b_substatus]\' == \'22\' && \'[b_customer]\' != \'[prev_b_customer]\'', 'plugin := idea\nmethod := changeRepaymentPlanCustomer', '', 5, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'action' AND method LIKE '%changeRepaymentPlanCustomer%');

######################################################################################
# 2024-09-10 - Updated settings for 'setCustomerRelatedObjects' - added new settings for object name template and option for skipping row completion

# Updated settings for 'setCustomerRelatedObjects' - added new settings for object name template and option for skipping row completion
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nobject_name_template := [type] [name]\r\nskip_role_completion := 1') WHERE `method` LIKE '%setCustomerRelatedObjects%' AND `method` LIKE '%garitagepark%' AND `settings` NOT LIKE '%object_name_template%';

######################################################################################
# 2025-05-22 - Removed unused settings from 'updateBuildingStagesInRelatedDocuments' automation

# Removed unused settings from 'updateBuildingStagesInRelatedDocuments' automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\ndocument_way_of_payment := way_of_payment_id\r\ndocument_way_of_payment_building := 42', '') WHERE module = 'nomenclatures' AND start_model_type = 5 AND automation_type = 'action' AND method LIKE '%updateBuildingStagesInRelatedDocuments%';
