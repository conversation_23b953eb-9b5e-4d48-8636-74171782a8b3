###################################################################################
### SQL nZoom Specific Updates АИЕСЕК (https://bulgariaplast.n-zoom.com/) ###
###################################################################################

######################################################################################
# 2022-06-06 - Add automation setGT2 for windows and accessories orders

# Add automation setGT2 for windows and accessories orders
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `nums`)
  SELECT 'Попълване GT2 на поръчка за дограма',
      'documents',
      'action',
      3,
'# Един или повече редове от групова таблица panel_group
gt2_var_article_code := [a_article_cd]
gt2_var_article_id := [a_article_panel_id]
gt2_var_article_name := [a_article_panel]
gt2_var_article_description := [a_notes_dograma]
gt2_var_quantity := [a_size_panel]
gt2_var_article_measure_name := [a_article_msr]
gt2_var_price := [a_price_panel]

# Един или повече редове от групова таблица accessories_services_group
gt2_var_article_code := [a_accessories_services_code]
gt2_var_article_id := [a_accessories_services_id]
gt2_var_article_name := [a_accessories_services_name]
gt2_var_article_description := [a_accessories_note]
gt2_var_quantity := [a_accessories_services_quantity]
gt2_var_article_measure_name := [a_accessories_services_measure]
gt2_var_price := [a_accessories_services_price]

# Един ред за монтаж
gt2_var_article_code := INSТ
gt2_var_article_id := 30
gt2_var_article_name := php(\'[b_model_lang]\' == \'en\' ? \'Installation\' : \'Монтаж\')
gt2_var_article_description :=
gt2_var_quantity := 1
gt2_var_article_measure_name := 1
gt2_var_price := [a_service_order__total]

# Един ред за транспорт
gt2_var_article_code := TRNS
gt2_var_article_id := 114
gt2_var_article_name := php(\'[b_model_lang]\' == \'en\' ? \'Transport\' : \'Транспорт\')
gt2_var_article_description :=
gt2_var_quantity := 1
gt2_var_article_measure_name := 1
gt2_var_price := [a_delivery_order__total]',

'condition := \'[request_is_post]\'
condition := \'[action]\' == \'setstatus\'
condition := \'[b_substatus]\' == \'20\'',

'method := setGT2',
      0
    WHERE NOT EXISTS(
        SELECT `id`
          FROM `automations`
          WHERE `module` = 'documents'
            AND `automation_type` = 'action'
            AND `start_model_type` = 3
            AND `method` = 'method := setGT2');
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `nums`)
  SELECT 'Попълване GT2 на поръчка за аксесоари',
      'documents',
      'action',
      12,
'# Един или повече редове от групова таблица accessories_services_group
gt2_var_article_code := [a_accessories_services_code]
gt2_var_article_id := [a_accessories_services_id]
gt2_var_article_name := [a_accessories_services_name]
gt2_var_article_description := [a_accessories_note]
gt2_var_quantity := [a_accessories_services_quantity]
gt2_var_article_measure_name := [a_accessories_services_measure]
gt2_var_price := [a_accessories_services_price]

# Един ред за монтаж
gt2_var_article_code := INSТ
gt2_var_article_id := 30
gt2_var_article_name := php(\'[b_model_lang]\' == \'en\' ? \'Installation\' : \'Монтаж\')
gt2_var_article_description :=
gt2_var_quantity := 1
gt2_var_article_measure_name := 1
gt2_var_price := [a_service_order__total]

# Един ред за транспорт
gt2_var_article_code := TRNS
gt2_var_article_id := 114
gt2_var_article_name := php(\'[b_model_lang]\' == \'en\' ? \'Transport\' : \'Транспорт\')
gt2_var_article_description :=
gt2_var_quantity := 1
gt2_var_article_measure_name := 1
gt2_var_price := [a_delivery_order__total]',

'condition := \'[request_is_post]\'
condition := \'[action]\' == \'setstatus\'
condition := \'[b_substatus]\' == \'42\'',

'method := setGT2',
      0
    WHERE NOT EXISTS(
        SELECT `id`
          FROM `automations`
          WHERE `module` = 'documents'
            AND `automation_type` = 'action'
            AND `start_model_type` = 12
            AND `method` = 'method := setGT2');

########################################################################
# 2022-06-07 - Added rest settings for the BulgariaplastApi
#            - Update all materials (8) to be of subtype commodity
#            - Update all materials (8) to be in category "All" (1)

# Added rest settings for the BulgariaplastApi
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'allowed_rest_user_agents', 'BulgariaPlastAPI'),
  ('rest', 'filter_vars_nomenclatures_8', 'all'),
  ('rest', 'filter_vars_nomenclatures_types', 'all')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

# Update all materials (8) to be of subtype commodity
UPDATE nom SET subtype='commodity' WHERE type=8 and subtype!='commodity';
# Update all materials (8) to be in category "All" (1)
INSERT IGNORE INTO nom_cats  SELECT id, 1, 'Nomenclature' FROM nom where type=8;

#######################################################################
# 2022-06-09 - Added rest settings for the BulgariaplastApi

# Added rest settings for the BulgariaplastApi
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_nomenclatures_11', 'all'),
  ('rest', 'filter_vars_nomenclatures_13', 'all'),
  ('rest', 'filter_vars_documents_2', 'all'),
  ('rest', 'filter_vars_documents_3', 'all')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

########################################################################
# 2022-06-20 - Added setting for settings relatives into createOrdersRelations

# Added setting for settings relatives createOrdersRelations
UPDATE automations
SET settings = REPLACE(
        settings,
        'set_relatives := ', 'set_relatives := 1')
WHERE method LIKE '%ustil%' AND method LIKE '%createOrdersRelations%'
  AND settings NOT LIKE '%set_relatives := 1%';

######################################################################################
# 2022-06-23 - New report: production_schedule

# New report: production_schedule
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  ('442', 'production_schedule', 'schedule_users := 1,11
order_types := 3,12
order_type_windows := 3
order_type_glass_panes :=
order_3_substatus_mount := 16
order_3_substatus_delivery := 21
order_3_substatus_place := 22
order_12_substatus_mount := 38
order_12_substatus_delivery := 43
order_12_substatus_place := 44
order_instalation_option_with_installation := 1
order_instalation_option_without_installation := 2
order_delivery_option_without_delivery := 118

doc_type_production_capacity := 6
doc_type_installation_request := 13
doc_type_production_request := 8
doc_type_delivery_request :=
nom_type_windows_profile_type := 12
nom_type_windows_color := 19

production_request_substatus_produced := 26
production_request_substatus_failed := 27
production_capacity_substatus_approved := 28
delivery_request_substatus_failed :=

windows_profile_type_type_var := type_dograma
blinds_kind_var := kind_sht
production_request_status_var := stat_req
production_request_status_option_normal := 1
production_request_status_option_urgent := 2
production_request_notes_var := notes_production
production_request_kind_production_var := kind_production
production_request_production_article_id_var := production_article_id
production_request_production_article_var := production_article
production_request_production_article_color_var := production_article_color
installation_request_installation_date_var := for_date
installation_request_kind_sht_var := kind_sht
installation_request_production_date_var := production_date

order_windows_profile_type_var :=
order_windows_num_frames_var :=
order_windows_color_var :=
order_windows_size_var :=
order_windows_related_order_id_var :=

order_glass_panes_num_single_var :=
order_glass_panes_num_double_var :=
order_glass_panes_num_triple_var :=
order_glass_panes_num_total_var :=
order_glass_panes_date_production_var :=

employee_blinds_kind_var_id :=

production_request_num_var_id := 4701
production_request_size_var_id := 4702
production_request_blinds_kind_var_id := 4703
production_request_status_var_id := 4704
production_request_notes_var_id := 4705
production_request_produced_on_var_id := 4708

production_capacity_blinds_kind_var_id := 4501
production_capacity_max_num_var_id := 4502
production_capacity_max_size_var_id := 4503
production_capacity_windows_profile_type_var_id := 4505

blinds_kind_windows_option := 1
blinds_kind_glass_panes_option := 4

max_num_results := 1000', 0);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('442', 'График производство', 'bg'),
  ('442', 'Production schedule', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '442', '1'),
  ('reports', '', 'export', '442', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '442';

######################################################################################
# 2022-06-30 - Add settings for report: production_schedule

# Add settings for report: production_schedule
UPDATE `reports`
  SET `settings` = CONCAT(`settings`, '\r\n\r\nshow_filter_trademark := 0\r\nshow_filter_color := 1')
  WHERE `type` = 'production_schedule'
    AND `settings` NOT LIKE '%show_filter_trademark%';
UPDATE `reports`
  SET `settings` = REPLACE(
    `settings`,
    'installation_request_installation_date_var',
    'production_request_workshift_id_var := workshift_id
production_request_workshift_var := workshift
production_request_workshop_id_var := workshop_id
production_request_workshop_var := workshop
production_request_produced_on_var := produced_on
production_request_blinds_kind_var := kind_production
installation_request_installation_date_var')
  WHERE `type` = 'production_schedule'
    AND `settings` NOT LIKE '%production_request_workshift_var%';
UPDATE `reports`
  SET `settings` = REPLACE(
    `settings`,
    'production_request_blinds_kind_var_id',
    '#production_request_blinds_kind_var_id')
  WHERE `type` = 'production_schedule'
    AND `settings` NOT LIKE '%#production_request_blinds_kind_var_id%';
UPDATE `reports`
  SET `settings` = REPLACE(
    `settings`,
    'production_request_produced_on_var_id',
    '#production_request_produced_on_var_id')
  WHERE `type` = 'production_schedule'
    AND `settings` NOT LIKE '%#production_request_produced_on_var_id%';

######################################################################################
# 2022-06-30 - Added var filter for document types
#            - Added automation that prepares api data

# Added var filter for document types
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_documents_types', 'all')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

# Added automation that prepares api data
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Подготовка на данни за BulgariaplastAPI', 0, NULL, 1, 'documents', NULL, 'before_action', '3', '', 'condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[request_is_post]\'', 'plugin := bulgariaplast\r\nmethod := prepareApiData', 'cancel_action_on_fail := 1', 10, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%prepareApiData%' AND `start_model_type`=3);

######################################################################################
# 2022-07-06 - Add settings for report: production_schedule

# Add settings for report: production_schedule
UPDATE `reports`
  SET `settings` = CONCAT(`settings`, '
show_filter_kind_production := 1
show_filter_type_dograma := 1
show_filter_installation_date := 1
show_filter_workshift := 1
show_filter_workshop := 1

# Да бъдат ли readonly полетата при показване на „Изработено“:
# стойност 1 означава да са readonly и да няма множествено задаване на датата „Произведено на“
# стойност 0 или липса на стойност означава да не са readonly и да има множествено задаване на датата „Произведено на“
production_readonly_fields := 0')
  WHERE `type` = 'production_schedule'
    AND `settings` NOT LIKE '%show_filter_type_dograma%';

######################################################################################
# 2022-07-07 - Change setting for report: production_schedule

# Change setting for report: production_schedule
UPDATE `reports`
  SET `settings` = REPLACE(
    `settings`,
    'show_filter_installation_date := 1',
    'show_filter_installation_date := 0')
  WHERE `type` = 'production_schedule'
    AND `settings` LIKE '%show_filter_installation_date := 1%';

######################################################################################
# 2022-07-18 - Add new automation for creating production requests from orders

# Add new automation for creating production requests from orders
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `nums`)
  SELECT 'Добавяне на заявки за производство от поръчка за дограма',
      'documents',
      'action',
      3,
'
# Задължителни настройки: module, type и поне една настройка за попълване на променлива.

# Модул, контролер и тип на целевия запис:
module := documents
controller :=
type := 8

# Попълване на целевия запис:
# Настройките с име с префикс b_ посочват коя основна променлива на целевия запис да се попълва.
# Настройките с име с префикс а_ посочват коя допълнителна променлива на целевия запис да се попълва, като тя трябва да е едноредова променлива, а не групова (т.е. да не е от групова таблица или друга структура, към която може да има повече от един ред).
# Стойността на настройката може да бъде:
# * placeholder, пример: [b_full_num]
# * твърда стойност, пример: 1
# * php, пример: php(Calculator::calc_sql($registry, \'SELECT ...\'))
# В случай, че стойността на настройката се замести с масив от данни, то ще се създадат толкова целеви записа, колкото елементи има масива. Ако за една настройка стойността е единична, а за друга е многоредова, ще се генерира грид от данни. Единичните стойности ще се повтарят за всеки ред. Ако има няколко многоредови стойности, то те трябва да са с еднакъв брой редове. Ако са с различен брой редове ще се изведе съобщение за грешка.
b_custom_num := [b_full_num]
b_date := [b_date]
b_customer := [b_customer]
b_branch := [b_branch]
b_contact_person := [b_contact_person]
b_office := [b_office]
b_deadline := [a_date_production]
a_kind_production := 1
a_production_article := [a_article_panel]
a_production_article_id := [a_article_panel_id]
a_production_article_color := [a_kind_panel]
a_num_req := [a_num_dograma]
a_meters_req := [a_size_panel]
a_stat_req := 1
a_notes_production := [a_notes_dograma]

# Условие, което се проверява на всеки ред от грида (получен от горните настройки), за да се прецени дали той да се използва.
condition := \'[a_num_dograma]\' != \'\' && \'[a_num_dograma]\' != \'0\'

# Променлива в текущия запис, в която да се попълват ID-тата на създаваните целеви записи.
relation_id_var := request_id
# Да се премахва ли ID-то на свързания целеви запис, когато реда не отговаря на условието от настройката condition.
# Тази настройка се гледа само ако за настройката relation_id_var е зададена стойност.
relation_id_remove_on_no_condition := 0

# При добавяне на модел, той да се добавя като дъщерен елемент в таб „Връзки“.
# Забележете „При добавяне“. Т.е. за стари записи не се правят връзки, дори да ги има в ДП от настройката relation_id_var. Ако се реши може да се доработи възможност (може би активирана от настройка) да се създават връзки и за тях.
# Връзки в таб „Връзки“ се правят дори да не е зададена настройката relation_id_var.
add_relatives_child := 1

# SQL филтриращи условия за намиране на вече съществуващи целеви модели (т.е. при редакция да отговарят на тези условия).
edit_filters_sql := d.status = \'opened\'

# Да се деактивират ли целевите записи, ако вече няма условия да ги има (реда е изтрит или не отговарят на условията).
# Съответно да се активират ли деактивираните целеви записи, ако отново реда отговаря на условията.
# ВНИМАНИЕ!!!:
# Да се има предвид, че деактивирането на записи от изтрити редове работи само когато АД-то е от тип action.
# При crontab няма old_model и принципно нямаме „предишно състояние“. За деактивиране при crontab ще е нужн отделно АД, което да открива записите, които са „увиснали“, чрез where condition с SQL подзаявка.
activate_and_deactivate := 1

# Съобщения:
# При успешно запазване на целеви записи:
message_success_saved_models_bg := Успешно запазване на заявки за производство!
# При успешно деактивиране на свързани целеви записи:
message_success_deactivated_models_bg :=
# При успешно активиране на свързани целеви записи, които преди са били деактивирани:
message_success_activated_models_bg :=
',

'condition := \'[request_is_post]\'
condition := \'[action]\' == \'setstatus\'
condition := \'[b_substatus]\' == \'20\'',

'method := gridToModels',
      0
    WHERE NOT EXISTS(
        SELECT `id`
          FROM `automations`
          WHERE `module` = 'documents'
            AND `automation_type` = 'action'
            AND `start_model_type` = 3
            AND `method` = 'method := gridToModels');

######################################################################################
# 2022-10-24 - Fixed the non-trimed codes of nomenclatures

# Fixed the non-trimed codes of nomenclatures
UPDATE nom SET `code`=TRIM(`code`) WHERE `code` LIKE '% ' OR `code` LIKE ' %';

########################################################################
# 2022-11-02 - Add new setting for report production_schedule

# Add new setting for report production_schedule
UPDATE `reports`
  SET `settings` = CONCAT(`settings`, '

# Допълнително филтриране на резултатите чрез SQL (alias-ите, които могат да се използват са доста, но могат да бъдат видени в кода в custom.report.query.php на справката и по-конкретно там, където се използва настройката).
production_requests_sql_filter := d.substatus != 57

# Бутон, при натискането на който да се прилагат action-автоматичните действия към заявките за производство:
# Етикет на бутона. Ако не е зададен етикет за текущия език на интерфейса - не се показва бутона. (суфикса bg е езика на интерфейса)
button_execute_action_automations_label_bg := Потвърди график
# Съобщение за успех. Ако не е зададено такова за текущия език на интерфейса се извежда съобщението за успех на бутона за запазване на промените. (суфикса bg е езика на интерфейса)
button_execute_action_automations_msg_success_bg := Успешно потвърждаване на график!
# Съобщение, което да се извежда при натискане на бутона, ако има незапазени промени. Ако не е зададено съобщение за текущия език на интерфейса - не се прави тази валидация. (суфикса bg е езика на интерфейса)
button_execute_action_automations_save_changes_validation_message_bg := За да потвърдите графика трябва първо да запишете промените!')
  WHERE `type` = 'production_schedule'
    AND `settings` NOT LIKE '%production_requests_sql_filter%';
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `nums`)
  SELECT
    'Потвърждаване на график на заявка за производство',
    'documents',
    'action',
    '8',
    '',
    'condition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'execute_action_automations\'',
    'method := status\r\nnew_status := locked\r\nnew_substatus := 57',
    0
  WHERE NOT EXISTS(
    SELECT `id`
      FROM `automations`
      WHERE `module` = 'documents'
        AND `automation_type` = 'action'
        AND `start_model_type` = '8'
        AND `conditions` LIKE '%execute_action_automations%'
        AND `method` LIKE 'method := status%');
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `nums`)
  SELECT
    'Попълване на График №',
    'documents',
    'action',
    '8',
    '',
    'condition := \'[request_is_post]\'\r\ncondition := (\'[action]\' == \'execute_action_automations\' || in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && \'[b_substatus]\' === \'57\')',
    'method := setAdditionalVar\r\nvar_name := schedule_id\r\nvar_value := php(General::strftime(\'%Y%m%d\', strtotime(\'[b_deadline]\')))',
    0
  WHERE NOT EXISTS(
    SELECT `id`
      FROM `automations`
      WHERE `module` = 'documents'
        AND `automation_type` = 'action'
        AND `start_model_type` = '8'
        AND `conditions` LIKE '%execute_action_automations%'
        AND `method` LIKE 'method := setAdditionalVar%');

########################################################################
# 2022-11-25 - Add new settings for report production_schedule

# Add new settings for report production_schedule
UPDATE `reports`
  SET `settings` = REPLACE(`settings`, 'th_measure_bg := м²\r\nth_measure_en := м²', 'th_measure_bg := кв.м.\r\nth_measure_en := кв.м.')
  WHERE `type` = 'production_schedule'
    AND `settings` LIKE '%th_measure_bg := м²\r\nth_measure_en := м²%';
UPDATE `reports`
  SET `settings` = REPLACE(`settings`, 'schedule_by_articles := 0', 'schedule_by_articles := 1')
  WHERE `type` = 'production_schedule'
    AND `settings` LIKE '%schedule_by_articles := 0%';
UPDATE `reports`
  SET `settings` = REPLACE(`settings`, 'schedule_by_articles_types :=\r\n', 'schedule_by_articles_types := 12\r\n')
  WHERE `type` = 'production_schedule'
    AND `settings` LIKE '%schedule_by_articles_types :=\r\n%';
UPDATE `reports`
  SET `settings` = REPLACE(`settings`, 'schedule_by_articles_workshops_types :=\r\n', 'schedule_by_articles_workshops_types := 18\r\n')
  WHERE `type` = 'production_schedule'
    AND `settings` LIKE '%schedule_by_articles_workshops_types :=\r\n%';

######################################################################################
# 2023-01-10 - Added export plugin for FSD

# Added export plugin for FSD
INSERT IGNORE INTO `exports` (`id`, `type`, `model`, `model_type`, `settings`, `visible`) VALUES
(69, 'bulgariaplast_to_fsd', 'Finance_Incomes_Reason', 1, 'export_file_name := FAKR1_ACC.FLT\r\nexport_hide_filters := format, separator\r\nexport_tags := 17\r\n\r\n#мерни единици\r\nmeasurement_1 := бр.\r\nmeasurement_2 := кг.\r\nmeasurement_3 := м2\r\nmeasurement_4 := м3\r\nmeasurement_5 := л.м.\r\nmeasurement_6 := м.\r\nmeasurement_7 := см.\r\nmeasurement_8 := к-т\r\nmeasurement_9 := л.\r\nmeasurement_10 := ч.\r\n\r\n# аналитичност за услуги\r\naccount_for_nom_type_6 :=\r\n# аналитичност за материали\r\naccount_for_nom_type_8 :=\r\n# аналитичност за аксесоари\r\naccount_for_nom_type_11 :=\r\n', 1),
(70, 'bulgariaplast_to_fsd', 'Finance_Incomes_Reason', 3, 'export_file_name := FAKR1_ACC.FLT\r\nexport_hide_filters := format, separator\r\nexport_tags := 17\r\n\r\n#мерни единици\r\nmeasurement_1 := бр.\r\nmeasurement_2 := кг.\r\nmeasurement_3 := м2\r\nmeasurement_4 := м3\r\nmeasurement_5 := л.м.\r\nmeasurement_6 := м.\r\nmeasurement_7 := см.\r\nmeasurement_8 := к-т\r\nmeasurement_9 := л.\r\nmeasurement_10 := ч.\r\n\r\n# аналитичност за услуги\r\naccount_for_nom_type_6 :=\r\n# аналитичност за материали\r\naccount_for_nom_type_8 :=\r\n# аналитичност за аксесоари\r\naccount_for_nom_type_11 :=\r\n', 1),
(71, 'bulgariaplast_to_fsd', 'Finance_Incomes_Reason', 4, 'export_file_name := FAKR1_ACC.FLT\r\nexport_hide_filters := format, separator\r\nexport_tags := 17\r\n\r\n#мерни единици\r\nmeasurement_1 := бр.\r\nmeasurement_2 := кг.\r\nmeasurement_3 := м2\r\nmeasurement_4 := м3\r\nmeasurement_5 := л.м.\r\nmeasurement_6 := м.\r\nmeasurement_7 := см.\r\nmeasurement_8 := к-т\r\nmeasurement_9 := л.\r\nmeasurement_10 := ч.\r\n\r\n# аналитичност за услуги\r\naccount_for_nom_type_6 :=\r\n# аналитичност за материали\r\naccount_for_nom_type_8 :=\r\n# аналитичност за аксесоари\r\naccount_for_nom_type_11 :=\r\n', 1),
(72, 'bulgariaplast_to_fsd', 'Finance_Expenses_Reason', 20, 'export_file_name := FAK1.FLT\r\nexport_hide_filters := format, separator\r\nexport_tags := 18\r\n\r\n#мерни единици\r\nmeasurement_1 := бр.\r\nmeasurement_2 := кг.\r\nmeasurement_3 := м2\r\nmeasurement_4 := м3\r\nmeasurement_5 := л.м.\r\nmeasurement_6 := м.\r\nmeasurement_7 := см.\r\nmeasurement_8 := к-т\r\nmeasurement_9 := л.\r\nmeasurement_10 := ч.\r\n\r\n# аналитичност за услуги\r\naccount_for_nom_type_6 :=\r\n# аналитичност за материали\r\naccount_for_nom_type_8 :=\r\n# аналитичност за аксесоари\r\naccount_for_nom_type_11 :=\r\n', 1),
(73, 'bulgariaplast_to_fsd', 'Finance_Expenses_Reason', 22, 'export_file_name := FAK1.FLT\r\nexport_hide_filters := format, separator\r\nexport_tags := 18\r\n\r\n#мерни единици\r\nmeasurement_1 := бр.\r\nmeasurement_2 := кг.\r\nmeasurement_3 := м2\r\nmeasurement_4 := м3\r\nmeasurement_5 := л.м.\r\nmeasurement_6 := м.\r\nmeasurement_7 := см.\r\nmeasurement_8 := к-т\r\nmeasurement_9 := л.\r\nmeasurement_10 := ч.\r\n\r\n# аналитичност за услуги\r\naccount_for_nom_type_6 :=\r\n# аналитичност за материали\r\naccount_for_nom_type_8 :=\r\n# аналитичност за аксесоари\r\naccount_for_nom_type_11 :=\r\n', 1),
(74, 'bulgariaplast_to_fsd', 'Finance_Expenses_Reason', 23, 'export_file_name := FAK1.FLT\r\nexport_hide_filters := format, separator\r\nexport_tags := 18\r\n\r\n#мерни единици\r\nmeasurement_1 := бр.\r\nmeasurement_2 := кг.\r\nmeasurement_3 := м2\r\nmeasurement_4 := м3\r\nmeasurement_5 := л.м.\r\nmeasurement_6 := м.\r\nmeasurement_7 := см.\r\nmeasurement_8 := к-т\r\nmeasurement_9 := л.\r\nmeasurement_10 := ч.\r\n\r\n# аналитичност за услуги\r\naccount_for_nom_type_6 :=\r\n# аналитичност за материали\r\naccount_for_nom_type_8 :=\r\n# аналитичност за аксесоари\r\naccount_for_nom_type_11 :=\r\n', 1);

INSERT IGNORE INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(69, 'Експорт на приходни документи', '', 'bg'),
(69, 'Export of revenue documents', '', 'en'),
(70, 'Експорт на приходни документи', '', 'bg'),
(70, 'Export of revenue documents', '', 'en'),
(71, 'Експорт на приходни документи', '', 'bg'),
(71, 'Export of revenue documents', '', 'en'),
(72, 'Експорт на разходни документи', '', 'bg'),
(72, 'Export of expense documents', '', 'en'),
(73, 'Експорт на разходни документи', '', 'bg'),
(73, 'Export of expense documents', '', 'en'),
(74, 'Експорт на разходни документи', '', 'bg'),
(75, 'Export of expense documents', '', 'en');

######################################################################################
# 2023-01-31 - Added new automation to manage the change the status of order if the production request order is changed
#            - Updated settings for 'production_schedule' report

# Added new automation to manage the change the status of order if the production request order is changed
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Промяна на статус на поръчка при смяна на стаутс на заявка', 0, NULL, 1, 'documents', NULL, 'action', '8', 'order_type_id := 3,12\r\n\r\norder_var_installation := instalation\r\norder_var_delivery := delivery\r\n\r\norder_3_substatus_mount := 16\r\norder_3_substatus_delivery := 21\r\norder_3_substatus_place := 22\r\norder_12_substatus_mount := 38\r\norder_12_substatus_delivery := 43\r\norder_12_substatus_place := 44\r\norder_instalation_option_with_installation := 1\r\norder_instalation_option_without_installation := 2\r\norder_delivery_option_without_delivery := 118', 'condition := \'[b_substatus]\' != \'[prev_b_substatus]\'\r\ncondition := \'[b_substatus]\' == \'26\'', 'plugin := bulgariaplast\r\nmethod := changeOrderStatus', NULL, 0, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 8 AND automation_type = 'action' AND method LIKE '%changeOrderStatus%');

# Updated settings for 'production_schedule' report
UPDATE `reports` SET `settings`='schedule_users := 1,10,11, 6\r\norder_types := 3,12\r\norder_type_windows := 3\r\norder_type_glass_panes :=\r\n\r\ndoc_type_production_capacity := 6\r\ndoc_type_installation_request := 13\r\ndoc_type_production_request := 8\r\ndoc_type_delivery_request :=\r\nnom_type_windows_profile_type := 12\r\nnom_type_windows_color := 19\r\n\r\nproduction_request_substatus_produced := 26\r\nproduction_request_substatus_failed := 27\r\nproduction_capacity_substatus_approved := 28\r\ndelivery_request_substatus_failed :=\r\n\r\nwindows_profile_type_type_var := type_dograma\r\nblinds_kind_var := kind_sht\r\nproduction_request_status_var := stat_req\r\nproduction_request_status_option_normal := 1\r\nproduction_request_status_option_urgent := 2\r\nproduction_request_notes_var := notes_production\r\nproduction_request_kind_production_var := kind_production\r\nproduction_request_production_article_id_var := production_article_id\r\nproduction_request_production_article_var := production_article\r\nproduction_request_production_article_color_var := production_article_color\r\nproduction_request_workshift_id_var := workshift_id\r\nproduction_request_workshift_var := workshift\r\nproduction_request_workshop_id_var := workshop_id\r\nproduction_request_workshop_var := workshop\r\nproduction_request_produced_on_var := produced_on\r\nproduction_request_blinds_kind_var := kind_production\r\ninstallation_request_installation_date_var := for_date\r\ninstallation_request_kind_sht_var := kind_sht\r\ninstallation_request_production_date_var := production_date\r\n\r\norder_windows_profile_type_var :=\r\norder_windows_num_frames_var :=\r\norder_windows_color_var :=\r\norder_windows_size_var :=\r\norder_windows_related_order_id_var :=\r\n\r\norder_glass_panes_num_single_var :=\r\norder_glass_panes_num_double_var :=\r\norder_glass_panes_num_triple_var :=\r\norder_glass_panes_num_total_var :=\r\norder_glass_panes_date_production_var :=\r\n\r\nemployee_blinds_kind_var_id :=\r\n\r\nproduction_request_num_var_id := 4701\r\nproduction_request_size_var_id := 4702\r\n#production_request_blinds_kind_var_id := 4703\r\nproduction_request_status_var_id := 4704\r\nproduction_request_notes_var_id := 4705\r\n#production_request_produced_on_var_id := 4708\r\n\r\nproduction_capacity_blinds_kind_var_id := 4501\r\nproduction_capacity_max_num_var_id := 4502\r\nproduction_capacity_max_size_var_id := 4503\r\nproduction_capacity_windows_profile_type_var_id := 4505\r\n\r\nblinds_kind_windows_option := 1\r\nblinds_kind_glass_panes_option := 4\r\n\r\nmax_num_results := 1000\r\n\r\nshow_filter_trademark := 0\r\nshow_filter_color := 1\r\nshow_filter_kind_production := 1\r\nshow_filter_type_dograma := 1\r\nshow_filter_installation_date := 0\r\nshow_filter_workshift := 1\r\nshow_filter_workshop := 1\r\n\r\n# Да бъдат ли readonly полетата при показване на „Изработено“:\r\n# стойност 1 означава да са readonly и да няма множествено задаване на датата „Произведено на“\r\n# стойност 0 или липса на стойност означава да не са readonly и да има множествено задаване на датата „Произведено на“\r\nproduction_readonly_fields := 0\r\n\r\n# Допълнително филтриране на резултатите чрез SQL (alias-ите, които могат да се използват са доста, но могат да бъдат видени в кода в custom.report.query.php на справката и по-конкретно там, където се използва настройката).\r\nproduction_requests_sql_filter := d.substatus != 57\r\n\r\n# Бутон, при натискането на който да се прилагат action-автоматичните действия към заявките за производство:\r\n# Етикет на бутона. Ако не е зададен етикет за текущия език на интерфейса - не се показва бутона. (суфикса bg е езика на интерфейса)\r\nbutton_execute_action_automations_label_bg := Потвърди график\r\n# Съобщение за успех. Ако не е зададено такова за текущия език на интерфейса се извежда съобщението за успех на бутона за запазване на промените. (суфикса bg е езика на интерфейса)\r\nbutton_execute_action_automations_msg_success_bg := Успешно потвърждаване на график!\r\n# Съобщение, което да се извежда при натискане на бутона, ако има незапазени промени. Ако не е зададено съобщение за текущия език на интерфейса - не се прави тази валидация. (суфикса bg е езика на интерфейса)\r\nbutton_execute_action_automations_save_changes_validation_message_bg := За да потвърдите графика трябва първо да запишете промените!\r\n\r\n#Да се скрие ли колоната „бр.“ (непразна стойност на настройката означава: да се скрива)\r\nhide_column_count := 0\r\n# Текст за антетката за мерна единица\r\nth_measure_bg := кв.м.\r\nth_measure_en := кв.м.\r\n\r\n# Да може ли да се генерира график по артикули (непразна стойност на настройката означава: да може, при коего ще се появи бутон „График по артикули“)\r\nschedule_by_articles := 1\r\n# ID-та на типовете артикули, които да се показват като колони в таблицата с график по артикули (към тях се добавят и артикулите, които участват в заявките за производство). Изброяват се разделени със запетая.\r\nschedule_by_articles_types := 12\r\n# Тагове, с които да са тагнати артикулите. Изброяват се със запетая.\r\nschedule_by_articles_tags :=\r\n# ID-та на типовете номенклатури за цехове, които да се показват като колони при график по артикули.\r\nschedule_by_articles_workshops_types := 18' WHERE  `type`='production_schedule' AND `settings` LIKE '%_substatus_mount%';

######################################################################################
# 2023-02-13 - Added additional rest settings
#

# Added var filter for customers
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_customers', 'all'),
    ('rest', 'filter_vars_documents_12', 'all')
ON DUPLICATE KEY UPDATE
                       `value` = VALUES(`value`);

######################################################################################
# 2023-02-17 - Added new report 'merge_orders', copy of the 'ustil_unite_orders' report

# Added new report 'merge_orders', copy of the 'ustil_unite_orders' report
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (448, 'merge_orders', 'invoice_to_types := 2,3,4\r\nreport_super_users := 11,22\r\norder_types := 3,12\r\nfailed_substatuses := 24,46\r\n\r\ntag_processed_order := 14\r\ntag_deposit := 15\r\n\r\ncustomer_invoice_to := customer_invoice_id\r\norders_total := total_with_vat\r\norder_pay_sum := link_pay_sum\r\norder_pay_id := link_pay_id\r\norder_pay_name := link_pay_name\r\n\r\nfin_incomes_reason_create_type := 101\r\nfin_incomes_reason_company := 1', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (448, 'Създаване на продажби/плащания', '01. Поръчки', NULL, 'bg'),
  (448, 'Create sales / payments', '01. Orders', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '448', '1'),
  ('reports', '', 'export', '448', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '448';

######################################################################################
# 2023-02-23 - Updated setting for 'merge_orders' report

# Updated setting for 'merge_orders' report
UPDATE `reports` SET `settings`='filters_oldest_date_interval := 2 months\r\n\r\ninvoice_to_types := 2,3,4\r\nreport_super_users := 11,22\r\nreport_bank_roles := 1,3\r\norder_types := 3,12\r\nfailed_substatuses := 24,46\r\n\r\ntag_processed_order := 14\r\ntag_deposit := 15\r\n\r\ncustomer_invoice_to := customer_invoice_id\r\ncustomer_invoice_to_name := customer_invoice\r\norders_total := total_with_vat\r\norder_pay_sum := link_pay_sum\r\norder_pay_id := link_pay_id\r\norder_pay_name := link_pay_name\r\norder_3_statuses := \r\norder_12_statuses := \r\n\r\nfin_incomes_reason_create_type := 101\r\nfin_incomes_reason_company := 1\r\n\r\npayment_id_3 := link_pay_id\r\npayment_name_3 := link_pay_name\r\npayment_sum_3 := link_pay_sum\r\npayment_id_12 := link_pay_id\r\npayment_name_12 := link_pay_name\r\npayment_sum_12 := link_pay_sum' WHERE  `type`='merge_orders';

#########################################################################################
# 2023-03-16 - Added new setting for createProductionRequests automation

# Added new setting for setting display_customer_type in Sales report
UPDATE `automations` SET `settings` = REPLACE(`settings`, 'get_company_data_by := user', 'get_company_data_by := office')
WHERE `method` LIKE '%createSalesForOrders%' AND settings LIKE '%get_company_data_by := user%';

#########################################################################################
# 2023-03-21 - Added settings whether to export rows data

# Added setting whether to export rows data
UPDATE exports SET settings=CONCAT(settings, "\r\nexports_rows_data := 1") WHERE type="bulgariaplast_to_fsd" AND model="Finance_Incomes_Reason" AND settings NOT LIKE '%exports_rows_data%';
UPDATE exports SET settings=CONCAT(settings, "\r\nexports_rows_data := ") WHERE type="bulgariaplast_to_fsd" AND model="Finance_Expenses_Reason" AND settings NOT LIKE '%exports_rows_data%';

# Added setting for the document debit/credit account
UPDATE exports SET settings=CONCAT(settings, "\r\nmain_debit_account := \r\nmain_credit_account := ") WHERE type="bulgariaplast_to_fsd" AND model="Finance_Incomes_Reason" AND settings NOT LIKE '%main_debit_account%';
UPDATE exports SET settings=CONCAT(settings, "\r\nmain_debit_account := account_number_expense\r\nmain_credit_account := 401") WHERE type="bulgariaplast_to_fsd" AND model="Finance_Expenses_Reason" AND settings NOT LIKE '%main_debit_account%';

######################################################################################
# 2023-06-14 - Added new report 'bulgariaplast_transport_schedule'

# Added new report 'bulgariaplast_transport_schedule'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (456, 'bulgariaplast_transport_schedule', 'doc_type_transport_request := 10\r\ndoc_var_delivery_date := date_delivery\r\ndoc_var_destination := delivery_destination_id\r\ndoc_var_city := delivery_cityid\r\ndoc_var_order_num := order_full_num\r\ndoc_var_vehicle := vehicle_id\r\ndoc_var_vehicle_name := vehicle_name\r\ndoc_var_driver := driver_id\r\ndoc_var_driver_name := driver_name\r\ndoc_var_comments := comments_address\r\ndoc_var_notes := order_note\r\ndoc_var_panel_file := file_panel\r\ndoc_var_module := module_num\r\ndoc_var_production_article := production_article_id\r\ndoc_var_req_num := num_req\r\ndoc_var_req_meters := meters_req\r\ndoc_var_req_transport_list := transport_document_id\r\n\r\ndoc_type_transport_list := 9\r\ndoc_transport_list_new_status := closed_49\r\ndoc_var_trans_vehicle := vehicle_id\r\ndoc_var_trans_vehicle_name := vehicle_name\r\ndoc_var_trans_driver := driver_id\r\ndoc_var_trans_driver_name := driver_name\r\ndoc_var_trans_request := request_id\r\ndoc_var_trans_request_num := request_num\r\ndoc_var_trans_request_customer := customer_order_id\r\ndoc_var_trans_request_customer_name := customer_order\r\ndoc_var_trans_request_destination := delivery_destination_id\r\ndoc_var_trans_request_destination_name := delivery_destination_name\r\ndoc_var_trans_request_city := delivery_cityid\r\ndoc_var_trans_request_city_name := delivery_city\r\ndoc_var_trans_request_date := date_order\r\ndoc_var_trans_request_full_num := order_full_num\r\ndoc_var_trans_request_custom_num := order_custom_num\r\ndoc_var_trans_request_description := order_description\r\ndoc_var_trans_request_file := file_panel\r\ndoc_var_trans_request_note := order_note\r\n\r\ncustomers_types_filtered := 1,3,4\r\ncustomer_type_driver := 1\r\n\r\nnom_type_vehicle := 9\r\nnom_var_capacity_count := windows_number\r\nnom_var_capacity_volume := vol_windows\r\n\r\nnom_type_region := 20\r\nnom_type_settlement := 16', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (456, 'График транспорт', NULL, NULL, 'bg'),
  (456, 'Transport schedule', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '456', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report')
      AND `model_type` = '456';

########################################################################
# 2023-06-16- Added pattern plugin for orders

# Added pattern plugin for orders
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(100, 'Document', 12, 'bulgariaplast', 'prepareOrder', '', '', NOW(), NOW());

INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(100, 'Подготовка за печат на поръчка за акесоари', '', 'bg', NOW()),
(100, 'Print order', '', 'en', NOW());

# Set the plugin, header and content to the pattern
UPDATE patterns SET plugin=100 WHERE id=30;

# Add placeholder
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'form_type', 'Document', 'basic', 'patterns', ',30,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Цех', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Form Type', NULL, 'en');

######################################################################################
# 2023-06-20 - Updated setting for 'bulgariaplast_transport_schedule' report

# Updated setting for 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings`='doc_type_transport_request := 10\r\ndoc_var_delivery_date := date_delivery\r\ndoc_var_destination := delivery_destination_id\r\ndoc_var_city := delivery_cityid\r\ndoc_var_order_num := order_full_num\r\ndoc_var_vehicle := vehicle_id\r\ndoc_var_vehicle_name := vehicle_name\r\ndoc_var_driver := driver_id\r\ndoc_var_driver_name := driver_name\r\ndoc_var_comments := comments_address\r\ndoc_var_notes := order_note\r\ndoc_var_panel_file := file_panel\r\ndoc_var_module := module_num\r\ndoc_var_production_article := production_article_id\r\ndoc_var_req_num := num_req\r\ndoc_var_req_meters := meters_req\r\ndoc_var_req_transport_list := transport_document_id\r\ndoc_var_req_payment_amount := payment_amount\r\n\r\ndoc_type_transport_list := 9\r\ndoc_transport_list_new_status := closed_49\r\ndoc_var_trans_vehicle := vehicle_id\r\ndoc_var_trans_vehicle_name := vehicle_name\r\ndoc_var_trans_driver := driver_id\r\ndoc_var_trans_driver_name := driver_name\r\ndoc_var_trans_request := request_id\r\ndoc_var_trans_request_num := request_num\r\ndoc_var_trans_request_customer := customer_order_id\r\ndoc_var_trans_request_customer_name := customer_order\r\ndoc_var_trans_request_destination := delivery_destination_id\r\ndoc_var_trans_request_destination_name := delivery_destination_name\r\ndoc_var_trans_request_city := delivery_cityid\r\ndoc_var_trans_request_city_name := delivery_city\r\ndoc_var_trans_request_date := date_order\r\ndoc_var_trans_request_full_num := order_full_num\r\ndoc_var_trans_request_custom_num := order_custom_num\r\ndoc_var_trans_request_description := order_description\r\ndoc_var_trans_request_file := file_panel\r\ndoc_var_trans_request_note := order_note\r\ndoc_var_trans_payment_amount := payment_amount\r\n\r\ncustomers_types_filtered := 1,2,3,4\r\ncustomer_type_driver := 1\r\n\r\nnom_type_vehicle := 9\r\nnom_var_capacity_count := windows_number\r\nnom_var_capacity_volume := vol_windows\r\n\r\nnom_type_region := 20\r\nnom_type_settlement := 16' WHERE  `type`='bulgariaplast_transport_schedule';

######################################################################################
# 2023-07-28 - Added additinoal settings for the 'merge_orders' report

# Added additinoal settings for the 'merge_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\norder_3_statuses :=', '\r\norder_pay_left_to_pay := balance_payment\r\norder_pay_total := link_tatal_paid\norder_3_statuses :=')
WHERE `type`='merge_orders' AND settings NOT LIKE '%order_pay_left_to_pay%';

######################################################################################
# 2023-09-13 - Added additinoal settings for the 'bulgariaplast_transport_schedule' report

# Added additinoal settings for the 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ndoc_type_transport_list :=', '\r\ndoc_var_article_color := production_article_color\r\ndoc_var_module_glazing := module_glazing\r\ndoc_var_accessories := module_accessories\r\n\r\ndoc_type_order := 3\r\ndoc_type_order_sum_to_pay := balance_payment\r\n\r\ndoc_type_transport_list :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_article_color%';

######################################################################################
# 2023-09-19 - Added index for the `documents_cstm` table column `value` for optimization

# PRE-DEPLOYED # Added index for the `documents_cstm` table column `value` for optimization
#ALTER TABLE `documents_cstm` ADD INDEX `Index 3` (`value`(255));

######################################################################################
# 2023-09-20 - Added automation to validate the annulment of PKO
#            - Added automation to validate the annulment of BP

# Added automation to validate the annulment of PKO
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Анулиране на плащания (валидация)', 0, NULL, 1, 'finance', 'payments', 'before_action', 'PKO', 'orders_types := 3,12\r\ndoc_payment_id := link_pay_id', 'condition := \'[action]\' == \'annul\'', 'plugin := bulgariaplast\r\nmethod := validatePaymentAnnulment', 'cancel_action_on_fail := 1', 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%validatePaymentAnnulment%' AND `start_model_type`='PKO');

# Added automation to validate the annulment of BP
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Анулиране на плащания (валидация)', 0, NULL, 1, 'finance', 'payments', 'before_action', 'BP', 'orders_types := 3,12\r\ndoc_payment_id := link_pay_id', 'condition := \'[action]\' == \'annul\'', 'plugin := bulgariaplast\r\nmethod := validatePaymentAnnulment', 'cancel_action_on_fail := 1', 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%validatePaymentAnnulment%' AND `start_model_type`='BP');

######################################################################################
# 2023-09-20 - Added gt2 modification plugin

INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(48, 'Finance_Incomes_Reason', 0, 'gt2', 'prepareModifiedGT2', 'gt2_quantity := 2\r\ngt2_rows := 2\r\ngt2_total := 2\r\ngt2_total_vat := 2\r\ngt2_total_with_vat:= 2\r\ndecimal_separator := .\r\nthousands_separator :=  &nbsp;\r\n', '', NOW(), NOW()),
(49, 'Document', 0, 'gt2', 'prepareModifiedGT2', 'gt2_quantity := 2\r\ngt2_rows := 2\r\ngt2_total := 2\r\ngt2_total_vat := 2\r\ngt2_total_with_vat:= 2\r\ndecimal_separator := .\r\nthousands_separator :=  &nbsp;\r\n', '', NOW(), NOW()),
(50, 'Finance_Warehouses_Document', 0, 'gt2', 'prepareModifiedGT2', 'gt2_quantity := 2\r\ngt2_rows := 2\r\ngt2_total := 2\r\ngt2_total_vat := 2\r\ngt2_total_with_vat:= 2\r\ndecimal_separator := .\r\nthousands_separator :=  &nbsp;\r\n', '', NOW(), NOW());

INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(48, 'Подготовка на ГТ2 данни', 'Подготвят се данни за печат на модифицирана GT2', 'bg', NOW()),
(48, 'Preparation of GT2', 'Prepare data for printing of modified GT2', 'en', NOW()),
(49, 'Подготовка на ГТ2 данни', 'Подготвят се данни за печат на модифицирана GT2', 'bg', NOW()),
(49, 'Preparation of GT2', 'Prepare data for printing of modified GT2', 'en', NOW()),
(50, 'Подготовка на ГТ2 данни', 'Подготвят се данни за печат на модифицирана GT2', 'bg', NOW()),
(50, 'Preparation of GT2', 'Prepare data for printing of modified GT2', 'en', NOW());

UPDATE patterns SET plugin=48 WHERE id=6;

######################################################################################
# 2023-09-26 - Added additinoal settings for the 'bulgariaplast_transport_schedule' report

# Added additinoal settings for the 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ncustomers_types_filtered :=', '\r\ndoc_var_trans_request_handle := module_handle\r\ndoc_var_trans_request_cartridge := module_cartridge\r\n\r\ncustomers_types_filtered :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_trans_request_handle%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ndoc_type_order :=', '\r\ndoc_var_handle := module_handle\r\ndoc_var_cartridge := module_cartridge\r\n\r\ndoc_type_order :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_handle%';

######################################################################################
# 2023-10-13 - Added additinoal settings for the 'bulgariaplast_transport_schedule' report

# Added additinoal settings for the 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ncustomers_types_filtered :=', '\r\ndoc_var_trans_request_num_req := num_req\r\ndoc_var_trans_request_meters := meters_req\r\n\r\ncustomers_types_filtered :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_trans_request_num_req%';

######################################################################################
# 2023-10-16 - Added new report 'bulgariaplast_total_turnover_area'

# Added new report 'bulgariaplast_total_turnover_area'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (461, 'bulgariaplast_total_turnover_area', 'customers_types_included := 1,3,4\r\narticles_types_included := 6,8,11,23\r\ndocuments_types_included := 3,12\r\n\r\ndocument_type_order_siding := 3\r\ndocument_type_order_accessories := 12', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (461, 'Поръчки общи обороти/квадратури', NULL, NULL, 'bg'),
  (461, 'Тotal turnover/area', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '461', '1'),
  ('reports', '', 'export', '461', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '461';

######################################################################################
# 2023-10-31 - Added waybill pattern plugin

# Added waybill pattern plugin
INSERT IGNORE INTO `patterns_plugins` (id, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(104, 'Document', 12, 'bulgariaplast', 'prepareWaybill', '', '', NOW(), NOW());

INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(104, 'Подготовка за печат на транспортен лист', '', 'bg', NOW()),
(104, 'Print waybill', '', 'en', NOW());

# Set the plugin, header and content to the pattern
UPDATE patterns SET plugin=104 WHERE id=39;

# Add placeholder
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'transport_requests', 'Document', 'basic', 'patterns', ',39,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Транспортни поръчки', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Transport Requests', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'transport_totals', 'Document', 'basic', 'patterns', ',39,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Транспортни суми', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Transport Totals', NULL, 'en');

######################################################################################
# 2023-01-02 - Fixed waybill plugin (model_type)

# AFixed waybill plugin (model_type)
UPDATE patterns_plugins SET model_type=9 WHERE id=104;

######################################################################################
# 2023-11-09 - Updated settings for the 'bulgariaplast_total_turnover_area' report

# Updated settings for the 'bulgariaplast_total_turnover_area' report
UPDATE `reports` SET `settings` = 'customers_types_included := 1,3,4,2\r\narticles_types_included := 5,6,8,11,13,23\r\ndocuments_types_included := 3,12\r\n\r\ndocuments_substatuses_exclude := 24,46'
WHERE `type`='bulgariaplast_total_turnover_area' AND settings NOT LIKE '%documents_substatuses_exclude%';

######################################################################################
# 2023-12-01 - Added rest settings for all types of nomenenclatures
#            - Made code teres searchable field

# Added rest settings for all types of nomenenclatures
INSERT INTO `settings` (`id`, `section`, `name`, `value`) VALUES
(NULL, 'rest', 'filter_vars_nomenclatures', 'all');

# Made code teres searchable field
UPDATE _fields_meta SET searchable='text' WHERE name='code_teres';

######################################################################################
# 2023-12-12 - Added rest settings for departments

# Added rest settings for departments
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
(NULL, 'rest', 'filter_vars_departments', 'all');

######################################################################################
# 2023-12-15 - Added rest settings for contracts

# Added rest settings for contracts
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
(NULL, 'rest', 'filter_vars_contracts', 'all');

######################################################################################
# 2023-12-18 - Added rest settings for documents leave request and sick note
#            - Added rest settings for documents pay slip

# Added rest settings for documents leave request and sick note
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
(NULL, 'rest', 'filter_vars_documents_28', 'all'),
(NULL, 'rest', 'filter_vars_documents_29', 'all');

# Added rest settings for documents pay slip
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
(NULL, 'rest', 'filter_vars_documents_34', 'all');

######################################################################################
# 2023-12-19 - Added formulas for calculation of quantity

# Added formulas for calculation of quantity
UPDATE _fields_meta SET `source`=CONCAT(`source`, '\njavascript := function calcQuantity(el) {let crow = el.id.replace(/.*_(\\d+)/, ''$1'');gt2calc($(''price_'' + crow));quantity_inv = $(''free_field1_'' + crow).value ? parseFloat($(''free_field1_'' + crow).value) : 0;coef = $(''free_field2_'' + crow).value ? parseFloat($(''free_field2_'' + crow).value) : 0;$(''quantity_'' + crow).value = (Math.round(quantity_inv * coef * 100) / 100).toFixed(2);material_id = $(''article_alternative_deliverer_'' + crow).value ? parseFloat($(''article_alternative_deliverer_'' + crow).value) : 0;let quantity_per_material = 0;$$(''.article_alternative_deliverer'').each(function(el) {if (el.value == material_id) {crow = el.id.replace(/.*_(\\d+)/, ''$1'');quantity_per_material += parseFloat($(''quantity_'' + crow).value);}});$$(''.article_alternative_deliverer'').each(function(el) {if (el.value == material_id) {crow = el.id.replace(/.*_(\\d+)/, ''$1'');$(''free_field3_'' + crow).value = (Math.round(quantity_per_material * 100) / 100).toFixed(2);}});gt2calc($(''price_'' + crow));}')  WHERE `name`='group_table_2' AND `model`='Finance_Expenses_Reason' AND `model_type`=103 AND `source` NOT LIKE '%calcQuantity%';
UPDATE _fields_meta SET `source`=CONCAT(`source`, '\njs_method := onkeyup => calcQuantity(this);') WHERE `name` IN ('free_field1') AND `model`='Finance_Expenses_Reason' AND `model_type`=103 AND `source` NOT LIKE '%calcQuantity%';
UPDATE _fields_meta SET `source`=CONCAT(`source`, '\nautocomplete_execute_after := calcQuantity(this);') WHERE `name` IN ('article_name') AND `model`='Finance_Expenses_Reason' AND `model_type`=103 AND `source` NOT LIKE '%calcQuantity%';
UPDATE _fields_meta SET `source`=REPLACE(`source`, 'autocomplete_fill_options := $quantity => 1', '#autocomplete_fill_options := $quantity => 1') WHERE `name` IN ('article_name') AND `model`='Finance_Expenses_Reason' AND `model_type`=103 AND `source` NOT LIKE '%#autocomplete_fill_options := $quantity => 1%';
UPDATE _fields_meta SET `readonly`=1 WHERE `name` IN ('quantity') AND `model`='Finance_Expenses_Reason' AND `model_type`=103;
UPDATE `_fields_i18n` SET `content`='К-во за материал' WHERE `parent_id`=102372 AND `content_type`='label' AND `lang`='bg';

######################################################################################
# 2023-12-20 - Fixed autocomplete execute_after
#            - Updated formula for quantity

# Fixed autocomplete execute_after
UPDATE _fields_meta SET `source`=replace(`source`, 'autocomplete_execute_after := calcQuantity(this);', 'autocomplete_execute_after := let cQuant=function(a,d){calcQuantity($(a.field))};cQuant') WHERE `name` IN ('article_name') AND `model`='Finance_Expenses_Reason' AND `model_type`=103;

# Updated formula for quantity
UPDATE _fields_meta SET `source`=replace(`source`,
    "javascript := function calcQuantity(el) {let crow = el.id.replace(/.*_(\\d+)/, '$1');gt2calc($('price_' + crow));quantity_inv = $('free_field1_' + crow).value ? parseFloat($('free_field1_' + crow).value) : 0;coef = $('free_field2_' + crow).value ? parseFloat($('free_field2_' + crow).value) : 0;$('quantity_' + crow).value = (Math.round(quantity_inv * coef * 100) / 100).toFixed(2);material_id = $('article_alternative_deliverer_' + crow).value ? parseFloat($('article_alternative_deliverer_' + crow).value) : 0;let quantity_per_material = 0;$$('.article_alternative_deliverer').each(function(el) {if (el.value == material_id) {crow = el.id.replace(/.*_(\\d+)/, '$1');quantity_per_material += parseFloat($('quantity_' + crow).value);}});$$('.article_alternative_deliverer').each(function(el) {if (el.value == material_id) {crow = el.id.replace(/.*_(\\d+)/, '$1');$('free_field3_' + crow).value = (Math.round(quantity_per_material * 100) / 100).toFixed(2);}});gt2calc($('price_' + crow));}",
    "javascript := function calcQuantity(el) {let crow = el.id.replace(/.*_(\\d+)/, '$1');gt2calc($('price_' + crow));quantity_inv = $('free_field1_' + crow).value ? parseFloat($('free_field1_' + crow).value) : 0;coef = $('free_field2_' + crow).value ? parseFloat($('free_field2_' + crow).value) : 1;$('quantity_' + crow).value = (Math.round(quantity_inv * coef * 100) / 100).toFixed(2);material_id = $('article_alternative_deliverer_' + crow).value ? parseFloat($('article_alternative_deliverer_' + crow).value) : 1;let quantity_per_material = 0;$$('.article_alternative_deliverer').each(function(el) {if (el.value == material_id) {crow = el.id.replace(/.*_(\\d+)/, '$1');quantity_per_material += parseFloat($('quantity_' + crow).value);}});$$('.article_alternative_deliverer').each(function(el) {if (el.value == material_id) {crow = el.id.replace(/.*_(\\d+)/, '$1');$('free_field3_' + crow).value = (Math.round(quantity_per_material * 100) / 100).toFixed(2);}});gt2calc($('price_' + crow));}"
)
WHERE `name`='group_table_2' AND `model`='Finance_Expenses_Reason' AND `model_type`=103;

######################################################################################
# 2023-12-21 - Fixed autocomplete execute_after
#            - Updated formula for quantity

######################################################################################
# 2023-12-21 - Added automation to create incomes reason and an outgoing protocol from joinery order
#            - Added automation to create incomes reason and an outgoing protocol from accesory order
#            - Added new settings for last delivery price and average weighted delivery price in createIncomesReasonAndPpp automation

# Added automation to create incomes reason and an outgoing protocol from joinery order
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Създаване на КСС и ППП', 0, NULL, 1, 'documents', NULL, 'action', '3', 'use_updated_model := 1\r\n\r\nincomes_reason_type := 106\r\nincomes_reason_company := 1\r\nincomes_reason_office := 1\r\nincomes_reason_payment_type := bank\r\nincomes_reason_container_id := 1\r\nincomes_reason_vat_rate := 20\r\nincomes_reason_tag_missing_ppp := 51\r\n\r\ndocument_article_id := material_id\r\ndocument_article_name := material_name\r\ndocument_article_code := material_code\r\ndocument_currency :=\r\ndocument_article_quantity := material_quantity\r\ndocument_article_measure_name := material_measure\r\ndocument_price := material_price\r\ndocument_available_quantity :=\r\ndocument_discount_percentage :=\r\ndocument_surplus_percentage :=\r\n\r\nprotocol_warehouse_id := 1', 'condition := \'[action]\' == \'setstatus\'\r\ncondition := \'[prev_b_substatus]\' != \'20\'\r\ncondition := \'[b_substatus]\' == \'20\'', 'plugin := bulgariaplast\r\nmethod := createIncomesReasonAndPpp', NULL, 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND `start_model_type`='3' AND `module`='documents');

# Added automation to create incomes reason and an outgoing protocol from accesory order
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Създаване на КСС и ППП', 0, NULL, 1, 'documents', NULL, 'action', '12', 'use_updated_model := 1\r\n\r\nincomes_reason_type := 106\r\nincomes_reason_company := 1\r\nincomes_reason_office := 1\r\nincomes_reason_payment_type := bank\r\nincomes_reason_container_id := 1\r\nincomes_reason_vat_rate := 20\r\nincomes_reason_tag_missing_ppp := 51\r\n\r\ndocument_article_id := article_id\r\ndocument_article_name := article_name\r\ndocument_article_code := article_code\r\ndocument_currency := currency\r\ndocument_article_quantity := quantity\r\ndocument_article_measure_name := article_measure_name\r\ndocument_price := price\r\ndocument_available_quantity := available_quantity\r\ndocument_discount_percentage := discount_percentage\r\ndocument_surplus_percentage := surplus_percentage\r\n\r\nprotocol_warehouse_id := 1', 'condition := \'[action]\' == \'setstatus\'\r\ncondition := \'[prev_b_substatus]\' != \'42\'\r\ncondition := \'[b_substatus]\' == \'42\'', 'plugin := bulgariaplast\r\nmethod := createIncomesReasonAndPpp', NULL, 1, 1, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND `start_model_type`='12' AND `module`='documents');

# Added new settings for last delivery price and average weighted delivery price in createIncomesReasonAndPpp automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nprotocol_warehouse_id :=', '\r\ndocument_last_delivery_price := last_delivery_price\r\ndocument_average_weighted_delivery_price := average_weighted_delivery_price\r\n\r\nprotocol_warehouse_id :=') WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND `settings` NOT LIKE '%document_average_weighted_delivery_price%' AND `start_model_type`='12';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nprotocol_warehouse_id :=', '\r\ndocument_last_delivery_price := material_price\r\ndocument_average_weighted_delivery_price := material_average_price\r\n\r\nprotocol_warehouse_id :=') WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND `settings` NOT LIKE '%document_average_weighted_delivery_price%' AND `start_model_type`='3';

######################################################################################
# 2023-10-31 - Added placeholders for orders waybill pattern plugin

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'transport_orders', 'Document', 'basic', 'patterns', ',39,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Транспортни поръчки', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Transport Orders', NULL, 'en');

######################################################################################
# 2024-01-19 - Added additinoal settings for the 'bulgariaplast_transport_schedule' report

# Added additinoal settings for the 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ndoc_type_order :=', '\r\ndoc_var_handle_description := module_handle_desc\r\ndoc_var_cartridge_description := module_cartridge_desc\r\n\r\ndoc_type_order :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_handle_description%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ncustomers_types_filtered :=', '\r\ndoc_var_trans_request_handle_description := module_handle_desc\r\ndoc_var_trans_request_cartridge_description := module_cartridge_desc\r\n\r\ncustomers_types_filtered :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_trans_request_handle_description%';

######################################################################################
# 2024-02-19 - Added additinoal settings for the 'bulgariaplast_transport_schedule' report for substatuses to be ignored

# Added additinoal settings for the 'bulgariaplast_transport_schedule' report for substatuses to be ignored
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ndoc_type_order :=', '\r\n\r\nignore_substatuses := 90\r\n\r\ndoc_type_order :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%ignore_substatuses%';

######################################################################################
# 2024-02-23 - Added new report 'bulgariaplast_deliverers_glazing_orders'

# Added new report 'bulgariaplast_deliverers_glazing_orders'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (468, 'bulgariaplast_deliverers_glazing_orders', 'documents_orders := 3,12\r\nignore_substatuses := 24,46\r\ndelivery_date_max_period_months := 3\r\n\r\ndocuments_glazing := glazing_id\r\ndocuments_supplier := glazing_supplier_id\r\ndocuments_glazing_length := glazing_l\r\ndocuments_glazing_height := glazing_h\r\ndocuments_glazing_count := glazing_num\r\ndocuments_glazing_area := glazing_m2\r\ndocuments_file := order_file_glass\r\n\r\ntag_sent_to_deliverer := 52\r\n\r\ncustomer_deliverer_type :=\r\ncustomers_types_filter := 1,3,4\r\n\r\nemail_id := 1008', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (468, 'Стъклопакети към доставчици', NULL, NULL, 'bg'),
  (468, 'Deliverers glazing orders', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '468', '1'),
  ('reports', '', 'export', '468', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '468';

######################################################################################
# 2024-02-27 - Rename setting in the 'bulgariaplast_deliverers_glazing_orders' report

# Added additinoal settings for the 'bulgariaplast_transport_schedule' report for substatuses to be ignored
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndelivery_date_max_period_months :=', '\r\norder_date_max_period_months :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings LIKE '%delivery_date_max_period_months%';

######################################################################################
# 2024-03-05 - Added new report 'bulgariaplast_order_materials_value'

# Added new report 'bulgariaplast_order_materials_value'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (469, 'bulgariaplast_order_materials_value', 'documents_orders_gt2 := 3\r\ndocuments_orders_group := 12\r\nignore_substatuses := 24,46\r\ndelivery_date_max_period_months := 3\r\ncustomer_types := 1,3,4\r\narticles_types := 6,8,11\r\n\r\nincomes_reasons_orders := 101,111\r\n\r\ndocument_var_material := material_id\r\ndocument_var_quantity := material_quantity\r\ndocument_var_measure := material_measure\r\ndocument_var_price := material_price\r\ndocument_var_average_price := material_average_price\r\ndocument_var_subtotal := material_subtotal\r\ndocument_var_total := material_total\r\ndocument_var_total_with_vat := total_with_vat\r\ndocument_var_difference := order_difference', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (469, 'Стойност на материали по поръчки', NULL, NULL, 'bg'),
  (469, 'Order materials value', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '469', '1'),
  ('reports', '', 'export', '469', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '469';

######################################################################################
# 2024-03-08 - Renamed setting for 'createIncomesReasonAndPpp' report
#            - Added new settings for alternative article in 'createIncomesReasonAndPpp' report

# Renamed setting for 'createIncomesReasonAndPpp' report
UPDATE `automations` SET `settings` = REPLACE(`settings`, '\r\nincomes_reason_tag_missing_ppp :=', '\r\nincomes_reason_tag_missing_partial_ppp :=')
WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND settings LIKE '%incomes_reason_tag_missing_ppp%';

# Added new settings for alternative article in 'createIncomesReasonAndPpp' report
UPDATE `automations` SET `settings` = REPLACE(`settings`, '\r\ndocument_currency :=', '\r\ndocument_article_alternative_id := article_alternative_deliverer\r\ndocument_article_alternative_name := article_alternative_deliverer_name\r\ndocument_article_alternative_code := free_field2\r\ndocument_currency :=')
WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND settings NOT LIKE '%document_article_alternative_id%' AND `start_model_type`=12;
UPDATE `automations` SET `settings` = REPLACE(`settings`, '\r\ndocument_currency :=', '\r\ndocument_article_alternative_id := material_warehouse_id\r\ndocument_article_alternative_name := material_warehouse_name\r\ndocument_article_alternative_code := material_warehouse_code\r\ndocument_currency :=')
WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND settings NOT LIKE '%document_article_alternative_id%' AND `start_model_type`=3;

######################################################################################
# 2024-03-12 - Rename setting in 'bulgariaplast_deliverers_glazing_orders' report
#            - Added additional settings for 'bulgariaplast_deliverers_glazing_orders' report

# Rename setting in 'bulgariaplast_deliverers_glazing_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\norder_date_max_period_months :=', '\r\nglazing_date_max_period_months :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings LIKE '%order_date_max_period_months%';

# Added additional settings for 'bulgariaplast_deliverers_glazing_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndocuments_file :=', '\r\ndocuments_glazing_date := date_glazing\r\ndocuments_glazing_module := glazing_modul\r\ndocuments_glazing_cell := glazing_cell\r\ndocuments_file :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_glazing_date%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\nemail_id :=', '\r\n\r\nnomenclature_subtype_var := form_type\r\nnomenclature_subtype_id := 5\r\n\r\nemail_id := ')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%nomenclature_subtype_var%';

######################################################################################
# 2024-03-13 - Added additinoal settings for the 'bulgariaplast_transport_schedule' report

# Added additinoal settings for the 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ndoc_type_transport_list :=', '\r\ndoc_type_order_supplier := glazing_supplier_id\r\n\r\ndoc_type_transport_list :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_type_order_supplier%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndoc_var_trans_request_file :=', '\r\ndoc_var_trans_request_order_id := order_id\r\ndoc_var_trans_request_supplier_id := glazing_supplier_id\r\ndoc_var_trans_request_supplier_name := glazing_supplier\r\ndoc_var_trans_request_file :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_trans_request_order_id%';

######################################################################################
# 2024-03-19 - Added automation to import worked time

# Added automation to import worked time
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
 SELECT 'Импортиране на работено време.', 0, NULL, 0, 'documents', NULL, 'crontab', '0', 'start_time := 08:00\r\nstart_before := 09:00\r\nsend_to_email := <EMAIL>\r\n\r\n# Номера на типа документ за работено време\r\ndocument_type := 33\r\n\r\n# За колко дни назад да е записа. В случай на нощни смени и среднощно изпълнение на АД-то ще са нужни 2 или повече дни.\r\nworktime_days_before := 1\r\n\r\n# Идентификатор на служител. Дали да се засичат данните по ucn или code (полето card_id).\r\nidentifier := tag\r\n\r\n# адресът към, който ще се прави заявката.\r\napiAddress := http://**************:6000\r\napiUsername := bgservice\r\napiPassword := bGservic3\r\n\r\n# testDate се ползва само когато се правят тестове. Трабва да е закоментирана или изтрита при нормалната работа на АД.\r\n#testDate := 2018-08-22', 'condition := 1', 'plugin := trackaccess\r\nmethod := updateWorkedTime', NULL, 0, 0, 1
  WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateWorkedTime%');

######################################################################################
# 2024-03-29 - Added additional settings for 'bulgariaplast_total_turnover_area' report

# Added additional settings for 'bulgariaplast_total_turnover_area' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\noffices_articles_included := 18446,18447,26961')
WHERE `type`='bulgariaplast_total_turnover_area' AND settings NOT LIKE '%offices_articles_included%';

######################################################################################
# 2024-04-04 - Added additional settings for 'bulgariaplast_total_turnover_area' report

# Added additional settings for 'bulgariaplast_total_turnover_area' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\noffices_articles_included := 18446,18447,26961', '\r\noffices_articles_tag_included := 50')
WHERE `type`='bulgariaplast_total_turnover_area' AND settings NOT LIKE '%offices_articles_tag_included%';

######################################################################################
# 2024-04-25 - Added additional settings for 'bulgariaplast_deliverers_glazing_orders' report

# Added additional settings for 'bulgariaplast_deliverers_glazing_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndocuments_file :=', '\r\ndocuments_glazing_note := glazing_note\r\ndocuments_file :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_glazing_note%';

######################################################################################
# 2024-05-15 - Added automation to automated issueing of handovers when all needed quantities are available

# Added automation to automated issueing of handovers when all needed quantities are available
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
 SELECT 'Доизписване на материали', 0, NULL, 1, 'finance', 'incomes_reasons', 'crontab', '106', 'start_time := 08:00\r\nstart_before := 10:00\r\n\r\ntag_no_partial_ppp := 51\r\nprotocol_warehouse_id := 1\r\n\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := bulgariaplast\r\nmethod := crontabCreatePpp', NULL, 0, 0, 1
   WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%crontabCreatePpp%');

######################################################################################
# 2024-07-03 - Added additional settings for 'bulgariaplast_transport_schedule' report

# Added additional settings for 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndoc_var_delivery_date :=', '\r\ndoc_type_transport_status_unlocked := opened_0\r\ndoc_var_delivery_date :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_type_transport_status_unlocked%';

######################################################################################
# 2024-07-30 - Added additional settings for 'bulgariaplast_transport_schedule' report
#            - Modified the wayBill pattern plugin

# Added additional settings for 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndocuments_file :=', '\r\ndocuments_glazing_curved := glazing_curved\r\ndocuments_file :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%documents_glazing_curved%';

# Modified the wayBill pattern plugin
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, 'date_order, ', '') WHERE `name`='transport_group' AND `model_type`=9 AND `model`='Document';
UPDATE `patterns_i18n` SET `content`='<p>[transport_requests]</p>\r\n\r\n<div class="break">[system_pagebreak]</div>\r\n\r\n<p>[transport_totals]</p>\r\n' WHERE parent_id=39 and lang='bg';

######################################################################################
# 2024-07-31 - Fixed query for adding the new setting for 'bulgariaplast_deliverers_glazing_orders' report
#            - Added new settings in 'bulgariaplast_deliverers_glazing_orders' report for customer tag for deliverers

# Fixed query for adding the new setting for 'bulgariaplast_deliverers_glazing_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndocuments_file :=', '\r\ndocuments_glazing_curved := glazing_curved\r\ndocuments_file :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_glazing_curved%';

# Added new settings in 'bulgariaplast_deliverers_glazing_orders' report for customer tag for deliverers
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ncustomer_deliverer_type :=', '\r\ntag_delivering_glazings := 38\r\n\r\ncustomer_deliverer_type :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%tag_delivering_glazings%';

######################################################################################
# 2024-10-07 - Added additional settings for 'bulgariaplast_deliverers_glazing_orders' report
#            - Added new placeholder to be used in 'bulgariaplast_deliverers_glazing_orders' report

# Added additional settings for 'bulgariaplast_deliverers_glazing_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\nignore_substatuses :=', '\r\ndocuments_type_accessories_order := 12\r\nignore_substatuses :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_type_accessories_order%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndocuments_glazing_curved :=', '\r\ndocuments_glazing_module_note := glazing_note2\r\ndocuments_glazing_curved :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_glazing_module_note%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\ndocuments_glazing_length :=', '\r\ndocuments_delivery_city := delivery_cityid\r\ndocuments_glazing_length :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_delivery_city%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\nnomenclature_subtype_var :=', '\r\nnomenclatures_types_city := 16\r\n\r\nnomenclature_subtype_var :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%nomenclatures_types_city%';

# Added new placeholder to be used in 'bulgariaplast_deliverers_glazing_orders' report
INSERT IGNORE INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  ('report_order_nums', 'Report', 'send', 'all', ',468,', 'report_order_nums', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Списък с номера на изпратени поръчки', NULL, 'bg'),
  (LAST_INSERT_ID(), 'List with sent orders nums', NULL, 'en');

######################################################################################
# 2024-10-09 - Added additional settings for 'bulgariaplast_transport_schedule' report

# Added additional settings for 'bulgariaplast_transport_schedule' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ncustomers_types_filtered :=', '\r\ndoc_var_trans_request_address := comments_address\r\ndoc_var_trans_request_target_city := city_id\r\ndoc_var_trans_request_target_city_name := city_name\r\n\r\ncustomers_types_filtered :=')
WHERE `type`='bulgariaplast_transport_schedule' AND settings NOT LIKE '%doc_var_trans_request_target_city%';

######################################################################################
# 2024-10-21 - Added rest settings for mzoom (hrZoom)

# Added rest settings for mzoom regarding the taskman and hr modules
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_dashlets', 'all'),
  ('rest', 'filter_vars_documents_38', 'all'),
  ('rest', 'filter_vars_users', 'id,username,firstname,lastname,employee,rights,role,settings');

# Updated allowed user agents
UPDATE `settings` SET `value` = CONCAT(`value`, ',mzoom-bulgariaplast')
WHERE `section` = 'rest' AND `name` = 'allowed_rest_user_agents';

######################################################################################
# 2024-10-24 - Added additional settings for 'bulgariaplast_order_materials_value' report
#            - Added additional settings for 'bulgariaplast_total_turnover_area' report

# Added additional settings for 'bulgariaplast_order_materials_value' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\nfreeze_table_headers :=', '\r\ndocument_var_glazing := glazing_id\r\ndocument_var_glazing_code := glazing_code\r\ndocument_var_glazing_quantity := glazing_m2\r\ndocument_var_glazing_measure := glazing_measure\r\ndocument_var_glazing_price := glazing_price\r\ndocument_var_glazing_average_price := glazing_average_price\r\ndocument_var_glazing_total := glazing_total\r\n\r\nfreeze_table_headers :=')
WHERE `type`='bulgariaplast_order_materials_value' AND settings NOT LIKE '%document_var_glazing%';
UPDATE `reports` SET `settings` = CONCAT('export_over := 5000\r\nskip_session_filters := 1\r\n\r\n', `settings`)
WHERE `type`='bulgariaplast_order_materials_value' AND settings NOT LIKE '%export_over%';

# Added additional settings for 'bulgariaplast_total_turnover_area' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ndocument_var_delivery := delivery\r\nnom_type_delivery := 21', `settings`)
WHERE `type`='bulgariaplast_total_turnover_area' AND settings NOT LIKE '%document_var_delivery%';

######################################################################################
# 2024-10-25 - Updated settings for 'bulgariaplast_total_turnover_area' report

# Updated settings for 'bulgariaplast_total_turnover_area' report
UPDATE `reports` SET `settings` = 'customers_types_included := 1,3,4,2\r\narticles_types_included := 2,5,6,8,11,13,23\r\ndocuments_types_included := 3,12\r\n\r\ndocuments_substatuses_exclude := 24,46\r\n\r\noffices_articles_tag_included := 50\r\n\r\ndocument_var_delivery := delivery\r\nnom_type_delivery := 21'
WHERE `type`='bulgariaplast_total_turnover_area';

######################################################################################
# 2024-10-29 - Added additinoal settings for the 'merge_orders' report

# Added additinoal settings for the 'merge_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\r\n\r\ncustomer_invoice_to :=', '\r\ntag_ignore_order := 69\r\n\r\ncustomer_invoice_to :=')
WHERE `type`='merge_orders' AND `settings` NOT LIKE '%tag_ignore_order%';

########################################################################
# 2024-11-01 - Automation createIncomesReasonAndPpp is made to be able to be executed more than once
#            - Added new automation for deactivating kss and ppp when order is unlocked
#            - Added new setting for glazings for createIncomesReasonAndPpp automation

# Automation createIncomesReasonAndPpp is made to be able to be executed more than once
UPDATE `automations` SET `nums`=0
WHERE method LIKE '%bulgariaplast%' AND method LIKE '%createIncomesReasonAndPpp%' AND `nums`=1;

# Added new automation for deactivating kss and ppp when order is unlocked
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Отключване на поръчка за дограма за редакция', 0, NULL, 1, 'documents', '', 'action', '3', 'fin_type_kss := 106', 'condition := \'[prev_b_substatus]\' == \'20\'\r\ncondition := in_array(\'[b_substatus]\', array(\'18\',\'19\',\'24\',\'59\'))', 'plugin := bulgariaplast\r\nmethod := unlockOrderForEdit', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%unlockOrderForEdit%' AND `start_model_type`=3);
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Отключване на поръчки за аксесоари за редакция', 0, NULL, 1, 'documents', '', 'action', '12', 'fin_type_kss := 106', 'condition := \'[prev_b_substatus]\' == \'42\'\r\ncondition := in_array(\'[b_substatus]\', array(\'40\',\'41\',\'63\',\'46\'))', 'plugin := bulgariaplast\r\nmethod := unlockOrderForEdit', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%unlockOrderForEdit%' AND `start_model_type`=12);

# Added new setting for glazings for createIncomesReasonAndPpp automation
UPDATE `automations` SET `settings` = REPLACE(`settings`, '\r\n\r\nprotocol_warehouse_id :=', '\r\n\r\ndocument_warehouse_glazing_id := glazing_material_warehouse_id\r\ndocument_warehouse_glazing_code := glazing_material_warehouse_code\r\ndocument_warehouse_glazing_name := glazing_material_warehouse_name\r\ndocument_glazing_id := glazing_id\r\ndocument_glazing_code := glazing_code\r\ndocument_glazing_name := glazing_name\r\ndocument_glazing_quantity := glazing_m2\r\ndocument_glazing_price := glazing_price\r\ndocument_glazing_last_delivery_price := glazing_price\r\ndocument_glazing_average_weighted_delivery_price := average_weighted_delivery_price\r\nnom_var_measure := measure_name\r\n\r\nprotocol_warehouse_id :=')
WHERE `method` LIKE '%createIncomesReasonAndPpp%' AND settings NOT LIKE '%document_warehouse_glazing_id%';

######################################################################################
# 2024-11-18 - Added new report 'bulgariaplast_emails_order_owed_dues'

# Added new report 'bulgariaplast_emails_order_owed_dues'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (474, 'bulgariaplast_emails_order_owed_dues', 'customers_types := 1,3,4\r\ncustomers_representatives := 2\r\ndocuments_orders := 3,12\r\ndocument_owed_dues_notification := 35\r\nignore_substatuses := 24,46\r\n\r\nbalance_threshold := 0.01\r\n\r\ndocuments_balance_var := balance_payment', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (474, 'Писма за дължими суми по поръчки', NULL, NULL, 'bg'),
  (474, 'Emails for orders owed dues', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '474', '1'),
  ('reports', '', 'export', '474', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '474';

######################################################################################
# 2024-12-04 - Added new report 'bulgariaplast_salary_calculation'

# Added new report 'bulgariaplast_salary_calculation'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (476, 'bulgariaplast_salary_calculation', 'previous_months := 6\r\n\r\nfilters_show_department := 1\r\nfilters_show_office := 1\r\n\r\nemployee_var_office := office_id\r\nemployee_var_salary_month := salary_month\r\nemployee_var_salary_day := salary_day\r\nemployee_var_bonus_per_day := bonus_day\r\nemployee_var_bonus_per_month := bonus_month\r\nemployee_var_bonus_full_month := bonus_full\r\n\r\ncontract_working_contract_type := 1\r\n\r\ndoc_days_off_type := 28\r\ndoc_days_off_var_day_start := plr_leave_start_date\r\ndoc_days_off_var_day_end := plr_leave_finish_date\r\ndoc_days_off_var_type := plr_leave_type\r\ndoc_days_off_type_paid := paid,other,blood,school\r\ndoc_days_off_type_unpaid := unpaid\r\ndoc_days_off_statuses_to_include := closed_83\r\n\r\ndoc_sickenss_type := 29\r\ndoc_sickenss_var_day_start := shospital_time\r\ndoc_sickenss_var_day_end := fhospital_time\r\ndoc_sickenss_statuses_to_include :=\r\n\r\ndoc_working_hours_type := 33\r\ndoc_working_hours_employee := employee_id\r\ndoc_working_hours_working_time := working_hours\r\ndoc_working_hours_overtime := outside_working_hours\r\n\r\ndoc_salary_document_type := 34\r\ndoc_salary_year := salary_year\r\ndoc_salary_month := salary_month\r\ndoc_salary_bank_payment := salary_bank\r\ndoc_salary_sick_payment := sick_leave_amount\r\ndoc_salary_worked_hours := Hours_worked\r\ndoc_salary_worked_days := days_worked\r\ndoc_salary_daily_payment := daily_remuneration\r\ndoc_salary_overtime := extra_hours\r\ndoc_salary_overtime_payment := overtime_pay\r\ndoc_salary_worked_days_payment := remuneration_days_worked\r\ndoc_salary_sick_payment := paid_leave_amount\r\ndoc_salary_work_trip_days := business_trips_days\r\ndoc_salary_work_trip_sum := business_trip_amount\r\ndoc_salary_deliveries_wo_failures := deliveries_without_shortages_num\r\ndoc_salary_deliveries_wo_failures_sum := deliveries_without_shortages_amount\r\ndoc_salary_bonus := bonus_salary\r\ndoc_salary_bonus_full := bonus_whole_month\r\ndoc_salary_bonus_worked := bonus_production\r\ndoc_salary_compensation := benefits\r\ndoc_salary_total_positive := total_amount_month\r\ndoc_salary_deliveries_w_failures := missing_deliveries_num\r\ndoc_salary_deliveries_w_failures_sum := missing_deliveries_amount\r\ndoc_salary_penalties := fine\r\ndoc_salary_loans := loans\r\ndoc_salary_penalties_cat := vehicle_registration_card\r\ndoc_salary_advance := advance_payment\r\ndoc_salary_total_negative := total_deductions\r\ndoc_salary_total := amount_received\r\n\r\ndoc_transport_sheet_type := 9\r\ndoc_transport_sheet_tag_deliveries_wo_failures := 57\r\ndoc_transport_sheet_tag_deliveries_w_failures := 58\r\ndoc_transport_sheet_driver := driver_id\r\n\r\ndoc_salary_extra_sums_type := 46\r\ndoc_salary_extra_sums_worktrip_day := trip_day\r\ndoc_salary_extra_sums_worktrip_overnight := trip_overnight\r\ndoc_salary_extra_sums_delivery_wo_failures := delivery_ok\r\ndoc_salary_extra_sums_delivery_w_failures := delivery_problem\r\n\r\ndoc_worktrip_type := 32\r\ndoc_worktrip_start_date := trip_time_from\r\ndoc_worktrip_end_date := trip_time_to\r\ndoc_worktrip_employee := trip_employee_id\r\n\r\ndoc_bonus_type := 36\r\ndoc_bonus_amount := amount_bonus\r\n\r\ndoc_compensations_type := 27\r\ndoc_compensations_sum := compensation_amount\r\n\r\ndoc_penalties_type := 37\r\ndoc_penalties_sum := amount_fine\r\n\r\ndoc_loans_type := 21\r\ndoc_loans_month := advance_month\r\ndoc_loans_year := advance_year\r\ndoc_loans_sum := advance_amount\r\n\r\nreason_expense_type := 102\r\nnom_penalty_cat := 18821\r\nnom_advance := 28630\r\n\r\nabsence_type_days_off_paid := ПО\r\nabsence_type_days_off_unpaid := НО\r\nabsence_type_sickness := Б\r\nabsence_type_worktrip := К', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (476, 'Изчисление на заплати', NULL, NULL, 'bg'),
  (476, 'Salary calculation', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '476', '1'),
  ('reports', '', 'export', '476', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '476';

######################################################################################
# 2024-12-06 - Updated settings for REST

# Added groups to user filter vars.
UPDATE `settings`
SET `value` = CONCAT(`value`, ',groups')
WHERE `section` = 'rest' AND `name` = 'filter_vars_users';

######################################################################################
# 2024-12-13 - Added new settings for `bulgariaplast_salary_calculation` and replaced settings with correct values

# Added new settings for `bulgariaplast_salary_calculation` and replaced settings with correct values
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'doc_salary_sick_payment := paid_leave_amount', 'doc_salary_paid_leave := paid_leave_amount') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` LIKE '%doc_salary_sick_payment := paid_leave_amount%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndoc_worktrip_start_date :=', '\r\ndoc_worktrip_status := 85\r\ndoc_worktrip_start_date :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_worktrip_status%';

######################################################################################
# 2024-12-19 - Added new report 'hr_employees_free_days_available'

# Added new report 'hr_employees_free_days_available'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (477, 'hr_employees_free_days_available', 'powerroles :=\r\n\r\nshow_employees_filter := 1\r\nshow_date_filter := 1\r\n\r\nsearch_employees_by := users\r\nemployee_responsible_for_department :=', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (477, 'Оставащи дни отпуск, право на отпуск', NULL, NULL, 'bg'),
  (477, 'Free days left/available', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '477', '1'),
  ('reports', '', 'export', '477', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '477';

INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_department', 'Отдел'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_filter_customer', 'Служител'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_to_date', 'Към дата'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_employee', 'Служител'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_company_period_to_work', 'Работи във фирмата от-до'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_left_days_from_previous_year', 'Оставащи дни от предходни години'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_left_days_from_current_year', 'Оставащи дни текуща година'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_left_days_to_date', 'Отпуск към %s'),
  ('reports', 'hr_employees_free_days_available', 'bg', 'reports_requested_days', 'Заявени дни');
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_department', 'Department'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_filter_customer', 'Employee'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_to_date', 'To date'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_employee', 'Employee'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_company_period_to_work', 'Work in the company from-to'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_left_days_from_previous_year', 'Free days left from previous years'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_left_days_from_current_year', 'Free days left from current year'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_left_days_to_date', 'Free days as of %s'),
  ('reports', 'hr_employees_free_days_available', 'en', 'reports_requested_days', 'Requested days');

######################################################################################
# 2024-12-20 - Upgraded settings for `bulgariaplast_salary_calculation` report
#            - The labels for the `bulgariaplast_salary_calculation` report are transferred to the database

# Upgraded settings for `bulgariaplast_salary_calculation` report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndoc_sickenss_statuses_to_include :=', '\r\ndoc_sickenss_statuses_to_exclude := closed_82') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` LIKE '%doc_sickenss_statuses_to_include%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndoc_transport_sheet_type :=', '\r\ndoc_salary_statuses_to_exclude := closed_0\r\n\r\ndoc_transport_sheet_type :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_statuses_to_exclude%';

# The labels for the `bulgariaplast_salary_calculation` report are transferred to the database
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_month_year', 'За период'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_department', 'Отдел'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_office', 'Офис'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_employee', 'Служител'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_three_names', 'Имена'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_month_days', 'ДНИ НА МЕСЕЦА'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_days_off_paid', 'ПО'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_days_off_unpaid', 'НО'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_sick_leave', 'Б'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_worked_hours', 'Отработени часове'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_worked_days', 'Отработени дни'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_daily_payment', 'Дневно възнаграждение'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_overtime', 'Извънредни часове'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_overtime_payment', 'Възнагр. извънредни часове'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_overtime_days', 'Възнагр. отработени дни'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_sickness_payment', 'Болнични сума'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_paid_days_off', 'Платен отпуск (сума)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_working_vacation_days', 'Командировки (бр.дни)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_working_vacation_sum', 'Командировка (сума)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_deliveries_wo_problems_count', 'Доставки без липси (брой)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_deliveries_wo_problems_sum', 'Доставки без липси (сума)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_bonus', 'Бонус (заплата)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_bonus_full_month', 'Бонус (цял месец)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_bonus_working', 'Бонус (заработка)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_compensation', 'Обезщетения'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_total_positive', 'Общо възнаг. за месеца'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_deliveries_w_problems_count', 'Доставки с липси (брой)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_deliveries_w_problems_sum', 'Доставки с проблем (сума)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_penalties', 'Глоба'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_loans', 'Заеми'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_penalties_cat', 'Фиш КАТ'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_advance', 'Аванс'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_total_negative', 'Общо удръжки'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_bank_payment', 'Заплата по банка'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_total', 'СУМА за получаване'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_legend', 'Легенда'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_legend_days_off_paid', 'Редовен отпуск'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_legend_days_off_unpaid', 'Неплатен отпуск'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_legend_sickness', 'Отпуск по болест'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_legend_worktrip', 'Командировка'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'report_complete_in_salary', 'Попълни във фиш за заплата'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'message_reports_salary_document_updated_successfully', 'Успешно обновяване на фишовете за заплати на %s!'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'error_reports_salary_document_updated_failed', 'Обновяването на фишовете за заплати на %s пропадна! Моля, свържете се с администратор на nZoom!'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'error_reports_select_required_filters', 'Моля, попълнете задължителните филтри!');
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_month_year', 'Period'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_department', 'Department'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_office', 'Office'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_employee', 'Employee'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_three_names', 'Names'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_month_days', 'DAYS OF MONTH'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_days_off_paid', 'DOP'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_days_off_unpaid', 'DOU'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_sick_leave', 'S'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_worked_hours', 'Worked hours'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_worked_days', 'Worked days'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_daily_payment', 'Daily payment'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_overtime', 'Overtime'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_overtime_payment', 'Overtime payment'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_overtime_days', 'Payment overtime'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_sickness_payment', 'Sick leave payment'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_paid_days_off', 'Days off (sum)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_working_vacation_days', 'Work trip (count)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_working_vacation_sum', 'Work trip (sum)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_deliveries_wo_problems_count', 'Deliveries w/o problems (count)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_deliveries_wo_problems_sum', 'eliveries w/o problems (sum)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_bonus', 'Bonus (monthly payment)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_bonus_full_month', 'Bonus (full month)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_bonus_working', 'Bonus (worked)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_compensation', 'Compensation'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_total_positive', 'Total monthly payment'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_deliveries_w_problems_count', 'Доставки с липси (брой)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_deliveries_w_problems_sum', 'Доставки с проблем (сума)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_penalties', 'Penalties'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_loans', 'Loans'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_penalties_cat', 'Fines'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_advance', 'Advance'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_total_negative', 'Total holdover'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_bank_payment', 'Bank payment'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_total', 'TOTAL'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_legend', 'Legend'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_legend_days_off_paid', 'Days off (paid)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_legend_days_off_unpaid', 'Days off (unpaid)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_legend_sickness', 'Sick leave'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_legend_worktrip', 'Work trip'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'report_complete_in_salary', 'Complete in pay check'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'message_reports_salary_document_updated_successfully', 'Successfully updated pay checks for %s!'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'error_reports_salary_document_updated_failed', 'Updating pay checks for %s failed! Please contact nZoom support!'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'error_reports_select_required_filters', 'Please, complete required filters!');

######################################################################################
# 2025-01-09 - Added new automation for track_access
#

# Added new automation for creating an attendance document by getting track_access data directly from db instead of API
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Импортиране на работено време през база данни', 0, NULL, 0, 'documents', NULL, 'crontab', '0', '#start_time := 07:00\r\n#start_before := 09:00\r\n#send_to_email := <EMAIL>, <EMAIL>\r\n\r\n# Номера на типа документ за работено време\r\ndocument_type := 33\r\n\r\n# За колко дни назад да е записа. В случай на нощни смени и среднощно изпълнение на АД-то ще са нужни 2 или повече дни.\r\nworktime_days_before := 1\r\n\r\n# Mode to run the automation in\r\nmode := test\r\n\r\n# адресът към, който ще се прави заявката.\r\ntrackacc_type := pgsql \r\ntrackacc_host := **************\r\ntrackacc_port := 54321\r\ntrackacc_user := bgplast\r\ntrackacc_pass := v7t8FW+qoBSiT5Pjql/QGbh6j0PEzoBj\r\ntrackacc_name := track_access\r\n\r\n# test mode data\r\ndiag_user := diagnzoom\r\ndiag_pass := 077r9nGEBWzVNAA+e8kz5g==\r\ntest_pass := L15ItyLl0SYcQSAbvOCoZBmX5bavmGw2\r\n\r\n\r\n# testDate се ползва само когато се правят тестове. Трабва да е закоментирана или изтрита при нормалната работа на АД.\r\ntestDate := 2024-12-06', 'condition := 1', 'plugin := trackaccess\r\nmethod := updateWorkedTimeFromDb', NULL, 0, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateWorkedTimeFromDb%' AND `start_model_type`=0 AND `automation_type`='crontab');

######################################################################################
# 2025-01-30 - Added more rest settings regarding notifications
#

# Extended rest settings to provide notification functionality
INSERT INTO `settings` (`section`, `name`, `value`)
VALUES
 ('rest', 'filter_vars_emails', 'all') AS `new_settings`
 ON DUPLICATE KEY UPDATE `value` = `new_settings`.value;

####################################################################################
# 2025-01-30 - Added new automation

# Adding automation to clear matching notification tokens
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Зачистване на съвпадащи токъни за нотификации', 0, NULL, 1, 'customers', '', 'action', '1', 'notifTokenVar := notification_user_token\r\n', 'condition := \'[action]\' == \'edit\' && \'[a_notification_user_token]\' != \'\'', 'plugin := appNotifTokens\r\nmethod := clearDuplicates', NULL, 1, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%clearDuplicates%' AND `start_model_type` = 1 AND `module` = 'customers');

######################################################################################
# 2025-03-06 - Added new automation for track_access
#

# Added new automation for resyncing the track access DB
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Ресинхронизация на база данни track access', 0, NULL, 0, 'documents', NULL, 'crontab', '0', '#start_time := 07:00\r\n#start_before := 09:00\r\n#send_to_email := <EMAIL>, <EMAIL>\r\n\r\n# Mode to run the automation in\r\nmode := test\r\n\r\n# адресът към, който ще се прави заявката.\r\ntrackacc_type := pgsql \r\ntrackacc_host := **************\r\ntrackacc_port := 54321\r\ntrackacc_user := bgplast\r\ntrackacc_pass := v7t8FW+qoBSiT5Pjql/QGbh6j0PEzoBj\r\ntrackacc_name := track_access\r\n\r\n# test mode data\r\ndiag_user := diagnzoom\r\ndiag_pass := 077r9nGEBWzVNAA+e8kz5g==\r\ntest_pass := L15ItyLl0SYcQSAbvOCoZBmX5bavmGw2\r\n\r\n\r\n# resyncStart обозначава началото на ресинк периода. Задължителна.\r\nresyncStart := 2025-02-01\r\n# resynceEnd обозначава края на ресинк периода. Незадължително. Взима се resyncStart ако не е подадена.\r\n# resyncEnd := 2025-02-28', 'condition := 1', 'plugin := trackaccess\r\nmethod := resyncWorkTimeDb', NULL, 0, 0, 0
  WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%resyncWorkTimeDb%' AND `start_model_type`=0 AND `automation_type`='crontab');

######################################################################################
# 2025-03-17 - Added new settings for 'bulgariaplast_salary_calculation' report
#            - Updated and added labels for 'bulgariaplast_salary_calculation' report

# Added new settings for 'bulgariaplast_salary_calculation' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncontract_working_contract_type :=', '\r\nemployee_var_bonus_full_saturday := bonus_full_saturday\r\nemployee_var_bonus_transport := bonus_transport\r\nemployee_var_overtime_coefficient := overtime_coefficient\r\nemployee_var_paid_leave := paid_leave\r\nemployee_var_paid_leave_option_full := 2\r\nemployee_tag_standart_working_time := 89\r\n\r\ncontract_working_contract_type :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%employee_var_overtime_coefficient%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndoc_salary_statuses_to_exclude :=', '\r\ndoc_salary_paid_days_off_working_contract := paid_leave_contract\r\ndoc_salary_other_charges := other_charges\r\ndoc_salary_vouchers := vouchers_value\r\ndoc_salary_statuses_to_exclude :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_paid_days_off_working_contract%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndoc_loans_type :=', '\r\ndoc_penalties_reason := reason_fine\r\ndoc_penalties_reason_option_no_checking := 1\r\ndoc_penalties_reason_option_other := 2\r\n\r\ndoc_loans_type :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_penalties_reason%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndoc_days_off_type_paid :=', '\r\ndoc_days_off_available := plr_leave_days_off\r\ndoc_days_off_type_paid :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_days_off_available%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndoc_salary_statuses_to_exclude :=', '\r\ndoc_salary_bonus_full_saturday := bonus_month_saturday\r\ndoc_salary_bonus_transport := transport_month\r\ndoc_salary_other_charges := other_charges\r\ndoc_salary_penalties_checking := fine_check\r\ndoc_salary_penalties_unpaid_leave := unpaid_leave_fine\r\ndoc_salary_description_fines := description_fines\r\ndoc_salary_statuses_to_exclude :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_bonus_full_saturday%';

# Updated and added labels for 'bulgariaplast_salary_calculation' report
UPDATE `i18n` SET `value`='Платен отпуск пълен (сума)' WHERE `name`='reports_paid_days_off' AND `group`='bulgariaplast_salary_calculation' AND `lang`='bg';
UPDATE `i18n` SET `value`='Days off full (sum)' WHERE `name`='reports_paid_days_off' AND `group`='bulgariaplast_salary_calculation' AND `lang`='en';
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_paid_days_off_per_contract', 'Платен отпуск по ТД'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_paid_days_off_per_contract', 'Days off per working contract (sum)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_month_salary', 'Заплата за месеца'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_month_salary', 'Month salary'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_bonus_full_month_with_saturdays', 'Бонус (цял месец + събота)'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_bonus_full_month_with_saturdays', 'Bonus (full month + Saturdays)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_bonus_transport', 'Транспорт'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_bonus_transport', 'Transport'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_other_charges', 'Други начисления'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_other_charges', 'Other charges'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_penalties_checking', 'Глоба чекиране'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_penalties_checking', 'Penalty (checking)'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_penalties_no', 'Глоба НО'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_penalties_no', 'Penalty NO'),
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_vouchers', 'Ваучери'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_vouchers', 'Vouchers');

######################################################################################
# 2025-03-20 - Added new settings for 'bulgariaplast_salary_calculation' report and labels for new columns

# Added new settings for 'bulgariaplast_salary_calculation' report and labels for new columns
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nemployee_var_paid_leave_option_full :=', '\r\nemployee_var_position := position_id\r\nemployee_var_paid_leave_option_full :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%employee_var_position%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nfilters_show_department :=', '\r\n\r\nrestricted_roles := 6\r\nrestricted_roles_allowed_columns := name,department,position,days_of_month,days_off_paid,days_off_unpaid,sick_leave,sick_payment,worked_hours,worked_days,overtime,paid_days_off_per_contract,work_trip_days,work_trip_sum,deliveries_wo_failures_sum,compensation,other_charges,deliveries_w_failures,penalties_checking,loans,penalties_cat,vouchers\r\n\r\nfilters_show_department :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%restricted_roles%';

INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_department', 'Отдел'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_department', 'Department');
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'bulgariaplast_salary_calculation', 'bg', 'reports_position', 'Длъжност'),
  ('reports', 'bulgariaplast_salary_calculation', 'en', 'reports_position', 'Position');

######################################################################################
# 2025-03-24 - Added new settings for 'bulgariaplast_salary_calculation' report and position in salary documents

# Added new settings for 'bulgariaplast_salary_calculation' report and position in salary documents
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndoc_salary_penalties_unpaid_leave :=', '\r\ndoc_salary_position := position_id\r\ndoc_salary_position_name := position_name\r\ndoc_salary_penalties_unpaid_leave :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_position_name%';

######################################################################################
# 2025-03-25 - Updated misspelled setting name in 'bulgariaplast_salary_calculation' report

# Updated misspelled setting name in 'bulgariaplast_salary_calculation' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nemployee_tag_standart_working_time :=', '\r\nemployee_tag_standard_working_time :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` LIKE '%employee_tag_standart_working_time%';

######################################################################################
# 2025-04-04 - Added setting for nomenclature type position in 'bulgariaplast_salary_calculation' report

# Added setting for nomenclature type position in 'bulgariaplast_salary_calculation' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\n\nnom_type_position := 10') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%nom_type_position%';

################################################################################
# 2025-04-29 - Adding mass import of customers

# Added import
INSERT IGNORE INTO `imports` SET
  `id` = 29,
  `type` = 'mass_customers_import',
  `settings` = '',
  `visible` = 1;
INSERT IGNORE INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
    (29, 'Импорт на Контрагенти', '', 'bg'),
    (29, 'Customers import', '', 'en');

######################################################################################
# 2025-05-21 - Added additional settings in 'bulgariaplast_salary_calculation' report

# Added additional settings in 'bulgariaplast_salary_calculation' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\ndoc_salary_bank_payment :=', '\ndoc_salary_contract := employee_contract\ndoc_salary_bank_payment :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_contract%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\n\ndoc_days_off_type :=', '\ncontract_no_working_contract_type := 2\n\ndoc_days_off_type :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%contract_no_working_contract_type%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\n\nreason_expense_type :=', '\ndoc_loans_sum_left := paid_payslip\n\nreason_expense_type :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_loans_sum_left%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\ndoc_salary_bonus_worked :=', '\ndoc_salary_bonus_description := description_bonus_salary\ndoc_salary_bonus_worked :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_bonus_description%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\ndoc_loans_sum := advance_amount', '') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` LIKE '%doc_loans_sum := advance_amount%';UPDATE `reports` SET `settings`=REPLACE(`settings`, '\ndoc_salary_penalties_cat :=', '\ndoc_salary_loans_left := loans_balance\ndoc_salary_penalties_cat :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_loans_left%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\ndoc_salary_penalties_cat :=', '\ndoc_salary_loans_left := loans_balance\ndoc_salary_penalties_cat :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_loans_left%';

######################################################################################
# 2025-05-27 - Added additional settings in 'bulgariaplast_salary_calculation' report

# Added additional settings in 'bulgariaplast_salary_calculation' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\ndoc_salary_work_trip_days :=', '\ndoc_salary_paid_days_off_total := paid_leave_all\ndoc_salary_work_trip_days :=') WHERE `type`= 'bulgariaplast_salary_calculation' AND `settings` NOT LIKE '%doc_salary_paid_days_off_total%';

######################################################################################
# 2025-06-05 - Added new settings in the 'bulgariaplast_deliverers_glazing_orders' report

# Added new settings in the 'bulgariaplast_deliverers_glazing_orders' report
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\n\nnomenclature_subtype_var :=', '\nnomenclatures_types_destination := 20\n\nnomenclature_subtype_var :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%nomenclatures_types_destination%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\ndocuments_glazing_length :=', '\ndocuments_delivery_destination := delivery_destination_id\ndocuments_glazing_length :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_delivery_destination%';
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\ndocuments_delivery_city :=', '\ndocuments_supplier_name := glazing_supplier\ndocuments_delivery_city :=')
WHERE `type`='bulgariaplast_deliverers_glazing_orders' AND settings NOT LIKE '%documents_supplier_name%';
