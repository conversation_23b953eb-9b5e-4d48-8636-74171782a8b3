####################################################################################
### SQL nZoom Specific Updates - Evtin Dom (https://evtindom.n-zoom.com/) ###
####################################################################################

####################################################################################
# 2024-06-19 - Added REST settings for Taskman in mZoom.

# The rest settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'allowed_rest_user_agents', 'mzoom-evtindom'),
    ('rest', 'filter_vars_customers', 'all'),
    ('rest', 'filter_vars_departments', 'all'),
    ('rest', 'filter_vars_dashlets', 'all'),
    ('rest', 'filter_vars_documents_34', 'all'),
    ('rest', 'filter_vars_nomenclatures_1', 'all'),
    ('rest', 'filter_vars_users', 'id, username, firstname, lastname, employee, rights, role')
ON DUPLICATE KEY UPDATE
             `value` = VALUES(`value`);

####################################################################################
# 2024-06-20 - Updated REST settings for Taskman in mZoom.

# Added settings to user vars.
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_users', 'id, username, firstname, lastname, employee, rights, role, settings')
ON DUPLICATE KEY UPDATE
  `value` = VALUES(`value`);

####################################################################################
# 2024-07-31 - Added more REST settings for Taskman in mZoom.

# Added settings for emails. Used for notifications titles and body.
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_emails', 'id,name,subject,body')
ON DUPLICATE KEY UPDATE
  `value` = VALUES(`value`);

####################################################################################
# 2025-01-28 - Added new automation

# Adding automation to clear matching notification tokens
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Зачистване на съвпадащи токъни за нотификации', 0, NULL, 1, 'customers', '', 'action', '1', 'notifTokenVar := notification_user_token\r\n', 'condition := \'[action]\' == \'edit\' && \'[a_notification_user_token]\' != \'\'', 'plugin := notifTokens\r\nmethod := clearMatchingNotifTokens', NULL, 1, 0, 1
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%clearMatchingNotifTokens%' AND `start_model_type`=1 AND `module` = 'customers');

########################################################################
# 2025-01-28 - Updated the method and plugin names

# Changed the names for clarity
UPDATE `automations` SET `method` = 'plugin := appNotifTokens\r\nmethod := clearDuplicates' WHERE `method` LIKE '%plugin := notifTokens\r\nmethod := clearMatchingNotifToken%';

########################################################################
# 2025-02-12 - Added inactive push notification automations

# Adding the automations
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT  'Изпращане на пуш нотификации - добавяне като изпълнител', 0, NULL, 1, 'documents', '', 'action', '34', 'host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n# host := https://mevtindom.n-zoom.com/\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\nuser := pushnotifier\r\npass := MQ4hCLnufPSCZorqIJpjbA==\r\n# pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\nnotificationTemplate := 1006\r\nnotificationAssignments := owner\r\n', 'condition := \'[action]\' == \'add\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'add\'');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - промяна статус', 0, NULL, 1, 'documents', '', 'action', '34', 'host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n# host := https://mevtindom.n-zoom.com/\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\nuser := pushnotifier\r\npass := MQ4hCLnufPSCZorqIJpjbA==\r\n# pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\nnotificationTemplate := 1009\r\nnotificationAssignments := owner,observer,responsible\r\n', 'condition := \'[action]\' == \'setstatus\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'setstatus\'');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - добавяне на коментар при стаус', 0, NULL, 1, 'documents', '', 'action', '34', 'host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n# host := https://mevtindom.n-zoom.com/\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\nuser := pushnotifier\r\npass := MQ4hCLnufPSCZorqIJpjbA==\r\n# pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\nnotificationTemplate := 1008\r\nnotificationAssignments := owner,responsible,observer\r\n', 'condition := \'[action]\' == \'setstatus\' && $request->get(\'comment\') != \'\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'setstatus\' && $request->get(\'comment\') != \'\'');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - добавяне на коментар, чисто през комуникации', 0, NULL, 1, 'communications', '', 'action', '34', 'host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n# host := https://mevtindom.n-zoom.com/\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\nuser := pushnotifier\r\npass := MQ4hCLnufPSCZorqIJpjbA==\r\n# pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\nnotificationTemplate := 1008\r\nnotificationAssignments := owner,observer,responsible\r\n', 'condition := \'[action]\' == \'ajax_save_communications_comment\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `conditions` LIKE 'condition := \'[action]\' == \'ajax_save_communications_comment\'');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - редакция за променен приоритет', 0, NULL, 1, 'documents', '', 'action', '34', 'host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n# host := https://mevtindom.n-zoom.com/\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\nuser := pushnotifier\r\npass := MQ4hCLnufPSCZorqIJpjbA==\r\n# pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\nnotificationTemplate := 1010\r\nnotificationAssignments := owner,responsible,observer\r\n', 'condition := \'[action]\' == \'edit\' && \'[a_task_priority]\' != \'[prev_a_task_priority]\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'edit\' && \'[a_task_priority]\' != \'[prev_a_task_priority]\'');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - изтекли задачи', 0, NULL, 1, 'documents', '', 'crontab', '34', '# host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n# host := https://mevtindom.n-zoom.com/\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\nuser := pushnotifier\r\npass := MQ4hCLnufPSCZorqIJpjbA==\r\n# pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\nnotificationTemplate := 1007\r\nnotificationAssignments := owner\r\nstart_time := 08:00\r\nstart_before := 18:00\r\nperiod := 30 minutes\r\n', 'condition := 1', 'plugin := appNotifications\r\nmethod := sendOverdueDocumentNotification', NULL, 1, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendOverdueDocumentNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `automation_type` = 'crontab');

########################################################################
# 2025-02-13 - Updated notification settings

# Added new customAction setting
UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\ncustomAction := status') WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'setstatus\'' AND `settings` NOT LIKE '%customAction := status%';

UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\ncustomAction := comment') WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'setstatus\' && $request->get(\'comment\') != \'\'' AND `settings` NOT LIKE '%customAction := comment%';

UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\ncustomAction := comment') WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `conditions` LIKE 'condition := \'[action]\' == \'ajax_save_communications_comment\'' AND `settings` NOT LIKE '%customAction := comment%';

UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\ncustomAction := priority') WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'edit\' && \'[a_task_priority]\' != \'[prev_a_task_priority]\'' AND `settings` NOT LIKE '%customAction := priority%';

UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\ncustomAction := overdue') WHERE `method` LIKE '%sendOverdueDocumentNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `automation_type` = 'crontab' AND `settings` NOT LIKE '%customAction := overdue%';

########################################################################
# 2025-02-14 - Updated notification Settings  for clarity and ease of use

# Updated add push notifications settings with comments
UPDATE `automations` SET `settings` = '# Генерални настройки за комуникацията с hrzoom
cookiejarName := pushnotifierevtindom
userAgent := pushnotifier

# Хостът на който се намира hrzoom
host := https://mevtindom.n-zoom.com/ # production
# host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


user := pushnotifier
pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25 #production
# pass := MQ4hCLnufPSCZorqIJpjbA==

# Шаблона за известяне който да се използва
notificationTemplate := 1006
# Кои назначения да се взимат в предвид
notificationAssignments := owner' WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'add\'';

# Updating status update push notifications settings with comments
UPDATE `automations` SET `settings` = '# Генерални настройки за комуникацията с hrzoom
cookiejarName := pushnotifierevtindom
userAgent := pushnotifier

# Хостът на който се намира hrzoom
# production
host := https://mevtindom.n-zoom.com/
# testing
# host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


user := pushnotifier
# production
pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
# testing
# pass := MQ4hCLnufPSCZorqIJpjbA==

# Шаблона за известяне който да се използва
notificationTemplate := 1009
# Кои назначения да се взимат в предвид
notificationAssignments := owner,observer,responsible

# Действието което да се използва при търсене на настройките
customAction := status' WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'setstatus\'';

# Updating comment from status update push notifications settings with comments
UPDATE `automations` SET `settings` = '# Генерални настройки за комуникацията с hrzoom
cookiejarName := pushnotifierevtindom
userAgent := pushnotifier

# Хостът на който се намира hrzoom
# production
host := https://mevtindom.n-zoom.com/
# testing
# host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


user := pushnotifier
# production
pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
# testing
# pass := MQ4hCLnufPSCZorqIJpjbA==

# Шаблона за известяне който да се използва
notificationTemplate := 1009
# Кои назначения да се взимат в предвид
notificationAssignments := owner,observer,responsible

# Замества стандартното действие което да се използва при търсене на настройките в досиетата
customAction := status' WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'setstatus\'';

# Updating comment from status update push notifications settings with comments
UPDATE `automations` SET `settings` = '# Генерални настройки за комуникацията с hrzoom
cookiejarName := pushnotifierevtindom
userAgent := pushnotifier

# Хостът на който се намира hrzoom
# production
host := https://mevtindom.n-zoom.com/
# testing
# host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


user := pushnotifier
# production
pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
# testing
# pass := MQ4hCLnufPSCZorqIJpjbA==

# Шаблона за известяне който да се използва
notificationTemplate := 1008
# Кои назначения да се взимат в предвид
notificationAssignments := owner,observer,responsible

# Замества стандартното действие което да се използва при търсене на настройките в досиетата
customAction := comment' WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'setstatus\' && $request->get(\'comment\') != \'\'';

# Inserting new communications automation for adding a comment in document type 34
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - добавяне на коментар, чисто през комуникации', 0, NULL, 1, 'communications', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom
cookiejarName := pushnotifierevtindom
userAgent := pushnotifier

# Хостът на който се намира hrzoom
# production
host := https://mevtindom.n-zoom.com/
# testing
# host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


user := pushnotifier
# production
pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
# testing
# pass := MQ4hCLnufPSCZorqIJpjbA==

# Шаблона за известяне който да се използва
notificationTemplate := 1008
# Кои назначения да се взимат в предвид
notificationAssignments := owner,observer,responsible

# Замества стандартното действие което да се използва при търсене на настройките в досиетата
customAction := comment
# Замества стандратният модул който да се използва при търсене на настройки в досиетата
customModule := documents','condition := \'[action]\' == \'ajax_save_communication_comment\'\r\n condition := $request->get(\'comment_id\') == \'\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `conditions` LIKE 'condition := \'[action]\' == \'ajax_save_communication_comment\'\r\n condition := $request->get(\'comment_id\') == \'\'');

# Updating priority change push notifications with comments
UPDATE `automations` SET `settings` = '# Генерални настройки за комуникацията с hrzoom
cookiejarName := pushnotifierevtindom
userAgent := pushnotifier

# Хостът на който се намира hrzoom
# production
host := https://mevtindom.n-zoom.com/
# testing
# host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


user := pushnotifier
# production
pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
# testing
# pass := MQ4hCLnufPSCZorqIJpjbA==

# Шаблона за известяне който да се използва
notificationTemplate := 1010
# Кои назначения да се взимат в предвид
notificationAssignments := owner,observer,responsible

# Замества стандартното действие което да се използва при търсене на настройките в досиетата
customAction := priority' WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'edit\' && \'[a_task_priority]\' != \'[prev_a_task_priority]\'';

# Updating overdue document push notifications with comments
UPDATE `automations` SET `settings` = '# Генерални настройки за комуникацията с hrzoom
cookiejarName := pushnotifierevtindom
userAgent := pushnotifier

# Хостът на който се намира hrzoom
# production
host := https://mevtindom.n-zoom.com/
# testing
# host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


user := pushnotifier
# production
pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
# testing
# pass := MQ4hCLnufPSCZorqIJpjbA==


start_time := 08:00
start_before := 18:00
period := 30 minutes

# Шаблона за известяне който да се използва
notificationTemplate := 1007
# Кои назначения да се взимат в предвид
notificationAssignments := owner

# Замества стандартното действие което да се използва при търсене на настройките в досиетата
customAction := overdue' WHERE `method` LIKE '%sendOverdueDocumentNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `automation_type` = 'crontab';

########################################################################
# 2025-02-14 - Updated notification Settings  for clarity and ease of use

# Updated add push notifications settings with comments
UPDATE `automations` SET `settings` = REPLACE(`settings`, 'pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25 #production\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==', '# productions\r\npass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\n# testing\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==') WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'add\'';

# Cleaning up the old communication automations that are now obsolete
DELETE FROM `automations` WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `conditions` LIKE 'condition := \'[action]\' == \'ajax_save_communication_comment\'\r\n condition := $request->get(\'comment_id\') == \'\'';
DELETE FROM `automations` WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `conditions` LIKE 'condition := \'[action]\' == \'ajax_save_communications_comment\'' AND `settings` NOT LIKE '%customAction := comment%';

# Inserting new communications automation for adding a comment in document type 34
  INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Изпращане на пуш нотификации - добавяне на коментар, чисто през комуникации', 0, NULL, 1, 'communications', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom
  cookiejarName := pushnotifierevtindom
  userAgent := pushnotifier

  # Хостът на който се намира hrzoom
  # production
  host := https://mevtindom.n-zoom.com/
  # testing
  # host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
  # host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


  user := pushnotifier
  # production
  pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
  # testing
  # pass := MQ4hCLnufPSCZorqIJpjbA==

  # Шаблона за известяне който да се използва
  notificationTemplate := 1008
  # Кои назначения да се взимат в предвид
  notificationAssignments := owner,observer,responsible

  # Замества стандартното действие което да се използва при търсене на настройките в досиетата
  customAction := comment
  # Замества стандратният модул който да се използва при търсене на настройки в досиетата
  customModule := documents','condition := \'[action]\' == \'ajax_save_communication_comment\'\r\ncondition := $registry[\'request\']->get(\'comment_id\') == \'\'\r\ncondition := $registry[\'request\']->get(\'module\') == \'documents\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `conditions` LIKE 'condition := \'[action]\' == \'ajax_save_communication_comment\'\r\ncondition := $registry[\'request\']->get(\'comment_id\') == \'\'\r\ncondition := $registry[\'request\']->get(\'module\') == \'documents\'');

########################################################################
# 2025-02-17 - Add extra setting to all automations using `sendNotification` from `appNotifications`

# New setting that determines whether to use the original user instead of curr (automated system user)
UPDATE `automations`SET `settings` = CONCAT(`settings`, '\r\n# Определя дали да се използва потребителя изпълнил действието вместо автоматична система\r\nexecute_as_original_user := 1') WHERE `method` LIKE '%appNotifications%sendNotification%' AND `settings` NOT LIKE '%execute_as_original_user := 1%';

########################################################################
# 2025-02-17 - Added two new automations

# New setting that determines whether to use the original user instead of curr (automated system user)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - редакция на задача, таблица за изпълнители', 0, NULL, 1, 'documents', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom
  cookiejarName := pushnotifierevtindom
  userAgent := pushnotifier

  # Хостът на който се намира hrzoom
  # production
  host := https://mevtindom.n-zoom.com/
  # testing
  # host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
  # host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


  user := pushnotifier
  # production
  pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
  # testing
  # pass := MQ4hCLnufPSCZorqIJpjbA==

  # Шаблона за известяне който да се използва
  notificationTemplate := 1006
  # Кои назначения да се взимат в предвид
  notificationAssignments := owner','condition := \'[action]\' == \'assign\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `conditions` LIKE 'condition := \'[action]\' == \'assign\'');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - редакция на задача, таблица за изпълнители', 0, NULL, 1, 'documents', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom
  cookiejarName := pushnotifierevtindom
  userAgent := pushnotifier

  # Хостът на който се намира hrzoom
  # production
  host := https://mevtindom.n-zoom.com/
  # testing
  # host := https://t.n-zoom.com/g/hrzoom/evtindom/43-taskman-notifications-nzoom/public/
  # host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/


  user := pushnotifier
  # production
  pass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25
  # testing
  # pass := MQ4hCLnufPSCZorqIJpjbA==

  # Шаблона за известяне който да се използва
  notificationTemplate := 1006
  # Кои назначения да се взимат в предвид
  notificationAssignments := owner','condition := \'[action]\' == \'edit\'\r\ncondition := \'[a_task_user_id]\' != \'[prev_a_task_user_id]\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `conditions` LIKE 'condition := \'[action]\' == \'edit\'\r\ncondition := \'[a_task_user_id]\' != \'[prev_a_task_user_id]\'');

########################################################################
# 2025-02-21 - Redid all notifications

# Removing old notifications
DELETE FROM `automations` WHERE `method` LIKE '%appNotifications%';

# Inserting all new automations
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - добавяне като изпълнител', 0, NULL, 1, 'documents', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\n\r\n# Хостът на който се намира hrzoom\r\n# production\r\nhost := https://mevtindom.n-zoom.com/ \r\n# testing hosts\r\n# host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n\r\nuser := pushnotifier\r\n# production\r\npass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\n# testing\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==\r\n\r\n# Шаблона за известяне който да се използва\r\nnotificationTemplate := 1006\r\n# Кои назначения да се взимат в предвид\r\nnotificationAssignments := owner\r\n# Действие което да замести текущото\r\ncustomAction := assign\r\n\r\nexecute_as_original_user := 1\r\n\r\n# Настройка която определя дали да се вземат само новоназначените потребители\r\nuseNewlyAssignedUsers := 1\r\n', 'condition := in_array(\'[action]\', [\'add\',\'assign\',\'clone\']) || (\'[action]\' == \'edit\' && (!empty($this->registry[\'request\']->get(\'task_user_id\')) ? implode(\',\',array_filter($this->registry[\'request\']->get(\'task_user_id\'))) : \'\') != $this->registry[\'request\']->get(\'assign_owners_all\'))', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `settings` LIKE '%notificationTemplate := 1006%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - добавяне на коментар при статус', 0, NULL, 1, 'documents', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\n\r\n# Хостът на който се намира hrzoom\r\n# production\r\nhost := https://mevtindom.n-zoom.com/ \r\n# testing hosts\r\n# host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n\r\nuser := pushnotifier\r\n# production\r\npass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\n# testing\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==\r\n\r\n# Шаблона за известяне който да се използва\r\nnotificationTemplate := 1008\r\n# Кои назначения да се взимат в предвид\r\nnotificationAssignments := owner,responsible,observer\r\n# Замества текущото действие\r\ncustomAction := comment\r\n\r\nexecute_as_original_user := 1\r\n', 'condition := \'[action]\' == \'setstatus\'\r\ncondition := $this->registry[\'request\']->get(\'comment\') != \'\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `settings` LIKE '%notificationTemplate := 1008%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - добавяне на коментар през таб комуникации', 0, NULL, 1, 'communications', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\n\r\n# Хостът на който се намира hrzoom\r\n# production\r\nhost := https://mevtindom.n-zoom.com/ \r\n# testing hosts\r\n# host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n\r\nuser := pushnotifier\r\n# production\r\npass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\n# testing\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==\r\n\r\n# Шаблона за известяне който да се използва\r\nnotificationTemplate := 1008\r\n# Кои назначения да се взимат в предвид\r\nnotificationAssignments := owner,responsible,observer\r\n\r\n# Заменя текущото действие\r\ncustomAction := comment\r\n# Заменя модула на АД-то\r\ncustomModule := documents\r\n\r\nexecute_as_original_user := 1\r\n', 'condition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'ajax_save_communication_comment\'\r\ncondition := $registry[\'request\']->get(\'comment_id\') == \'\'\r\ncondition := $registry[\'request\']->get(\'module\') ==  \'documents\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'communications' AND `settings` LIKE '%notificationTemplate := 1008%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - смяна на статус', 0, NULL, 1, 'documents', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\n\r\n# Хостът на който се намира hrzoom\r\n# production\r\nhost := https://mevtindom.n-zoom.com/ \r\n# testing hosts\r\n# host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n\r\nuser := pushnotifier\r\n# production\r\npass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\n# testing\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==\r\n\r\n# Шаблона за известяне който да се използва\r\nnotificationTemplate := 1009\r\n# Кои назначения да се взимат в предвид\r\nnotificationAssignments := owner,responsible,observer\r\n\r\n# заменя текущото действие\r\ncustomAction := status\r\n\r\nexecute_as_original_user := 1\r\n', 'condition := \'[action]\' == \'setstatus\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `settings` LIKE '%notificationTemplate := 1009%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации - смяна на приоритет', 0, NULL, 1, 'documents', '', 'action', '34', '# Генерални настройки за комуникацията с hrzoom\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\n\r\n# Хостът на който се намира hrzoom\r\n# production\r\nhost := https://mevtindom.n-zoom.com/ \r\n# testing hosts\r\n# host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n\r\nuser := pushnotifier\r\n# production\r\npass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\n# testing\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==\r\n\r\n# Шаблона за известяне който да се използва\r\nnotificationTemplate := 1010\r\n# Кои назначения да се взимат в предвид\r\nnotificationAssignments := owner,responsible,observer\r\n\r\n# заменя текущото действие\r\ncustomAction := priority\r\n\r\nexecute_as_original_user := 1\r\n', 'condition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'edit\'\r\ncondition := \'[a_task_priority]\' != \'[prev_a_task_priority]\'', 'plugin := appNotifications\r\nmethod := sendNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `settings` LIKE '%notificationTemplate := 1010%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Изпращане на пуш нотификации за изтекли документи', 0, NULL, 1, 'documents', '', 'crontab', '34', '# Генерални настройки за комуникацията с hrzoom\r\ncookiejarName := pushnotifierevtindom\r\nuserAgent := pushnotifier\r\n\r\n# Хостът на който се намира hrzoom\r\n# production\r\nhost := https://mevtindom.n-zoom.com/ \r\n# testing hosts\r\n# host := https://t.n-zoom.com/g/hrzoom/evtindom/main/public/\r\n# host := https://gtt.n-zoom.com/hrzoom/evtindom/main/public/\r\n\r\nuser := pushnotifier\r\n# production\r\npass := 71Kc7SbMOthddbN+bV/hASecS7LkN/25\r\n# testing\r\n# pass := MQ4hCLnufPSCZorqIJpjbA==\r\n\r\n# Шаблона за известяне който да се използва\r\nnotificationTemplate := 1007\r\n# Кои назначения да се взимат в предвид\r\nnotificationAssignments := owner,responsible,observer\r\n\r\n# заменя текущото действие\r\ncustomAction := overdue\r\n\r\nexecute_as_original_user := 1\r\n', 'condition := 1', 'plugin := appNotifications\r\nmethod := sendOverdueDocumentNotification', NULL, 99, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%sendOverdueDocumentNotification%' AND `start_model_type`=34 AND `module` = 'documents' AND `automation_type` = 'crontab' AND `settings` LIKE '%notificationTemplate := 1007%');

########################################################################
# 2025-05-27 - Removing the use of CustomModule

# Commenting the unneccessary setting
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'customModule', '#customModule')
WHERE `method` LIKE '%sendNotification%' AND `settings` NOT LIKE '%#customModule%';
