###########################################################################
### SQL nZoom Specific Updates БГ Сервиз (http://bgs.n-zoom.com/) ###
###########################################################################

######################################################################################
# 2010-11-03 - Added automation plugin for document type 96

DELETE FROM automations WHERE id = 38;
INSERT INTO automations SET
    id = NULL, depend = 0, exclude = NULL, should_continue = 1, `module` = 'documents',
    controller = NULL, automation_type = 'action', start_model_type = 96,
    settings = 'document_status := locked\ndocument_substatus := 90\ntimesheet_activity := 1',
    conditions = 'condition := 1', method = 'plugin := bgservice\nmethod := requestCustomNum',
    after_action = NULL, position = 0, nums = 9999;

######################################################################################
# 2011-04-26 - Added new report 'bgservice_remote_work' for BG Service installation (1707)

# Added new report 'bgservice_remote_work' for BG Service installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (169, 'bgservice_remote_work', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (169, 'Отдалечена работа', NULL, NULL, 'bg'),
  (169, 'Remote Work', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 169, 0, 1),
  ('reports', 'export', 169, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=169;

######################################################################################
# 2011-06-16 - Added option for creating patterns for 'bgservice_remote_work' report
#            - Added placeholders for 'bgservice_remote_work' report

# Added option for creating patterns for 'bgservice_remote_work' report
UPDATE `reports` SET `settings`='allows_files_generation := 1' WHERE `type`='bgservice_remote_work';

# Added placeholders for 'bgservice_remote_work' report
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'remote_work_table', 'Report', 'send', 'all', ',169,', 'remote_work_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Отдалечена работа (таблица)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Remote work (table)', NULL, 'en');

######################################################################################
# 2011-06-29 - Added automation for restriction for adding documents of type "Remote work"

# Added automation for restriction for adding documents of type "Remote work"
INSERT INTO `automations` (`id`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 0, NULL, 1, 'documents', NULL, 'before_action', 85, '', 'condition := 1', 'plugin := bgservice\r\nmethod := checkDailyReport', '', 0, 0);

######################################################################################
# 2011-07-11 - Added new report 'bgservice_calls' for BG Service installation (1707)

# Added new report 'bgservice_calls' for BG Service installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (172, 'bgservice_calls', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (172, 'Обаждания', NULL, NULL, 'bg'),
  (172, 'Calls', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 172, 0, 1),
  ('reports', 'export', 172, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=172;

######################################################################################
# 2011-07-28 - Added new report 'bgservice_customer_file' for BG Service installation (1707)

# Added new report 'bgservice_customer_file' for BG Service installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (179, 'bgservice_customer_file', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (179, 'Досие на контрагент', NULL, NULL, 'bg'),
  (179, 'Customer file', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 179, 0, 1),
  ('reports', 'export', 179, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=179;

########################################################################
# 2011-08-20 - Updated data for sent mails from campaigns - fix records for deleted targetlists data where possible

# Updated data for sent mails from campaigns - fix records for deleted targetlists data where possible
UPDATE `emails_sentbox` esb, customers c
SET esb.model_id=c.id
WHERE esb.model='Customer' AND esb.model_id=0 AND esb.recipient LIKE '%@bgservice.net' AND c.type=1 AND c.email LIKE CONCAT('%', esb.recipient, '%');

########################################################################
# 2011-08-29 - Added automation which get the list calls from the previous day and makes events and minitasks from their rows

# Added automation which get the list calls from the previous day and makes events and minitasks from their rows
INSERT INTO `automations` (`id`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 0, NULL, 1, 'documents', NULL, 'crontab', 102, 'start_time := 01:00\r\nstart_before := 03:00\r\ncall_type := list_call_status\r\ncall_meeting := call_status_second\r\ncall_minitasks := call_status_fift,call_status_first\r\ncall_minitasks_special := call_status_fourth\r\ncustomer_name := customer_name\r\ncustomer_id := customer_id\r\ncall_list_next_step := list_datenext_call\r\ncall_description := call_description\r\ncall_next_step_comment := list_coment', 'condition := 1', 'plugin := bgservice\r\nmethod := callListMeetings', NULL, 0, 0);

######################################################################################
# 2011-09-07 - Added new report 'bgservice_feedback_analysis' for BG Service installation (1707)

# Added new report 'bgservice_feedback_analysis' for BG Service installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (187, 'bgservice_feedback_analysis', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (187, 'Анализ на обратни връзки', NULL, NULL, 'bg'),
  (187, 'Feedback analysis', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 187, 0, 1),
  ('reports', 'export', 187, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=187;

######################################################################################
# 2011-09-08 - Added new report 'bgservice_qualifications_registry' for BG Service installation (1707)

# Added new report 'bgservice_qualifications_registry' for BG Service installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (188, 'bgservice_qualifications_registry', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (188, 'Регистър на квалификации', NULL, NULL, 'bg'),
  (188, 'Qualifications registry', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 188, 0, 1),
  ('reports', 'export', 188, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 188;

######################################################################################
# 2011-09-14 - Added pattern plugins for financial documents

# Added pattern plugins for financial documents
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(20, 'Finance_Incomes_Reason', 1, 'bgservice', 'prepareInvoices', 'invoice.png', NOW(), NOW()),
(21, 'Finance_Incomes_Reason', 2, 'bgservice', 'prepareInvoices', 'invoice.png', NOW(), NOW()),
(22, 'Finance_Incomes_Reason', 3, 'bgservice', 'prepareInvoices', 'invoice.png', NOW(), NOW()),
(23, 'Finance_Incomes_Reason', 4, 'bgservice', 'prepareInvoices', 'invoice.png', NOW(), NOW());

INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(20, 'Подготовка на фактури (БГСервиз)', 'Данните за фактурите се обработват по специфичен начин, за да се постигне желаният печат.', 'bg', NOW()),
(20, 'Invoices Preparation (BGService)', 'Invoice data is prepared in a specific manner to print the pattern in the desired mode.', 'en', NOW()),
(21, 'Подготовка на проформа фактури (БГСервиз)', 'Данните за проформа фактурите се обработват по специфичен начин, за да се постигне желаният печат.', 'bg', NOW()),
(21, 'Proforma Invoices Preparation (BGService)', 'Proforma invoice data is prepared in a specific manner to print the pattern in the desired mode.', 'en', NOW()),
(22, 'Подготовка на кредитни известия (БГСервиз)', 'Данните за кредитните известия се обработват по специфичен начин, за да се постигне желаният печат.', 'bg', NOW()),
(22, 'Credit Notes Preparation (BGService)', 'Credit note data is prepared in a specific manner to print the pattern in the desired mode.', 'en', NOW()),
(23, 'Подготовка на дебитни известия (БГСервиз)', 'Данните за дебитните известия се обработват по специфичен начин, за да се постигне желаният печат.', 'bg', NOW()),
(23, 'Debit Notes Preparation (BGService)', 'Debit note data is prepared in a specific manner to print the pattern in the desired mode.', 'en', NOW());

######################################################################################
# 2011-10-07 - Added crontab automation plugin for renewing contracts

# Added crontab automation plugin for renewing contracts
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Подновяване на договори', 0, NULL, 1, 'contracts', NULL, 'crontab', 1, 'start_week_day := 7\r\nperiod := 7 day\r\nstart_time := 03:00\r\nstart_before := 08:00\r\ncontract_types := 1,2\r\ncontract_substatuses := 1,2\r\nbefore_date_validity := 30\r\ncheck_var := automatically_renew\r\ncheck_var_value := 1\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := bgservice\r\nmethod := renewContracts', NULL, 0, 0);

######################################################################################
# 2011-10-19 - Added new report 'bgservice_trial_balance' for BG Service installation (1707)

# Added new report 'bgservice_trial_balance' for BG Service installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (189, 'bgservice_trial_balance', NULL, 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (189, 'Оборотна ведомост', '01. Финанси', NULL, 'bg'),
  (189, 'Trial balance', '01. Financial', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 189, 0, 1),
  ('reports', 'export', 189, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 189;

######################################################################################
# 2011-11-01 - Added new report 'bgservice_suppliers_clients_obligations' for BG Service installation (1707)

# Added new report 'bgservice_trial_balance' for BG Service installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (190, 'bgservice_suppliers_clients_obligations', 'incomes_reason_order := 104\r\nfinance_expense := 101\r\nexpense_request := 86\r\ntype_deliverer := 12\r\ntype_client := 8\r\nnew_reason_company := 1\r\n', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (190, 'Задължения на клиенти и доставчици', '01. Финанси', NULL, 'bg'),
  (190, 'Suppliers and clients obligations', '01. Financial', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 190, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report') AND `model_type` = 190;

######################################################################################
# 2011-11-03 - Update the name of the report 'bgservice_trial_balance' to 'trial_balance'
#            - Added notification for the result of execution of the calls list event automation

# Update the name of the report 'bgservice_trial_balance' to 'trial_balance'
UPDATE `reports` SET `type` = 'trial_balance' WHERE `id` =189;

# Added notification for the result of execution of the calls list event automation
UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\nsend_to_email := <EMAIL>') WHERE `method` LIKE '%method := callListMeetings%';

######################################################################################
# 2012-02-06 - Added export of financial documents, warehouses documents and payments to AJUR L

# Added export of financial documents, warehouses documents and payments to AJUR L
INSERT INTO `exports` (`id`, `type`, `model`, `model_type`, `settings`, `visible`) VALUES
(1, 'bgservice_to_ajur', 'Finance_Incomes_Reason', 1, 'export_file_name := invoices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n', 1),
(2, 'bgservice_to_ajur', 'Finance_Incomes_Reason', 3, 'export_file_name := credit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n', 1),
(3, 'bgservice_to_ajur', 'Finance_Incomes_Reason', 4, 'export_file_name := debit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n', 1),
(4, 'bgservice_to_ajur', 'Finance_Warehouses_Document', 7, 'export_file_name := handovers\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 3\r\nexport_hide_filters := file_name, format, separator\r\n', 1),
(5, 'bgservice_to_ajur', 'Finance_Warehouses_Document', 13, 'export_file_name := missing\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 3\r\nexport_hide_filters := file_name, format, separator\r\n', 1),
(6, 'bgservice_to_ajur', 'Finance_Warehouses_Document', 19, 'export_file_name := surplus\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 3\r\nexport_hide_filters := file_name, format, separator\r\n', 1),
(7, 'bgservice_to_ajur', 'Finance_Warehouses_Document', 11, 'export_file_name := waste\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 3\r\nexport_hide_filters := file_name, format, separator\r\n', 1),
(8, 'bgservice_to_ajur', 'Finance_Payment', 0, 'export_file_name := payments\r\nexport_encoding := windows-1251\r\nexport_delimiter := #\r\nexport_tags := 4\r\nexport_hide_filters := file_name, format, separator\r\n', 1);
INSERT INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(1, 'Експорт на фактури към Ажур L', 'Фактурите се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(1, 'Export of invoices to Ajur L', 'The invoices are exported in two files with special format', 'en'),
(2, 'Експорт на кредитни известия към Ажур L', 'Кредитните известия се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(2, 'Export of Credit Notices to Ajur L', 'The credit notices are exported in two files with special format', 'en'),
(3, 'Експорт на дебитни известия към Ажур L', 'Дебитните известия се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(3, 'Export of Debit Notices to Ajur L', 'The debit notices are exported in two files with special format', 'en'),
(4, 'Експорт на приемо-предавателни протоколи към Ажур L', 'Приемо-предавателните протоколи се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(4, 'Export of records of handover to Ajur L', 'The records of handover are exported in two files with special format', 'en'),
(5, 'Експорт на протоколи за липси към Ажур L', 'Протоколите за липси се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(5, 'Export of records of missing stock to Ajur L', 'The records of missing stocks are exported in two files with special format', 'en'),
(6, 'Експорт на протоколи за излишъци към Ажур L', 'Протоколите за излишъци се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(6, 'Export of surplus records to Ajur L', 'The surplus records are exported in two files with special format', 'en'),
(7, 'Експорт на протоколи за брак към Ажур L', 'Протоколите за брак се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(7, 'Export of records of waste to Ajur L', 'The records of waste are exported in two files with special format', 'en'),
(8, 'Експорт на платежни документи към Ажур L', 'Експортира се разпределението на сумите по финансови документи', 'bg'),
(8, 'Export of payments to Ajur L', 'The payments are exported in special format', 'en');

######################################################################################
# 2012-03-16 - Updated tags for export of financial documents, warehouses documents and payments to AJUR L

# Updated tags for export of financial documents, warehouses documents and payments to AJUR L
UPDATE `exports` SET `settings` = 'export_file_name := invoices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 40\r\nexport_hide_filters := file_name, format, separator\r\n'       WHERE `id` = '1';
UPDATE `exports` SET `settings` = 'export_file_name := credit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 40\r\nexport_hide_filters := file_name, format, separator\r\n' WHERE `id` = '2';
UPDATE `exports` SET `settings` = 'export_file_name := debit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 40\r\nexport_hide_filters := file_name, format, separator\r\n'  WHERE `id` = '3';
UPDATE `exports` SET `settings` = 'export_file_name := handovers\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 33\r\nexport_hide_filters := file_name, format, separator\r\n'      WHERE `id` = '4';
UPDATE `exports` SET `settings` = 'export_file_name := missing\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 33\r\nexport_hide_filters := file_name, format, separator\r\n'        WHERE `id` = '5';
UPDATE `exports` SET `settings` = 'export_file_name := surplus\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 33\r\nexport_hide_filters := file_name, format, separator\r\n'        WHERE `id` = '6';
UPDATE `exports` SET `settings` = 'export_file_name := waste\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 33\r\nexport_hide_filters := file_name, format, separator\r\n'          WHERE `id` = '7';
UPDATE `exports` SET `settings` = 'export_file_name := payments\r\nexport_encoding := windows-1251\r\nexport_delimiter := #\r\nexport_tags := 42\r\nexport_hide_filters := file_name, format, separator\r\n'       WHERE `id` = '8';

######################################################################################
# 2012-04-21 - Update the settings of the report 'bgservice_suppliers_clients_obligations'

# Update the settings of the report 'bgservice_suppliers_clients_obligations'
UPDATE `reports`  SET `settings`='incomes_reason_order := 104\r\nfinance_expense := 101,20,21\r\nfinance_expense_related_types := 20,21,22,23\r\nexpense_request := 86\r\ntype_deliverer := 12\r\ntype_client := 8\r\nnew_reason_company := 1\r\ncustomer_bg_service := 5' WHERE id=190;

######################################################################################
# 2012-04-24 - Added footer plugin
#            - Added placeholders for footer plugin

#Added footer plugin
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(29, '', 0, 'bgservice', 'prepareFooter', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(29, 'Подготовка на футър', 'Подготвят се данни за "Идентификация", "Текуща редакция", "Дата на текуща редакция" и пр.', 'bg', NOW()),
(29, 'Footer Preparation', 'Data prepared for footer content including Code, Current Version, Current Date etc.', 'en', NOW());

#Added placeholders for footer plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'footer_code', 'Document', 'basic', 'pattern_plugins', ',29,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Идентификация/Код', NULL, 'bg'),
(LAST_INSERT_ID(), 'Code', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'footer_current_version', 'Document', 'basic', 'pattern_plugins', ',29,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Текуща редакция', NULL, 'bg'),
(LAST_INSERT_ID(), 'Current Version', NULL, 'en');
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'footer_date_current', 'Document', 'basic', 'pattern_plugins', ',29,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на текуща редакция', NULL, 'bg'),
(LAST_INSERT_ID(), 'Current Date', NULL, 'en');

########################################################################
# 2012-04-25 - Add new report - 'qms_management' for the BGService installation (1707)
#            - Enable the notify_payment_status crontab in BGS
#            - Added new report 'bgservice_suppliers_obligations' for BGService installation (1707)

# Add new report - 'qms_management' for the BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('205', 'qms_management', 'nomenclature_type_id := 10\r\ndefault_bg_color := ECEDEE\r\ndoc_type_color := FFA500\r\nnomenclature_color := 3FCC59\r\ndoc_type_width := 40\r\ncode_name_width := 635\r\ncurrent_version_min_width := 10\r\ndate_current_width := 70', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('205', 'Управление на СУК', NULL, NULL, 'bg'),
  ('205', 'Management of QMS', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '205', '0', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` = 'generate_report'
      AND `model_type` = '205';

# Enable the notify_payment_status crontab in BGS
UPDATE settings SET value=REPLACE(value, ', notify_payment_status', '') WHERE value LIKE '%notify_payment_status%' AND name='disable' AND section='crontab';

# Added new report 'bgservice_suppliers_obligations' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (207, 'bgservice_suppliers_obligations', 'finance_expense := 101', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (207, 'Задължения към доставчици', '01. Финанси', NULL, 'bg'),
  (207, 'Suppliers obligations', '01. Financial', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 207, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report') AND `model_type` = 207;

########################################################################
# 2012-05-29 - Add new report - 'bgservice_requests' for the BGService installation (1707)
#            - Add SQL for the QMS management automation

# Added new report 'bgservice_requests' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (211, 'bgservice_requests', 'type_request_client := 22\r\nrequest_client_description := description_inq\r\ntype_request := 96\r\nadded_by_users := 4,5,93,122,57\r\nworking_time_starts := 09:00\r\nworking_time_ends := 18:00\r\nbugzilla_base_link := http://bugzilla.bgservice.net/bugzilla3/show_bug.cgi?id=', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (211, 'Заявки', NULL, NULL, 'bg'),
  (211, 'Requests', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 211, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report') AND `model_type` = 211;

# Add SQL for the QMS management automation (it should be reconfigured by Projects Dept)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, NULL, 0, NULL, 1, 'documents', NULL, 'action', 0, 'nomenclature_type := 18\r\nstatus := closed\r\nsubstatus := 42', 'condition := ''[a_qms_doc_id]'' > 0\r\ncondition := ''[b_status]'' == ''closed''\r\ncondition := ''[b_substatus]'' == ''42''', 'plugin := bgservice\r\nmethod := updateNomenclatureQMS', NULL, 0, 1);

########################################################################
# 2012-07-09 - Added new report - 'due_or_recoverable_vat' for BGService installation (1707)

# Added new report - 'due_or_recoverable_vat' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (220, 'due_or_recoverable_vat', 'allows_files_generation := 1\r\nfile_origin := self\r\nhide_export_default := 1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (220, 'Дължимо/възстановимо ДДС', '01. Финанси', NULL, 'bg'),
  (220, 'Due/recoverable VAT', '01. Financial', NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '220', '0', '1'),
  ('reports', 'export', '220', '0', '2');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '220';

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'period_expenses_and_incomes', 'Report', 'send', 'all', ',220,', 'period_expenses_and_incomes', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Разходи/приходи за периода (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Period expenses/revenues (table)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'due_or_recoverable_vat', 'Report', 'send', 'all', ',220,', 'due_or_recoverable_vat', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Due/recoverable VAT (table)', NULL, 'en');

INSERT INTO `patterns` (`id`, `model`, `model_type`, `section`, `list`, `for_printform`, `company`, `position`, `header`, `footer`, `background_image`, `background_image_position`, `landscape`, `prefix`, `format`, `force_generate`, `not_regenerate_finished_record`, `plugin`, `handover_direction`, `is_portal`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
  (NULL, 'Report', '220', 0, 0, 0, 0, 1, 0, 0, NULL, 'left_top', 0, '[current_date]_[name]', 'pdf', 0, 0, 0, NULL, 0, 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
INSERT INTO `patterns_i18n` (`parent_id`, `name`, `description`, `content`, `lang`, `translated`) VALUES
  (LAST_INSERT_ID(), 'Дължимо/възстановимо ДДС', '', '<p>\r\n [period_expenses_and_incomes]</p>\r\n<p>\r\n    [due_or_recoverable_vat]</p>\r\n', 'bg', NOW()),
  (LAST_INSERT_ID(), 'Due/recoverable VAT', '', '<p>\r\n  [period_expenses_and_incomes]</p>\r\n<p>\r\n    [due_or_recoverable_vat]</p>\r\n', 'en', NOW());

######################################################################################
# 2012-07-23 - Added automation for validating the appointment of the Order type documents

# Added automation for validating the appointment of the Order type documents
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, '0', NULL, '1', 'documents', NULL, 'before_action', '43', 'substatus := locked_76', 'condition := \'[prev_b_substatus]\' != \'76\'', 'plugin := bgservice\r\nmethod := validateDocumentOrder', 'cancel_action_on_fail := 1', '0', '0');

######################################################################################
# 2012-07-25 - Added automation for publishing an Order type document

# Added automation for publishing an Order type document
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, '0', NULL, '1', 'documents', NULL, 'action', '43', 'substatus := locked_76\r\nnew_substatus := locked_77\r\npattern_id := 15\r\nannouncement_type_id := 9\r\nannouncement_category_type_id := 8\r\nannouncement_priority := normal\r\nannouncement_default_validity_term := 2030-12-31', 'condition := \'[prev_b_substatus]\' != \'76\'\r\ncondition := \'[b_substatus]\' == \'76\'', 'plugin := bgservice\r\nmethod := publishDocumentOrder', NULL, '0', '0');

########################################################################
# 2012-07-27 - Add new report - 'bgservice_inventory' for the BGService installation (1707)
#            - Add new report - 'bgservice_buildings_rooms_registry' for BGService installation (1707)

# Added new report 'bgservice_inventory' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(225, 'bgservice_inventory', 'inventory_report := 81\r\nnom_type_equipment := 1\r\n\r\ninventory_employee := empl_name_id\r\ninventory_date := inventory_date\r\ninventory_id := inventory_id\r\ninventory_building := build_num\r\ninventory_room := room_num\r\ninventory_destination := file_destination\r\n\r\ndestination_received := get_inventory\r\ndestination_returned := return_inventory', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (225, 'Инвентар', NULL, NULL, 'bg'),
  (225, 'Inventory', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 225, 0, 1),
  ('reports', 'export', 225, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 225;

# Add new report - 'bgservice_buildings_rooms_registry' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (226, 'bgservice_buildings_rooms_registry', 'nom_type_building := 11\r\nnom_type_room := 12\r\nnom_type_working_place := 13\r\n\r\nbuilding_city := build_town\r\nbuilding_address := build_address\r\nbuilding_room_id := room_id\r\n\r\nroom_working_place := work_place_id\r\nroom_employee := empl_id', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (226, 'Регистър на сгради и помещения', NULL, NULL, 'bg'),
  (226, 'Buildings and rooms registry', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 226, 0, 1),
  ('reports', 'export', 226, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 226;

########################################################################
# 2012-07-31 - Changed settings of the 'bgservice_inventory' report

# Changed settings of the 'bgservice_inventory' report
UPDATE `reports` SET `settings`='inventory_report := 81\r\nnom_type_equipment := 1\r\n\r\ninventory_employee := empl_name_id\r\ninventory_date := inventory_date\r\ninventory_id := inventory_id\r\ninventory_building := build_id\r\ninventory_room := room_id\r\ninventory_destination := file_destination\r\ninventory_ocs := ocs_link\r\n\r\ndestination_received := get_inventory\r\ndestination_returned := return_inventory' WHERE `type`='bgservice_inventory';

########################################################################
# 2012-08-03 - Added export permissions for 'bgservice_suppliers_clients_obligations' report

# Added export permissions for 'bgservice_suppliers_clients_obligations' report
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'export', 190, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'export') AND `model_type` = 190;

########################################################################
# 2012-08-09 - Add new report - 'bgservice_cars' for BGService installation (1707)

# Add new report - 'bgservice_cars' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (227, 'bgservice_cars', 'nom_type_car := 2\r\nnom_type_part_service := 3\r\ncustomer_car_owner := 7\r\ncustomer_insurers := 3,8,12\r\n\r\ncar_owned_by := firm_bgs_id\r\ncar_insured_by := insurer_id\r\ncar_insured_until := insurer_date_finish\r\ncar_inspected_until := technical_review_finish\r\ncar_tax_paid_until := tax_finish\r\ncar_insurance_payment_type := insurer_payment', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (227, 'Автомобили', NULL, NULL, 'bg'),
  (227, 'Cars', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 227, 0, 1),
  ('reports', 'export', 227, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 227;

########################################################################
# 2012-08-14 - Changed settings of the 'bgservice_cars' report

# Changed settings of the 'bgservice_cars' report
UPDATE `reports` SET `settings`='nom_type_car := 2\r\nnom_type_part_service := 3\r\ncustomer_car_owner := 7\r\ncustomer_insurers := 3,8,12\r\n\r\ncar_owned_by := firm_bgs_id\r\ncar_insured_by := insurer_id\r\ncar_insured_until := insurer_date_finish\r\ncar_inspected_until := technical_review_finish\r\ncar_tax_paid_until := tax_finish\r\ncar_insurance_payment_type := insurer_payment\r\n\r\ndoc_type_fix_card := 105\r\nfix_card_car := car_id\r\nfix_card_total := total\r\n\r\ninvoice_total := total\r\n\r\nexpenses_reason_type_id := 101\r\nexpenses_reason_total := total\r\n\r\ndoc_request_expense := 86\r\nrequest_expense_total := total' WHERE `type`='bgservice_cars';

######################################################################################
# 2012-09-26 - Moved the updateNomenclatureQMS method to separate QMS plugin (instead of one that is specific to the company)
#            - Moved the printHeaderFooter plugin method to separate QMS plugin (instead of one that is specific to the company)

# Moved the updateNomenclatureQMS method to separate QMS plugin (instead of one that is specific to the company)
UPDATE `automations` SET method = 'plugin := qms\r\nmethod := updateNomenclatureQMS' WHERE `method` LIKE '%method := updateNomenclatureQMS%';

# Moved the printHeaderFooter plugin method to separate QMS plugin (instead of one that is specific to the company)
UPDATE `patterns_plugins` SET folder = 'qms', method = 'prepareHeaderFooter', settings = 'type := footer\r\nnom_type := 10\r\nnom_var_name := nzoom_id\r\n' WHERE `method` LIKE '%prepareFooter%';

########################################################################
# 2012-09-28 - Updated settings for the 'bgservice_suppliers_clients_obligations' report so type private client will be used in Clients filter

# Updated settings for the 'bgservice_suppliers_clients_obligations' report so type private client will be used in Clients filter
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ntype_private_client := 10') WHERE `type`='bgservice_suppliers_clients_obligations';

########################################################################
# 2012-10-01 - Add new report - 'bgservice_receivables_overdue' for BGService installation (1707)

# Add new report - 'bgservice_receivables_overdue' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(235, 'bgservice_receivables_overdue', NULL, 70, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(235, 'Вземания до дата', '01. Финанси', NULL, 'bg'),
(235, 'Receivables Overdue', '01. Financial', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 235, 0, 1),
  ('reports', 'export', 235, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND (`action` = 'generate_report' OR `action` = 'export') AND `model_type` = 235;

########################################################################
# 2012-10-02 - Add new pattern plugin (and placeholders for it) for printing leave requests document

# Add new pattern plugin (and placeholders for it) for printing leave requests document
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(37, 'Document', 6, 'bgservice', 'prepareLeaveRequestAdditionalPrintVars', 'leave_request_type := plr_leave_type\r\nworking_contract_type := 3\r\nemployee_position := contract_position\r\nleave_request_year := plr_leave_year\r\ncontract_available_free_days := due_leave\r\n\r\nrequest_type_paid := paid\r\nrequest_type_unpaid := unpaid', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(37, 'Подготовка на специални променливи', '', 'bg', NOW()),
(37, 'Preparation of specific variables', '', 'en', NOW());

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'leave_request_legal_basis', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Законово основание', NULL, 'bg'),
(LAST_INSERT_ID(), 'Legal basis', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'employee_position', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Длъжност на служителя', NULL, 'bg'),
(LAST_INSERT_ID(), 'Employee position', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'working_date_start', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на постъпване на работа на служителя', NULL, 'bg'),
(LAST_INSERT_ID(), 'Employee''s starting working day', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'used_paid_days', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Използвани дни платен отпуск', NULL, 'bg'),
(LAST_INSERT_ID(), 'Used paid days off', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'used_unpaid_days', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Използвани дни  неплатен отпуск', NULL, 'bg'),
(LAST_INSERT_ID(), 'Used unpaid days off', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'leave_request_date', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на заявката за отпуск', NULL, 'bg'),
(LAST_INSERT_ID(), 'Leave request date', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_available_free_days', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Общо брой дни отпуск', NULL, 'bg'),
(LAST_INSERT_ID(), 'Contract available days off', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'leave_request_days_left_to_use', 'Document', 'basic', 'pattern_plugins', ',37,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Остатък дни за ползване', NULL, 'bg'),
(LAST_INSERT_ID(), 'Days off left to use', NULL, 'en');

########################################################################
# 2012-10-15 - Modified placeholders for 'due_or_recoverable_vat report' for BGService installation (1707)
#            - Fixed duplicated main branches in the customers

# Modified placeholders for 'due_or_recoverable_vat report' for BGService installation (1707)
SELECT @ph_id := id FROM `placeholders` WHERE `varname`='period_expenses_and_incomes' AND `model`='Report' AND `pattern_id`=',220,';

UPDATE `placeholders`
SET `varname`='invoice_expenses_and_incomes', `source`='invoice_expenses_and_incomes'
WHERE id=@ph_id;

UPDATE `placeholders` p, `placeholders_i18n` pi
SET pi.`name`='Разходи/приходи по фактури (таблица)'
WHERE p.id=pi.parent_id AND pi.lang="bg" AND p.id=@ph_id;

UPDATE `placeholders` p, `placeholders_i18n` pi
SET pi.`name`='Expenses/revenues by invoices (table)'
WHERE p.id=pi.parent_id AND pi.lang="en" AND p.id=@ph_id;

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'due_or_recoverable_vat_documents', 'Report', 'send', 'all', ',220,', 'due_or_recoverable_vat_documents', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Документи, генериращи ДДС за внасяне/възстановяване (таблица)', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Documents generating due/recoverable VAT (table)', NULL, 'en');

UPDATE `patterns` p, `patterns_i18n` pi
SET pi.`content`='<p>[invoice_expenses_and_incomes]</p>\r\n<p>[due_or_recoverable_vat_documents]</p>\r\n<p>[due_or_recoverable_vat]</p>\r\n'
WHERE p.id=pi.parent_id AND p.model='Report' AND p.model_type=220;

#Fixed duplicated main branches in the customers
UPDATE customers SET parent_customer = 2818 WHERE parent_customer = 5514;

UPDATE documents SET branch = 302 WHERE branch = 5520;
UPDATE archive_documents SET branch = 302 WHERE branch = 5520;
UPDATE contracts SET branch = 302 WHERE branch = 5520;
UPDATE events SET branch = 302 WHERE branch = 5520;
UPDATE tasks SET branch = 302 WHERE branch = 5520;
UPDATE users SET default_branch = 302 WHERE default_branch = 5520;

UPDATE documents SET branch = 1479 WHERE branch = 5517;
UPDATE archive_documents SET branch = 1479 WHERE branch = 5517;
UPDATE contracts SET branch = 1479 WHERE branch = 5517;
UPDATE events SET branch = 1479 WHERE branch = 5517;
UPDATE tasks SET branch = 1479 WHERE branch = 5517;
UPDATE users SET default_branch = 1479 WHERE default_branch = 5517;

UPDATE documents SET branch = 1564 WHERE branch = 5518;
UPDATE archive_documents SET branch = 1564 WHERE branch = 5518;
UPDATE contracts SET branch = 1564 WHERE branch = 5518;
UPDATE events SET branch = 1564 WHERE branch = 5518;
UPDATE tasks SET branch = 1564 WHERE branch = 5518;
UPDATE users SET default_branch = 1564 WHERE default_branch = 5518;

UPDATE documents SET branch = 1596 WHERE branch = 5519;
UPDATE archive_documents SET branch = 1596 WHERE branch = 5519;
UPDATE contracts SET branch = 1596 WHERE branch = 5519;
UPDATE events SET branch = 1596 WHERE branch = 5519;
UPDATE tasks SET branch = 1596 WHERE branch = 5519;
UPDATE users SET default_branch = 1596 WHERE default_branch = 5519;

UPDATE documents SET branch = 2453 WHERE branch = 5516;
UPDATE archive_documents SET branch = 2453 WHERE branch = 5516;
UPDATE contracts SET branch = 2453 WHERE branch = 5516;
UPDATE events SET branch = 2453 WHERE branch = 5516;
UPDATE tasks SET branch = 2453 WHERE branch = 5516;
UPDATE users SET default_branch = 2453 WHERE default_branch = 5516;

UPDATE documents SET branch = 2818 WHERE branch = 5514;
UPDATE archive_documents SET branch = 2818 WHERE branch = 5514;
UPDATE contracts SET branch = 2818 WHERE branch = 5514;
UPDATE events SET branch = 2818 WHERE branch = 5514;
UPDATE tasks SET branch = 2818 WHERE branch = 5514;
UPDATE users SET default_branch = 2818 WHERE default_branch = 5514;

UPDATE documents SET branch = 4490 WHERE branch = 5515;
UPDATE archive_documents SET branch = 4490 WHERE branch = 5515;
UPDATE contracts SET branch = 4490 WHERE branch = 5515;
UPDATE events SET branch = 4490 WHERE branch = 5515;
UPDATE tasks SET branch = 4490 WHERE branch = 5515;
UPDATE users SET default_branch = 4490 WHERE default_branch = 5515;

DELETE FROM customers_i18n WHERE parent_id IN ( 302, 5520, 1479, 5517, 1564, 5518, 1596, 5519, 2453, 5516, 2818, 5514, 4490, 5515 );
DELETE FROM customers WHERE id IN ( 302, 5520, 1479, 5517, 1564, 5518, 1596, 5519, 2453, 5516, 2818, 5514, 4490, 5515 );

######################################################################################
# 2012-10-17 - Contracts module and part of the finance module have been rebuilt

UPDATE gt2_details SET current = 1 WHERE model = "contract" AND article_id IN (184, 185, 187, 190, 195, 197, 203, 204, 247, 356);

######################################################################################
# 2012-10-28 - Added automation for attaching a trademark to a customer

# Added automation for attaching a trademark to a customer
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, NULL, '0', NULL, '1', 'nomenclatures', NULL, 'action', '4', '', 'condition := \'[a_client_id]\' != \'\'', 'method := attachTrademarkToCustomer\r\ncustomer_id_field := client_id', NULL, '0', '0');

######################################################################################
# 2012-11-20 - Removed wrong links in fin_balance

CREATE TABLE fin_balance_tmp
  SELECT fb.id
  FROM fin_incomes_reasons fir
  JOIN fin_balance fb
    ON fb.parent_id = fir.id AND fb.parent_model_name = "finance_incomes_reason" AND fb.paid_to_model_name = "finance_payment"
    OR fb.paid_to = fir.id AND fb.paid_to_model_name = "finance_incomes_reason" AND fb.parent_model_name = "finance_payment"
  JOIN fin_payments fp
    ON fp.id = IF(fb.parent_model_name = "finance_payment", fb.parent_id, fb.paid_to)
    AND fp.customer != fir.customer

  UNION

  SELECT fb.id
  FROM fin_expenses_reasons fer
  JOIN fin_balance fb
    ON fb.parent_id = fer.id AND fb.parent_model_name = "finance_expenses_reason" AND fb.paid_to_model_name = "finance_payment"
    OR fb.paid_to = fer.id AND fb.paid_to_model_name = "finance_expenses_reason" AND fb.parent_model_name = "finance_payment"
  JOIN fin_payments fp
    ON fp.id = IF(fb.parent_model_name = "finance_payment", fb.parent_id, fb.paid_to)
    AND fp.customer != fer.customer

  UNION

  SELECT fb.id
  FROM fin_expenses_reasons fer
  JOIN fin_balance fb
    ON fb.parent_id = fer.id AND fb.parent_model_name = "finance_expenses_reason" AND fb.paid_to_model_name = "finance_incomes_reason"
    OR fb.paid_to = fer.id AND fb.paid_to_model_name = "finance_expenses_reason" AND fb.parent_model_name = "finance_incomes_reason"
  JOIN fin_incomes_reasons fir
    ON fir.id = IF(fb.parent_model_name = "finance_incomes_reason", fb.parent_id, fb.paid_to)
    AND fir.customer != fer.customer

  UNION

  SELECT fb.id
  FROM fin_expenses_reasons fer
  JOIN fin_balance fb
    ON fb.parent_id = fer.id AND fb.parent_model_name = "finance_expenses_reason" AND fb.paid_to_model_name = "finance_expenses_reason"
  JOIN fin_expenses_reasons fer2
    ON fer2.id = fb.paid_to
    AND fer2.customer != fer.customer

  UNION

  SELECT fb.id
  FROM fin_incomes_reasons fir
  JOIN fin_balance fb
    ON fb.parent_id = fir.id AND fb.parent_model_name = "finance_incomes_reason" AND fb.paid_to_model_name = "finance_incomes_reason"
  JOIN fin_incomes_reasons fir2
    ON fir2.id = fb.paid_to
    AND fir2.customer != fir.customer;
DELETE FROM fin_balance WHERE id IN (SELECT id FROM fin_balance_tmp);
DROP TABLE fin_balance_tmp;

######################################################################################
# 2012-11-21 - Fixed balance links because of the wrong links in fin_balance

# Update paid_amount
UPDATE `fin_balance` SET `paid_amount`=217.01   WHERE `id`=6;
UPDATE `fin_balance` SET `paid_amount`=432.00   WHERE `id`=15;
UPDATE `fin_balance` SET `paid_amount`=414.00   WHERE `id`=21;
UPDATE `fin_balance` SET `paid_amount`=936.00   WHERE `id`=30;
UPDATE `fin_balance` SET `paid_amount`=1800.00  WHERE `id`=32;
UPDATE `fin_balance` SET `paid_amount`=1980.00  WHERE `id`=34;
UPDATE `fin_balance` SET `paid_amount`=96.00    WHERE `id`=51;
UPDATE `fin_balance` SET `paid_amount`=800.40   WHERE `id`=54;
UPDATE `fin_balance` SET `paid_amount`=456.00   WHERE `id`=77;
UPDATE `fin_balance` SET `paid_amount`=2856.00  WHERE `id`=83;
UPDATE `fin_balance` SET `paid_amount`=1068.00  WHERE `id`=132;
UPDATE `fin_balance` SET `paid_amount`=618.96   WHERE `id`=133;
UPDATE `fin_balance` SET `paid_amount`=1221.02  WHERE `id`=212;
UPDATE `fin_balance` SET `paid_amount`=360.00   WHERE `id`=327;
UPDATE `fin_balance` SET `paid_amount`=1770.00  WHERE `id`=530;
UPDATE `fin_balance` SET `paid_amount`=2130.00  WHERE `id`=559;
UPDATE `fin_balance` SET `paid_amount`=1020.00  WHERE `id`=1113;

# Insert deleted rows
INSERT IGNORE INTO `fin_balance` (`id`, `parent_id`, `parent_model_name`, `paid_to`, `paid_to_model_name`, `paid_amount`, `paid_currency`, `added`, `added_by`, `modified`, `modified_by`) VALUES
(16, 20, 'Finance_Payment', 17, 'Finance_Incomes_Reason', 340.80, 'BGN', '2011-09-21 17:12:14', 114, '2011-09-21 17:12:14', 114),
(26, 32, 'Finance_Payment', 44, 'Finance_Incomes_Reason', 696.48, 'BGN', '2011-09-26 17:19:50', 114, '2011-09-26 17:19:50', 114),
(31, 39, 'Finance_Payment', 39, 'Finance_Incomes_Reason', 240.00, 'BGN', '2011-09-27 14:17:55', 114, '2011-09-27 14:17:55', 114),
(50, 67, 'Finance_Payment', 24, 'Finance_Incomes_Reason', 282.00, 'BGN', '2011-09-28 11:57:00', 114, '2011-09-28 11:57:00', 114),
(55, 73, 'Finance_Payment', 21, 'Finance_Incomes_Reason', 579.60, 'BGN', '2011-09-28 14:42:33', 114, '2011-09-28 14:42:33', 114),
(66, 86, 'Finance_Payment', 130, 'Finance_Incomes_Reason', 32.50, 'BGN', '2011-09-30 12:18:06', 6, '2011-09-30 12:18:06', 6),
(68, 88, 'Finance_Payment', 30, 'Finance_Incomes_Reason', 804.00, 'BGN', '2011-09-30 14:21:48', 114, '2011-09-30 14:21:48', 114),
(70, 90, 'Finance_Payment', 123, 'Finance_Incomes_Reason', 201.60, 'BGN', '2011-09-30 14:24:34', 114, '2011-09-30 14:24:34', 114),
(73, 93, 'Finance_Payment', 33, 'Finance_Incomes_Reason', 120.00, 'BGN', '2011-09-30 16:47:38', 114, '2011-09-30 16:47:38', 114),
(74, 94, 'Finance_Payment', 32, 'Finance_Incomes_Reason', 150.00, 'BGN', '2011-09-30 16:58:22', 114, '2011-09-30 16:58:22', 114),
(78, 99, 'Finance_Payment', 161, 'Finance_Incomes_Reason', 119.71, 'BGN', '2011-10-04 14:20:08', 114, '2011-10-04 14:20:08', 114),
(139, 165, 'Finance_Payment', 23, 'Finance_Incomes_Reason', 312.00, 'BGN', '2011-10-10 13:58:46', 114, '2011-10-10 13:58:46', 114),
(176, 203, 'Finance_Payment', 201, 'Finance_Incomes_Reason', 579.00, 'BGN', '2011-10-13 11:47:24', 114, '2011-10-13 11:47:24', 114),
(224, 250, 'Finance_Payment', 35, 'Finance_Incomes_Reason', 780.00, 'BGN', '2011-10-19 11:12:15', 6, '2011-10-19 11:12:15', 6),
(280, 271, 'Finance_Payment', 312, 'Finance_Incomes_Reason', 35.00, 'BGN', '2011-10-20 15:03:30', 114, '2011-10-20 15:03:30', 114),
(455, 428, 'Finance_Payment', 127, 'Finance_Incomes_Reason', 31.20, 'BGN', '2011-11-02 14:59:44', 114, '2011-11-02 14:59:44', 114),
(582, 581, 'Finance_Payment', 31, 'Finance_Incomes_Reason', 480.00, 'BGN', '2011-11-16 16:48:09', 114, '2011-11-16 16:48:09', 114),
(674, 723, 'Finance_Payment', 22, 'Finance_Incomes_Reason', 564.00, 'BGN', '2011-11-25 16:00:49', 114, '2011-11-25 16:00:49', 114),
(816, 859, 'Finance_Payment', 47, 'Finance_Incomes_Reason', 60.00, 'BGN', '2011-12-08 11:36:56', 114, '2011-12-08 11:36:56', 114);

######################################################################################
# 2011-11-27 - Changed pattern plugin for financial documents to use article ids from settings

# Changed pattern plugin for financial documents to use article ids from settings
UPDATE `patterns_plugins` SET `settings`='article_ids := 184, 190, 195, 197, 204, 356'
WHERE `folder`='bgservice' AND `method`='prepareInvoices' AND `settings`='';

########################################################################
# 2012-12-10 - Add new report - 'bgservice_employee_file' for BGService installation (1707)

# Add new report - 'bgservice_employee_file' for BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(250, 'bgservice_employee_file', 'years_var := plr_leave_year\r\n\r\nworking_contract_type := 3\r\ncontract_position := real_position\r\ncontract_education := step_school\r\navailable_days_off := due_leave\r\n\r\ndocument_type_leave_year := 6\r\nfree_days_type := plr_leave_type\r\nfree_days_count := plr_leave_days\r\nfree_days_year := plr_leave_year\r\nfree_days_start_date := plr_leave_start_date\r\nfree_days_end_date := plr_leave_finish_date\r\n\r\nspecial_year_days_off_prefix := specialyear_\r\n\r\nleave_request_paid := paid\r\nleave_request_unpaid := unpaid\r\n\r\ncustomer_special_days_off := special_leave_days\r\ncustomer_special_year_days_off := special_leave_year\r\n\r\ndays_off_substatus_approved := 138\r\ndays_off_substatus_disapproved := 139\r\n\r\nworking_contract_pattern := 45\r\nworking_agreement_pattern := 45\r\nleave_request_pattern_id := 3\r\nsick_rest_pattern_id := \r\n\r\nemployee_picture := empl_picture\r\nemployee_coming_doc_received := doc_received\r\nemployee_coming_doc_received_date := doc_received_date\r\nemployee_coming_doc_received_file := doc_received_file\r\nemployee_departure_doc_received := doc_departure\r\nemployee_coming_doc_received_date := doc_departure_date\r\nemployee_coming_doc_received_file := doc_departure_file\r\n\r\nsickness_rest_document_id := 20\r\nsickness_rest_start_date := shospital_time\r\nsickness_rest_end_date := fhospital_time\r\nsickness_rest_count_days := total_days\r\nsickness_rest_file := file_uploud', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(250, 'Досие на служител', NULL, NULL, 'bg'),
(250, 'Employee file', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 250, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` = 'generate_report' AND `model_type` = 250;

######################################################################################
# 2012-12-18 - Added automation for custom validation when adding a "Leave request" type document using wizard
#            - Changed setting for the additional var plr_leave_year

# Added automation for custom validation when adding a "Leave request" type document using wizard
INSERT INTO `automations` (`module`, `automation_type`, `start_model_type`, `conditions`, `method`, `after_action`, `nums`) VALUES
  ('documents', 'before_action', '6', 'condition := 1', 'plugin := bgservice\r\nmethod := validateAddLeaveRequest', 'cancel_action_on_fail := 1', '0');

# Changed setting for the additional var plr_leave_year
UPDATE `_fields_meta`
  SET `required` = '0'
  WHERE `model`      = 'Document'
    AND `model_type` = '6'
    AND `name`       = 'plr_leave_year';

######################################################################################
# 2013-01-07 - Added automation for notifying when a leave request is approved

# Added automation for notifying when a leave request is approved
INSERT INTO `automations` (`module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `position`, `nums`) VALUES
  ('documents', 'action', '6', 'document_status_approved := closed\r\ndocument_substatus_approved := 138\r\ndocument_status_unapproved := closed\r\ndocument_substatus_unapproved := 139\r\nfield_start_date := plr_leave_start_date\r\nfield_end_date := plr_leave_finish_date\r\nevent_type_keyword := leave\r\nreminder_type := email\r\nreminder_offset_days := 3\r\nreminder_offset_hours := 0\r\nreminder_offset := 0\r\nreminder_users := ', 'condition := 1', 'plugin := bgservice\r\nmethod := notifyLeaveRequest', '0', '0'),
  ('events', 'crontab', '7', '', 'condition := strtotime("[b_event_start]") < time()\r\ncondition := \'[b_status]\' == \'planning\'', 'method := status\r\nnew_status := progress', '1', '1'),
  ('events', 'crontab', '7', '', 'condition := strtotime("[b_event_start]") + [b_duration]*60 < time()\r\ncondition := \'[b_status]\' == \'progress\'', 'method := status\r\nnew_status := finished', '2', '1');

######################################################################################
# 2013-01-24 - Fixed durations of "Leave request" type events

# Fixed durations of "Leave request" type events
UPDATE `events`
  SET `duration`   = '1440',
    `allday_event` = '0',
    `event_start`  = CONCAT(DATE(DATE_ADD(`event_start`, INTERVAL -1 DAY)), ' 22:00:00')
  WHERE `id` IN ('3845', '3850', '3851', '3854', '3872');
UPDATE `events`
  SET `duration`   = '4320',
    `allday_event` = '0',
    `event_start`  = CONCAT(DATE(DATE_ADD(`event_start`, INTERVAL -1 DAY)), ' 22:00:00')
  WHERE `id` = '3852';
UPDATE `events`
  SET `duration`   = '11520',
    `allday_event` = '0',
    `event_start`  = CONCAT(DATE(DATE_ADD(`event_start`, INTERVAL -1 DAY)), ' 22:00:00')
  WHERE `id` = '3853';
UPDATE `events`
  SET `duration`   = '4320',
    `allday_event` = '0',
    `event_start`  = CONCAT(DATE(DATE_ADD(`event_start`, INTERVAL -1 DAY)), ' 22:00:00')
  WHERE `id` = '3855';
UPDATE `events`
  SET `duration`   = '7200',
    `allday_event` = '0',
    `event_start`  = CONCAT(DATE(DATE_ADD(`event_start`, INTERVAL -1 DAY)), ' 22:00:00')
  WHERE `id` = '3875';

######################################################################################
# 2013-02-26 - Updated settings for the 'bgservice_employee_file' report to include data for assigned equipment, working place and office

# Updated settings for the 'bgservice_employee_file' report to include data for assigned equipment, working place and office
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nnomenclature_room := 12\r\nnomenclature_employee := empl_id\r\nnomenclature_work_place := work_place_id\r\n\r\ninventory_report := 81\r\ninventory_employee := empl_name_id\r\ninventory_date := inventory_date\r\ninventory_id := inventory_id\r\ninventory_destination := file_destination\r\n\r\ndestination_received := get_inventory\r\ndestination_returned := return_inventory') WHERE `type`='bgservice_employee_file' AND `settings` NOT LIKE '%nomenclature_room%';

######################################################################################
# 2013-03-07 - Updated 1-day "Leave request" events to be allday events in order to be displayed in calendar

# Updated 1-day "Leave request" events to be allday events in order to be displayed in calendar
UPDATE `events` SET `event_start`=DATE_ADD(`event_start`, INTERVAL 2 HOUR), `allday_event`=1
WHERE `type`=7 and `duration`=1440 and `allday_event`=0;

######################################################################################
# 2013-04-01 - Added new dashlet plugin: "lazy_reporting"

# Added new dashlet plugin: "lazy_reporting"
INSERT INTO `dashlets_plugins` (`type`, `settings`) VALUES
  ('lazy_reporting', 'tasks_type := 6\r\ntasks_status := progress\r\n\r\nlightbox_width := 415\r\nlightbox_height := 280');
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Мързеливо отчитане', 'Инфо панел за по-лесно отчитане на времето по текущите задачи.',  'bg'),
  (LAST_INSERT_ID(), 'Lazy reporting',     'Dashlet for easier reporting of the time for the current tasks.', 'en');

######################################################################################
# 2013-04-11 - Added export of financial documents and payments to Microinvest Delta

# Added export of financial documents, warehouses documents and payments to Microinvest Delta
INSERT INTO `exports` (`id`, `type`, `model`, `model_type`, `settings`, `visible`) VALUES
(41, 'bgservice_to_microinvest', 'Finance_Incomes_Reason', 1, 'export_file_name := invoices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n\r\nadvance_nomenclature := 199\r\nnegative_advance_value := 412-411%', 1),
(42, 'bgservice_to_microinvest', 'Finance_Incomes_Reason', 3, 'export_file_name := credit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n\r\nadvance_nomenclature := 199\r\nnegative_advance_value := 412-411%', 1),
(43, 'bgservice_to_microinvest', 'Finance_Incomes_Reason', 4, 'export_file_name := debit_notices\r\nexport_encoding := windows-1251\r\nexport_delimiter := |\r\nexport_tags := 2\r\nexport_hide_filters := file_name, format, separator\r\n\r\nadvance_nomenclature := 199\r\nnegative_advance_value := 412-411%', 1),
(44, 'bgservice_to_microinvest', 'Finance_Payment', 0, 'export_file_name := payments\r\nexport_encoding := utf-8\r\nexport_delimiter := #\r\nexport_tags := 4\r\nexport_hide_filters := file_name, format, separator\r\n', 1);
INSERT INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(41, 'Експорт на фактури към Microinvest Delta', 'Фактурите се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(41, 'Export of invoices to Microinvest Delta', 'The invoices are exported in two files with special format', 'en'),
(42, 'Експорт на кредитни известия към Microinvest Delta', 'Кредитните известия се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(42, 'Export of Credit Notices to Microinvest Delta', 'The credit notices are exported in two files with special format', 'en'),
(43, 'Експорт на дебитни известия към Microinvest Delta', 'Дебитните известия се експортират по специфичен начин в два отделни файла за общата част и детайлната част', 'bg'),
(43, 'Export of Debit Notices to Microinvest Delta', 'The debit notices are exported in two files with special format', 'en'),
(44, 'Експорт на платежни документи към Microinvest Delta', 'Експортира се разпределението на сумите по финансови документи', 'bg'),
(44, 'Export of payments to Microinvest Delta', 'The payments are exported in special format', 'en');

######################################################################################
# 2013-04-12 - Correct setting name for Microinvest Delta export

# Correct setting name for Microinvest Delta export
UPDATE `exports` SET `settings`=REPLACE(`settings`, 'negative_advance_value', 'positive_advance_value') WHERE `type`='bgservice_to_microinvest' AND `model`='Finance_Incomes_Reason' AND `settings` LIKE '%negative_advance_value%'

######################################################################################
# 2013-04-26 - Added new transition for validating when setting status of a "Request (client)" type document

# Added new transition for validating when setting status of a "Request (client)" type document
INSERT INTO `transitions` (`module`, `action`, `start_model_type`, `conditions`, `method`) VALUES
  ('documents', 'setstatus', '22', 'condition := \'[b_status]\' == \'closed\' && \'[b_substatus]\' == \'closed_49\'', 'method := CheckCondition\r\ncondition := false; $params[\'model\']->getTags(); $valid = (in_array(\'50\', $params[\'model\']->get(\'tags\'))) ? true : false');
INSERT INTO `transitions_i18n` (`parent_id`, `error_text`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Не може да приключите този запис, преди да проверите направените функционалности и да отбележите в заявката, че сте извършили необходимите тестове.', 'bg');

######################################################################################
# 2013-06-11 - Fixed relations between invoices and contracts

# Fixed relations between invoices and contracts
INSERT INTO fin_reasons_relatives (parent_id, parent_model_name, link_to, link_to_model_name) VALUES
    (119, "Finance_Incomes_Reason", 41, "Contract"),
    (113, "Finance_Incomes_Reason", 14, "Contract");

######################################################################################
# 2013-08-07 - For user "yanastasova", mark events as all day events if the duration is more than or equal to one day (1440 minutes), the start time is 00:00:00 and the end time is 00:00:00

# For user "yanastasova", mark events as all day events if the duration is more than or equal to one day (1440 minutes), the start time is 00:00:00 and the end time is 00:00:00
UPDATE `events` AS `e`
  LEFT JOIN `personal_settings` AS `ps`
    ON (`ps`.`user_id`   = `e`.`added_by`
      AND `ps`.`section` = 'interface'
      AND `ps`.`name`    = 'timezone')
  LEFT JOIN `settings` AS `s`
    ON (`s`.`section` = 'calendars'
      AND `s`.`name`  = 'default_timezone')
  SET `e`.`event_start` = CONVERT_TZ(`e`.`event_start`, 'UTC', IF (`ps`.`value` IS NOT NULL AND `ps`.`value` != '', `ps`.`value`, IF(`s`.`value` IS NOT NULL AND `s`.`value` != '', `s`.`value`, 'Europe/Sofia'))),
    `e`.`allday_event` = '1'
  WHERE `e`.`allday_event` != '1'
    AND `e`.`duration` >= '1440'
    AND `e`.`added_by` = '4'
    AND TIME(CONVERT_TZ(`e`.`event_start`, 'UTC', 'Europe/Sofia')) = '00:00:00'
    AND TIME(CONVERT_TZ(DATE_ADD(`e`.`event_start`, INTERVAL `e`.`duration` MINUTE), 'UTC', 'Europe/Sofia')) = '00:00:00';

######################################################################################
# 2013-08-12 - Update "Leave request" documents: copy employee as customer where customer is "BG Service"
#            - Update "Leave request" documents: set status to "approved" where the additional_var "plr_approval" is "approved" too
#            - Update "Leave request" documents: set status to "unapproved" where the additional_var "plr_approval" is "unapproved" too
#            - Delete the data for the additional_var "plr_approval" of "Leave request" documents, where there is no value for it

# Update "Leave request" documents: copy employee as customer where customer is "BG Service"
UPDATE `documents`
  SET `customer` = `employee`
  WHERE `type` = '6'
    AND `customer` != `employee`
    AND `customer` = '5';
# Update "Leave request" documents: set status to "approved" where the additional_var "plr_approval" is "approved" too
UPDATE `documents` AS `d`
  JOIN `documents_cstm` AS `dc`
    ON (`dc`.`model_id` = `d`.`id`
      AND `dc`.`var_id` = '71')
  SET `d`.`substatus` = '138'
  WHERE `dc`.`value` = 'Одобрена'
    AND `d`.`substatus` != '138';
# Update "Leave request" documents: set status to "unapproved" where the additional_var "plr_approval" is "unapproved" too
UPDATE `documents` AS `d`
  JOIN `documents_cstm` AS `dc`
    ON (`dc`.`model_id` = `d`.`id`
      AND `dc`.`var_id` = '71')
  SET `d`.`substatus` = '139'
  WHERE `dc`.`value` = 'Неодобрена'
    AND `d`.`substatus` != '139';
# Delete the data for the additional_var "plr_approval" of "Leave request" documents, where there is no value for it
DELETE FROM `documents_cstm`
  WHERE `var_id` = '71';

########################################################################
# 2013-08-13 - Updated settings for the 'bgservice_suppliers_clients_obligations' report and include setting for nomenclatures which are not supposed to be shown

# Updated settings for the 'bgservice_suppliers_clients_obligations' report and include setting for nomenclatures which are not supposed to be shown
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\nnomenclatures_not_shown := 209,207,206,205,201,200,199,198') WHERE `type`='bgservice_suppliers_clients_obligations' AND `settings` NOT LIKE '%nomenclatures_not_shown%';

########################################################################
# 2013-09-05 - Replace standard automation, for changing the status of "Meeting protocol" type documents, with custom automation, which changes the status and adds timesheets

# Replace standard automation, for changing the status of "Meeting protocol" type documents, with custom automation, which changes the status and adds timesheets
DELETE FROM `automations`
  WHERE `module` = 'documents'
    AND `start_model_type` = '25'
    AND (`automation_type` = 'crontab'
      AND `method` LIKE 'method := status%'
      OR `method` LIKE '%addMeetingProtocolTimesheet%');
INSERT INTO `automations` (`module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  ('documents', 'crontab',       '25', 'customer_type_employee := 1\r\ncp_company := 5\r\ntimesheet_activity := 1', 'condition := "[b_status]" != "closed"\r\ncondition := strtotime("[b_added]")+3*24*60*60 <= time()', 'plugin := bgservice\r\nmethod := addMeetingProtocolTimesheet\r\nnew_status := closed', '', '1', '1'),
  ('documents', 'before_action', '25', 'customer_type_employee := 1\r\ncp_company := 5\r\ntimesheet_activity := 1', 'condition := 1',                                                                                    'plugin := bgservice\r\nmethod := addMeetingProtocolTimesheet\r\nnew_status := closed', 'cancel_action_on_fail := 1', '0', '0');

########################################################################
# 2013-11-26 - Modified the employee file report allowing permissions by department
#            - Added additional conditions that apply preliminary filters to save resources in crontab automations of leave request events

# Modified the employee file report allowing permissions by department
UPDATE `reports` SET settings='years_var := plr_leave_year\r\n\r\nworking_contract_type := 3\r\ncontract_position := real_position\r\ncontract_education := step_school\r\navailable_days_off := due_leave\r\n\r\ndocument_type_leave_year := 6\r\nfree_days_type := plr_leave_type\r\nfree_days_count := plr_leave_days\r\nfree_days_year := plr_leave_year\r\nfree_days_start_date := plr_leave_start_date\r\nfree_days_end_date := plr_leave_finish_date\r\n\r\nspecial_year_days_off_prefix := specialyear_\r\n\r\nleave_request_paid := paid\r\nleave_request_unpaid := unpaid\r\n\r\ncustomer_special_days_off := special_leave_days\r\ncustomer_special_year_days_off := special_leave_year\r\n\r\ndays_off_substatus_approved := 138\r\ndays_off_substatus_disapproved := 139\r\n\r\nworking_contract_pattern := 45\r\nworking_agreement_pattern := 45\r\nleave_request_pattern_id := 3\r\nsick_rest_pattern_id := \r\n\r\nemployee_picture := empl_picture\r\nemployee_coming_doc_received := doc_received\r\nemployee_coming_doc_received_date := doc_received_date\r\nemployee_coming_doc_received_file := doc_received_file\r\nemployee_departure_doc_received := doc_departure\r\nemployee_coming_doc_received_date := doc_departure_date\r\nemployee_coming_doc_received_file := doc_departure_file\r\n\r\nsickness_rest_document_id := 20\r\nsickness_rest_start_date := shospital_time\r\nsickness_rest_end_date := fhospital_time\r\nsickness_rest_count_days := total_days\r\nsickness_rest_file := file_uploud\r\n\r\nuser_3 := department_19\r\nuser_4 := user_57,user_93,user_122,user_133,user_134\r\nuser_143 := user_97,user_98,user_127,user_132\r\n\r\nnomenclature_room := 12\r\nnomenclature_employee := empl_id\r\nnomenclature_work_place := work_place_id\r\n\r\ninventory_report := 81\r\ninventory_employee := empl_name_id\r\ninventory_date := inventory_date\r\ninventory_id := inventory_id\r\ninventory_destination := file_destination\r\n\r\ndestination_received := get_inventory\r\ndestination_returned := return_inventory' WHERE type='bgservice_employee_file';

# Added additional conditions that apply preliminary filters to save resources in crontab automations of leave request events
UPDATE automations SET conditions='condition := strtotime("[b_event_start]") < time()\r\ncondition := \'[b_status]\' == \'planning\'\r\nfilter_sql_condition := event_start < NOW()\r\nfilter_sql_condition := status = \'planning\'' WHERE module='events' AND start_model_type=7 AND method like '%new_status := progress%';
UPDATE automations SET conditions='condition := strtotime("[b_event_start]") + [b_duration]*60 < time()\r\ncondition := \'[b_status]\' == \'progress\'\r\nfilter_sql_condition := event_start < NOW()\r\nfilter_sql_condition := status = \'progress\'' WHERE module='events' AND start_model_type=7 AND method like '%new_status := finished%';

########################################################################
# 2013-11-27 - New dashlet plugin has been added for campaigns calls

# New dashlet plugin has been added for campaigns calls
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
    (NULL, 'campaign_calls', NULL, '0', '1');
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Обаждания по кампании', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Campaign calls', NULL, 'en');

########################################################################
# 2013-12-09 - Modified incorrect settings in employee file report

# Modified incorrect settings in employee file report
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'employee_coming_doc_received_date := doc_departure_date', 'employee_departure_doc_received_date := doc_departure_date') WHERE `type`='bgservice_employee_file' AND `settings` LIKE '%employee_coming_doc_received_date := doc_departure_date%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'employee_coming_doc_received_file := doc_departure_file', 'employee_departure_doc_received_file := doc_departure_file') WHERE `type`='bgservice_employee_file' AND `settings` LIKE '%employee_coming_doc_received_file := doc_departure_file%';

########################################################################
# 2013-12-18 - Added report for goods movement based on revenue and expense invoices
#            - Made 'article_name' and 'article_measure_name' fields searchable for incoming/outgoing Invoice, Credit note and Debit note

# Added report for goods movement based on revenue and expense invoices
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(283, 'bgservice_goods_movement', 'nom_type_incomes_expenses := 7\r\nnom_cat_incomes := 5\r\ncurrency_native := лв.', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(283, 'Движение на стоки', NULL, NULL, 'bg'),
(283, 'Goods movement', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 283, 0, 1),
  ('reports', 'export', 283, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = '283';

# Made 'article_name' and 'article_measure_name' fields searchable for incoming/outgoing Invoice, Credit note and Debit note
UPDATE `_fields_meta` SET `searchable`='autocompleter' WHERE `name`='article_name' AND (`model`='Finance_Incomes_Reason' AND `model_type` IN (1,3,4) OR `model`='Finance_Expenses_Reason' AND `model_type` IN (20,22,23));
UPDATE `_fields_meta` SET `searchable`='dropdown' WHERE `name`='article_measure_name' AND (`model`='Finance_Incomes_Reason' AND `model_type` IN (1,3,4) OR `model`='Finance_Expenses_Reason' AND `model_type` IN (20,22,23));

########################################################################
# 2013-12-20 - Removed setting for native currency from "Goods movement" report

# Removed setting for native currency from "Goods movement" report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ncurrency_native := лв.', '')
WHERE `type`='bgservice_goods_movement' AND `settings` LIKE '%\r\ncurrency_native := лв.%';

########################################################################
# 2014-01-14 - Added automation for assigning users and setting a project to "Request (client)" type documents after adding them

# Added automation for assigning users and setting a project to "Request (client)" type documents after adding them
INSERT INTO `automations` (`module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `position`, `nums`) VALUES
  ('documents', 'action', '22', 'project_type_support_nzoom := 3', 'condition := in_array($this->registry->get(\'action\'), array(\'add\', \'addportal\'))', 'plugin := bgservice\r\nmethod := afterAddRequestClient', '1', '0');

########################################################################
# 2014-01-15 - Added roles definitions for report for goods movement

# Added roles definitions for report for goods movement
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 283, 0, 1),
  ('reports', 'export', 283, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` IN ('generate_report', 'export')
      AND `model_type` = 283;

########################################################################
# 2014-01-29 - Fixed current column for GT2 data in contracts, templates, invoices, credits, debits

# Fixed current column for GT2 data in contracts, templates, invoices, credits, debits
UPDATE gt2_details g
JOIN fin_invoices_templates f
  on g.model_id = f.id AND f.contract_id NOT IN (134,181,513,514,526,530,531,558,587,597,633,643,708,709,710,711,738,740,754,755,757)
SET current = 1
WHERE g.model = "Finance_Invoices_Template" AND g.article_id IN (184,187,185,197,190,204,195,203,477,247,464,356,216,472,463,246,550);

UPDATE gt2_details g
JOIN fin_incomes_reasons f
  on g.model_id = f.id
JOIN fin_invoices_templates_info i
  ON i.invoice_id = g.model_id AND i.contract_id NOT IN (134,181,513,514,526,530,531,558,587,597,633,643,708,709,710,711,738,740,754,755,757)
SET current = 1
WHERE g.model = "Finance_Incomes_Reason" AND g.article_id IN (184,187,185,197,190,204,195,203,477,247,464,356,216,472,463,246,550);

UPDATE gt2_details g
JOIN fin_reasons_relatives r
  on r.parent_model_name = g.model AND r.link_to_model_name = g.model AND r.parent_id = g.id
JOIN fin_invoices_templates_info i
  ON i.invoice_id = r.link_to AND i.contract_id NOT IN (134,181,513,514,526,530,531,558,587,597,633,643,708,709,710,711,738,740,754,755,757)
SET current = 1
WHERE g.model = "Finance_Incomes_Reason" AND g.article_id IN (184,187,185,197,190,204,195,203,477,247,464,356,216,472,463,246,550);

update gt2_details g
join fin_invoices_templates f
  on g.model_id = f.id AND f.contract_id IN (134, 526, 530, 531) AND f.recurrent > 0
left join fin_invoices_templates_info i
  ON i.parent_id = f.id
left join gt2_details g1
  ON g1.model = "Finance_Incomes_Reason" AND g1.model_id = i.invoice_id
SET g.current = 1, g1.current = 1
WHERE g.model = "Finance_Invoices_Template";

INSERT INTO `_variables_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `system`, `position`, `width`, `height`) VALUES
    (NULL, 'Contract', '1', 'tpl_editable_fields', NULL, NULL, 'text', '1', NULL, NULL, NULL);
INSERT INTO `_variables_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
    (LAST_INSERT_ID(), 'label', 'Допълнителни редактируеми полета за шаблони', 'bg');
INSERT INTO `_variables_cstm` (`model`, `model_id`, `var_id`, `value`, `from_date`, `to_date`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
    ('Contract', '0', LAST_INSERT_ID(), 'article_description', NULL, NULL, NOW(), '1', NOW(), '1', '');

########################################################################
# 2014-03-05 - Updated settings for universal report 'timesheets_per_employees_and_customers'

# Updated settings for universal report 'timesheets_per_employees_and_customers'
UPDATE `reports` SET `settings`='documents_types := 34,96\r\nprojects_types := 2\r\ntasks_types :=\r\n\r\nshow_billing_time := 1\r\ninclude_partial_periods := 0\r\n\r\nlawyer_installation := 0\r\n\r\n#allows_files_generation := 1\r\n#files_origin := self' WHERE `type`='timesheets_per_employees_and_customers';

########################################################################
# 2014-04-04 - Added automations for controlling clients' requests for BG Service installation (1707)

# Added automations for controlling clients' requests for BG Service installation (1707)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
('Управление на клиентски заявки (промяна на срокове) - стъпка 1', 0, NULL, 1, 'documents', NULL, 'action', '22', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ntype_deadline := offset\r\nhours_offset := 8', 'condition := ''[prev_b_id]'' != ''[b_id]''', 'plugin := bgservice\r\nmethod := changeRequestsDeadlines', NULL, 10, 0),
('Управление на клиентски заявки (промяна на срокове) - стъпка 2', 0, NULL, 1, 'documents', NULL, 'action', '22', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ntype_deadline := offset\r\nhours_offset := 27', 'condition := [prev_b_substatus] != 46\r\ncondition := [b_substatus] == 46', 'plugin := bgservice\r\nmethod := changeRequestsDeadlines', NULL, 15, 0),
('Управление на клиентски заявки (промяна на срокове) - стъпка 3', 0, NULL, 1, 'documents', NULL, 'action', '22', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ntype_deadline := offset\r\nhours_offset := 27', 'condition := [prev_b_substatus] != 48\r\ncondition := [b_substatus] == 48', 'plugin := bgservice\r\nmethod := changeRequestsDeadlines', NULL, 20, 0),
('Управление на клиентски заявки (промяна на срокове) - стъпка 4', 0, NULL, 1, 'documents', NULL, 'action', '22', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ntype_deadline := offset\r\nhours_offset := 27', 'condition := (([prev_b_substatus] != 126 && [b_substatus] == 126) || ([prev_b_substatus] != 147 && [b_substatus] == 147)) || (([prev_b_substatus] == 126 && [b_substatus] == 126) || ([prev_b_substatus] == 147 && [b_substatus] == 147))', 'plugin := bgservice\r\nmethod := changeRequestsDeadlines', NULL, 25, 0),
('Управление на клиентски заявки (промяна на срокове) - стъпка 5', 0, NULL, 1, 'documents', NULL, 'action', '22', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ntype_deadline := offset\r\nhours_offset := 180', 'condition := [prev_b_substatus] != 146\r\ncondition := [b_substatus] == 146', 'plugin := bgservice\r\nmethod := changeRequestsDeadlines', NULL, 30, 0),
('Управление на клиентски заявки (промяна на срокове) - стъпка 6', 0, NULL, 1, 'documents', NULL, 'action', '22', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ntype_deadline := update\r\nhours_offset :=', 'condition := [prev_b_substatus] != 47\r\ncondition := [b_substatus] == 47', 'plugin := bgservice\r\nmethod := changeRequestsDeadlines', NULL, 35, 0);

########################################################################
# 2014-04-17 - Added automation for controlling clients request statuses which are transformed into other documents in BG Service installation (1707)

# Added automation for controlling clients request statuses which are transformed into other documents in BG Service installation (1707)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
('Управление на клиентски заявки (промяна на срокове след трансформация)', 0, NULL, 1, 'documents', NULL, 'crontab', '22', 'minutes_interval := 30\r\nrelated_automations := 70,71,72,73,74,75\r\n\r\nworking_day_start := 09:00\r\nworking_day_end := 18:00\r\n\r\ntype_deadline_146 := offset\r\nhours_offset_146 := 180', 'condition := 1', 'plugin := bgservice\r\nmethod := changeTransformedRequestsDeadlines', NULL, 0, 0);

########################################################################
# 2014-04-29 - Update the name and the settings of the remote work report fo BG Service installation (1707)

# Update the name and the settings of the 'bgservice_remote_work' report fo BG Service installation (1707)
UPDATE `reports_i18n` SET `name` = 'Отчетено време (поддръжка)' WHERE `parent_id`=(SELECT id FROM `reports` WHERE `type`='bgservice_remote_work') AND `lang` = 'bg';
UPDATE `reports_i18n` SET `name` = 'Timesheets (support)' WHERE `parent_id`=(SELECT id FROM `reports` WHERE `type`='bgservice_remote_work') AND `lang` = 'en';
UPDATE `reports` SET `settings` = 'allows_files_generation := 1\r\n\r\ndocument_type_remote_work_id := 85\r\ndocument_type_visit_id := 5\r\n\r\nremote_work_date_work := date_work\r\nremote_work_work_customer := customer_id\r\nremote_work_start_working_time := start_work\r\nremote_work_end_working_time := finish_work\r\nremote_work_description := task_description\r\nremote_work_customer_object := customer_obect\r\n\r\nvisit_start_work := ppp_start_work\r\nvisit_end_work := ppp_end_work\r\nvisit_employee := ppp_worker\r\nvisit_description := ppp_service_description\r\n\r\nworking_day_start := 09:00\r\nworking_day_end := 18:00'  WHERE `type`='bgservice_remote_work';

########################################################################
# 2014-05-15 - Update the settings of the 'bgservice_requests' report fo BG Service installation (1707)

# Update the settings of the 'bgservice_requests' report fo BG Service installation (1707)
UPDATE `reports` SET `settings` = 'type_request_client := 22\r\ntype_request := 96\r\nrequest_client_description := description_inq\r\nrequest_description := new_func_desc\r\nworking_time_starts := 09:00\r\nworking_time_ends := 18:00\r\nbugzilla_base_link := http://bugzilla.bgservice.net/bugzilla3/show_bug.cgi?id=\r\n\r\nusers_departments := 20,21' WHERE `type`='bgservice_requests';

########################################################################
# 2014-05-26 - Added new automation to calculate the end date for projects type 1 in BG Service installation (1707)

# Added new automation to calculate the end date for projects type 1 in BG Service installation (1707)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
('Краен срок за внедряване', 0, NULL, 1, 'projects', NULL, 'action', '1', 'start_project_date := start_term_date\r\ncount_project_days := start_term_days\r\nproject_term_type := start_term_type\r\n\r\ntype_calendar_days := 1\r\ntype_working_days := 2', 'condition := [a_start_term_date] != '''' && [a_start_term_date] != ''0''\r\ncondition := [a_start_term_days] != '''' && [a_start_term_days] != ''0''\r\ncondition := [a_start_term_type] != '''' && [a_start_term_type] != ''0''', 'plugin := bgservice\r\nmethod := implementationDeadline', NULL, 1, 1);

######################################################################################
# 2014-07-16 - Report 'bgservice_customer_file' is renamed to 'customer_file' and its settings are moved to DB

# Report 'bgservice_customer_file' is renamed to 'customer_file' and its settings are moved to DB
UPDATE `reports` SET `type`='customer_file', `settings`='document_calls_list := 102\r\ndocument_meetings_protocol := 25\r\nevents_meetings := 1,3,4\r\ndocuments_offer_deals := 13,32,38\r\nprojects_types := 1,2,3,4,6,8,13\r\nproject_commercial_campaign_type := 4\r\ntasks_types := 2\r\n\r\ncall_customer_id := customer_id\r\ncall_date := list_to_date\r\ncall_description := call_description\r\ncall_status := list_call_status\r\ncall_comment := list_coment\r\nmeeting_protocol_date := meeting_start\r\nmeeting_protocol_next_step := next_step\r\nmeeting_protocol_customer := our_participants\r\npreposal_employee := idoc_consultant\r\nproject_commercial_campaign_customer := customer_id' WHERE `type`='bgservice_customer_file';

######################################################################################
# 2014-08-24 - Excluded system event types from options of a variable

# Excluded system event types from options of a variable
UPDATE `_fields_meta` SET `source`=CONCAT(`source`, '\nwhere := t.keyword NOT IN (''reminder'', ''plannedtime'')')
WHERE `model`='Nomenclature' AND `model_type`=15 AND `name`='list_call_addtype_subtype' AND `source` NOT LIKE '%where :=%';

######################################################################################
# 2014-08-25 - Fixed missing measures in contracts and invoices_templates

update gt2_details g
join  gt2_details_i18n gi
on gi.parent_id = g.id and gi.article_measure_name = ""
set gi.article_measure_name = 1
where model="contract" || model = "finance_invoices_template";

######################################################################################
# 2014-09-09 - Change settings for automation: addMeetingProtocolTimesheet

# Change settings for automation: addMeetingProtocolTimesheet
UPDATE automations
  SET settings = CONCAT(settings, '\r\nfield_doc_meeting_start := meeting_start\r\nfield_doc_meeting_finish := meeting_finish\r\nfield_doc_person_id := person_id\r\nfield_cus_link_contact_persons_id := link_contact_persons_id'),
    conditions = IF(automation_type = 'crontab', conditions, 'condition := \'[action]\' == \'setstatus\' && \'[request_is_post]\' == \'1\'')
  WHERE method LIKE '%addMeetingProtocolTimesheet%'
    AND settings NOT LIKE '%field_doc_meeting_finish%';

######################################################################################
# 2014-11-05 - Added automation that creates tasks from meeting protocol upon manual status change or 3 days after beeing added (crontab)
#            - Added automation that validates task fields in  meeting protocol

# Added automation that creates tasks from meeting protocol upon manual status change or 3 days after beeing added (crontab)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Създаване на (мини) задачи от протокол от среща', 0, NULL, 1, 'documents', NULL, 'before_action', '25', 'create_model := task\r\ntask_type := 2\r\ntask_name := discussion_theme\r\ntask_finish_date := discussion_term\r\ntask_description := discussion_decision\r\ntask_assignee := performer_name_id\r\n', 'condition := \'[request_is_post]\' == \'1\'\r\ncondition := [action] == \'setstatus\'\r\ncondition := $this->registry->get(\'request\')->get(\'status\') == \'closed\'\r\n', 'plugin := bgservice\r\nmethod := createTasks\r\nnew_status := closed\r\n', 'cancel_action_on_fail := 1', 1, 1, 1),
(NULL, 'Създаване на (мини) задачи от протокол от среща (3 дни след добавяне)', 0, NULL, 1, 'documents', NULL, 'crontab', '25', 'create_model := task\r\ntask_type := 2\r\ntask_name := discussion_theme\r\ntask_finish_date := discussion_term\r\ntask_description := discussion_decision\r\ntask_assignee := performer_name_id\r\n#send_to_email := <EMAIL>', 'condition := "[b_status]" != "closed"\r\ncondition := strtotime("[b_added]")+3*24*60*60 <= time()\r\nfilter_sql_condition := status != "closed"\r\nfilter_sql_condition := DATE_ADD(added, INTERVAL 3 DAY) <= NOW()', 'plugin := bgservice\r\nmethod := createTasks\r\nnew_status := closed\r\n', '', 1, 1, 1),
(NULL, 'Валидация на данни за задачи в протокол от среща', 0, NULL, 1, 'documents', NULL, 'before_action', '25', 'task_finish_date := discussion_term\r\ntask_assignee := performer_name_id', 'condition := \'[request_is_post]\' == \'1\'\r\ncondition := in_array(\'[action]\', array(\'add\', \'edit\'))\r\n', 'plugin := bgservice\r\nmethod := validateProtocolTaskFields\r\n', 'cancel_action_on_fail := 1', 1, 0, 1);

######################################################################################
# 2014-11-10 - Fixed the sequence of two correlated automations

# Fixed the sequence of two correlated automations
UPDATE automations SET conditions=CONCAT(conditions, '\r\nfilter_sql_condition := status != "closed"\r\nfilter_sql_condition := DATE_ADD(added, INTERVAL 3 DAY) <= NOW()') WHERE start_model_type=25 AND automation_type='crontab' AND method like '%addMeetingProtocolTimesheet%' AND conditions NOT LIKE '%filter_sql_condition%';
UPDATE automations SET position=0 WHERE start_model_type=25 AND method LIKE '%createTasks%' AND automation_type='crontab';

######################################################################################
# 2014-11-11 - Fixed the dependency of two correlated automations(addMeetingProtocolTimesheet depends on createTasks)

# Fixed the dependency of two correlated automations(addMeetingProtocolTimesheet depends on createTasks)
UPDATE automations a1, automations a2
SET a1.depend=a2.id
WHERE a2.start_model_type=25 AND a2.method LIKE '%createTasks%' AND a2.automation_type='crontab' AND
a1.start_model_type=25 AND a1.method LIKE '%addMeetingProtocolTimesheet%' AND a1.automation_type='crontab';

######################################################################################
# 2014-11-18 - Added validation of task name field

# Added validation of task name field
UPDATE `automations` SET `settings`=CONCAT(settings, '\r\ntask_name := discussion_theme') WHERE method LIKE '%validateProtocolTaskFields%' AND settings NOT LIKE '%task_name := discussion_theme%';

######################################################################################
# 2014-02-23 - Created automation for different models creation. Automations for (mini)tasks creation have been substituted

#Created automation for different models creation. Automations for (mini)tasks creation have been substituted
DELETE FROM automations WHERE id IN (95, 96);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
    (95, 'Създаване на (мини) задачи от протокол от среща', 0, NULL, 1, 'documents', NULL, 'before_action', '25', '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := php(preg_replace(''#\\r\\n|\\r|\\n#'', '' '', ''[a_discussion_theme]''))\r\ndescription := [a_discussion_decision]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\ndepartment := php($db->GetOne(''SELECT default_department FROM users WHERE id = [a_performer_name_id]'') || PH_DEPARTMENT_FIRST)\r\ngroup := php($db->GetOne(''SELECT default_group FROM users WHERE id = [a_performer_name_id]'') || PH_GROUP_FIRST)\r\nplanned_start_date := php(date(''Y-m-d H:i:s''))\r\nplanned_finish_date := [a_discussion_term] 18:00:00\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]\r\n\r\n# assignments to be thrown to assign automation method\r\nnew_assign_owner := [a_performer_name_id]\r\nnew_assign_observer := [assignments_decision]\r\n\r\n# field in a grouping table that defines how much records will be created (OPTIONAL)\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\n# validation rules for new model fields - must return true/false (OPTIONAL)\r\nvalidate_planned_finish_date := > date(''Y-m-d 18:00:00'')', 'condition := ''[request_is_post]'' && ''[action]'' == ''setstatus'' && $this->registry->get(''request'')->get(''status'') == ''closed'' && ''[b_status]'' != ''closed''\n', 'method := createModels\r\n', 'cancel_action_on_fail := 1', 1, 1, 1),
    (96, 'Създаване на (мини) задачи от протокол от среща (3 дни след добавяне)', 0, NULL, 1, 'documents', NULL, 'crontab', '25', '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := php(preg_replace(''#\\r\\n|\\r|\\n#'', '' '', ''[a_discussion_theme]''))\r\ndescription := [a_discussion_decision]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\ndepartment := php($db->GetOne(''SELECT default_department FROM users WHERE id = [a_performer_name_id]'') || PH_DEPARTMENT_FIRST)\r\ngroup := php($db->GetOne(''SELECT default_group FROM users WHERE id = [a_performer_name_id]'') || PH_GROUP_FIRST)\r\nplanned_start_date := php(date(''Y-m-d H:i:s''))\r\nplanned_finish_date := [a_discussion_term] 18:00:00\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]\r\n\r\n# assignments to be thrown to assign automation method\r\nnew_assign_owner := [a_performer_name_id]\r\nnew_assign_observer := [assignments_decision]\r\n\r\n# field in a grouping table that defines how much records will be created (OPTIONAL)\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\n# validation rules for new model fields - must return true/false (OPTIONAL)\r\nvalidate_planned_finish_date := > date(''Y-m-d 18:00:00'')\r\n\r\n# set this status of the source model\r\nnew_status := closed\r\n\r\nsend_to_email := <EMAIL>', 'condition := "[b_status]" != "closed"\r\ncondition := strtotime("[b_added]")+3*24*60*60 <= time()\r\nfilter_sql_condition := status != "closed"\r\nfilter_sql_condition := DATE_ADD(added, INTERVAL 3 DAY) <= NOW()', 'method := createModels\r\n', '', 0, 1, 1);

######################################################################################
# 2014-03-19 - Changed settings for create models automation

# Changed settings for create models automation
UPDATE automations SET settings = '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := php(preg_replace(''#\\r\\n|\\r|\\n#'', '' '', ''[a_discussion_theme]''))\r\ndescription := [a_discussion_decision]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\ndepartment := php($db->GetOne(''SELECT default_department FROM users WHERE id = "[a_performer_name_id]"'') || PH_DEPARTMENT_FIRST)\r\ngroup := php($db->GetOne(''SELECT default_group FROM users WHERE id = "[a_performer_name_id]"'') || PH_GROUP_FIRST)\r\nplanned_start_date := php(date(''Y-m-d H:i:s''))\r\nplanned_finish_date := [a_discussion_term] 18:00:00\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]\r\n\r\n#conditions to be checked to create a model\r\ncreate_condition := !empty(''[a_performer_name_id]'')\r\n\r\n# assignments to be thrown to assign automation method\r\nnew_assign_owner := [a_performer_name_id]\r\nnew_assign_observer := [assignments_decision]\r\n\r\n# field in a grouping table that defines how much records will be created (OPTIONAL)\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\n# validation rules for new model fields - must return true/false (OPTIONAL)\r\nvalidate := !empty([name]) && !empty([description]) && [planned_finish_date] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!' WHERE id = 95;
UPDATE automations SET settings = '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := php(preg_replace(''#\\r\\n|\\r|\\n#'', '' '', ''[a_discussion_theme]''))\r\ndescription := [a_discussion_decision]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\ndepartment := php($db->GetOne(''SELECT default_department FROM users WHERE id = [a_performer_name_id]'') || PH_DEPARTMENT_FIRST)\r\ngroup := php($db->GetOne(''SELECT default_group FROM users WHERE id = [a_performer_name_id]'') || PH_GROUP_FIRST)\r\nplanned_start_date := php(date(''Y-m-d H:i:s''))\r\nplanned_finish_date := [a_discussion_term] 18:00:00\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]\r\n\r\n#conditions to be checked to create a model\r\ncreate_condition := !empty(''[a_performer_name_id]'')\r\n\r\n# assignments to be thrown to assign automation method\r\nnew_assign_owner := [a_performer_name_id]\r\nnew_assign_observer := [assignments_decision]\r\n\r\n# field in a grouping table that defines how much records will be created (OPTIONAL)\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\n# validation rules for new model fields - must return true/false (OPTIONAL)\r\nvalidate := !empty([name]) && !empty([description]) && [planned_finish_date] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!\r\n\r\n# set this status of the source model\r\nnew_status := closed\r\n\r\nsend_to_email := <EMAIL>' WHERE id = 96;
DELETE FROM automations WHERE id = 97;

######################################################################################
# 2014-03-24 - Changed settings for create models automation

# Changed settings for create models automation
UPDATE automations SET settings = '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := php(preg_replace(''#\\r\\n|\\r|\\n#'', '' '', ''[a_discussion_theme]''))\r\ndescription := [a_discussion_decision]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\ndepartment := php($db->GetOne(''SELECT default_department FROM users WHERE id = "[a_performer_name_id]"'') || PH_DEPARTMENT_FIRST)\r\ngroup := php($db->GetOne(''SELECT default_group FROM users WHERE id = "[a_performer_name_id]"'') || PH_GROUP_FIRST)\r\nplanned_start_date := php(date(''Y-m-d H:i:s''))\r\nplanned_finish_date := [a_discussion_term] 18:00:00\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]\r\n\r\n#conditions to be checked to create a model\r\ncreate_condition := ''[a_performer_name_id]'' !=''''\r\n\r\n# assignments to be thrown to assign automation method\r\nnew_assign_owner := [a_performer_name_id]\r\nnew_assign_observer := [assignments_decision]\r\n\r\n# field in a grouping table that defines how much records will be created (OPTIONAL)\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\n# validation rules for new model fields - must return true/false (OPTIONAL)\r\nvalidate := !empty([name]) && !empty([description]) && [planned_finish_date] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!' WHERE id = 95;
UPDATE automations SET settings = '# model to be created\r\ncreate_model := task\r\n\r\n#fields relations\r\n#    [a_*] for additional variable\r\n#    [b_*] for basic variable\r\n#    php(*) - php code do be executed - execution result will be field value\r\n#    any other text  - will be provided as value\r\n#    combinations of the above rules are accepted\r\ntype := 2\r\nname := php(preg_replace(''#\\r\\n|\\r|\\n#'', '' '', ''[a_discussion_theme]''))\r\ndescription := [a_discussion_decision]\r\ncustomer := [b_customer]\r\nbranch := [b_branch]\r\ncontact_person := [b_contact_person]\r\nproject := [b_project]\r\nphase := [b_phase]\r\ndepartment := php($db->GetOne(''SELECT default_department FROM users WHERE id = [a_performer_name_id]'') || PH_DEPARTMENT_FIRST)\r\ngroup := php($db->GetOne(''SELECT default_group FROM users WHERE id = [a_performer_name_id]'') || PH_GROUP_FIRST)\r\nplanned_start_date := php(date(''Y-m-d H:i:s''))\r\nplanned_finish_date := [a_discussion_term] 18:00:00\r\n\r\n# this is needed to set task as document relative\r\ncreate_from_document := [b_id]\r\n\r\n#conditions to be checked to create a model\r\ncreate_condition := ''[a_performer_name_id]'' !=''''\r\n\r\n# assignments to be thrown to assign automation method\r\nnew_assign_owner := [a_performer_name_id]\r\nnew_assign_observer := [assignments_decision]\r\n\r\n# field in a grouping table that defines how much records will be created (OPTIONAL)\r\nfield_defining_multiple := [a_discussion_decision]\r\n\r\n# validation rules for new model fields - must return true/false (OPTIONAL)\r\nvalidate := !empty([name]) && !empty([description]) && [planned_finish_date] > date(''Y-m-d 18:00:00'')\r\nvalidate_message_bg := Грешка на ред [row]: Моля попълнете полетата "Тема" и "Решение/задачи"! Полето "Срок" трябва да е попълнено с дата по-голяма от днешната!\r\n\r\n# set this status of the source model\r\nnew_status := closed\r\n\r\nsend_to_email := <EMAIL>' WHERE id = 96;

######################################################################################
# 2015-06-30 - Updated 'bgservice_remote_work' report for BG Service installation (BGS) with additional settings for employees working time

# Updated 'bgservice_remote_work' report for BG Service installation (BGS) with additional settings for employees working time
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nemployee_working_from := working_from\r\nemployee_working_to := working_to') WHERE `type`='bgservice_remote_work' AND `settings` NOT LIKE '%employee_working_from%';

######################################################################################
# 2015-07-30 - Add automation to remind for cars dates

# Add automation to remind for cars dates
INSERT INTO automations
  SET automation_type = 'crontab',
    module = 'nomenclatures',
    start_model_type = '2',
    name = 'Напомняния за наближаващи дати: ГО, ГТП и данък',
    settings = 'period := 1 day\r\nstart_time := 03:00\r\n\r\nreminders_days := 10,5\r\nremind_users := 5,57,66,201\r\n\r\nfield_nom_vehicle_insurer_date_finish := insurer_date_finish\r\nfield_nom_vehicle_insurer_payment := insurer_payment\r\nfield_nom_vehicle_technical_review_finish := technical_review_finish\r\nfield_nom_vehicle_tax_finish := tax_finish',
    conditions = 'condition := 1',
    method = 'plugin := bgservice\r\nmethod := remindCarDates',
    nums = 0;

INSERT INTO emails
  SET model = 'Nomenclature',
    model_type = 2,
    name = 'automations_plugin_bgservice_remindcardates',
    active = 1,
    `group` = 1,
    added = NOW(),
    added_by = 1,
    modified = NOW(),
    modified_by = 1;
SELECT @pattern_id := LAST_INSERT_ID();
INSERT INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
  (@pattern_id, 'Напомняне за [nomenclature_type_name] „[nomenclature_name]“ с рег. №: [nomenclature_code]', '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n  <tbody>\r\n    <tr>\r\n      <td>Здравейте, [user_name],</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>Имате напомняне за <a href="[nomenclature_view_url]">[nomenclature_type_name] „[nomenclature_name]“ с рег. №: [nomenclature_code]</a> <em>(добавил/а [nomenclature_added_by])</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>[reminder_description]</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td><strong>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на <strong>nZoom</strong>.</td>\r\n    </tr>\r\n    <tr>\r\n      <td><em>При възникнали проблеми можете да <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">изпратите e-mail</a> на екипа за поддръжка на <strong>nZoom</strong>.</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n  </tbody>\r\n</table>\r\n', 'Напомняне за номенклатура.', 'bg', NOW()),
  (@pattern_id, 'Reminder for [nomenclature_type_name] "[nomenclature_name]" with reg. No: [nomenclature_code]', '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n  <tbody>\r\n    <tr>\r\n      <td>Hello, [user_name],</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>You have a reminder for <a href="[nomenclature_view_url]">[nomenclature_type_name] "[nomenclature_name]" with reg. No: [nomenclature_code]</a> <em>(added by [nomenclature_added_by])</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td>[reminder_description]</td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n    <tr>\r\n      <td><strong>PLEASE, DO NOT REPLY TO THIS MESSAGE!</strong> It was sent by the <strong>nZoom</strong>&#39;s notification system from an unmonitored e-mail address. Mail sent to this address cannot be answered.</td>\r\n    </tr>\r\n    <tr>\r\n      <td><em>Should you encounter any problems, please do not hesitate to <a href="mailto:<EMAIL>?subject=Contact%20Form%20-%20nZoom%20Notification%20System">send an e-mail back</a> to the <strong>nZoom</strong> support team.</em></td>\r\n    </tr>\r\n    <tr>\r\n      <td>&nbsp;</td>\r\n    </tr>\r\n  </tbody>\r\n</table>\r\n', 'Reminder of nomenclature.', 'en', NOW());

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_type_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Име на типа номенклатура', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature type name', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('user_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Имена на потребителя', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Name of user', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_name', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Име на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature name', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_code', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Код на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Nomenclature code', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_view_url', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Адресът за достъпване на номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'The address for accessing the nomenclature', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('nomenclature_added_by', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Имена на потребителя добавил номенклатурата', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Name of the user added the nomenclature', NULL, 'en');

INSERT INTO placeholders (varname, model, `type`, `usage`, pattern_id, source, multilang) VALUES
  ('reminder_description', 'Nomenclature', 'basic', 'emails', CONCAT(',', @pattern_id, ','), '', 0);
INSERT INTO placeholders_i18n (parent_id, name, description, lang) VALUES
  (LAST_INSERT_ID(), 'Напомняне: описание', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Reminder: description', NULL, 'en');

########################################################################
# 2015-08-10 - Added settings to the bgservice_calls report replacing the hardcoded values

# Added settings to the bgservice_calls report
UPDATE `reports` SET `settings`='document_calls_list := 102\r\nproject_comercial_campaign := 4\r\nproject_request := 6\r\ncustomer_id := customer_id\r\ncall_status := list_call_status\r\ncall_date := list_to_date\r\ncall_date_next_call := list_datenext_call\r\ncall_minutes := list_call_minute\r\ncall_description := call_description\r\ncall_comment := list_coment\r\n' WHERE  type='bgservice_calls';

########################################################################
# 2015-09-08 - Update settings of 'bgservice_suppliers_clients_obligations' report to contain additional incomes reasons to be included and also reasons to tag to be excluded from the report results
#            - Add new plugin automation addClientRequestTask
#            - Add new plugin automation copyClientRequestAssignments
#            - Add new plugin automation finishClientRequestTask

# Update settings of 'bgservice_suppliers_clients_obligations' report to contain additional incomes reasons to be included and also reasons to tag to be excluded from the report resuls
UPDATE `reports` SET `settings`='incomes_reasons_included := 104,106,108\r\nfinance_expense := 101,20,21,109\r\nfinance_expense_related_types := 20,21,22,23\r\nexpense_request := 86\r\ntype_deliverer := 8, 12, 7\r\ntype_client := 8, 15, 7\r\nnew_reason_company := 1\r\ncustomer_bg_service := 5\r\ntype_private_client := 10\r\ntype_private_client := 10\r\nnomenclatures_not_shown := 209,207,206,201,200,199,198,205\r\n\r\nexclude_finance_expenses_tags :=\r\nexclude_finance_incomes_tags :=' WHERE  type='bgservice_suppliers_clients_obligations' AND `settings` NOT LIKE '%exclude_finance_incomes_tags%';

# Add new plugin automation addClientRequestTask
INSERT INTO automations
  SET name = 'Създаване на задача при смяна на статус на Заявка (клиент)',
    module = 'documents',
    automation_type = 'action',
    start_model_type = '22',
    conditions = 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'setstatus\' && \'[b_substatus]\' == \'46\'',
    method = 'plugin := bgservice\r\nmethod := addClientRequestTask',
    settings = 'task_days := \r\ntask_department := 21\r\ntask_group := 1\r\ntask_status := \r\ntask_substatus := \r\ntask_severity := ',
    position = 60;

# Add new plugin automation copyClientRequestAssignments
INSERT INTO automations
  SET name = 'Копиране на всички назначения от Заявка (клиент) към задача.',
    module = 'documents',
    automation_type = 'action',
    start_model_type = '22',
    conditions = 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'assign\'',
    method = 'plugin := bgservice\r\nmethod := copyClientRequestAssignments',
    settings = '',
    position = 60,
    nums = 0;

# Add new plugin automation finishClientRequestTask
INSERT INTO automations
  SET name = 'Приключване на задача при смяна на статус на Заявка (клиент)',
    module = 'documents',
    automation_type = 'action',
    start_model_type = '22',
    conditions = 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'setstatus\' && in_array(\'[b_substatus]\', array(\'49\', \'50\', \'51\', \'52\'))',
    method = 'plugin := bgservice\r\nmethod := finishClientRequestTask',
    settings = 'task_finished_substatus := 7',
    position = 60;

########################################################################
# 2015-09-09 - Updated settings for the 'bgservice_remote_work' report to contain extra setting for department which subdepartments to be included in the filters

# PRE-DEPLOYED # Updated settings for the 'bgservice_remote_work' report to contain extra setting for department which subdepartments to be included in the filters
#UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nsubdepartments_tree_roots := 3') WHERE `type`='bgservice_remote_work' AND `settings` NOT LIKE '%subdepartments_tree_roots%';

########################################################################
# 2015-09-10 - Added automation for adding Loan payment document when loan taken document is finished

# PRE-DEPLOYED # Added automation for adding Loan payment document when loan taken document is finished
#INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#('Създаване на заем (връщане)', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', '110', 'payment_loan_type := 105', 'condition := ''[b_status]'' == ''finished'' && ''[prev_b_status]'' != ''finished''', 'plugin := bgservice\r\nmethod := createLoanPayment', NULL, 0, 1, 1);

########################################################################
# 2015-09-17 - Added automations that tag expense invoices

# Added automations that tag expense invoices
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Тагване на разходни фактури (според контрагенти)', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', '20', 'tag_customer_1 := 56\ntag_invoice_1 := 58\ntag_customer_2 := 57\ntag_invoice_2 := 59', 'condition := \'[b_status]\' == \'finished\' && \'[prev_b_status]\' != \'finished\'', 'plugin := bgservice\r\nmethod := tagExpenseInvoicesByCustomer', NULL, 0, 1, 1),
('Тагване на разходни фактури (възстановен ДДС)', 0, NULL, 1, 'finance', 'expenses_reasons', 'action', '111', 'tag_VAT_restore := 60\ntag_VAT_restored := 61', 'condition := \'[b_status]\' == \'finished\' && \'[prev_b_status]\' != \'finished\'', 'plugin := bgservice\r\nmethod := tagExpenseInvoicesByExpenseDocument', NULL, 0, 1, 1);

########################################################################
# 2015-09-23 - Updated the setting of the automation that tags expense invoices

# Updated the setting of the automation that tags expense invoices
UPDATE automations SET settings='tag_customer_1 := 56\ntag_invoice_1 := 58\ntag_customer_2 := 57\ntag_invoice_2 := 59\ntag_customer_3 := 65\ntag_invoice_3 := 62' WHERE method LIKE '%tagExpenseInvoicesByCustomer%' AND settings NOT LIKE '%tag_invoice_3%';

########################################################################
# 2015-09-25 - Fixed the default settings of hr_who_is_resting report

# Fixed the default settings of hr_who_is_resting report
 UPDATE `reports` SET settings='powerusers := 8,6,236,35,5\r\n\r\ndocument_leave_type := 6\r\nfree_days_type := plr_leave_type\r\nfree_days_year := plr_leave_year\r\nfree_days_start_date := plr_leave_start_date\r\nfree_days_end_date := plr_leave_finish_date\r\nfree_days_replacement := plr_deputy\r\n\r\ndays_off_substatus_approved := 138\r\ndays_off_substatus_disapproved := 139\r\n\r\ninclude_only_working_days :='
  WHERE type='hr_who_is_resting';

######################################################################################
# 2015-10-23 - Added new report: 'bgservice_warranty_cards'
#            - Added new automation plugin 'prepareWarrantyCard'
#            - Added new automation plugin 'afterWarrantyCard'

# Added new report: 'bgservice_warranty_cards'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  ('328', 'bgservice_warranty_cards', 'cus_type_provider := 12\r\ncus_type_client := 1,3,8,10,11,14,15\r\nexpenses_invoices_status := finished\r\ndoc_type_warranty_card := 92', '0');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('328', 'Гаранционни карти', 'bg'),
  ('328', 'Warranty cards', 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '328', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     = 'generate_report'
      AND `model_type` = '328';

# Added new automation plugin 'prepareWarrantyCard'
INSERT INTO automations
  SET name = 'Подготвяне на данните за гаранционна карта',
    module = 'documents',
    automation_type = 'before_viewer',
    start_model_type = '92',
    conditions = 'condition := \'[request_is_post]\' != \'1\' && \'[action]\' == \'add\' && \'[b_customer]\' == \'\'',
    method = 'plugin := bgservice\r\nmethod := prepareWarrantyCard',
    settings = 'report_files_field := ',
    nums = 0;

# Added new automation plugin 'afterWarrantyCard'
INSERT INTO automations
  SET name = 'Прикачване на файлове към гаранционна карта',
    module = 'documents',
    automation_type = 'action',
    start_model_type = '92',
    conditions = 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'add\'',
    method = 'plugin := bgservice\r\nmethod := afterWarrantyCard',
    settings = 'report_files_field := ',
    nums = 0;

######################################################################################
# 2015-10-27 - Updated data and variables in Onsite support record (id 5)
#            - Added print plugin for Onsite support record
#            - Fixed settings and data of autocompleter in VAT recovery expense document

# Updated data and variables in Onsite support record (id 5)
-- clear any existing data
DELETE FROM `documents_cstm` WHERE `var_id` BETWEEN 275 AND 278;

-- activity_id
INSERT INTO `documents_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
SELECT `model_id`, 275, `num`, CASE
WHEN `value` IN (
    'Настройване на принтер/скенер/мултифункционално устройство',
    'Диагностика на скенер/принтер/мултифункционално устройство',
    'Пренастройване на мрежов принтер',
    'Инсталация на мрежов принтер',
    'Настройване на техника'
) THEN 705
WHEN `value` IN (
    'Софтуерна профилактика'
) THEN 708
WHEN `value` IN (
    'Хардуерна профилактика',
    'Тест на хардуер',
    'Подмяна на хардуер'
) THEN 709
WHEN `value` IN (
    'Отстраняване на софтуерни проблеми',
    'Почистване на вируси'
) THEN 710
WHEN `value` IN (
    'Инсталация на софтуер',
    'Настройка на поща',
    'Прехвърляне на поща',
    'Прехвърляне на информация',
    'Преинсталация на Windows',
    'Отстраняване на проблем с e-mail клиент',
    'Преинсталация на софтуер',
    'Инсталация на клиентски софтуер',
    'Обновяване на софтуер'
) THEN 711
WHEN `value` IN (
    'Взимане на техника за ремонт',
    'Връщане на техника',
    'Взимане на компютър за преинсталация'
) THEN 712
WHEN `value` IN (
    'Доставка и настройка на техника'
) THEN 713
WHEN `value` IN (
    'Консултация'
) THEN 714
WHEN `value` IN (
    'Оглед'
) THEN 715
WHEN `value` IN (
    'Отстраняване на мрежов проблем',
    'Диагностика на мрежа',
    'Мрежови настройки'
) THEN 716
WHEN `value` IN (
    'Разместване на техника'
) THEN 739
WHEN `value` IN (
    'work_office'
) THEN 740
WHEN `value` IN (
    '---'
) THEN 741
WHEN `value` IN (
    'Окабеляване'
) THEN 742
WHEN `value` IN (
    'други'
) THEN 743
WHEN `value` IN (
    'Влагане на оборотна техника'
) THEN 744
ELSE ''
END AS `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`
FROM `documents_cstm` WHERE `var_id` = 52;

-- activity_name
INSERT INTO `documents_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
SELECT `model_id`, 276, `num`, IFNULL(ni.`name`, '') AS `value`, `added`, `added_by`, `modified`, `modified_by`, dc.`lang`
FROM `documents_cstm` dc
LEFT JOIN `nom_i18n` ni
  ON dc.`value` = ni.`parent_id` AND ni.`lang` = 'bg'
WHERE dc.`var_id` = 275;

-- committed_id
INSERT INTO `documents_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
SELECT `model_id`, 277, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`
FROM `documents_cstm`
WHERE `var_id` = 510;

-- committed_name
INSERT INTO `documents_cstm` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
SELECT `model_id`, 278, `num`, IFNULL(CONCAT(ci.`name`, ' ', ci.`lastname`), '') AS `value`, `added`, `added_by`, `modified`, `modified_by`, dc.`lang`
FROM `documents_cstm` dc
LEFT JOIN `customers_i18n` ci
  ON dc.`value` = ci.`parent_id` AND ci.`lang` = 'bg'
WHERE dc.`var_id` = 510;

-- fixed autocompleter settings
UPDATE `_fields_meta` SET `source` = REPLACE(`source`, '<firstname>', '<name>') WHERE `id` = 278;
UPDATE `_fields_meta` SET `source` = CONCAT(`source`, '\nautocomplete_filter := <type> => 1') WHERE `id` = 278 AND `source` NOT LIKE '%autocomplete_filter := <type>%';
UPDATE `_fields_meta` SET `source` = REPLACE(`source`, 'autocomplete_filter := type ', 'autocomplete_filter := <type> ') WHERE `source` LIKE '%autocomplete_filter := type %';

-- showed new fields and hid old ones
UPDATE `_fields_meta` SET `hidden` = 0 WHERE `id` IN (276,278);
UPDATE `_fields_meta` SET `hidden` = 1 WHERE `id` IN (52);
UPDATE `_fields_meta` SET `required` = 1 WHERE `id` IN (278);

-- removed old employee variable and data
DELETE FROM `_fields_i18n` WHERE `parent_id` = 510;
DELETE FROM `_fields_meta` WHERE `id` = 510;
DELETE FROM `documents_cstm` WHERE `var_id` = 510;

-- updated report settings
UPDATE `reports` SET `settings` = REPLACE(`settings`, 'ppp_worker', 'committed_id') WHERE `type` = 'bgservice_remote_work' AND `settings` LIKE '%ppp_worker%';

# Added print plugin for Onsite support record
INSERT INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(76, 'Document', 5, 'bgservice', 'prepareOnsiteSupportRecord', 'header_company1 := 12\r\nheader_company2 := 1\r\nheader_company3 := 8\r\n\r\nfooter_company1 := 13\r\nfooter_company2 := 19\r\nfooter_company3 := 19\r\n\r\ncontract_type := 1\r\ncontract_support_service := 184\r\n\r\nvisit_employee := committed_id\r\nvisit_start_work := ppp_start_work\r\nvisit_end_work := ppp_end_work\r\nvisit_activity := activity_id\r\nvisit_equipment_activities := 712,713\r\nvisit_equipment_group := machine_group\r\n\r\nworking_day_start := 09:00\r\nworking_day_end := 18:00\r\n\r\nemployee_working_from := working_from\r\nemployee_working_to := working_to', '', NOW(), NOW());
INSERT INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(76, 'Данни от договор и работно време', 'Избор на хедър и футър, подготовка на данни от договор за поддръжка и според работно време на служители.', 'bg', NOW()),
(76, 'Contract data and working hours', 'Selection of header and footer, preparation of data from support contract and based on working hours of employees.', 'en', NOW());

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'company_name', 'Document', 'basic', 'pattern_plugins', ',76,', '', 1);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Own company name', NULL, 'en'),
(LAST_INSERT_ID(), 'Име на собствена фирма', NULL, 'bg');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'total_working_time', 'Document', 'basic', 'pattern_plugins', ',76,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Working time', NULL, 'en'),
(LAST_INSERT_ID(), 'Работно време', NULL, 'bg');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'total_overtime', 'Document', 'basic', 'pattern_plugins', ',76,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Overtime', NULL, 'en'),
(LAST_INSERT_ID(), 'Извънработно време', NULL, 'bg');

UPDATE `patterns` SET `plugin` = 76 WHERE `id` = 83;

UPDATE `patterns_i18n` SET `content` = '<table border="0" cellpadding="0" cellspacing="0" style="width:100%">\r\n   <tbody>\r\n     <tr>\r\n            <td style="width:2%">&nbsp;</td>\r\n            <td style="text-align:center; width:98%">&nbsp;</td>\r\n        </tr>\r\n       <tr>\r\n            <td style="width:2%">&nbsp;</td>\r\n            <td style="text-align:center; width:98%">&nbsp;</td>\r\n        </tr>\r\n       <tr>\r\n            <td style="width:2%">&nbsp;</td>\r\n            <td style="text-align:center; width:98%"><span style="font-size:36px"><span style="color:rgb(0, 132, 220)"><strong><span style="font-family:opalcyr">ПРОТОКОЛ ЗА ИЗВЪРШЕНА РАБОТА</span></strong></span></span></td>\r\n        </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td style="text-align:center"><strong><span style="font-size:18px"><span style="font-family:opalcyr">№ [document_num]/<span style="font-size:large"><span style="font-family:opalcyr">[document_added_date|date_format:%d.%m.%Y]</span></span></span></span></strong><strong><span style="font-size:18px"><span style="font-family:opalcyr"> г.</span></span></strong></td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td style="text-align:center">&nbsp;</td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td style="text-align:center">&nbsp;</td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td style="text-align:center">&nbsp;</td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>\r\n            <table border="0" cellpadding="3" cellspacing="0" style="width:100%">\r\n               <tbody>\r\n                 <tr>\r\n                        <td style="background-color:rgb(0, 132, 220); text-align:center; width:49%"><span style="font-size:26px"><span style="color:rgb(255, 255, 255)"><strong><span style="font-family:opalcyr">Клиент</span></strong></span></span></td>\r\n                     <td style="width:2%">&nbsp;</td>\r\n                        <td style="background-color:rgb(0, 132, 220); text-align:center; width:49%"><span style="font-size:26px"><span style="color:rgb(255, 255, 255)"><strong><span style="font-family:opalcyr">Доставчик</span></strong></span></span></td>\r\n                  </tr>\r\n                   <tr>\r\n                        <td style="background-color:rgb(222, 235, 242)"><strong><span style="font-size:18px"><span style="font-family:opalcyr">[bg_customer_name] [bg_customer_lastname]</span></span></strong></td>\r\n                        <td>&nbsp;</td>\r\n                     <td style="background-color:rgb(222, 235, 242)"><strong><span style="font-size:18px"><span style="font-family:opalcyr">[bg_company_name]</span></span></strong></td>\r\n                    </tr>\r\n                   <tr>\r\n                        <td style="background-color:rgb(222, 235, 242)"><span style="font-size:18px"><span style="font-family:opalcyr">Обект: </span></span><span style="font-size:large"><span style="font-family:opalcyr">[bg_document_branch]</span></span></td>\r\n                     <td>&nbsp;</td>\r\n                     <td style="background-color:rgb(222, 235, 242)"><span style="font-size:18px"><span style="font-family:opalcyr">Офис: [bg_document_office]</span></span></td>\r\n                    </tr>\r\n               </tbody>\r\n            </table>\r\n            </td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td><span style="font-size:large"><span style="font-family:opalcyr">[a_ppp_services_group] </span></span></td>\r\n      </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td><span style="font-size:large"><span style="font-family:opalcyr">[a_ppp_time_work_group] </span></span></td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td><span style="font-size:18px"><span style="font-family:opalcyr">[a_machine_group]</span></span></td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>\r\n            <table border="0" cellpadding="1" cellspacing="1" style="height:66px; width:100%">\r\n              <tbody>\r\n                 <tr>\r\n                        <td style="text-align:right; width:20%"><span style="font-size:18px"><span style="font-family:opalcyr">Общо време:</span></span></td>\r\n                       <td style="width:10%"><span style="font-size:18px"><span style="font-family:opalcyr"><strong><span style="font-family:opalcyr">[a_ppp_generally_work]</span></strong></span></span></td>\r\n                        <td style="text-align:right; width:20%"><span style="font-size:18px"><span style="font-family:opalcyr">Работно време:</span></span></td>\r\n                        <td style="width:10%"><strong><span style="font-size:18px"><span style="font-family:opalcyr">[total_working_time]</span></span></strong></td>\r\n                       <td style="text-align:right; width:20%"><span style="font-size:18px"><span style="font-family:opalcyr">Извънработно време:</span></span></td>\r\n                       <td style="width:10%"><strong><span style="font-family:opalcyr"><span style="font-size:18px">[total_overtime]</span></span></strong></td>\r\n                       <td>&nbsp;</td>\r\n                 </tr>\r\n               </tbody>\r\n            </table>\r\n            </td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>\r\n            <table border="0" cellpadding="3" cellspacing="0" style="width:100%">\r\n               <tbody>\r\n                 <tr>\r\n                        <td style="width:49%">&nbsp;</td>\r\n                       <td style="width:2%">&nbsp;</td>\r\n                        <td style="background-color:rgb(0, 132, 220); width:49%"><span style="color:#fff"><strong><span style="font-size:18px"><span style="font-family:opalcyr">Приел</span></span></strong></span></td>\r\n                   </tr>\r\n                   <tr>\r\n                        <td>&nbsp;</td>\r\n                     <td>&nbsp;</td>\r\n                     <td style="background-color:rgb(222, 235, 242)">\r\n                        <div>&nbsp;</div>\r\n\r\n                       <div><span style="font-size:18px"><span style="font-family:opalcyr"><span style="font-size:large"><span style="font-family:opalcyr">[a_ppp_accept_work] </span></span></span></span></div>\r\n\r\n                      <div style="text-align: right;"><span style="font-size:12px"><span style="font-family:opalcyr">(подпис)</span></span></div>\r\n                     </td>\r\n                   </tr>\r\n               </tbody>\r\n            </table>\r\n            </td>\r\n       </tr>\r\n       <tr>\r\n            <td>&nbsp;</td>\r\n         <td>&nbsp;</td>\r\n     </tr>\r\n   </tbody>\r\n</table>\r\n\r\n<p>&nbsp;</p>\r\n' WHERE `parent_id` = 83 AND `lang` = 'bg';

# Fixed settings and data of autocompleter in VAT recovery expense document
UPDATE `_fields_meta` SET `source` = REPLACE(`source`, '$ article_deliverer', '$article_deliverer') WHERE source like '%$ article_deliverer%';
UPDATE `gt2_details` g
JOIN `gt2_details_i18n` gi
  ON g.id = gi.parent_id AND gi.lang = 'bg'
JOIN `fin_expenses_reasons` f1
  ON f1.id = g.model_id AND g.model = 'Finance_Expenses_Reason' AND f1.type = 111
JOIN `fin_expenses_reasons` f2
  ON f2.num = gi.article_deliverer_name
SET g.article_deliverer = f2.id;

######################################################################################
# 2015-10-28 - Added validation to several fields in Onsite support record (id 5)

# Added validation to several fields in Onsite support record (id 5)
UPDATE `_fields_meta` SET `validate` = 'method := validateConditionalRequired\r\ncondition_field := activity_id\r\ncondition_value := 712,713'
WHERE `model` = 'Document' AND `model_type` = 5 AND `name` IN ('machine_type', 'technic_delivery', 'machine_model');

UPDATE `_fields_meta` SET `validate` = 'method := regexpCompare\r\nregexp := /[a-zа-я]{3,}/iu'
WHERE `model` = 'Document' AND `model_type` = 5 AND `name` IN ('ppp_accept_work');

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(60, 'help', 'Въведете името на лицето, приело работата, или кратко обяснение, когато няма такова.', 'bg');

######################################################################################
# 2015-12-07 - Added report for sending emails for debt collection
#            - Modified settings of GT2 in document of type 107
#            - Updated payment status of an invoiced proforma
#            - Updated event with negative duration (-21758700)

# Added report for sending emails for debt collection
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(334, 'bgs_debt_collection_email_sender', '# settings for report filters\r\nfin_documents_types := \r\ncustomers_types := 8\r\nupcoming_num_days := 2\r\n\r\n# created document types per company\r\ndocument_type1 := 107\r\ndocument_type2 := \r\ndocument_type3 := \r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date', 0, 0, 1);

INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(334, 'Писма за дължими суми', NULL, NULL, 'bg'),
(334, 'Debt collection emails', NULL, NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 334, 0, 1);

INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module` = 'reports' AND `action` = 'generate_report' AND `model_type` = 334;

# Modified settings of GT2 in document of type 107
-- article_name (num)
UPDATE `_fields_meta`
SET `source` = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nautocomplete := autocompleters\nautocomplete_plugin_search := customQuery\nautocomplete_plugin_param_sql := SELECT id, num, type, issue_date, date_of_payment, IF(date_of_payment<CURDATE(), DATEDIFF(CURDATE(), date_of_payment), '''') AS overdue, ROUND(total_with_vat, 2) AS total_with_vat, currency, IF(payment_status = ''unpaid'' AND currency = <currency>, ROUND(total_with_vat, 2), '''') AS remaining_amount FROM fin_incomes_reasons WHERE (num LIKE ''%<search_string_parts>%'' OR issue_date LIKE ''%<search_string_parts>%'')  AND customer = <customer> AND payment_status IN (''unpaid'', ''partial'') AND status = ''finished'' AND annulled_by = 0 AND active = 1 AND type NOT IN (5,18)\nautocomplete_plugin_param_customer := $customer\nautocomplete_plugin_param_currency := $currency\nautocomplete_fill_options := $article_name => <num>\nautocomplete_fill_options := $article_id => <id>\nautocomplete_fill_options := $quantity => 1\nautocomplete_fill_options := $free_field5 => <type>\nautocomplete_fill_options := $free_field1 => <issue_date>\nautocomplete_fill_options := $free_field3 => <date_of_payment>\nautocomplete_fill_options := $free_field4 => <overdue>\nautocomplete_fill_options := $article_height => <total_with_vat>\nautocomplete_fill_options := $article_barcode => <currency>\nautocomplete_fill_options := $price => <remaining_amount>\nautocomplete_suggestions := <num>/<issue_date>\nautocomplete_clear := 1\nautocomplete_view_mode := link\nautocomplete_view_mode_url := index.php?launch=finance&controller=incomes_reasons&incomes_reasons=view&view='
WHERE `id` = 103185;

-- free_field5 (type)
UPDATE `_fields_meta`
SET `source` = 'text_align := left\npermissions_edit := 1\npermissions_view := 1\nmethod := getCustomDropdown\ntable := DB_TABLE_FINANCE_DOCUMENTS_TYPES\ntable_i18n := DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N\nwhere := t.model = ''Finance_Incomes_Reason'' AND t.active = 1 AND t.id NOT IN (5,18)', `readonly` = 1
WHERE `id` = 103229;
DELETE FROM `_fields_options` WHERE `parent_name` = 'free_field5';

-- free_field4 (days overdue)
UPDATE `_fields_meta`
SET `source` = 'text_align := right\npermissions_edit := 1\npermissions_view := 1', `validate` = 'js_filter := insertOnlyDigits', `readonly` = 1, `width` = 100
WHERE `id` = 103228;

-- price (due amout)
UPDATE `_fields_meta` SET `hidden` = 0, `position` = 12 WHERE `id` = 103188;
UPDATE `_fields_i18n` SET `content` = 'Задължение' WHERE `parent_id` = 103188 AND `content_type` = 'label' AND `lang` = 'bg';

-- free_text1, free_text2 (not used)
UPDATE `_fields_meta` SET `hidden` = 1 WHERE `id` IN (103220,103221);

-- price_with_discount (not used)
UPDATE `_fields_meta` SET `hidden` = 1, `position` = 36 WHERE `id` = 103190;

-- article_height (document total)
UPDATE `_fields_meta` SET `hidden` = 0, `readonly` = 1, `position` = 9, `width` = 80 WHERE `id` = 103212;
UPDATE `_fields_i18n` SET `content` = 'Стойност' WHERE `parent_id` = 103212 AND `content_type` = 'label' AND `lang` = 'bg';

-- article_barcode (document currency)
UPDATE `_fields_meta`
SET `type` = 'dropdown', `source` = 'text_align := right\npermissions_edit := 1\npermissions_view := 1\nmethod := getCurrencies', `hidden` = 0, `readonly` = 1, `position` = 10, `width` = 40
WHERE `id` = 103211;
UPDATE `_fields_i18n` SET `content` = 'Валута' WHERE `parent_id` = 103211 AND `content_type` = 'label' AND `lang` = 'bg';

-- free_field1, free_field3 (dates)
UPDATE `_fields_meta` SET `readonly` = 1 WHERE `id` IN (103225,103227);

UPDATE `emails` SET `model_type` = 107 WHERE `model` = 'Document' AND `model_type` = 88;

# Updated payment status of an invoiced proforma
UPDATE `fin_incomes_reasons` i
JOIN `fin_reasons_relatives` r
  ON i.id = r.parent_id AND r.parent_model_name = 'Finance_Incomes_Reason' AND i.type = 1 AND i. status = 'finished' AND i.active = 1 AND i.annulled_by = 0
JOIN `fin_incomes_reasons` p
  ON p.id = r.link_to AND r.link_to_model_name = 'Finance_Incomes_Reason' AND p.type = 2 AND p. status = 'finished' AND p.active = 1 AND p.annulled_by = 0
SET p.payment_status = 'invoiced'
WHERE p.id = 15256 AND p.payment_status = 'unpaid';

# Updated event with negative duration (-21758700)
UPDATE `events` SET `duration` = 120 WHERE `id` = 2690;

######################################################################################
# 2015-12-10 - Added settings to automation requestCustomNum

# Added settings to automation requestCustomNum
UPDATE `automations` SET `settings` = CONCAT(`settings`, '\nbugtracker_url := http://bugzilla.bgservice.net/bugzilla3/show_bug.cgi?id=%d') WHERE method LIKE '%requestCustomNum%' AND settings NOT LIKE '%bugtracker_url%';

######################################################################################
# 2015-12-14 - Added payment system tag
#            - Added setting for max number of selected recipients for report for sending emails for debt collection

# Added payment system tag
UPDATE settings SET value=67 WHERE section='finance' AND name='payments_system_tag' AND value='';

# Added setting for max number of selected recipients for report for sending emails for debt collection
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nmax_recipients := 100')
WHERE `type` = 'bgs_debt_collection_email_sender' AND `settings` NOT LIKE '%max_recipients%';

######################################################################################
# 2016-01-21 - Added new report: 'bgservice_installations'

# Added new report: 'bgservice_installations'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  ('341', 'bgservice_installations', '', '0');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('341', 'Карта на инсталация', 'bg'),
  ('341', 'Installation map', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '341', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     = 'generate_report'
      AND `model_type` = '341';

######################################################################################
# 2016-05-13 - Merged same automations for different types into one record
#            - Deactivated automations for inactive types
#            - Updated conditions of crontab automations

# Merged same automations for different types into one record
DELETE FROM `automations` WHERE `id` IN (37,41,100);
UPDATE `automations_history` SET `parent_id` = 6 WHERE `parent_id` IN (37,41,100);

UPDATE `automations` SET `name` = 'Приключване на документ - няколко типа (10 часа след добавяне)', `start_model_type` = 0, `conditions` = 'where := d.type IN (5, 14, 85, 102)\r\nwhere := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 10 HOUR) <= NOW()' WHERE `id` = 6;

# Deactivated automations for inactive types
UPDATE `automations` SET `name` = 'Приключване на Клиентски проблем - неактивен тип', `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 30 DAY) <= NOW()', `active` = 0 WHERE `id` = 8;
UPDATE `automations` SET `name` = 'Приключване на ChangeLog - неактивен тип', `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 6 DAY) <= NOW()', `active` = 0 WHERE `id` = 14;

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := e.active = 1\r\nwhere := e.event_start < UTC_TIMESTAMP()\r\nwhere := e.status = ''planning''' WHERE `id` = 56;
UPDATE `automations` SET `conditions` = 'where := e.active = 1\r\nwhere := e.event_start + INTERVAL e.duration MINUTE <= UTC_TIMESTAMP()\r\nwhere := e.status = ''progress''' WHERE `id` = 57;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 1 DAY) <= NOW()' WHERE `id` = 64;
UPDATE `automations` SET `name` = 'Приключване на Задача (nZoom)', `conditions` = 'where := t.active = 1\r\nwhere := t.status != ''finished''\r\nwhere := DATE_ADD(t.planned_finish_date, INTERVAL 5 HOUR) <= NOW()' WHERE `id` = 81;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 3 DAY) <= NOW()' WHERE `id` = 88;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 3 DAY) <= NOW()' WHERE `id` = 90;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 3 DAY) <= NOW()' WHERE `id` = 92;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 3 DAY) <= NOW()' WHERE `id` = 93;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != "closed"\r\nwhere := DATE_ADD(d.added, INTERVAL 1 DAY) <= NOW()\r\nwhere := a__performer_name_id != ''''' WHERE `id` = 96;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 7 DAY) <= NOW()' WHERE `id` = 97;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 3 DAY) <= NOW()' WHERE `id` = 108;
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != ''closed''\r\nwhere := DATE_ADD(d.added, INTERVAL 3 DAY) <= NOW()' WHERE `id` = 109;

########################################################################
# 2016-05-18 - Changed condition of automation in order to avoid parsing error

# Changed condition of automation in order to avoid parsing error
UPDATE `automations`
SET `conditions` = 'condition := \'[a_start_term_date]\' != \'\' && \'[a_start_term_date]\' != \'0\'\r\ncondition := \'[a_start_term_days]\' != \'\' && \'[a_start_term_days]\' != \'0\'\r\ncondition := \'[a_start_term_type]\' != \'\' && \'[a_start_term_type]\' != \'0\''
WHERE `id` = 83;

########################################################################
# 2016-06-15 - Sends an email which contains the list of customers who have not been serviced the last 30 days

# Sends an email which contains the list of customers who have not been serviced the last 30 days
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
  ('Изпращане на имейл за необслужван 30 дни клиент', 0, NULL, 1, 'documents', NULL, 'crontab', '0', 'start_after := 01:00\r\nstart_before := 04:00\r\n\r\nunattended_days := 30\r\nemail_template_id := 1025\r\nemails := <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>\r\n', 'condition := 1', 'plugin := bgservice\r\nmethod := sendEmailForUnattendedClient', NULL, 0, 0, 1);

######################################################################################
# 2016-06-28 - Changed conditions of a crontab automation because another one depends on it

# Changed conditions of a crontab automation because another one depends on it
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := d.status != "closed"\r\nwhere := DATE_ADD(d.added, INTERVAL 1 DAY) <= NOW()' WHERE `id` = 96;

######################################################################################
# 2016-07-11 - Added new setting for 'bgservice_inventory' report to define if the last column will be shown
#            - Added new automation that sends email for offers

# Added new setting for 'bgservice_inventory' report to define if the last column will be shown
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nshow_ocs_column := 1') WHERE `type` = 'bgservice_inventory' AND `settings` NOT LIKE '%show_ocs_column%';

# Added new automation that sends email for offers
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `nums`, `active`) VALUES
  ('Смяна на статуси и нотификация', 'documents', 'crontab', '13', 'start_after := 01:00\r\nstart_before := 04:00\r\nworking_days := 2\r\nemail_template_id := 1026\r\nlocked_substatus_id := 6\r\nclosed_substatus_id := 8', 'condition := 1', 'plugin := bgservice\r\nmethod := offersStatusesAndNotifications', NULL, 0, 1);

######################################################################################
# 2016-08-29 - Set correct substatus settings in transformations

# Set correct substatus settings in transformations
UPDATE `transformations` SET `settings` = REPLACE(`settings`, 'closed_85', 'closed_151')
WHERE `source_model` = 'Document' AND `source_type` = 49 AND `settings` LIKE '%substatus_after_transform := closed_85%';

######################################################################################
# 2016-09-12 - Fixed conditions of automations (added quotes) to avoid parsing error
#            - Removed old values of additional variables of customers of type 8 (client)
#            - Added report 'bgs_work_outside_contracted' for installation

# Fixed conditions of automations (added quotes) to avoid parsing error
UPDATE `automations` SET `conditions` = 'condition := ''[b_name]'' != ''''' WHERE `conditions` = 'condition := [b_name] != ''''';

# Removed old values of additional variables of customers of type 8 (client)
DELETE FROM `customers_cstm` WHERE `var_id` IN (8016,8021) OR `var_id` IN (8015,8018,8019,8020) AND `num` > 1;
UPDATE `customers_cstm` SET `value` = '' WHERE `var_id` IN (8019,8020,8024,8025) AND `value` NOT IN ('', '30', '60');
UPDATE `customers_cstm` SET `value` = '' WHERE `var_id` IN (8015,8018) AND `value` != '' AND `value` NOT RLIKE '^[0-9.]+$';

# Added report 'bgs_work_outside_contracted' for installation
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(354, 'bgs_work_outside_contracted', 'contract_type := 1\r\ncustomer_type := 8\r\ncustomer_tags := 6\r\ndoc_type_visit := 5\r\ndoc_type_remote := 85\r\n\r\nworking_day_start := 09:00\r\nworking_day_end := 18:00', 0, 0, 1);

INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(354, 'Работа извън договорено', '04. IT Поддръжка', NULL, 'bg'),
(354, 'Work outside contracted', '04. IT Support', NULL, 'en');

INSERT INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
(NULL, 'reports', '', 'generate_report', 354, 0, 1);

INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT '1', `id`, 'all'
FROM `roles_definitions`
WHERE `module` = 'reports' AND `action` = 'generate_report' AND `model_type` = '354';

######################################################################################
# 2016-09-13 - Added new automation to tag order/sells when proforma is issued from it

# Added new automation to tag order/sells when proforma is issued from it
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Тагване на поръчка/продажба при издаване на проформа', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '104', '', 'condition := ''[action]'' == ''addproformainvoice'' && ''[request_is_post]'' == ''1''', 'method := tag\r\nnew_tags := 69', NULL, 0, 1, 1);

######################################################################################
# 2016-09-29 - Fixed conditions of automations (added quotes and updated a typing error) to avoid parsing error

# Fixed conditions of automations (added quotes and updated a typing error) to avoid parsing error
UPDATE `automations`
SET `conditions` = REPLACE(REPLACE(`conditions`, 'condition := [perv_b_customer] ==', 'condition := ''[prev_b_customer]'' =='), 'condition := [b_customer] ==', 'condition := ''[b_customer]'' ==')
WHERE `conditions` LIKE '%condition := [perv_b_customer] ==%';

UPDATE `automations`
SET `conditions` = REPLACE(REPLACE(`conditions`, 'condition := [prev_b_substatus] ', 'condition := ''[prev_b_substatus]'' '), 'condition := [b_substatus] ', 'condition := ''[b_substatus]'' ')
WHERE `conditions` LIKE '%condition := [prev_b_substatus] %';

UPDATE `automations`
SET `conditions` = REPLACE(REPLACE(`conditions`, '([prev_b_substatus] ', '(''[prev_b_substatus]'' '), '&& [b_substatus] ', '&& ''[b_substatus]'' ')
WHERE `conditions` LIKE '%([prev_b_substatus] %';

######################################################################################
# 2016-10-06 - Added new setting for 'bgservice_requests' report to mark if the column for deadline will be visible

# Added new setting for 'bgservice_requests' report to mark if the column for deadline will be visible
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\nhide_deadline_column :=') WHERE `type` = 'bgservice_requests' AND `settings` NOT LIKE '%hide_deadline_column%';

######################################################################################
# 2016-10-12 - The automation 'createLoanPayment' was updated with option for finishing the newly added incomes reason

# The automation 'createLoanPayment' was updated with option for finishing the newly added incomes reason
UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\nadd_new_reason_as_finished :=') WHERE `method` LIKE '%createLoanPayment%' AND `settings` NOT LIKE '%add_new_reason_as_finished%';

######################################################################################
# 2017-01-13 - Update requestCustomNum to manageInternalRequest

# Update requestCustomNum to manageInternalRequest
UPDATE automations
  SET method='plugin := bgservice\r\nmethod := manageInternalRequest'
  WHERE method LIKE 'plugin := bgservice%method := requestCustomNum';

######################################################################################
# 2017-02-21 - Delete documents of non existing types
#            - Delete customers of non existing types
#            - Update projects and announcements that have no type specified
#            - Added settings to report 'bgs_work_outside_contracted'

# Delete documents of non existing types
#delete records for executed crontab jobs
DELETE c.* FROM crontab c, documents d WHERE c.model_id=d.id AND c.model='Document' AND d.type IN (2,4,9,11,12,33,54,89);

#delete system tasks (used for timesheets) and all related to the system task
DELETE FROM tasks_assignments WHERE parent_id IN (SELECT parent_id FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89));
DELETE ta.* FROM tasks_history AS th, tasks_audit as ta WHERE th.model='task' AND ta.parent_id=th.h_id AND th.model_id IN (SELECT parent_id FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89));
DELETE FROM tasks_history WHERE model='Task' AND model_id IN (SELECT parent_id FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89));
DELETE FROM tasks_notifications WHERE parent_id IN (SELECT parent_id FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89));
DELETE FROM tasks_timesheets WHERE task_id IN (SELECT parent_id FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89));
DELETE FROM tasks WHERE id IN (SELECT parent_id FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89));
DELETE FROM tasks_i18n WHERE parent_id IN (SELECT parent_id FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89));
DELETE tr.* FROM tasks_relatives tr, documents d WHERE tr.link_to=d.id AND tr.origin='document' AND d.type IN (2,4,9,11,12,33,54,89);

#delete all additional variables for the document type
DELETE fi.* FROM _fields_i18n fi, _fields_meta fm WHERE fm.id=fi.parent_id AND fm.model='Document' AND fm.model_type IN (2,4,9,11,12,33,54,89);
DELETE FROM _fields_meta WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);

#delete all layouts (system and user)
DELETE lap.* FROM layouts l, layouts_assign_permissions lap WHERE l.model='Document' AND l.model_type IN (2,4,9,11,12,33,54,89) AND lap.parent_id=l.layout_id;
DELETE lp.* FROM layouts l, layouts_permissions lp WHERE l.model='Document' AND l.model_type IN (2,4,9,11,12,33,54,89) AND lp.parent_id=l.layout_id;
DELETE li.* FROM layouts l, layouts_i18n li WHERE l.model='Document' AND l.model_type IN (2,4,9,11,12,33,54,89) AND li.parent_id=l.layout_id;
DELETE FROM layouts WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);

#delete minitasks
DELETE mt.* FROM minitasks mt, documents d WHERE mt.model_id=d.id AND mt.model='Document' AND d.type IN (2,4,9,11,12,33,54,89);

#delete outlooks
DELETE oa.* FROM outlooks o, outlooks_assignments oa WHERE o.module='documents' AND o.controller='documents' AND o.model_id IN (2,4,9,11,12,33,54,89) AND o.section=0 AND oa.parent_id=o.id;
DELETE os.* FROM outlooks o, outlooks_settings os WHERE o.module='documents' AND o.controller='documents' AND o.model_id IN (2,4,9,11,12,33,54,89) AND o.section=0 AND os.parent_id=o.id;
DELETE oi.* FROM outlooks o, outlooks_i18n oi WHERE o.module='documents' AND o.controller='documents' AND o.model_id IN (2,4,9,11,12,33,54,89) AND o.section=0 AND oi.parent_id=o.id;
DELETE FROM outlooks WHERE module='documents' AND controller='documents' AND model_id IN (2,4,9,11,12,33,54,89) AND section=0;

#delete patterns
DELETE pi.* FROM patterns p, patterns_i18n pi WHERE p.model='Document' AND p.model_type IN (2,4,9,11,12,33,54,89) AND pi.parent_id=p.id;
DELETE FROM patterns WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);

#delete roles definitions
DELETE rp.* FROM roles_permissions rp, roles_definitions rd WHERE rd.module='documents' AND rd.controller='' AND rd.model_type IN (2,4,9,11,12,33,54,89) AND rp.definition_id=rd.id;
DELETE FROM roles_definitions WHERE module='documents' AND controller='' AND model_type IN (2,4,9,11,12,33,54,89);

#delete settings for assignments and additional validation
DELETE si.* FROM settings s, settings_i18n si WHERE (s.name=CONVERT(CONCAT('assignment_types_', @id) USING utf8) COLLATE utf8_unicode_ci OR s.name=CONVERT(CONCAT('validate_', @id) USING utf8) COLLATE utf8_unicode_ci) AND si.parent_id=s.id;
DELETE FROM settings WHERE name=CONVERT(CONCAT('assignment_types_', @id) USING utf8) COLLATE utf8_unicode_ci OR name=CONVERT(CONCAT('validate_', @id) USING utf8) COLLATE utf8_unicode_ci;

#delete side panels
DELETE FROM side_panels WHERE module='documents' AND controller='documents' AND model_type IN (2,4,9,11,12,33,54,89);

#delete status transition rules
DELETE FROM status_settings WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);

#delete bb records (including archived ones)
DELETE FROM bb WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);
DELETE FROM archive_bb WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);

#delete comments (including archived ones)
DELETE c.* FROM comments c, documents d WHERE c.model='Document' AND c.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);
DELETE c.* FROM archive_comments c, archive_documents d WHERE c.model='Document' AND c.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);

#delete saved configurations (including archived ones)
DELETE FROM configurator WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);
DELETE FROM archive_configurator WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);
DELETE FROM configurator_group WHERE model='Document' AND model_type IN (2,4,9,11,12,33,54,89);

#delete default document assignments
DELETE FROM documents_default_assignments WHERE type IN (2,4,9,11,12,33,54,89);

#delete document substatus for the type
DELETE FROM documents_statuses WHERE doc_type IN (2,4,9,11,12,33,54,89);

#delete email notification patterns
DELETE ei.* FROM emails e, emails_i18n ei WHERE e.model_type IN (2,4,9,11,12,33,54,89) AND e.model='Document' AND e.id=ei.parent_id;
DELETE FROM emails WHERE model_type IN (2,4,9,11,12,33,54,89) AND model='Document';

#delete sent emails
DELETE esb.* FROM emails_sentbox esb, documents d WHERE esb.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);

#delete export plugins
DELETE ei.* FROM exports e, exports_i18n ei WHERE e.model_type IN (2,4,9,11,12,33,54,89) AND e.model='Document' AND e.id=ei.parent_id;
DELETE FROM exports WHERE model_type IN (2,4,9,11,12,33,54,89) AND model='Document';

#delete gt2 records, audit, indexes (including archived ones)
DELETE gtai18n.*, gta.* FROM gt2_audit gta, gt2_audit_i18n gtai18n WHERE gtai18n.parent_id = gta.id  AND gta.row_id IN (SELECT id FROM gt2_details WHERE model = 'Document' AND model_id IN (SELECT id FROM documents WHERE type IN (2,4,9,11,12,33,54,89)));
DELETE gt2i.* FROM gt2_details gt2, gt2_indexes gt2i WHERE gt2i.parent_id = gt2.id AND gt2.model='Document' AND gt2.model_id IN (SELECT id FROM documents WHERE type IN (2,4,9,11,12,33,54,89));
DELETE gt2i18n.*, gt2.* FROM gt2_details gt2, gt2_details_i18n gt2i18n WHERE gt2i18n.parent_id = gt2.id AND gt2.model='Document' AND gt2.model_id IN (SELECT id FROM documents WHERE type IN (2,4,9,11,12,33,54,89));
DELETE gtai18n.*, gta.* FROM archive_gt2_audit gta, archive_gt2_audit_i18n gtai18n WHERE gtai18n.parent_id = gta.id  AND gta.row_id IN (SELECT id FROM gt2_details WHERE model = 'Document' AND model_id IN (SELECT id FROM archive_documents WHERE type IN (2,4,9,11,12,33,54,89)));
DELETE gt2i.* FROM archive_gt2_details gt2, archive_gt2_indexes gt2i WHERE gt2i.parent_id = gt2.id AND gt2.model='Document' AND gt2.model_id IN (SELECT id FROM archive_documents WHERE type IN (2,4,9,11,12,33,54,89));
DELETE gt2i18n.*, gt2.* FROM archive_gt2_details gt2, archive_gt2_details_i18n gt2i18n WHERE gt2i18n.parent_id = gt2.id AND gt2.model='Document' AND gt2.model_id IN (SELECT id FROM archive_documents WHERE type IN (2,4,9,11,12,33,54,89));

#delete assignments (including archived ones)
DELETE da.* FROM archive_documents_assignments da, archive_documents d WHERE da.parent_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);
DELETE da.* FROM documents_assignments da, documents d WHERE da.parent_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);

#delete audit (including archived ones)
DELETE da.* FROM documents_history AS dh, documents_audit as da, documents as d WHERE dh.model='Document' AND da.parent_id=dh.h_id AND dh.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);
DELETE da.* FROM archive_documents_history AS dh, archive_documents_audit as da, archive_documents as d WHERE dh.model='Document' AND da.parent_id=dh.h_id AND dh.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);

#delete history (including archived ones)
DELETE dh.* FROM documents_history AS dh, documents as d WHERE dh.model='Document' AND dh.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);
DELETE dh.* FROM archive_documents_history AS dh, archive_documents as d WHERE dh.model='Document' AND dh.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);

#delete document additional data (including archived ones)
DELETE dc.* FROM documents_cstm dc, documents d WHERE dc.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);
DELETE dc.* FROM archive_documents_cstm dc, archive_documents d WHERE dc.model_id=d.id AND d.type IN (2,4,9,11,12,33,54,89);

#delete document relatives (including archived ones)
DELETE dr.* FROM documents_relatives dr, documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND (dr.parent_model_name='Document' AND dr.parent_id=d.id OR dr.link_to_model_name='Document' AND dr.link_to=d.id);
DELETE dr.* FROM archive_documents_relatives dr, archive_documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND (dr.parent_model_name='Document' AND dr.parent_id=d.id OR dr.link_to_model_name='Document' AND dr.link_to=d.id);

#delete document tags (including archived ones)
DELETE dt.* FROM tags_models dt, documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND d.id=dt.model_id AND dt.model='Document';
DELETE dt.* FROM archive_tags_models dt, documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND d.id=dt.model_id AND dt.model='Document';

#delete document files i18n (including archived ones)
DELETE fi.* FROM files_i18n fi, files f, documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND d.id=f.model_id AND f.model='Document' AND f.id=fi.parent_id;
DELETE fi.* FROM archive_files_i18n fi, archive_files f, archive_documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND d.id=f.model_id AND f.model='Document' AND f.id=fi.parent_id;

#delete document files (including archived ones)
DELETE f.* FROM files f, documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND d.id=f.model_id AND f.model='Document';
DELETE f.* FROM archive_files f, documents d WHERE d.type IN (2,4,9,11,12,33,54,89) AND d.id=f.model_id AND f.model='Document';

#delete automation history (including archived ones)
DELETE ah.* FROM automations_history ah, automations a WHERE a.id=ah.parent_id AND a.start_model_type IN (2,4,9,11,12,33,54,89) AND module='documents' AND controller='documents';
DELETE a.* FROM automations a WHERE a.start_model_type IN (2,4,9,11,12,33,54,89) AND module='documents' AND controller='documents';

#delete documents i18n records (including archived ones)
DELETE di.* FROM documents_i18n di, documents d WHERE d.id=di.parent_id AND d.type IN (2,4,9,11,12,33,54,89);
DELETE di.* FROM archive_documents_i18n di, archive_documents d WHERE d.id=di.parent_id AND d.type IN (2,4,9,11,12,33,54,89);

#delete related records (used in autocompleters)
DELETE FROM cstm_relatives WHERE model='Document' AND model_id IN (SELECT id FROM customers WHERE type IN (2,4,9,11,12,33,54,89)) OR cstm_model='Document' AND cstm_model_id IN (SELECT id FROM customers WHERE type IN (2,4,9,11,12,33,54,89));

#delete documents of that type (including archived ones)
DELETE FROM documents WHERE type IN (2,4,9,11,12,33,54,89);
DELETE FROM archive_documents WHERE type IN (2,4,9,11,12,33,54,89);



# Delete customers of non existing types

#delete customer trademarks
DELETE FROM customers_trademarks WHERE parent_id IN (SELECT id FROM customers WHERE type IN (5));

#delete customer tags
DELETE FROM tags_models WHERE model_id IN (SELECT id FROM customers WHERE type IN (5)) AND model='Customer';

#delete customer relatives
DELETE FROM customers_relatives WHERE parent_id IN (SELECT id FROM customers WHERE type IN (5)) OR child_id IN (SELECT id FROM customers WHERE type IN (5));

#delete customer audit and history
DELETE ca.* FROM customers_history AS ch, customers_audit as ca WHERE ch.model='customer' AND ch.model_id IN (SELECT id FROM customers WHERE type IN (5)) AND ca.parent_id=ch.h_id;
DELETE FROM customers_history WHERE model='customer' AND model_id IN (SELECT id FROM customers WHERE type IN (5));

#delete customer variables
DELETE FROM customers_cstm WHERE model_id IN (SELECT id FROM customers WHERE type IN (5));
DELETE FROM bb WHERE model_id IN (SELECT id FROM customers WHERE type IN (5)) AND model='Customer';
DELETE FROM configurator WHERE model_id IN (SELECT id FROM customers WHERE type IN (5)) AND model_type='Customer';

#delete customer files
DELETE fi18n.*, f.* FROM files_i18n AS fi18n, files as f WHERE f.model_id IN (SELECT id FROM customers WHERE type IN (5)) AND f.model='Customer' AND f.id=fi18n.parent_id;

#delete customer crontab
DELETE FROM crontab WHERE model_id IN (SELECT id FROM customers WHERE type IN (5)) AND model='Customer';

#delete customer comments
DELETE FROM comments WHERE model_id IN (SELECT id FROM customers WHERE type IN (5)) AND model='Customer';

#delete sent emails
DELETE esb.* FROM emails_sentbox esb, customers c WHERE esb.model_id=c.id AND c.type IN (5);

#delete gt2 records, audit, indexes
DELETE gtai18n.*, gta.* FROM gt2_audit gta, gt2_audit_i18n gtai18n WHERE gtai18n.parent_id = gta.id  AND gta.row_id IN (SELECT id FROM gt2_details WHERE model = 'Customer' AND model_id IN (SELECT id FROM customers WHERE type IN (5)));
DELETE gt2i.*, gt2i18n.*, gt2.* FROM gt2_details gt2, gt2_details_i18n gt2i18n, gt2_indexes gt2i WHERE gt2i.parent_id = gt2.id AND gt2i18n.parent_id = gt2.id AND gt2.model='Customer' AND gt2.model_id IN (SELECT id FROM customers WHERE type IN (5));

#delete related records (used in autocompleters)
DELETE FROM cstm_relatives WHERE model='Customer' AND model_id IN (SELECT id FROM customers WHERE type IN (5)) OR cstm_model='Customer' AND cstm_model_id IN (SELECT id FROM customers WHERE type IN (5));

#delete contact, branches and customer themselves
DELETE ci.*, c1.* FROM customers_i18n as ci, customers c1, customers c2, customers c3 WHERE c1.id=ci.parent_id AND c1.subtype='contact' AND c1.parent_customer=c2.id AND c2.subtype='branch' AND c2.parent_customer=c3.id AND c3.type IN (5);
DELETE ci.*, c.* FROM customers_i18n as ci, customers c, customers c2 WHERE c.id=ci.parent_id AND c.subtype='branch' AND c.parent_customer=c2.id AND c2.type IN (5);
DELETE ci.*, c.* FROM customers_i18n as ci, customers c WHERE c.id=ci.parent_id AND c.subtype='normal' AND c.type IN (5);



# Update projects and announcements that have no type specified
UPDATE projects SET type=1 WHERE type=0;
UPDATE announcements SET type=14 WHERE type=0;

# Added settings to report 'bgs_work_outside_contracted'
UPDATE `reports`
SET `settings` = 'contract_type := 1\r\ncustomer_type := 8\r\ncustomer_tags := 6\r\ndoc_type_visit := 5\r\ndoc_type_remote := 85\r\n\r\nworking_day_start := 09:00\r\nworking_day_end := 18:00\r\n\r\n# interface settings\r\ndisplay_num_past_months := 24\r\nfreeze_table_headers := 1\r\ncreate_max_records := 100\r\n\r\n########################################\r\n#   settings for creation of records   #\r\n########################################\r\n\r\ncreate_model := document\r\ncreate_type := 106\r\nwork_article_id := 215\r\nequals_b_status := opened\r\nequals_b_substatus := 177\r\n\r\n# map financial companies to customers\r\nown_company1 := 5\r\nown_company2 := 2150\r\nown_company3 := 4634\r\nown_company4 := 5\r\n\r\n# basic and plain vars\r\ntransform_b_customer := customer\r\ntransform_b_branch := branch\r\ntransform_b_employee := employee\r\ntransform_b_date := date\r\ntransform_a_report_to := period_from\r\ntransform_a_report_do := period_to\r\ntransform_a_own_company := company_name\r\ntransform_a_own_company_id := company\r\n\r\n# config\r\ntransform_a_hours_tmin__contract := working_hour_num\r\ntransform_a_hours_tmin__period := total_working_hour_num_rounded\r\ntransform_a_hours_tmin__plus := over_working_hour_num\r\ntransform_a_hours_tmin__price := working_hour_price\r\ntransform_a_hours_tmin__total := over_working_hour_amount\r\n\r\ntransform_a_visit_tmin__contract := working_visit_num\r\ntransform_a_visit_tmin__period := total_working_visit_num\r\ntransform_a_visit_tmin__plus := over_working_visit_num\r\ntransform_a_visit_tmin__price := working_visit_price\r\ntransform_a_visit_tmin__total := over_working_visit_amount\r\n\r\ntransform_a_hours_tover__contract := overtime_hour_num\r\ntransform_a_hours_tover__period := total_overtime_hour_num_rounded\r\ntransform_a_hours_tover__plus := over_overtime_hour_num\r\ntransform_a_hours_tover__price := overtime_hour_price\r\ntransform_a_hours_tover__total := over_overtime_hour_amount\r\n\r\ntransform_a_visit_tover__contract := overtime_visit_num\r\ntransform_a_visit_tover__period := total_overtime_visit_num\r\ntransform_a_visit_tover__plus := over_overtime_visit_num\r\ntransform_a_visit_tover__price := overtime_visit_price\r\ntransform_a_visit_tover__total := over_overtime_visit_amount\r\n\r\n# GT\r\ntransform_a_work_done_docdate := docdate\r\ntransform_a_work_done_docnum := full_num\r\ntransform_a_work_done_docnum_id := id\r\ntransform_a_work_done := work_done\r\ntransform_a_work_done_employee := employee_name\r\ntransform_a_report_tmin := working\r\ntransform_a_report_tmin_round := working_rounded\r\ntransform_a_report_tover := overtime\r\ntransform_a_report_tover_round := overtime_rounded\r\ntransform_a_work_done_employee_id := employee_id\r\n'
WHERE `type` = 'bgs_work_outside_contracted';

######################################################################################
# 2017-05-25 - Updated incorrectly calculated values in fields in grouping tables

# Updated incorrectly calculated values in fields in grouping tables
SET @modified := '2017-05-22 00:00:00';

-- 63368
UPDATE `documents_cstm` SET `value` = '0:30' WHERE `model_id` = '63368' AND `var_id` = '59' AND `num` = '1' AND `lang` = '' AND `value` = '6:00' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '0:15' WHERE `model_id` = '63368' AND `var_id` = '511' AND `num` = '1' AND `lang` = '' AND `value` = '3:00' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '0:15' WHERE `model_id` = '63368' AND `var_id` = '511' AND `num` = '2' AND `lang` = '' AND `value` = '3:00' AND `modified` <= @modified;

-- 63414
UPDATE `documents_cstm` SET `value` = '0:20' WHERE `model_id` = '63414' AND `var_id` = '59' AND `num` = '1' AND `lang` = '' AND `value` = '0:10' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '0:20' WHERE `model_id` = '63414' AND `var_id` = '511' AND `num` = '1' AND `lang` = '' AND `value` = '0:10' AND `modified` <= @modified;

-- 63862
UPDATE `documents_cstm` SET `value` = '1:00' WHERE `model_id` = '63862' AND `var_id` = '59' AND `num` = '1' AND `lang` = '' AND `value` = '0:20' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '1:00' WHERE `model_id` = '63862' AND `var_id` = '511' AND `num` = '1' AND `lang` = '' AND `value` = '0:20' AND `modified` <= @modified;

-- 62296
UPDATE `documents_cstm` SET `value` = '02:31' WHERE `model_id` = '62296' AND `var_id` = '8508' AND `num` = '3' AND `lang` = '' AND `value` = '00:20' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '01:09' WHERE `model_id` = '62296' AND `var_id` = '8508' AND `num` = '4' AND `lang` = '' AND `value` = '02:25' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '00:30' WHERE `model_id` = '62296' AND `var_id` = '8508' AND `num` = '5' AND `lang` = '' AND `value` = '02:31' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '00:20' WHERE `model_id` = '62296' AND `var_id` = '8508' AND `num` = '6' AND `lang` = '' AND `value` = '01:09' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '00:00' WHERE `model_id` = '62296' AND `var_id` = '8508' AND `num` = '7' AND `lang` = '' AND `value` = '00:30' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '04:30' WHERE `model_id` = '62296' AND `var_id` = '8509' AND `num` = '6' AND `lang` = '' AND `value` = '00:00' AND `modified` <= @modified;
UPDATE `documents_cstm` SET `value` = '00:44' WHERE `model_id` = '62296' AND `var_id` = '8509' AND `num` = '7' AND `lang` = '' AND `value` = '00:00' AND `modified` <= @modified;

######################################################################################
# 2017-08-04 - Add relations between expenses invoices and warranty card documents

# Add relations between expenses invoices and warranty card documents
INSERT IGNORE INTO fin_reasons_relatives (parent_id, parent_model_name, link_to, link_to_model_name, rows_links, changes)
  SELECT d.       id            AS parent_id,
      'Document'                AS parent_model_name,
      fer.id                    AS link_to,
      'Finance_Expenses_Reason' AS link_to_model_name,
      NULL                      AS rows_links,
      ''                        AS changes
    FROM documents AS d
    JOIN gt2_details AS g
      ON (d.`type` = 92
        AND g.model = 'Document'
        AND g.model_id = d.id)
    JOIN gt2_details AS g1
      ON (g1.model = 'Finance_Expenses_Reason'
        AND g1.id = g.free_field2)
    JOIN fin_expenses_reasons AS fer
      ON (fer.`type` = '20'
        AND fer.id = g1.model_id)
    GROUP BY fer.id, d.id;

#########################################################################################
# 2017-08-08 - Added value of setting for optional tags for revenue documents to ignore

# Added value of setting for optional tags for revenue documents to ignore
UPDATE `reports`
SET `settings` = REPLACE(`settings`, 'ignore_tags := \r\n', 'ignore_tags := 70\r\n')
WHERE `type` = 'debt_collection_email_sender' AND `settings` LIKE '%ignore_tags := \r\n%';

#########################################################################################
# 2017-09-08 - Cut the relations between the cloned documents of type 13

# Cut the relations between the cloned documents of type 13
UPDATE documents_relatives AS dr
  JOIN documents AS d
    ON (d.`type` = 13
      AND dr.link_to_model_name = 'Document'
      AND dr.link_to = d.id
      AND dr.origin = 'cloned'
      AND dr.parent_model_name = 'Document')
  JOIN documents AS d1
    ON (d1.type = 13
      AND d1.id = dr.parent_id)
  SET dr.parent_model_name = 'Document-deleted',
    dr.link_to_model_name = 'Document-deleted';

#########################################################################################
# 2017-11-13 - Added 'use_old_model' flag to automation performed on source model (reason) on creation of proforma invoice
#            - Added automation for copying generated file from document to invoice when invoice is issued from reason transformed from document

# Added 'use_old_model' flag to automation performed on source model (reason) on creation of proforma invoice
UPDATE `automations`
SET `settings` = CONCAT(IFNULL(`settings`, ''), IF(IFNULL(`settings`, '') != '', '\r\n', ''), 'use_old_model := 1')
WHERE `module` = 'finance' AND `controller` = 'incomes_reasons' AND `start_model_type` > 100 AND `conditions` LIKE '%''addproformainvoice''%' AND `settings` NOT LIKE '%use_old_model%';

# Added automation for copying generated file from document to invoice when invoice is issued from reason transformed from document
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Копиране на файл от Отчет за изработено време при издаване на Фактура', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '1', 'fir_type := 104\ndoc_type := 106\ndoc_pattern_id := 96', 'condition := \'[action]\' == \'addinvoice\'', 'plugin := bgservice\r\nmethod := attachWorkedTimeReportFile', NULL, 1, 1, 1);

#########################################################################################
# 2017-11-24 - Added installation-specific settings to general report 'turnover_comparison'

# Added installation-specific settings to general report 'turnover_comparison'
UPDATE `reports`
SET `settings` = '# possible values: \"all\" / \"none\" / list of ids > 100\r\nfir_types_reasons := 104\r\n# possible values: \"all\" / \"none\" / list of ids: 1, 3, 4\r\nfir_types_invoices := all\r\n\r\n# list of customer types for customer and tag filters\r\ncustomer_types := 8\r\n\r\n# list of nomenclature types for article filter\r\narticle_types := 7\r\n\r\n# enabled optional filters: \"all\" / empty value / list of the following: fir_types, company, office, employee, department, article, article_category, customer, trademark, tag, currency\r\nenabled_filters := all\r\n\r\n# filters to select multiple values for: empty value / list of the following: employee, department, article, article_category, customer, trademark, tag\r\nmultiple_filters := article, customer, tag\r\n\r\n# absolute or relative year to start period options from (absolute has priority if both set)\r\nperiod_start_year := \r\nperiod_num_past_years := 4\r\n\r\n# default period duration and offset between periods\r\nperiod_default_num_months := 12\r\n\r\n# enables search of inactive employees when 1\r\nemployee_include_inactive := 0\r\n'
WHERE `type` = 'turnover_comparison';

#########################################################################################
# 2018-01-11 - Rename automation 'sendEmailForUnattendedClient' to 'sendEmailsForUnattendedClients' and change some settings

# Rename automation 'sendEmailForUnattendedClient' to 'sendEmailsForUnattendedClients' and change some settings
UPDATE automations
  SET settings = 'start_after := 01:00\r\nstart_before := 04:00\r\n\r\nunattended_days := 30\r\nemail_template_id := 1025\r\nemails_office_manastirska := <EMAIL>,<EMAIL>,<EMAIL>\r\nemails_office_varna := <EMAIL>',
    method = REPLACE(method, 'sendEmailForUnattendedClient', 'sendEmailsForUnattendedClients')
  WHERE method LIKE '%sendEmailForUnattendedClient%';

#########################################################################################
# 2018-05-10 - Added dashlet plugin for ticket management

# Added dashlet plugin for ticket management
UPDATE `settings`
SET `value` = 'validity_term'
WHERE `section` = 'documents'
  AND `name` = 'validate_121';

INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`)  VALUES
(NULL, 'documents', 'row_link_action_121', 'href := launch=dashlets&dashlets=custom_action&plugin=bgs_tickets&custom_plugin_action=load&force=1&id=b_id&model_lang=b_model_lang\r\ntarget := lightbox\r\nlightbox_height := auto\r\nlightbox_background_color := #f1f1f1\r\nmodule := documents');

DELETE dp.*, dpi.*
FROM `dashlets_plugins` dp
LEFT JOIN `dashlets_plugins_i18n` dpi
  ON dp.id = dpi.parent_id
WHERE dp.`type` = 'bgs_tickets';

DELETE dpi.*
FROM `dashlets_plugins_i18n` dpi
WHERE (SELECT `id` FROM `dashlets_plugins` WHERE `id` = dpi.`parent_id`) IS NULL;

INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'bgs_tickets', 'doc_type_ticket := 121\r\ndoc_type_it_passport := 35\r\ncust_type_client := 8\r\ncust_types_search := 8,7\r\n\r\n# only type 8 (client) has such vars\r\nclient_sla_var := customer_sla\r\nclient_spiceworks_url_var := spiceworks_url\r\nclient_assigned_employee_name1_var := assigned_customer\r\nclient_assigned_employee_name2_var := assigned_substitute\r\n\r\n# ticket vars\r\nticket_phone_var := client_phone\r\nticket_email_var := client_mail\r\nticket_category_var := ticket_for\r\nticket_priority_var := ticket_priority\r\nticket_reaction_time_var := reaction_time\r\nticket_information_security_var := Information_security\r\n\r\n', 0, 1);

INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Тикети', NULL, 'bg'),
(LAST_INSERT_ID(), 'Tickets', NULL, 'en');

#########################################################################################
# 2018-05-16 - Added call event processing to dashlet plugin for ticket management

# Added call event processing to dashlet plugin for ticket management
INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`) VALUES
(NULL, 'events', 'row_link_action_10', 'href := launch=dashlets&dashlets=custom_action&plugin=bgs_tickets&custom_plugin_action=manageCall&force=1&event_id=b_id&model_lang=b_model_lang\r\ntarget := lightbox\r\nlightbox_height := auto\r\nlightbox_background_color := #f1f1f1\r\nmodule := events');

UPDATE `dashlets_plugins`
SET `settings` = 'doc_type_ticket := 121\r\ndoc_type_it_passport := 35\r\ncust_type_client := 8\r\ncust_types_search := 8,7\r\n\r\nevent_type_call := 10\r\ndoc_type_request := 22\r\nproj_type_inquiry := 6\r\ncust_id_unknown := 13191\r\n\r\n# only type 8 (client) has such vars\r\nclient_sla_var := customer_sla\r\nclient_spiceworks_url_var := spiceworks_url\r\nclient_assigned_employee_name1_var := assigned_customer\r\nclient_assigned_employee_name2_var := assigned_substitute\r\n\r\n# ticket vars\r\nticket_phone_var := client_phone\r\nticket_email_var := client_mail\r\nticket_category_var := ticket_for\r\nticket_priority_var := ticket_priority\r\nticket_reaction_time_var := reaction_time\r\nticket_information_security_var := Information_security\r\n'
WHERE `type` = 'bgs_tickets' AND `settings` NOT LIKE '%event_type_call%';

UPDATE `layouts` l JOIN `layouts_i18n` li
  ON l.`layout_id` = li.`parent_id` AND li.`lang` = 'bg' and l.`model` = 'Event' AND l.`model_type` = 10 AND l.`keyname` = 'location'
SET li.`name` = 'Телефон'
WHERE li.`name` = 'Място';

SELECT @dashlet_id := `id`
FROM `dashlets`
WHERE `module` = 'events' AND `added_by` = 1
ORDER BY `id` DESC
LIMIT 0, 1;

INSERT IGNORE INTO `dashlets` (`id`, `module`, `controller`, `col_settings`, `filters`, `full_width`, `records_per_page`, `active`, `default`, `group`, `is_portal`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
(@dashlet_id, 'events', 'events', 'a:2:{s:7:\"visible\";a:5:{i:0;s:1:\"1\";i:1;s:1:\"1\";i:2;s:1:\"1\";i:3;s:1:\"1\";i:4;s:1:\"1\";}s:7:\"columns\";a:5:{i:0;s:8:\"customer\";i:1;s:8:\"location\";i:2;s:8:\"added_by\";i:3;s:11:\"event_start\";i:4;s:6:\"status\";}}', 'YTo3OntzOjEzOiJzZWFyY2hfZmllbGRzIjthOjY6e2k6MDtzOjY6ImUudHlwZSI7aToxO3M6ODoiZS5hY3RpdmUiO2k6MjtzOjg6ImUuc3RhdHVzIjtpOjM7czo4OiJlLnN0YXR1cyI7aTo0O3M6MTI6ImVybDEubGlua190byI7aTo1O3M6MTI6ImVybDIubGlua190byI7fXM6MTU6ImNvbXBhcmVfb3B0aW9ucyI7YTo2OntpOjA7czo4OiI9IFwnJXNcJyI7aToxO3M6ODoiPSBcJyVzXCciO2k6MjtzOjg6Ij0gXCclc1wnIjtpOjM7czo4OiI9IFwnJXNcJyI7aTo0O3M6ODoiPSBcJyVzXCciO2k6NTtzOjg6Ij0gXCclc1wnIjt9czo2OiJ2YWx1ZXMiO2E6Njp7aTowO3M6MjoiMTAiO2k6MTtzOjE6IjEiO2k6MjtzOjg6ImZpbmlzaGVkIjtpOjM7czo5OiJ1bnN0YXJ0ZWQiO2k6NDtzOjA6IiI7aTo1O3M6MDoiIjt9czoxOToidmFsdWVzX2F1dG9jb21wbGV0ZSI7YToyOntpOjQ7czowOiIiO2k6NTtzOjA6IiI7fXM6MTY6ImxvZ2ljYWxfb3BlcmF0b3IiO2E6NTp7aTowO3M6MzoiQU5EIjtpOjE7czozOiJBTkQiO2k6MjtzOjI6Ik9SIjtpOjM7czozOiJBTkQiO2k6NDtzOjM6IkFORCI7fXM6NDoic29ydCI7YToxOntpOjA7czo3OiJlLmFkZGVkIjt9czo1OiJvcmRlciI7YToxOntpOjA7czo0OiJERVNDIjt9fQ==', 0, 5, 1, 0, 1, 0, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);

INSERT IGNORE INTO `dashlets_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`)
SELECT `id`, 'Обаждания', 'Необработени импортирани обаждания', 'bg', NOW()
FROM `dashlets`
WHERE `module` = 'events' AND `added_by` = 1
ORDER BY `id` DESC
LIMIT 0, 1;

#########################################################################################
# 2018-08-13 - New automation for coping vars from modified active contracts to the relavent customer

# New automation for coping vars from modified active contracts to the relavent customer
DELETE FROM `automations` WHERE `method` LIKE '%copyContractDataToCustomer%';
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
VALUES ('Копиране на данни от договор към контрагент', 0, NULL, 1, 'contracts', NULL, 'crontab', '1', 'period := 1 day\r\nstart_time := 03:00\r\n\r\nsend_to_email := <EMAIL>\r\n\r\n# Тип на контрагента\r\ncustomer_type := 8\r\n\r\n# Имена на полетата, които ще се копират. Името на настройката е името на променливата в договори с префикс field_.\r\n# Стойността е името на променливата в клиент.\r\nfield_customer_sla := customer_sla\r\nfield_working_visit_num := working_visit_num\r\nfield_working_hour_num := working_hour_num\r\nfield_working_hour_price := working_hour_price\r\nfield_working_hour_first := working_hour_first\r\nfield_working_hour_next := working_hour_next\r\nfield_overtime_hour_num := overtime_hour_num\r\nfield_overtime_hour_price := overtime_hour_price\r\nfield_overtime_hour_first := overtime_hour_first\r\nfield_overtime_hour_next := overtime_hour_next\r\nfield_computers__descriptions := computers__descriptions\r\nfield_computers__quontity := computers__quontity\r\nfield_servers__descriptions := servers__descriptions\r\nfield_servers__quontity := servers__quontity\r\nfield_routers__descriptions := routers__descriptions\r\nfield_routers__quontity := routers__quontity\r\nfield_periphery__descriptions := periphery__descriptions\r\nfield_periphery__quontity := periphery__quontity\r\nfield_monday__to := monday__to\r\nfield_monday__do := monday__do\r\nfield_saturday__to := saturday__to\r\nfield_saturday__do := saturday__do\r\nfield_sunday__to := sunday__to\r\nfield_sunday__do := sunday__do\r\nfield_remark_desc := remark_desc', 'where := co.active = 1\r\nwhere := co.deleted_by = 0\r\nwhere := DATE(co.modified) = DATE(NOW()- INTERVAL 1 DAY)', 'plugin := bgservice\r\nmethod := copyContractDataToCustomer', NULL, 0, 0, 1);

#########################################################################################
# 2018-08-31 - Adding 3 new settings for copy vars from contract to customer

# Adding 3 new settings for copy vars from contract to customer
UPDATE `automations`
SET settings = 'period := 1 day\r\nstart_time := 03:00\r\n\r\nsend_to_email := <EMAIL>\r\n\r\n# Тип на контрагента\r\ncustomer_type := 8\r\n\r\n# Имена на полетата, които ще се копират. Името на настройката е името на променливата в договори с префикс field_.\r\n# Стойността е името на променливата в клиент.\r\nfield_customer_sla := customer_sla\r\nfield_working_visit_num := working_visit_num\r\nfield_working_hour_num := working_hour_num\r\nfield_working_hour_price := working_hour_price\r\nfield_working_hour_first := working_hour_first\r\nfield_working_hour_next := working_hour_next\r\nfield_overtime_hour_num := overtime_hour_num\r\nfield_overtime_hour_price := overtime_hour_price\r\nfield_overtime_hour_first := overtime_hour_first\r\nfield_overtime_hour_next := overtime_hour_next\r\nfield_computers__descriptions := computers__descriptions\r\nfield_computers__quontity := computers__quontity\r\nfield_servers__descriptions := servers__descriptions\r\nfield_servers__quontity := servers__quontity\r\nfield_routers__descriptions := routers__descriptions\r\nfield_routers__quontity := routers__quontity\r\nfield_periphery__descriptions := periphery__descriptions\r\nfield_periphery__quontity := periphery__quontity\r\nfield_monday__to := monday__to\r\nfield_monday__do := monday__do\r\nfield_saturday__to := saturday__to\r\nfield_saturday__do := saturday__do\r\nfield_sunday__to := sunday__to\r\nfield_sunday__do := sunday__do\r\nfield_remark_desc := remark_desc\r\nfield_working_visit_price := working_visit_price\r\nfield_overtime_visit_price := overtime_visit_price\r\nfield_overtime_visit_num := overtime_visit_num'
WHERE method LIKE "%copyContractDataToCustomer%";

#########################################################################################
# 2018-09-19 - Updated settings of 'bgservice_warranty_cards' report
#            - Updated 'added_by' of files attached by 'afterWarrantyCard' automation to be original user

# Updated settings of 'bgservice_warranty_cards' report
UPDATE `reports`
SET `settings` = CONCAT(`settings`, '\r\nnom_cats_warranty := 28')
WHERE `type` = 'bgservice_warranty_cards' AND `settings` NOT LIKE '%nom_cats_warranty%';

# Updated 'added_by', 'modified_by' of files attached by 'afterWarrantyCard' automation to be original user
UPDATE `documents` d
JOIN `files` f
  ON d.`id` = f.`model_id` AND f.`model` = 'Document' AND d.`type` = 92 AND f.`origin` = 'attached' AND d.`added_by` > 0 AND f.`added_by` = -1
SET f.`added_by` = d.`added_by`, f.`modified_by` = d.`added_by`;

#########################################################################################
# 2018-10-16 - new settings for report ustil_expenses_analysis

# new settings for report ustil_expenses_analysis
UPDATE `reports` SET `settings`='csea_report_customer_model := Finance_Expenses_Reason\r\ncsea_report_customer_model_type := 20,21,22,23,101,102,109\r\ncsea_report_customers_types := 1,3,7,8,10,11,12,14,15,16,17\r\ncsea_report_expenses_wo_invoice := 101,102,109\r\ncsea_report_incom_proform_invoice := 21\r\ncsea_report_incom_invoice := 20\r\ncsea_report_payment_order := PN\r\ncsea_report_expense_cash_receipts := RKO\r\n\r\n# Настройва АК къде да търси. Ако е празно ще търси в номенклатури.\r\ncsea_report_article_deliverer_mode := customers\r\ncsea_report_article_deliverer_filterstype := 7\r\ncsea_report_article_filterstype := 7\r\n\r\n# Етикет да за служител/лице/създал документа\r\ncsea_report_employeemoney_label := Създал/добавил документа\r\n\r\n#Покажи служител във филтри и резултатна таблица? true или false\r\ncsea_report_show_employee := false\r\n\r\n# От кое поле да се взема описание на разхода. Следващата настройка е алиас на таблицата в която се намира.\r\n# Текущи алиаси: gt2_details = g2d, gt2_details_i18n = g2d_i, finance_expenses_reasons_i18n = fer_di, customers_i18n = ci и други.\r\ncsea_report_tips_expense_field := article_description\r\ncsea_report_tips_expense_field_table_alias := g2d_i\r\n'
WHERE  `type` = 'ustil_expenses_analysis';

#########################################################################################
# 2018-11-16 - Added new crontab automation which will create documents or comments from e-mails

# Added new crontab automation which will create documents or comments from e-mails
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Обработване на отговори на служебни писма', 0, NULL, 1, 'documents', NULL, 'crontab', '121', 'server :=\r\nemail :=\r\nport :=\r\npassword :=\r\ncert :=\r\n\r\ncreate_model_name := document\r\ncreate_model_type_id := 121\r\n\r\nemployee := \r\nvalidity_term_calc_period :=\r\n\r\ndefault_customer := 13191\r\nticket_priority :=', 'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 1, 0, 0);

#########################################################################################
# 2018-11-20 - new condition when new tag is added on customer the automation will work

# new condition when new tag is added on customer the automation will work
UPDATE `automations` SET `conditions`='where := co.active = 1\r\nwhere := co.deleted_by = 0\r\nwhere := DATE(co.modified) = DATE(NOW()- INTERVAL 1 DAY) OR\r\nwhere := co.id IN (SELECT con.id FROM customers AS c JOIN customers_history AS ch ON (c.type = 8 AND DATE(ch.h_date) = DATE(NOW() - INTERVAL 1 DAY) AND c.active = 1 AND c.deleted = 0 AND c.id = ch.model_id AND action_type = \'tag\')  JOIN tags_models AS tm ON (tag_id = 6 AND c.id = tm.model_id AND tm.model = \'Customer\')  JOIN contracts AS con ON (c.id = con.customer AND con.`type` = 1 AND con.active = 1 AND con.deleted = 0 AND con.subtype = \'contract\')  JOIN gt2_details AS gt2 ON (con.id = gt2.model_id AND gt2.`model` = \'Contract\')  JOIN gt2_details_i18n AS gti18n ON (gt2.id = gti18n.parent_id AND gt2.article_id = 184)GROUP BY con.id)'
 WHERE  `name` LIKE 'Копиране на данни от договор към контрагент';

#########################################################################################
# 2018-11-23 - Added setting for reaction time var for customer in the processRepliedEmails automation

# Added setting for reaction time var for customer in the processRepliedEmails automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nreaction_time_var :=') WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%reaction_time_var%';

#########################################################################################
# 2019-04-01 - Added setting for assigned observers in the processRepliedEmails automation

# Added setting for assigned observers in the processRepliedEmails automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nassign_observers :=') WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%assign_observers%';

#########################################################################################
# 2019-06-13 - Added settings to dashlet plugin for ticket management

# Added settings to dashlet plugin for ticket management
UPDATE `dashlets_plugins`
SET `settings` = 'doc_type_ticket := 121\r\ndoc_type_it_passport := 35\r\ncust_type_client := 8\r\ncust_types_search := 8,7\r\n\r\nevent_type_call := 10\r\ndoc_type_request := 22\r\nproj_type_inquiry := 6\r\ncust_id_unknown := 13191\r\ncust_tags_groups := 13, 14\r\n\r\n# only type 8 (client) has such vars\r\nclient_sla_var := customer_sla\r\nclient_spiceworks_url_var := spiceworks_url\r\nclient_assigned_employee_name1_var := assigned_customer\r\nclient_assigned_employee_name2_var := assigned_substitute\r\n\r\n# ticket vars\r\nticket_phone_var := client_phone\r\nticket_email_var := client_mail\r\nticket_category_var := ticket_for\r\nticket_priority_var := ticket_priority\r\nticket_reaction_time_var := reaction_time\r\nticket_solution_time_var := \r\nticket_information_security_var := Information_security\r\n\r\nworking_day_start := 09:00\r\nworking_day_end := 18:00\r\n\r\ndashlets_reload := 187\r\n'
WHERE `type` = 'bgs_tickets' AND `settings` NOT LIKE '%cust_tags_groups%';

#########################################################################################
# 2019-06-14 - Added setting hashtag field which will be matched when e-mails are processed by processRepliedEmails automation

# Added setting hashtag field which will be matched when e-mails are processed by processRepliedEmails automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nhashtag_field_match :=') WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%hashtag_field_match%';

#########################################################################################
# 2019-06-17 - Added automation for generation of GT2 of "Worked Time Report" document

# Added automation for generation of GT2 of "Worked Time Report" document
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Създаване на GT2 на Отчет за изработено време', 0, NULL, 1, 'documents', NULL, 'action', '106', 'contract_type := 1\r\ncustomer_type := 8\r\ndoc_type_visit := 5\r\ndoc_type_remote := 85\r\n\r\nwork_article_id := 215\r\ncompany := own_company_id\r\n\r\n# map financial companies to customers\r\nown_company1 := 5\r\nown_company2 := 2150\r\nown_company3 := 4634\r\nown_company4 := 5\r\n\r\n# config vars\r\nworking_hour_num := hours_tmin__contract\r\nworking_hour_price := hours_tmin__price\r\nworking_visit_num := visit_tmin__contract\r\nworking_visit_price := visit_tmin__price\r\novertime_hour_num := hours_tover__contract\r\novertime_hour_price := hours_tover__price\r\novertime_visit_num := visit_tover__contract\r\novertime_visit_price := visit_tover__price\r\n\r\n# GT\r\ndocdate := work_done_docdate\r\ndocid := work_done_docnum_id\r\nworking_rounded := report_tmin_round\r\novertime_rounded := report_tover_round\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'add\', \'edit\'))', 'plugin := bgservice\r\nmethod := createWorkedTimeReportGt2', NULL, 1, 0, 1);

#########################################################################################
# 2019-07-03 - Added new reminder type to "remindCarDates" automation

# Added new reminder type to "remindCarDates" automation
UPDATE `automations` SET settings=CONCAT(settings, '\r\nfield_nom_vehicle_casco_ins_payment := casco_ins_payment') WHERE id=101 AND settings NOT LIKE '%field_nom_vehicle_casco_ins_payment%';

#########################################################################################
# 2019-12-10 - Added placeholders for the e-mail sent by 'offersStatusesAndNotifications' automation for notifing the employee for offer waiting for reply
#            - Added additional setting for offersStatusesAndNotifications automation which will point which var data will be taken as offer date

# Added placeholders for the e-mail sent by 'offersStatusesAndNotifications' automation for notifing the employee for offer waiting for reply
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'expired_offers_table', 'Document', 'send', 'emails', ',1034,', 'expired_offers_table', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица с оферти, за които се чака отговор', NULL, 'bg'),
(LAST_INSERT_ID(), 'Offers, waiting for reply', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'expired_offers_user_name', 'Document', 'send', 'emails', ',1034,', 'expired_offers_user_name', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Служител, изпратил офертата', NULL, 'bg'),
(LAST_INSERT_ID(), 'Sent by employee', NULL, 'en');

# Added additional setting for offersStatusesAndNotifications automation which will point which var data will be taken as offer date
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\t\n\r\ncheck_var := a_offer_date') WHERE `method` LIKE '%offersStatusesAndNotifications%' AND `settings` NOT LIKE '%check_var%';

#########################################################################################
# 2020-02-05 - Added setting for vignette reminder in remindCarDates automation

# Added setting for vignette reminder in remindCarDates automation
UPDATE automations
SET settings=REPLACE(settings, 'field_nom_vehicle_vignette_date_finish := \r\n', 'field_nom_vehicle_vignette_date_finish := vignette_date_finish\r\n')
WHERE method LIKE '%remindCarDates%' AND settings LIKE '%field_nom_vehicle_vignette_date_finish := \r\n%';

#########################################################################################
# 2020-03-10 - Changed settings for processRepliedEmails automation

# Changed settings for processRepliedEmails automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nreaction_time_var := working_visit_price\r\n', '\r\ncust_to_doc_vars := working_visit_price => reaction_time\r\ndefault_doc_vars :=\r\n') WHERE `method` LIKE '%processRepliedEmails%' AND `method` LIKE '%bgservice%' AND `settings` NOT LIKE '%cust_to_doc_vars%';

#########################################################################################
# 2020-03-23 - Added new automation to process cleits requests from e-mail

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Обработване на писма към клиентски заявки (Email2nZoom)', 0, NULL, 1, 'documents', '', 'crontab', '22', 'server :=\r\nemail :=\r\nport :=\r\npassword :=\r\ncert :=\r\nprocessed_mails_folder := INBOX/processed_emails\r\n\r\nskip_add_new_model :=\r\ncreate_model_name_on_model_match := comment\r\ncreate_model_type_id := 22\r\n\r\nemployee := 14247\r\nvalidity_term_calc_period := +0 day\r\n\r\ndefault_customer := 13191\r\nticket_priority :=\r\n\r\ndefault_doc_vars := priority_inq => priorityinq_low, type_inq => typeinq_bug, link_problem => #\r\nreaction_time_var :=\r\nassign_observers :=\r\nhashtag_field_match :=', 'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 0, 0, 1);

#########################################################################################
# 2020-05-11 - Adding 2 new settings for copy vars from contract to customer

# Adding 2 new settings for copy vars from contract to customer
UPDATE `automations`
SET settings = CONCAT(settings, '\r\nfield_workingoffice_hour_first := workingoffice_hour_first\r\nfield_overtimeoffice_hour_first := overtimeoffice_hour_first\r\n')
WHERE method LIKE "%copyContractDataToCustomer%" AND settings NOT LIKE '%field_workingoffice_hour_first%';

#########################################################################################
# 2020-10-21 - Fixed phone call events with zero duration

# Fixed phone call events with zero duration
UPDATE events SET duration=1 WHERE duration=0 AND type=10;

#########################################################################################
# 2020-10-27 - Added corrections to automations, dashlet and customer autocompleter for tickets

# update setAdditionalVar reaction_time to work in edit mode
UPDATE `automations`
SET conditions='condition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\') && \'[request_is_post]\'==\'1\' && \'[a_reaction_time]\' == \'\''
WHERE id=181;

# Updated settings for calculateTicketAcceptTime
UPDATE `automations`
SET settings=CONCAT(settings, "\nuse_updated_model := 1")
WHERE id=184 AND settings NOT LIKE "%use_updated_model%";
#make it start only once
UPDATE `automations`
SET nums=1
WHERE id=184 AND nums=0;

# Update conditions for automation 189
UPDATE `automations`
SET conditions=REPLACE(conditions, "[b_customer]", "[prev_b_customer]")
WHERE id=189 AND conditions LIKE "%[b_customer]%";

# Add specific customer for document Ticket (121)
INSERT IGNORE INTO `_fields_meta` (`model`, `model_type`, `name`, `source`, `validate`, `type`, `searchable`, `sortable`, `outlooks`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
('Document', 121, 'customer', 'autocomplete := customers\r\nautocomplete_search := <name>, <code>\r\nautocomplete_sort := <name>\r\nautocomplete_fill_options := $customer => <id>\r\nautocomplete_fill_options := $customer_autocomplete => [<code>] <name> <lastname>\r\nautocomplete_fill_options := $customer_oldvalue => [<code>] <name> <lastname>\r\nautocomplete_fill_options := $ticket_email_to => <a_ticket_email_to>\r\nautocomplete_fill_options := $reaction_time => <a_customer_sla>\r\nautocomplete_filter := <type> => 16,15,12,8\r\nautocomplete_clear := 1\r\nautocomplete_add := 1', '', 'autocompleter', '', 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6285, 0, '', '', '');

# Added default settings to dashlet plugin
UPDATE `dashlets_plugins`
SET settings=CONCAT(settings, "\n\ndefault_priority := 3\ndefault_category := 1")
WHERE type="bgs_tickets" AND settings NOT LIKE "%default_priority%";

#########################################################################################
# 2020-11-09 - Added field to employee for sending emails, added two send fields for tickets (Document 121)

# Added field to employee for sending emails, added two send fields for tickets (Document 121)
 INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `searchable`, `sortable`, `outlooks`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
 (7470, 'Document', 121, 'custom_from_name', NULL, NULL, 'text', 'text', 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6300, 8, '', '', ''),
 (7469, 'Document', 121, 'custom_sender', NULL, NULL, 'text', 'text', 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6300, 8, '', '', ''),
 (783, 'Customer', 1, 'ticket_email_from', NULL, NULL, 'text', 'text', 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 840, 8, '', '', '');

 INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
 (7470, 'label', 'From Name', 'bg'),
 (7469, 'label', 'From Email', 'bg'),
 (783, 'label', 'Email за Тикети', 'bg');

# Added automation for tickets (Document 121) that set custom_from_name/custom_sender for
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Вписване на email за кореспонденция в тикети', 0, NULL, 1, 'documents', NULL, 'action', '121', '', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\'==\'1\' && \'[a_custom_sender]\' == \'\'', 'method := setAdditionalVar\r\nvar_name := custom_from_name|custom_sender\r\nvar_value := BGService|php(\'employee\'; $employee_id = $request->get(\'employee\'); $employee=$employee_id  ? Customers::searchOne($this->registry, array(\'where\'=>[\'c.id = \' . $employee_id])) : null;$custom_sender = $employee ? $employee->getVarValue(\'ticket_email_from\'):\'\';$result = $custom_sender ?:\'<EMAIL>\';)\r\ndelimiter := |', NULL, 15, 1, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 121 AND automation_type = 'action' AND method LIKE '%custom_sender%');

#########################################################################################
# 2020-11-10 - Updated password in processRepliedEmails automation with encrypted ones
#            - Update the default vars for newly created documents in processRepliedEmails automations
#            - Added synchronization of tickets and system tasks

# Updated password in processRepliedEmails automation with encrypted ones
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'password := tnSW5fnTt6hp', 'password := /K7PksGVyYhL9ihvwin/9g==')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%password := tnSW5fnTt6hp%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'password := Txq228FhpV57', 'password := UtzJGiXQ8BGmHvha7zH8Uw==')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%password := Txq228FhpV57%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'password := 4C7PHQqQ7NFB', 'password := ok/KV+7SaHKuAs7CCnXc7Q==')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%password := 4C7PHQqQ7NFB%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'password := DxHF2bbNwNnK', 'password := PDmpOtNzrtd0ETAoa6sH/w==')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%password := DxHF2bbNwNnK%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'password := aqI3YxnlKRpmB2QB', 'password := tfb0dzPJadt0zhoaFM/FbHYE6jatXXoc')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%password := aqI3YxnlKRpmB2QB%';

# Update the default vars for newly created documents in processRepliedEmails automations
UPDATE `automations`
SET `settings` = REPLACE(`settings`, '\r\nassign_observers :=', ', custom_sender => <EMAIL>, custom_from_name => BGService\r\nassign_observers :=')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%custom_sender%' AND `settings` LIKE '%<EMAIL>%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, '\r\nassign_observers :=', ', custom_sender => <EMAIL>, custom_from_name => BGService\r\nassign_observers :=')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%custom_sender%' AND `settings` LIKE '%<EMAIL>%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, '\r\nreaction_time_var :=', ', custom_sender => <EMAIL>, custom_from_name => BGService\r\nreaction_time_var :=')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%custom_sender%' AND `settings` LIKE '%<EMAIL>%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, '\r\nassign_observers :=', ', custom_sender => <EMAIL>, custom_from_name => BGService\r\nassign_observers :=')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%custom_sender%' AND `settings` LIKE '%<EMAIL>%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, '\r\nassign_observers :=', ', custom_sender => <EMAIL>, custom_from_name => BGService\r\nassign_observers :=')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%custom_sender%' AND `settings` LIKE '%<EMAIL>%';

# Added synchronization of tickets and system tasks
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Преотваряне на системна задача', 0, NULL, 1, 'documents', NULL, 'action', '121', '', 'condition := \'[prev_b_substatus]\' != 222\r\ncondition := \'[b_substatus]\' == 222\r\n', 'method := syncSystemTask\r\n', NULL, 99, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 121 AND automation_type = 'action' AND method LIKE '%syncSystemTask%' AND conditions LIKE '%prev_b_substatus%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Сменяне на контрагент на системна задача', 0, NULL, 1, 'documents', NULL, 'action', '121', '', 'condition := \'[action]\' == \'edit\' && \'[request_is_post]\' == 1\r\n', 'method := syncSystemTask', NULL, 99, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 121 AND automation_type = 'action' AND method LIKE '%syncSystemTask%' AND conditions LIKE '%edit%');

#########################################################################################
# 2020-11-11 - Update the custom sender e-mails to match the e-mail for each automation

# Update the custom sender e-mails to match the e-mail for each automation
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'custom_sender => <EMAIL>', 'custom_sender => <EMAIL>')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%custom_sender => <EMAIL>%' AND `settings` LIKE '%<EMAIL>%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'custom_sender => <EMAIL>', 'custom_sender => <EMAIL>')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%custom_sender => <EMAIL>%' AND `settings` LIKE '%<EMAIL>%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'custom_sender => <EMAIL>', 'custom_sender => <EMAIL>')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%custom_sender => <EMAIL>%' AND `settings` LIKE '%<EMAIL>%';
UPDATE `automations`
SET `settings` = REPLACE(`settings`, 'custom_sender => <EMAIL>', 'custom_sender => <EMAIL>')
WHERE `method` LIKE '%processRepliedEmails%' AND `settings` LIKE '%custom_sender => <EMAIL>%' AND `settings` LIKE '%<EMAIL>%';

#########################################################################################
# 2020-11-19 - Added merge of tickets: variables, dashlet settings and automation
#            - Added additional settings for bgservice_remote_work report

# Added merge of tickets: variables, dashlet settings and automation
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `source`, `validate`, `type`, `searchable`, `sortable`, `outlooks`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(7471, 'Document', 121, 'merge_document_id', NULL, NULL, 'text', 'text', 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6300, 8, '', '', ''),
(7472, 'Document', 121, 'merge_document', NULL, NULL, 'text', 'text', 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6300, 8, '', '', '');

INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(7471, 'label', 'Обединен към (ID)', 'bg'),
(7472, 'label', 'Обединен към', 'bg');

 UPDATE `dashlets_plugins`
 SET `settings` = CONCAT(settings, "\r\nticket_substatus_merged :=220\r\nticket_merge_document_var := merge_document\r\n")
 WHERE `type` = 'bgs_tickets' AND `settings` NOT LIKE '%ticket_substatus_merged%';

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Обединяване на тикет', 0, NULL, 1, 'documents', NULL, 'action', '121', 'var_merge_destination_document_id := merge_document_id\r\nstatus := closed\r\nsubstatus := 220', 'condition := \'[action]\' == \'edit\' && "[a_merge_document_id]" != ""\r\n', 'plugin := bgservice\r\nmethod := mergeTicket', NULL, 99, 1, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 121 AND automation_type = 'action' AND method LIKE '%mergeTicket%');

# Added additional settings for bgservice_remote_work report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nremote_work_date_work :=', '\r\ndocument_type_ticket_id := 121\r\ntask_type_fast_report_id := 13\r\n\r\nremote_work_date_work :=') WHERE `type`='bgservice_remote_work' AND `settings` NOT LIKE '%document_type_ticket_id%';

#########################################################################################
# 2020-11-23 - Fixed the filters of the merge document autocompleter

# Fixed the merge document autocompleter
UPDATE _fields_meta
SET
    `source`='autocomplete := documents\r\nautocomplete_fill_options := $merge_document_id => <id>\r\nautocomplete_fill_options := $merge_document => <full_num>\r\nautocomplete_suggestions := <full_num>\r\nautocomplete_buttons := clear\r\nautocomplete_clear := 1\r\nautocomplete_view_mode := link\r\nautocomplete_filter := <type> => 121\r\nautocomplete_filter := <substatus> => NOT IN (220)\r\nautocomplete_filter := <id> => NOT IN ($id)\r\n',
    `type`='autocompleter',
    `searchable`='autocompleter',
    `hidden`=1
WHERE id=7472;
UPDATE _fields_meta
SET `hidden`=1
WHERE id=7471;

#########################################################################################
# 2020-12-11 - Added additional settings for bgs_work_outside_contracted report

# Added additional settings for bgs_work_outside_contracted report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nworking_day_start :=', '\r\ndoc_type_ticket := 121\r\ntask_fast_reports := 13\r\n\r\nworking_day_start :=') WHERE `type`='bgs_work_outside_contracted' AND `settings` NOT LIKE '%doc_type_ticket%';

#########################################################################################
# 2021-01-22 - Added e-mail for notifying for newly added incoming e-mail from processRepliedEmails automation

# Added e-mail for notifying for newly added incoming e-mail from processRepliedEmails automation
INSERT INTO `emails` (`model`, `model_type`, `name`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
('Document', 121, '', 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
SET @new_email_template := LAST_INSERT_ID();
INSERT INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
(@new_email_template, 'Нов входящ e-mail по тикет', '<table style=\"width:100%\">\r\n	<tbody>\r\n		<tr>\r\n			<td>Здравейте,&nbsp;[recipient_name],</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>Получен е нов имейл по&nbsp;<a href=\"[document_view_url]\">[document_type] [document_num]</a></td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL! Той е генериран и изпратен от автоматичната система за известяване на nZoom.</td>\r\n		</tr>\r\n		<tr>\r\n			<td>При възникнали проблеми можете да изпратите e-mail на екипа за поддръжка на nZoom.</td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n', '', 'bg', NOW());
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nnotify_income_email := ', @new_email_template) WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%notify_income_email%';

#########################################################################################
# 2021-03-26 - Added setting for customer domain var in processRepliedEmails automation

# Added setting for customer domain var in processRepliedEmails automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\ncust_to_doc_vars :=', '\r\ncustomer_domain :=\r\n\r\ncust_to_doc_vars :=') WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%customer_domain%';

#########################################################################################
# 2021-04-23 - Added additional setting status of the active contract in bgs_work_outside_contracted report

# Added additional setting status of the active contract in bgs_work_outside_contracted report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\customer_type :=', '\r\ncontract_include_status := 1\r\ncustomer_type :=') WHERE `type`='bgs_work_outside_contracted' AND `settings` NOT LIKE '%contract_include_status%';

######################################################################################
# 2021-08-19 - Added the report 'payments_period_report' to BG Service installation (BGS)

# Added the report 'payments_period_report' to BG Service installation (BGS)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (103, 'payments_period_report', 'reports_additional_types :=', 60, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (103, 'Отдалечена работа', NULL, NULL, 'bg'),
  (103, 'Remote Work', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 103, 0, 1),
  ('reports', 'export', 103, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=103;

########################################################################
# 2021-09-02 - Added power roles settings for 'hr_employees_free_days_left' report

# Added power roles settings for 'hr_employees_free_days_left' report
UPDATE reports SET settings = REPLACE(`settings`, 'powerroles :=\r\n', 'powerroles := 3,4,6,27,30\r\n')
WHERE type = 'hr_employees_free_days_left' AND settings LIKE '%powerroles :=\r\n%';

########################################################################
# 2021-11-02 - Added REST user agent for hrZoom
# Add rest settings for customers and leave/days off

# Added REST user agent for hrZoom
UPDATE `settings` SET `value` = 'ticky, hrzoom'
WHERE `section` = 'rest' AND `name` = 'allowed_rest_user_agents';

# Add rest settings for customers and leave/days off
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_documents_6', 'all'),
    ('rest', 'filter_vars_customers_1', 'all')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2022-02-09 - Added the new universal report 'overdue_obligations' to BG Service installation (BGS)

# Added the new universal report 'overdue_obligations' to BG Service installation (BGS)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (438, 'overdue_obligations', 'show_responsibles_filter :=\r\ncustomer_type_client :=\r\n\r\ntype_record_contracts :=\r\ntype_record_incomes :=\r\n\r\nnomenclatures_types :=', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (438, 'Просрочени задължения по стоки/услуги', '', NULL, 'bg'),
  (438, 'Overdue obligations by services/articles', '', NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 438, 0, 1),
  ('reports', 'export', 438, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=438;

########################################################################
# 2022-03-18 - Added rest settings for document id 121 (ticket)
# Add rest settings for customers and leave/days off
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_documents_121', 'all')
ON DUPLICATE KEY UPDATE
                       `value` = VALUES(`value`);

#########################################################################################
# 2022-06-10 - Added settings to dashlet plugin for second client type (11)

# Added settings to dashlet plugin for second client type (11)
UPDATE `dashlets_plugins`
SET `settings` = REPLACE(`settings`, 'cust_type_client := 8\r\n', 'cust_type_client := 8,11\r\n')
WHERE `type` = 'bgs_tickets' AND `settings` NOT LIKE '%cust_type_client := 8\n%';

#########################################################################################
# 2022-06-16 - Added new two version of an automation to calculate the deadlines based on working time

# Added new two version of an automation to calculate the deadlines based on working time
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Попълване на стойности по подразбиране за срокове за изпълнение на тикети', 0, NULL, 1, 'documents', NULL, 'action', '121', 'ticket_priority_var := ticket_priority\r\n\r\nworking_day_start := 09:00\r\nworking_day_end := 18:00\r\n\r\ndefault_priority := Normal\r\n\r\nticket_b_deadline := reaction_time\r\nticket_b_validity_term := time_solution\r\n\r\ncalculation_based_on := b_added', 'condition := \'[action]\' == \'edit\'', 'plugin := bgservice\r\nmethod := completeTicketDeadlines', NULL, 1, 0, 1
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 121 AND automation_type = 'action' AND method LIKE '%completeTicketDeadlines%');

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Попълване на стойности по подразбиране за срокове за изпълнение на тикети (вариант 2)', 0, NULL, 1, 'documents', NULL, 'action', '121', '', 'condition := \'[action]\' == \'edit\'', 'method := setBasicVar\r\nvar_name := deadline\r\nvar_value := php(Calendars_Calendar::calculateBasedOnWorkTime($this->registry, \'[b_added]\', (int)\'[a_reaction_time]\', \'09:00\', \'18:00\');)\r\ndelimiter := |', NULL, 1, 0, 0
WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 121 AND automation_type = 'action' AND method LIKE '%completeTicketDeadlines%' AND `name` LIKE '%вариант%');

#########################################################################################
# 2022-06-24 - Added new setting for processRepliedEmails to configure folfer to reed

# Added new setting for processRepliedEmails to configure folfer to reed 'read_folder'. Default is INBOX, so add it with hash infront
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nprocessed_mails_folder :=', '\r\n#read_folder := INBOX\r\n\r\nprocessed_mails_folder :=') WHERE `method` LIKE '%processRepliedEmails%' AND `settings` NOT LIKE '%read_folder :=%';

#########################################################################################
# 2022-07-08 - Fixed the IT Passport type id in bgs_tickets dashlet
#            - Added setting to display trademark

# Fixed the IT Passport type id in bgs_tickets dashlet
UPDATE dashlets_plugins SET settings=REPLACE(settings, 'doc_type_it_passport := 35', 'doc_type_it_passport := 122')
WHERE settings LIKE '%doc_type_it_passport := 35%';
# Added setting to display trademark
UPDATE dashlets_plugins SET settings=CONCAT(settings, '\r\ndisplay_trademark := ')
WHERE settings NOT LIKE '%display_trademark%';

#########################################################################################
# 2022-10-19 - Added new automation for making timesheets from additional data of a protocol

# Added new automation for making timesheets from additional data of a protocol
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Създаване на отчет след приключване на Протокол за работа на място', 0, NULL, 1, 'documents', NULL, 'crontab', '5', 'timesheet_employee_id := committed_id\r\ntimesheet_duration_id := time_transport\r\ntimesheet_activity := 6\r\ntimesheet_description := Транспортно време\r\n\r\nsend_to_email := <EMAIL>', '', 'plugin := bgservice\r\nmethod := createTimesheetsForVisitProtocols', NULL, 10, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'crontab' AND method LIKE '%createTimesheetsForVisitProtocols%');
SET @new_automation := (SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'crontab' AND method LIKE '%createTimesheetsForVisitProtocols%');
UPDATE `automations` SET `conditions`=CONCAT('where := d.id IN(SELECT id FROM documents as doc LEFT JOIN automations_history as ah ON (ah.model_id=doc.id AND ah.parent_id=', @new_automation, ') WHERE doc.type=5 AND doc.active=1 AND doc.deleted_by=0 AND doc.status=\'closed\' AND ah.model_id IS NULL ORDER BY doc.id DESC)') WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'crontab' AND method LIKE '%createTimesheetsForVisitProtocols%';

#########################################################################################
# 2022-10-25 - Updated automation settings for createTimesheetsForVisitProtocols and changed conditions for execution

# Updated automation settings for createTimesheetsForVisitProtocols and changed conditions for execution
SET @new_automation := (SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'crontab' AND method LIKE '%createTimesheetsForVisitProtocols%');
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nsend_to_notifications := errors, warnings'), `conditions`=CONCAT('where := d.id IN(SELECT id FROM documents as doc INNER JOIN documents_cstm d_cstm ON (d_cstm.model_id=doc.id AND d_cstm.var_id=282 AND d_cstm.VALUE!=\"\" AND d_cstm.VALUE!=\"0\") LEFT JOIN automations_history as ah ON (ah.model_id=doc.id AND ah.parent_id=', @new_automation, ') WHERE doc.type=5 AND doc.active=1 AND doc.deleted_by=0 AND doc.status=\'closed\' AND ah.model_id IS NULL ORDER BY doc.id DESC)') WHERE module = 'documents' AND start_model_type = 5 AND automation_type = 'crontab' AND method LIKE '%createTimesheetsForVisitProtocols%' AND `settings` NOT LIKE '%send_to_notifications%';

#########################################################################################
# 2022-11-10 - Added new automation to complete the free days left before the current leave days request

# Added new automation to complete the free days left before the current leave days request
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Вписване на оставащи дни отпуск', 0, NULL, 1, 'documents', NULL, 'action', '6', 'days_off_year := plr_leave_year\r\ndays_left := plr_leave_days_off', 'condition := \'[request_is_post]\' == \'1\' && \'[action]\' == \'add\'', 'plugin := bgservice\r\nmethod := completeFreeDaysLeft', NULL, 0, 1, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 6 AND automation_type = 'action' AND method LIKE '%completeFreeDaysLeft%');

#########################################################################################
# 2022-11-22 - Added new setting for completeFreeDaysLeft automation to keep the var for days off end date

# Added new setting for completeFreeDaysLeft automation to keep the var for days off end date
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\ndays_off_end_date := plr_leave_finish_date') WHERE `method` LIKE '%completeFreeDaysLeft%' AND `settings` NOT LIKE '%days_off_end_date%';

#########################################################################################
# 2022-11-24 - Added new setting for using tickets in bgservice_requests report

# Added new setting for using tickets in bgservice_requests report
UPDATE reports SET settings = REPLACE(`settings`, '\r\nrequest_client_description :=', '\r\ntype_tickets := 121\r\nrequest_client_description :=')
WHERE type = 'bgservice_requests' AND settings NOT LIKE '%type_tickets%';

######################################################################################
# 2022-11-28 - Added the report 'bgservice_tickets_protocols' for BG Service installation (BGS)

# Added the report 'bgservice_tickets_protocols' for BG Service installation (BGS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (444, 'bgservice_tickets_protocols', 'documents_tickets := 121\r\ndocuments_visit := 5\r\ncustomer_types_to_search := 8\r\nusers_roles_to_search := 2\r\n\r\nvar_ticket_priority := ticket_priority\r\nvar_accepted_time := accepted_for\r\nvar_reaction_time := reaction_time\r\nvar_ticket_for := ticket_for\r\n\r\nvar_visit_employee := committed_id\r\nvar_visit_activity := activity_id\r\nvar_visit_description := ppp_service_description\r\n\r\nworking_time_starts := 09:00\r\nworking_time_ends := 18:00', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (444, 'Тикети/Протоколи', '', NULL, 'bg'),
  (444, 'Tickets/Protocols', '', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 444, 0, 1),
  ('reports', 'export', 444, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=444;


#########################################################################################
# 2023-01-04 - Added new setting for ignoring certain statuses in bgservice_tickets_protocols

# Added new setting for ignoring certain statuses in bgservice_tickets_protocols
UPDATE reports SET settings = CONCAT(`settings`, '\r\n\r\nignore_statuses := 220')
WHERE type = 'bgservice_tickets_protocols' AND settings NOT LIKE '%ignore_statuses %';

#########################################################################################
# 2023-01-05 - Added mysql event to delete forwarded emails from
#              <EMAIL> -> <EMAIL>
#              <EMAIL> -> <EMAIL>
#              <EMAIL> -> <EMAIL>

# Added mysql event to delete forwarded emails
DROP EVENT IF EXISTS `delete_forwarded_received_emails`;
delimiter |
CREATE EVENT `delete_forwarded_received_emails`
    ON SCHEDULE EVERY 1 MINUTE STARTS DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:01') ON COMPLETION PRESERVE ENABLE DO
    BEGIN
        DELETE es2.*
        FROM emails_sentbox es1
                 JOIN emails_sentbox es2
                      ON es1.id!=es2.id AND es1.CODE=es2.CODE AND es1.status='received' AND es2.status='received'
                          AND (es1.recipient = '<EMAIL>' AND es2.recipient = '<EMAIL>' OR
                               es1.recipient = '<EMAIL>' AND es2.recipient = '<EMAIL>' OR
                               es1.recipient = '<EMAIL>' AND es2.recipient = '<EMAIL>')
        WHERE es2.recipient IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

    END|
delimiter ;

#########################################################################################
# 2023-01-18 - Added new automation to set ticket in approval status if the customer has no support contract

# Added new automation to set ticket in approval status if the customer has no support contract
  INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Проверка на тикет за договор за поддръжка  и смяна на статус на тикет към "За одобрение"', 0, NULL, 1, 'documents', NULL, 'action', '121', 'tag_support := 6\r\nstatus := opened\r\nsubstatus := 236\r\nunknown_customer := 13191\r\nassign_owner := ', 'condition := \'[prev_b_customer]\' !=  \'[b_customer]\'\r\n\r\n\r\n', 'plugin := bgservice\r\nmethod := setTicketForApproval\r\n', NULL, 18, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 121 AND automation_type = 'action' AND method LIKE '%setTicketForApproval%');

#########################################################################################
# 2023-01-25 - Fixed mysql event to delete forwarded emails from
#              <EMAIL> -> <EMAIL>
#              <EMAIL> -> <EMAIL>
#              <EMAIL> -> <EMAIL>

# Fixed mysql event to delete forwarded emails
DROP EVENT IF EXISTS `delete_forwarded_received_emails`;
delimiter |
CREATE EVENT `delete_forwarded_received_emails`
    ON SCHEDULE EVERY 1 MINUTE STARTS DATE_FORMAT(NOW(), '%Y-%m-%d 00:00:01') ON COMPLETION PRESERVE ENABLE DO
    BEGIN
        DELETE es2.*
        FROM emails_sentbox es1
                 JOIN emails_sentbox es2
                      ON es1.id!=es2.id AND es1.CODE=es2.CODE AND es1.status='received' AND es2.status='received'
                          AND (es1.recipient = '<EMAIL>' AND es2.recipient = '<EMAIL>' OR
                               es1.recipient = '<EMAIL>' AND es2.recipient = '<EMAIL>' OR
                               es1.recipient = '<EMAIL>' AND es2.recipient = '<EMAIL>')
        WHERE es2.recipient IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

    END|
delimiter ;

#########################################################################################
# 2023-01-27 - Updated automation configurations for new processRepliedEmails (mails2nzoom)

# Updated automation configurations for new processRepliedEmails (mails2nzoom)
UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := ok/KV+7SaHKuAs7CCnXc7Q==\r\nIMAP__authenticationMethod := IMAP\r\n\r\n#IMAP__providerConfig__provider := \r\n#IMAP__providerConfig__clientId := \r\n#IMAP__providerConfig__clientSecret := \r\n#IMAP__providerConfig__tenantId := \r\n#IMAP__providerConfig__refreshToken := \r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model :=\r\ncreate_model_name_on_model_match := comment\r\ncreate_model_type_id := 22\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_customer := 13191\r\nticket_priority :=\r\n\r\ndefault_doc_vars := priority_inq => priorityinq_low, type_inq => typeinq_bug, link_problem => #, custom_sender => <EMAIL>, custom_from_name => BGService\r\nreaction_time_var :=\r\nassign_observers :=\r\nhashtag_field_match :=\r\n\r\nnotify_income_email := 1040'
    WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxBloHpwHDL6HtkiDMGCJcfKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjPKrXP+L0hFHt5YvO3d1t3udDSIDfWC4r9fYJ89bYRJzRHKQN3LQ0ywRP6x2cCW1P/vPJW628sxm1YY66t15efzHUeCOORG+3Ivcj6wKY4LHB+LAyvVJuvYTzVaBgwi4YileXWtfHYM5Cq9FSfCs2rvRnsG+qFvW/0ebTy4/IGKykS7E6QCIj8tozadlJaPebwFC6plg+BcPF7cQLbY2PvIY/LEUQxRNYtRJGXVoHfl6KWoKIIzw/JayXUE/lcev1fbnH3SgYD2M0FZ7dUQvshRMTj78gMlO97eVMkWjoULAKV7dFL8L0fmehGxuVztW77b7i0ljpvpR6O+IJ12WZkHZnSGjLhffp2AEo6ombVXcYlbj/Xp2AGw9kkD9NQTfu91BPRxv4S/CExo7othVWgNL88poOBRugtqiMA5sh0ydz67KZLeVCfspbCSZbAUq+Q2NMhLDZwLpJyzalJ534OaEGjzp5rzKO81LvOhRukcSKYjCTZN1l6UX4mFpNKYeV0xbIEKhG+7FwdWBfYtNlbt9i96ArJpxQI8EEnhhBo6w9K0AmOBoBufX8osklCa1uFlpu8peJ4FbUQfzzt4HptkTG7T1gzjugU1QNwH9GZSZVzk3MW7jGzExxhrS48jz/bK8if/dD9Q+cpr4/1fn/smZCw1NCd0VPKynqaq+5zvVjJ5+bQj4uLSEO1WO4SfccLBhRFuQmuaUIwP0/hEPLwtNSENlK1C1GvBU5XZTNc6HsED09hX8n80a5Js4P97uhzXDkHLlAtYW52bbD2nMPzCC8kmSXS6LA8ftIa/Hr8rR2GBkOSuLqoxZNXjW8YJdkCfkuGzYMInasQ4/PPPnlOS/mAmGzUOaMzFp3coUibFb5qhicinqyQYgKimgYi2//6Zruh+lmRb7AuvH/dP2gzFu0Rqvb1o/g0p6rwUdDCLgGVJjAxHhmHimJ84yw/MlRNFvWpbst1mcZ7WFcAFdb8ZhjVOg/7T00\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
    WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxBloHpwHDL6HtkiDMGCJcfKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjPKrXP+L0hFHt5YvO3d1t3udDSIDfWC4r9fYJ89bYRJzRHKQN3LQ0ywRP6x2cCW1P/vPJW628sxm1YY66t15efzHUeCOORG+3Ivcj6wKY4LHB+LAyvVJuvYTzVaBgwi4YileXWtfHYM5Cq9FSfCs2rvRnsG+qFvW/0ebTy4/IGKykS7E6QCIj8tozadlJaPebwFC6plg+BcPF7cQLbY2PvIY/LEUQxRNYtRJGXVoHfl6KWoKIIzw/JayXUE/lcev1fbnH3SgYD2M0FZ7dUQvshRMTj78gMlO97eVMkWjoULAKV7dFL8L0fmehGxuVztW77b7i0ljpvpR6O+IJ12WZkHZnSGjLhffp2AEo6ombVXcYlbj/Xp2AGw9kkD9NQTfu91BPRxv4S/CExo7othVWgNL88poOBRugtqiMA5sh0ydz67KZLeVCfspbCSZbAUq+Q2NMhLDZwLpJyzalJ534OaEGjzp5rzKO81LvOhRukcSKYjCTZN1l6UX4mFpNKYeV0xbIEKhG+7FwdWBfYtNlbt9i96ArJpxQI8EEnhhBo6w9K0AmOBoBufX8osklCa1uFlpu8peJ4FbUQfzzt4HptkTG7T1gzjugU1QNwH9GZSZVzk3MW7jGzExxhrS48jz/bK8if/dD9Q+cpr4/1fn/smZCw1NCd0VPKynqaq+5zvVjJ5+bQj4uLSEO1WO4SfccLBhRFuQmuaUIwP0/hEPLwtNSENlK1C1GvBU5XZTNc6HsED09hX8n80a5Js4P97uhzXDkHLlAtYW52bbD2nMPzCC8kmSXS6LA8ftIa/Hr8rR2GBkOSuLqoxZNXjW8YJdkCfkuGzYMInasQ4/PPPnlOS/mAmGzUOaMzFp3coUibFb5qhicinqyQYgKimgYi2//6Zruh+lmRb7AuvH/dP2gzFu0Rqvb1o/g0p6rwUdDCLgGVJjAxHhmHimJ84yw/MlRNFvWpbst1mcZ7WFcAFdb8ZhjVOg/7T00\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk Email\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
    WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := Junk%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := LMr57tx20w9zGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYx6hwwFP4rBQibFMvh6i9LrKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjsKPRSqSNnCOqT1Yiu7djjl/9pJotLRJH/y5cNga5SnPg4aVWUFWKIYqCAFabVTx0J3yghqudIOWYKO/ks5TXaLETs3C02J6+vDTaRz0hGKQvwC/+u2B2llXsigVP+HgxVfxP/DS76iWBEVXSjRj8cI/Kd87mBgdIELW8ibC2wNlD+on8LpdLs6tsOtka3zIueGDdxGHBG30ZAwZvrBoUWLZwuALpP5k4zVtc4pRNUp1Dwai0GIARBI5C1hJWPc6rsBsdhYESUcqfSDAasMKjhDYJrGxMegnLnebVW7gl7vPBcjmyz7Py1C4njDQsrD8h0QGKjIH/h44N9ejsq6z9YmoMpmcNWbCxbXh3qE1qeEDi4xlj7Gh3Puzaf/2svxDpsprAosN6OLg7SoBOWLweRgw0rQVvi/OW4sny9OnOxGeuEbDPGAdXsnbx5Hx2tOn9pJKDcpMZyGPhW2N27Y3IqrU7u2LPyHPQhcd0pLFTOgZeb5H6qbDk40izz/tm+XHa2tYvjhTZH6xTXJvVU7cgzeVUb7a4pi6CU3IKBQHUoSTGwc6YExCY/qMWbAaIzUePWVDZmdgbCRUVajDBrwwVMA4nr2FatsGSmHBUYeeOfQ4kHOnPkaajsSk3EehW4UtDn9JETbMe+7/JvE/ec9z61p2AjJEqtOlo0CKf8j1Hp7B9gF8YVgFgPmMoRDz1glb1Wd1FousZ+AhdLmqrl28LGK6QyPTZhMcUtxwzIRo12AUhK/g5RdQdq5pdugqNaTp+fy1Y1UrBLZ/5vcl2HWYABTzmxnaudCUUGYFrvcLfgY7RLaa6I/USMnKYEw9tH7lw7WLpLJjen87aI7pAYLUoBOeZLpgsZJ7NUdQgUITQpiCKoZNxboRnI9nP4UhMbrVBXiYuVYu+zg+7LpAZeNkIki+DZvvS99cAIoRFaN9Kx+rodw4w2vw9fCUZF21M54ozupieFczCveU/sBI3hA7bbA==\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
    WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := LMr57tx20w9zGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYx6hwwFP4rBQibFMvh6i9LrKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjsKPRSqSNnCOqT1Yiu7djjl/9pJotLRJH/y5cNga5SnPg4aVWUFWKIYqCAFabVTx0J3yghqudIOWYKO/ks5TXaLETs3C02J6+vDTaRz0hGKQvwC/+u2B2llXsigVP+HgxVfxP/DS76iWBEVXSjRj8cI/Kd87mBgdIELW8ibC2wNlD+on8LpdLs6tsOtka3zIueGDdxGHBG30ZAwZvrBoUWLZwuALpP5k4zVtc4pRNUp1Dwai0GIARBI5C1hJWPc6rsBsdhYESUcqfSDAasMKjhDYJrGxMegnLnebVW7gl7vPBcjmyz7Py1C4njDQsrD8h0QGKjIH/h44N9ejsq6z9YmoMpmcNWbCxbXh3qE1qeEDi4xlj7Gh3Puzaf/2svxDpsprAosN6OLg7SoBOWLweRgw0rQVvi/OW4sny9OnOxGeuEbDPGAdXsnbx5Hx2tOn9pJKDcpMZyGPhW2N27Y3IqrU7u2LPyHPQhcd0pLFTOgZeb5H6qbDk40izz/tm+XHa2tYvjhTZH6xTXJvVU7cgzeVUb7a4pi6CU3IKBQHUoSTGwc6YExCY/qMWbAaIzUePWVDZmdgbCRUVajDBrwwVMA4nr2FatsGSmHBUYeeOfQ4kHOnPkaajsSk3EehW4UtDn9JETbMe+7/JvE/ec9z61p2AjJEqtOlo0CKf8j1Hp7B9gF8YVgFgPmMoRDz1glb1Wd1FousZ+AhdLmqrl28LGK6QyPTZhMcUtxwzIRo12AUhK/g5RdQdq5pdugqNaTp+fy1Y1UrBLZ/5vcl2HWYABTzmxnaudCUUGYFrvcLfgY7RLaa6I/USMnKYEw9tH7lw7WLpLJjen87aI7pAYLUoBOeZLpgsZJ7NUdQgUITQpiCKoZNxboRnI9nP4UhMbrVBXiYuVYu+zg+7LpAZeNkIki+DZvvS99cAIoRFaN9Kx+rodw4w2vw9fCUZF21M54ozupieFczCveU/sBI3hA7bbA==\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk Email\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
    WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := Junk%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxaynabU3KeeN21GJ+JJmd1KiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjXULMCKra/FhoauVRTvh2D0nCNMD6wakj5ktiW2fg/aij2nGNV66fqhurnjuqNTWxcD/o2DcRSpL3l2gEIoZXV3lj6r4/21LMvS9YeoEXXaC1s/zikfKBqj+eLEPgek4GAdRIKyNCmg9moIGtxaGt/3Qnnfa8Y1cTJvoENneSHhRtJK0cC68EzYa5Rx1v945QmJbB9KROjzeHReYQFfZVVpaAfUk5sfaFbqSjU/+Hbd/FL+FKKLDbmg/ascgHk41otQsGK6/GkA4o4Ff9RhFRrrtTA0ww5HcMt4v8a9yFd5rzPEDW6eNLine5C/iItOSkGL7zdoGtJ4/MaOt+fDGRhUlOmlNu+ySrpaWsobYmJtLpl3IJ5HpdlrMlCG0JvRxg4zOaFF5Y3kMMbOpPFZ9cIUv8rf2pOcZXnF3XE+FOPBled3tvf8e0OL2Vz3aqHw0oZmI8lNVTXYPaNmFkZoTRMR3aiOki8S7ZU2ddbabIX95+IQYaxu9mrJNg7XZn/Gorkk+nBTWL2+7t6MBvI1T//FwHhqkUj2PyOLz7Ch2cf+xrDLajt4nlwcLcYiUuockhuXXQPugU9Z76nHruI7vTh5MfssNSMNgd/sthAb86Knpw0huGihK2beuB3S3ArYMykcAZ2zi9M5NR+s/FoN/ZeHO5zcpFY+SiD3KW6fV7pDfwbH1R8LsiJg07wWgw+QW8pvdm6VKTta9A9FHFJmoKEuvUXbu4cqtNAWHjn5zcrQBdpq/johNJGiEJ9rIcEhxj0n7NH3syN3jcb/HLLlDrsPOKfJIfUT5JgJOQQ3pVsULm4V7mFWpf14CW4u5F/EmBuEXjQqQyOu6IwQtzGw31cscv+Q84H49EhFfXnGoteXnPF31NpP+spLIf6NlA9hF5lZEZzIP49O7tYYFCiBRQogpy1YMQf5V5zN6t3A9T1ky0sWqoYaxmv+nBFwEhxuJcflPmV4nfjO/ZRm3/ub562T49NvvmhThp\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder: INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
    WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxaynabU3KeeN21GJ+JJmd1KiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjXULMCKra/FhoauVRTvh2D0nCNMD6wakj5ktiW2fg/aij2nGNV66fqhurnjuqNTWxcD/o2DcRSpL3l2gEIoZXV3lj6r4/21LMvS9YeoEXXaC1s/zikfKBqj+eLEPgek4GAdRIKyNCmg9moIGtxaGt/3Qnnfa8Y1cTJvoENneSHhRtJK0cC68EzYa5Rx1v945QmJbB9KROjzeHReYQFfZVVpaAfUk5sfaFbqSjU/+Hbd/FL+FKKLDbmg/ascgHk41otQsGK6/GkA4o4Ff9RhFRrrtTA0ww5HcMt4v8a9yFd5rzPEDW6eNLine5C/iItOSkGL7zdoGtJ4/MaOt+fDGRhUlOmlNu+ySrpaWsobYmJtLpl3IJ5HpdlrMlCG0JvRxg4zOaFF5Y3kMMbOpPFZ9cIUv8rf2pOcZXnF3XE+FOPBled3tvf8e0OL2Vz3aqHw0oZmI8lNVTXYPaNmFkZoTRMR3aiOki8S7ZU2ddbabIX95+IQYaxu9mrJNg7XZn/Gorkk+nBTWL2+7t6MBvI1T//FwHhqkUj2PyOLz7Ch2cf+xrDLajt4nlwcLcYiUuockhuXXQPugU9Z76nHruI7vTh5MfssNSMNgd/sthAb86Knpw0huGihK2beuB3S3ArYMykcAZ2zi9M5NR+s/FoN/ZeHO5zcpFY+SiD3KW6fV7pDfwbH1R8LsiJg07wWgw+QW8pvdm6VKTta9A9FHFJmoKEuvUXbu4cqtNAWHjn5zcrQBdpq/johNJGiEJ9rIcEhxj0n7NH3syN3jcb/HLLlDrsPOKfJIfUT5JgJOQQ3pVsULm4V7mFWpf14CW4u5F/EmBuEXjQqQyOu6IwQtzGw31cscv+Q84H49EhFfXnGoteXnPF31NpP+spLIf6NlA9hF5lZEZzIP49O7tYYFCiBRQogpy1YMQf5V5zN6t3A9T1ky0sWqoYaxmv+nBFwEhxuJcflPmV4nfjO/ZRm3/ub562T49NvvmhThp\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk Email\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
    WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := Junk%';

#########################################################################################
# 2023-03-14 - Updated automation configurations for new processRepliedEmails (mails2nzoom)

# Updated automation configurations for new processRepliedEmails (mails2nzoom) To continue to work with zimbra
UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := ok/KV+7SaHKuAs7CCnXc7Q==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model :=\r\ncreate_model_name_on_model_match := comment\r\ncreate_model_type_id := 22\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_customer := 13191\r\nticket_priority :=\r\n\r\ndefault_doc_vars := priority_inq => priorityinq_low, type_inq => typeinq_bug, link_problem => #, custom_sender => <EMAIL>, custom_from_name => BGService\r\nreaction_time_var :=\r\nassign_observers :=\r\nhashtag_field_match :=\r\n\r\nnotify_income_email := 1040'
WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := Lm4yP5Y3SyLjruemeJt7Ug==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := Lm4yP5Y3SyLjruemeJt7Ug==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := Junk%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := IiRmPUNXFizFWcs4bHX6Aw==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := IiRmPUNXFizFWcs4bHX6Aw==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := Junk%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := xQPH1dW/OlOF2DXQLLLPsg==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := xQPH1dW/OlOF2DXQLLLPsg==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE method LIKE '%processRepliedEmails%' AND settings LIKE '%email := <EMAIL>%' AND settings LIKE '%read_folder := Junk%';

# Add automation configurations for new processRepliedEmails (mails2nzoom) Ready to work with office365 (with active=0)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Създаване на тикети от имейл Email2nZoom (Office365) - <EMAIL>',                0, NULL, 1, 'documents', NULL, 'crontab', '121', 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := LMr57tx20w9zGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYx6hwwFP4rBQibFMvh6i9LrKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjsKPRSqSNnCOqT1Yiu7djjl/9pJotLRJH/y5cNga5SnPg4aVWUFWKIYqCAFabVTx0J3yghqudIOWYKO/ks5TXaLETs3C02J6+vDTaRz0hGKQvwC/+u2B2llXsigVP+HgxVfxP/DS76iWBEVXSjRj8cI/Kd87mBgdIELW8ibC2wNlD+on8LpdLs6tsOtka3zIueGDdxGHBG30ZAwZvrBoUWLZwuALpP5k4zVtc4pRNUp1Dwai0GIARBI5C1hJWPc6rsBsdhYESUcqfSDAasMKjhDYJrGxMegnLnebVW7gl7vPBcjmyz7Py1C4njDQsrD8h0QGKjIH/h44N9ejsq6z9YmoMpmcNWbCxbXh3qE1qeEDi4xlj7Gh3Puzaf/2svxDpsprAosN6OLg7SoBOWLweRgw0rQVvi/OW4sny9OnOxGeuEbDPGAdXsnbx5Hx2tOn9pJKDcpMZyGPhW2N27Y3IqrU7u2LPyHPQhcd0pLFTOgZeb5H6qbDk40izz/tm+XHa2tYvjhTZH6xTXJvVU7cgzeVUb7a4pi6CU3IKBQHUoSTGwc6YExCY/qMWbAaIzUePWVDZmdgbCRUVajDBrwwVMA4nr2FatsGSmHBUYeeOfQ4kHOnPkaajsSk3EehW4UtDn9JETbMe+7/JvE/ec9z61p2AjJEqtOlo0CKf8j1Hp7B9gF8YVgFgPmMoRDz1glb1Wd1FousZ+AhdLmqrl28LGK6QyPTZhMcUtxwzIRo12AUhK/g5RdQdq5pdugqNaTp+fy1Y1UrBLZ/5vcl2HWYABTzmxnaudCUUGYFrvcLfgY7RLaa6I/USMnKYEw9tH7lw7WLpLJjen87aI7pAYLUoBOeZLpgsZJ7NUdQgUITQpiCKoZNxboRnI9nP4UhMbrVBXiYuVYu+zg+7LpAZeNkIki+DZvvS99cAIoRFaN9Kx+rodw4w2vw9fCUZF21M54ozupieFczCveU/sBI3hA7bbA==\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040',                                  'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 0, 0, 0),
('Създаване на тикети от имейл Email2nZoom (Office365) - <EMAIL> - JUNK',         0, NULL, 1, 'documents', NULL, 'crontab', '121', 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := LMr57tx20w9zGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYx6hwwFP4rBQibFMvh6i9LrKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjsKPRSqSNnCOqT1Yiu7djjl/9pJotLRJH/y5cNga5SnPg4aVWUFWKIYqCAFabVTx0J3yghqudIOWYKO/ks5TXaLETs3C02J6+vDTaRz0hGKQvwC/+u2B2llXsigVP+HgxVfxP/DS76iWBEVXSjRj8cI/Kd87mBgdIELW8ibC2wNlD+on8LpdLs6tsOtka3zIueGDdxGHBG30ZAwZvrBoUWLZwuALpP5k4zVtc4pRNUp1Dwai0GIARBI5C1hJWPc6rsBsdhYESUcqfSDAasMKjhDYJrGxMegnLnebVW7gl7vPBcjmyz7Py1C4njDQsrD8h0QGKjIH/h44N9ejsq6z9YmoMpmcNWbCxbXh3qE1qeEDi4xlj7Gh3Puzaf/2svxDpsprAosN6OLg7SoBOWLweRgw0rQVvi/OW4sny9OnOxGeuEbDPGAdXsnbx5Hx2tOn9pJKDcpMZyGPhW2N27Y3IqrU7u2LPyHPQhcd0pLFTOgZeb5H6qbDk40izz/tm+XHa2tYvjhTZH6xTXJvVU7cgzeVUb7a4pi6CU3IKBQHUoSTGwc6YExCY/qMWbAaIzUePWVDZmdgbCRUVajDBrwwVMA4nr2FatsGSmHBUYeeOfQ4kHOnPkaajsSk3EehW4UtDn9JETbMe+7/JvE/ec9z61p2AjJEqtOlo0CKf8j1Hp7B9gF8YVgFgPmMoRDz1glb1Wd1FousZ+AhdLmqrl28LGK6QyPTZhMcUtxwzIRo12AUhK/g5RdQdq5pdugqNaTp+fy1Y1UrBLZ/5vcl2HWYABTzmxnaudCUUGYFrvcLfgY7RLaa6I/USMnKYEw9tH7lw7WLpLJjen87aI7pAYLUoBOeZLpgsZJ7NUdQgUITQpiCKoZNxboRnI9nP4UhMbrVBXiYuVYu+zg+7LpAZeNkIki+DZvvS99cAIoRFaN9Kx+rodw4w2vw9fCUZF21M54ozupieFczCveU/sBI3hA7bbA==\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk Email\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040',                        'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 0, 0, 0),
('Създаване на тикети от имейл Email2nZoom (Office365) - <EMAIL>',           0, NULL, 1, 'documents', NULL, 'crontab', '121', 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxBloHpwHDL6HtkiDMGCJcfKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjPKrXP+L0hFHt5YvO3d1t3udDSIDfWC4r9fYJ89bYRJzRHKQN3LQ0ywRP6x2cCW1P/vPJW628sxm1YY66t15efzHUeCOORG+3Ivcj6wKY4LHB+LAyvVJuvYTzVaBgwi4YileXWtfHYM5Cq9FSfCs2rvRnsG+qFvW/0ebTy4/IGKykS7E6QCIj8tozadlJaPebwFC6plg+BcPF7cQLbY2PvIY/LEUQxRNYtRJGXVoHfl6KWoKIIzw/JayXUE/lcev1fbnH3SgYD2M0FZ7dUQvshRMTj78gMlO97eVMkWjoULAKV7dFL8L0fmehGxuVztW77b7i0ljpvpR6O+IJ12WZkHZnSGjLhffp2AEo6ombVXcYlbj/Xp2AGw9kkD9NQTfu91BPRxv4S/CExo7othVWgNL88poOBRugtqiMA5sh0ydz67KZLeVCfspbCSZbAUq+Q2NMhLDZwLpJyzalJ534OaEGjzp5rzKO81LvOhRukcSKYjCTZN1l6UX4mFpNKYeV0xbIEKhG+7FwdWBfYtNlbt9i96ArJpxQI8EEnhhBo6w9K0AmOBoBufX8osklCa1uFlpu8peJ4FbUQfzzt4HptkTG7T1gzjugU1QNwH9GZSZVzk3MW7jGzExxhrS48jz/bK8if/dD9Q+cpr4/1fn/smZCw1NCd0VPKynqaq+5zvVjJ5+bQj4uLSEO1WO4SfccLBhRFuQmuaUIwP0/hEPLwtNSENlK1C1GvBU5XZTNc6HsED09hX8n80a5Js4P97uhzXDkHLlAtYW52bbD2nMPzCC8kmSXS6LA8ftIa/Hr8rR2GBkOSuLqoxZNXjW8YJdkCfkuGzYMInasQ4/PPPnlOS/mAmGzUOaMzFp3coUibFb5qhicinqyQYgKimgYi2//6Zruh+lmRb7AuvH/dP2gzFu0Rqvb1o/g0p6rwUdDCLgGVJjAxHhmHimJ84yw/MlRNFvWpbst1mcZ7WFcAFdb8ZhjVOg/7T00\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040',           'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 0, 0, 0),
('Създаване на тикети от имейл Email2nZoom (Office365) - <EMAIL> - JUNK',    0, NULL, 1, 'documents', NULL, 'crontab', '121', 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxBloHpwHDL6HtkiDMGCJcfKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjPKrXP+L0hFHt5YvO3d1t3udDSIDfWC4r9fYJ89bYRJzRHKQN3LQ0ywRP6x2cCW1P/vPJW628sxm1YY66t15efzHUeCOORG+3Ivcj6wKY4LHB+LAyvVJuvYTzVaBgwi4YileXWtfHYM5Cq9FSfCs2rvRnsG+qFvW/0ebTy4/IGKykS7E6QCIj8tozadlJaPebwFC6plg+BcPF7cQLbY2PvIY/LEUQxRNYtRJGXVoHfl6KWoKIIzw/JayXUE/lcev1fbnH3SgYD2M0FZ7dUQvshRMTj78gMlO97eVMkWjoULAKV7dFL8L0fmehGxuVztW77b7i0ljpvpR6O+IJ12WZkHZnSGjLhffp2AEo6ombVXcYlbj/Xp2AGw9kkD9NQTfu91BPRxv4S/CExo7othVWgNL88poOBRugtqiMA5sh0ydz67KZLeVCfspbCSZbAUq+Q2NMhLDZwLpJyzalJ534OaEGjzp5rzKO81LvOhRukcSKYjCTZN1l6UX4mFpNKYeV0xbIEKhG+7FwdWBfYtNlbt9i96ArJpxQI8EEnhhBo6w9K0AmOBoBufX8osklCa1uFlpu8peJ4FbUQfzzt4HptkTG7T1gzjugU1QNwH9GZSZVzk3MW7jGzExxhrS48jz/bK8if/dD9Q+cpr4/1fn/smZCw1NCd0VPKynqaq+5zvVjJ5+bQj4uLSEO1WO4SfccLBhRFuQmuaUIwP0/hEPLwtNSENlK1C1GvBU5XZTNc6HsED09hX8n80a5Js4P97uhzXDkHLlAtYW52bbD2nMPzCC8kmSXS6LA8ftIa/Hr8rR2GBkOSuLqoxZNXjW8YJdkCfkuGzYMInasQ4/PPPnlOS/mAmGzUOaMzFp3coUibFb5qhicinqyQYgKimgYi2//6Zruh+lmRb7AuvH/dP2gzFu0Rqvb1o/g0p6rwUdDCLgGVJjAxHhmHimJ84yw/MlRNFvWpbst1mcZ7WFcAFdb8ZhjVOg/7T00\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk Email\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040', 'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 0, 0, 0),
('Създаване на тикети от имейл Email2nZoom (Office365) - <EMAIL>',             0, NULL, 1, 'documents', NULL, 'crontab', '121', 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxaynabU3KeeN21GJ+JJmd1KiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjXULMCKra/FhoauVRTvh2D0nCNMD6wakj5ktiW2fg/aij2nGNV66fqhurnjuqNTWxcD/o2DcRSpL3l2gEIoZXV3lj6r4/21LMvS9YeoEXXaC1s/zikfKBqj+eLEPgek4GAdRIKyNCmg9moIGtxaGt/3Qnnfa8Y1cTJvoENneSHhRtJK0cC68EzYa5Rx1v945QmJbB9KROjzeHReYQFfZVVpaAfUk5sfaFbqSjU/+Hbd/FL+FKKLDbmg/ascgHk41otQsGK6/GkA4o4Ff9RhFRrrtTA0ww5HcMt4v8a9yFd5rzPEDW6eNLine5C/iItOSkGL7zdoGtJ4/MaOt+fDGRhUlOmlNu+ySrpaWsobYmJtLpl3IJ5HpdlrMlCG0JvRxg4zOaFF5Y3kMMbOpPFZ9cIUv8rf2pOcZXnF3XE+FOPBled3tvf8e0OL2Vz3aqHw0oZmI8lNVTXYPaNmFkZoTRMR3aiOki8S7ZU2ddbabIX95+IQYaxu9mrJNg7XZn/Gorkk+nBTWL2+7t6MBvI1T//FwHhqkUj2PyOLz7Ch2cf+xrDLajt4nlwcLcYiUuockhuXXQPugU9Z76nHruI7vTh5MfssNSMNgd/sthAb86Knpw0huGihK2beuB3S3ArYMykcAZ2zi9M5NR+s/FoN/ZeHO5zcpFY+SiD3KW6fV7pDfwbH1R8LsiJg07wWgw+QW8pvdm6VKTta9A9FHFJmoKEuvUXbu4cqtNAWHjn5zcrQBdpq/johNJGiEJ9rIcEhxj0n7NH3syN3jcb/HLLlDrsPOKfJIfUT5JgJOQQ3pVsULm4V7mFWpf14CW4u5F/EmBuEXjQqQyOu6IwQtzGw31cscv+Q84H49EhFfXnGoteXnPF31NpP+spLIf6NlA9hF5lZEZzIP49O7tYYFCiBRQogpy1YMQf5V5zN6t3A9T1ky0sWqoYaxmv+nBFwEhxuJcflPmV4nfjO/ZRm3/ub562T49NvvmhThp\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040',               'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 0, 0, 0),
('Създаване на тикети от имейл Email2nZoom (Office365) - <EMAIL> - JUNK',      0, NULL, 1, 'documents', NULL, 'crontab', '121', 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := 916DNPo1akFzGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYxaynabU3KeeN21GJ+JJmd1KiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjXULMCKra/FhoauVRTvh2D0nCNMD6wakj5ktiW2fg/aij2nGNV66fqhurnjuqNTWxcD/o2DcRSpL3l2gEIoZXV3lj6r4/21LMvS9YeoEXXaC1s/zikfKBqj+eLEPgek4GAdRIKyNCmg9moIGtxaGt/3Qnnfa8Y1cTJvoENneSHhRtJK0cC68EzYa5Rx1v945QmJbB9KROjzeHReYQFfZVVpaAfUk5sfaFbqSjU/+Hbd/FL+FKKLDbmg/ascgHk41otQsGK6/GkA4o4Ff9RhFRrrtTA0ww5HcMt4v8a9yFd5rzPEDW6eNLine5C/iItOSkGL7zdoGtJ4/MaOt+fDGRhUlOmlNu+ySrpaWsobYmJtLpl3IJ5HpdlrMlCG0JvRxg4zOaFF5Y3kMMbOpPFZ9cIUv8rf2pOcZXnF3XE+FOPBled3tvf8e0OL2Vz3aqHw0oZmI8lNVTXYPaNmFkZoTRMR3aiOki8S7ZU2ddbabIX95+IQYaxu9mrJNg7XZn/Gorkk+nBTWL2+7t6MBvI1T//FwHhqkUj2PyOLz7Ch2cf+xrDLajt4nlwcLcYiUuockhuXXQPugU9Z76nHruI7vTh5MfssNSMNgd/sthAb86Knpw0huGihK2beuB3S3ArYMykcAZ2zi9M5NR+s/FoN/ZeHO5zcpFY+SiD3KW6fV7pDfwbH1R8LsiJg07wWgw+QW8pvdm6VKTta9A9FHFJmoKEuvUXbu4cqtNAWHjn5zcrQBdpq/johNJGiEJ9rIcEhxj0n7NH3syN3jcb/HLLlDrsPOKfJIfUT5JgJOQQ3pVsULm4V7mFWpf14CW4u5F/EmBuEXjQqQyOu6IwQtzGw31cscv+Q84H49EhFfXnGoteXnPF31NpP+spLIf6NlA9hF5lZEZzIP49O7tYYFCiBRQogpy1YMQf5V5zN6t3A9T1ky0sWqoYaxmv+nBFwEhxuJcflPmV4nfjO/ZRm3/ub562T49NvvmhThp\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk Email\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040',     'condition := 1', 'plugin := bgservice\r\nmethod := processRepliedEmails', NULL, 0, 0, 0);


#########################################################################################
# 2023-03-28 - Updated automation configurations for new processRepliedEmails (mails2nzoom) 3-rd try

# Updated automation configurations for new processRepliedEmails (mails2nzoom) To continue to work with zimbra (considering the previous updates that have errors)
UPDATE automations SET `settings` = 'IMAP__debug := false\r\nIMAP__host := zmail.bgservice.net\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := ok/KV+7SaHKuAs7CCnXc7Q==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := comment\r\ncreate_model_type_id := 22\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := \r\ndefault_doc_vars := priority_inq => priorityinq_low, type_inq => typeinq_bug, link_problem => #, custom_sender => <EMAIL>, custom_from_name => BGService\r\nassign_observers := \r\nhashtag_field_match := \r\n\r\nnotify_income_email := 1040'
WHERE `name` NOT LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := zmail.bgservice.net\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := IiRmPUNXFizFWcs4bHX6Aw==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE `name` NOT LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := zmail.bgservice.net\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := IiRmPUNXFizFWcs4bHX6Aw==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE `name` NOT LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := Junk Email%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := zmail.bgservice.net\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := Lm4yP5Y3SyLjruemeJt7Ug==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE `name` NOT LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := INBOX\r\n%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := zmail.bgservice.net\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := Lm4yP5Y3SyLjruemeJt7Ug==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 26, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 274\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE `name` NOT LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := Junk Email%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := zmail.bgservice.net\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := xQPH1dW/OlOF2DXQLLLPsg==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := INBOX\r\noperation__processedMailsFolder := INBOX/processed_emails\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE`name` NOT LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := INBOX%';

UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := zmail.bgservice.net\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := xQPH1dW/OlOF2DXQLLLPsg==\r\nIMAP__authenticationMethod := IMAP\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Junk\r\nprocessed_mails_folder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 13, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 17,33\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE `name` NOT LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := Junk Email%';

# Update new <NAME_EMAIL> - Junc to use folder name in cyrillic
UPDATE automations SET settings = 'IMAP__debug := false\r\nIMAP__host := outlook.office365.com\r\nIMAP__port := 993\r\nIMAP__cert := ssl\r\nIMAP__username := <EMAIL>\r\nIMAP__password := \r\nIMAP__authenticationMethod := oauth2\r\n\r\nIMAP__providerConfig__provider := Azure\r\nIMAP__providerConfig__clientId := 8369ec59-46d7-4ca8-9a70-7dd4926765bf\r\nIMAP__providerConfig__clientSecret := ****************************************\r\nIMAP__providerConfig__tenantId := 73e9be01-6286-4a2e-9bba-c5fdff9123f2\r\nIMAP__providerConfig__refreshToken := LMr57tx20w9zGGHa9S3MZtsvduoec5yWdyaPizSoj1FvTIUrpqfuWfnD0s8IcgYx6hwwFP4rBQibFMvh6i9LrKiB+Npsh/Fe65rv0YZ4xyxi2aWl/R6fJx9c4Kqx5gcjsKPRSqSNnCOqT1Yiu7djjl/9pJotLRJH/y5cNga5SnPg4aVWUFWKIYqCAFabVTx0J3yghqudIOWYKO/ks5TXaLETs3C02J6+vDTaRz0hGKQvwC/+u2B2llXsigVP+HgxVfxP/DS76iWBEVXSjRj8cI/Kd87mBgdIELW8ibC2wNlD+on8LpdLs6tsOtka3zIueGDdxGHBG30ZAwZvrBoUWLZwuALpP5k4zVtc4pRNUp1Dwai0GIARBI5C1hJWPc6rsBsdhYESUcqfSDAasMKjhDYJrGxMegnLnebVW7gl7vPBcjmyz7Py1C4njDQsrD8h0QGKjIH/h44N9ejsq6z9YmoMpmcNWbCxbXh3qE1qeEDi4xlj7Gh3Puzaf/2svxDpsprAosN6OLg7SoBOWLweRgw0rQVvi/OW4sny9OnOxGeuEbDPGAdXsnbx5Hx2tOn9pJKDcpMZyGPhW2N27Y3IqrU7u2LPyHPQhcd0pLFTOgZeb5H6qbDk40izz/tm+XHa2tYvjhTZH6xTXJvVU7cgzeVUb7a4pi6CU3IKBQHUoSTGwc6YExCY/qMWbAaIzUePWVDZmdgbCRUVajDBrwwVMA4nr2FatsGSmHBUYeeOfQ4kHOnPkaajsSk3EehW4UtDn9JETbMe+7/JvE/ec9z61p2AjJEqtOlo0CKf8j1Hp7B9gF8YVgFgPmMoRDz1glb1Wd1FousZ+AhdLmqrl28LGK6QyPTZhMcUtxwzIRo12AUhK/g5RdQdq5pdugqNaTp+fy1Y1UrBLZ/5vcl2HWYABTzmxnaudCUUGYFrvcLfgY7RLaa6I/USMnKYEw9tH7lw7WLpLJjen87aI7pAYLUoBOeZLpgsZJ7NUdQgUITQpiCKoZNxboRnI9nP4UhMbrVBXiYuVYu+zg+7LpAZeNkIki+DZvvS99cAIoRFaN9Kx+rodw4w2vw9fCUZF21M54ozupieFczCveU/sBI3hA7bbA==\r\n\r\n\r\noperation__email := <EMAIL>\r\noperation__readFolder := Нежелана поща\r\noperation__processedMailsFolder := INBOX/processed_emails_junk\r\noperation__chunkSize := 10\r\n#operation__messageMaxAge := 10000 days\r\n\r\nskip_add_new_model := \r\ncreate_model_name_on_model_match := email\r\ncreate_model_type_id := 121\r\n\r\nemployee := 14247\r\nvalidity_term_field := \r\nvalidity_term_calc_period := \r\n\r\ndefault_name :=\r\ndefault_customer := 13191\r\nticket_priority := 3\r\ncustomer_domain := domain\r\n\r\ncust_to_doc_vars := customer_sla => reaction_time\r\ndefault_doc_vars := reaction_time => 240, b_department => 14, custom_sender => <EMAIL>, custom_from_name => BGService, ticket_for => 1\r\nassign_observers := 231\r\nhashtag_field_match := full_num\r\n\r\nnotify_income_email := 1040'
WHERE `name` LIKE '%(Office365)%' AND
        `method` LIKE '%processRepliedEmails%' AND
        `settings` LIKE '%IMAP__username := <EMAIL>%' AND
        `settings` LIKE '%operation__readFolder := Junk Email';


#########################################################################################
# 2023-06-20 - Updated automation configurations with new setting ignore_status_closed_for_minutes

# Add new setting ignore_status_closed_for_minutes in processRepliedEmails automations
UPDATE automations SET settings = REPLACE(`settings`, '\r\ncreate_model_type_id :=', '\r\n# Any closed document will be considered opened for this amount of minutes after changing status, and any new email or comment, will be attached to it. This will prevent ''thank you'' emails from creating new tickets right after closing\r\n# ignore_status_closed_for_minutes := 30\r\ncreate_model_type_id :=')
WHERE   `method` LIKE '%processRepliedEmails%' AND
        `settings` NOT LIKE '%ignore_status_closed_for_minutes :=%';

#########################################################################################
# 2024-01-11 - Added setting for customer invoice client in 'bgs_work_outside_contracted' report

# Added setting for customer invoice client in 'bgs_work_outside_contracted' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ncustomer_tags :=', '\r\ncustomer_type_invoice_client := 15\r\ncustomer_tags :=') WHERE `type`='bgs_work_outside_contracted' AND `settings` NOT LIKE '%customer_type_invoice_client%';

######################################################################################
# 2025-02-19 - Updated settings for REST

# Added groups to user filter vars.
UPDATE `settings`
SET `value` = CONCAT(`value`, ',groups')
WHERE `section` = 'rest' AND `name` = 'filter_vars_users' AND `value` NOT LIKE 'groups';

#########################################################################################
# 2025-06-12 - Added settings for default reply subject

INSERT IGNORE INTO `settings` (`id`, `section`, `name`, `value`)  VALUES
(NULL, 'emails', 'default_reply_prefix_document_121', 'Re #[full_num]:');
