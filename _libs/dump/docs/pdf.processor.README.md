# Nzoom PDF Processor Documentation

## Overview

The **Nzoom PDF Processor** (`\Nzoom\Pdf\Processor`) is an enterprise-grade PDF processing engine that provides a simplified, high-level interface to sophisticated PDF operations. Built on top of the powerful Ghostscript processor, it abstracts complex PDF manipulation into an intuitive, fluent API designed for modern PHP applications.

## Table of Contents

- [Installation & Requirements](#installation--requirements)
- [Quick Start](#quick-start)
- [Core Features](#core-features)
- [API Reference](#api-reference)
- [Usage Examples](#usage-examples)
- [Integration with Ghostscript](#integration-with-ghostscript)
- [Error Handling](#error-handling)
- [Debugging & Monitoring](#debugging--monitoring)
- [Best Practices](#best-practices)
- [Advanced Usage](#advanced-usage)

## Installation & Requirements

### System Requirements

- **PHP**: 8.0 or higher with modern type system support
- **Ghostscript**: 9.50+ installed and accessible via PATH
- **Memory**: Sufficient RAM for processing PDF files
- **Disk Space**: Adequate space for temporary operations
- **Permissions**: Read/write access to input/output directories

### Installation

```bash
# Ensure Ghostscript is installed (see Ghostscript documentation)
# The PDF Processor automatically detects and uses Ghostscript

# Autoloading via Composer
require_once 'vendor/autoload.php';
use Nzoom\Pdf\Processor;
```

### Dependencies

The PDF Processor depends on:
- `\Nzoom\Ghostscript\Processor` - Core Ghostscript engine
- Standard PHP extensions (no additional requirements)

## Quick Start

### Basic PDF Merging

```php
use Nzoom\Pdf\Processor;

// Create processor instance
$processor = new Processor();

// Merge multiple PDFs
$processor->setFiles(['doc1.pdf', 'doc2.pdf', 'doc3.pdf'])
          ->merge('merged_output.pdf');
```

### Image to PDF Conversion

```php
// Convert image to PDF
$processor->convertImageToPdf('image.jpg', 'output.pdf');
```

### PDF Optimization

```php
// Optimize PDF for web delivery
$processor->optimizeForWeb('large.pdf', 'optimized.pdf');

// Optimize for print quality
$processor->optimizeForPrint('document.pdf', 'print_ready.pdf');
```

### PDF Validation

```php
// Validate a single PDF
$isValid = $processor->isValid('document.pdf');

// Get detailed validation information
$validation = $processor->validatePdf('document.pdf');
if (!$validation['valid']) {
    foreach ($validation['details']['errors'] as $error) {
        echo "Error: $error\n";
    }
}
```

## Core Features

### 📄 Document Operations
- **PDF Merging**: Combine multiple PDFs with preserved formatting and quality
- **Page Extraction**: Extract specific page ranges from documents
- **Format Conversion**: Convert between PDF and image formats (PNG, JPEG, TIFF)
- **Document Optimization**: Optimize for web delivery or print quality

### 🔄 Format Conversion
- **Image to PDF**: Convert images to PDF with quality control
- **PDF to Image**: Convert PDF pages to various image formats
- **Multi-format Support**: PNG, JPEG, TIFF with automatic format detection
- **Quality Control**: Configurable resolution and compression settings

### ⚡ Optimization Engine
- **Web Optimization**: Fast loading, small file sizes for online delivery
- **Print Optimization**: High quality, color accuracy for professional printing
- **Intelligent Compression**: Quality preservation with size reduction
- **Font Management**: Automatic font embedding and subsetting

### 🛡️ Validation & Quality Assurance
- **PDF Validation**: Comprehensive integrity checking with detailed reports
- **Corruption Detection**: Identify and report damaged or invalid files
- **Batch Validation**: Process multiple files with summary statistics
- **Error Categorization**: Detailed error and warning classification

### 🔍 Monitoring & Debugging
- **Performance Metrics**: Real-time processing statistics and throughput data
- **Execution History**: Complete audit trail of all operations
- **Memory Tracking**: Resource usage monitoring and optimization analysis
- **Error Diagnostics**: Detailed error reporting with actionable insights

## API Reference

### Constructor

```php
public function __construct(array $ghostscriptOptions = [])
```

Initialize the PDF processor with optional Ghostscript configuration.

**Parameters:**
- `$ghostscriptOptions` (array): Optional configuration for the underlying Ghostscript processor

**Example:**
```php
// Basic initialization
$processor = new Processor();

// With custom Ghostscript options
$processor = new Processor([
    'resolution' => 600,
    'quality' => 0.95,
    'compress_pages' => true
]);
```

### File Management Methods

#### setFiles(array $files): self
Set the complete collection of files for processing.

#### addFile(string $file): self
Add a single file to the processing collection.

#### getFiles(): array
Get the current collection of files queued for processing.

#### clearFiles(): self
Clear the entire file collection.

### Core Processing Methods

#### merge(string $outputFilePath): bool
Merge multiple PDF files into a single consolidated document.

#### convertImageToPdf(string $imageFile, string $outputFile = 'image.pdf'): bool
Convert an image file to PDF format.

#### optimizeForWeb(string $inputFile, string $outputFile): bool
Optimize PDF for web delivery with reduced file size.

#### optimizeForPrint(string $inputFile, string $outputFile): bool
Optimize PDF for high-quality printing.

#### extractPages(string $inputFile, string $outputFile, int $firstPage, ?int $lastPage = null): bool
Extract specific pages from a PDF document.

#### convertToImage(string $inputFile, string $outputFile, string $format = 'png'): bool
Convert PDF to image format (PNG, JPEG, TIFF).

### Validation Methods

#### isValid(string $file): bool
Quick validation check for PDF file integrity.

#### validatePdf(string $file): array
Get detailed validation information for a PDF file.

#### validatePdfFiles(array $files): array
Validate multiple PDF files with batch processing.

### Debug and Monitoring Methods

#### enableDebug(): self
Activate comprehensive debug mode.

#### disableDebug(): self
Deactivate debug mode for production use.

#### isDebugEnabled(): bool
Check current debug mode status.

#### getLastDebugInfo(): array
Get debug information from the last operation.

#### getExecutionHistory(): array
Get complete execution history.

#### getExecutionStats(): array
Get execution statistics and performance metrics.

### Error Handling Methods

#### getErrors(): array
Get all error messages from operations.

#### getLastError(): ?string
Get the most recent error message.

#### hasErrors(): bool
Check if there are any errors.

#### clearErrors(): self
Clear all error messages.

### Advanced Integration

#### getGhostscriptProcessor(): GhostscriptProcessor
Get direct access to the underlying Ghostscript processor for advanced operations.

## Usage Examples

### 1. Basic PDF Operations

#### Simple PDF Merging
```php
use Nzoom\Pdf\Processor;

$processor = new Processor();

// Merge multiple PDFs in order
$processor->setFiles([
    '/path/to/cover.pdf',
    '/path/to/content.pdf',
    '/path/to/appendix.pdf'
]);

$success = $processor->merge('/path/to/merged_document.pdf');

if ($success) {
    echo "PDF merge completed successfully\n";
} else {
    echo "Merge failed: " . $processor->getLastError() . "\n";
}
```

#### Incremental File Building
```php
// Build file collection incrementally
$processor = new Processor();

$processor->addFile('/path/to/title_page.pdf')
          ->addFile('/path/to/table_of_contents.pdf')
          ->addFile('/path/to/chapter1.pdf')
          ->addFile('/path/to/chapter2.pdf')
          ->merge('/path/to/complete_book.pdf');
```

#### File Collection Management
```php
// Inspect current file collection
$files = $processor->getFiles();
echo "Files queued: " . count($files) . "\n";
foreach ($files as $index => $file) {
    echo ($index + 1) . ". " . basename($file) . "\n";
}

// Clear and start fresh
$processor->clearFiles()
          ->addFile('new_document.pdf');
```

### 2. Format Conversion Operations

#### Image to PDF Conversion
```php
// Convert single image to PDF
$processor->convertImageToPdf('photo.jpg', 'photo.pdf');

// Convert with custom output name
$processor->convertImageToPdf('scan.png', 'scanned_document.pdf');

// Batch convert images to PDFs
$images = ['image1.jpg', 'image2.png', 'image3.tiff'];
foreach ($images as $image) {
    $outputName = pathinfo($image, PATHINFO_FILENAME) . '.pdf';
    $processor->convertImageToPdf($image, $outputName);
}
```

#### PDF to Image Conversion
```php
// Convert PDF to PNG (default)
$processor->convertToImage('document.pdf', 'preview.png');

// Convert to JPEG with specific format
$processor->convertToImage('document.pdf', 'preview.jpg', 'jpeg');

// Convert to TIFF for archival
$processor->convertToImage('document.pdf', 'archive.tiff', 'tiff');

// Batch convert multiple PDFs to images
$pdfs = ['doc1.pdf', 'doc2.pdf', 'doc3.pdf'];
foreach ($pdfs as $pdf) {
    $imageName = pathinfo($pdf, PATHINFO_FILENAME) . '.png';
    $processor->convertToImage($pdf, $imageName, 'png');
}
```

### 3. Page Extraction and Manipulation

#### Extract Specific Pages
```php
// Extract pages 1-5 from a document
$processor->extractPages('large_document.pdf', 'first_chapter.pdf', 1, 5);

// Extract single page
$processor->extractPages('document.pdf', 'cover_page.pdf', 1);

// Extract last 10 pages (assuming 50-page document)
$processor->extractPages('document.pdf', 'appendix.pdf', 41, 50);

// Extract middle section
$processor->extractPages('manual.pdf', 'installation_guide.pdf', 15, 25);
```

#### Dynamic Page Extraction
```php
// Extract pages based on conditions
function extractChapter($processor, $sourceFile, $chapterName, $startPage, $endPage) {
    $outputFile = strtolower(str_replace(' ', '_', $chapterName)) . '.pdf';

    $success = $processor->extractPages($sourceFile, $outputFile, $startPage, $endPage);

    if ($success) {
        echo "Extracted '$chapterName' to $outputFile\n";
    } else {
        echo "Failed to extract '$chapterName': " . $processor->getLastError() . "\n";
    }

    return $success;
}

// Extract multiple chapters
extractChapter($processor, 'textbook.pdf', 'Introduction', 1, 10);
extractChapter($processor, 'textbook.pdf', 'Chapter 1', 11, 25);
extractChapter($processor, 'textbook.pdf', 'Chapter 2', 26, 40);
```

### 4. Quality Optimization

#### Web Optimization
```php
// Basic web optimization
$processor->optimizeForWeb('large_document.pdf', 'web_optimized.pdf');

// Optimize multiple files for web
$documents = ['report1.pdf', 'report2.pdf', 'presentation.pdf'];
foreach ($documents as $doc) {
    $optimizedName = 'web_' . $doc;
    $processor->optimizeForWeb($doc, $optimizedName);

    // Compare file sizes
    $originalSize = filesize($doc);
    $optimizedSize = filesize($optimizedName);
    $reduction = (($originalSize - $optimizedSize) / $originalSize) * 100;

    echo "Optimized $doc: " . number_format($reduction, 1) . "% size reduction\n";
}
```

#### Print Optimization
```php
// High-quality print optimization
$processor->optimizeForPrint('document.pdf', 'print_ready.pdf');

// Optimize for commercial printing
$processor->optimizeForPrint('brochure.pdf', 'commercial_print.pdf');

// Batch optimize for print
$printJobs = ['flyer.pdf', 'poster.pdf', 'business_card.pdf'];
foreach ($printJobs as $job) {
    $printReady = 'print_' . $job;
    $processor->optimizeForPrint($job, $printReady);
    echo "Print-optimized: $job -> $printReady\n";
}
```

### 5. Validation and Quality Assurance

#### Single File Validation
```php
// Quick validation check
if ($processor->isValid('document.pdf')) {
    echo "Document is valid\n";
} else {
    echo "Document has issues\n";
}

// Detailed validation
$validation = $processor->validatePdf('document.pdf');

echo "File: {$validation['file_path']}\n";
echo "Valid: " . ($validation['valid'] ? 'Yes' : 'No') . "\n";
echo "Size: " . number_format($validation['file_info']['size_mb'], 2) . " MB\n";

if ($validation['details']['error_count'] > 0) {
    echo "Errors found:\n";
    foreach ($validation['details']['errors'] as $error) {
        echo "  - $error\n";
    }
}

if ($validation['details']['warning_count'] > 0) {
    echo "Warnings:\n";
    foreach ($validation['details']['warnings'] as $warning) {
        echo "  - $warning\n";
    }
}
```

#### Batch Validation
```php
// Validate multiple files
$pdfFiles = glob('/path/to/pdfs/*.pdf');
$results = $processor->validatePdfFiles($pdfFiles);

echo "Validation Summary:\n";
echo "Total files: {$results['summary']['total_files']}\n";
echo "Valid files: {$results['summary']['valid_files']}\n";
echo "Invalid files: {$results['summary']['invalid_files']}\n";
echo "Files with warnings: {$results['summary']['files_with_warnings']}\n";
echo "Total size: " . number_format($results['summary']['total_size_mb'], 2) . " MB\n";

// Process individual results
foreach ($results['results'] as $result) {
    if (!$result['valid']) {
        echo "\nInvalid file: {$result['file_path']}\n";
        echo "Errors:\n";
        foreach ($result['details']['errors'] as $error) {
            echo "  - $error\n";
        }
    }
}

// Filter valid files for further processing
$validFiles = array_filter($results['results'], fn($r) => $r['valid']);
$validFilePaths = array_column($validFiles, 'file_path');

if (!empty($validFilePaths)) {
    $processor->setFiles($validFilePaths)
              ->merge('validated_merged.pdf');
}
```

## Integration with Ghostscript

### Direct Ghostscript Access

The PDF Processor provides direct access to the underlying Ghostscript processor for advanced operations:

```php
// Get direct access to Ghostscript processor
$gs = $processor->getGhostscriptProcessor();

// Apply custom quality presets
$gs->applyQualityPreset(\Nzoom\Ghostscript\Processor::QUALITY_PREPRESS)
   ->setOption('resolution', 1200)
   ->setOption('mono_image_resolution', 2400);

// Execute PDF operation with custom settings
$processor->merge('high_quality_output.pdf');

// Access detailed execution metrics
$metrics = $gs->getExecutionStats();
echo "Average throughput: {$metrics['average_throughput']} files/sec\n";
```

### Custom Ghostscript Configuration

```php
// Initialize with custom Ghostscript options
$processor = new Processor([
    'resolution' => 600,
    'quality' => 0.95,
    'device' => \Nzoom\Ghostscript\Processor::DEVICE_PDF,
    'compatibility' => \Nzoom\Ghostscript\Processor::PDF_VERSION_1_7,
    'compress_pages' => true,
    'embed_all_fonts' => true
]);

// Use advanced Ghostscript features
$gs = $processor->getGhostscriptProcessor();
$gs->setOptions([
    'color_image_resolution' => 600,
    'grayscale_image_resolution' => 600,
    'mono_image_resolution' => 1200,
    'allow_printing' => true,
    'allow_copying' => false
]);
```

### Quality Presets Integration

```php
// Apply Ghostscript quality presets through PDF processor
$gs = $processor->getGhostscriptProcessor();

// Screen quality for web
$gs->applyQualityPreset(\Nzoom\Ghostscript\Processor::QUALITY_SCREEN);
$processor->optimizeForWeb('input.pdf', 'web_output.pdf');

// Prepress quality for commercial printing
$gs->applyQualityPreset(\Nzoom\Ghostscript\Processor::QUALITY_PREPRESS);
$processor->optimizeForPrint('input.pdf', 'print_output.pdf');
```

## Error Handling

### Exception Types

The PDF Processor can throw the following exceptions:

- **`Exception`**: High-level operation failures with descriptive messages
- **`RuntimeException`**: Underlying Ghostscript execution issues (propagated from Ghostscript processor)
- **`InvalidArgumentException`**: Invalid parameters or file paths (propagated from Ghostscript processor)

### Comprehensive Error Handling

```php
try {
    $processor = new Processor();

    // Validate files before processing
    $files = ['doc1.pdf', 'doc2.pdf', 'doc3.pdf'];
    foreach ($files as $file) {
        if (!file_exists($file)) {
            throw new Exception("File not found: $file");
        }

        if (!$processor->isValid($file)) {
            $validation = $processor->validatePdf($file);
            $errors = implode(', ', $validation['details']['errors']);
            throw new Exception("Invalid PDF '$file': $errors");
        }
    }

    // Execute operation with error checking
    $processor->setFiles($files);
    $success = $processor->merge('merged_output.pdf');

    if (!$success) {
        $errors = $processor->getErrors();
        throw new Exception("Merge failed: " . implode(', ', $errors));
    }

    echo "Operation completed successfully\n";

} catch (RuntimeException $e) {
    echo "Ghostscript error: " . $e->getMessage() . "\n";
    // Log error for debugging
    error_log("Ghostscript error: " . $e->getMessage());

} catch (Exception $e) {
    echo "PDF processing error: " . $e->getMessage() . "\n";
    // Handle application-specific errors

} finally {
    // Clean up resources
    if (isset($processor)) {
        $processor->clearFiles()->clearErrors();
    }
}
```

### Error Recovery Patterns

```php
function processWithRetry($processor, $operation, $maxRetries = 3) {
    $attempt = 0;

    while ($attempt < $maxRetries) {
        try {
            $processor->clearErrors();

            $result = $operation($processor);

            if ($result && !$processor->hasErrors()) {
                return true; // Success
            }

            // Log errors and retry
            $errors = $processor->getErrors();
            error_log("Attempt " . ($attempt + 1) . " failed: " . implode(', ', $errors));

        } catch (Exception $e) {
            error_log("Exception on attempt " . ($attempt + 1) . ": " . $e->getMessage());
        }

        $attempt++;

        // Wait before retry
        if ($attempt < $maxRetries) {
            sleep(1);
        }
    }

    return false; // All attempts failed
}

// Usage example
$success = processWithRetry($processor, function($proc) {
    return $proc->optimizeForWeb('large.pdf', 'optimized.pdf');
});

if (!$success) {
    echo "Operation failed after all retry attempts\n";
}
```

### Validation-First Approach

```php
function safeProcessing($processor, $files, $operation) {
    // Step 1: Validate all files
    $validFiles = [];
    $invalidFiles = [];

    foreach ($files as $file) {
        if (!file_exists($file)) {
            $invalidFiles[] = ['file' => $file, 'error' => 'File not found'];
            continue;
        }

        $validation = $processor->validatePdf($file);
        if ($validation['valid']) {
            $validFiles[] = $file;
        } else {
            $invalidFiles[] = [
                'file' => $file,
                'error' => implode(', ', $validation['details']['errors'])
            ];
        }
    }

    // Step 2: Report invalid files
    if (!empty($invalidFiles)) {
        echo "Invalid files detected:\n";
        foreach ($invalidFiles as $invalid) {
            echo "  - {$invalid['file']}: {$invalid['error']}\n";
        }

        if (empty($validFiles)) {
            return false; // No valid files to process
        }
    }

    // Step 3: Process valid files
    try {
        $processor->setFiles($validFiles);
        return $operation($processor);

    } catch (Exception $e) {
        echo "Processing failed: " . $e->getMessage() . "\n";
        return false;
    }
}

// Usage
$files = ['doc1.pdf', 'doc2.pdf', 'corrupted.pdf'];
$success = safeProcessing($processor, $files, function($proc) {
    return $proc->merge('safe_merged.pdf');
});
```

## Debugging & Monitoring

### Enable Debug Mode

```php
// Enable comprehensive debugging
$processor->enableDebug();

// Execute operation with monitoring
$processor->optimizeForWeb('input.pdf', 'output.pdf');

// Access debug information
$debugInfo = $processor->getLastDebugInfo();

echo "Debug Information:\n";
echo "Operation: {$debugInfo['operation_type']}\n";
echo "Duration: " . number_format($debugInfo['total_duration'], 4) . "s\n";
echo "Memory used: " . number_format($debugInfo['memory_used'] / 1024 / 1024, 2) . "MB\n";
echo "Success: " . ($debugInfo['success'] ? 'Yes' : 'No') . "\n";
```

### Performance Monitoring

```php
// Enable debug mode for performance tracking
$processor->enableDebug();

// Process multiple operations
$operations = [
    ['merge', ['doc1.pdf', 'doc2.pdf'], 'merged.pdf'],
    ['optimizeForWeb', ['large.pdf'], 'web.pdf'],
    ['extractPages', ['book.pdf', 'chapter.pdf', 1, 10], null]
];

foreach ($operations as [$operation, $params, $output]) {
    $startTime = microtime(true);

    if ($operation === 'merge') {
        $processor->setFiles($params)->merge($output);
    } elseif ($operation === 'optimizeForWeb') {
        $processor->optimizeForWeb($params[0], $output);
    } elseif ($operation === 'extractPages') {
        $processor->extractPages($params[0], $params[1], $params[2], $params[3]);
    }

    $endTime = microtime(true);
    $duration = $endTime - $startTime;

    echo "Operation '$operation' completed in " . number_format($duration, 4) . "s\n";
}

// Get overall execution statistics
$stats = $processor->getExecutionStats();
echo "\nOverall Statistics:\n";
echo "Total executions: {$stats['total_executions']}\n";
echo "Success rate: " . number_format($stats['success_rate'], 2) . "%\n";
echo "Average duration: " . number_format($stats['average_duration'], 4) . "s\n";
echo "Average throughput: " . number_format($stats['average_throughput'], 2) . " files/sec\n";
```

### Debug Information Analysis

```php
$processor->enableDebug();
$processor->merge('output.pdf');

$debugInfo = $processor->getLastDebugInfo();

// Analyze performance metrics
if (isset($debugInfo['performance'])) {
    $perf = $debugInfo['performance'];
    echo "Performance Analysis:\n";
    echo "Files per second: " . number_format($perf['files_per_second'], 2) . "\n";
    echo "MB per second: " . number_format($perf['mb_per_second'], 2) . "\n";
    echo "Memory efficiency: " . number_format($perf['memory_efficiency'], 2) . "%\n";
}

// Check for errors or warnings
if (!empty($debugInfo['parsed_output']['errors'])) {
    echo "Errors encountered:\n";
    foreach ($debugInfo['parsed_output']['errors'] as $error) {
        echo "  - $error\n";
    }
}

if (!empty($debugInfo['parsed_output']['warnings'])) {
    echo "Warnings:\n";
    foreach ($debugInfo['parsed_output']['warnings'] as $warning) {
        echo "  - $warning\n";
    }
}
```

## Best Practices

### 1. Resource Management

```php
// Always clear resources between operations
$processor->clearFiles()
          ->clearErrors();

// Use try-finally for guaranteed cleanup
try {
    $processor->setFiles($files)->merge('output.pdf');
} finally {
    $processor->clearFiles()->clearErrors();
}

// Disable debug mode in production
if (!$isProduction) {
    $processor->enableDebug();
}
```

### 2. File Validation Strategy

```php
// Validate before processing
function validateBeforeProcessing($processor, $files) {
    $validFiles = [];

    foreach ($files as $file) {
        if (!file_exists($file)) {
            echo "Warning: File not found: $file\n";
            continue;
        }

        if ($processor->isValid($file)) {
            $validFiles[] = $file;
        } else {
            echo "Warning: Invalid PDF: $file\n";
        }
    }

    return $validFiles;
}

// Use validated files
$files = ['doc1.pdf', 'doc2.pdf', 'doc3.pdf'];
$validFiles = validateBeforeProcessing($processor, $files);

if (!empty($validFiles)) {
    $processor->setFiles($validFiles)->merge('output.pdf');
}
```

### 3. Error Handling Patterns

```php
// Implement graceful degradation
function processWithFallback($processor, $primaryOperation, $fallbackOperation) {
    try {
        return $primaryOperation($processor);
    } catch (Exception $e) {
        echo "Primary operation failed: " . $e->getMessage() . "\n";
        echo "Attempting fallback operation...\n";

        try {
            return $fallbackOperation($processor);
        } catch (Exception $fallbackError) {
            echo "Fallback also failed: " . $fallbackError->getMessage() . "\n";
            return false;
        }
    }
}

// Usage
$success = processWithFallback(
    $processor,
    fn($p) => $p->optimizeForPrint('input.pdf', 'output.pdf'),
    fn($p) => $p->optimizeForWeb('input.pdf', 'output.pdf')
);
```

### 4. Performance Optimization

```php
// Batch processing for efficiency
function batchProcess($processor, $files, $batchSize = 10) {
    $batches = array_chunk($files, $batchSize);
    $results = [];

    foreach ($batches as $batchIndex => $batch) {
        echo "Processing batch " . ($batchIndex + 1) . "/" . count($batches) . "\n";

        $outputFile = "batch_" . ($batchIndex + 1) . ".pdf";

        try {
            $processor->setFiles($batch)->merge($outputFile);
            $results[] = $outputFile;
            echo "Batch completed: $outputFile\n";
        } catch (Exception $e) {
            echo "Batch failed: " . $e->getMessage() . "\n";
        }

        // Clear resources between batches
        $processor->clearFiles()->clearErrors();
    }

    return $results;
}

// Process large number of files in batches
$allFiles = glob('/path/to/pdfs/*.pdf');
$batchResults = batchProcess($processor, $allFiles, 5);
```

### 5. Configuration Management

```php
// Use configuration objects for reusable settings
class PdfProcessingConfig {
    public static function webOptimized(): array {
        return [
            'resolution' => 150,
            'quality' => 0.7,
            'compress_pages' => true,
            'optimize' => true
        ];
    }

    public static function printQuality(): array {
        return [
            'resolution' => 600,
            'quality' => 0.95,
            'embed_all_fonts' => true,
            'compress_pages' => false
        ];
    }

    public static function archival(): array {
        return [
            'resolution' => 600,
            'quality' => 1.0,
            'embed_all_fonts' => true,
            'compress_pages' => false,
            'optimize' => false
        ];
    }
}

// Apply configurations
$webProcessor = new Processor(PdfProcessingConfig::webOptimized());
$printProcessor = new Processor(PdfProcessingConfig::printQuality());
$archivalProcessor = new Processor(PdfProcessingConfig::archival());
```

## Advanced Usage

### Custom Workflow Builder

```php
class PdfWorkflow {
    private $processor;
    private $steps = [];

    public function __construct(Processor $processor) {
        $this->processor = $processor;
    }

    public function addStep(string $name, callable $operation): self {
        $this->steps[$name] = $operation;
        return $this;
    }

    public function execute(): array {
        $results = [];

        foreach ($this->steps as $name => $operation) {
            try {
                $result = $operation($this->processor);
                $results[$name] = ['success' => true, 'result' => $result];
                echo "Step '$name' completed successfully\n";
            } catch (Exception $e) {
                $results[$name] = ['success' => false, 'error' => $e->getMessage()];
                echo "Step '$name' failed: " . $e->getMessage() . "\n";
            }
        }

        return $results;
    }
}

// Build and execute workflow
$workflow = new PdfWorkflow($processor);

$workflow->addStep('merge', function($p) {
        return $p->setFiles(['doc1.pdf', 'doc2.pdf'])->merge('merged.pdf');
    })
    ->addStep('optimize', function($p) {
        return $p->optimizeForWeb('merged.pdf', 'optimized.pdf');
    })
    ->addStep('validate', function($p) {
        return $p->validatePdf('optimized.pdf');
    });

$results = $workflow->execute();
```

### Integration with File Systems

```php
// Process files from directory with filtering
function processDirectory($processor, $directory, $pattern = '*.pdf') {
    $files = glob($directory . '/' . $pattern);

    if (empty($files)) {
        echo "No PDF files found in $directory\n";
        return false;
    }

    echo "Found " . count($files) . " PDF files\n";

    // Validate all files first
    $validFiles = [];
    foreach ($files as $file) {
        if ($processor->isValid($file)) {
            $validFiles[] = $file;
        } else {
            echo "Skipping invalid file: " . basename($file) . "\n";
        }
    }

    if (empty($validFiles)) {
        echo "No valid PDF files to process\n";
        return false;
    }

    // Process valid files
    $outputFile = $directory . '/merged_' . date('Y-m-d_H-i-s') . '.pdf';
    return $processor->setFiles($validFiles)->merge($outputFile);
}

// Usage
$success = processDirectory($processor, '/path/to/pdfs');
```

---

## Troubleshooting

### Common Issues

1. **File not found errors**
   - Verify file paths are correct
   - Check file permissions
   - Ensure files exist before processing

2. **Invalid PDF errors**
   - Use `validatePdf()` for detailed error information
   - Check if files are corrupted
   - Verify PDF format compliance

3. **Memory issues with large files**
   - Enable debug mode to monitor memory usage
   - Process files individually instead of batch
   - Increase PHP memory limit if necessary

4. **Ghostscript errors**
   - Check Ghostscript installation
   - Verify version compatibility (9.50+)
   - Review Ghostscript processor documentation

### Debug Commands

```php
// Test basic functionality
$processor->enableDebug();
$testResult = $processor->convertImageToPdf('test.jpg', 'test.pdf');

if (!$testResult) {
    echo "Test failed. Debug info:\n";
    print_r($processor->getLastDebugInfo());
}

// Check processor status
echo "Debug mode: " . ($processor->isDebugEnabled() ? 'enabled' : 'disabled') . "\n";
echo "Files queued: " . count($processor->getFiles()) . "\n";
echo "Has errors: " . ($processor->hasErrors() ? 'yes' : 'no') . "\n";
```

---

## License

This documentation is part of the nZoom PDF Processor package.

## Support

For issues and questions:
- Check the debug output for detailed error information
- Verify Ghostscript installation and configuration
- Review the comprehensive error handling examples above
- Consult the Ghostscript processor documentation for advanced features
