# Nzoom Ghostscript Processor Documentation

## Overview

The **Nzoom Ghostscript Processor** (`\Nzoom\Ghostscript\Processor`) is an advanced, enterprise-grade PHP interface to Ghostscript that provides comprehensive PDF processing, document conversion, and optimization capabilities. It abstracts the complexity of Ghostscript command-line operations into a fluent, type-safe PHP API with extensive debugging and monitoring features.

## Table of Contents

- [Installation & Requirements](#installation--requirements)
- [Quick Start](#quick-start)
- [Core Features](#core-features)
- [API Reference](#api-reference)
- [Configuration Options](#configuration-options)
- [Quality Presets](#quality-presets)
- [Usage Examples](#usage-examples)
- [Error Handling](#error-handling)
- [Debugging & Monitoring](#debugging--monitoring)
- [Best Practices](#best-practices)

## Installation & Requirements

### System Requirements

- **PHP**: 8.0 or higher
- **Ghostscript**: 9.50 or higher installed and accessible via PATH
- **Memory**: Sufficient RAM for processing large PDF files
- **Disk Space**: Adequate space for temporary file operations
- **Permissions**: Read/write access to input/output directories

### Installation

```bash
# Ensure Ghostscript is installed
# Ubuntu/Debian
sudo apt-get install ghostscript

# CentOS/RHEL
sudo yum install ghostscript

# macOS
brew install ghostscript

# Windows
# Download and install from https://www.ghostscript.com/download/gsdnld.html
```

### Autoloading

The class follows PSR-4 autoloading standards:

```php
require_once 'vendor/autoload.php';
use Nzoom\Ghostscript\Processor;
```

## Quick Start

### Basic PDF Merging

```php
use Nzoom\Ghostscript\Processor;

// Create processor instance
$processor = new Processor();

// Merge multiple PDFs
$processor->addInputFiles(['doc1.pdf', 'doc2.pdf', 'doc3.pdf'])
          ->setOutputFile('merged.pdf')
          ->setupPdfMerging()
          ->execute();
```

### Image Conversion

```php
// Convert PDF to PNG
$processor->addInputFile('document.pdf')
          ->setOutputFile('output.png')
          ->setupImageConversion(Processor::DEVICE_PNG)
          ->execute();
```

### Quality Optimization

```php
// Optimize for web delivery
$processor->addInputFile('large.pdf')
          ->setOutputFile('optimized.pdf')
          ->applyQualityPreset(Processor::QUALITY_SCREEN)
          ->execute();
```

## Core Features

### 🔧 Document Operations
- **PDF Merging**: Combine multiple PDFs with preserved formatting
- **Page Extraction**: Extract specific page ranges from documents
- **Format Conversion**: Convert between PDF, PS, EPS, and image formats
- **Document Optimization**: Optimize for web, print, or archival use

### 📊 Quality Management
- **Predefined Presets**: Screen, ebook, printer, and prepress quality levels
- **Resolution Control**: Granular DPI settings for different content types
- **Compression Options**: Intelligent compression with quality preservation
- **Font Handling**: Comprehensive font embedding and subsetting

### 🛡️ Validation & Security
- **PDF Validation**: Comprehensive integrity checking with detailed reports
- **Corruption Detection**: Identify and report damaged files
- **Permission Management**: Control printing, copying, and editing permissions
- **Security Settings**: Apply password protection and access restrictions

### 🔍 Monitoring & Debugging
- **Performance Metrics**: Real-time processing statistics
- **Execution History**: Complete audit trail of operations
- **Memory Tracking**: Monitor resource usage and optimization
- **Error Diagnostics**: Detailed error categorization and reporting

## API Reference

### Constants

#### Output Devices
```php
Processor::DEVICE_PDF      // PDF output (pdfwrite)
Processor::DEVICE_PNG      // PNG images (png16m)
Processor::DEVICE_JPEG     // JPEG images (jpeg)
Processor::DEVICE_TIFF     // TIFF images (tiff24nc)
Processor::DEVICE_PS       // PostScript (ps2write)
Processor::DEVICE_EPS      // Encapsulated PostScript (eps2write)
```

#### PDF Compatibility Levels
```php
Processor::PDF_VERSION_1_2 // Acrobat 3.0 compatibility
Processor::PDF_VERSION_1_3 // Acrobat 4.0 compatibility
Processor::PDF_VERSION_1_4 // Acrobat 5.0 compatibility (recommended)
Processor::PDF_VERSION_1_5 // Acrobat 6.0 compatibility
Processor::PDF_VERSION_1_6 // Acrobat 7.0 compatibility
Processor::PDF_VERSION_1_7 // Acrobat 8.0 compatibility
```

#### Quality Presets
```php
Processor::QUALITY_SCREEN    // 72 DPI - Web/screen viewing
Processor::QUALITY_EBOOK     // 150 DPI - Digital reading
Processor::QUALITY_PRINTER   // 300 DPI - Office printing
Processor::QUALITY_PREPRESS  // 600+ DPI - Commercial printing
```

### Core Methods

#### Constructor
```php
public function __construct(array $options = [])
```
Initialize processor with optional configuration.

#### File Management
```php
public function addInputFile(string $file): self
public function addInputFiles(array $files): self
public function setInputFiles(array $files): self
public function getInputFiles(): array
public function clearInputFiles(): self
public function setOutputFile(string $file): self
public function getOutputFile(): string
```

#### Configuration
```php
public function setOption(string $key, $value): self
public function setOptions(array $options): self
public function getOption(string $key, $default = null)
public function getOptions(): array
public function resetOptions(): self
public function applyQualityPreset(string $preset): self
```

#### Operations
```php
public function execute(): bool
public function setupPdfMerging(): self
public function setupImageConversion(string $format = self::DEVICE_PNG): self
public function setupPageExtraction(int $firstPage, int $lastPage = null): self
public function setupWebOptimization(): self
public function setupPrintOptimization(): self
```

#### Validation
```php
public function validatePdf(string $file): array
public function validatePdfFiles(array $files): array
```

#### Debugging
```php
public function enableDebug(): self
public function disableDebug(): self
public function isDebugEnabled(): bool
public function getLastDebugInfo(): array
public function getExecutionHistory(): array
public function getExecutionStats(): array
```

#### Error Handling
```php
public function getErrors(): array
public function getLastError(): ?string
public function hasErrors(): bool
public function clearErrors(): self
```

## Configuration Options

### Core Execution Options
- `device` (string): Output device type
- `quiet` (bool): Suppress informational messages
- `nopause` (bool): Disable interactive prompts
- `batch` (bool): Enable batch processing mode
- `safer` (bool): Enable safer execution mode

### PDF-Specific Options
- `compatibility` (string): PDF version compatibility
- `compress_pages` (bool): Enable page compression
- `compress_streams` (bool): Enable stream compression
- `use_flate_compression` (bool): Use Flate compression
- `optimize` (bool): Enable general optimization
- `embed_all_fonts` (bool): Embed all fonts

### Resolution Settings
- `resolution` (int): Default resolution in DPI
- `color_image_resolution` (int): Color image resolution
- `grayscale_image_resolution` (int): Grayscale image resolution
- `mono_image_resolution` (int): Monochrome image resolution

### Quality Settings
- `color_image_quality` (float): Color image quality (0.0-1.0)
- `grayscale_image_quality` (float): Grayscale image quality (0.0-1.0)

### Security Options
- `allow_printing` (bool): Allow document printing
- `allow_copying` (bool): Allow content copying
- `allow_annotation` (bool): Allow annotations
- `allow_form_filling` (bool): Allow form completion

## Quality Presets

### QUALITY_SCREEN (Web/Screen)
- **Resolution**: 72 DPI
- **Use Case**: Web pages, email attachments, screen presentations
- **Characteristics**: Small file size, fast loading, moderate quality

### QUALITY_EBOOK (Digital Reading)
- **Resolution**: 150 DPI
- **Use Case**: E-readers, tablets, digital publications
- **Characteristics**: Balanced quality and size, clear text

### QUALITY_PRINTER (Office Printing)
- **Resolution**: 300 DPI
- **Use Case**: Office documents, reports, presentations
- **Characteristics**: High quality, suitable for desktop printing

### QUALITY_PREPRESS (Commercial Printing)
- **Resolution**: 600+ DPI
- **Use Case**: Commercial printing, marketing materials
- **Characteristics**: Maximum quality, large file size, color accuracy

## Usage Examples

### 1. Basic PDF Operations

#### Simple PDF Merging
```php
use Nzoom\Ghostscript\Processor;

$processor = new Processor();

// Merge multiple PDFs in order
$success = $processor->addInputFiles([
    '/path/to/cover.pdf',
    '/path/to/content.pdf',
    '/path/to/appendix.pdf'
])->setOutputFile('/path/to/merged.pdf')
  ->setupPdfMerging()
  ->execute();

if ($success) {
    echo "PDF merge completed successfully\n";
} else {
    echo "Merge failed: " . $processor->getLastError() . "\n";
}
```

#### Page Extraction
```php
// Extract pages 5-10 from a document
$processor->clearInputFiles()
          ->addInputFile('large_document.pdf')
          ->setOutputFile('extracted_pages.pdf')
          ->setupPageExtraction(5, 10)
          ->execute();

// Extract single page
$processor->setupPageExtraction(1) // Only first page
          ->execute();
```

#### Format Conversion
```php
// Convert PDF to high-quality PNG
$processor->addInputFile('document.pdf')
          ->setOutputFile('document.png')
          ->setOption('resolution', 300)
          ->setupImageConversion(Processor::DEVICE_PNG)
          ->execute();

// Convert to JPEG with custom quality
$processor->setOption('color_image_quality', 0.85)
          ->setupImageConversion(Processor::DEVICE_JPEG)
          ->execute();
```

### 2. Quality Optimization

#### Web Optimization
```php
// Optimize large PDF for web delivery
$processor = new Processor();
$processor->addInputFile('large_document.pdf')
          ->setOutputFile('web_optimized.pdf')
          ->applyQualityPreset(Processor::QUALITY_SCREEN)
          ->execute();

// Custom web optimization
$processor->setOptions([
    'resolution' => 96,
    'color_image_quality' => 0.6,
    'compress_pages' => true,
    'optimize' => true
])->setupWebOptimization()
  ->execute();
```

#### Print Optimization
```php
// Optimize for high-quality printing
$processor->addInputFile('document.pdf')
          ->setOutputFile('print_ready.pdf')
          ->applyQualityPreset(Processor::QUALITY_PREPRESS)
          ->setOptions([
              'resolution' => 600,
              'mono_image_resolution' => 1200,
              'embed_all_fonts' => true
          ])->execute();
```

### 3. Batch Processing

#### Process Multiple Files
```php
$inputFiles = [
    'document1.pdf',
    'document2.pdf',
    'document3.pdf'
];

$processor = new Processor();

foreach ($inputFiles as $index => $file) {
    $outputFile = "optimized_" . ($index + 1) . ".pdf";

    $processor->clearInputFiles()
              ->addInputFile($file)
              ->setOutputFile($outputFile)
              ->applyQualityPreset(Processor::QUALITY_EBOOK)
              ->execute();

    if ($processor->hasErrors()) {
        echo "Error processing $file: " . $processor->getLastError() . "\n";
        $processor->clearErrors();
    } else {
        echo "Successfully processed: $file -> $outputFile\n";
    }
}
```

#### Batch Validation
```php
$pdfFiles = glob('/path/to/pdfs/*.pdf');

$results = $processor->validatePdfFiles($pdfFiles);

echo "Validation Summary:\n";
echo "Total files: {$results['summary']['total_files']}\n";
echo "Valid files: {$results['summary']['valid_files']}\n";
echo "Invalid files: {$results['summary']['invalid_files']}\n";
echo "Files with warnings: {$results['summary']['files_with_warnings']}\n";

// Process individual results
foreach ($results['results'] as $result) {
    if (!$result['valid']) {
        echo "\nInvalid file: {$result['file_path']}\n";
        foreach ($result['details']['errors'] as $error) {
            echo "  - $error\n";
        }
    }
}
```

### 4. Advanced Configuration

#### Custom Quality Settings
```php
$processor = new Processor([
    'device' => Processor::DEVICE_PDF,
    'compatibility' => Processor::PDF_VERSION_1_7,
    'resolution' => 300,
    'color_image_resolution' => 300,
    'grayscale_image_resolution' => 300,
    'mono_image_resolution' => 1200,
    'color_image_quality' => 0.9,
    'compress_pages' => true,
    'compress_streams' => true,
    'use_flate_compression' => true,
    'optimize' => true,
    'embed_all_fonts' => true
]);
```

#### Security Configuration
```php
// Create PDF with restricted permissions
$processor->setOptions([
    'allow_printing' => false,
    'allow_copying' => false,
    'allow_annotation' => false,
    'allow_form_filling' => true
])->execute();
```

#### Custom Parameters
```php
// Add custom Ghostscript parameters
$processor->setOption('custom_parameters', [
    '-dPDFSETTINGS=/prepress',
    '-dColorConversionStrategy=/RGB',
    '-dProcessColorModel=/DeviceRGB'
]);
```

### 5. Error Handling & Validation

#### Comprehensive Error Handling
```php
try {
    $processor = new Processor();

    // Validate input files before processing
    foreach ($processor->getInputFiles() as $file) {
        if (!file_exists($file)) {
            throw new Exception("Input file not found: $file");
        }

        $validation = $processor->validatePdf($file);
        if (!$validation['valid']) {
            throw new Exception("Invalid PDF: $file - " .
                implode(', ', $validation['details']['errors']));
        }
    }

    // Execute with error checking
    $success = $processor->execute();

    if (!$success) {
        $errors = $processor->getErrors();
        throw new Exception("Processing failed: " . implode(', ', $errors));
    }

    echo "Processing completed successfully\n";

} catch (RuntimeException $e) {
    echo "Ghostscript error: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "Application error: " . $e->getMessage() . "\n";
}
```

#### Validation with Detailed Reporting
```php
$validation = $processor->validatePdf('document.pdf');

echo "File: {$validation['file_path']}\n";
echo "Valid: " . ($validation['valid'] ? 'Yes' : 'No') . "\n";
echo "Size: " . number_format($validation['file_size'] / 1024, 2) . " KB\n";

if ($validation['details']['error_count'] > 0) {
    echo "Errors:\n";
    foreach ($validation['details']['errors'] as $error) {
        echo "  - $error\n";
    }
}

if ($validation['details']['warning_count'] > 0) {
    echo "Warnings:\n";
    foreach ($validation['details']['warnings'] as $warning) {
        echo "  - $warning\n";
    }
}
```

## Debugging & Monitoring

### Enable Debug Mode

```php
// Enable comprehensive debugging
$processor->enableDebug();

// Execute operation with monitoring
$processor->addInputFile('document.pdf')
          ->setOutputFile('output.pdf')
          ->execute();

// Access debug information
$debugInfo = $processor->getLastDebugInfo();
```

### Performance Monitoring

```php
// Get execution statistics
$stats = $processor->getExecutionStats();

echo "Performance Metrics:\n";
echo "Total executions: {$stats['total_executions']}\n";
echo "Success rate: " . number_format($stats['success_rate'], 2) . "%\n";
echo "Average duration: " . number_format($stats['average_duration'], 4) . "s\n";
echo "Average memory: " . number_format($stats['average_memory_used'] / 1024 / 1024, 2) . "MB\n";
echo "Total files processed: {$stats['total_files_processed']}\n";
echo "Average throughput: " . number_format($stats['average_throughput'], 2) . " files/sec\n";
```

### Detailed Debug Information

```php
$processor->enableDebug();
$processor->execute();

$debugInfo = $processor->getLastDebugInfo();

echo "Debug Information:\n";
echo "Operation: {$debugInfo['operation_type']}\n";
echo "Command: {$debugInfo['command']}\n";
echo "Duration: " . number_format($debugInfo['total_duration'], 4) . "s\n";
echo "Memory used: " . number_format($debugInfo['memory_used'] / 1024 / 1024, 2) . "MB\n";
echo "Peak memory: " . number_format($debugInfo['peak_memory'] / 1024 / 1024, 2) . "MB\n";
echo "Files processed: {$debugInfo['input_files_count']}\n";
echo "Success: " . ($debugInfo['success'] ? 'Yes' : 'No') . "\n";

// Performance metrics
$perf = $debugInfo['performance'];
echo "Processing rate: " . number_format($perf['files_per_second'], 2) . " files/sec\n";
echo "Throughput: " . number_format($perf['mb_per_second'], 2) . " MB/sec\n";

// Error analysis
if (!empty($debugInfo['parsed_output']['errors'])) {
    echo "Errors encountered:\n";
    foreach ($debugInfo['parsed_output']['errors'] as $error) {
        echo "  - $error\n";
    }
}
```

### Export Debug Data

```php
// Export comprehensive debug information
$exportData = $processor->exportDebugInfo();

// Save to file for analysis
file_put_contents('debug_export.json', json_encode($exportData, JSON_PRETTY_PRINT));

echo "Debug data exported:\n";
echo "Exported at: {$exportData['exported_at']}\n";
echo "PHP version: {$exportData['php_version']}\n";
echo "OS: {$exportData['os']}\n";
echo "Ghostscript version: {$exportData['ghostscript_version']}\n";
```

## Error Handling

### Exception Types

The processor can throw the following exceptions:

- **`RuntimeException`**: Ghostscript execution failures, missing executable
- **`InvalidArgumentException`**: Invalid parameters, missing files, bad configuration

### Error Recovery Patterns

```php
// Graceful error handling with recovery
function processWithRetry($processor, $inputFile, $outputFile, $maxRetries = 3) {
    $attempt = 0;

    while ($attempt < $maxRetries) {
        try {
            $processor->clearErrors()
                      ->clearInputFiles()
                      ->addInputFile($inputFile)
                      ->setOutputFile($outputFile)
                      ->execute();

            if (!$processor->hasErrors()) {
                return true; // Success
            }

            // Log errors and retry with different settings
            $errors = $processor->getErrors();
            error_log("Attempt " . ($attempt + 1) . " failed: " . implode(', ', $errors));

            // Try with lower quality on retry
            if ($attempt > 0) {
                $processor->applyQualityPreset(Processor::QUALITY_SCREEN);
            }

        } catch (Exception $e) {
            error_log("Exception on attempt " . ($attempt + 1) . ": " . $e->getMessage());
        }

        $attempt++;
    }

    return false; // All attempts failed
}
```

### Validation Before Processing

```php
function validateAndProcess($processor, $files) {
    $validFiles = [];
    $invalidFiles = [];

    // Pre-validate all files
    foreach ($files as $file) {
        if (!file_exists($file)) {
            $invalidFiles[] = ['file' => $file, 'error' => 'File not found'];
            continue;
        }

        $validation = $processor->validatePdf($file);
        if ($validation['valid']) {
            $validFiles[] = $file;
        } else {
            $invalidFiles[] = [
                'file' => $file,
                'error' => implode(', ', $validation['details']['errors'])
            ];
        }
    }

    // Report invalid files
    if (!empty($invalidFiles)) {
        echo "Invalid files found:\n";
        foreach ($invalidFiles as $invalid) {
            echo "  - {$invalid['file']}: {$invalid['error']}\n";
        }
    }

    // Process valid files
    if (!empty($validFiles)) {
        $processor->setInputFiles($validFiles);
        return $processor->execute();
    }

    return false;
}
```

## Best Practices

### 1. Resource Management

```php
// Always clear resources between operations
$processor->clearInputFiles()
          ->clearErrors()
          ->clearDebugInfo();

// Use appropriate quality settings for use case
$processor->applyQualityPreset(Processor::QUALITY_SCREEN); // For web
$processor->applyQualityPreset(Processor::QUALITY_PRINTER); // For print
```

### 2. Performance Optimization

```php
// Disable debug mode in production
$processor->disableDebug();

// Use batch processing for multiple files
$processor->setInputFiles($allFiles)->execute();

// Rather than individual processing:
// foreach ($files as $file) { $processor->addInputFile($file)->execute(); }
```

### 3. Error Handling

```php
// Always check for errors after execution
if (!$processor->execute()) {
    $errors = $processor->getErrors();
    // Handle errors appropriately
}

// Use try-catch for critical operations
try {
    $processor->execute();
} catch (RuntimeException $e) {
    // Handle Ghostscript-specific errors
} catch (Exception $e) {
    // Handle general errors
}
```

### 4. Configuration Management

```php
// Use configuration arrays for reusable settings
$webConfig = [
    'resolution' => 96,
    'color_image_quality' => 0.6,
    'compress_pages' => true,
    'optimize' => true
];

$printConfig = [
    'resolution' => 300,
    'color_image_quality' => 0.9,
    'embed_all_fonts' => true
];

// Apply configurations as needed
$processor->setOptions($webConfig);
```

### 5. Memory Management

```php
// For large files, monitor memory usage
$processor->enableDebug();
$processor->execute();

$memoryUsed = $processor->getLastMemoryUsage();
if ($memoryUsed > 100 * 1024 * 1024) { // 100MB
    // Consider using lower quality settings or processing in chunks
    $processor->applyQualityPreset(Processor::QUALITY_SCREEN);
}
```

### 6. Validation Strategy

```php
// Implement comprehensive validation workflow
function robustPdfProcessing($files, $outputFile) {
    $processor = new Processor();

    // 1. Validate all input files
    $validationResults = $processor->validatePdfFiles($files);
    if ($validationResults['summary']['invalid_files'] > 0) {
        throw new Exception("Invalid files detected");
    }

    // 2. Configure for optimal processing
    $processor->applyQualityPreset(Processor::QUALITY_PRINTER)
              ->enableDebug();

    // 3. Execute with error checking
    $success = $processor->setInputFiles($files)
                         ->setOutputFile($outputFile)
                         ->setupPdfMerging()
                         ->execute();

    // 4. Validate output
    if ($success && file_exists($outputFile)) {
        $outputValidation = $processor->validatePdf($outputFile);
        if (!$outputValidation['valid']) {
            throw new Exception("Output file validation failed");
        }
    }

    return $success;
}
```

## Troubleshooting

### Common Issues

1. **Ghostscript not found**
   - Ensure Ghostscript is installed and in PATH
   - Check executable permissions
   - Verify version compatibility (9.50+)

2. **Memory issues with large files**
   - Use lower quality presets
   - Process files individually instead of batch
   - Increase PHP memory limit

3. **Permission errors**
   - Check file and directory permissions
   - Ensure write access to output directory
   - Verify input file readability

4. **Quality issues**
   - Adjust resolution settings
   - Use appropriate quality presets
   - Check font embedding settings

### Debug Commands

```php
// Get system information
echo "Ghostscript version: " . $processor->getVersion() . "\n";
echo "Supported devices: " . implode(', ', $processor->getSupportedDevices()) . "\n";

// Test basic functionality
$processor->enableDebug();
$testResult = $processor->addInputFile('test.pdf')
                        ->setOutputFile('test_output.pdf')
                        ->execute();

if (!$testResult) {
    echo "Test failed. Debug info:\n";
    print_r($processor->getLastDebugInfo());
}
```

---

## License

This documentation is part of the nZoom Ghostscript Processor package.

## Support

For issues and questions:
- Check the debug output for detailed error information
- Verify Ghostscript installation and version
- Review configuration options and quality presets
- Consult the comprehensive error handling examples above
