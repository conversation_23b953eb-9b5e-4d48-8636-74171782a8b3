<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Adapter</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item"><a href="index.html">Adapter</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#25">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">6%</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">29%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#25">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">43397</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">1217</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">368</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">245</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="JsonExportFormatAdapter.php.html#167"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#763"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#481"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#532"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#555"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomDateFormat">convertCustomDateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#586"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#604"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#628"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#665"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#722"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#742"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#793"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#392"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#895"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#918"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#949"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#966"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#990"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#18"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#134"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#143"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#367"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#23"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#181"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#93"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#318"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#120"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#54"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#136"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createSpreadsheet">createSpreadsheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#165"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#188"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#239"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#260"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportData">processExportData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#287"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#302"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::styleHeaderRow">styleHeaderRow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#43"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::__construct">__construct</abbr></a></td><td class="text-right">80%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#793"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">6972</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#392"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#481"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#23"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#18"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#665"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#318"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#120"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#54"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#628"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#763"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#918"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#949"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#742"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#188"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#165"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CsvExportFormatAdapter.php.html#93"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#367"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#895"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#722"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#532"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#287"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#604"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#586"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExcelExportFormatAdapter.php.html#239"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractExportFormatAdapter.php.html#43"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::__construct">__construct</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 13 10:24:11 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([0,3,0,1,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([47,0,0,0,0,0,0,0,0,1,0,13], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[6.521739130434782,38,"<a href=\"AbstractExportFormatAdapter.php.html#12\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter<\/a>"],[29.310344827586203,25,"<a href=\"CsvExportFormatAdapter.php.html#13\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter<\/a>"],[0.6944444444444444,210,"<a href=\"ExcelExportFormatAdapter.php.html#25\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter<\/a>"],[4.477611940298507,20,"<a href=\"JsonExportFormatAdapter.php.html#13\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[80,2,"<a href=\"AbstractExportFormatAdapter.php.html#43\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::__construct<\/a>"],[100,1,"<a href=\"AbstractExportFormatAdapter.php.html#57\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::setConfiguration<\/a>"],[0,5,"<a href=\"AbstractExportFormatAdapter.php.html#70\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getExportFilename<\/a>"],[0,3,"<a href=\"AbstractExportFormatAdapter.php.html#100\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::sendHeaders<\/a>"],[0,4,"<a href=\"AbstractExportFormatAdapter.php.html#127\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::handleExportError<\/a>"],[0,5,"<a href=\"AbstractExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getRecordHeaders<\/a>"],[0,1,"<a href=\"AbstractExportFormatAdapter.php.html#181\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getFormatOptions<\/a>"],[0,3,"<a href=\"AbstractExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateAndPrepareSaveTarget<\/a>"],[0,2,"<a href=\"AbstractExportFormatAdapter.php.html#213\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateStringTarget<\/a>"],[0,5,"<a href=\"AbstractExportFormatAdapter.php.html#231\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validatePhpStreamWrapper<\/a>"],[0,3,"<a href=\"AbstractExportFormatAdapter.php.html#273\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePath<\/a>"],[0,4,"<a href=\"AbstractExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePointer<\/a>"],[0,10,"<a href=\"CsvExportFormatAdapter.php.html#23\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::export<\/a>"],[0,4,"<a href=\"CsvExportFormatAdapter.php.html#93\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDelimiter<\/a>"],[0,5,"<a href=\"CsvExportFormatAdapter.php.html#120\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::normalizeDelimiter<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#136\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#144\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#153\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#169\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"CsvExportFormatAdapter.php.html#177\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatOptions<\/a>"],[0,5,"<a href=\"ExcelExportFormatAdapter.php.html#54\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::export<\/a>"],[0,11,"<a href=\"ExcelExportFormatAdapter.php.html#104\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::extractSizingOptions<\/a>"],[0,1,"<a href=\"ExcelExportFormatAdapter.php.html#136\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createSpreadsheet<\/a>"],[0,4,"<a href=\"ExcelExportFormatAdapter.php.html#165\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setSpreadsheetLocale<\/a>"],[0,4,"<a href=\"ExcelExportFormatAdapter.php.html#188\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getApplicationLocale<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#239\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setDocumentProperties<\/a>"],[0,1,"<a href=\"ExcelExportFormatAdapter.php.html#260\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportData<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#287\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addHeaders<\/a>"],[0,1,"<a href=\"ExcelExportFormatAdapter.php.html#302\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::styleHeaderRow<\/a>"],[0,8,"<a href=\"ExcelExportFormatAdapter.php.html#318\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportDataRecords<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#367\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportRecord<\/a>"],[0,17,"<a href=\"ExcelExportFormatAdapter.php.html#392\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellValueWithFormatting<\/a>"],[0,5,"<a href=\"ExcelExportFormatAdapter.php.html#453\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellDateValue<\/a>"],[0,15,"<a href=\"ExcelExportFormatAdapter.php.html#481\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatFromExportValue<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#532\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomNumberFormat<\/a>"],[0,1,"<a href=\"ExcelExportFormatAdapter.php.html#555\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomDateFormat<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#586\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleExportRecordCellError<\/a>"],[0,2,"<a href=\"ExcelExportFormatAdapter.php.html#604\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeExportDataColumns<\/a>"],[0,5,"<a href=\"ExcelExportFormatAdapter.php.html#628\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyColumnWidthConstraints<\/a>"],[0,8,"<a href=\"ExcelExportFormatAdapter.php.html#665\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyRowHeightConstraints<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#722\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyVerticalAlignment<\/a>"],[0,4,"<a href=\"ExcelExportFormatAdapter.php.html#742\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createWriter<\/a>"],[0,4,"<a href=\"ExcelExportFormatAdapter.php.html#763\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleSpreadsheetError<\/a>"],[0,83,"<a href=\"ExcelExportFormatAdapter.php.html#793\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatting<\/a>"],[0,3,"<a href=\"ExcelExportFormatAdapter.php.html#895\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::optimizeMemoryForExport<\/a>"],[0,4,"<a href=\"ExcelExportFormatAdapter.php.html#918\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertToBytes<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#941\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getSupportedExtensions<\/a>"],[0,4,"<a href=\"ExcelExportFormatAdapter.php.html#949\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"ExcelExportFormatAdapter.php.html#966\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#974\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"ExcelExportFormatAdapter.php.html#982\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"ExcelExportFormatAdapter.php.html#990\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatOptions<\/a>"],[0,10,"<a href=\"JsonExportFormatAdapter.php.html#18\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::export<\/a>"],[0,4,"<a href=\"JsonExportFormatAdapter.php.html#89\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getJsonOptions<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#126\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getSupportedExtensions<\/a>"],[0,1,"<a href=\"JsonExportFormatAdapter.php.html#134\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"JsonExportFormatAdapter.php.html#143\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#151\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"JsonExportFormatAdapter.php.html#159\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"JsonExportFormatAdapter.php.html#167\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatOptions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
