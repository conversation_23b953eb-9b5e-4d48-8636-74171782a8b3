<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/Nzoom-Hella/_libs/Nzoom/Export</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/nv.d3.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportService.php.html#13">Nzoom\Export\ExportService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#12">Nzoom\Export\Streamer\FileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#11">Nzoom\Export\Streamer\PointerFileStreamer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#25">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">6%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">29%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#25">Nzoom\Export\Adapter\ExcelExportFormatAdapter</a></td><td class="text-right">43397</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#12">Nzoom\Export\Adapter\AbstractExportFormatAdapter</a></td><td class="text-right">1217</td></tr>
       <tr><td><a href="ExportService.php.html#13">Nzoom\Export\ExportService</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#11">Nzoom\Export\Streamer\StreamHeaders</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#12">Nzoom\Export\Streamer\FileStreamer</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#11">Nzoom\Export\Streamer\GeneratorFileStreamer</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\JsonExportFormatAdapter</a></td><td class="text-right">368</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#11">Nzoom\Export\Streamer\PointerFileStreamer</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#13">Nzoom\Export\Adapter\CsvExportFormatAdapter</a></td><td class="text-right">245</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#107"><abbr title="Nzoom\Export\Streamer\FileStreamer::setCacheExpires">setCacheExpires</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#278"><abbr title="Nzoom\Export\Streamer\FileStreamer::getFilename">getFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#265"><abbr title="Nzoom\Export\Streamer\FileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#253"><abbr title="Nzoom\Export\Streamer\FileStreamer::increaseExecutionTime">increaseExecutionTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#243"><abbr title="Nzoom\Export\Streamer\FileStreamer::isClientConnected">isClientConnected</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#225"><abbr title="Nzoom\Export\Streamer\FileStreamer::outputChunk">outputChunk</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#175"><abbr title="Nzoom\Export\Streamer\FileStreamer::prepareForStreaming">prepareForStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#142"><abbr title="Nzoom\Export\Streamer\FileStreamer::stream">stream</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#129"><abbr title="Nzoom\Export\Streamer\FileStreamer::setLastModified">setLastModified</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#118"><abbr title="Nzoom\Export\Streamer\FileStreamer::setETag">setETag</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#96"><abbr title="Nzoom\Export\Streamer\FileStreamer::setTimeIncrement">setTimeIncrement</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#85"><abbr title="Nzoom\Export\Streamer\FileStreamer::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\FileStreamer::getHeaders">getHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#60"><abbr title="Nzoom\Export\Streamer\FileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#326"><abbr title="Nzoom\Export\ExportService::handleExportError">handleExportError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#296"><abbr title="Nzoom\Export\ExportService::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#284"><abbr title="Nzoom\Export\ExportService::isFormatSupported">isFormatSupported</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#273"><abbr title="Nzoom\Export\ExportService::getSupportedFormats">getSupportedFormats</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#259"><abbr title="Nzoom\Export\ExportService::getAdapter">getAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#247"><abbr title="Nzoom\Export\ExportService::setFormatFactory">setFormatFactory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#232"><abbr title="Nzoom\Export\ExportService::getFormatFactory">getFormatFactory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#288"><abbr title="Nzoom\Export\Streamer\FileStreamer::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#181"><abbr title="Nzoom\Export\ExportService::createTempStream">createTempStream</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#47"><abbr title="Nzoom\Export\Streamer\StreamHeaders::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#204"><abbr title="Nzoom\Export\Streamer\StreamHeaders::send304NotModified">send304NotModified</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#138"><abbr title="Nzoom\Export\Streamer\StreamHeaders::prepareCacheHeaders">prepareCacheHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#117"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sanitizeFilename">sanitizeFilename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#101"><abbr title="Nzoom\Export\Streamer\StreamHeaders::setFileContentHeaders">setFileContentHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#89"><abbr title="Nzoom\Export\Streamer\StreamHeaders::removeHeader">removeHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#78"><abbr title="Nzoom\Export\Streamer\StreamHeaders::hasHeader">hasHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#67"><abbr title="Nzoom\Export\Streamer\StreamHeaders::clear">clear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#57"><abbr title="Nzoom\Export\Streamer\StreamHeaders::getAll">getAll</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#36"><abbr title="Nzoom\Export\Streamer\StreamHeaders::setHeaders">setHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#25"><abbr title="Nzoom\Export\Streamer\StreamHeaders::addHeader">addHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#136"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#110"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::getChunkSize">getChunkSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#93"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#150"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::reset">reset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#140"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::getTotalSize">getTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#201"><abbr title="Nzoom\Export\ExportService::streamToBrowser">streamToBrowser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#156"><abbr title="Nzoom\Export\ExportService::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#392"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#367"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#318"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#302"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::styleHeaderRow">styleHeaderRow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#287"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#260"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportData">processExportData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#239"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#188"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#165"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#136"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createSpreadsheet">createSpreadsheet</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#54"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#481"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#120"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#93"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#23"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#181"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#532"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#142"><abbr title="Nzoom\Export\ExportService::createGeneratorFileStreamer">createGeneratorFileStreamer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#990"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#127"><abbr title="Nzoom\Export\ExportService::createExportData">createExportData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#110"><abbr title="Nzoom\Export\ExportService::createExportAction">createExportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#96"><abbr title="Nzoom\Export\ExportService::setModelFactoryName">setModelFactoryName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#84"><abbr title="Nzoom\Export\ExportService::setModelName">setModelName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportService.php.html#69"><abbr title="Nzoom\Export\ExportService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#167"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getFormatOptions">getFormatOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#143"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#134"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#18"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#966"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getDefaultExtension">getDefaultExtension</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#555"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomDateFormat">convertCustomDateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#949"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#918"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#895"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#793"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#763"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#742"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#722"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#665"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#628"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#604"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#586"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#223"><abbr title="Nzoom\Export\Streamer\StreamHeaders::handleCacheValidation">handleCacheValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#171"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isValidAdapter">isValidAdapter</abbr></a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#793"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatting">getExcelFormatting</abbr></a></td><td class="text-right">6972</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#392"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellValueWithFormatting">setCellValueWithFormatting</abbr></a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#481"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getExcelFormatFromExportValue">getExcelFormatFromExportValue</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#104"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::extractSizingOptions">extractSizingOptions</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#18"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::export">export</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#23"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::export">export</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#318"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportDataRecords">processExportDataRecords</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#68"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#665"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyRowHeightConstraints">applyRowHeightConstraints</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#223"><abbr title="Nzoom\Export\Streamer\StreamHeaders::handleCacheValidation">handleCacheValidation</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::performStreaming">performStreaming</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#628"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyColumnWidthConstraints">applyColumnWidthConstraints</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#453"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setCellDateValue">setCellDateValue</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#70"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#161"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::getRecordHeaders">getRecordHeaders</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#54"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::export">export</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#120"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::normalizeDelimiter">normalizeDelimiter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#231"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validatePhpStreamWrapper">validatePhpStreamWrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#188"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getApplicationLocale">getApplicationLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#763"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleSpreadsheetError">handleSpreadsheetError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#138"><abbr title="Nzoom\Export\Streamer\StreamHeaders::prepareCacheHeaders">prepareCacheHeaders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#127"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::handleExportError">handleExportError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#98"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportService.php.html#326"><abbr title="Nzoom\Export\ExportService::handleExportError">handleExportError</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ExportService.php.html#296"><abbr title="Nzoom\Export\ExportService::getExportFilename">getExportFilename</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/JsonExportFormatAdapter.php.html#89"><abbr title="Nzoom\Export\Adapter\JsonExportFormatAdapter::getJsonOptions">getJsonOptions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#949"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::getMimeType">getMimeType</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#918"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertToBytes">convertToBytes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#742"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::createWriter">createWriter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#165"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setSpreadsheetLocale">setSpreadsheetLocale</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#295"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePointer">validateFilePointer</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/CsvExportFormatAdapter.php.html#93"><abbr title="Nzoom\Export\Adapter\CsvExportFormatAdapter::getDelimiter">getDelimiter</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#273"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateFilePath">validateFilePath</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#895"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::optimizeMemoryForExport">optimizeMemoryForExport</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#100"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::sendHeaders">sendHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#193"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateAndPrepareSaveTarget">validateAndPrepareSaveTarget</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#168"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sendPreparedHeaders">sendPreparedHeaders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportService.php.html#156"><abbr title="Nzoom\Export\ExportService::export">export</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ExportService.php.html#201"><abbr title="Nzoom\Export\ExportService::streamToBrowser">streamToBrowser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#722"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::applyVerticalAlignment">applyVerticalAlignment</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#367"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::processExportRecord">processExportRecord</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#532"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::convertCustomNumberFormat">convertCustomNumberFormat</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#123"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::setTotalSize">setTotalSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#117"><abbr title="Nzoom\Export\Streamer\StreamHeaders::sanitizeFilename">sanitizeFilename</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#93"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/PointerFileStreamer.php.html#121"><abbr title="Nzoom\Export\Streamer\PointerFileStreamer::setChunkSize">setChunkSize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#36"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#287"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::addHeaders">addHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/StreamHeaders.php.html#189"><abbr title="Nzoom\Export\Streamer\StreamHeaders::flushHeaders">flushHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/GeneratorFileStreamer.php.html#56"><abbr title="Nzoom\Export\Streamer\GeneratorFileStreamer::initializeGenerator">initializeGenerator</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#175"><abbr title="Nzoom\Export\Streamer\FileStreamer::prepareForStreaming">prepareForStreaming</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#265"><abbr title="Nzoom\Export\Streamer\FileStreamer::cleanup">cleanup</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#253"><abbr title="Nzoom\Export\Streamer\FileStreamer::increaseExecutionTime">increaseExecutionTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#225"><abbr title="Nzoom\Export\Streamer\FileStreamer::outputChunk">outputChunk</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#200"><abbr title="Nzoom\Export\Streamer\FileStreamer::setStreamingOptimizationHeaders">setStreamingOptimizationHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#142"><abbr title="Nzoom\Export\Streamer\FileStreamer::stream">stream</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Streamer/FileStreamer.php.html#71"><abbr title="Nzoom\Export\Streamer\FileStreamer::getHeaders">getHeaders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#586"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::handleExportRecordCellError">handleExportRecordCellError</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#259"><abbr title="Nzoom\Export\ExportService::getAdapter">getAdapter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#239"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::setDocumentProperties">setDocumentProperties</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#181"><abbr title="Nzoom\Export\ExportService::createTempStream">createTempStream</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/ExcelExportFormatAdapter.php.html#604"><abbr title="Nzoom\Export\Adapter\ExcelExportFormatAdapter::finalizeExportDataColumns">finalizeExportDataColumns</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Adapter/AbstractExportFormatAdapter.php.html#213"><abbr title="Nzoom\Export\Adapter\AbstractExportFormatAdapter::validateStringTarget">validateStringTarget</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExportService.php.html#232"><abbr title="Nzoom\Export\ExportService::getFormatFactory">getFormatFactory</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Factory/ExportFormatFactory.php.html#171"><abbr title="Nzoom\Export\Factory\ExportFormatFactory::isValidAdapter">isValidAdapter</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 13 12:38:26 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script src="_js/nv.d3.min.js?v=9.2.32" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,3,0,1,0,0,0,0,0,0,5,3], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([106,0,0,0,0,0,0,1,1,4,5,129], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[6.521739130434782,38,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#12\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter<\/a>"],[29.310344827586203,25,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#13\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter<\/a>"],[0.6944444444444444,210,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#25\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter<\/a>"],[4.477611940298507,20,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#13\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter<\/a>"],[99.01960784313727,21,"<a href=\"DataFactory.php.html#16\">Nzoom\\Export\\DataFactory<\/a>"],[100,18,"<a href=\"Entity\/ExportColumn.php.html#10\">Nzoom\\Export\\Entity\\ExportColumn<\/a>"],[91.75257731958763,56,"<a href=\"Entity\/ExportData.php.html#12\">Nzoom\\Export\\Entity\\ExportData<\/a>"],[98.48484848484848,33,"<a href=\"Entity\/ExportHeader.php.html#11\">Nzoom\\Export\\Entity\\ExportHeader<\/a>"],[100,27,"<a href=\"Entity\/ExportRecord.php.html#11\">Nzoom\\Export\\Entity\\ExportRecord<\/a>"],[95.71428571428572,61,"<a href=\"Entity\/ExportValue.php.html#10\">Nzoom\\Export\\Entity\\ExportValue<\/a>"],[100,26,"<a href=\"ExportActionFactory.php.html#11\">Nzoom\\Export\\ExportActionFactory<\/a>"],[0,29,"<a href=\"ExportService.php.html#13\">Nzoom\\Export\\ExportService<\/a>"],[96.36363636363636,25,"<a href=\"Factory\/ExportFormatFactory.php.html#13\">Nzoom\\Export\\Factory\\ExportFormatFactory<\/a>"],[0,23,"<a href=\"Streamer\/FileStreamer.php.html#12\">Nzoom\\Export\\Streamer\\FileStreamer<\/a>"],[0,20,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer<\/a>"],[0,18,"<a href=\"Streamer\/PointerFileStreamer.php.html#11\">Nzoom\\Export\\Streamer\\PointerFileStreamer<\/a>"],[0,27,"<a href=\"Streamer\/StreamHeaders.php.html#11\">Nzoom\\Export\\Streamer\\StreamHeaders<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[80,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#43\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::__construct<\/a>"],[100,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#57\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::setConfiguration<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#70\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getExportFilename<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#100\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::sendHeaders<\/a>"],[0,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#127\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::handleExportError<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getRecordHeaders<\/a>"],[0,1,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#181\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::getFormatOptions<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#193\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateAndPrepareSaveTarget<\/a>"],[0,2,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#213\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateStringTarget<\/a>"],[0,5,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#231\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validatePhpStreamWrapper<\/a>"],[0,3,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#273\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePath<\/a>"],[0,4,"<a href=\"Adapter\/AbstractExportFormatAdapter.php.html#295\">Nzoom\\Export\\Adapter\\AbstractExportFormatAdapter::validateFilePointer<\/a>"],[0,10,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#23\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::export<\/a>"],[0,4,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#93\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDelimiter<\/a>"],[0,5,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#120\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::normalizeDelimiter<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#136\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getSupportedExtensions<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#144\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getMimeType<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#153\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#161\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#169\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatName<\/a>"],[100,1,"<a href=\"Adapter\/CsvExportFormatAdapter.php.html#177\">Nzoom\\Export\\Adapter\\CsvExportFormatAdapter::getFormatOptions<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#54\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::export<\/a>"],[0,11,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#104\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::extractSizingOptions<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#136\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createSpreadsheet<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#165\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setSpreadsheetLocale<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#188\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getApplicationLocale<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#239\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setDocumentProperties<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#260\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportData<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#287\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::addHeaders<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#302\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::styleHeaderRow<\/a>"],[0,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#318\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportDataRecords<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#367\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::processExportRecord<\/a>"],[0,17,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#392\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellValueWithFormatting<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#453\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::setCellDateValue<\/a>"],[0,15,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#481\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatFromExportValue<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#532\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomNumberFormat<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#555\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertCustomDateFormat<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#586\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleExportRecordCellError<\/a>"],[0,2,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#604\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::finalizeExportDataColumns<\/a>"],[0,5,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#628\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyColumnWidthConstraints<\/a>"],[0,8,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#665\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyRowHeightConstraints<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#722\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::applyVerticalAlignment<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#742\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::createWriter<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#763\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::handleSpreadsheetError<\/a>"],[0,83,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#793\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getExcelFormatting<\/a>"],[0,3,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#895\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::optimizeMemoryForExport<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#918\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::convertToBytes<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#941\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getSupportedExtensions<\/a>"],[0,4,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#949\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#966\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#974\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#982\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/ExcelExportFormatAdapter.php.html#990\">Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter::getFormatOptions<\/a>"],[0,10,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#18\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::export<\/a>"],[0,4,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#89\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getJsonOptions<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#126\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getSupportedExtensions<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#134\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getMimeType<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#143\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getDefaultExtension<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#151\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::supportsFormat<\/a>"],[100,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#159\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatName<\/a>"],[0,1,"<a href=\"Adapter\/JsonExportFormatAdapter.php.html#167\">Nzoom\\Export\\Adapter\\JsonExportFormatAdapter::getFormatOptions<\/a>"],[100,1,"<a href=\"DataFactory.php.html#33\">Nzoom\\Export\\DataFactory::__construct<\/a>"],[100,2,"<a href=\"DataFactory.php.html#45\">Nzoom\\Export\\DataFactory::__invoke<\/a>"],[100,4,"<a href=\"DataFactory.php.html#71\">Nzoom\\Export\\DataFactory::createHeaderFromOutlook<\/a>"],[100,2,"<a href=\"DataFactory.php.html#101\">Nzoom\\Export\\DataFactory::processModelsChunk<\/a>"],[100,4,"<a href=\"DataFactory.php.html#115\">Nzoom\\Export\\DataFactory::createRecordFromModel<\/a>"],[100,1,"<a href=\"DataFactory.php.html#149\">Nzoom\\Export\\DataFactory::mapFieldTypeToExportType<\/a>"],[100,1,"<a href=\"DataFactory.php.html#178\">Nzoom\\Export\\DataFactory::setChunkSize<\/a>"],[100,2,"<a href=\"DataFactory.php.html#192\">Nzoom\\Export\\DataFactory::createStreaming<\/a>"],[96.15384615384616,4,"<a href=\"DataFactory.php.html#239\">Nzoom\\Export\\DataFactory::createCursorStreaming<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#53\">Nzoom\\Export\\Entity\\ExportColumn::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#74\">Nzoom\\Export\\Entity\\ExportColumn::getVarName<\/a>"],[100,2,"<a href=\"Entity\/ExportColumn.php.html#86\">Nzoom\\Export\\Entity\\ExportColumn::setVarName<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#100\">Nzoom\\Export\\Entity\\ExportColumn::getLabel<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#110\">Nzoom\\Export\\Entity\\ExportColumn::setLabel<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#120\">Nzoom\\Export\\Entity\\ExportColumn::getType<\/a>"],[100,2,"<a href=\"Entity\/ExportColumn.php.html#131\">Nzoom\\Export\\Entity\\ExportColumn::setType<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#150\">Nzoom\\Export\\Entity\\ExportColumn::getFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#161\">Nzoom\\Export\\Entity\\ExportColumn::setFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#171\">Nzoom\\Export\\Entity\\ExportColumn::getWidth<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#181\">Nzoom\\Export\\Entity\\ExportColumn::setWidth<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#191\">Nzoom\\Export\\Entity\\ExportColumn::getStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#201\">Nzoom\\Export\\Entity\\ExportColumn::setStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#212\">Nzoom\\Export\\Entity\\ExportColumn::addStyle<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#223\">Nzoom\\Export\\Entity\\ExportColumn::validateValue<\/a>"],[100,1,"<a href=\"Entity\/ExportColumn.php.html#235\">Nzoom\\Export\\Entity\\ExportColumn::createValue<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#50\">Nzoom\\Export\\Entity\\ExportData::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#64\">Nzoom\\Export\\Entity\\ExportData::setRecordProvider<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#81\">Nzoom\\Export\\Entity\\ExportData::isLazy<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#91\">Nzoom\\Export\\Entity\\ExportData::getPageSize<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#102\">Nzoom\\Export\\Entity\\ExportData::setPageSize<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#113\">Nzoom\\Export\\Entity\\ExportData::getHeader<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#123\">Nzoom\\Export\\Entity\\ExportData::setHeader<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#133\">Nzoom\\Export\\Entity\\ExportData::getMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#143\">Nzoom\\Export\\Entity\\ExportData::setMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#155\">Nzoom\\Export\\Entity\\ExportData::getMetadataValue<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#166\">Nzoom\\Export\\Entity\\ExportData::setMetadataValue<\/a>"],[80,4,"<a href=\"Entity\/ExportData.php.html#180\">Nzoom\\Export\\Entity\\ExportData::addRecord<\/a>"],[100,3,"<a href=\"Entity\/ExportData.php.html#199\">Nzoom\\Export\\Entity\\ExportData::getRecords<\/a>"],[100,4,"<a href=\"Entity\/ExportData.php.html#220\">Nzoom\\Export\\Entity\\ExportData::getRecordAt<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#242\">Nzoom\\Export\\Entity\\ExportData::count<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#259\">Nzoom\\Export\\Entity\\ExportData::createRecord<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#275\">Nzoom\\Export\\Entity\\ExportData::isEmpty<\/a>"],[80.76923076923077,15,"<a href=\"Entity\/ExportData.php.html#291\">Nzoom\\Export\\Entity\\ExportData::sortByColumn<\/a>"],[100,1,"<a href=\"Entity\/ExportData.php.html#339\">Nzoom\\Export\\Entity\\ExportData::filter<\/a>"],[75,3,"<a href=\"Entity\/ExportData.php.html#350\">Nzoom\\Export\\Entity\\ExportData::validate<\/a>"],[100,3,"<a href=\"Entity\/ExportData.php.html#368\">Nzoom\\Export\\Entity\\ExportData::toArray<\/a>"],[100,2,"<a href=\"Entity\/ExportData.php.html#388\">Nzoom\\Export\\Entity\\ExportData::getIterator<\/a>"],[90,4,"<a href=\"Entity\/ExportData.php.html#402\">Nzoom\\Export\\Entity\\ExportData::getLazyIterator<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#44\">Nzoom\\Export\\Entity\\ExportHeader::__construct<\/a>"],[100,2,"<a href=\"Entity\/ExportHeader.php.html#57\">Nzoom\\Export\\Entity\\ExportHeader::addColumn<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#79\">Nzoom\\Export\\Entity\\ExportHeader::hasColumn<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#89\">Nzoom\\Export\\Entity\\ExportHeader::getColumns<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#100\">Nzoom\\Export\\Entity\\ExportHeader::getColumnAt<\/a>"],[100,2,"<a href=\"Entity\/ExportHeader.php.html#111\">Nzoom\\Export\\Entity\\ExportHeader::getColumnByVarName<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#125\">Nzoom\\Export\\Entity\\ExportHeader::getBackgroundColor<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#135\">Nzoom\\Export\\Entity\\ExportHeader::setBackgroundColor<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#145\">Nzoom\\Export\\Entity\\ExportHeader::getStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#155\">Nzoom\\Export\\Entity\\ExportHeader::setStyles<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#166\">Nzoom\\Export\\Entity\\ExportHeader::addStyle<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#176\">Nzoom\\Export\\Entity\\ExportHeader::count<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#186\">Nzoom\\Export\\Entity\\ExportHeader::getLabels<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#198\">Nzoom\\Export\\Entity\\ExportHeader::getVarNames<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#210\">Nzoom\\Export\\Entity\\ExportHeader::getTypes<\/a>"],[100,6,"<a href=\"Entity\/ExportHeader.php.html#223\">Nzoom\\Export\\Entity\\ExportHeader::reorderColumns<\/a>"],[88.88888888888889,5,"<a href=\"Entity\/ExportHeader.php.html#264\">Nzoom\\Export\\Entity\\ExportHeader::validateRecord<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#291\">Nzoom\\Export\\Entity\\ExportHeader::rewind<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#301\">Nzoom\\Export\\Entity\\ExportHeader::current<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#311\">Nzoom\\Export\\Entity\\ExportHeader::key<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#319\">Nzoom\\Export\\Entity\\ExportHeader::next<\/a>"],[100,1,"<a href=\"Entity\/ExportHeader.php.html#329\">Nzoom\\Export\\Entity\\ExportHeader::valid<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#38\">Nzoom\\Export\\Entity\\ExportRecord::__construct<\/a>"],[100,2,"<a href=\"Entity\/ExportRecord.php.html#52\">Nzoom\\Export\\Entity\\ExportRecord::addValue<\/a>"],[100,4,"<a href=\"Entity\/ExportRecord.php.html#71\">Nzoom\\Export\\Entity\\ExportRecord::setValueAt<\/a>"],[100,2,"<a href=\"Entity\/ExportRecord.php.html#98\">Nzoom\\Export\\Entity\\ExportRecord::setValueByColumnName<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#116\">Nzoom\\Export\\Entity\\ExportRecord::getValues<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#127\">Nzoom\\Export\\Entity\\ExportRecord::getValueAt<\/a>"],[100,2,"<a href=\"Entity\/ExportRecord.php.html#138\">Nzoom\\Export\\Entity\\ExportRecord::getValueByColumnName<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#153\">Nzoom\\Export\\Entity\\ExportRecord::hasValue<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#163\">Nzoom\\Export\\Entity\\ExportRecord::getRawValues<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#175\">Nzoom\\Export\\Entity\\ExportRecord::getFormattedValues<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#187\">Nzoom\\Export\\Entity\\ExportRecord::getMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#197\">Nzoom\\Export\\Entity\\ExportRecord::setMetadata<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#209\">Nzoom\\Export\\Entity\\ExportRecord::getMetadataValue<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#220\">Nzoom\\Export\\Entity\\ExportRecord::setMetadataValue<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#231\">Nzoom\\Export\\Entity\\ExportRecord::validate<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#241\">Nzoom\\Export\\Entity\\ExportRecord::count<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#251\">Nzoom\\Export\\Entity\\ExportRecord::rewind<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#261\">Nzoom\\Export\\Entity\\ExportRecord::current<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#271\">Nzoom\\Export\\Entity\\ExportRecord::key<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#279\">Nzoom\\Export\\Entity\\ExportRecord::next<\/a>"],[100,1,"<a href=\"Entity\/ExportRecord.php.html#289\">Nzoom\\Export\\Entity\\ExportRecord::valid<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#58\">Nzoom\\Export\\Entity\\ExportValue::getValidTypes<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#70\">Nzoom\\Export\\Entity\\ExportValue::__construct<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#82\">Nzoom\\Export\\Entity\\ExportValue::getValue<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#92\">Nzoom\\Export\\Entity\\ExportValue::setValue<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#102\">Nzoom\\Export\\Entity\\ExportValue::getType<\/a>"],[100,3,"<a href=\"Entity\/ExportValue.php.html#113\">Nzoom\\Export\\Entity\\ExportValue::setType<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#131\">Nzoom\\Export\\Entity\\ExportValue::getFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#142\">Nzoom\\Export\\Entity\\ExportValue::setFormat<\/a>"],[100,1,"<a href=\"Entity\/ExportValue.php.html#153\">Nzoom\\Export\\Entity\\ExportValue::isNull<\/a>"],[95,27,"<a href=\"Entity\/ExportValue.php.html#163\">Nzoom\\Export\\Entity\\ExportValue::validate<\/a>"],[90,15,"<a href=\"Entity\/ExportValue.php.html#196\">Nzoom\\Export\\Entity\\ExportValue::getFormattedValue<\/a>"],[100,8,"<a href=\"Entity\/ExportValue.php.html#233\">Nzoom\\Export\\Entity\\ExportValue::__toString<\/a>"],[100,3,"<a href=\"ExportActionFactory.php.html#55\">Nzoom\\Export\\ExportActionFactory::__construct<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#85\">Nzoom\\Export\\ExportActionFactory::setModelName<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#97\">Nzoom\\Export\\ExportActionFactory::setModelFactoryName<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#110\">Nzoom\\Export\\ExportActionFactory::__invoke<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#122\">Nzoom\\Export\\ExportActionFactory::createExportAction<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#141\">Nzoom\\Export\\ExportActionFactory::prepareExportOptions<\/a>"],[100,3,"<a href=\"ExportActionFactory.php.html#177\">Nzoom\\Export\\ExportActionFactory::initializeFilterVisibility<\/a>"],[100,2,"<a href=\"ExportActionFactory.php.html#194\">Nzoom\\Export\\ExportActionFactory::firstOrZero<\/a>"],[100,4,"<a href=\"ExportActionFactory.php.html#209\">Nzoom\\Export\\ExportActionFactory::getPluginOptions<\/a>"],[100,4,"<a href=\"ExportActionFactory.php.html#280\">Nzoom\\Export\\ExportActionFactory::buildBaseExportOptions<\/a>"],[100,2,"<a href=\"ExportActionFactory.php.html#329\">Nzoom\\Export\\ExportActionFactory::getFormatOptions<\/a>"],[100,1,"<a href=\"ExportActionFactory.php.html#364\">Nzoom\\Export\\ExportActionFactory::getGroupTablesOptions<\/a>"],[100,2,"<a href=\"ExportActionFactory.php.html#397\">Nzoom\\Export\\ExportActionFactory::getDelimiterOptions<\/a>"],[0,1,"<a href=\"ExportService.php.html#69\">Nzoom\\Export\\ExportService::__construct<\/a>"],[0,1,"<a href=\"ExportService.php.html#84\">Nzoom\\Export\\ExportService::setModelName<\/a>"],[0,1,"<a href=\"ExportService.php.html#96\">Nzoom\\Export\\ExportService::setModelFactoryName<\/a>"],[0,1,"<a href=\"ExportService.php.html#110\">Nzoom\\Export\\ExportService::createExportAction<\/a>"],[0,1,"<a href=\"ExportService.php.html#127\">Nzoom\\Export\\ExportService::createExportData<\/a>"],[0,1,"<a href=\"ExportService.php.html#142\">Nzoom\\Export\\ExportService::createGeneratorFileStreamer<\/a>"],[0,3,"<a href=\"ExportService.php.html#156\">Nzoom\\Export\\ExportService::export<\/a>"],[0,2,"<a href=\"ExportService.php.html#181\">Nzoom\\Export\\ExportService::createTempStream<\/a>"],[0,3,"<a href=\"ExportService.php.html#201\">Nzoom\\Export\\ExportService::streamToBrowser<\/a>"],[0,2,"<a href=\"ExportService.php.html#232\">Nzoom\\Export\\ExportService::getFormatFactory<\/a>"],[0,1,"<a href=\"ExportService.php.html#247\">Nzoom\\Export\\ExportService::setFormatFactory<\/a>"],[0,2,"<a href=\"ExportService.php.html#259\">Nzoom\\Export\\ExportService::getAdapter<\/a>"],[0,1,"<a href=\"ExportService.php.html#273\">Nzoom\\Export\\ExportService::getSupportedFormats<\/a>"],[0,1,"<a href=\"ExportService.php.html#284\">Nzoom\\Export\\ExportService::isFormatSupported<\/a>"],[0,4,"<a href=\"ExportService.php.html#296\">Nzoom\\Export\\ExportService::getExportFilename<\/a>"],[0,4,"<a href=\"ExportService.php.html#326\">Nzoom\\Export\\ExportService::handleExportError<\/a>"],[100,1,"<a href=\"Factory\/ExportFormatFactory.php.html#44\">Nzoom\\Export\\Factory\\ExportFormatFactory::__construct<\/a>"],[100,3,"<a href=\"Factory\/ExportFormatFactory.php.html#60\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapter<\/a>"],[100,3,"<a href=\"Factory\/ExportFormatFactory.php.html#94\">Nzoom\\Export\\Factory\\ExportFormatFactory::getAdapterClass<\/a>"],[91.66666666666666,6,"<a href=\"Factory\/ExportFormatFactory.php.html#112\">Nzoom\\Export\\Factory\\ExportFormatFactory::discoverAdapters<\/a>"],[100,4,"<a href=\"Factory\/ExportFormatFactory.php.html#148\">Nzoom\\Export\\Factory\\ExportFormatFactory::getClassNameFromFile<\/a>"],[66.66666666666666,2,"<a href=\"Factory\/ExportFormatFactory.php.html#171\">Nzoom\\Export\\Factory\\ExportFormatFactory::isValidAdapter<\/a>"],[100,3,"<a href=\"Factory\/ExportFormatFactory.php.html#186\">Nzoom\\Export\\Factory\\ExportFormatFactory::getSupportedFormats<\/a>"],[100,1,"<a href=\"Factory\/ExportFormatFactory.php.html#213\">Nzoom\\Export\\Factory\\ExportFormatFactory::isFormatSupported<\/a>"],[100,2,"<a href=\"Factory\/ExportFormatFactory.php.html#227\">Nzoom\\Export\\Factory\\ExportFormatFactory::createAdapterFromFilename<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#60\">Nzoom\\Export\\Streamer\\FileStreamer::__construct<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\FileStreamer::getHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#85\">Nzoom\\Export\\Streamer\\FileStreamer::setHeaders<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#96\">Nzoom\\Export\\Streamer\\FileStreamer::setTimeIncrement<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#107\">Nzoom\\Export\\Streamer\\FileStreamer::setCacheExpires<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#118\">Nzoom\\Export\\Streamer\\FileStreamer::setETag<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#129\">Nzoom\\Export\\Streamer\\FileStreamer::setLastModified<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#142\">Nzoom\\Export\\Streamer\\FileStreamer::stream<\/a>"],[100,0,"<a href=\"Streamer\/FileStreamer.php.html#168\">Nzoom\\Export\\Streamer\\FileStreamer::performStreaming<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#175\">Nzoom\\Export\\Streamer\\FileStreamer::prepareForStreaming<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#200\">Nzoom\\Export\\Streamer\\FileStreamer::setStreamingOptimizationHeaders<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#225\">Nzoom\\Export\\Streamer\\FileStreamer::outputChunk<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#243\">Nzoom\\Export\\Streamer\\FileStreamer::isClientConnected<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#253\">Nzoom\\Export\\Streamer\\FileStreamer::increaseExecutionTime<\/a>"],[0,2,"<a href=\"Streamer\/FileStreamer.php.html#265\">Nzoom\\Export\\Streamer\\FileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#278\">Nzoom\\Export\\Streamer\\FileStreamer::getFilename<\/a>"],[0,1,"<a href=\"Streamer\/FileStreamer.php.html#288\">Nzoom\\Export\\Streamer\\FileStreamer::getMimeType<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::__construct<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#56\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::initializeGenerator<\/a>"],[0,8,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#68\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::performStreaming<\/a>"],[0,4,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#98\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::cleanup<\/a>"],[0,2,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#123\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::setTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#140\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::getTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/GeneratorFileStreamer.php.html#150\">Nzoom\\Export\\Streamer\\GeneratorFileStreamer::reset<\/a>"],[0,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#36\">Nzoom\\Export\\Streamer\\PointerFileStreamer::__construct<\/a>"],[0,6,"<a href=\"Streamer\/PointerFileStreamer.php.html#71\">Nzoom\\Export\\Streamer\\PointerFileStreamer::performStreaming<\/a>"],[0,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#93\">Nzoom\\Export\\Streamer\\PointerFileStreamer::cleanup<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#110\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getChunkSize<\/a>"],[0,2,"<a href=\"Streamer\/PointerFileStreamer.php.html#121\">Nzoom\\Export\\Streamer\\PointerFileStreamer::setChunkSize<\/a>"],[0,1,"<a href=\"Streamer\/PointerFileStreamer.php.html#136\">Nzoom\\Export\\Streamer\\PointerFileStreamer::getTotalSize<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#25\">Nzoom\\Export\\Streamer\\StreamHeaders::addHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#36\">Nzoom\\Export\\Streamer\\StreamHeaders::setHeaders<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#47\">Nzoom\\Export\\Streamer\\StreamHeaders::getHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#57\">Nzoom\\Export\\Streamer\\StreamHeaders::getAll<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#67\">Nzoom\\Export\\Streamer\\StreamHeaders::clear<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#78\">Nzoom\\Export\\Streamer\\StreamHeaders::hasHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#89\">Nzoom\\Export\\Streamer\\StreamHeaders::removeHeader<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#101\">Nzoom\\Export\\Streamer\\StreamHeaders::setFileContentHeaders<\/a>"],[0,2,"<a href=\"Streamer\/StreamHeaders.php.html#117\">Nzoom\\Export\\Streamer\\StreamHeaders::sanitizeFilename<\/a>"],[0,4,"<a href=\"Streamer\/StreamHeaders.php.html#138\">Nzoom\\Export\\Streamer\\StreamHeaders::prepareCacheHeaders<\/a>"],[0,3,"<a href=\"Streamer\/StreamHeaders.php.html#168\">Nzoom\\Export\\Streamer\\StreamHeaders::sendPreparedHeaders<\/a>"],[0,2,"<a href=\"Streamer\/StreamHeaders.php.html#189\">Nzoom\\Export\\Streamer\\StreamHeaders::flushHeaders<\/a>"],[0,1,"<a href=\"Streamer\/StreamHeaders.php.html#204\">Nzoom\\Export\\Streamer\\StreamHeaders::send304NotModified<\/a>"],[0,7,"<a href=\"Streamer\/StreamHeaders.php.html#223\">Nzoom\\Export\\Streamer\\StreamHeaders::handleCacheValidation<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
