<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Entity</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">Entity</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="39.03" aria-valuemin="0" aria-valuemax="100" style="width: 39.03%">
           <span class="sr-only">39.03% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">39.03%</div></td>
       <td class="warning small"><div align="right">121&nbsp;/&nbsp;310</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="40.43" aria-valuemin="0" aria-valuemax="100" style="width: 40.43%">
           <span class="sr-only">40.43% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">40.43%</div></td>
       <td class="warning small"><div align="right">38&nbsp;/&nbsp;94</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;5</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportColumn.php.html">ExportColumn.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="70.00" aria-valuemin="0" aria-valuemax="100" style="width: 70.00%">
           <span class="sr-only">70.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">70.00%</div></td>
       <td class="success small"><div align="right">21&nbsp;/&nbsp;30</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="68.75" aria-valuemin="0" aria-valuemax="100" style="width: 68.75%">
           <span class="sr-only">68.75% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">68.75%</div></td>
       <td class="warning small"><div align="right">11&nbsp;/&nbsp;16</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportData.php.html">ExportData.php</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="37.11" aria-valuemin="0" aria-valuemax="100" style="width: 37.11%">
           <span class="sr-only">37.11% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">37.11%</div></td>
       <td class="warning small"><div align="right">36&nbsp;/&nbsp;97</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="26.09" aria-valuemin="0" aria-valuemax="100" style="width: 26.09%">
           <span class="sr-only">26.09% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">26.09%</div></td>
       <td class="danger small"><div align="right">6&nbsp;/&nbsp;23</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportHeader.php.html">ExportHeader.php</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="39.39" aria-valuemin="0" aria-valuemax="100" style="width: 39.39%">
           <span class="sr-only">39.39% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">39.39%</div></td>
       <td class="warning small"><div align="right">26&nbsp;/&nbsp;66</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="45.45" aria-valuemin="0" aria-valuemax="100" style="width: 45.45%">
           <span class="sr-only">45.45% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">45.45%</div></td>
       <td class="warning small"><div align="right">10&nbsp;/&nbsp;22</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportRecord.php.html">ExportRecord.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="27.66" aria-valuemin="0" aria-valuemax="100" style="width: 27.66%">
           <span class="sr-only">27.66% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">27.66%</div></td>
       <td class="danger small"><div align="right">13&nbsp;/&nbsp;47</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="28.57" aria-valuemin="0" aria-valuemax="100" style="width: 28.57%">
           <span class="sr-only">28.57% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">28.57%</div></td>
       <td class="danger small"><div align="right">6&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportValue.php.html">ExportValue.php</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="35.71" aria-valuemin="0" aria-valuemax="100" style="width: 35.71%">
           <span class="sr-only">35.71% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">35.71%</div></td>
       <td class="warning small"><div align="right">25&nbsp;/&nbsp;70</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="41.67" aria-valuemin="0" aria-valuemax="100" style="width: 41.67%">
           <span class="sr-only">41.67% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">41.67%</div></td>
       <td class="warning small"><div align="right">5&nbsp;/&nbsp;12</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 35%</span>
     <span class="warning"><strong>Medium</strong>: 35% to 70%</span>
     <span class="success"><strong>High</strong>: 70% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Fri Jun 13 12:22:20 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
