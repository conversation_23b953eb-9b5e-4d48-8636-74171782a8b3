<form onsubmit="return false;">
  <input type="hidden" name="step" value="info" />
  <input type="hidden" name="document_id" value="{$document_id}" />
  <input type="hidden" name="event_id" value="{$event_id}" />
  <input type="hidden" name="reminder_type" value="email" />
  <input type="hidden" name="old_reminder_user_id" value="{$reminder_user_id}" />
  <table border="0">
    <tr>
      <td class="next_step_title">{#plugin_wienerberger_meeting_protocol_meeting_next_step#}: {#plugin_wienerberger_meeting_protocol_meeting_next_step_info#|escape}</td>
    </tr>
    <tr>
      <td><span class="required">*</span>{#plugin_wienerberger_meeting_protocol_meeting_next_step_info_employee#}:</td>
    </tr>
    <tr>
      <td>
        {include file=input_autocompleter.html
          name='reminder_user_id'
          autocomplete_var_type='basic'
          autocomplete=$reminder_user_id_autocomplete
          standalone=true
          width=250
          value=$reminder_user_id
          value_autocomplete=$reminder_user_name
        }
      </td>
    </tr>
    <tr>
      <td><span class="required">*</span>{#plugin_wienerberger_meeting_protocol_meeting_next_step_info_date#}:</td>
    </tr>
    <tr>
      <td>
        {include file="input_datetime.html"
          name='reminder_date'
          standalone=true
          width=100
          value=$reminder_date
        }
      </td>
    </tr>
    <tr>
      <td><span class="required">*</span>{#plugin_wienerberger_meeting_protocol_meeting_next_step_info_description#}:</td>
    </tr>
    <tr>
      <td>
        {include file=input_textarea.html
          name='custom_message'
          standalone=true
          value=$custom_message
        }
      </td>
    </tr>
    <tr>
      <td>
        <button type="submit" class="button" onclick="dashletWienerbergerMeetingProtocol.addNextStep(this)">
          {if $event_id}
            {#plugin_wienerberger_meeting_protocol_meeting_button_edit#}
          {else}
            {#plugin_wienerberger_meeting_protocol_meeting_button_add#}
          {/if}
        </button>
        <button type="button" class="button" onclick="dashletWienerbergerMeetingProtocol.cancelNextStep()">{#plugin_wienerberger_meeting_protocol_meeting_button_cancel#}</button>
      </td>
    </tr>
  </table>
</form>