<?php

class Custom_View_Viewer extends Viewer {

    public function __construct(&$registry, $is_main) {
        $this->template = 'view.html';

        // plugin name matches plugin folder name
        $pn = explode(DIRECTORY_SEPARATOR, realpath(dirname(__FILE__)));
        $this->pluginName = $pn[count($pn)-2];

        parent::__construct($registry, $is_main);
    }

    /**
     * Sets paths within the plugin
     */
    public function setPaths() {
        $this->pluginDir        = PH_MODULES_DIR . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->pluginUrl        = PH_MODULES_URL . 'dashlets/plugins/' . $this->pluginName . '/';
        $this->templatesDir     = $this->pluginDir . 'templates/';

        $this->modelsDir        = PH_MODULES_DIR . $this->module . '/models/';
        $this->viewersDir       = PH_MODULES_DIR . $this->module . '/viewers/';
        $this->controllersDir   = PH_MODULES_DIR . $this->module . '/controllers/';

        $this->i18nDir          = $this->pluginDir . 'i18n/' . $this->registry['lang'] . '/';

        $this->scriptsDir       = $this->pluginDir . 'javascript/';
        $this->scriptsUrl       = $this->pluginUrl . 'javascript/';

        return true;
    }

    public function prepare() {

        $this->prepareTitleBar();

        $registry = &$this->registry;

        $this->loadCustomI18NFiles(FilesLib::readDir(PH_MODULES_DIR . $this->module . '/i18n/' . $this->registry['lang'] . '/', false, '', '', true));

        //get the model
        $this->model = $this->registry->get('dashlet');
        if ($this->model->get('filters')) {
            foreach ($this->model->get('filters') as $k => $v) {
                $this->model->set($k, $v, true);
            }
            $this->model->unsetProperty('filters', true);
        }

        //prepare group
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $group = Groups::searchOne($this->registry, array('where' => array('g.id = ' . $this->model->get('group')),
                                                          'sanitize' => true));
        if ($group) {
            $this->data['group'] = $group->get('name');
        } else {
            $this->data['group'] = 0;
        }

        $this->data['projects_types'] = Dropdown::getProjectsTypes(array($this->registry));
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('dashlets_view');
    }
}

?>
