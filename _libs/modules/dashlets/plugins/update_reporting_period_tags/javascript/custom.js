/*
 * Function to search reports documents by selected customer
 */
searchFinishedReports = function () {
    Effect.Center('loading');
    Effect.Appear('loading');

    var form = $('quick_tag_reports');
    if (!form) {
        Effect.Fade('loading');
        return false;
    }

    var get_params = Form.serialize(form);

    var opt = {
        method: 'get',
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var ajax_result = t.responseText;
            var container = $('result_search_table');

            if (!ajax_result) {
                container.innerHTML = '';
            } else {
                container.innerHTML = ajax_result;
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    var url = form.action;

    new Ajax.Request(url, opt);
};

/*
 * Function to clear the tags of a selected report document
 * @param doc_id - the ID of the document which tags has to be cleared
 * @param dashlet_plugin - the name of the dashlet plugin
 * @param dashlet_id - the ID of the dashlet plugin
 */
clearReportTags = function (doc_id, dashlet_plugin, dashlet_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&custom_plugin_action=clear_report_tags&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_id + '&document_id=' + doc_id;

    var opt = {
        method: 'get',
        asynchronous: true,
        onComplete: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            searchFinishedReports();
            // effect has already faded in searchFinishedReports function,
            // do not call it again, this causes a short blinking on screen
            //Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
};

/*
 * Function to clear the tags of a selected report document
 * @param doc_id - the ID of the document whose tags have to be changed
 * @param dashlet_plugin - the name of the dashlet plugin
 * @param dashlet_id - the ID of the dashlet plugin
 * @param load_from_dashlet - flag to show if the function is triggered from lightbox
 */
setMonthTags = function (doc_id, dashlet_plugin, dashlet_id, load_from_dashlet) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&custom_plugin_action=set_report_document_tags&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_id + '&document_id=' + doc_id;

    if (load_from_dashlet) {
        // get the radio buttons to define which one is checked
        var tag_selected = '';
        var radio_buttons = $$('input[name="tags"]');
        for (var j = 0; j < radio_buttons.length; j++) {
            if (radio_buttons[j].checked) {
                tag_selected = radio_buttons[j].value;
            }
        }
        url += '&tag_for_month=' + tag_selected;
    } else {
        url += '&tag_for_month=current';
    }

    var opt = {
        method: 'get',
        asynchronous: true,
        onComplete: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            searchFinishedReports();
            if (load_from_dashlet) {
                lb.deactivate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
};

/*
 * Function to load tags options
 * @param doc_id - the ID of the document which tags has to be changed
 * @param dashlet_plugin - the name of the dashlet plugin
 * @param dashlet_id - the ID of the dashlet plugin
 */
loadTagsOptions = function (doc_id, dashlet_plugin, dashlet_id, load_from_dashlet) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&custom_plugin_action=prepare_lightbox_template&plugin=' + dashlet_plugin + '&dashlet=' + dashlet_id + '&document_id=' + doc_id;

    var opt = {
        method: 'get',
        asynchronous: true,
        onComplete: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            lb = new lightbox({content: result['template'], title: result['title'], width: '420px', height: '110px'});
            lb.activate();
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
};
