{* Look the link bellow to know why DEFER is used *}
{* http://msdn.microsoft.com/en-us/library/ms533897.aspx *}
{assign var='custom_filters' value=$dashlet->get('filters')}
<form method="post" id="quick_tag_reports" onsubmit="return false" name="quick_tag_reports" action="{$smarty.server.PHP_SELF}?{$module_param}=dashlets&amp;dashlets=custom_action&amp;custom_plugin_action=search_finished_reports&amp;plugin={$dashlet->get('controller')}&amp;dashlet={$dashlet->get('id')}">
  <script defer="defer" src="{$scripts_url}?{$system_options.build}"></script>
  <table border="0" cellpadding="5" cellspacing="0" style="width: 100%; margin-top: 5px;">
    <tr>
      <td class="labelbox">{#plugin_change_tags_search_customer#}:</td>
      <td>&nbsp;</td>
      <td>
        {include file=`$theme->templatesDir`input_autocompleter.html
            name='customer_find'
            width='250'
            autocomplete_type='customers'
            autocomplete_var_type='basic'
            autocomplete_buttons_hide='search'
            autocomplete_buttons='clear'
            standalone=true}
      </td>
    </tr>
    <tr>
      <td class="labelbox">{#plugin_change_tags_user_bank#}:</td>
      <td>&nbsp;</td>
      <td>
        {include file=`$theme->templatesDir`input_autocompleter.html
            name='user_bank'
            width='250'
            autocomplete=$filters.user_bank_autocomplete
            autocomplete_var_type='basic'
            standalone=true}
      </td>
    </tr>
    <tr>
      <td class="labelbox">{#plugin_change_tags_status#}:</td>
      <td>&nbsp;</td>
      <td>
        {include file=`$theme->templatesDir`input_dropdown.html
            name='status'
            label=#plugin_change_tags_status#
            width='250'
            options=$filters.statuses
            standalone=true}
      </td>
    </tr>
    <tr>
      <td colspan="3">
        <button onclick="searchFinishedReports();" class="button">{#plugin_change_tags_search#}</button>
      </td>
    </tr>
  </table>
</form>
<div id="result_search_table"></div>
