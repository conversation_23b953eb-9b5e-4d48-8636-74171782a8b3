<?php

include_once __DIR__ . '/../general/credits.general.adapter.php';

class Credits_Leno_Adapter extends Credits_General_Adapter {

    /**
     * Function to calculate the payment plan with penalty
     *
     * @param array $gt2_var - the gt2 table which will be updated
     * @param array $overdue_rows - list of overdue rows
     * @param float $penalty_interest - the penalty percent
     * @return void
     */
    protected function calcTableWithPenalty(&$gt2_var, $overdue_rows, float $penalty_interest): void
    {
        $total_left_principal = array_sum(array_column($gt2_var['values'], 'average_weighted_delivery_price'));
        foreach ($gt2_var['values'] as $row_key => $row_values) {
            if (!in_array($row_values['id'], $overdue_rows)) {
                continue;
            }

            // check the previous day
            $temp_date = new DateTime();
            $days_multiplier = 1;
            $temp_date->sub(new DateInterval('P1D'));
            while (in_array($temp_date->format('w'), array(0, 6, 7))) {
                if ($row_values['article_code'] < $temp_date->format('Y-m-d')) {
                    $days_multiplier++;
                }
                $temp_date->sub(new DateInterval('P1D'));
            }

            $extra_obligation = round(((($penalty_interest / 100) / 30) * $total_left_principal) * $days_multiplier, 2);
            $this->total_extra_obligations += $extra_obligation;

            $gt2_var['values'][$row_key]['free_text5'] = sprintf('%.2f',
                round(floatval($row_values['free_text5']) + $extra_obligation, 2));
            $gt2_var['values'][$row_key]['free_field5'] = sprintf('%.2f',
                round(floatval($row_values['free_field5']) + $extra_obligation, 2));
            $gt2_var['values'][$row_key]['article_second_code'] = sprintf('%.2f',
                round(floatval($row_values['article_second_code']) + $extra_obligation, 2));
            $gt2_var['values'][$row_key]['free_text3'] = sprintf('%.2f',
                round(floatval($row_values['free_text3']) + $extra_obligation, 2));
        }
    }

    /**
     * Function to get the penalty percent
     *
     * @return float
     */
    protected function getPenaltyPercent(): float
    {
        if (empty($this->settings['penalty_interest']) ||
            empty($this->current_model_vars[$this->settings['penalty_interest']]['value'])) {
            return 0;
        }
        return floatval($this->current_model_vars[$this->settings['penalty_interest']]['value']);
    }

    /**
     * Function containing required fields
     *
     * @return array
     */
    protected function getUserCreditRequiredFields(): array
    {
        return array(
            $this->settings['credit_product'],
            $this->settings['credit_amount'],
            $this->settings['credit_currency'],
            $this->settings['credit_repayment_period'],
            $this->settings['credit_first_payment_date']
        );
    }

    /**
     * Function to calculate the Interest for investment credit
     *
     * @param array $new_row
     * @param integer $i
     * @param bool $credit_utilize
     * @param array $previous_row
     */
    protected function calculateInvestmentCreditRowInterest(
        array &$new_row, int $i, bool $credit_utilize = false, array $previous_row = array()
    ): void {
        $date_start_period = '';
        $new_row['quantity'] = $this->interest_rate * $this->not_covered_principal;
        $full_month_interval = 30;

        if ($this->isCreditLine() && $credit_utilize) {
            if (!$date_start_period) {
                $date_start_period = new DateTime($this->contract_periods[$i]['split_period_date']);
            }
            $date_split_period = new DateTime($this->calculate_from_date);

            $split_principal = 0;
            if ($i != 0) {
                // get the previous row
                $split_principal = $previous_row['last_delivery_price'] + $previous_row['average_weighted_delivery_price'];
            }

            $splits = array(
                $date_start_period->format('Y-m-d') => $split_principal
            );

            foreach ($this->current_model_vars[$this->settings['credit_transfer_date']]['value'] as $trans_key => $transfer_date) {
                $transfer_date = new DateTime($transfer_date);
                if ($transfer_date < $date_start_period || $transfer_date > $date_split_period) {
                    continue;
                }

                $splits[$transfer_date->format('Y-m-d')] = $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$trans_key] + $split_principal;
                $split_principal += $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$trans_key];
            }

            $split_dates = array_keys($splits);
            $interval_days_total = 0;
            $calculated_interest = 0;
            foreach ($splits as $dt_begin => $split_principal_current) {
                $current_key = array_search($dt_begin, $split_dates);
                if ($current_key === 0) {
                    continue;
                }

                // PROCESS THE DATA FROM THE PREVIOUS PERIOD
                $split_start_date = new DateTime($split_dates[$current_key - 1]);
                $split_end_date = new DateTime($dt_begin);
                $split_real_date = clone $split_end_date;
                $split_end_date = $split_end_date->sub(new DateInterval('P1D'));
                $split_principal_calc = $splits[$split_dates[$current_key - 1]];
                $split_interval = $split_start_date->diff($split_end_date)->d + 1;

                // check if the real split date is in different month and
                // if so perform calculation as if the current month has 30 days
                // (add 1 or 2 for 29 and 28 days or subtract -1 if 31 days)
                if ($split_start_date->format('Y-m') != $split_real_date->format('Y-m')) {
                    $split_start_date->modify('last day of this month');
                    $split_start_date->format('j');
                    $add_days = 30 - intval($split_start_date->format('j'));
                    $split_interval += $add_days;
                }

                $calculated_interest += $split_principal_calc * ($this->interest_rate / 30) * $split_interval;
                $interval_days_total += $split_interval;

                if ($current_key == count($split_dates) - 1) {
                    // PROCESS THE DATA FOR THE LAST PERIOD
                    // get the remaining days according to the requirements from the client
                    $split_interval = $full_month_interval - $interval_days_total;
                    $calculated_interest += $split_principal_current * ($this->interest_rate / 30) * $split_interval;
                }
            }

            $new_row['quantity'] = $calculated_interest;
        }

        $new_row['free_field1'] = sprintf('%.2f', round(($new_row['quantity'] - $new_row['article_barcode']), 2));
    }
}

?>
