<?php

include_once __DIR__ . '/buildPaymentPlan.trait.php';
include_once __DIR__ . '/calculatePaymentPlanPenaltyOverduePrincipal.trait.php';
include_once __DIR__ . '/autoDistributePayments.trait.php';
include_once __DIR__ . '/autoCreateContractIncomesReason.trait.php';

class Credits_General_Adapter {
    use \Nzoom\Mvc\ControllerTrait\I18nTrait;
    use buildPaymentPlan;
    use calculatePaymentPlanPenaltyOverduePrincipal;
    use autoDistributePayments;
    use autoCreateContractIncomesReason;

    /* @var $registry Registry */
    public $registry;

    /* @var $related_automation Automations_Controller */
    public $related_automation;
    public $settings;
    public $automation_params;
    public $current_model;
    public $current_old_model;
    public $reason_for_change;
    public $current_model_vars = array();
    public $update_vars_sql = array();
    public $update_additional_vars_template = '("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")';
    private $executionMessages = array();
    private $executionWarnings = array();
    private $executionErrors = array();


    public function __construct($registry, $settings, Automations_Controller $related_controller) {
        $this->registry = $registry;
        $this->settings = $settings;
        $this->related_automation = $related_controller;
    }

    /**
     * Write edit history for a loan contract
     *
     * @return void
     */
    protected function writeCurrentModelHistory() : void
    {
        // write history
        $filters = array(
            'where' => array('d.id="' . $this->current_model->get('id') . '"')
        );
        $new_model = Documents::searchOne($this->registry, $filters);
        $new_model->getVars();

        $history_params = array(
            'model' => $new_model,
            'action_type' => 'edit',
            'new_model' => $new_model,
            'old_model' => $this->current_old_model
        );
        Documents_History::saveData($this->registry, $history_params);
    }

    /*
     * Function to check if incomes reason in the required currency exists
     *
     * @param int $document_id - the id of the contract
     * @param string $currency - the currency of the contract
     * @param int incomes_reason_type - the id of the incomes reason type
     * @param int customer - the id of the customer
     */
    function checkExistingFinIncomesReason($document_id, $currency, $incomes_reason_type, $customer = 0) {
        $sql = 'SELECT fir.id' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
               '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to=d.id AND frr.link_to_model_name="Document")' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=frr.parent_id AND fir.type="' . $incomes_reason_type . '" AND fir.currency="' . $currency . '" AND fir.annulled_by=0 AND fir.active=1)' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
               '  ON (firi18n.parent_id=fir.id AND firi18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE d.id="' . $document_id . '"' . "\n";
        if ($customer) {
            $sql .= ' AND fir.customer="' . $customer . '"';
        }

        return $this->registry['db']->GetOne($sql);
    }

    /*
     * Function to issue income reason correction
     */
    public function issueFinIncomeReasonCorrection ($income_reason_id, $change_sum, $nom_id) {
        if ($change_sum<0.01 && $change_sum>-0.01) {
            // if the change is less than 0.01 skip the try
            return true;
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
        $result = true;
        $filters = array(
            'where' => array('fir.id = \'' . $income_reason_id . '\'')
        );
        $income_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $income_reason->getVars();
        $gt2_var = $income_reason->getGT2Vars();

        $old_income_reason = clone $income_reason;

        foreach ($gt2_var['values'] as $key_row => $row_vals) {
            if ($row_vals['article_id'] == $nom_id) {
                $new_price = round(($row_vals['price'] + $change_sum), 2);
                $gt2_var['values'][$key_row]['price'] = sprintf('%.2f', ($new_price<0 ? 0 : $new_price));
                if ($new_price<0 && $gt2_var['values'][$key_row]['surplus_value'] > 0) {
                    // check for interest if any sum has left
                    $leftover = round(($gt2_var['values'][$key_row]['surplus_value'] + $new_price), 2);
                    $gt2_var['values'][$key_row]['surplus_value'] = sprintf('%.2f', ($leftover<0 ? 0 : $leftover));
                }
                break;
            }
        }

        $income_reason->set('description', strval($this->reason_for_change), true);
        $income_reason->set('grouping_table_2', $gt2_var, true);
        $income_reason->calculateGT2();
        $income_reason->set('table_values_are_set', true, true);
        $this->registry->set('get_old_vars', $get_old_vars, true);

        if ($income_reason->validate('edit') && $income_reason->saveCorrect($old_income_reason)) {
            $filters = array('where' => array('fir.id = ' . $income_reason->get('id'),
                                              'fir.annulled_by = 0'));
            $new_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $new_incomes_reason->getGT2Vars();

            Finance_Incomes_Reasons_History::saveData(
                $this->registry,
                array(
                    'action_type' => ($old_income_reason->get('correction_id') ? 'addcorrect' : 'edit'),
                    'new_model' => $new_incomes_reason,
                    'old_model' => $old_income_reason
                )
            );
        } else {
            $result = false;
            $this->registry['db']->FailTrans();
        }

        return $result;
    }

    /*
     * Function to create the incomes reason to be related to the loan contract
     */
    public function createContractIncomesReason($params) : bool {
        $result = false;

        // CREATE THE INCOMES REASON THAT WILL BE CONNECTED TO THIS
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';

        $income_reason = new Finance_Incomes_Reason($this->registry);
        $old_income_reason = clone $income_reason;
        $old_income_reason->sanitize();
        $income_reason->set('type', $params['type'], true);
        $income_reason->set('group', 1, true);
        $income_reason->set('status', 'finished', true);
        $income_reason->set('customer', $params['customer'], true);
        $income_reason->set('issue_date', $params['issue_date'], true);
        $income_reason->set('company', $params['company'], true);
        $income_reason->set('office', $params['office'], true);
        $income_reason->set('payment_type', $params['payment_type'], true);
        $income_reason->set('container_id', $params['container_id'], true);
        $income_reason->set('total', $params['total'], true);
        $income_reason->set('total_without_discount', $params['total'], true);
        $income_reason->set('total_with_vat', $params['total'], true);
        $income_reason->set('fiscal_total', $params['total'], true);
        $income_reason->set('currency', $params['currency'], true);
        // fin_field_1 contains the id of the related reason (reason from the same type but in different currency)
        $income_reason->set('fin_field_1', $params['parent_reason'], true);

        //get the type
        $filters = array('where' => array('fdt.id=' . $params['type']), 'sanitize' => true);

        //get the event type reminder
        if (isset($params['payment_date'])) {
            $income_reason->set('date_of_payment', $params['payment_date'], true);
        } else {
            $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
            $income_reason->set('date_of_payment_count', $fin_doc_type->get('default_date_of_payment_count'), true);
            $income_reason->set('date_of_payment_period_type', $fin_doc_type->get('default_date_of_payment_period_type'), true);
            $income_reason->set('date_of_payment_point', $fin_doc_type->get('default_date_of_payment_point'), true);
        }
        if (isset($params['name'])) {
            $income_reason->set('name', $params['name'], true);
        }

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $gt2_new_model = $income_reason->getGT2Vars();

        $row_values = array();
        $row_values['quantity'] = 1;
        $row_values['article_measure_name'] = 1;
        $row_values['free_field2'] = '';
        $row_values['price'] = $params['total'];
        $row_values['article_id'] = $params['article_id'];

        $sql = 'SELECT `name` FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' WHERE `parent_id`="' . $params['article_id'] . '" AND `lang`="' . $this->registry['lang'] . '"';
        $row_values['article_name'] = $this->registry['db']->GetOne($sql);

        $gt2_new_model['values'][] = $row_values;
        $income_reason->set('grouping_table_2', $gt2_new_model, true);
        $income_reason->calculateGT2();
        $income_reason->set('table_values_are_set', true, true);

        $this->registry['db']->StartTrans();
        if ($income_reason->save()) {
            // Write history
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php';
            $filters = array(
                'where'      => array('fir.id = \'' . $income_reason->get('id') . '\''),
                'model_lang' => $income_reason->get('model_lang')
            );
            $new_income_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $new_income_reason->getVars();

            Finance_Incomes_Reasons_History::saveData(
                $this->registry,
                array(
                    'model'       => $new_income_reason,
                    'action_type' => 'add',
                    'new_model'   => $income_reason,
                    'old_model'   => $old_income_reason
                )
            );

            $sql = 'INSERT INTO `fin_reasons_relatives` (`parent_id`, `parent_model_name`, `link_to`, `link_to_model_name`, `rows_links`, `changes`)' . "\n" .
                   '  VALUES ("%d", "%s", "%d", "%s", "", "")' . "\n";
            $sql = sprintf($sql, $new_income_reason->get('id'), 'Finance_Incomes_Reason', $params['document_id'], 'Document');
            $this->registry['db']->Execute($sql);

            if ($this->registry['db']->HasFailedTrans()) {
                $this->setMessage('error', 'error_reason_establish_relations_failed');
            } else {
                $result = true;
            }
        } else {
            $this->setMessage('error', 'error_reason_add_failed');
        }
        $this->registry->set('get_old_vars', $get_old_vars, true);

        $this->registry['db']->CompleteTrans();
        return $result;
    }

    /**
     * Update the additional vars of certain document
     * @return void
     */
    private function updateDocumentAdditionalVars(): void
    {
        if (empty($this->update_vars_sql)) {
            return;
        }
        // update the other additional vars
        $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
               'VALUES ' . implode(",\n", $this->update_vars_sql) . "\n" .
               'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
        $this->registry['db']->Execute($sql);
    }

    /**
     * Get all the messages of certain type from the current adapter
     *
     * @return array
     */
    public function getMessages($type): array
    {
        $access_param = '';
        switch ($type) {
            case 'message':
                $access_param = 'executionMessages';
                break;
            case 'warning':
                $access_param = 'executionWarnings';
                break;
            case 'error':
                $access_param = 'executionErrors';
                break;
        }

        if (!$access_param) {
            return array();
        }

        return $this->$access_param;
    }

    /**
     * Get certain message by key
     *
     * @return array
     */
    public function getMessageByKey($type, $key): array
    {
        $type_messages = $this->getMessages($type);
        $message_keys = array_combine(array_keys($type_messages), array_column($type_messages, 'key'));
        $search_key = array_search($key, $message_keys);
        if ($search_key === null) {
            return array();
        }

        return $message_keys[$search_key];
    }

    /**
     * Get all the messages generated by the current report
     *
     * @return array[]
     */
    public function getAllMessages() {
        $messages = array(
            'messages' => $this->getMessages('message'),
            'warnings' => $this->getMessages('warning'),
            'errors'   => $this->getMessages('error')
        );
        return $messages;
    }

    /**
     * Set message in the current adapter
     *
     * @return void
     */
    protected function setMessage($type, $key, $placeholders = array()): void
    {
        $access_method = '';
        switch ($type) {
            case 'message':
                $access_method = 'executionMessages';
                break;
            case 'warning':
                $access_method = 'executionWarnings';
                break;
            case 'error':
                $access_method = 'executionErrors';
                break;
        }
        $this->$access_method[] = ['key' => $key, 'placeholders' => $placeholders];
    }

    /**
     * Add months based on a date
     *
     * @param DateTime $dateObject - the date which we will base our calculations on
     * @param int $months - number of months to add
     * @return DateTime - the new date with the number of added months
     * @throws Exception
     */
    public function addMonths(DateTime $dateObject, int $months): DateTime
    {
        $next = clone $dateObject;
        $next->modify('last day of +' . $months . ' month');

        if($dateObject->format('d') > $next->format('d')) {
            return $dateObject->add($dateObject->diff($next));
        }
        return $dateObject->add(new DateInterval('P' . $months . 'M'));
    }

    /**
     * Sub months based on a date
     *
     * @param DateTime $dateObject - the date which we will base our calculations on
     * @param int $months - number of months to sub
     * @return DateTime - the new date with the number of subtracted months
     * @throws Exception
     */
    public function subMonths(DateTime $dateObject, int $months): DateTime
    {
        $prev = clone $dateObject;
        $prev->modify('last day of -' . $months . ' month');

        if($dateObject->format('d') > $prev->format('d')) {
            return $dateObject->sub($prev->diff($dateObject));
        }
        return $dateObject->sub(new DateInterval('P' . $months . 'M'));
    }

    /*
     * Explore the related incomes reasons and define if there is need to create new one or to extend the existing
     */
    public function processIncomesReasons($payment, $contract_id, $distributed_sum_original, $contract_currency, $reason_change) {
        $error = false;

        // check for incomes reason with the currency of the payment
        $incomes_reason_exist = $this->checkExistingFinIncomesReason($contract_id, $payment->get('currency'), $this->settings['incomes_reason_type_id'], $payment->get('customer'));
        $convert_rate = Finance_Currencies::getRate($this->registry, $payment->get('currency'), $contract_currency);

        $reduce_main_inc_reason_amount = false;
        $distributed_sum_converted = round($distributed_sum_original * $convert_rate, 2);

        if (!$incomes_reason_exist) {
            // incomes reason does not exist, so we need to add it
            $params_reason = array(
                'type'         => $this->settings['incomes_reason_type_id'],
                'customer'     => $payment->get('customer'),
                'issue_date'   => $payment->get('issue_date'),
                'company'      => $payment->get('company'),
                'office'       => $payment->get('office'),
                'payment_type' => ($payment->get('container_type') == 'bank_account' ? 'bank' : 'cash'),
                'container_id' => $payment->get('container_id'),
                'total'        => $distributed_sum_original,
                'article_id'   => $this->settings['nom_contract_obligations_id'],
                'document_id'  => $contract_id,
                'parent_reason'=> (!empty($this->settings['incomes_reason_certain_id']) ? $this->settings['incomes_reason_certain_id'] : ''),
                'currency'     => $payment->get('currency')
            );
            $result = $this->createContractIncomesReason($params_reason);
            $reduce_main_inc_reason_amount = true;
            if (!$result) {
                $this->setMessage('error', 'error_reason_add_failed');
                $error = true;
            }
        } elseif ($incomes_reason_exist && $contract_currency != $payment->get('currency')) {
            // correct the existing document
            $reduce_main_inc_reason_amount = true;
            $this->reason_for_change = $reason_change;
            if (!$this->issueFinIncomeReasonCorrection($incomes_reason_exist, $distributed_sum_original, $this->settings['nom_contract_obligations_id'])) {
                $this->setMessage('error', 'error_failed_issue_correction_incomes_reason');
                $error = true;
            }
        }

        if ($reduce_main_inc_reason_amount) {
            // decrease the main incomes reason amount with the amount of the converted payment
            $sql = 'SELECT fir.id' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                   '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to=d.id AND frr.link_to_model_name="Document"' . (!empty($this->settings['incomes_reason_certain_id']) ? ' AND frr.parent_id="' . $this->settings['incomes_reason_certain_id'] . '"' : '') . ')' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
                   '  ON (fir.id=frr.parent_id AND fir.type="' . $this->settings['incomes_reason_type_id'] . '" AND fir.currency="' . $contract_currency . '")' . "\n" .
                   'WHERE d.id="' . $contract_id . '"' . "\n";
            $org_incomes_reason = $this->registry['db']->GetOne($sql);

            $changed_sum = $this->recalculateMainIncomesReasonSum($contract_id, $org_incomes_reason, $distributed_sum_converted);
            $this->reason_for_change = $reason_change;
            if (!$this->issueFinIncomeReasonCorrection($org_incomes_reason, $changed_sum, $this->settings['nom_contract_obligations_id'])) {
                $this->setMessage('error','error_reason_add_failed');
                $error = true;
            }
        }

        return !$error;
    }

    /*
     * Function to recalculate the remaining sum for the incomes reason related with the contract
     */
    function recalculateMainIncomesReasonSum($contract_id, $incomes_reason_id, $new_sum) {
        $add_vars = array(
            $this->settings['schedule_var_principal'], $this->settings['schedule_var_warranty'], $this->settings['schedule_var_interest'],
            $this->settings['schedule_var_tax'], $this->settings['schedule_var_tax_ang'], $this->settings['schedule_var_lpg'],
            $this->settings['schedule_var_currency_rate'], $this->settings['schedule_var_penalty']
        );

        $sql = 'SELECT `type` FROM ' . DB_TABLE_DOCUMENTS . ' WHERE `id`=' . $contract_id;
        $document_type = $this->registry['db']->GetOne($sql);

        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . "\n" .
               'WHERE `model`="Document" AND `model_type`="' . $document_type . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
        $add_vars = $this->registry['db']->GetAssoc($sql);

        // get the payments amounts in different currency
        $sql = 'SELECT SUM(d_cstm_princ.value + d_cstm_war.value + d_cstm_intr.value + IF(d_cstm_pen.value IS NULL, 0, d_cstm_pen.value) + IF(d_cstm_lpg.value IS NULL, 0, d_cstm_lpg.value) + d_cstm_tax.value + d_cstm_ang.value)' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_princ' . "\n" .
               '  ON (d_cstm_princ.model_id=d.id AND d_cstm_princ.var_id="' . $add_vars[$this->settings['schedule_var_principal']] . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_war' . "\n" .
               '  ON (d_cstm_war.model_id=d.id AND d_cstm_war.var_id="' . $add_vars[$this->settings['schedule_var_warranty']] . '" AND d_cstm_war.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_intr' . "\n" .
               '  ON (d_cstm_intr.model_id=d.id AND d_cstm_intr.var_id="' . $add_vars[$this->settings['schedule_var_interest']] . '" AND d_cstm_intr.num=d_cstm_princ.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pen' . "\n" .
               '  ON (d_cstm_pen.model_id=d.id AND d_cstm_pen.var_id="' . $add_vars[$this->settings['schedule_var_penalty']] . '" AND d_cstm_pen.num=d_cstm_princ.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_lpg' . "\n" .
               '  ON (d_cstm_lpg.model_id=d.id AND d_cstm_lpg.var_id="' . $add_vars[$this->settings['schedule_var_lpg']] . '" AND d_cstm_lpg.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tax' . "\n" .
               '  ON (d_cstm_tax.model_id=d.id AND d_cstm_tax.var_id="' . $add_vars[$this->settings['schedule_var_tax']] . '" AND d_cstm_tax.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ang' . "\n" .
               '  ON (d_cstm_ang.model_id=d.id AND d_cstm_ang.var_id="' . $add_vars[$this->settings['schedule_var_tax_ang']] . '" AND d_cstm_ang.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_rate' . "\n" .
               '  ON (d_cstm_rate.model_id=d.id AND d_cstm_rate.var_id="' . $add_vars[$this->settings['schedule_var_currency_rate']] . '" AND d_cstm_rate.num=d_cstm_princ.num)' . "\n" .
               'WHERE d.id="' . $contract_id . '" AND CAST(d_cstm_rate.value as DECIMAL(9,2))!=1' . "\n";
        $current_payments_different_currency = round(($this->registry['db']->GetOne($sql)), 2);

        // get the total owed
        $sql = 'SELECT SUM(article_second_code)' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' as gt2' . "\n" .
               ' ON (d.id=gt2.model_id AND gt2.model="Document")' . "\n" .
               'WHERE d.id="' . $contract_id . '"' . "\n";
        $contracts_total_owed = round($this->registry['db']->GetOne($sql), 2);

        $sql = 'SELECT `total_with_vat` FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' WHERE `id`="' . $incomes_reason_id . '"' . "\n";
        $incomes_reason_current_sum = $this->registry['db']->GetOne($sql);
        $new_incomes_reason_sum = $contracts_total_owed-($current_payments_different_currency + round($new_sum, 2));

        return round($new_incomes_reason_sum-$incomes_reason_current_sum, 2);
    }
}

?>
