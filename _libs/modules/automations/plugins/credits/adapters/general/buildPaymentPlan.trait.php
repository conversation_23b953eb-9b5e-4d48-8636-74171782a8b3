<?php

trait buildPaymentPlan {
    public $credit_type = '';
    private $contract_grace_periods = array();
    public $contract_periods = array();

    public $clear_table = false;
    public $contract_currency = '';
    public $payment_periods = 0;
    public $principal = 0;
    public $interest_rate = 0;
    public $interest = 0;
    public $left_interest = 0;
    public $not_covered_principal = 0;
    public $left_principal = 0;
    public $is_credit_line;
    public $utilize_row;
    public $utilize_sum;
    public $prepaid_principal;
    public $calculate_from_date;
    private $monthly_payment;
    private $commitment_fee;
    public $mid_contract_recalculation = false;
    public $fee_management_month;
    public $deleted_rows_data = [];


    /**
     * Function to create incomes reason and ppp
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function createPaymentPlan(array $params): bool
    {
        $this->automation_params = $params;
        $this->reason_for_change = $params['id'];

        // validate if the table can be built
        $this->current_model = clone ($this->automation_params['model']);
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $this->current_model->unsanitize();
        $this->current_model->getVars();
        $this->current_model_vars = $this->current_model->getAssocVars();

        // define the product type
        $this->defineCreditType();

        switch ($this->credit_type) {
            case 'user':
                $result = $this->calculateUserCreditPayments();
                break;
            case 'investment':
                $result = $this->calculateInvestmentCreditPayments(true);
                break;
            default:
                // no credit type defined
                $result = false;
                break;
        }
        $this->registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }

    /**
     * @return bool
     */
    private function checkUserCreditPlanCreationConditions(): bool
    {
        // status check
        $status_key = sprintf('%s_%d', $this->current_model->get('status'), $this->current_model->get('substatus'));

        $result = true;
        if ($status_key != $this->settings['status_create_payments_table']) {
            $this->setMessage('warning', 'warning_repayment_plan_not_calculated_status_passed');
            $result = false;
        }

        if (!$result) {
            return $result;
        }
        $result = $this->validatePaymentsTableRequiredFields();

        return $result;
    }

    /**
     * @return bool
     */
    private function checkInvestementCreditPlanCreationConditions(): bool
    {
        return $this->validatePaymentsTableRequiredFields();
    }

    /*
     * Function to validate the building of the payments table
     * @return mixed array $not_completed_fields - list with the labels of the fields which must be completed
     */
    protected function validatePaymentsTableRequiredFields()
    {
        $not_completed_fields = array();
        $required_fields = $this->getRequiredFields();

        foreach ($required_fields as $req_field) {
            if (is_array($req_field)) {
                $current_optional = array();
                $completed_optional = 0;
                foreach ($req_field as $rf) {
                    if (!isset($this->current_model_vars[$rf])) {
                        continue;
                    }
                    $current_optional[] = $this->current_model_vars[$rf]['label'];
                    if (!empty(floatval($this->current_model_vars[$rf]['value']))) {
                        $completed_optional++;
                    }
                }
                if ($completed_optional != 1) {
                    $not_completed_fields[] = sprintf($this->i18n('error_repayment_plan_optional_required_field'), implode(' ' . $this->i18n('or') . ' ', $current_optional));
                }
            } else {
                if (!isset($this->current_model_vars[$req_field])) {
                    continue;
                }
                if (is_array($this->current_model_vars[$req_field]['value'])) {
                    $completed_rows = array_filter($this->current_model_vars[$req_field]['value']);
                    if (empty($completed_rows)) {
                        $not_completed_fields[] = $this->current_model_vars[$req_field]['label'];
                    }
                } elseif (empty($this->current_model_vars[$req_field]['value'])) {
                    $not_completed_fields[] = $this->current_model_vars[$req_field]['label'];
                }
            }
        }

        if (!empty($not_completed_fields)) {
            $this->setMessage('error', 'error_repayment_plan_missing_completed_required_filed', [implode(', ', $not_completed_fields)]);
        }

        return empty($not_completed_fields);
    }

    /**
     * Function to define the credit type
     *
     */
    private function defineCreditType(): void
    {
        if (empty($this->settings['credit_product']) || empty($this->current_model_vars[$this->settings['credit_product']]['value'])) {
            $this->setMessage('error', 'error_no_credit_type_defined');
            return;
        }
        $user_credits = array_filter(preg_split('/\s*,\s*/', $this->settings['credit_type_user']));
        $investment_credits = array_filter(preg_split('/\s*,\s*/', $this->settings['credit_type_investment']));
        if (in_array($this->current_model_vars[$this->settings['credit_product']]['value'], $user_credits)) {
            $this->credit_type = 'user';
        } elseif (in_array($this->current_model_vars[$this->settings['credit_product']]['value'], $investment_credits)) {
            $this->credit_type = 'investment';
        }
    }

    /**
     * Function to calculate the payment plan for investment credits
     *
     * @return bool
     */
    public function calculateInvestmentCreditPayments($define_clear_table = false) : bool
    {
        if (!$this->checkInvestementCreditPlanCreationConditions()) {
            return false;
        }

        $gt2 = $this->current_model->getGT2Vars();
        $this->current_old_model = clone $this->current_model;

        if ($define_clear_table) {
            $this->clear_table = !($this->isCreditLine() && $this->settings['contract_substatus_active'] == $this->current_model->get('substatus'));
        }

        $gt2 = $this->buildInvestmentCreditTable($gt2);

        $this->current_model->set('grouping_table_2', $gt2, true);
        $this->current_model->calculateGT2();
        $this->current_model->set('table_values_are_set', true, true);

        $result = $this->saveInvestmentCredit();

        return $result;
    }

    /**
     * Function to calculate the payment plan for user credit
     *
     * @return bool
     */
    public function calculateUserCreditPayments() : bool
    {
        if (!$this->checkUserCreditPlanCreationConditions()) {
            return false;
        }

        $gt2 = $this->current_model->getGT2Vars();
        $this->current_old_model = clone $this->current_model;

        $gt2 = $this->buildUserCreditTable($gt2);

        $this->current_model->set('grouping_table_2', $gt2, true);
        $this->current_model->calculateGT2();
        $this->current_model->set('table_values_are_set', true, true);

        $result = $this->saveUserCredit();

        return $result;
    }

    /**
     * Method that defines the grace periods for the contract
     *
     * @return void
     */
    private function defineGracePeriods(): void
    {
        // get the grace periods
        if (!empty($this->current_model_vars[$this->settings['grace_period_from']]['value']) && !empty($this->current_model_vars[$this->settings['grace_period_to']]['value'])) {
            foreach ($this->current_model_vars[$this->settings['grace_period_from']]['value'] as $row_num => $period_from) {
                if (!empty($period_from) && !empty($this->current_model_vars[$this->settings['grace_period_to']]['value'][$row_num])) {
                    $this->contract_grace_periods[] = array(
                        'from' => $period_from,
                        'to'   => $this->current_model_vars[$this->settings['grace_period_to']]['value'][$row_num],
                    );
                }
            }
        }
    }

    /**
     * @param $assoc_vars
     * @return void
     */
    private function getUserCreditInterestRate(): void {
        // define the interest
        $this->interest_rate = 0;
        if (floatval($this->current_model_vars[$this->settings['credit_interest_rate_fixed']]['value'])) {
            $this->interest_rate = floatval($this->current_model_vars[$this->settings['credit_interest_rate_fixed']]['value']);
        } elseif (floatval($this->current_model_vars[$this->settings['credit_interest_rate_floating']]['value'])) {
            $this->interest_rate = floatval($this->current_model_vars[$this->settings['credit_interest_rate_floating']]['value']);
        }

        $this->interest_rate = ($this->interest_rate / 12) / 100;
    }

    /**
     * Get interest rate for investment credit
     *
     * @return void
     */
    private function getInvestmentCreditInterestRate(): void {
        // define the interest
        if (floatval($this->current_model_vars[$this->settings['credit_interest_rate_fixed']]['value'])) {
            $this->base_interest_rate = $this->interest_rate = floatval($this->current_model_vars[$this->settings['credit_interest_rate_fixed']]['value']);
        } elseif (floatval($this->current_model_vars[$this->settings['credit_interest_rate_floating']]['value'])) {
            $this->base_interest_rate = $this->interest_rate = floatval($this->current_model_vars[$this->settings['credit_interest_rate_floating']]['value']);
        }
        $this->interest_rate = ($this->interest_rate / 12) / 100;
    }

    /**
     * Function to calculate the total interest
     *
     * @return void
     */
    private function getUserCreditTotalInterestRate(): void {
        // define the total interest
        $this->interest = $this->left_interest = round($this->principal * $this->payment_periods * $this->interest_rate, 2);
    }

    private function getUserCreditPrincipal(): void
    {
        $this->principal = floatval($this->current_model_vars[$this->settings['credit_amount']]['value']);
        $this->left_principal = $this->not_covered_principal = $this->principal;
    }

    /**
     * Function to define the periods of the contract
     *
     * @return void
     */
    private function definePaymentPeriods($start_payment_date = ''): void
    {
        // calculate the months without the grace periods
        if (!$start_payment_date) {
            $start_payment_date = $this->current_model_vars[$this->settings['credit_first_payment_date']]['value'];
        }
        $first_payment_date = new DateTime($start_payment_date);
        $this->contract_periods = array();
        $set_last_month_date_deadline = !empty($this->current_model_vars[$this->settings['credit_month_last_date']]['value']);
        for ($i = 0; $i < $this->payment_periods; $i++) {
            $period_end = $this->addMonths(clone($first_payment_date), $i);
            if ($set_last_month_date_deadline) {
                $period_end = new DateTime($period_end->format('Y-m-t'));
            }
            $customer_pay_date = clone $period_end;
            if (!Calendars_Calendar::getWorkingDays($this->registry, $customer_pay_date->format('Y-m-d'),
                $customer_pay_date->format('Y-m-d'))) {
                $customer_pay_date = new DateTime(Calendars_Calendar::calcDateOnWorkingDays($this->registry,
                    $customer_pay_date->format('Y-m-d'), 1));
            }
            if ($i == 0) {
                // for the first period, return one month
                $period_start = $this->subMonths(clone($period_end), 1);
                $split_period_date = clone $period_start;
            } else {
                // for all periods after the first, get the previous payment date and add one day
                $split_period_date = $this->addMonths(clone($first_payment_date), $i - 1);
                if ($set_last_month_date_deadline) {
                    $split_period_date = new DateTime($split_period_date->format('Y-m-t'));
                }
                $period_start = clone $split_period_date;
                $period_start->add(new DateInterval('P1D'));
            }
            $this->contract_periods[$i] = array(
                'period_start' => $period_start->format('Y-m-d'),
                'period_end'   => $period_end->format('Y-m-d'),
                'split_period_date' => $split_period_date->format('Y-m-d'),
                'customer_pay_date' => $customer_pay_date->format('Y-m-d'),
                'graced' => false,
                'passed' => false
            );
            if (!empty($this->contract_grace_periods)) {
                foreach ($this->contract_grace_periods as $gp) {
                    if ($gp['from'] <= $this->contract_periods[$i]['period_end'] && $this->contract_periods[$i]['period_end'] <= $gp['to']) {
                        $this->contract_periods[$i]['graced'] = true;
                        if ($this->calculate_from_date>=$this->contract_periods[$i]['period_end']) {
                            $this->contract_periods[$i]['graced'] = true;
                        };
                        break;
                    }
                }
            }
        }
    }

    /**
     * Function to calculate the management fee
     *
     * @param array $current_payment
     * @param $payment_number
     * @return float
     */
    private function getManagementFee(array $current_payment, int $payment_number): float
    {
        if (empty($this->settings['management_fee_calculation']) ||
            !method_exists($this, $this->settings['management_fee_calculation'])) {
            return 0;
        }
        return $this->{$this->settings['management_fee_calculation']}($current_payment, $payment_number);
    }

    /**
     * Get the management fee months
     *
     * @return void
     */
    private function getFeeMangementMonths() : void
    {
        if (!empty($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value']) && !empty($this->current_model_vars[$this->settings['credit_fee_management_type']]['value'])) {
            switch ($this->current_model_vars[$this->settings['credit_fee_management_type']]['value']) {
                case $this->settings['nom_fee_management_month']:
                    $this->fee_management_month = 1;
                    break;
                case $this->settings['nom_fee_management_trimester']:
                    $this->fee_management_month = 3;
                    break;
                case $this->settings['nom_fee_management_year']:
                    $this->fee_management_month = 12;
                    break;
            }
        }
    }

    /**
     * Calculate the management fee based on the total grant credit
     *
     * @param array $current_payment
     * @param int $payment_number
     * @return float
     */
    private function calculateManagementFeeGrantCredit(array $current_payment, int $payment_number): float
    {
        $fee = 0;
        if ($this->fee_management_month && !($payment_number % $this->fee_management_month)) {
            $fee = floatval($this->current_model_vars[$this->settings['credit_amount']]['value']) *
                (floatval($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value']) / 100);
        }
        return $fee;
    }

    /**
     * Calculate the management fee based on the uncovered fee
     *
     * @param array $current_payment
     * @param int $payment_number
     * @return float
     */
    private function calculateManagementFeeUncoveredPrincipal(array $current_payment, int $payment_number): float
    {
        $fee = 0;
        if ($this->fee_management_month && !($payment_number % $this->fee_management_month)) {
            if ($this->fee_management_month == 1) {
                $fee = $this->previous_row_not_covered_principal * ($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value'] / 100);
            } else {
                $fee = $current_payment['last_delivery_price'] * ($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value'] / 100);
            }
        }

        return $fee;
    }

    /**
     * Function to prepare the payment periods
     *
     * @return void
     */
    private function getRepaymentPeriods()
    {
        $this->payment_periods = intval($this->current_model_vars[$this->settings['credit_repayment_period']]['value']);
    }

    /**
     * Calculate the principal for user credit per row
     *
     * @param array $new_row
     * @param integer $i
     * @param bool $last_not_grace_period
     * @return void
     */
    private function calculateUserCreditRowPrincipal(&$new_row, $i, &$last_not_grace_period): void
    {
        // check the grace periods
        $inside_grace_period = $this->contract_periods[$i]['graced'];

        // CALCULATE THE PRINCIPAL
        if ($inside_grace_period) {
            $new_row['price'] = 0.00;
        } elseif ($last_not_grace_period === $i) {
            $new_row['price'] = $this->left_principal;
        } elseif ($i != $this->payment_periods - 1) {
            $last_not_grace_period = $i;
            $new_row['price'] = round($this->principal / $this->payment_periods, 2);
        } else {
            $new_row['price'] = $this->left_principal;
        }

        $this->left_principal = $this->left_principal - $new_row['price'];
        $new_row['average_weighted_delivery_price'] = $new_row['price'] - $new_row['article_deliverer_name'];
        $new_row['average_weighted_delivery_price'] = sprintf('%.2f', round($new_row['average_weighted_delivery_price'], 2));
    }

    /**
     * Calculate the interest for user credit per row
     *
     * @param array $new_row
     * @param integer $i
     * @return void
     */
    private function calculateUserCreditRowInterest(&$new_row, $i): void
    {
        if ($i == $this->payment_periods - 1) {
            // for the last row complete the leftover interest
            $new_row['quantity'] = $this->left_interest;
        } else {
            $new_row['quantity'] = round($this->interest / $this->payment_periods, 2);
        }
        $this->left_interest -= $new_row['quantity'];
        $new_row['free_field1'] = sprintf('%.2f', $new_row['quantity'] - $new_row['article_barcode']);
    }

    /**
     * Calculate the insurance for user credit per row
     *
     * @param array $new_row
     * @return void
     */
    private function calculateUserCreditRowInsurance(&$new_row): void
    {
        //$new_row['article_trademark'] = 0;
        //$new_row['free_field2'] = $new_row['article_trademark'] - $new_row['article_height'];
    }

    /**
     * Calculate the first tax for user credit per row
     *
     * @param array $new_row
     * @return void
     */
    private function calculateUserCreditRowTax1(&$new_row): void
    {
        //$new_row['free_text1'] = 0;
        //$new_row['free_field3'] = $new_row['free_text1'] - $new_row['article_width'];
    }

    /**
     * Calculate the second tax for user credit per row
     *
     * @param array $new_row
     * @return void
     */
    private function calculateUserCreditRowTax2(&$new_row): void
    {
        // $new_row['free_text2'] = 0;
        // $new_row['article_description'] = $new_row['free_text2'] - $new_row['article_name'];
    }

    /**
     * Calculate the LPG for user credit per row
     *
     * @param array $new_row
     * @return void
     */
    private function calculateUserCreditRowLpg(&$new_row): void
    {
        //$new_row['free_text3'] = 0;
        //$new_row['free_text5'] = $new_row['free_text3'] - $new_row['free_text4'];
    }

    /**
     * Calculate the penalty for user credit per row
     *
     * @param array $new_row
     * @return void
     */
    private function calculateUserCreditRowPenalty(&$new_row): void
    {
        //$new_row['article_delivery_code'] = 0;
        //$new_row['free_field4'] = $new_row['article_delivery_code'] - $new_row['article_weight'];
    }

    /**
     * Calculate the totals for user credit per row
     *
     * @param array $new_row
     * @return void
     */
    private function calculateUserCreditRowTotal(&$new_row): void
    {
        // total payment
        $new_row['article_second_code'] = sprintf('%.2f', round($new_row['price'],2) +
                                                          round($new_row['quantity'],2) + round($new_row['article_trademark'],2) +
                                                          round($new_row['free_text1'], 2) + round($new_row['free_text2'], 2) +
                                                          round($new_row['free_text3'], 2) + round($new_row['article_delivery_code'], 2));
        $new_row['free_field5'] = $new_row['article_second_code'] - $new_row['article_volume'];
    }

    /**
     * Function containing required fields for user credit
     *
     * @return array
     */
    protected function getUserCreditRequiredFields(): array
    {
        return array(
            $this->settings['credit_product'],
            $this->settings['credit_amount'],
            $this->settings['credit_currency'],
            $this->settings['credit_repayment_period'],
            $this->settings['credit_first_payment_date'],
            array(
                $this->settings['credit_interest_rate_fixed'],
                $this->settings['credit_interest_rate_floating']
            )
        );
    }

    /**
     * Function containing required fields for investment credit
     *
     * @return array
     */
    protected function getInvestmentCreditRequiredFields(): array
    {
        return array(
            $this->settings['credit_amount'],
            $this->settings['credit_repayment_period'],
            $this->settings['credit_first_payment_date'],
            $this->settings['credit_premium_insurance_full'],
            $this->settings['credit_premium_insurance_partial'],
            $this->settings['credit_transfer_date'],
            $this->settings['credit_currency'],
            array(
                $this->settings['credit_interest_rate_fixed'],
                $this->settings['credit_interest_rate_floating']
            )
        );
    }

    /**
     * Function to get the required fields depending on the credit type
     *
     * @return array
     */
    protected function getRequiredFields(): array
    {
        $requiredFields = array();
        switch ($this->credit_type) {
            case 'user':
                $requiredFields = $this->getUserCreditRequiredFields();
                break;
            case 'investment':
                $requiredFields = $this->getInvestmentCreditRequiredFields();
                break;
            default:
                break;
        }
        return $requiredFields;
    }

    /**
     * @return bool
     */
    private function saveUserCredit(): bool
    {
        $this->getContractCurrency();
        $this->getRepaymentPeriods();

        // SET ADDITIONAL VARS
        // set average monthly payment
        $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
                                   $this->current_model->get('id'),
                                   $this->current_model_vars[$this->settings['monthly_payment']]['id'],
                                   1,
                                   sprintf('%.2f', round($this->calculateUserCreditNewTotal()/$this->payment_periods, 2)),
                                   $this->registry['currentUser']->get('id'),
                                   $this->registry['currentUser']->get('id'),
                                   ($this->current_model_vars[$this->settings['monthly_payment']]['multilang'] ? $this->registry['lang'] : ''));
        // set contract currency
        $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
                                   $this->current_model->get('id'),
                                   $this->current_model_vars[$this->settings['repayment_plan_currency']]['id'],
                                   1,
                                   $this->contract_currency,
                                   $this->registry['currentUser']->get('id'),
                                   $this->registry['currentUser']->get('id'),
                                   ($this->current_model_vars[$this->settings['repayment_plan_currency']]['multilang'] ? $this->registry['lang'] : ''));

        $this->registry['db']->StartTrans();

        // update the repayment plan
        if (!$this->current_model->saveGT2Vars()) {
            $this->registry['db']->FailTrans();
        }
        $this->updateDocumentAdditionalVars();

        $sum_after_change = $this->calculateUserCreditNewTotal();
        if (!$this->registry['db']->HasFailedTrans()) {
            $this->writeCurrentModelHistory();

            // check the incomes reason and create it if needed
            $incomes_reason_exists = $this->checkExistingFinIncomesReason($this->current_model->get('id'),
                $this->contract_currency, $this->settings['incomes_reason_type_id']);

            if ($incomes_reason_exists) {
                // check the difference in values
                $change_sum = $sum_after_change - $this->calculateUserCreditOldTotal();

                // change the existing incomes reason
                if (!$this->issueFinIncomeReasonCorrection($incomes_reason_exists, $change_sum,
                    $this->settings['nom_contract_obligations_id'])) {
                    $this->setMessage('error', 'error_failed_issue_correction_incomes_reason');
                    $this->registry['db']->FailTrans();
                }
            } else {
                // create a new incomes reason
                // it does not exist, so we need to add it
                $params_reason = array(
                    'type' => $this->settings['incomes_reason_type_id'],
                    'customer' => $this->current_model->get('customer'),
                    'issue_date' => $this->current_model->get('date'),
                    'company' => $this->settings['incomes_reason_company'],
                    'office' => $this->settings['incomes_reason_office'],
                    'payment_type' => '',
                    'container_id' => '',
                    'total' => $sum_after_change,
                    'article_id' => $this->settings['nom_contract_obligations_id'],
                    'document_id' => $this->current_model->get('id'),
                    'parent_reason' => '',
                    'currency' => $this->contract_currency
                );

                if (isset($this->settings['incomes_reason_container_' . strtolower($params_reason['currency'])])) {
                    $container_data = explode('_',
                        $this->settings['incomes_reason_container_' . strtolower($params_reason['currency'])]);
                    $params_reason['payment_type'] = $container_data[0];
                    $params_reason['container_id'] = $container_data[1];
                    if (!$this->createContractIncomesReason($params_reason)) {
                        $this->setMessage('error', 'error_repayment_plan_failed_adding_incomes_reason');
                        $this->registry['db']->FailTrans();
                    }
                } else {
                    $this->setMessage('error', 'error_repayment_plan_missing_data_for_incomes_reason');
                    $this->registry['db']->FailTrans();
                }
            }
        } else {
            $this->setMessage('error', 'error_repayment_plan_creation_failed');
            $this->registry['db']->FailTrans();
        }
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();
        return $result;
    }

    /**
     * Calculate the old sum of the payment plan based on the old model GT2
     *
     * @return float
     */
    public function calculateUserCreditOldTotal() : float
    {
        $sum_before_change = 0;
        $gt2_old = $this->current_old_model->get('grouping_table_2');
        $gt2_rows_old = (!empty($gt2_old['values']) ? $gt2_old['values'] : array());
        foreach ($gt2_rows_old as $rw) {
            $sum_before_change += (!empty($rw['article_second_code']) ? floatval($rw['article_second_code']) : 0);
        }
        return $sum_before_change;
    }

    /**
     * Calculate the new sum of the payment plan based on the current model GT2
     *
     * @return float
     */
    public function calculateUserCreditNewTotal() : float
    {
        $sum_after_change = 0;
        $gt2_new = $this->current_model->get('grouping_table_2');
        $gt2_rows_new = (!empty($gt2_new['values']) ? $gt2_new['values'] : array());
        foreach ($gt2_rows_new as $rw) {
            if (!empty($rw['deleted'])) {
                continue;
            }
            $sum_after_change += (!empty($rw['article_second_code']) ? floatval($rw['article_second_code']) : 0);
        }
        return $sum_after_change;
    }

    /**
     * Build the repayment plan for the user credits
     *
     * @param $gt2 - the existing GT2
     * @return array
     */
    private function buildUserCreditTable($gt2): array
    {
        $this->getContractCurrency();
        $this->getRepaymentPeriods();
        $this->defineGracePeriods();
        $this->definePaymentPeriods();
        $this->getUserCreditInterestRate();
        $this->getUserCreditPrincipal();
        $this->getUserCreditTotalInterestRate();

        /*
         * Mark the old rows as deleted
         */
        $gt2_rows = (!empty($gt2['values']) ? $gt2['values'] : array());
        foreach ($gt2_rows as $idx => $rw) {
            $gt2_rows[$idx]['deleted'] = 1;
        }

        /*
         * Start building rows
         */
        $last_not_grace_period = false;
        for ($i = 0; $i < $this->payment_periods; $i++) {
            $new_row = array(
                'article_code' => $this->contract_periods[$i]['customer_pay_date'],
                'article_measure_name' => $this->contract_periods[$i]['period_end'],
                'article_id' => 0,
                'last_delivery_price' => 0,
                'article_deliverer_name' => 0,
                'article_barcode' => 0,
                'article_height' => 0,
                'article_name' => 0,
                'article_width' => 0,
                'free_text3' => 0,
                'free_text4' => 0,
                'article_delivery_code' => 0,
                'article_weight' => 0,
                'article_volume' => 0,
                'average_weighted_delivery_price' => 0,
                'quantity' => 0,
                'free_text2' => 0,
                'article_description' => 0,
                'free_text1' => 0,
                'free_field3' => 0,
                'article_trademark' => 0,
                'free_field2' => 0,
            );

            // CALCULATE PRINCIPAL
            $this->calculateUserCreditRowPrincipal($new_row, $i, $last_not_grace_period);

            // CALCULATE INTEREST
            $this->calculateUserCreditRowInterest($new_row, $i);

            // CALCULATE THE INSURANCE
            $this->calculateUserCreditRowInsurance($new_row);

            // CALCULATE TAX 1
            $this->calculateUserCreditRowTax1($new_row);

            // CALCULATE TAX 2
            $this->calculateUserCreditRowTax2($new_row);

            // CALCULATE LPG
            $this->calculateUserCreditRowLpg($new_row);

            // CALCULATE PENALTY
            $this->calculateUserCreditRowPenalty($new_row);

            // CALCULATE TOTALS
            $this->calculateUserCreditRowTotal($new_row);

            $gt2_rows[] = $new_row;
        }

        $gt2['values'] = $gt2_rows;
        return $gt2;
    }

    /**
     * Function that defines if the credit is with credit line tag
     *
     * @return bool
     */
    protected function isCreditLine() : bool
    {
        if ($this->is_credit_line !== null) {
            return $this->is_credit_line;
        }
        $this->current_model->getTags();
        $tags = $this->current_model->get('tags');
        $this->is_credit_line = in_array($this->settings['credit_line_tag'], $tags);

        return $this->is_credit_line;
    }

    /**
     * Define the grace periods
     *
     * @return array
     */
    private function getGracePeriods(): array
    {
        // get the grace periods
        if (!empty($this->current_model_vars[$this->settings['grace_period_from']]['value']) && !empty($this->current_model_vars[$this->settings['grace_period_to']]['value'])) {
            foreach ($this->current_model_vars[$this->settings['grace_period_from']]['value'] as $row_num => $period_from) {
                if (!empty($period_from) && !empty($this->current_model_vars[$this->settings['grace_period_to']]['value'][$row_num])) {
                    $this->contract_grace_periods[] = array(
                        'from' => $period_from,
                        'to'   => $this->current_model_vars[$this->settings['grace_period_to']]['value'][$row_num],
                    );
                }
            }
        }
        return $this->contract_grace_periods;
    }

    /**
     * Get investment credit principal
     *
     * @return void
     */
    private function getInvestmentCreditPrincipal(): void
    {
        $this->principal = floatval($this->current_model_vars[$this->settings['credit_amount']]['value']);
        if ($this->isCreditLine()) {
            $this->principal = floatval($this->current_model_vars[$this->settings['credit_amount_utilized']]['value']);
            $this->defineSumUtilization();
        }
        $this->left_principal = $this->not_covered_principal = $this->principal;
    }

    /**
     * Define if new sum have to be utilized
     *
     * @return void
     */
    private function defineSumUtilization(): void
    {
        $utilized = !empty($this->current_model_vars[$this->settings['amount_utilized_yes']]['value']) ? $this->current_model_vars[$this->settings['amount_utilized_yes']]['value'] : array();
        foreach ($utilized as $row => $utlz) {
            if ($utlz != $this->settings['amount_utilized_yes_opt']) {
                $this->utilize_row = $row;
            }
        }
    }

    /**
     * Get the currency of the contract
     *
     * @return void
     */
    private function getContractCurrency(): void
    {
        $this->contract_currency = $this->current_model_vars[$this->settings['credit_currency']]['value'];
    }

    /**
     * Function to create the credit table for investment credits
     *
     * @param $gt2
     * @return array
     */
    public function buildInvestmentCreditTable($gt2): array {
        $first_payment_date = $this->current_model_vars[$this->settings['credit_first_payment_date']]['value'];
        $this->calculate_from_date = (!empty($this->calculate_from_date) ? $this->calculate_from_date : $first_payment_date);

        $this->getRepaymentPeriods();
        $this->getGracePeriods();
        $this->getInvestmentCreditInterestRate();
        $this->getInvestmentCreditPrincipal();
        $this->getContractCurrency();

        if ($this->utilize_row && !$this->prepaid_principal) {
            $this->calculate_from_date = $this->current_model_vars[$this->settings['credit_transfer_date']]['value'][$this->utilize_row];
            $this->utilize_sum = $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$this->utilize_row];
            $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
                $this->current_model->get('id'),
                $this->current_model_vars[$this->settings['amount_utilized_yes']]['id'],
                $this->utilize_row,
                $this->settings['amount_utilized_yes_opt'],
                $this->registry['currentUser']->get('id'),
                $this->registry['currentUser']->get('id'),
                ($this->current_model_vars[$this->settings['credit_transfer_date']]['multilang'] ? $this->registry['lang'] : ''));
        }

        $payment_start = 0;
        $gt2_rows = (!empty($gt2['values']) ? $gt2['values'] : array());
        foreach ($gt2_rows as $idx => $rw) {
            if ($rw['article_measure_name']>=$this->calculate_from_date || $this->clear_table) {
                $gt2_rows[$idx]['deleted'] = 1;
                $this->deleted_rows_data[] = $gt2_rows[$idx];
            } else {
                $this->left_principal = $this->not_covered_principal = ($this->not_covered_principal - $rw['price']);
                $payment_start++;
            }
        }

        // check if there is fee management
        $this->getFeeMangementMonths();

        // calculate the months without the grace periods
        $this->definePaymentPeriods($first_payment_date);
        $grace_periods_count = count(array_filter($this->contract_periods, fn($subperiod) => $subperiod['graced']));
        $passed_graced_periods = count(array_filter($this->contract_periods, function($subperiod) {
            return ($subperiod['graced'] && $this->calculate_from_date>=$subperiod['period_end']);
        }));

        // define the default month payment
        // it is based on the total number of months minus all the graced periods
        $monthly_payments_months = intval($this->payment_periods)-$grace_periods_count;
        if ($this->mid_contract_recalculation) {
            $monthly_payments_months = (intval($this->payment_periods)-$payment_start)-($grace_periods_count-$passed_graced_periods);
        }

        $this->monthly_payment = $this->calcPMT($this->interest_rate, $monthly_payments_months, $this->not_covered_principal);

        /*
         * Start building rows
         */
        $payments_list = array_keys($gt2_rows);
        for ($i = $payment_start; $i < $this->payment_periods; $i++) {
            $credit_utilize = false;
            $new_row = array(
                'article_code' => $this->contract_periods[$i]['customer_pay_date'],
                'article_measure_name' => $this->contract_periods[$i]['period_end'],
                'article_id' => 0,
                'last_delivery_price' => 0,
                'article_deliverer_name' => 0,
                'article_barcode' => 0,
                'article_height' => 0,
                'article_name' => 0,
                'article_width' => 0,
                'free_text4' => 0,
                'free_text3' => 0,
                'free_text5' => 0,
                'free_field4' => 0,
                'article_delivery_code' => 0,
                'article_weight' => 0,
                'article_volume' => 0,
                'average_weighted_delivery_price' => 0,
                'article_alternative_deliverer_name' => 0,
                'quantity' => 0,
                'free_text2' => 0,
                'article_description' => 0,
                'free_text1' => 0,
                'free_field3' => 0,
                'article_trademark' => 0,
                'free_field2' => 0,
            );

            if ($this->isCreditLine() && $i == $payment_start &&
                $this->utilize_sum &&
                ($this->calculate_from_date >= $this->contract_periods[$i]['period_start'] && $this->calculate_from_date <= $this->contract_periods[$i]['period_end'])) {
                // for the first row process the data of the last active row
                if (isset($gt2_rows[$payments_list[$payment_start]]) &&
                    $gt2_rows[$payments_list[$payment_start]]['article_measure_name'] >= $this->contract_periods[$i]['period_start'] &&
                    $gt2_rows[$payments_list[$payment_start]]['article_measure_name'] <= $this->contract_periods[$i]['period_end']) {
                    $credit_utilize = true;
                    foreach ($new_row as $k => $val) {
                        $new_row[$k] = $gt2_rows[$payments_list[$payment_start]][$k];
                    }

                    $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
                        $this->current_model->get('id'),
                        $this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['id'],
                        $this->utilize_row,
                        $this->not_covered_principal,
                        $this->registry['currentUser']->get('id'),
                        $this->registry['currentUser']->get('id'),
                        ($this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['multilang'] ? $this->registry['lang'] : ''));
                }
            }

            // complete the previously paid sums if needed
            if ($this->calculate_from_date) {
                $paid_sums_fields = array('article_deliverer_name', 'article_barcode', 'article_height',
                                          'article_name', 'article_width', 'free_text4', 'article_weight', 'article_volume');
                $deleted_date_index_relations = array_combine(array_column($this->deleted_rows_data, 'article_measure_name'), array_keys($this->deleted_rows_data));

                if (isset($deleted_date_index_relations[$new_row['article_measure_name']])) {
                    $idx_deleted_row = $deleted_date_index_relations[$new_row['article_measure_name']];
                    foreach ($paid_sums_fields as $del_field) {
                        $new_row[$del_field] = $this->deleted_rows_data[$idx_deleted_row][$del_field];
                    }
                }
            }

            // CALCULATE INTEREST
            $prev_row_idx = ($payments_list[$payment_start - 1] ?? '');
            $this->calculateInvestmentCreditRowInterest($new_row, $i, $credit_utilize,
                (!empty($gt2_rows[$prev_row_idx]) ? $gt2_rows[$prev_row_idx] : array()));

             // CALCULATE THE PRINCIPAL
             $this->calculateInvestmentCreditRowPrincipal($new_row, $i, $credit_utilize, $payment_start);

             // CALCULATE THE INSURANCE
             $this->calculateInvestmentCreditRowInsurance($new_row, $i);

             // RECALCULATE NOT COVERED PRINCIPAL
             $this->calculateInvestmentCreditRowNotCoveredPrincipal($new_row, $i);

            // CALCULATE THE FEE MANAGEMENT
            $this->calculateInvestmentCreditRowTax2($new_row, $i);

            // CALCULATE THE FEE COMMITMENT
            $this->calculateInvestmentCreditRowTax1($new_row);

            // TOTAL PAYMENT
            $this->calculateInvestmentCreditRowTotal($new_row);

            $gt2_rows[] = $new_row;
        }

        foreach ($gt2_rows as $r => $gt2_r) {
            if (!empty($gt2_r['deleted'])) {
                continue;
            }
            foreach ($gt2_r as $gt2_var => $gt2_val) {
                if ($gt2_var == 'article_code' || $gt2_var == 'article_measure_name') {
                    continue;
                }
                $gt2_rows[$r][$gt2_var] = sprintf('%.2f', round($gt2_val, 2));
            }
        }
        $gt2['values'] = $gt2_rows;

        return $gt2;
    }

    /*
     * Function to calculate the GPR based on the gt2 rows and the data in the loan contract
     *
     * @param array $rows - rows of the gt2 table
     * @param array $model_vars - model id which we need the GPR for
     *
     * @return float - the calculated GPR
     */
    public function calcGPR($rows, $model_id) {
        $total_principal= 0;
        $total_costs = 0;
        $flow = array();

        foreach ($rows as $gt2_r) {
            if (!empty($gt2_r['deleted'])) {
                continue;
            }
            $flow[] = $gt2_r['article_second_code'];
            $total_principal += floatval($gt2_r['price']);
        }

        // Get the current vars in the model
        // write history
        $filters = array(
            'where' => array('d.id="' . $model_id . '"')
        );
        $new_model = Documents::searchOne($this->registry, $filters);
        $model_vars = $new_model->getAssocVars();

        // calculate the additional costs
        $available_costs_types = array(
            $this->settings['credit_mortgage_cost'],
            $this->settings['credit_collateral_cost'],
            $this->settings['credit_review_documents_cost'],
            $this->settings['credit_other_expense_cost']
        );
        $available_costs_types = array_filter($available_costs_types);
        $costs_types = $model_vars[$this->settings['credit_cost_type']]['value'];
        $costs_amount = $model_vars[$this->settings['credit_cost_sum_converted']]['value'];

        foreach ($costs_types as $r => $cost_type) {
            if (!in_array($cost_type, $available_costs_types)) {
                continue;
            }
            $total_costs += floatval($costs_amount[$r]);
        }

        // calculate GPR
        $positive_values = $total_principal - $total_costs;
        $gpr_irr = $this->calcIRR($positive_values, $flow);
        $gpr = ((pow(($gpr_irr+100)/100, 12)) * 100)-100;

        return $gpr;
    }

    /*
     * Calculate IRR (based on the Excel function of the same name)
     *
     * @param int $investment - the total that was given
     * @param array $flow - all the payments that are expected
     * @param float $precision - the precision of the calculation - set pretty low by default
     * @return float - the calculated IRR
     */
    public function calcIRR($investment, $flow, $precision = 0.0000000001) {
        $min = 0;
        $max = 1;
        $net_present_value = 1;
        $max_iterations = 100000;
        $iterator = 0;

        while(abs($net_present_value - $investment) > $precision && $iterator<$max_iterations) {
            $iterator++;
            $net_present_value = 0;
            $guess = ($min + $max) / 2;
            foreach ($flow as $period => $cashflow) {
                $net_present_value += $cashflow / (1 + $guess) ** ($period + 1);
            }
            if ($net_present_value - $investment > 0) {
                $min = $guess;
            } else {
                $max = $guess;
            }
        }

        return $guess * 100;
    }

        /*
     * Function to calculate the field collateral_to_outstanding
     */
    function calcCollateralToOutstanding($contract) {
        //$this->getCustomAutomationSettings('buildPaymentsTable');
        $gt2 = $contract->get('grouping_table_2');

        $total_principal_left = 0;
        foreach ($gt2['values'] as $row) {
            if (empty($row['deleted'])) {
                $total_principal_left += $row['average_weighted_delivery_price'];
            }
        }

        $assoc_vars = $contract->getAssocVars();
        if (!isset($assoc_vars[$this->settings['credit_collateral_percent']])) {
            return true;
        }
        $currency = (!empty($assoc_vars[$this->settings['credit_currency']]['value']) ? $assoc_vars[$this->settings['credit_currency']]['value'] : 'BGN');
        $collateral_total = (!empty($assoc_vars[$this->settings['credit_collateral_total']]['value']) ? $assoc_vars[$this->settings['credit_collateral_total']]['value'] : 0);

        // convert the sum in the BGN
        $conversion_rate = Finance_Currencies::getRate($this->registry, $currency, 'BGN');
        $total_principal_left = $conversion_rate * $total_principal_left;
        $collateral = (!$total_principal_left ? 0 : ($collateral_total / $total_principal_left) * 100);

        $vals = array();
        $vals[] = sprintf('("%d", "%d", "1", "%.2f", NOW(), %d, NOW(), %d, "%s")', $contract->get('id'), $assoc_vars[$this->settings['credit_collateral_percent']]['id'], $collateral, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[$this->settings['credit_collateral_percent']]['multilang'] ? $this->registry['lang'] : ''));

        // prepare the edit query
        $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
               'VALUES ' . implode(',' . "\n", $vals) . "\n" .
               'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";

        // update the fields
        $this->registry['db']->Execute($sql);

        return true;
    }

    /**
     * Function to save the Investment credit
     *
     * @return bool
     */
    private function saveInvestmentCredit(): bool
    {
        $gt2 = $this->current_model->get('grouping_table_2');
        $new_total = $this->calculateUserCreditNewTotal();
        $gt2_rows_count = count(array_filter($gt2['values'], fn($subperiod) => empty($subperiod['deleted'])));
        $gpr = $this->calcGPR($gt2['values'], $this->current_model->get('id'));

        // calculate avarage monthly payment
        $average_monthly_payment = sprintf('%.2f', round($new_total / $gt2_rows_count, 2));

        // set additional vars
        $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
            $this->current_model->get('id'),
            $this->current_model_vars[$this->settings['credit_gpr']]['id'],
            1,
            sprintf('%.4f', round($gpr, 4)),
            $this->registry['currentUser']->get('id'),
            $this->registry['currentUser']->get('id'),
            ($this->current_model_vars[$this->settings['credit_gpr']]['multilang'] ? $this->registry['lang'] : ''));
        $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
            $this->current_model->get('id'),
            $this->current_model_vars[$this->settings['monthly_payment']]['id'],
            1,
            $average_monthly_payment,
            $this->registry['currentUser']->get('id'),
            $this->registry['currentUser']->get('id'),
            ($this->current_model_vars[$this->settings['monthly_payment']]['multilang'] ? $this->registry['lang'] : ''));
        $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
            $this->current_model->get('id'),
            $this->current_model_vars[$this->settings['repayment_plan_currency']]['id'],
            1,
            $this->contract_currency,
            $this->registry['currentUser']->get('id'),
            $this->registry['currentUser']->get('id'),
            ($this->current_model_vars[$this->settings['repayment_plan_currency']]['multilang'] ? $this->registry['lang'] : ''));

        if ($this->clear_table) {
            // set all utilized sums to YES and update the uncovered principal
            $previously_utilized_sum = 0;
            foreach ($this->current_model_vars[$this->settings['amount_utilized_yes']]['value'] as $kr => $dat) {
                $previously_utilized_sum += (!empty($this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$kr]) ? floatval($this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$kr]) : 0);
                $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
                    $this->current_model->get('id'),
                    $this->current_model_vars[$this->settings['amount_utilized_yes']]['id'],
                    $kr,
                    $this->settings['amount_utilized_yes_opt'],
                    $this->registry['currentUser']->get('id'),
                    $this->registry['currentUser']->get('id'),
                    ($this->current_model_vars[$this->settings['amount_utilized_yes']]['multilang'] ? $this->registry['lang'] : ''));
                $this->update_vars_sql[] = sprintf($this->update_additional_vars_template,
                    $this->current_model->get('id'),
                    $this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['id'],
                    $kr,
                    $previously_utilized_sum,
                    $this->registry['currentUser']->get('id'),
                    $this->registry['currentUser']->get('id'),
                    ($this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['multilang'] ? $this->registry['lang'] : ''));
            }
        }

        $this->registry['db']->StartTrans();

        // update the repayment plan
        if (!$this->current_model->saveGT2Vars()) {
            $this->registry['db']->FailTrans();
        }

        // check if the customer is company or person
        $sql = 'SELECT `is_company` FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id`="' . $this->current_model->get('customer') . '"' . "\n";
        $customer_is_company = $this->registry['db']->GetOne($sql);

        if (!$customer_is_company && $gpr > ($this->base_interest_rate * 5)) {
            $this->executionErrors[] = sprintf($this->i18n('error_repayment_plan_creation_failed_gpr_too_high'),
                $this->current_model->get('name'), $gpr, $this->base_interest_rate);
            $this->registry['db']->FailTrans();
        }

        $this->updateDocumentAdditionalVars();

        $this->current_model->getGT2Vars();
        $this->calcCollateralToOutstanding($this->current_model);

        if (!$this->registry['db']->HasFailedTrans()) {
            // write history
            $filters = array(
                'where' => array('d.id="' . $this->current_model->get('id') . '"')
            );
            $new_model = Documents::searchOne($this->registry, $filters);
            $new_model->getVars();

            $history_params = array(
                'model' => $new_model,
                'action_type' => 'edit',
                'new_model' => $new_model,
                'old_model' => $this->current_old_model
            );
            Documents_History::saveData($this->registry, $history_params);

            // check the incomes reason and create it if needed
            $incomes_reason_exists = $this->checkExistingFinIncomesReason($new_model->get('id'),
                $this->contract_currency, $this->settings['incomes_reason_type_id']);

            if ($incomes_reason_exists) {
                // check the difference in values
                $change_sum = $new_total - $this->calculateUserCreditOldTotal();

                // change the existing incomes reason
                if (!$this->issueFinIncomeReasonCorrection($incomes_reason_exists, $change_sum,
                    $this->settings['nom_contract_obligations_id'])) {
                    $this->executionErrors[] = $this->i18n('error_failed_issue_correction_incomes_reason');
                    $this->registry['db']->FailTrans();
                }
            } else {
                // create a new incomes reason
                // does not exist, so we need to add it
                $params_reason = array(
                    'type' => $this->settings['incomes_reason_type_id'],
                    'customer' => $new_model->get('customer'),
                    'issue_date' => $new_model->get('date'),
                    'company' => $this->settings['incomes_reason_company'],
                    'office' => $this->settings['incomes_reason_office'],
                    'payment_type' => '',
                    'container_id' => '',
                    'total' => $new_total,
                    'article_id' => $this->settings['nom_contract_obligations_id'],
                    'document_id' => $new_model->get('id'),
                    'parent_reason' => '',
                    'currency' => $this->contract_currency
                );

                if (isset($this->settings['incomes_reason_container_' . strtolower($params_reason['currency'])])) {
                    $container_data = explode('_',
                        $this->settings['incomes_reason_container_' . strtolower($params_reason['currency'])]);
                    $params_reason['payment_type'] = $container_data[0];
                    $params_reason['container_id'] = $container_data[1];

                    if (!$this->createContractIncomesReason($params_reason)) {
                        $this->executionErrors[] = $this->i18n('error_repayment_plan_failed_adding_incomes_reason');
                        $this->registry['db']->FailTrans();
                    }
                } else {
                    $this->executionErrors[] = $this->i18n('error_repayment_plan_missing_data_for_incomes_reason');
                    $this->registry['db']->FailTrans();
                }
            }
        } else {
            $this->executionErrors[] = $this->i18n('error_repayment_plan_creation_failed');
            $this->registry['db']->FailTrans();
        }
        $result = !$this->registry['db']->HasFailedTrans();

        $this->registry['db']->CompleteTrans();
        return $result;
    }

    /*
     * Representation of PMT formula from Excel
     * @param float $interest_rate - Interest rate.
     * @param integer $months      - loan length in months.
     * @param float $loan_amount   - loan amount.
     */
    function calcPMT($interest_rate, $months, $loan_amount) {
        $amount = (-0 -$loan_amount * pow(1 + $interest_rate, $months)) / (1 + $interest_rate * 0) / ((pow(1 + $interest_rate, $months) - 1) / $interest_rate);
        return (-1) * $amount;
    }

    /**
     * Function to calculate the Interest for investment credit
     *
     * @param array $new_row
     * @param integer $i
     * @param bool $credit_utilize
     * @param array $previous_row
     */
    protected function calculateInvestmentCreditRowInterest(
        array &$new_row, int $i, bool $credit_utilize = false, array $previous_row = array()
    ): void {
        $date_start_period = '';
        $full_month_interval = 30;
        $first_payment_date = $this->current_model_vars[$this->settings['credit_first_payment_date']]['value'];
        if ($i == 0) {
            // for the first row
            $loan_transfer_dates = array_filter($this->current_model_vars[$this->settings['credit_transfer_date']]['value']);
            $loan_transfer = reset($loan_transfer_dates);

            $date_loan = $date_start_period = new DateTime($loan_transfer);
            $date_first = new DateTime($first_payment_date);

            $full_month_interval = $date_loan->diff($date_first)->days + 1;
            $new_row['quantity'] = ($this->interest_rate / 30) * $full_month_interval * $this->not_covered_principal;
        } else {
            // for all rows after the first
            $new_row['quantity'] = $this->interest_rate * $this->not_covered_principal;
        }

        if ($this->isCreditLine() && $credit_utilize) {
            if (!$date_start_period) {
                $date_start_period = new DateTime($this->contract_periods[$i]['split_period_date']);
            }
            $date_split_period = new DateTime($this->calculate_from_date);

            $split_principal = 0;
            if ($i != 0) {
                // get the previous row
                $split_principal = $previous_row['last_delivery_price'] + $previous_row['average_weighted_delivery_price'];
            }

            $splits = array(
                $date_start_period->format('Y-m-d') => $split_principal
            );

            foreach ($this->current_model_vars[$this->settings['credit_transfer_date']]['value'] as $trans_key => $transfer_date) {
                $transfer_date = new DateTime($transfer_date);
                if ($transfer_date < $date_start_period || $transfer_date > $date_split_period) {
                    continue;
                }

                $splits[$transfer_date->format('Y-m-d')] = $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$trans_key] + $split_principal;
                $split_principal += $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$trans_key];
            }

            $split_dates = array_keys($splits);
            $interval_days_total = 0;
            $calculated_interest = 0;
            foreach ($splits as $dt_begin => $split_principal_current) {
                $current_key = array_search($dt_begin, $split_dates);
                if ($current_key === 0) {
                    continue;
                }

                // PROCESS THE DATA FROM THE PREVIOUS PERIOD
                $split_start_date = new DateTime($split_dates[$current_key - 1]);
                $split_end_date = new DateTime($dt_begin);
                $split_real_date = clone $split_end_date;
                $split_end_date = $split_end_date->sub(new DateInterval('P1D'));
                $split_principal_calc = $splits[$split_dates[$current_key - 1]];
                $split_interval = $split_start_date->diff($split_end_date)->d + 1;

                // check if the real split date is in different month and
                // if so perform calculation as if the current month has 30 days
                // (add 1 or 2 for 29 and 28 days or subtract -1 if 31 days)
                if ($split_start_date->format('Y-m') != $split_real_date->format('Y-m')) {
                    $split_start_date->modify('last day of this month');
                    $split_start_date->format('j');
                    $add_days = 30 - intval($split_start_date->format('j'));
                    $split_interval += $add_days;
                }

                $calculated_interest += $split_principal_calc * ($this->interest_rate / 30) * $split_interval;
                $interval_days_total += $split_interval;

                if ($current_key == count($split_dates) - 1) {
                    // PROCESS THE DATA FOR THE LAST PERIOD
                    // get the remaining days according to the requirements from the client
                    $split_interval = $full_month_interval - $interval_days_total;
                    $calculated_interest += $split_principal_current * ($this->interest_rate / 30) * $split_interval;
                }
            }

            $new_row['quantity'] = $calculated_interest;
        }

        $new_row['free_field1'] = sprintf('%.2f', round(($new_row['quantity'] - $new_row['article_barcode']), 2));
    }

    /**
     * Function to calculate the Principal for investment credit
     *
     * @param array $new_row
     * @param integer $i
     * @param bool $credit_utilize
     * @param int $payment_start
     *
     * @return void
     */
    private function calculateInvestmentCreditRowPrincipal(
        array &$new_row, int $i, bool $credit_utilize = false, int $payment_start = 0
    ): void {
        if (!$payment_start) {
            $payment_start = $i;
        }

        // check the grace periods
        $inside_grace_period = $this->contract_periods[$i]['graced'];
        $grace_periods_count = count(array_filter($this->contract_periods, fn($subperiod) => $subperiod['graced']));
        $passed_graced_periods = count(array_filter($this->contract_periods, function($subperiod) use($i) {
            return ($subperiod['graced'] && $this->contract_periods[$i]['period_end']>=$subperiod['period_end']);
        }));

        $last_not_grace_period = null;

        $filtered_period_keys = array_keys(array_filter($this->contract_periods, fn($subperiod) => !$subperiod['passed']));
        if (!empty($filtered_period_keys)) {
            $last_not_grace_period = end($filtered_period_keys);
        }

        if ($inside_grace_period) {
            $new_row['price'] = 0.00;
        } elseif ($last_not_grace_period === $i) {
            $new_row['price'] = $this->left_principal;
        } elseif ($credit_utilize) {
            $first_pmt = $this->calcPMT($this->interest_rate,
                intval($this->payment_periods) - ($payment_start) - $grace_periods_count,
                $new_row['average_weighted_delivery_price'] + $new_row['last_delivery_price'] + $this->utilize_sum);
            if ($i == 0) {
                $new_row['price'] = $first_pmt - ($this->interest_rate * $this->not_covered_principal);
            } else {
                $new_row['price'] = $first_pmt - $new_row['quantity'];
            }
            // recalculate monthly payments
            $this->monthly_payment = $this->calcPMT($this->interest_rate,
                intval($this->payment_periods) - ($payment_start + 1) - $grace_periods_count,
                ($this->not_covered_principal - $new_row['price']));
        } elseif ($i == 0) {
            $new_row['price'] = $this->monthly_payment - ($this->interest_rate * $this->not_covered_principal);
        } else {
            $new_row['price'] = $this->monthly_payment - $new_row['quantity'];
        }

        // add the prepaid principal
        if ($this->prepaid_principal &&
            ($this->calculate_from_date > $this->contract_periods[$i]['period_start'] && $this->calculate_from_date <= $this->contract_periods[$i]['period_end'])) {
            $new_row['price'] = $this->prepaid_principal;

            // recalculate monthly payments
            $this->monthly_payment = $this->calcPMT($this->interest_rate,
                    (intval($this->payment_periods)-$payment_start-1)-($grace_periods_count-$passed_graced_periods),
                    ($this->not_covered_principal - $new_row['price']));
        }

        $this->left_principal = $this->left_principal - round($new_row['price'], 2);
        $new_row['average_weighted_delivery_price'] = $new_row['price'] - $new_row['article_deliverer_name'];
        $new_row['average_weighted_delivery_price'] = sprintf('%.2f',
            round($new_row['average_weighted_delivery_price'], 2));
    }

    /**
     * Function to calculate the investment credit row insurance
     *
     * @param array $new_row
     * @param integer $i
     * @return void
     */
    private function calculateInvestmentCreditRowInsurance(array &$new_row, int $i): void
    {
        $currency_rate = Finance_Currencies::getRate($this->registry, 'BGN', $this->contract_currency);
        if ($i == 0) {
            // write the sum of the partial insurance in the first payment
            $new_row['article_trademark'] = $currency_rate * floatval($this->current_model_vars[$this->settings['insurance_partial']]['value']);
        }

        $chk_date = new DateTime($new_row['article_measure_name']);
        if ($chk_date->format('n') == $this->settings['insurance_month']) {
            // write the sum of the insurance in the month set in the settings
            $new_row['article_trademark'] = $currency_rate * floatval($this->current_model_vars[$this->settings['insurance_premium']]['value']);
        }
        $new_row['free_field2'] = $new_row['article_trademark'] - $new_row['article_height'];
    }

    /**
     * Function to calculate the not covered principal for investment credit
     *
     * @param array $new_row
     * @param integer $i
     * @return void
     */
    private function calculateInvestmentCreditRowNotCoveredPrincipal(array &$new_row, int $i): void
    {
        $inside_grace_period = $this->contract_periods[$i]['graced'];

        $last_not_grace_period = null;
        $filtered_period_keys = array_keys(array_filter($this->contract_periods, fn($subperiod) => !$subperiod['passed']));
        if (!empty($filtered_period_keys)) {
            $last_not_grace_period = end($filtered_period_keys);
        }

        $this->previous_row_not_covered_principal = $this->not_covered_principal;
        $new_row['last_delivery_price'] = $this->not_covered_principal = ($last_not_grace_period === $i ? 0 : ($this->not_covered_principal - $new_row['price']));
    }

    /**
     * Function to calculate tax 1 for the row of the investment credit
     *
     * @param array $new_row
     * @return void
     */
    private function calculateInvestmentCreditRowTax1(array &$new_row): void
    {
        $new_row['free_text1'] = $this->getCommitmentFee();
        $new_row['free_field3'] = $new_row['free_text1'] - $new_row['article_width'];
    }

    /**
     * Function to calculate tax 2 for the row of the investment credit
     *
     * @param array $new_row
     * @param int $i
     * @return void
     */
    private function calculateInvestmentCreditRowTax2(array &$new_row, int $i): void
    {
        $new_row['free_text2'] = $this->getManagementFee($new_row, $i);
        $new_row['article_description'] = $new_row['free_text2'] - $new_row['article_name'];
    }

    /**
     * @return float
     */
    private function getCommitmentFee() : float
    {
        if ($this->commitment_fee === null) {
            if ($this->settings['commitment_fee_fixed'] && !empty($this->current_model_vars[$this->settings['commitment_fee_fixed']]['value'])) {
                $this->commitment_fee = $this->calculateCommitmentFeeFixed();
            } else {
                $this->commitment_fee = $this->calculateCommitmentFeePercent();
            }
        }
        return $this->commitment_fee;
    }

    /**
     * @return float|int
     */
    private function calculateCommitmentFeePercent() : float
    {
        $fee_commitment = 0;
        if (!empty($this->current_model_vars[$this->settings['credit_fee_commitment_percent']]['value']) && !empty($this->current_model_vars[$this->settings['credit_fee_commitment_type']]['value'])) {
            if ($this->current_model_vars[$this->settings['credit_fee_commitment_type']]['value'] == $this->settings['nom_approved_amount']) {
                $calc_based_on = ($this->isCreditLine() ? $this->current_model_vars[$this->settings['credit_amount_utilized']]['value'] : $this->current_model_vars[$this->settings['credit_amount']]['value']);
                $fee_commitment = $calc_based_on * ($this->current_model_vars[$this->settings['credit_fee_commitment_percent']]['value'] / 100);
            } elseif ($this->current_model_vars[$this->settings['credit_fee_commitment_type']]['value'] == $this->settings['nom_not_utilized_amount']) {
                $fee_commitment = $this->current_model_vars[$this->settings['credit_amount_notutilize']]['value'] * ($this->current_model_vars[$this->settings['credit_fee_commitment_percent']]['value'] / 100);
            }
        }
        return $fee_commitment;
    }

    /**
     * @return float|int
     */
    private function calculateCommitmentFeeFixed() : float
    {
        $fee_commitment = $this->current_model_vars[$this->settings['commitment_fee_fixed']]['value'];
        return $fee_commitment;
    }

    /**
     * @param array $new_row
     * @return void
     */
    private function calculateInvestmentCreditRowTotal(&$new_row): void
    {
        $new_row['article_second_code'] = sprintf('%.2f', round($new_row['price'], 2) +
                                                          round($new_row['quantity'], 2) +
                                                          round($new_row['article_trademark'], 2) +
                                                          round($new_row['free_text1'], 2) +
                                                          round($new_row['free_text2'], 2));
        $new_row['free_field5'] = $new_row['article_second_code'] - $new_row['article_volume'];
    }
}
