<?php

class Ariseco_Automations_Controller extends Automations_Controller {

    private $stats = array();

    /**
     * SKU Vault Tenant token
     * @see https://dev.skuvault.com/reference#gettokens
     * @var string $tenantToken
     */
    private $tenantToken;

    /**
     * SKU Vault user token
     * @see https://dev.skuvault.com/reference#gettokens
     * @var string $userToken
     */
    private $userToken;

    /**
     * Sync data into Channel Adviser
     *
     * @param array $params - arguments for the method, containing registry
     * @return string $barcode - the GS1 barcode generated
     */
    public function nzoom2ChannelAdvisorSync($params) {

        if ($this->metaData['automation_type'] == 'crontab' && empty($this->metaData['model'])) {

            //get all the:
            // - nomenclatures tagged with tag_approved_id
            //IMPORTANT: exclude those that have already been synced for the day
            // (once per day per nomenclature, ignore the result of the automation)
            $nom_table = DB_TABLE_NOMENCLATURES;
            $tags_models_table = DB_TABLE_TAGS_MODELS;
            $auto_history_table = DB_TABLE_AUTOMATIONS_HISTORY;
            $query = "
                SELECT n.id
                FROM {$nom_table} as n
                JOIN {$tags_models_table} as tm
                    ON n.type = {$this->metaData['start_model_type']} AND
                    tm.model = 'Nomenclature' AND
                    n.id = tm.model_id AND
                    tm.tag_id = {$this->settings['tag_approved_id']}
                LEFT JOIN {$auto_history_table} as ah
                  ON ah.parent_id = {$this->metaData['id']}
                     AND n.id = ah.model_id
                WHERE  ah.num IS NULL OR ah.added < n.modified
                GROUP BY id";
            $model_ids = $this->registry['db']->GetCol($query);
        } else {
            $model_ids = [$this->metaData['model']->get('id')];
        }

        if (!empty($model_ids)) {
            $models = Nomenclatures::search(
                $this->registry,
                array(
                    'where' => array(
                        'n.id IN (' . implode($model_ids) . ')',
                    ),
                )
            );
        }

        if (empty($models)) {
            // no nomenclatures to sync
            return false;
        }

        //do not start sync as some errors in previous automations might occur
        $errors = $this->registry['messages']->getErrors();
        $warnings = $this->registry['messages']->getWarnings();
        $executionErrors = $this->executionErrors;
        if (!empty($errors) || !empty($warnings) || !empty($executionErrors)) {
            $this->executionErrors[] = 'The sync to Channel Advisor did not start due to errors in previous automations!';
            return false;
        }

        $this->registry->set('get_old_vars', true, true);

        foreach($models as $model) {
            $data = $this->_prepareCAProduct($model);

            $caid = $model->getVarValue('caid');

            if (empty($data)) {
                $errors = [
                    $this->i18n('error_nomenclature_not_synced', [$model->get('code')]),
                    $this->i18n('error_parent_not_synced'),
                ];
                $this->_reportError($errors);
                continue;
            }
            $url = '/Products' . ($caid ? "({$caid})" : '');
            $action = !empty($caid) ? 'PATCH' : 'POST';

            $json = json_encode($data, JSON_PRETTY_PRINT);
            $response = $this->_requestChannelAdvisor($url, $action, $json);

            $result = true;
            if (!empty($response['error'])) {
                $errors = [
                    $this->i18n('error_nomenclature_not_synced', [$model->get('code')]),
                ];
                if (isset($response['error']['message'])) {
                    $errors[] = $response['error']['message'];
                }
                if (isset($response['error']['innererror'])) {
                    $errors[] = $response['error']['innererror']['message'];
                }
                $this->_reportError($errors);
                $result = false;
            } elseif ($action == 'POST') {
                //update the caid when adding it to Channel Advisor
                $result = $this->_setCaId($model, $response['ID']);
            }

            if ($result) {
                $this->updateAutomationHistory($this->metaData, $model, 1);
                if ($this->metaData['automation_type'] != 'crontab') {
                    //update the automation history of the corresponding
                    $query = 'INSERT INTO ' . DB_TABLE_AUTOMATIONS_HISTORY .  "\n" .
                    'SELECT id, ' . $model->get('id').  ', NOW(), 1, 1 FROM automations WHERE method LIKE "%' . __FUNCTION__ . '%" AND automation_type="crontab" AND start_model_type=' . $this->metaData['start_model_type'] . "\n" .
                    'ON DUPLICATE KEY UPDATE added=NOW(), num=VALUES(num)+1, result=1';
                    $this->registry['db']->Execute($query);
                }
            }
            if ($result && $this->metaData['automation_type'] != 'crontab') {
                $this->registry['messages']->setMessage($this->i18n('messages_sync_success'));
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        if (empty($this->executionErrors)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Updates nZoom nomenclature model with Channel Advisor ID
     *
     * @param Nomenclature $model - Nomenclature object
     * @param integer $caid - channel advisor id
     * @return bool  - result of the operation
     */
    public function _setCaId(Nomenclature $model, $caid) {
        $model->set('plain_vars', null, true);
        $vars = $model->getAssocVars(true);

        $vars['caid']['value'] = $caid;

        //save shoe nomenclature
        $old_model = clone $model;
        $model->set('vars', array_values($vars), true);
        $model->unsanitize();

        if ($model->save()) {
            $filters = array(
                'where' => array(
                    'n.id = ' . $model->get('id')
                ),
                'model_lang' => $model->get('model_lang')
            );
            $new_model = Nomenclatures::searchOne($this->registry, $filters);
            $new_model->getVars();

            Nomenclatures_History::saveData($this->registry, array('model' => $model, 'action_type' => 'edit', 'new_model' => $new_model, 'old_model' => $old_model));

            $result = true;
        } else {
            $result = false;
        }

        return $result;
    }

    /**
     * Prepare data for Cahnnel Advisor procedure of creating/updating product
     *
     * @param Nomenclature $nomenclature
     * @return array $data - prepare
     */
    private function _prepareCAProduct($nomenclature) {
        if ($nomenclature->get('type') == 18) {
            $data = $this->_prepareCAParentProduct($nomenclature);
        } else {
            $data = $this->_prepareCAChildProduct($nomenclature);
        }

        return $data;
    }

    /**
     * Prepare data for Cahnnel Advisor parent product
     *
     * @see https://developer.channeladvisor.com/working-with-products/create-products/create-a-parent
     * @param Nomenclature $nomenclature - nzoom model
     * @return array $data - prepare
     */
    private function _prepareCAParentProduct($nomenclature) {
        //caid
        $caid = $nomenclature->getVarValue('caid');

        //get super parent model clothes
        $filters = array(
            'where' => array(
                'n.id = ' . $nomenclature->getVarValue('model_clothes_id')
            ),
            'model_lang' => $nomenclature->get('model_lang')
        );
        $modelClothes = Nomenclatures::searchOne($this->registry, $filters);

        $category = $modelClothes->getVarValue('article_type');

        $data = [
            //'ID' => $caid,
            'ProfileID' => $this->settings['channel_advisor_profile_id'],
            'IsInRelationship' => true,
            'IsParent' => true,
            //'CopyToChildren' => true,
            'RelationshipName' => 'Size',
            //'ParentProductID' => '',
            //ToDo: value to be defined by tag or variable
            'IsBlocked' => false,
            'IsExternalQuantityBlocked' => false,
            'ASIN' => '',
            'Brand' => $nomenclature->getVarValue('brand_name'),
            'Condition' => 'New',
            'Description' => $nomenclature->getVarValue('long_desc'),
            'EAN' => '',
            'FlagDescription' => '',
            /**
             * Should be enum
             *  @see https://developer.channeladvisor.com/working-with-products/inventory-enumerations#InventoryEnumerations-FlagType
             */
            //'Flag' => $modelClothes->getVarValue('article_type'),
            'HarmonizedCode' => '',
            'Manufacturer' => 'Marcellamoda',
            'ShortDescription' => $nomenclature->getVarValue('short_desc'),
            'Sku' => $nomenclature->get('code'),
            'Subtitle' => '',
            'TaxProductCode' => '',
            'Title' => $nomenclature->get('name'),
            'UPC' => '',
            'Height' => floatval($modelClothes->getVarValue('height')),
            'Length' => floatval($modelClothes->getVarValue('length')),
            'Width' => floatval($modelClothes->getVarValue('width')),
            'Weight' => floatval($modelClothes->getVarValue('weight')),
            'Cost' => 0,
            'Margin' => 0,
            'RetailPrice' => floatval($nomenclature->get('sell_price')),
            'SupplierName' => 'Arise Co',
            'Classification' => '',
            'BundleType' => 'None',
        ];

        return $data;
    }

    /**
     * Prepare data for Cahnnel Advisor child product
     *
     * @see https://developer.channeladvisor.com/working-with-products/create-products/create-a-child
     * @param Nomenclature $nomenclature - nzoom model
     * @return mixed $data - array or false
     */
    private function _prepareCAChildProduct($nomenclature) {
        //caid
        $caid = $nomenclature->getVarValue('caid');

        //parent id
        $parentID = $nomenclature->getVarValue('parentsku_name_id');

        //parent
        $filters = array(
            'where' => array(
                'n.id = ' . $parentID
            ),
            'model_lang' => $nomenclature->get('model_lang')
        );
        $parentModel = Nomenclatures::searchOne($this->registry, $filters);

        //IMPORTANT: the parent product has not been synced yet
        if (!$parentModel->getVarValue('caid')) {
            return false;
        }

        //get super parent model clothes
        $filters = array(
            'where' => array(
                'n.id = ' . $nomenclature->getVarValue('model_clothes_id')
            ),
            'model_lang' => $nomenclature->get('model_lang')
        );
        $modelClothes = Nomenclatures::searchOne($this->registry, $filters);

        $data = [
            //'ID' => $caid,
            'ProfileID' => $this->settings['channel_advisor_profile_id'],
            'IsInRelationship' => true,
            'IsParent' => false,
            //not applicable for child
            //'CopyToChildren' => '',
            'RelationshipName' => 'Size',
            'ParentProductID' => $parentModel->getVarValue('caid'),
            //ToDo: value to be defined by tag or variable
            'IsBlocked' => false,
            'IsExternalQuantityBlocked' => false,
            'ASIN' => '',
            'Brand' => $parentModel->getVarValue('brand_name'),
            'Condition' => 'New',
            'Description' => '',
            'EAN' => $nomenclature->getVarValue('barcode'),
            'FlagDescription' => '',
            /**
             * Should be enum
             *  @see https://developer.channeladvisor.com/working-with-products/inventory-enumerations#InventoryEnumerations-FlagType
             */
            //'Flag' => $modelClothes->getVarValue('article_type'),
            'HarmonizedCode' => '',
            'Manufacturer' => 'Marcellamoda',
            'ShortDescription' => '',
            'Sku' => $nomenclature->get('code'),
            'Subtitle' => '',
            'TaxProductCode' => '',
            'Title' => $nomenclature->get('name'),
            'UPC' => $nomenclature->getVarValue('upc'),
            'Height' => floatval($modelClothes->getVarValue('height')),
            'Length' => floatval($modelClothes->getVarValue('length')),
            'Width' => floatval($modelClothes->getVarValue('width')),
            'Weight' => floatval($modelClothes->getVarValue('weight')),
            'Cost' => floatval($nomenclature->getVarValue('costprice')),
            'Margin' => floatval($parentModel->get('sell_price')) - floatval($nomenclature->getVarValue('costprice')),
            'RetailPrice' => floatval($parentModel->get('sell_price')),
            'SupplierName' => 'Arise Co',
            'Classification' => '',
            'BundleType' => 'None',
        ];

        return $data;
    }

    /**
     * Request Channel Advisor and return the response
     *
     * @param string $service_url - url to the service (e.g. /Products)
     * @param string $action - REST method: GET, POST, PUT, DELETE
     * @param string $json - the request string in JSON
     * @return array $response - the response string in JSON
     */
    private function _requestChannelAdvisor($service_url, $action = 'GET', $json = '') {
        $response = '';

        //get access token
        $accessToken = $this->_getChannelAdvisorAccessToken();

        if (empty($accessToken)) {
            return false;
        }

        $url = $this->settings['channel_advisor_url'] . $service_url;
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            "Authorization: Bearer {$accessToken}",
            "Content-Type: application/json",
            "Content-Length: " . mb_strlen($json)
        ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $action);
        if ($json) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $error_number = curl_errno($ch);
        curl_close($ch);

        if ($error_number) {
            $response = '{"error": {"message": "' . $error_number . ':' . $error . '"}}';
        }

        $response = json_decode($response, true);

        if (empty($response['error']) && substr($status, 0, 1) == 4) {
            //error status is 4XX
            $response = ["error" =>
                ["message" => "Error requesting API, status: ' . $status . '"]
            ];
        }

        return $response;
    }

    /**
     * Get Channel Advisor using a refresh token
     *
     * @see https://developer.channeladvisor.com/authorization/updating-access-token
     * @return string $accessToken - access token
     */
    private function _getChannelAdvisorAccessToken() {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        $url = $this->settings['channel_advisor_token_url'];
        $refresh_token = $this->settings['channel_advisor_refresh_token'];
        $application_id = $this->settings['channel_advisor_application_id'];
        $shared_secret = $this->settings['channel_advisor_shared_secret'];
        $data = [
            'grant_type' => 'refresh_token',
            'refresh_token' => $refresh_token,
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/x-www-form-urlencoded'
        ));
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, $application_id . ":" . $shared_secret);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $error_number = curl_errno($ch);
        curl_close($ch);

        $accessToken = false;
        if (!$error_number) {
            $response = json_decode($response, true);
            if (!$error_number && !empty($response['access_token'])) {
                $accessToken = $response['access_token'];
            }
        }

        return $accessToken;
    }

    /**
     * Report an error
     *
     * @var mixed $errors - error text or array of error texts
     */
    private function _reportError($errors) {
        if (!is_array($errors)) {
            $errors = [$errors];
        }
        if ($this->metaData['automation_type'] == 'crontab') {
            $this->executionErrors = array_merge($this->executionErrors, $errors);
        } else {
            foreach($errors as $error) {
                $this->registry['messages']->setError($error);
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

    /**
     * Automatically set the tags down the chain design -> parent SKU -> child SKU
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the operation
     */
    public function autoSetNomTags($params) {
        set_time_limit(0);

        // get the tags groups which have to be checked
        $settings = $this->getSettings($params);
        $db = $this->registry['db'];

        $tags_groups = preg_split('/\s*\,\s*/', $settings['groups_tags']);
        $tags_groups = array_filter($tags_groups);

        // get the tags included in the tags groups
        $tags_list = array();
        if (!empty($tags_groups)) {
            $sql = 'SELECT `id`' . "\n" .
                   'FROM ' . DB_TABLE_TAGS . "\n" .
                   'WHERE `active`=1 AND `deleted_by`=0 AND `model`="nomenclatures" AND `section` IN ("' . implode('","', $tags_groups) . '")' . "\n";
            $tags_list = $db->GetCol($sql);
        }

        if (empty($tags_list)) {
            // if tags list is empty we don't have anything more to do here
            return true;
        }

        // define which tags have been added or removed from the current model
        $added_tags = array_diff($params['model']->get('tags'), $params['model']->get('old_model')->get('tags'));
        $added_tags = array_intersect($added_tags, $tags_list);
        $removed_tags = array_diff($params['model']->get('old_model')->get('tags'), $params['model']->get('tags'));
        $removed_tags = array_intersect($removed_tags, $tags_list);

        // define the relations
        $sku_to_update = array(
            'parent' => array(),
            'child'  => array()
        );

        $parent_sku_related = array();

        if ($params['model']->get('type') == $settings['nom_parent_sku_type_id']) {
            // find the child
            $parent_sku_related[] = $params['model']->get('id');
        } elseif ($params['model']->get('type') == $settings['nom_design_type_id']) {
            // get all the parent SKUs related to the current design
            $parent_sku_related = $sku_to_update['parent'] = $this->_getDesignParentChildSKURelations($settings['nom_parent_sku_type_id'], $settings['parent_sku_relation_var'], array($params['model']->get('id')));
        }

        $sku_to_update['child'] = $this->_getDesignParentChildSKURelations($settings['nom_child_sku_type_id'], $settings['child_sku_relation_var'], $parent_sku_related);

        // START TO SET TAGS TO ALL THE FOUND MODELS
        foreach ($sku_to_update as $sku_list) {
            if (empty($sku_list)) {
                continue;
            }

            // get the models of the related SKUs
            $filters = array('where' => array('n.id IN ("' . implode('","', $sku_list) . '")'), 'model_lang' => $this->registry['lang']);
            $skus = Nomenclatures::search($this->registry, $filters);

            foreach ($skus as $sku) {
                $update_model_tags = false;
                $sku->getTags();

                $current_model_tags = $sku->get('tags');

                // check if tags has to be added
                foreach ($added_tags as $add_tag) {
                    if (!in_array($add_tag, $current_model_tags)) {
                        $current_model_tags[] = $add_tag;
                        $update_model_tags = true;
                    }
                }

                // check if tags has to be removed
                foreach ($removed_tags as $rem_tag) {
                    if (in_array($rem_tag, $current_model_tags)) {
                        $tag_idx = array_search($rem_tag, $current_model_tags);
                         unset($current_model_tags[$tag_idx]);
                        $update_model_tags = true;
                    }
                }

                if (!$update_model_tags) {
                    continue;
                }

                $old_sku = clone $sku;
                $old_sku->getModelTagsForAudit();
                $sku->set('tags', $current_model_tags, true);
                $sku->unsanitize();

                if ($sku->updateTags(array('skip_permissions' => true))) {
                    // get the updated document
                    $filters = array('where'      => array('n.id="' . $sku->get('id') . '"'),
                                     'model_lang' => $this->registry['lang']);
                    $new_sku = Nomenclatures::searchOne($this->registry, $filters);
                    $new_sku->getModelTagsForAudit();
                    $new_sku->sanitize();

                    Nomenclatures_History::saveData($this->registry, array('model' => $new_sku, 'action_type' => 'tag', 'new_model' => $new_sku, 'old_model' => $old_sku));
                }
            }
        }

        return true;
    }

    /**
     * Get the related parent or child SKUs based on the parent models
     *
     * @param integer $related_type_id - url to the service (e.g. /Products)
     * @param string $relation_var - the var which is to used to relate the models
     * @param array $parent_ids - the ids of the parent nomenclatures
     * @return array $related_ids - the ids of the related parent/child SKUs
     */
    private function _getDesignParentChildSKURelations($related_type_id, $relation_var, $parent_ids = array()) {
        $related_ids = array();

        if (empty($parent_ids)) {
            return $related_ids;
        }

        //get ids for additional var
        $query = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                 ' WHERE `name`="' . $relation_var . '" AND `model`="Nomenclature" AND `model_type`="' . $related_type_id . '"';
        $relation_var_id = $this->registry['db']->GetOne($query);

        // get the related model ids
        $query = 'SELECT n.id' . "\n" .
                 'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                 'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm' . "\n" .
                 ' ON (n.type="' . $related_type_id . '" AND n.active=1 AND n.deleted_by=0 AND n_cstm.model_id=n.id AND n_cstm.var_id="' . $relation_var_id . '" AND n_cstm.value IN ("' . implode('","', $parent_ids) . '"))' . "\n";
        $related_ids = $this->registry['db']->GetCol($query);

        return $related_ids;
    }

    /**
     * Automatically to move all fabrics from the Child SKU and place them
     * in a new Cut SKU and
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the operation
     */
    public function createCutSKUFromFabrics($params) {
        $result = true;
        // get the tags groups which have to be checked
        $settings = $this->getSettings($params);

        // get the nomecnaltures from the grouping table
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $filters = array('where' => array(
            'n.id = ' . $params['model']->get('id')),
            'model_lang' => $params['model']->get('model_lang')
        );
        $automation_model = Nomenclatures::searchOne($this->registry, $filters);
        $automation_model->getVars();

        $child_sku_assoc_vars = $automation_model->getAssocVars(true);

        $products = array();
        $grouping_index = 0;
        if (!empty($child_sku_assoc_vars[$settings['child_sku_product_id']]['value'])) {
            $products = array_filter($child_sku_assoc_vars[$settings['child_sku_product_id']]['value']);
            $grouping_index = $child_sku_assoc_vars[$settings['child_sku_product_id']]['grouping'];
        }

        if (empty($products)) {
            // the grouping table is empty
            $this->registry->set('get_old_vars', $get_old_vars, true);
            return true;
        }
        $products_qty = array_filter($child_sku_assoc_vars[$settings['child_sku_product_qty']]['value']);
        $products_waste = array_filter($child_sku_assoc_vars[$settings['child_sku_product_waste_percentage']]['value']);
        $products_main_fabric = array_filter($child_sku_assoc_vars[$settings['child_sku_product_main_fabric']]['value']);

        // check the types of the included products
        $searched_types = array($settings['nom_type_fabrics'], $settings['nom_type_cut_sku']);
        $sql = 'SELECT n.id, ni18n.name as name, n.type ' . "\n" .
               'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n ' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               ' ON (ni18n.parent_id=n.id AND ni18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE n.id IN (' . implode(',', $products) .  ') AND n.type IN ("' . implode('","', $searched_types) . '") AND n.active=1';
        $products_list = $this->registry['db']->GetAssoc($sql);

        $fabrics_list = array();
        $cut_sku_list = array();
        foreach ($products_list as $pl_k => $pl) {
            if ($pl['type'] == $settings['nom_type_cut_sku']) {
                $cut_sku_list[$pl_k] = $pl;
            } elseif ($pl['type'] == $settings['nom_type_fabrics']) {
                $fabrics_list[$pl_k] = $pl;
            }
        }

        if (empty($fabrics_list)) {
            // no fabrics so the automation has no further work to do
            $this->registry->set('get_old_vars', $get_old_vars, true);
            return true;
        }

        if (empty($cut_sku_list)) {
            // prepare the cut sku model
            $model_properties = array(
                'code'           => 'Cut ' . $automation_model->get('code'),
                'name'           => 'Cut ' . $automation_model->get('name'),
                'type'           => $settings['nom_type_cut_sku'],
                'subtype'        => 'commodity',
                'has_batch'      => 1,
                'has_batch_code' => 1,
                'group'          => 1,
                'active'         => 1,
                'model_lang'     => $this->registry['lang']
            );

            $cut_sku = new Nomenclature($this->registry, $model_properties);
        } else {
            // cut sku exists so insert the fabrics in it
            $cut_sku_list_ids = array_keys($cut_sku_list);
            $cut_sku_id = reset($cut_sku_list_ids);

            $filters = array('where' => array(
                                 'n.id = ' . $cut_sku_id
                             ),
                             'model_lang' => $this->registry['lang']
            );

            $cut_sku = Nomenclatures::searchOne($this->registry, $filters);
        }

        $cut_sku_vars = $cut_sku->getAssocVars();
        $old_cut_sku = clone $cut_sku;

        // FIX: mark to don't touch the categories
        if ($update_categories_is_requested = $this->registry['request']->isRequested('update_categories')) {
            $update_categories = $this->registry['request']->get('update_categories');
            $this->registry['request']->set('update_categories', false, 'post', true);
        }

        $cut_sku->set('categories', $automation_model->getCategories(), true);

        $cut_sku_action = 'edit';
        if ($cut_sku->get('id')) {
            $additional_vars_for_nom = array();
        } else {
            $additional_vars_for_nom = array(
                $settings['cut_sku_child_sku']       => $automation_model->get('code') . ' ' . $automation_model->get('name'),
                $settings['cut_sku_product_measure'] => 1,
            );
            $cut_sku_action = 'add';
        }

        $import_table = new Imports_Table(
            $this->registry,
            array(
                'persist_phpexcel' => true,
                'model' => $cut_sku->modelName,
                'model_type' => $cut_sku->get('type'),
                'grouping' => 0,
                'column_file' => array(),
                'column_table' => $additional_vars_for_nom
            )
        );

        // prepare the values per row for the grouping table
        $total_value = 0;
        $last_completed_row = 0;

        if (!empty($additional_vars_for_nom)) {
            // complete the child sku AC
            $import_table->prepareAutocompleteValues($settings['cut_sku_child_sku'], $cut_sku_vars, $additional_vars_for_nom);
        } else {
            if (!empty($cut_sku_vars[$settings['cut_sku_product']]['value'])) {
                foreach ($cut_sku_vars[$settings['cut_sku_product']]['value'] as $row_num => $row_val) {
                    $last_completed_row = $row_num;
                    if (!empty($cut_sku_vars[$settings['cut_sku_product_cost']]['value'][$row_num])) {
                        $total_value += floatval($cut_sku_vars[$settings['cut_sku_product_cost']]['value'][$row_num]);
                    }
                }
            }
            $last_completed_row++;
        }

        $grouping_vars_by_groups = array();
        foreach ($cut_sku_vars as $idx => $var) {
            if ($var['type'] != 'group' && !empty($var['grouping'])) {
                if (!isset($grouping_vars_by_groups[$var['grouping']])) {
                    $grouping_vars_by_groups[$var['grouping']] = array();
                }
                $grouping_vars_by_groups[$var['grouping']][$var['name']] = $var;
            }
        }

        $new_table_rows = array();
        foreach ($fabrics_list as $fl_id => $fl_name) {
            $table_row = array(
                $settings['cut_sku_product'] => $fl_name['name']
            );

            if (isset($grouping_vars_by_groups[$cut_sku_vars[$settings['cut_sku_product']]['grouping']][$settings['cut_sku_product']]['autocomplete']['plugin_params']['sql'])) {
                // search for a customQuery and change the '<search_string_parts>' part with exact match
                $grouping_vars_by_groups[$cut_sku_vars[$settings['cut_sku_product']]['grouping']][$settings['child_sku_product_name']]['autocomplete']['plugin_params']['sql'] =
                str_replace('<search_string_parts>', $fl_name['name'], $grouping_vars_by_groups[$cut_sku_vars[$settings['cut_sku_product']]['grouping']][$settings['cut_sku_product']]['autocomplete']['plugin_params']['sql']);
            }

            // find source row
            $source_row = array_search($fl_id, $products);
            $table_row[$settings['cut_sku_product_qty']] = isset($products_qty[$source_row]) ? $products_qty[$source_row] : 0;
            $table_row[$settings['cut_sku_product_waste_percentage']] = isset($products_waste[$source_row]) ? $products_waste[$source_row] : 0;
            $table_row[$settings['cut_sku_product_price']] = 0;

            $table_row = $import_table->prepareImportValuesRow($table_row, $grouping_vars_by_groups[$cut_sku_vars[$settings['cut_sku_product']]['grouping']]);
            $table_row[$settings['cut_sku_product_main_fabric']] = isset($products_main_fabric[$source_row]) ? $products_main_fabric[$source_row] : '';
            $table_row[$settings['cut_sku_product_waste_value']] = round((floatval($table_row[$settings['cut_sku_product_waste_percentage']])/100) * $table_row[$settings['cut_sku_product_qty']], 4);
            $table_row[$settings['cut_sku_product_waste_value']] = sprintf('%.4f', $table_row[$settings['cut_sku_product_waste_value']] + $table_row[$settings['cut_sku_product_qty']]);
            $table_row[$settings['cut_sku_product_cost']] = sprintf('%.4f', $table_row[$settings['cut_sku_product_waste_value']] * floatval($table_row[$settings['cut_sku_product_price']]));
            $total_value += $table_row[$settings['cut_sku_product_cost']];
            $new_table_rows[$last_completed_row] = $table_row;
            $last_completed_row++;
        }

        $additional_vars_for_nom[$settings['cut_sku_total']] = $total_value;

        // complete the vars in the assoc array with additional vars
        foreach ($additional_vars_for_nom as $var_name => $var_val) {
            $cut_sku_vars[$var_name]['value'] = $var_val;
        }

        // complete the grouping table vars
        foreach ($new_table_rows as $row_idx => $tbl_row) {
            foreach ($tbl_row as $tbl_var => $tbl_var_val) {
                if (!is_array($cut_sku_vars[$tbl_var]['value'])) {
                    $cut_sku_vars[$tbl_var]['value'] = array();
                }
                $cut_sku_vars[$tbl_var]['value'][$row_idx] = $tbl_var_val;
            }
        }

        $cut_sku->set('vars', array_values($cut_sku_vars), true);
        $cut_sku->set('plain_vars', null, true);
        $cut_sku->set('assoc_vars', null, true);

        // get the currencies from the parent model
        $cut_sku->set('sell_price_currency', $automation_model->get('sell_price_currency'), true);
        $cut_sku->set('last_delivery_price_currency', $automation_model->get('last_delivery_price_currency'), true);
        $cut_sku->set('average_weighted_delivery_price_currency', $automation_model->get('average_weighted_delivery_price_currency'), true);
        if (!$cut_sku->isDefined('sell_price')) {
            $cut_sku->set('sell_price', 0, true);
        }
        if (!$cut_sku->isDefined('last_delivery_price')) {
            $cut_sku->set('last_delivery_price', 0, true);
        }
        if (!$cut_sku->isDefined('average_weighted_delivery_price')) {
            $cut_sku->set('average_weighted_delivery_price', 0, true);
        }

        // save the cut SKU
        $this->registry['request']->set('update_categories', true, 'post', true);

        $this->registry['db']->StartTrans();
        if ($cut_sku->save()) {
            $this->registry['request']->set('update_categories', false, 'post', true);
            $filters = array('where' => array(
                             'n.id = ' . $cut_sku->get('id')),
                             'model_lang' => $cut_sku->get('model_lang')
            );

            $new_cut_sku = Nomenclatures::searchOne($this->registry, $filters);
            $new_cut_sku->getVars();

            Nomenclatures_History::saveData($this->registry, array('model' => $cut_sku, 'action_type' => $cut_sku_action, 'new_model' => $new_cut_sku, 'old_model' => $old_cut_sku));
            $this->executeActionAutomations($old_cut_sku, $new_cut_sku, $cut_sku_action);

            // UPDATE THE CHILD SKU
            // find which rows have to be removed
            $old_child_sku = clone $automation_model;

            $child_sku_grouping_table = array();
            $rows_indexes_to_clear = array();
            foreach (array_keys($fabrics_list) as $fabric_id) {
                $rows_indexes_to_clear[] = array_search($fabric_id, $child_sku_assoc_vars[$settings['child_sku_product_id']]['value']);
            }

            // prepare the grouping vars and clear the rows containing fabrics
            $highest_row = 0;
            $total_value = 0;
            $update_row = array_search($new_cut_sku->get('id'), $child_sku_assoc_vars[$settings['child_sku_product_id']]['value']);
            foreach ($child_sku_assoc_vars as $v_name => $var) {
                if ($var['grouping'] == $grouping_index && $var['type'] != 'group') {
                    // clear the extra rows
                    foreach ($var['value'] as $row_idx => $row_value) {
                        if (in_array($row_idx, $rows_indexes_to_clear)) {
                            unset($child_sku_assoc_vars[$v_name]['value'][$row_idx]);
                        } elseif ($var['name'] == $settings['child_sku_product_cost']) {
                            if ($update_row === false || ($update_row !== false && $update_row != $row_idx)) {
                                $total_value += $row_value;
                            }
                        }
                    }
                    $child_sku_grouping_table[$var['name']] = $child_sku_assoc_vars[$v_name];
                    // get the highest row left
                    $row_list = array_keys($child_sku_assoc_vars[$v_name]['value']);
                    $current_highest_row = end($row_list);
                    if ($current_highest_row > $highest_row) {
                        $highest_row = $current_highest_row;
                    }
                }
            }

            if ($update_row === false) {
                $update_row = $highest_row+1;
            }

            // use the functions for triggering the autocompleter
            $import_table = new Imports_Table(
                $this->registry,
                array(
                    'persist_phpexcel' => true,
                    'model' => $automation_model->modelName,
                    'model_type' => $automation_model->get('type'),
                    'grouping' => 0,
                    'column_file' => array(),
                    'column_table' => array()
                )
            );

            $table_row = array(
                $settings['child_sku_product_qty']              => 1,
                $settings['child_sku_product_name']             => $new_cut_sku->get('code'),
                $settings['child_sku_product_waste_percentage'] => 0
            );

            if (isset($child_sku_grouping_table[$settings['child_sku_product_name']]['autocomplete']['plugin_params']['sql'])) {
                // search for a customQuery and change the '<search_string_parts>' part with exact match
                $child_sku_grouping_table[$settings['child_sku_product_name']]['autocomplete']['plugin_params']['sql'] =
                str_replace('<search_string_parts>', $new_cut_sku->get('code'), $child_sku_grouping_table[$settings['child_sku_product_name']]['autocomplete']['plugin_params']['sql']);
            }

            $table_row = $import_table->prepareImportValuesRow($table_row, $child_sku_grouping_table);
            $table_row[$settings['child_sku_product_waste_value']] = $table_row[$settings['child_sku_product_waste_percentage']] ?
                                                                     ((floatval($table_row[$settings['child_sku_product_waste_percentage']])/100) * floatval($table_row[$settings['child_sku_product_qty']])) :
                                                                     $table_row[$settings['child_sku_product_qty']];
            $table_row[$settings['child_sku_product_waste_value']] = sprintf('%.2f', $table_row[$settings['child_sku_product_waste_value']]);
            $table_row[$settings['child_sku_product_cost']] = sprintf('%.4f', floatval($table_row[$settings['child_sku_product_waste_value']]) * floatval($table_row[$settings['child_sku_product_price']]));
            $total_value += floatval($table_row[$settings['child_sku_product_cost']]);

            foreach ($table_row as $var_name => $var_new_value) {
                $child_sku_assoc_vars[$var_name]['value'][$update_row] = $var_new_value;
            }
            $child_sku_assoc_vars[$settings['child_sku_total']]['value'] = $total_value;

            $automation_model->set('vars', array_values($child_sku_assoc_vars), true);
            $automation_model->set('plain_vars', null, true);
            $automation_model->set('assoc_vars', null, true);
            $automation_model->unsanitize();

            if ($automation_model->save()) {
               $filters = array('where' => array(
                                'n.id = ' . $automation_model->get('id')),
                                'model_lang' => $automation_model->get('model_lang')
               );

               $new_child_sku = Nomenclatures::searchOne($this->registry, $filters);
               $new_child_sku->getVars();

               Nomenclatures_History::saveData($this->registry, array('model' => $automation_model, 'action_type' => 'edit', 'new_model' => $new_child_sku, 'old_model' => $old_child_sku));
            } else {
                $this->registry['db']->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_create_cut_sku_from_fabrics_child_sku_edit_failed'));
                $this->registry['messages']->insertInSession($this->registry);
                $result = false;
            }
        } else {
            $this->registry['db']->FailTrans();
            $this->registry['messages']->setError($this->i18n('error_create_cut_sku_from_fabrics_cut_sku_add_failed'));
            $this->registry['messages']->insertInSession($this->registry);
            $result = false;
        }

        if ($update_categories_is_requested) {
            $this->registry['request']->set('update_categories', $update_categories, 'post', true);
        }

        $this->registry['db']->CompleteTrans();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }

    /**
     * Automation to complete the Parent SKU in the selected Design
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the operation
     */
    public function connectParentSKUtoDesign($params) {
        $result = true;

        // get the tags groups which have to be checked
        $settings = $this->getSettings($params);

        // get the nomecnaltures from the grouping table
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $parent_sku_assoc_vars = $params['model']->getAssocVars();

        $design_id = !empty($parent_sku_assoc_vars[$settings['parent_sku_design_id']]['value']) ? $parent_sku_assoc_vars[$settings['parent_sku_design_id']]['value'] : 0;
        if (!$design_id) {
            // no selected design so quit the execution
            $this->registry->set('get_old_vars', $get_old_vars, true);
            return false;
        }

        // get the design
        $filters = array('where' => array(
            'n.id = ' . $design_id),
            'model_lang' => $params['model']->get('model_lang')
        );

        $design = Nomenclatures::searchOne($this->registry, $filters);
        $design->getVars();
        $old_design = clone $design;

        $design_assoc_vars = $design->getAssocVars();

        // check if the parent SKU is not already set in the Design
        if (!empty($design_assoc_vars[$settings['design_parent_sku_id']]['value']) &&
            is_array($design_assoc_vars[$settings['design_parent_sku_id']]['value']) &&
            in_array($params['model']->get('id'), $design_assoc_vars[$settings['design_parent_sku_id']]['value'])) {
            // the parent sku is already in the design so exit
            $this->registry->set('get_old_vars', $get_old_vars, true);
            return false;
        }

        // get the grouping index for the needed table in the designs
        $grouping_index = $design_assoc_vars[$settings['design_parent_sku_id']]['grouping'];

        // select all the needed grouping vars
        $grouping_table = array();
        $completed_rows = array();
        foreach ($design_assoc_vars as $var_name => $var_data) {
            if ($var_data['grouping'] == $grouping_index && $var_data['type'] != 'group') {
                $grouping_table[$var_name] = $var_data;

                // find all the completed rows
                $completed_rows = array_merge($completed_rows, array_keys(array_filter($var_data['value'])));
            }
        }

        // get the unique list of the completed rows
        $completed_rows = array_values(array_unique($completed_rows));
        array_walk($completed_rows,function(&$rw_val){intval($rw_val);});
        sort($completed_rows);

        $new_row = 1;
        while(in_array($new_row, $completed_rows)) {
            $new_row++;
        }

        // use the functions for triggering the autocompleter
        $import_table = new Imports_Table(
            $this->registry,
            array(
                'persist_phpexcel' => true,
                'model' => $design->modelName,
                'model_type' => $design->get('type'),
                'grouping' => 0,
                'column_file' => array(),
                'column_table' => array()
            )
        );

        $table_row = array(
            $settings['design_parent_sku_name'] => $params['model']->get('code')
        );

        $table_row = $import_table->prepareImportValuesRow($table_row, $grouping_table);

        $check_completed_row = array_filter($table_row);
        if (empty($check_completed_row)) {
            // not found data - skip further processing
            $this->registry->set('get_old_vars', $get_old_vars, true);
            return false;
        }

        // complete the data from the row in the vars array
        foreach ($grouping_table as $var_name => $var_group) {
            $design_assoc_vars[$var_name]['value'][$new_row] = (!empty($table_row[$var_name]) ? $table_row[$var_name] : '');
        }

        $design->set('vars', array_values($design_assoc_vars), true);
        $design->set('plain_vars', null, true);
        $design->set('assoc_vars', null, true);
        $design->unsanitize();

        $this->registry['db']->StartTrans();
        if ($design->save()) {
            $filters = array('where' => array(
                'n.id = ' . $design->get('id')),
                'model_lang' => $design->get('model_lang')
            );

            $new_design = Nomenclatures::searchOne($this->registry, $filters);
            $new_design->getVars();

            Nomenclatures_History::saveData($this->registry, array('model' => $design, 'action_type' => 'edit', 'new_model' => $new_design, 'old_model' => $old_design));
        } else {
            $this->registry['db']->FailTrans();
            $this->registry['messages']->setError($this->i18n('error_create_cut_sku_from_fabrics_child_sku_edit_failed'));
            $this->registry['messages']->insertInSession($this->registry);
            $result = false;
        }

        $this->registry['db']->CompleteTrans();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }

    /**
     * Get the latest sales
     *
     * @param array $params - arguments for the method, containing registry
     * @return array $sales - sales as get from SKUVault
     */
    private function _getSKUVaultSales() {

        //get the automation history date
        $query = 'SELECT extra FROM ' . DB_TABLE_LOGS . ' WHERE event="skuvault2NzoomSaleSync" ORDER BY id DESC LIMIT 1';
        $lastSKUFilters = $this->registry['db']->GetOne($query);

        $lastOmitted = array();
        if ($lastSKUFilters) {
            $lastSKUFilters = json_decode($lastSKUFilters, true);
            $lastSynced = $lastSKUFilters['ToDate'] ?? null;
            $lastOmitted = $lastSKUFilters['Omitted'] ?? array();
        }

        //initial data time of the import
        $initialDate = $this->settings['initial_datetime'] ??
            '2021-01-01 00:00:00';

        $now = new DateTime();

        $fromDateTime = new DateTime($lastSynced ??$initialDate);
        //Important: one week is the limit
        $toDateTime = clone $fromDateTime;
        $toDateTime->modify('+1 week');
        $toDateTime = min($now, $toDateTime);


        $dateFormat = 'Y-m-d H:i:sP';

        //get the sales
        $skuVaultData = [
            'DateField' => 'Modified',
            'FromDate' => $fromDateTime->format($dateFormat),
            'ToDate' => $toDateTime->format($dateFormat),
            'PageSize' => 10000,
            'PageNumber' => 0,
            'TenantToken' => $this->tenantToken,
            'UserToken' => $this->userToken

        ];
        $skuVaultDataJSON = json_encode($skuVaultData);
        $action = 'POST';
        $endPoint = '/sales/getSalesByDate';
        if (!empty($this->settings['morningtool_host'])) {
            //morning tool uses the same requests as SkuVault
            $response = $this->_requestMorningTool($endPoint, $action, $skuVaultData);
        } else {
            $response = $this->_requestSkuVault($endPoint, $action, $skuVaultData);
        }

        $sales = json_decode($response, true);
        if (!empty($sales)) {
            //make it associative by Sale Id
            $sales = array_combine(
                array_column($sales, 'Id'),
                $sales
            );
        }

        //save the filters in the logs table
        //IMPORTANT: do this no matter of the log_requests setting
        $query = 'INSERT INTO ' . DB_TABLE_LOGS . "\n" .
            'SET date = NOW(),'. "\n" .
            '    event = "skuvault2NzoomSaleSync",'. "\n" .
            '    extra = "' . General::slashesEscape(json_encode($skuVaultData)) . '",'. "\n" .
            '    user  = "' . ($this->registry['currentUser'] ? $this->registry['currentUser']->get('id') : 0) . '"';
        $this->registry['db']->Execute($query);
        $logId = $this->registry['db']->insert_Id();
        $this->registry->set('skuvault_log_id', $logId, true);

        if (!empty($lastOmitted)) {
            $lastOmitted = is_array($lastOmitted) ? $lastOmitted : preg_split('#\s*,\s*#', $lastOmitted);
            //get the sales
            $skuVaultData = [
                'OrderIds' => $lastOmitted,
                'TenantToken' => $this->tenantToken,
                'UserToken' => $this->userToken

            ];
            $skuVaultDataJSON = json_encode($skuVaultData);
            $action = 'POST';
            $endPoint = '/sales/getSales';
            if (!empty($this->settings['morningtool_host'])) {
                //morning tool uses the same requests as SkuVault
                $response = $this->_requestMorningTool($endPoint, $action, $skuVaultData);
            } else {
                $response = $this->_requestSkuVault($endPoint, $action, $skuVaultData);
            }
            $omittedSales = json_decode($response, true);
            if (!empty($omittedSales['Sales'])) {
                foreach($omittedSales['Sales'] as $sale) {
                    //IMPORTANT: the getSales method lacks the ProcessedItems
                    $sale['ProcessedItems'] = $sale['ProcessedItems'] ?? array();
                    $sales[$sale['Id']] = $sale;
                }
            }
        }

        return $sales;
    }

    /**
     * Get the latest transactions
     *
     * @param array $params - arguments for the method, containing registry
     * @return array $transactions - sales as get from SKUVault
     */
    private function _getSKUVaultTransactions() {

        //get the automation history date
        $query = 'SELECT extra FROM ' . DB_TABLE_LOGS . ' WHERE event="skuvault2NzoomPickedProductsSync" ORDER BY id DESC LIMIT 1';
        $lastSKUFilters = $this->registry['db']->GetOne($query);

        $lastOmitted = array();
        if ($lastSKUFilters) {
            $lastSKUFilters = json_decode($lastSKUFilters, true);
            $lastSynced = $lastSKUFilters['ToDate'] ?? null;
            $lastOmitted = $lastSKUFilters['Omitted'] ?? array();
        }

        //initial data time of the import
        $initialDate = $this->settings['initial_datetime'] ??
            '2021-08-01 00:00:00';

        $now = new DateTime();

        $fromDateTime = new DateTime($lastSynced ??$initialDate);
        //Important: one week is the limit
        $toDateTime = clone $fromDateTime;
        $toDateTime->modify('+1 week');
        $toDateTime = min($now, $toDateTime);


        $dateFormat = 'Y-m-d H:i:sP';

        //get the sales
        $skuVaultData = [
            'FromDate' => $fromDateTime->format($dateFormat),
            'ToDate' => $toDateTime->format($dateFormat),
            'TransactionType' => 'Pick',
            'TenantToken' => $this->tenantToken,
            'UserToken' => $this->userToken

        ];
        $skuVaultDataJSON = json_encode($skuVaultData);
        $action = 'POST';
        $endPoint = '/inventory/getTransactions';
        if (!empty($this->settings['morningtool_host'])) {
            //morning tool uses the same requests as SkuVault
            $response = $this->_requestMorningTool($endPoint, $action, $skuVaultData);
        } else {
            $response = $this->_requestSkuVault($endPoint, $action, $skuVaultData);
        }

        $skuVaultTransactions = json_decode($response, true);


        //group the transactions
        $transactions = array();
        if (!$skuVaultTransactions['Transactions']) {
            foreach ($skuVaultTransactions['Transactions'] as $skuVaultTransaction) {
                if (empty($skuVaultTransaction['Context']) || empty($skuVaultTransaction['Context']['Type']) ||
                    empty($skuVaultTransaction['Context']['ID']) || $skuVaultTransaction['Context']['Type'] != 'Sale') {
                    continue;
                }
                $saleId = $skuVaultTransaction['Context']['ID'];

                if (!array_key_exists($saleId, $transactions)) {
                    $transactions[$saleId] = array();
                }
                $transactions[$saleId][] = $skuVaultTransaction;
            }
        }

        //save the filters in the logs table
        //IMPORTANT: do this no matter of the log_requests setting
        $query = 'INSERT INTO ' . DB_TABLE_LOGS . "\n" .
            'SET date = NOW(),'. "\n" .
            '    event = "skuvault2NzoomPickedProductsSync",'. "\n" .
            '    extra = "' . General::slashesEscape(json_encode($skuVaultData)) . '",'. "\n" .
            '    user  = "' . ($this->registry['currentUser'] ? $this->registry['currentUser']->get('id') : 0) . '"';
        $this->registry['db']->Execute($query);
        $logId = $this->registry['db']->insert_Id();
        $this->registry->set('skuvault_log_id', $logId, true);

        if (!empty($lastOmitted)) {
            foreach($lastOmitted as $transactionData) {
                $fromDate = new Datetime($transactionData['date']);
                $toDate = clone $fromDate;
                $toDate->modify('+1 second');
                //get the sales
                $skuVaultData = [
                    'SaleId' => $transactionData['saleId'],
                    'FromDate' => $fromDate->format($dateFormat),
                    'ToDate' => $toDate->format($dateFormat),
                    'TransactionType' => 'Pick',
                    'TenantToken' => $this->tenantToken,
                    'UserToken' => $this->userToken

                ];
                $action = 'POST';
                $endPoint = '/inventory/getTransactions';
                if (!empty($this->settings['morningtool_host'])) {
                    //morning tool uses the same requests as SkuVault
                    $response = $this->_requestMorningTool($endPoint, $action, $skuVaultData);
                } else {
                    $response = $this->_requestSkuVault($endPoint, $action, $skuVaultData);
                }
                $response = json_decode($response, true);
                if (!empty($response['Transactions'])) {
                    foreach ($response['Transactions'] as $transaction) {
                        if (empty($transaction['Context']) || empty($transaction['Context']['Type']) ||
                            empty($transaction['Context']['ID']) || $transaction['Context']['Type'] != 'Sale') {
                            continue;
                        }
                        $saleId = $transaction['Context']['ID'];

                        if (!array_key_exists($saleId, $transactions)) {
                            $transactions[$saleId] = array();
                        }
                        $transactions[$saleId][] = $transaction;
                    }
                }
            }
        }

        return $transactions;
    }

    /**
     * Convert SKU Sale status into nZoom status
     *
     * @return array $statusData - status and substatus
     */
    private function _getnZoomSalesStatus($skuStatus) {
        //check the status info
        $statusData = $this->registry['db']->GetRow(
            'SELECT id as substatus, status FROM ' . DB_TABLE_DOCUMENTS_STATUSES .
            ' WHERE doc_type=15 AND name="' . $skuStatus . '"'
        );

        if (empty($statusData)) {
            switch ($skuStatus) {
                case 'ReadyToShip':
                    $statusData = [
                        'status' => 'opened',
                        'substatus' => 44
                    ];
                    break;
            }
        }

        return $statusData;
    }

    /**
     * Checks whether document should be saved (true):
     * if document with SKV sale id does not exist
     * or if document exists and its checksum is different
     *
     * @param string $skvSaleId
     * @param string $checksum
     * @return bool tr
     */
    private function shouldSaveSale(string $skvSaleId, string $checksum): bool
    {

        //3401 - sale_id
        //3406 - skuvault_checksum
        $query = "SELECT d.id, dc1.value as saleId, dc2.value as checksum
                  FROM " . DB_TABLE_DOCUMENTS . " AS d
                  JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                    ON d.active = 1 AND
                       d.deleted = 0 AND
                       d.id = dc1.model_id AND
                       dc1.var_id = 3401 AND
                       dc1.value = '{$skvSaleId}'
                  LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                    ON dc2.model_id = dc1.model_id AND
                       dc2.var_id = 3406";
        $docData = $this->registry['db']->GetRow($query);
        if (empty($docData)) {
            //document does not exist, save the sale
            return true;
        } elseif ($docData['checksum'] != $checksum) {
            //document exist, but checksum is different
            return true;
        }

        return false;
    }

    /**
     * Sync data from SKUVault
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function skuvault2NzoomSaleSync($params) {

        /** @var ADODB_mysqli $db */
        $db = $this->registry['db'];

        //check settings
        if (empty($this->settings['skuvault_host']) ||
            empty($this->settings['skuvault_user']) ||
            empty($this->settings['skuvault_pass'])) {
            $this->_reportError('SKU Vault credentials are not set');
            return false;
        }

        //get the tokens
        if (!$this->_getSkuVaultTokens()) {
            $this->_reportError('SKU Vault credentials are invalid or cannot get SkuVault tokens');
            return false;
        }

        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        //get latest sales
        $skuSales = $this->_getSKUVaultSales();

        if (empty($skuSales)) {
            $this->updateAutomationHistory($this->metaData, null, 1);
            //save the automation history
            return true;
        }

        $this->stats = array(
            'added' => array(),
            'modified' => array(),
            'omitted' => array(),
            'skipped' => array(),
            'erred' => array(),
        );

        $docTypeId = $params['start_model_type'];
        $filters = array(
            'where' => array(
                'dt.id = ' . $docTypeId,
                'dt.active = 1'
            ),
            'sanitize' => true
        );
        $docType = Documents_Types::searchOne($this->registry, $filters);

        $edit_all = $this->registry->get('edit_all');
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);

        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');

        foreach($skuSales as $skuSale) {
            $checksum = $this->calcSkuVaultSaleChecksum($skuSale);
            if (!$this->shouldSaveSale($skuSale['Id'], $checksum)) {
                $this->stats['skipped'][] = $skuSale['Id'];
                continue;
            }

            $filters = array(
                'where' => array(
                    "d.type = {$docTypeId}",
                    "a__sale_id = '{$skuSale['Id']}'",
                )
            );
            $document = Documents::searchOne($this->registry, $filters);

            if ($document) {
                //document exists - EDIT
                $action = 'edit';
                $docAssocVars = $document->getAssocVars();

                if ($docAssocVars['skuvault_checksum']['value'] == $checksum) {
                    //checksum is the same, no modifications
                    //do not notify as error
                    $this->stats['skipped'][] = $skuSale['Id'];

                    continue;
                }

                /*
                if ($document->get('status') == 'closed') {
                    $errorMsg = "Failed to {$action} SaleId: {$skuSale['Id']}" .
                        (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '') .
                        '. The document has already been closed in nZoom!';
                    $this->_reportError($errorMsg);
                    $this->stats['erred'][] = $skuSale['Id'];
                    continue;
                    }
                */
            } else {
                //no document with this sale_id - ADD
                $action = 'add';

                // prepare the cut sku model
                $docProperties = array(
                    'type' => $docType->get('id'),
                    'generate_system_task' => $docType->get('generate_system_task'),
                    'direction' => $docType->get('direction'),
                    'type_name' => $docType->get('name'),
                    'name' => $docType->get('default_name'),
                    'department' => $docType->getDefaultDepartment(),
                    'group' => $docType->getDefaultGroup(),
                    'media' => $docType->get('media'),
                    'media_name' => $docType->get('media_name'),
                    'customer' => $docType->get('default_customer'),
                );
                $document = new Document($this->registry, $docProperties);
                $docAssocVars = $document->getAssocVars();
            }

            //check the status info
            $statusData = $this->_getnZoomSalesStatus($skuSale['Status']);
            if (empty($statusData)) {
                $errorMsg = "Failed to {$action} SaleId: {$skuSale['Id']}" .
                    (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '') .
                    '. No corresponding substatus in nZoom: ' . $skuSale['Status'];
                $this->_reportError($errorMsg);

                $this->stats['omitted'][] = $skuSale['Id'];
                $this->stats['erred'][] = $skuSale['Id'];

                continue;
            }

            $old_document = clone $document;

            $db->StartTrans();

            $saleDate = substr($skuSale['SaleDate'], 0, 10);
            $saleTime = substr($skuSale['SaleDate'], 11, 8);
            $document->set('date', $saleDate, true);

            //only by MorningTool provides ProductionDeadline
            if (array_key_exists('ProductionDeadline', $skuSale)) {
                $deadline =
                    substr($skuSale['ProductionDeadline'], 0, 10) . ' ' .
                    substr($skuSale['ProductionDeadline'], 11, 8);
            } else {
                //deadline is 5 working days after SaleDate
                $deadline = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $saleDate, 5) . ' ' . $saleTime;
            }
            $document->set('deadline', $deadline, true);

            //set notes (provided only by MorningTool)
            if (array_key_exists('Notes', $skuSale)) {
                $document->set('notes', $skuSale['Notes'], true);
            }

            $channels = array_combine(
                array_column($docAssocVars['channel_info']['options'], 'option_value'),
                array_column($docAssocVars['channel_info']['options'], 'label')
            );
            $chanelName = $skuSale['Channel'] ?? $skuSale['Marketplace'] ?? '';
            $channelId = array_keys($channels, $chanelName);
            $channelId = is_array($channelId) ? reset($channelId) : '';

            $docAssocVars['skuvault_checksum']['value'] = $checksum;
            $docAssocVars['sale_id']['value'] = $skuSale['Id'];
            $docAssocVars['sale_order_num']['value'] = $skuSale['ChannelId'];
            $docAssocVars['channel_info']['value'] = $channelId;
            $docAssocVars['market_place']['value'] = $skuSale['Marketplace'];
            //ToDO: define the currency
            $docAssocVars['group_table_2']['plain_values']['currency'] = 'USD';

            $items = array();
            if (!empty($skuSale['MerchantItems'])) {
                //this is derived from /getSalesByDate
                $items = $skuSale['MerchantItems'];
            } elseif (!empty($skuSale['SaleItems'])) {
                //this is derived from /getSales
                $items = $skuSale['SaleItems'];
            }

            if (!empty($skuSale['MerchantKits'])) {
                //this is derived from /getSalesByDate
                foreach($skuSale['MerchantKits'] as $kit) {
                    if (!empty($kit['Items'])) {
                        $kitPrice = $kit['UnitPrice']['a'] ?? 0;
                        $kitAllItemsCount = array_sum(array_column($kit['Items'], 'Quantity'));
                        foreach($kit['Items'] as $item) {
                            $itemPrice = $item['UnitPrice']['a'] ?? 0;
                            if ($itemPrice == 0) {
                                //recalculate the price as an equal part of the total quantity
                                $itemPrice = sprintf('%.2F', $kitPrice/$kitAllItemsCount);
                                $item['UnitPrice']['a'] = $itemPrice;
                            }
                            $items[] = $item;
                        }
                    }
                }
            } elseif (!empty($skuSale['SaleKits'])) {
                //this is derived from /getSales
                foreach($skuSale['SaleKits'] as $kit) {
                    if (!empty($kit['KitItems'])) {
                        $kitPrice = $kit['UnitPrice']['a'] ?? 0;
                        $kitAllItemsCount = array_sum(array_values($kit['KitItems']));
                        foreach($kit['KitItems'] as $itemSku => $itemQuantity) {
                            $itemPrice = sprintf('%.2F', $kitPrice/$kitAllItemsCount);
                            $item = array(
                                'Sku' => $itemSku,
                                'Quantity' => $itemQuantity,
                                'UnitPrice' => array(
                                    'a' => $itemPrice,
                                    's' => $kit['UnitPrice']['s'] ?? '',
                                ),
                            );
                            $items[] = $item;
                        }
                    }
                }
            }
            if (empty($items)) {
                $errorMsg = "Failed to {$action} SaleId: {$skuSale['Id']}" .
                    (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '') .
                    ". No MerchantItems/MerchantKits within the Sale!!!";
                $this->_reportError($errorMsg);

                $db->FailTrans();
                $db->CompleteTrans();

                $this->stats['omitted'][] = $skuSale['Id'];
                $this->stats['erred'][] = $skuSale['Id'];

                continue;
            }

            $pickedItems = array();
            if (!empty($skuSale['ProcessedItems'])) {
                $pickedItems = array_combine(
                    array_column($skuSale['ProcessedItems'], 'Sku'),
                    array_column($skuSale['ProcessedItems'], 'PickedQuantity')
                );
            }

            //used only in Morning Tool
            $productionItems = array();
            if (!empty($skuSale['ProductionItems'])) {
                $productionItems = array_combine(
                    array_column($skuSale['ProductionItems'], 'Sku'),
                    array_column($skuSale['ProductionItems'], 'Quantity')
                );
                $productionItemsUrgent = array_combine(
                    array_column($skuSale['ProductionItems'], 'Sku'),
                    array_column($skuSale['ProductionItems'], 'UrgentQuantity')
                );
            }

            //IMPORTANT: always replace the GT2, do not edit it
            //           the reason is that SkuVault might change the order items
            $newGIdx = 0;
            $gt2Values = array();
            foreach($items as $item) {
                $gIdx = -- $newGIdx;

                $nom = Nomenclatures::searchOne($this->registry, array('where' => array("n.code = '{$item['Sku']}'")));
                if (!is_object($nom)) {
                    $errorMsg = "Failed to {$action} SaleId: {$skuSale['Id']}" .
                        (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '') .
                        ". Nomenclature with SKU: '{$item['Sku']}' not found!!!";

                    $this->_reportError($errorMsg);

                    $db->FailTrans();
                    $db->CompleteTrans();

                    $this->stats['omitted'][] = $skuSale['Id'];
                    $this->stats['erred'][] = $skuSale['Id'];

                    continue 2;
                }

                $gt2Values[$gIdx]['article_id'] = $nom->get('id');
                $gt2Values[$gIdx]['article_name'] = $nom->get('name');
                $gt2Values[$gIdx]['article_measure_name'] = $nom->getVarValue('product_measure', true);
                $gt2Values[$gIdx]['article_code'] = $item['Sku'];

                if (!empty($this->settings['morningtool_host'])) {
                    //morning tool
                    //quantity contains production quantity
                    $gt2Values[$gIdx]['quantity'] = $productionItems[$item['Sku']] ?? 0;
                    //free_field1 contains ordered quantity
                    $gt2Values[$gIdx]['free_field1'] = $item['Quantity'];
                    //free_field5 contains urgent quantity
                    $gt2Values[$gIdx]['free_field5'] = $productionItemsUrgent[$item['Sku']] ?? 0;
                } else {
                    //skuvault
                    //both quantity and free_field1 contain ordered quantity!!!
                    $gt2Values[$gIdx]['quantity'] = $item['Quantity'];
                    $gt2Values[$gIdx]['free_field1'] = $item['Quantity'];
                }
                $gt2Values[$gIdx]['price'] = $item['UnitPrice']['a'];
                //picked quantity
                $gt2Values[$gIdx]['free_field3'] = $pickedItems[$item['Sku']] ?? '';
            }
            $docAssocVars['group_table_2']['values'] = $gt2Values;
            $this->registry['request']->set('gt2_requested', true, 'all', true);

            $document->set('vars', array_values($docAssocVars), true);
            $document->set('assoc_vars', $docAssocVars, true);

            if ($document->save()) {
                $document->sanitize();

                $filters = array('where' => array('d.id = ' . $document->get('id')));
                $new_document = Documents::searchOne($this->registry, $filters);
                $new_document->getVars();

                Documents_History::saveData($this->registry,
                                            array(
                                                'model' => $document,
                                                'action_type' => $action,
                                                'new_model' => $new_document,
                                                'old_model' => $old_document
                                            ));

                $this->executeActionAutomations($old_document, $new_document, $action);
                if ($db->HasFailedTrans()) {
                    $errorMsg = "Failed to {$action} (automations save failed) SaleId: {$skuSale['Id']}" .
                        (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '');
                    $this->_reportError($errorMsg);

                    $this->stats['erred'][] = $skuSale['Id'];
                }
            } else {
                $errorMsg = "Failed to {$action} SaleId: {$skuSale['Id']}" .
                    (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '');
                $this->_reportError($errorMsg);
                $this->_reportError($this->registry['messages']->getErrors());

                $this->stats['erred'][] = $skuSale['Id'];

                $this->registry['messages']->flush();
                $db->FailTrans();
            }

            if (!$db->HasFailedTrans() &&
                ($document->get('substatus') != $statusData['substatus'] ||  $document->get('status') != $statusData['status'])) {

                $old_document = clone $document;

                $worker = new Automations_Controller($this->registry);
                $status_params = array(
                    'model_id' => $document->get('id'),
                    'module' => 'documents',
                    'model' => $document,
                    'new_status' => $statusData['status'],
                    'new_substatus' => $statusData['substatus']
                );

                // Saves the new status of the document
                if ($worker->status($status_params)) {
                    $filters = array(
                        'where' => array('d.id = ' . $document->get('id')),
                        'model_lang' => $document->get('model_lang'),
                        'skip_assignments' => true,
                        'skip_permissions_check' => true
                    );
                    $new_document = Documents::searchOne($this->registry, $filters);

                    //run the automations
                    $this->executeActionAutomations($old_document, $new_document, 'status');

                    if ($db->HasFailedTrans()) {
                        $errorMsg = "Failed to {$action} (automations status failed) SaleId: {$skuSale['Id']}" .
                            (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '');
                        $this->_reportError($errorMsg);

                        $this->stats['erred'][] = $skuSale['Id'];
                    }
                } else {
                    $errorMsg = "Failed to {$action} (status change failed) SaleId: {$skuSale['Id']}" .
                        (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '');
                    $this->_reportError($errorMsg);

                    $this->stats['erred'][] = $skuSale['Id'];

                    $db->FailTrans();
                }
            }

            $result = !$db->HasFailedTrans();

            $db->CompleteTrans();

            if ($result) {
                $statsSection = $action == 'edit' ? 'modified' : 'added';
                $this->stats[$statsSection][] = $skuSale['Id'];

                $this->updateAutomationHistory($params, $document, $result);
            }
        }

        $this->registry->set('get_old_vars', $edit_all, true);
        $this->registry->set('edit_all', $get_old_vars, true);

        if (!empty($this->settings['log_stats'])) {
            $log_message = "STATS:\n " . json_encode($this->stats, JSON_PRETTY_PRINT);
            General::log($this->registry, __METHOD__, $log_message);
        }

        //log the omitted
        if ($this->registry['skuvault_log_id'] && !empty($this->stats['omitted'])) {
            $query = 'SELECT extra FROM ' . DB_TABLE_LOGS .
                ' WHERE `event`="skuvault2NzoomSaleSync" AND id="' . $this->registry['skuvault_log_id'] . '"';
            $lastLog = $this->registry['db']->getOne($query);
            if ($lastLog) {
                $updateLog = json_decode($lastLog, true);
                $updateLog['Omitted'] = $this->stats['omitted'];
                $query = 'UPDATE ' . DB_TABLE_LOGS .
                    ' SET extra="' . General::slashesEscape(json_encode($updateLog)) . '"' .
                    ' WHERE `event`="skuvault2NzoomSaleSync" AND id="' . $this->registry['skuvault_log_id'] . '"';
                $this->registry['db']->Execute($query);
            }
        }

        if (empty($this->executionErrors)) {
            return true;
        } else {
            $log_message = "Errors:\n" . json_encode($this->executionErrors, JSON_PRETTY_PRINT);
            General::log($this->registry, __METHOD__, $log_message);
            return false;
        }
    }

    /**
     * Sync picked quantities from SKUVault
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function skuvault2NzoomPickedProductsSync($params) {

        /** @var ADODB_mysqli $db */
        $db = $this->registry['db'];

        //check settings
        if (empty($this->settings['skuvault_host']) ||
            empty($this->settings['skuvault_user']) ||
            empty($this->settings['skuvault_pass'])) {
            $this->_reportError('SKU Vault credentials are not set');
            return false;
        }

        //get the tokens
        if (!$this->_getSkuVaultTokens()) {
            $this->_reportError('SKU Vault credentials are invalid or cannot get SkuVault tokens');
            return false;
        }

        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        //get latest transactions
        $skuTransactions = $this->_getSKUVaultTransactions();

        if (empty($skuTransactions)) {
            $this->updateAutomationHistory($this->metaData, null, 1);
            //save the automation history
            return true;
        }

        $this->stats = array(
            'omitted' => array(),
            'modified' => array(),
            'erred' => array(),
        );

        $docTypeId = $params['start_model_type'];

        $edit_all = $this->registry->get('edit_all');
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);

        $this->registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');

        //the transactions are grouped by saleId in _getSKUVaultTransactions
        foreach($skuTransactions as $saleId => $transactions) {
            $filters = array(
                'where' => array(
                    "d.type = {$docTypeId}",
                    "a__sale_id = '{$saleId}'",
                )
            );
            $document = Documents::searchOne($this->registry, $filters);

            if (!$document) {
                $errorMsg = "Failed to find Sale with saleId: {$saleId}";
                $this->_reportError($errorMsg);

                foreach($transactions as $transaction) {
                    $this->stats['omitted'][] = array(
                        'saleId' => $saleId,
                        'date' => $transaction['TransactionDate'],
                    );
                }
                $this->stats['erred'][] = $saleId;

                continue;
            }

            //document exists - EDIT
            $action = 'edit';

            /*
            if ($document->get('status') == 'closed') {
                $errorMsg = "Failed to {$action} SaleId: {$saleId}" .
                    (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '') .
                    '. The document has already been closed in nZoom!';
                $this->_reportError($errorMsg);

                $this->stats['erred'][] = $saleId;

                continue;
            }
            */


            $docAssocVars = $document->getAssocVars();
            $gt2Values = $docAssocVars['group_table_2']['values'];
            $passedTransactions = $docAssocVars['skuvault_transactions']['value'];
            //prepare row ids by Sku codes (article_code)
            $gt2RowIdsBySku = array_combine(
                array_column($gt2Values, 'article_code'),
                array_keys($gt2Values)
            );
            $old_document = clone $document;

            $db->StartTrans();

            $transactionCount = 0;
            foreach($transactions as $transaction) {
                if (!array_key_exists($transaction['Sku'], $gt2RowIdsBySku)) {
                    $errorMsg = "Failed to {$action} SaleId: {$saleId}" .
                        (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '') .
                        ". Could not find row with SKU: {$transaction['Sku']}" .
                        ". picked: {$transaction['TransactionDate']}!";
                    $this->_reportError($errorMsg);

                    //ToDo: when the row is not found, the transaction is mot likely outdated
                    //$this->stats['omitted'][] = array(
                    //    'saleId' => $saleId,
                    //    'date' => $transaction['TransactionDate'],
                    //);
                    $this->stats['erred'][] = $saleId;

                    $db->FailTrans();
                    $db->CompleteTrans();
                    continue 2;
                }

                //validate to check if the transaction has already been processed in this order
                //the transaction has no unique ID, use the date as unique key
                $transactionDate = $transaction['TransactionDate'];
                $transactioUser = $transaction['User'];
                if (strpos($passedTransactions, $transactionDate) !== false) {
                    $errorMsg = "Skipped transaction {$transactionDate} for Document id: {$document->get('id')} - already imported!";
                    //ToDo: this should not be reported
                    //$this->_reportError($errorMsg);
                    //$this->stats['erred'][] = $saleId;
                    continue;
                }

                $gIdx = $gt2RowIdsBySku[$transaction['Sku']];

                //change ONLY the picked quantity
                $gt2Values[$gIdx]['free_field3'] = floatval($gt2Values[$gIdx]['free_field3']) + $transaction['Quantity'];
                if ($gt2Values[$gIdx]['free_field3'] > floatval($gt2Values[$gIdx]['quantity'])) {
                    $errorMsg = "Skipped transaction {$transactionDate} for Document id: {$document->get('id')} - picked exceeds quantity!";
                    //ToDo: this should not be reported
                    //$this->_reportError($errorMsg);
                    //$this->stats['erred'][] = $saleId;
                    continue;
                }
                $passedTransactions .= "\n{$transactioUser}: {$transactionDate}";
                $transactionCount++;
            }

            if ($transactionCount > 0) {
                $docAssocVars['group_table_2']['values'] = $gt2Values;
                $docAssocVars['skuvault_transactions']['value'] = trim($passedTransactions);
                $this->registry['request']->set('gt2_requested', true, 'all', true);

                $document->set('vars', array_values($docAssocVars), true);
                $document->set('assoc_vars', $docAssocVars, true);

                if ($document->save()) {
                    $document->sanitize();

                    $filters = array('where' => array('d.id = ' . $document->get('id')));
                    $new_document = Documents::searchOne($this->registry, $filters);
                    $new_document->getVars();

                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $document,
                            'action_type' => $action,
                            'new_model' => $new_document,
                            'old_model' => $old_document
                        )
                    );

                    $this->executeActionAutomations($old_document, $new_document, $action);

                    if ($db->HasFailedTrans()) {
                        $errorMsg = "Failed to {$action} (automations save failed) SaleId: {$saleId}" .
                            (($action == 'edit') ?  " (Document id: {$document->get('id')})" : '');
                        $this->_reportError($errorMsg);

                        $this->stats['erred'][] = $saleId;
                    }
                }
            }

            $result = !$db->HasFailedTrans();

            $db->CompleteTrans();

            if ($result) {
                $this->stats['modified'][] = $saleId;
                $this->updateAutomationHistory($params, $document, $result);
            }
        }

        $this->registry->set('get_old_vars', $edit_all, true);
        $this->registry->set('edit_all', $get_old_vars, true);

        if (!empty($this->settings['log_stats'])) {
            $log_message = "STATS:\n " . json_encode($this->stats, JSON_PRETTY_PRINT);
            General::log($this->registry, __METHOD__, $log_message);
        }

        //log the omitted
        if ($this->registry['skuvault_log_id'] && !empty($this->stats['omitted'])) {
            $query = 'SELECT extra FROM ' . DB_TABLE_LOGS .
                ' WHERE `event`="skuvault2NzoomPickedProductsSync" AND id="' . $this->registry['skuvault_log_id'] . '"';
            $lastLog = $this->registry['db']->getOne($query);
            if ($lastLog) {
                $updateLog = json_decode($lastLog, true);
                $updateLog['Omitted'] = $this->stats['omitted'];
                $query = 'UPDATE ' . DB_TABLE_LOGS .
                    ' SET extra="' . General::slashesEscape(json_encode($updateLog)) . '"' .
                    ' WHERE `event`="skuvault2NzoomPickedProductsSync" AND id="' . $this->registry['skuvault_log_id'] . '"';
                $this->registry['db']->Execute($query);
            }
        }

        if (empty($this->executionErrors)) {
            return true;
        } else {
            $log_message = "Errors:\n" . json_encode($this->executionErrors, JSON_PRETTY_PRINT);
            General::log($this->registry, __METHOD__, $log_message);
            return false;
        }
    }

    /**
     * Get Channel Advisor using a refresh token
     *
     * @see https://dev.skuvault.com/reference#gettokens
     * @return bool - result of the operation
     */
    private function _getSkuVaultTokens() {
        if ($this->tenantToken && $this->userToken) {
            return true;
        }

        $user = $this->settings['skuvault_user'];
        $pass = General::decrypt($this->settings['skuvault_pass'], $user, 'xtea');
        $data = [
            'Email' => $user,
            'Password' => $pass,
        ];

        //set to no log this request!
        $log_requests = $this->settings['log_requests']??'';
        $this->settings['log_requests'] = false;

        $response = $this->_requestSkuVault('/gettokens', 'POST', $data);
        $response = json_decode($response, true);

        //restore the log settings
        $this->settings['log_requests'] = $log_requests;

        if (empty($response['errors']) &&
            !empty($response['TenantToken']) &&
            !empty($response['UserToken'])) {
            $this->tenantToken = $response['TenantToken'];
            $this->userToken = $response['UserToken'];
        } else {
            return false;
        }

        return true;
    }

    /**
     * Request SKU Vault and return the response
     *
     * @param string $endpointAddress - address of the API
     * @param string $api_token - API token
     * @param string $action - REST method: GET, POST, PUT, DELETE
     * @param array $json - the request JSON as array
     * @param string $content_type - content type of the header
     * @return string $response - the response string in JSON
     */
    public function _requestSkuVault($endpointAddress, $action = 'GET', $json = '', $content_type = 'application/json') {
        $json = json_encode($json);

        $url = $this->settings['skuvault_host'] . $endpointAddress;
        $ch = curl_init($url);
        $headers = [
            'Accept: application/json',
            'Content-Type: ' . $content_type,
            'Content-Length: ' . ($content_type == 'application/json' ? mb_strlen($json) : strlen($json)),
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $action);
        if ($json) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $error_number = curl_errno($ch);
        curl_close($ch);

        if ($error_number) {
            $response = '{"errors": ' . $error_number . ':' . $error . '}';
        }

        if (!empty($this->settings['log_requests'])) {
            $log_message = "SKUVault Endpoint:\n{$url}\nAction: {$action}\n\nSKUVault Request:\n {$json}\n\nSKUVault Response:\n {$response}";
            General::log($this->registry, __METHOD__, $log_message);
        }

        return $response;
    }

    /**
     * Request Morning Tool and return the response
     *
     * @param string $endpointAddress - address of the API
     * @param string $api_token - API token
     * @param string $action - REST method: GET, POST, PUT, DELETE
     * @param array $json - the request JSON as array
     * @param string $content_type - content type of the header
     * @return string $response - the response string in JSON
     */
    public function _requestMorningTool($endpointAddress, $action = 'GET', $json = '', $content_type = 'application/json') {
        $json = json_encode($json);

        $url = $this->settings['morningtool_host'] . $endpointAddress;
        $ch = curl_init($url);
        $headers = [
            'Accept: application/json',
            'Content-Type: ' . $content_type,
            'Content-Length: ' . ($content_type == 'application/json' ? mb_strlen($json) : strlen($json)),
            'Secret-Token: ' . $this->settings['morningtool_token']
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $action);
        if ($json) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $error_number = curl_errno($ch);
        curl_close($ch);

        if ($error_number) {
            $response = '{"errors": ' . $error_number . ':' . $error . '}';
        }

        if (!empty($this->settings['log_requests'])) {
            $log_message = "MorningTool Endpoint:\n{$url}\nAction: {$action}\n\nMorningTool Request:\n {$json}\n\nMorningTool Response:\n {$response}";
            General::log($this->registry, __METHOD__, $log_message);
        }


        return $response;
    }

    /**
     * Calculate COGM
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function calcCOGM($params) {
        // Default result: true
        $result = true;

        // TODO: This is a fast fix, without this it happens to not mark the automation as executed and not sending mail with the errors/warnings
        $this->isExecuted = true;

        /*
         * Prepare some basics
         */
        $settings = $this->settings;
        $registry = $this->registry;
        $db = $registry['db'];

        // Get the document
        $document = $params['model'];

        // Get the GT2
        $registry->set('get_old_vars', true, true);
        $document->getVars();
        $registry->set('get_old_vars', true, true);
        $gt2 = $document->getGT2Vars();

        // If the GT2 is not empty
        if (is_array($gt2['values']) && !empty($gt2['values'])) {
            // Get the SKUs IDs
            $sku_ids_list = implode(', ', array_filter(array_column($gt2['values'], 'article_id')));
            // If there are any SKUs IDs
            if ($sku_ids_list) {
                // The result is false until the change is done successfully
                $result = false;

                // Get products real quantity weight from the "Bill of materials"
                $query = "
                    SELECT gd.article_id AS product_id,
                        SUM(gd.quantity) AS real_quantity,
                        n.average_weighted_delivery_price
                                         AS average_weighted_delivery_price
                      FROM " . DB_TABLE_DOCUMENTS . " AS d
                      JOIN " . DB_TABLE_FINANCE_REASONS_RELATIVES . " AS frr
                        ON (d.id = {$document->get('id')}
                          AND frr.link_to_model_name = 'Document'
                          AND frr.link_to = d.id
                          AND frr.parent_model_name = 'Finance_Incomes_Reason')
                      JOIN " . DB_TABLE_FINANCE_INCOMES_REASONS . " AS fir
                        ON (fir.id = frr.parent_id
                          AND fir.type = {$settings['fir_type_bill_of_materials']}
                          AND !fir.annulled
                          AND fir.active
                          AND fir.status = 'finished')
                      JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                        ON (gd.model = 'Finance_Incomes_Reason'
                          AND gd.model_id = fir.id)
                      JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        ON (n.id = gd.article_id)
                      GROUP BY product_id";
                $products = $db->GetAssoc($query);
                if ($products) {
                    // $query = "
                    //     SELECT tmp.product_id          AS product_id,
                    //         SUM(tmp.produced_quantity) AS produced_quantity
                    //       FROM (
                    //         SELECT n1.id AS product_id,
                    //             CAST(gd.free_field3 AS DECIMAL(25, 6)) * CAST(nc1.value AS DECIMAL(25, 6))
                    //                      AS produced_quantity
                    //           FROM " . DB_TABLE_DOCUMENTS . " AS d
                    //           JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                    //             ON (d.id = {$document->get('id')}
                    //               AND gd.model = 'Document'
                    //               AND gd.model_id = d.id)
                    //           JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                    //             ON (n.id = gd.article_id
                    //               AND n.type != {$settings['nom_type_semi_product']})
                    //           JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    //             ON (fm.model = 'Nomenclature'
                    //               AND fm.model_type = n.type
                    //               AND fm.name = 'product_id')
                    //           JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    //             ON (nc.model_id = n.id
                    //               AND nc.var_id = fm.id
                    //               AND nc.lang = ''
                    //               AND nc.value != '')
                    //           JOIN " . DB_TABLE_NOMENCLATURES . " AS n1
                    //             ON (n1.id = nc.value)
                    //           JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                    //             ON (fm1.model = 'Nomenclature'
                    //               AND fm1.model_type = n.type
                    //               AND fm1.name = 'product_num')
                    //           JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                    //             ON (nc1.model_id = n.id
                    //               AND nc1.var_id = fm1.id
                    //               AND nc1.num = nc.num
                    //               AND nc1.lang = ''
                    //               AND nc1.value != '')
                    //         UNION ALL
                    //         SELECT n.id AS product_id,
                    //             CAST(gd.free_field3 AS DECIMAL(25, 6))
                    //                     AS produced_quantity
                    //           FROM " . DB_TABLE_DOCUMENTS . " AS d
                    //           JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                    //             ON (d.id = {$document->get('id')}
                    //               AND gd.model = 'Document'
                    //               AND gd.model_id = d.id)
                    //           JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                    //             ON (n.id = gd.article_id
                    //               AND n.type = {$settings['nom_type_semi_product']})
                    //       ) AS tmp
                    //       GROUP BY tmp.product_id";
                    $query = "
                        SELECT n1.id AS product_id,
                            SUM(CAST(gd.free_field3 AS DECIMAL(25, 6)) * CAST(nc1.value AS DECIMAL(25, 6)))
                                     AS produced_quantity
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                            ON (d.id = {$document->get('id')}
                              AND gd.model = 'Document'
                              AND gd.model_id = d.id)
                          JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                            ON (n.id = gd.article_id)
                          JOIN " . DB_TABLE_FIELDS_META . " AS fm
                            ON (fm.model = 'Nomenclature'
                              AND fm.model_type = n.type
                              AND fm.name = 'product_id')
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                            ON (nc.model_id = n.id
                              AND nc.var_id = fm.id
                              AND nc.lang = ''
                              AND nc.value != '')
                          JOIN " . DB_TABLE_NOMENCLATURES . " AS n1
                            ON (n1.id = nc.value)
                          JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                            ON (fm1.model = 'Nomenclature'
                              AND fm1.model_type = n.type
                              AND fm1.name = 'product_num')
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                            ON (nc1.model_id = n.id
                              AND nc1.var_id = fm1.id
                              AND nc1.num = nc.num
                              AND nc1.lang = ''
                              AND nc1.value != '')
                          GROUP BY product_id";
                    $products_produced_quantity = $db->GetAssoc($query);
                    $products = array_intersect_key($products, $products_produced_quantity);
                    foreach ($products_produced_quantity as $product_id => $produced_quantity) {
                        if (!array_key_exists($product_id, $products)) {
                            continue;
                        }

                        $produced_quantity = floatval($produced_quantity);
                        if ($produced_quantity > 0) {
                            $products[$product_id]['real_quantity_weight'] = floatval($products[$product_id]['real_quantity']) / $produced_quantity;
                        } else {
                            unset($products[$product_id]);
                        }
                    }
                }
                if ($products) {
                    // Get some Child SKU fields
                    $query = "
                        SELECT name, id
                          FROM " . DB_TABLE_FIELDS_META . "
                          WHERE model = 'Nomenclature'
                            AND model_type = {$settings['nom_type_child_sku']}
                            AND name IN (
                                'model_clothes_id',
                                'product_id',
                                'product_num'
                            )";
                    $child_sku_fields = $db->GetAssoc($query);
                    $query = "
                        SELECT name, id
                          FROM " . DB_TABLE_FIELDS_META . "
                          WHERE model = 'Nomenclature'
                            AND model_type = {$settings['nom_type_semi_product']}
                            AND name IN (
                                'model_clothes_id',
                                'product_id',
                                'product_num'
                            )";
                    $semi_product_fields = $db->GetAssoc($query);

                    // If the document type is "Production order" or "Third party order"
                    if (in_array($document->get('type'), [
                        $settings['doc_type_production_order'],
                        $settings['doc_type_third_party_products_delivery_order']
                    ])) {
                        // Get Child SKUs data
                        // $query = "
                        //     SELECT gd.id  AS gt2_row_id,
                        //         nc.value  AS product_id,
                        //         nc1.value AS product_recipe_quantity,
                        //         nc2.value AS design_id
                        //       FROM " . DB_TABLE_DOCUMENTS . " AS d
                        //       JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                        //         ON (d.id = {$document->get('id')}
                        //             AND gd.model = 'Document'
                        //             AND gd.model_id = d.id
                        //             AND CAST(gd.free_field3 AS DECIMAL(25, 6)) > 0)
                        //       JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        //         ON (n.id = gd.article_id
                        //           AND n.type != {$settings['nom_type_semi_product']})
                        //       JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                        //         ON (nc.model_id = n.id
                        //           AND nc.var_id = {$child_sku_fields['product_id']}
                        //           AND nc.lang = '')
                        //       JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                        //         ON (nc1.model_id = n.id
                        //           AND nc1.var_id = {$child_sku_fields['product_num']}
                        //           AND nc1.num = nc.num
                        //           AND nc1.lang = ''
                        //           AND nc1.value != '')
                        //       JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                        //         ON (nc2.model_id = n.id
                        //           AND nc2.var_id = {$child_sku_fields['model_clothes_id']}
                        //           AND nc2.num = 1
                        //           AND nc2.lang = ''
                        //           AND nc2.value != '')
                        //     UNION ALL
                        //     SELECT gd.id  AS gt2_row_id,
                        //         n.id      AS product_id,
                        //         1         AS product_recipe_quantity,
                        //         nc2.value AS design_id
                        //       FROM " . DB_TABLE_DOCUMENTS . " AS d
                        //       JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                        //         ON (d.id = {$document->get('id')}
                        //             AND gd.model = 'Document'
                        //             AND gd.model_id = d.id
                        //             AND CAST(gd.free_field3 AS DECIMAL(25, 6)) > 0)
                        //       JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        //         ON (n.id = gd.article_id
                        //           AND n.type = {$settings['nom_type_semi_product']})
                        //       JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                        //         ON (nc2.model_id = n.id
                        //           AND nc2.var_id = {$semi_product_fields['model_clothes_id']}
                        //           AND nc2.num = 1
                        //           AND nc2.lang = ''
                        //           AND nc2.value != '')";
                        // $gt2_recipe_rows_raw = $db->GetAll($query);
                        $query = "
                            SELECT gd.id  AS gt2_row_id,
                                nc.value  AS product_id,
                                nc1.value AS product_recipe_quantity,
                                nc2.value AS design_id
                              FROM " . DB_TABLE_DOCUMENTS . " AS d
                              JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                                ON (d.id = {$document->get('id')}
                                    AND gd.model = 'Document'
                                    AND gd.model_id = d.id
                                    AND CAST(gd.free_field3 AS DECIMAL(25, 6)) > 0)
                              JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                                ON (n.id = gd.article_id)
                              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                                ON (nc.model_id = n.id
                                  AND nc.var_id IN ({$child_sku_fields['product_id']}, {$semi_product_fields['product_id']})
                                  AND nc.lang = '')
                              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                                ON (nc1.model_id = n.id
                                  AND nc1.var_id IN ({$child_sku_fields['product_num']}, {$semi_product_fields['product_num']})
                                  AND nc1.num = nc.num
                                  AND nc1.lang = ''
                                  AND nc1.value != '')
                              LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                                ON (nc2.model_id = n.id
                                  AND nc2.var_id IN ({$child_sku_fields['model_clothes_id']}, {$semi_product_fields['model_clothes_id']})
                                  AND nc2.num = 1
                                  AND nc2.lang = '')";
                        $gt2_recipe_rows_raw = $db->GetAll($query);

                        $query = "
                            SELECT gd.article_id AS child_sku_id,
                                gd.price         AS child_sku_cogm
                              FROM " . DB_TABLE_DOCUMENTS . " AS d
                              JOIN " . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr
                                ON (d.id = {$document->get('id')}
                                  AND dr.parent_model_name = 'Document'
                                  AND dr.parent_id = d.id
                                  AND dr.link_to_model_name = 'Document')
                              JOIN " . DB_TABLE_DOCUMENTS_RELATIVES . " AS dr1
                                ON (dr1.link_to_model_name = 'Document'
                                  AND dr1.link_to = dr.link_to
                                  AND dr1.parent_model_name = 'Document')
                              JOIN " . DB_TABLE_DOCUMENTS . " AS d1
                                ON (d1.id = dr1.parent_id
                                  AND d1.type = {$settings['doc_type_cut_order']}
                                  AND NOT d1.deleted
                                  AND d1.active)
                              JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                                ON (gd.model = 'Document'
                                  AND gd.model_id = d1.id)
                              JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                                ON (n.id = gd.article_id
                                  AND n.type != {$settings['nom_type_semi_product']})";
                        $cut_skus_cogm = $db->GetAssoc($query);

                        $gt2_rows_data = [];
                        $designs_ids = [];
                        foreach ($gt2_recipe_rows_raw as $gt2_recipe_row) {
                            if (!array_key_exists($gt2_recipe_row['product_id'], $products)) {
                                continue;
                            }

                            if (!array_key_exists($gt2_recipe_row['gt2_row_id'], $gt2_rows_data)) {
                                $gt2_rows_data[$gt2_recipe_row['gt2_row_id']] = [
                                    'total_cost' => 0,
                                    'design_id'  => $gt2_recipe_row['design_id']
                                ];
                                if ($gt2_recipe_row['design_id']) {
                                    $designs_ids[$gt2_recipe_row['design_id']] = $gt2_recipe_row['design_id'];
                                }
                            }

                            $product_recipe_quantity = floatval($gt2_recipe_row['product_recipe_quantity']);
                            $product_real_quantity_weight = $products[$gt2_recipe_row['product_id']]['real_quantity_weight'];

                            if (array_key_exists($gt2_recipe_row['product_id'], $cut_skus_cogm)) {
                                $product_price = floatval($cut_skus_cogm[$gt2_recipe_row['product_id']]);
                            } else {
                                $product_price = floatval($products[$gt2_recipe_row['product_id']]['average_weighted_delivery_price']);
                            }

                            $row_cost = $product_recipe_quantity * $product_real_quantity_weight * $product_price;
                            $gt2_rows_data[$gt2_recipe_row['gt2_row_id']]['total_cost'] += $row_cost;
                        }
                    } else if ($document->get('type') == $settings['doc_type_cut_order']) {
                        // If the document type is Cut order
                        // Get Cut SKUs data
                        $query = "
                            SELECT name, id
                              FROM " . DB_TABLE_FIELDS_META . "
                              WHERE model = 'Nomenclature'
                                AND model_type = {$settings['nom_type_cut_sku']}
                                AND name IN (
                                    'product_id',
                                    'product_num'
                                )";
                        $cut_sku_fields = $db->GetAssoc($query);
                        // $query = "
                        //     SELECT gd.id  AS gt2_row_id,
                        //         nc.value  AS product_id,
                        //         nc1.value AS product_recipe_quantity
                        //       FROM " . DB_TABLE_DOCUMENTS . " AS d
                        //       JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                        //         ON (d.id = {$document->get('id')}
                        //             AND gd.model = 'Document'
                        //             AND gd.model_id = d.id
                        //             AND CAST(gd.free_field3 AS DECIMAL(25, 6)) > 0)
                        //       JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        //         ON (n.id = gd.article_id
                        //           AND n.type != {$settings['nom_type_semi_product']})
                        //       JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                        //         ON (nc.model_id = n.id
                        //           AND nc.var_id = {$cut_sku_fields['product_id']}
                        //           AND nc.lang = '')
                        //       JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                        //         ON (nc1.model_id = n.id
                        //           AND nc1.var_id = {$cut_sku_fields['product_num']}
                        //           AND nc1.num = nc.num
                        //           AND nc1.lang = ''
                        //           AND nc1.value != '')
                        //     UNION ALL
                        //     SELECT gd.id AS gt2_row_id,
                        //         n.id     AS product_id,
                        //         1        AS product_recipe_quantity
                        //       FROM " . DB_TABLE_DOCUMENTS . " AS d
                        //       JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                        //         ON (d.id = {$document->get('id')}
                        //             AND gd.model = 'Document'
                        //             AND gd.model_id = d.id
                        //             AND CAST(gd.free_field3 AS DECIMAL(25, 6)) > 0)
                        //       JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                        //         ON (n.id = gd.article_id
                        //           AND n.type = {$settings['nom_type_semi_product']})";
                        // $gt2_recipe_rows_raw = $db->GetAll($query);
                        $query = "
                            SELECT gd.id  AS gt2_row_id,
                                nc.value  AS product_id,
                                nc1.value AS product_recipe_quantity
                              FROM " . DB_TABLE_DOCUMENTS . " AS d
                              JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                                ON (d.id = {$document->get('id')}
                                    AND gd.model = 'Document'
                                    AND gd.model_id = d.id
                                    AND CAST(gd.free_field3 AS DECIMAL(25, 6)) > 0)
                              JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                                ON (n.id = gd.article_id)
                              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                                ON (nc.model_id = n.id
                                  AND nc.var_id IN({$cut_sku_fields['product_id']}, {$semi_product_fields['product_id']})
                                  AND nc.lang = '')
                              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                                ON (nc1.model_id = n.id
                                  AND nc1.var_id IN ({$cut_sku_fields['product_num']}, {$semi_product_fields['product_num']})
                                  AND nc1.num = nc.num
                                  AND nc1.lang = ''
                                  AND nc1.value != '')";
                        $gt2_recipe_rows_raw = $db->GetAll($query);
                        $gt2_rows_data = [];
                        foreach ($gt2_recipe_rows_raw as $gt2_recipe_row) {
                            if (!array_key_exists($gt2_recipe_row['product_id'], $products)) {
                                continue;
                            }

                            if (!array_key_exists($gt2_recipe_row['gt2_row_id'], $gt2_rows_data)) {
                                $gt2_rows_data[$gt2_recipe_row['gt2_row_id']] = [
                                    'total_cost' => 0,
                                    'design_id'  => '',
                                ];
                            }

                            $product_recipe_quantity = floatval($gt2_recipe_row['product_recipe_quantity']);
                            $product_real_quantity_weight = $products[$gt2_recipe_row['product_id']]['real_quantity_weight'];
                            $product_price = floatval($products[$gt2_recipe_row['product_id']]['average_weighted_delivery_price']);
                            $row_cost = $product_recipe_quantity * $product_real_quantity_weight * $product_price;
                            $gt2_rows_data[$gt2_recipe_row['gt2_row_id']]['total_cost'] += $row_cost;
                        }

                        // If there's any Cut SKUs data
                        if ($gt2_rows_data) {
                            // Get the Cut SKUs designs IDs
                            $query = "
                                SELECT STRAIGHT_JOIN
                                    gd.id     AS gt2_row_id,
                                    nc1.value AS design_id
                                  FROM " . DB_TABLE_DOCUMENTS . " AS d
                                  JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                                    ON (d.id = {$document->get('id')}
                                        AND gd.model = 'Document'
                                        AND gd.model_id = d.id)
                                  JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                                    ON (n.id = gd.article_id
                                      AND n.type != {$settings['nom_type_semi_product']})
                                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                                    ON (nc.var_id = {$child_sku_fields['product_id']}
                                      AND nc.lang = ''
                                      AND nc.value = gd.article_id)
                                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                                    ON (nc1.model_id = nc.model_id
                                      AND nc1.var_id = {$child_sku_fields['model_clothes_id']}
                                      AND nc1.num = 1
                                      AND nc1.lang = ''
                                      AND nc1.value != '')
                                UNION
                                SELECT STRAIGHT_JOIN
                                    gd.id     AS gt2_row_id,
                                    nc1.value AS design_id
                                  FROM " . DB_TABLE_DOCUMENTS . " AS d
                                  JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                                    ON (d.id = {$document->get('id')}
                                        AND gd.model = 'Document'
                                        AND gd.model_id = d.id)
                                  JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                                    ON (n.id = gd.article_id
                                      AND n.type = {$settings['nom_type_semi_product']})
                                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                                    ON (nc1.model_id = n.id
                                      AND nc1.var_id = {$semi_product_fields['model_clothes_id']}
                                      AND nc1.num = 1
                                      AND nc1.lang = ''
                                      AND nc1.value != '')";
                            $gt2_rows_designs_ids = $db->GetAssoc($query);
                            $designs_ids = [];
                            $gt2_rows_data = array_intersect_key($gt2_rows_data, $gt2_rows_designs_ids);
                            foreach ($gt2_rows_designs_ids as $gt2_row_id => $design_id) {
                                if (array_key_exists($gt2_row_id, $gt2_rows_data)) {
                                    $gt2_rows_data[$gt2_row_id]['design_id'] = $design_id;
                                    $designs_ids[$design_id] = $design_id;
                                }
                            }
                        }
                    }

                    if (!empty($gt2_rows_data)) {
                        /*
                         * Get designs data
                         */
                        $designs_data = [];
                        if (!empty($designs_ids)) {
                            // Get some designs fields
                            $query = "
                                SELECT name, id
                                  FROM " . DB_TABLE_FIELDS_META . "
                                  WHERE model = 'Nomenclature'
                                    AND model_type = {$settings['nom_type_designs']}
                                    AND name IN (
                                      'administrative_costs',
                                      'unit_complexity')";
                            $design_fields = $db->GetAssoc($query);
                            // Get the designs IDs
                            $designs_ids_list = implode(', ', $designs_ids);
                            // Get the designs data
                            $query = "
                                SELECT n.id   AS design_id,
                                    nc4.value AS overhead,
                                    nc5.value AS unit_complexity
                                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc4
                                    ON (n.id IN ({$designs_ids_list})
                                      AND nc4.model_id = n.id
                                      AND nc4.var_id = {$design_fields['administrative_costs']}
                                      AND nc4.num = 1
                                      AND nc4.lang = '')
                                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc5
                                    ON (nc5.model_id = n.id
                                      AND nc5.var_id = {$design_fields['unit_complexity']}
                                      AND nc5.num = 1
                                      AND nc5.lang = '')";
                            $designs_data = $db->GetAssoc($query);
                        }

                        // Get multiplier
                        $query = "
                            SELECT ni.name
                              FROM " . DB_TABLE_NOMENCLATURES . " AS n
                              JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni
                                ON (!n.deleted
                                  AND n.active
                                  AND n.type = {$settings['nom_type_multiplier_arise']}
                                  AND ni.parent_id = n.id
                                  AND ni.lang = '{$document->get('model_lang')}')
                              JOIN " . DB_TABLE_FIELDS_META . " AS fm
                                ON (fm.model = 'Nomenclature'
                                  AND fm.model_type = n.type
                                  AND fm.name = 'multiplier_date')
                              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                                ON (nc.model_id = n.id
                                  AND nc.var_id = fm.id
                                  AND nc.num = 1
                                  AND nc.lang = ''
                                  AND nc.value != ''
                                  AND DATE(nc.value) <= CURDATE())
                              ORDER BY DATE(nc.value) DESC, n.id DESC
                              LIMIT 1";
                        $multiplier = $db->GetOne($query);
                        $multiplier = $multiplier ? floatval($multiplier) : 0;

                        // Get additional costs overhead
                        if ($designs_data) {
                            $query = "
                                SELECT n.id
                                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                                    ON (!n.deleted
                                      AND n.active
                                      AND n.type = {$settings['nom_type_additional_cost_overhead']}
                                      AND fm.model = 'Nomenclature'
                                      AND fm.model_type = n.type
                                      AND fm.name = 'overhead_date')
                                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                                    ON (nc.model_id = n.id
                                      AND nc.var_id = fm.id
                                      AND nc.num = 1
                                      AND nc.lang = ''
                                      AND nc.value != ''
                                      AND DATE(nc.value) <= CURDATE())
                                  ORDER BY DATE(nc.value) DESC, n.id DESC
                                  LIMIT 1";
                            $additional_costs_overhead_nom_id = $db->GetOne($query);
                            if ($additional_costs_overhead_nom_id) {
                                $query = "
                                    SELECT nc.value AS category_id,
                                        nc1.value   AS value
                                      FROM " . DB_TABLE_NOMENCLATURES . " AS n
                                      JOIN " . DB_TABLE_FIELDS_META . " AS fm
                                        ON (n.id = {$additional_costs_overhead_nom_id}
                                          AND fm.model = 'Nomenclature'
                                          AND fm.model_type = n.type
                                          AND fm.name = 'overhead_category')
                                      JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                                        ON (nc.model_id = n.id
                                          AND nc.var_id = fm.id
                                          AND nc.lang = ''
                                          AND nc.value != '')
                                      JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                                        ON (fm1.model = fm.model
                                          AND fm1.model_type = fm.model_type
                                          AND fm1.name = 'overhead_value')
                                      JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                                        ON (nc1.model_id = nc.model_id
                                          AND nc1.var_id = fm1.id
                                          AND nc1.num = nc.num
                                          AND nc1.lang = '')";
                                $additional_costs_overhead = $db->GetAssoc($query);

// TODO: For each design get the first and deepest possible category, for which we have $additional_costs_overhead
// TODO: and define $designs_data[...]['category_overhead'] for it
// TODO: For that purpose, get the categories tree, get the level and position for each category, sort the array by level DESC and by position ASC,
// TODO: then go through them and leave the ones for which we have $additional_costs_overhead
// TODO: then for each design get it's categories, then go through the categories we found from the previous row and use the first match
                                // $categories_tree = Nomenclatures_Categories::getTree($registry);
                                foreach ($designs_data as $design_id => $design_data) {
// Temporary hardcode to use the additional cost for category "All" (ID 1)
                                    $designs_data[$design_id]['category_overhead'] = floatval($additional_costs_overhead[1]);
                                }
                            }
                        }

                        /*
                         * Prepare GT2 values
                         */
                        $row_num = 0;
                        $rows_not_calculated = [];
                        foreach ($gt2['values'] as $row_index => $row) {
                            $row_num++;

                            // If there's data for the current row SKU
                            if (array_key_exists($row['id'], $gt2_rows_data)) {
                                // Set the total cost
                                $gt2['values'][$row_index]['article_width'] = $gt2_rows_data[$row['id']]['total_cost'];

                                // Get the design ID
                                $design_id = $gt2_rows_data[$row['id']]['design_id'];

                                // If this is not a Cut order
                                if ($document->get('type') != $settings['doc_type_cut_order']) {
                                    // Set the production price, if not already set
                                    if (empty((float)$row['article_height'])) {
                                        $unitComplexity = array_key_exists($design_id, $designs_data) ? floatval($designs_data[$design_id]['unit_complexity']) : 0;
                                        $gt2['values'][$row_index]['article_height'] = $unitComplexity * $multiplier;
                                    }

                                    // Get the overhead
                                    if (array_key_exists($design_id, $designs_data)) {
                                        if ($designs_data[$design_id]['overhead']) {
                                            $gt2['values'][$row_index]['article_volume'] = floatval($designs_data[$design_id]['overhead']);
                                        } else {
                                            $gt2['values'][$row_index]['article_volume'] = $designs_data[$design_id]['category_overhead']??0;
                                        }
                                    } else {
                                        $gt2['values'][$row_index]['article_volume'] = 0;
                                    }
                                }

                                /*
                                 * Calculate the COGM
                                 */
                                $article_width = floatval($gt2['values'][$row_index]['article_width']);
                                $article_height = floatval($gt2['values'][$row_index]['article_height']);
                                $price = $article_width + $article_height;
                                // Include the overhead only if this is not a Cut order
                                if ($document->get('type') != $settings['doc_type_cut_order']) {
                                    $article_volume = floatval($gt2['values'][$row_index]['article_volume']);
                                    $price += $article_volume;
                                }
                                $gt2['values'][$row_index]['price'] = $price;
                            } else {
                                $rows_not_calculated[] = $row_num;
                            }
                        }

                        // Warn for rows not being calculated
                        if ($rows_not_calculated) {
                            if ($this->metaData['automation_type'] == 'crontab') {
                                $this->executionWarnings[] = sprintf($this->i18n('calccogm_rows_not_calculated_for_document_id'), $document->get('id'), implode(', ', $rows_not_calculated));
                            } else {
                                $registry['messages']->setWarning(sprintf($this->i18n('calccogm_rows_not_calculated'), implode(', ', $rows_not_calculated)));
                                $registry['messages']->insertInSession($registry);
                            }
                        }

                        /*
                         * Save the GT2
                         */
                        $document->set('table_values_are_set', true, true);
                        $document_old = clone $document;
                        $document_old->sanitize();
                        if ($document->saveGT2Vars($gt2)) {
                            // Write history
                            $document_new = Documents::searchOne($registry,
                                [
                                    'where'                  => ["d.id = {$document->get('id')}"],
                                    'model_lang'             => $document->get('model_lang'),
                                    'skip_assignments'       => true,
                                    'skip_permissions_check' => true
                                ]
                            );

                            $registry->set('get_old_vars', true, true);
                            $document_new->getVars();
                            $registry->set('get_old_vars', true, true);
                            $history_id = Documents_History::saveData($registry,
                                [
                                    'action_type' => 'edit',
                                    'old_model'   => $document_old,
                                    'model'       => $document,
                                    'new_model'   => $document_new,
                                ]
                            );
                            // If all is done successfully
                            if ($history_id) {
                                // The result is true
                                $result = true;
                            }
                        }
                    }
                }
            }
        }

        if (!$result) {
            if ($this->metaData['automation_type'] == 'crontab') {
                $this->executionWarnings[] = sprintf($this->i18n('calccogm_failed_for_document_id'), $document->get('id'));
            } else {
                $registry['messages']->setWarning($this->i18n('calccogm_failed'));
                $registry['messages']->insertInSession($registry);
            }
        }

        return $result;
    }

    /**
     * Change sku recipes in background
     *
     * @return bool - result of the operation
     */
    public function backgroundChangeRecipe() {
        //get dashlet settings
        $query = 'SELECT settings FROM ' . DB_TABLE_DASHLETS_PLUGINS . ' WHERE type = "' . $this->settings['dashlet_keyname'] . '"';
        $settings = $this->registry['db']->GetOne($query);

        //create an instance of the custom dashlet model
        require_once PH_MODULES_DIR  . 'dashlets/plugins/' . $this->settings['dashlet_keyname'] . '/models/custom.model.php';
        // define and load custom plugin lang files
        $this->loadI18NFiles(array(PH_MODULES_DIR . 'dashlets/plugins/' . $this->settings['dashlet_keyname'] . '/i18n/' . $this->registry['lang'] . '/custom.ini'));
        $model = new Custom_Model($settings);

        $this->setOriginalUserAsCurrent();
        $res = $model->backgroundChangeRecipe();

        return $res;
    }

    /**
     * Fill Stock order GT2 using Parent SKU
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function parentSKU2GT2($params) {
        /*
         * Prepare some basics
         */
        $result = true;
        $settings = $this->settings;
        $registry = $this->registry;
        $request = $registry['request'];
        $db = $registry['db'];
        $lang = $registry['lang'];

        // Get the Stock order
        $document = $params['model'];

        // Get the requested data
        $article_deliverer = $request->get('article_deliverer');
        $article_id = $request->get('article_id');
        $quantity = $request->get('quantity');
        $price = $request->get('price');

        // Get Parent SKUs to replace
        $parent_sku_ids = [];
        if (is_array($article_deliverer)) {
            foreach ($article_deliverer as $k => $v) {
                // If this is a new GT2 row
                if ($k < 0
                        // and there is a Parent SKU for it
                        && !empty($v)
                        // and there is no Child SKU for it
                        && array_key_exists($k, $article_id) && empty($article_id[$k])
                        // and there is no quantity for it
                        && array_key_exists($k, $quantity) && empty($quantity[$k])
                        // and there is no price for it
                        && array_key_exists($k, $price) && empty($price[$k])) {
                    $parent_sku_ids[] = $v;
                }
            }
        }

        // If there are Parent SKUs to replace
        if ($parent_sku_ids) {
            // Default result is false until success
            $result = false;

            // Prepare the Parent SKUs as IDs list
            $parent_sku_ids_list = implode(',', $parent_sku_ids);

            /*
             * Get fields
             */
            $query = "
                SELECT name, id
                  FROM " . DB_TABLE_FIELDS_META . "
                  WHERE model = 'Nomenclature'
                    AND model_type = '{$settings['nom_type_child_sku']}'
                    AND name IN (
                      'parentsku_name_id',
                      'color_name_id',
                      'size_info'
                    )";
            $child_sku_fields = $db->GetAssoc($query);
            $query = "
                SELECT name, id
                  FROM " . DB_TABLE_FIELDS_META . "
                  WHERE model = 'Nomenclature'
                    AND model_type = '{$settings['nom_type_parent_sku']}'
                    AND name IN (
                      'model_clothes_id'
                    )";
            $parent_sku_fields = $db->GetAssoc($query);
            $query = "
                SELECT name, id
                  FROM " . DB_TABLE_FIELDS_META . "
                  WHERE model = 'Nomenclature'
                    AND model_type = '{$settings['nom_type_designs']}'
                    AND name IN (
                      'category_letter'
                    )";
            $design_fields = $db->GetAssoc($query);

            // Get the clothes sizes ordered by their position
            $query = "
                SELECT GROUP_CONCAT(n.id SEPARATOR ',')
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    ON (!n.deleted
                      AND n.active
                      AND n.type = {$settings['nom_type_size_clothes']}
                      AND fm.model = 'Nomenclature'
                      AND fm.model_type = n.type
                      AND fm.name = 'size_position')
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (nc.model_id = n.id
                      AND nc.var_id = fm.id
                      AND nc.num = 1
                      AND nc.lang = '')
                  ORDER BY nc.value ASC,
                    n.id ASC";
            $size_clothes_positions = $db->GetOne($query);

            // Get the Parent SKU's Child SKUs as GT2 rows
            $query = "
                SELECT n2.id AS article_alternative_deliverer,
                    n2.code  AS article_alternative_deliverer_name,
                    n1.id    AS article_deliverer,
                    n1.code  AS article_deliverer_name,
                    n.code   AS article_code,
                    n.id     AS article_id,
                    ni.name  AS article_name,
                    n4.code  AS free_text4,
                    n3.code  AS free_field1
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (!n.deleted
                      AND n.active
                      AND n.type = {$settings['nom_type_child_sku']}
                      AND nc.model_id = n.id
                      AND nc.var_id = {$child_sku_fields['parentsku_name_id']}
                      AND nc.num = 1
                      AND nc.lang = ''
                      AND nc.value IN ({$parent_sku_ids_list}))
                  JOIN " . DB_TABLE_NOMENCLATURES . " AS n1
                    ON (n1.id = nc.value
                      AND n1.type = {$settings['nom_type_parent_sku']})
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                    ON (nc1.model_id = n1.id
                      AND nc1.var_id = {$parent_sku_fields['model_clothes_id']}
                      AND nc1.num = 1
                      AND nc1.lang = '')
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc3
                    ON (nc3.model_id = n.id
                      AND nc3.var_id = {$child_sku_fields['color_name_id']}
                      AND nc3.num = 1
                      AND nc3.lang = '')
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc4
                    ON (nc4.model_id = n.id
                      AND nc4.var_id = {$child_sku_fields['size_info']}
                      AND nc4.num = 1
                      AND nc4.lang = '')
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni
                    ON (ni.parent_id = n.id
                      AND ni.lang = '{$lang}')
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES . " AS n2
                    ON (n2.id = nc1.value
                      AND n2.type = {$settings['nom_type_designs']})
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                    ON (nc2.model_id = n2.id
                      AND nc2.var_id = {$design_fields['category_letter']}
                      AND nc2.num = 1
                      AND nc2.lang = '')
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES . " AS n4
                    ON (n4.id = nc2.value
                      AND n4.type = {$settings['nom_type_category_letter']})
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES . " AS n3
                    ON (n3.id = nc3.value
                      AND n3.type = {$settings['nom_type_color_clothes']})
                  ORDER BY FIND_IN_SET(n1.id, '{$parent_sku_ids_list}') ASC,
                    FIND_IN_SET(nc4.value, '{$size_clothes_positions}') ASC,
                    n.code ASC";
            $new_gt2_rows = $db->GetAll($query);

            // If there's any new GT2 rows data
            if ($new_gt2_rows) {
                // Load the document vars from the database
                $registry->set('get_old_vars', true, true);
                $document->getVars();
                $registry->set('get_old_vars', true, true);

                // Get the GT2
                $gt2 = $document->getGT2Vars();

                // Add the new GT2 rows
                foreach ($new_gt2_rows as $new_gt2_row) {
                    $new_gt2_row['article_measure_name'] = $settings['article_measure_name'];
                    $new_gt2_row['quantity'] = 0;
                    $new_gt2_row['price'] = 0;
                    $gt2['values'][] = $new_gt2_row;
                }

                /*
                 * Save the GT2
                 */
                $document->set('table_values_are_set', true, true);
                $document_old = clone $document;
                $document_old->sanitize();
                if ($document->saveGT2Vars($gt2)) {
                    // Write history
                    $document_new = Documents::searchOne($registry,
                        [
                            'where'                  => ["d.id = {$document->get('id')}"],
                            'model_lang'             => $document->get('model_lang'),
                            'skip_assignments'       => true,
                            'skip_permissions_check' => true
                        ]
                    );
                    $history_id = Documents_History::saveData($registry,
                        [
                            'action_type' => 'edit',
                            'old_model'   => $document_old,
                            'model'       => $document,
                            'new_model'   => $document_new,
                        ]
                    );
                    // If all is done successfully
                    if ($history_id) {
                        // The result is true
                        $result = true;
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Copy document deadline to GT2, where the date into the GT2 is the same as
     * the deadline of the document
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function documentDeadline2Gt2($params) {
        /*
         * Prepare some basics
         */
        $result = true;
        $registry = $this->registry;
        $document = $params['model'];
        $old_document = $document->get('old_model');

        // If there's an old model
        if ($old_document) {
            // Get the old deadline and the new deadline AS A DATE, not a datetime
            $prev_deadline = $old_document->get('deadline');
            $prev_deadline = ((!empty($prev_deadline) && (int)$prev_deadline !== 0) ? General::strftime($this->i18n('date_iso_short'), $prev_deadline) : '');
            $deadline = $document->get('deadline');
            $deadline = ((!empty($deadline) && (int)$deadline !== 0) ? General::strftime($this->i18n('date_iso_short'), $deadline) : '');

            // If the deadline (as a date) is changed
            if ($prev_deadline !== $deadline) {
                // Get the GT2
                $registry->set('get_old_vars', true, true);
                $document->getVars();
                $registry->set('get_old_vars', true, true);
                $gt2 = $document->getGT2Vars();

                // If the GT2 is not empty
                if (!empty($gt2['values']) && is_array($gt2['values'])) {
                    // Set flag to check if there are changes
                    $has_changes = false;

                    // Get the name of the GT2 field in which to set the deadline
                    $gt2_deadline_field = $this->settings['gt2_deadline_field']??'';

                    // Go through the GT2 rows
                    foreach ($gt2['values'] as $k => $row) {
                        // If the deadline field value is the same as the old deadline
                        if (array_key_exists($gt2_deadline_field, $row) && $row[$gt2_deadline_field] === $prev_deadline) {
                            // Change it
                            $gt2['values'][$k][$gt2_deadline_field] = $deadline;
                            // Set that there are changes
                            $has_changes = true;
                        }
                    }

                    // If there are changes
                    if ($has_changes) {
                        // Set the default result: false
                        $result = false;

                        // Clone the current model as old one
                        $document_old = clone $document;
                        $document_old->sanitize();

                        // Try to save the GT2
                        $document->set('table_values_are_set', true, true);
                        if ($document->saveGT2Vars($gt2)) {
                            // Write history
                            $document_new = Documents::searchOne($registry,
                                [
                                    'where'                  => ["d.id = {$document->get('id')}"],
                                    'model_lang'             => $document->get('model_lang'),
                                    'skip_assignments'       => true,
                                    'skip_permissions_check' => true
                                ]
                            );
                            $registry->set('get_old_vars', true, true);
                            $document_new->getVars();
                            $document_new->getGT2Vars();
                            $registry->set('get_old_vars', false, true);
                            $history_id = Documents_History::saveData($registry,
                                [
                                    'action_type' => 'edit',
                                    'old_model'   => $document_old,
                                    'model'       => $document,
                                    'new_model'   => $document_new,
                                ]
                            );
                            // If all is done successfully
                            if ($history_id) {
                                // The result is true
                                $result = true;
                            }
                        }

                        // If edit failed
                        if (!$result) {
                            // Set an error message
                            $registry['messages']->setError($this->i18n('error_automation_documentdeadline2gt2_failed'));
                            $registry['messages']->setError($this->i18n('error_technical_error_please_contact_nzoom_support'));
                        }
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Set status for documents which IDs are inside a given GT2 field
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function setGt2DocumentsStatus($params) {
        // Default result is true
        $result = true;

        // Get settings
        $settings = $this->settings;

        // Define required settings
        $required_settings = [
            'gt2_documents_ids_field' => null,
            'gt2_documents_status'    => null,
        ];

        // If required settings are set
        if (count(array_intersect_key($required_settings, array_filter($settings))) == count($required_settings)) {
            // Prepare the registry and the current document model
            $registry = $this->registry;
            $document = $params['model'];

            // Get the GT2
            $registry->set('get_old_vars', true, true);
            $document->getVars();
            $registry->set('get_old_vars', true, true);
            $gt2 = $document->getGT2Vars();

            // If the GT2 is not empty
            if (!empty($gt2['values']) && is_array($gt2['values'])) {
                // Get the IDs of the documents for which the status should be set
                $gt2_documents_ids = array_unique(
                    array_filter(
                        array_column($gt2['values'], $settings['gt2_documents_ids_field'])
                    )
                );

                // If there are any documents IDs from the GT2
                if ($gt2_documents_ids) {
                    /*
                     * Get the GT2 documents models
                     */
                    $gt2_documents_ids_list = implode(', ', $gt2_documents_ids);
                    $gt2_documents_filters = [
                        'where'                  => [
                            "d.id IN ({$gt2_documents_ids_list})",
                        ],
                        'sort'                   => ['d.id ASC'],
                        'sanitize'               => true,
                        'model_lang'             => $document->get('model_lang'),
                        'skip_assignments'       => true,
                        'skip_permissions_check' => true
                    ];
                    // If we should set a substatus
                    if (!empty($settings['gt2_documents_substatus'])) {
                        // Then find documents, which has different substatus
                        $gt2_documents_filters['where'][] =
                            "(d.status != '{$settings['gt2_documents_status']}' OR d.substatus != '{$settings['gt2_documents_substatus']}')";
                    } else {
                        // Find documents, which has different status
                        $gt2_documents_filters['where'][] =
                            "d.status != '{$settings['gt2_documents_status']}'";
                    }
                    // If the type of the GT2 documents is set
                    if (!empty($settings['gt2_documents_type'])) {
                        // Filter them by the given type
                        $gt2_documents_filters['where'][] =
                            "d.type = {$settings['gt2_documents_type']}";
                    }
                    // Get the GT2 documents models
                    $gt2_documents = Documents::search($registry, $gt2_documents_filters);

                    // If we found any GT2 documents models
                    if ($gt2_documents) {
                        // Then we should change their status, so make the result to false, until it's completely successful
                        $result = false;

                        /*
                         * Prepare the status params
                         */
                        // Set some default values for all the GT2 documents
                        $status_params = array(
                            'module'     => 'documents',
                            'model_id'   => '',
                            'model'      => '',
                            'new_status' => $settings['gt2_documents_status'],
                        );
                        // Substatus is not a required setting, so set it only if it's given
                        if (!empty($settings['gt2_documents_substatus'])) {
                            $status_params['new_substatus'] = $settings['gt2_documents_substatus'];
                        }

                        // Prepare to collect links to the processed documents (successfully and unsuccessfully processed)
                        $successful_gt2_documents_html = [];
                        $failed_gt2_documents_html = [];

                        // Process the GT2 documents
                        foreach ($gt2_documents as $gt2_document) {
                            // Unsanitize the GT2 document
                            $gt2_document->unsanitize();

                            // Set some status params for the GT2 document
                            $status_params['model_id'] = $gt2_document->get('id');
                            $status_params['model'] = $gt2_document;

                            // Prepare a link to the GT2 document
                            $gt2_document_link_html = sprintf(
                                '<a href="%s?%s=documents&documents=view&view=%d" target="_blank">%s</a>',
                                $_SERVER['PHP_SELF'],
                                $registry['module_param'],
                                $gt2_document->get('id'),
                                $gt2_document->get('full_num')
                            );

                            // Try to set status for the GT2 document
                            // using the universal automations method status()
                            if ($this->status($status_params)) {
                                // Collect links for successfully processed documents
                                $successful_gt2_documents_html[] = $gt2_document_link_html;
                            } else {
                                // Collect links for unsuccessfully processed documents
                                $failed_gt2_documents_html[] = $gt2_document_link_html;
                            }
                        }

                        // If setting the status for some GT2 documents has failed
                        if ($failed_gt2_documents_html) {
                            // Set a warning message (because this is an action automation,
                            // i.e. the main action is already done successful)
                            // with links to the all unsuccessfully processed documents
                            $registry['messages']->setWarning(sprintf(
                                    $this->i18n('setgt2documentsstatus_failed_gt2_documents'),
                                    implode(', ', $failed_gt2_documents_html)
                                )
                            );
                            // In this case, the result remains false, because
                            // the process failed for at least one document
                        } else {
                            // If the process hasn't failed, then the result is true
                            $result = true;
                        }

                        // If there are successfully processed GT2 documents
                        if ($successful_gt2_documents_html) {
                            // Set a message with links to them
                            $registry['messages']->setMessage(sprintf(
                                    $this->i18n('setgt2documentsstatus_successful_gt2_documents'),
                                    implode(', ', $successful_gt2_documents_html)
                                )
                            );
                        }

                        // If there are any successfully or unsuccessfully processed documents
                        if ($failed_gt2_documents_html || $successful_gt2_documents_html) {
                            // Load the messages into the session
                            $registry['messages']->insertInSession($registry);
                        }
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Fill GT2 with Object cost
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function fillGt2ObjectCost($params) {
        // Default result: true
        $result = true;

        /*
         * Prepare some basics
         */
        $settings = $this->settings;
        $registry = $this->registry;
        $model = $params['model'];

        // Get the GT2
        $registry->set('get_old_vars', true, true);
        $model->getVars();
        $registry->set('get_old_vars', true, true);
        $gt2 = $model->getGT2Vars();

        // If the GT2 is not empty
        if (is_array($gt2['values']) && !empty($gt2['values']) && count($gt2['values']) > 1) {
            $first_row = reset($gt2['values']);

            if ($first_row[$settings['gt2_field_object_expense_name']] != '' && $first_row[$settings['gt2_field_object_expense_id']] != ''
                    && $first_row[$settings['gt2_field_expense_account_name']] != '' && $first_row[$settings['gt2_field_expense_account_id']] != '') {
                $has_changes = false;
                foreach ($gt2['values'] as $gt2_row_id => $gt2_row) {
                    if (empty($gt2_row[$settings['gt2_field_object_expense_name']]) && empty($gt2_row[$settings['gt2_field_object_expense_id']])
                            && empty($gt2_row[$settings['gt2_field_expense_account_name']]) && empty($gt2_row[$settings['gt2_field_expense_account_id']])) {
                        $gt2['values'][$gt2_row_id][$settings['gt2_field_object_expense_name']] = $first_row[$settings['gt2_field_object_expense_name']];
                        $gt2['values'][$gt2_row_id][$settings['gt2_field_object_expense_id']] = $first_row[$settings['gt2_field_object_expense_id']];
                        $gt2['values'][$gt2_row_id][$settings['gt2_field_expense_account_name']] = $first_row[$settings['gt2_field_expense_account_name']];
                        $gt2['values'][$gt2_row_id][$settings['gt2_field_expense_account_id']] = $first_row[$settings['gt2_field_expense_account_id']];

                        $has_changes = true;
                    }
                }

                if ($has_changes) {
                    $result = false;

                    /*
                     * Save the GT2
                     */
                    $model->set('table_values_are_set', true, true);
                    $old_model = clone $model;
                    $old_model->sanitize();
                    if ($model->saveGT2Vars($gt2)) {
                        // Write history
                        $factory_class_name = $model->getFactory();
                        $module = $params['module'];
                        $controller = $params['controller'] ?: $params['module'];
                        $model_alias = $factory_class_name::getAlias($module, $controller);
                        $new_model = $factory_class_name::searchOne($registry,
                            [
                                'where'                  => ["{$model_alias}.id = {$model->get('id')}"],
                                'model_lang'             => $model->get('model_lang'),
                                'skip_assignments'       => true,
                                'skip_permissions_check' => true
                            ]
                        );

                        $registry->set('get_old_vars', true, true);
                        $new_model->getVars();
                        $registry->set('get_old_vars', true, true);
                        $history_class_name = "{$factory_class_name}_History";
                        $history_id = $history_class_name::saveData($registry,
                            [
                                'action_type' => 'edit',
                                'old_model'   => $old_model,
                                'model'       => $model,
                                'new_model'   => $new_model,
                            ]
                        );
                        // If all is done successfully
                        if ($history_id) {
                            // The result is true
                            $result = true;
                        }
                    }
                }
            }
        }

        if (!$result) {
            $registry['messages']->setWarning($this->i18n('fillgt2objectcost_failed'));
            $registry['messages']->insertInSession($registry);
        }

        return $result;
    }

    /**
     * Update name and code for Designs and Parent SKU
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - the result of the operation
     */
    public function updateDesignParentSKU($params) {
        $result = false;
        $updated = false;

        /*
         * Prepare some basics
         */
        $registry = $this->registry;
        $settings = $this->settings;
        $db = $registry['db'];
        $source_nom = $params['model'];
        $source_nom_id = $source_nom->get('id');
        $warnings = [];

        if (in_array($source_nom->get('type'), [$settings['nom_type_design'], $settings['nom_type_parent_sku']])) {
            if ($source_nom->get('type') == $settings['nom_type_design']) {
                $destination_nom_types = [
                    $settings['nom_type_parent_sku'],
                    $settings['nom_type_child_sku']
                ];
                $destination_nom_id_field_name = 'model_clothes_id';
            } else {
                $destination_nom_types = [
                    $settings['nom_type_child_sku']
                ];
                $destination_nom_id_field_name = 'parentsku_name_id';
            }
            $destination_nom_types_list = implode(',', $destination_nom_types);
            $query = "
                SELECT n.id
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    ON (n.type IN ({$destination_nom_types_list})
                      AND fm.model = 'Nomenclature'
                      AND fm.model_type = n.type
                      AND fm.name = '{$destination_nom_id_field_name}')
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (nc.model_id = n.id
                      AND nc.var_id = fm.id
                      AND nc.num = 1
                      AND nc.lang = ''
                      AND nc.value = '{$source_nom_id}')";
            $destination_nom_ids = $db->GetCol($query);
            if ($destination_nom_ids) {
                $destination_nom_ids_list = implode(',', $destination_nom_ids);
                $destination_noms = Nomenclatures::search($registry, [
                    'where'                  => ["n.id IN ({$destination_nom_ids_list})"],
                    'model_lang'             => $source_nom->get('model_lang'),
                    'skip_permissions_check' => true,
                ]);
            }
            if (!empty($destination_noms)) {
                $db->StartTrans();

                // FIX: mark to don't touch the categories
                if ($update_categories_is_requested = $registry['request']->isRequested('update_categories')) {
                    $update_categories = $registry['request']->get('update_categories');
                    $registry['request']->set('update_categories', false, 'post', true);
                }

                foreach ($destination_noms as $destination_nom) {
                    $destination_nom->unsanitize();
                    $get_old_vars = $registry->get('get_old_vars');
                    $registry->set('get_old_vars', true, true);
                    $destination_nom->getVars();
                    $registry->set('get_old_vars', $get_old_vars, true);
                    $vars = $destination_nom->get('vars');
                    $destination_nom_type = $destination_nom->get('type');

                    if ($source_nom->get('type') == $settings['nom_type_design']) {
                        if ($destination_nom_type == $settings['nom_type_parent_sku']) {
                            $vars_to_process = 2;
                        } else {
                            $vars_to_process = 1;
                        }
                    } else {
                        $vars_to_process = 1;
                    }

                    foreach ($vars as $var_key => $var) {
                        if ($source_nom->get('type') == $settings['nom_type_design']) {
                            if ($destination_nom_type == $settings['nom_type_parent_sku']) {
                                if ($var['name'] == 'model_clothes_name') {
                                    $vars[$var_key]['value'] = $source_nom->get('name');
                                    $vars_to_process--;
                                } elseif ($var['name'] == 'model_sku') {
                                    $vars[$var_key]['value'] = $source_nom->get('code');
                                    $vars_to_process--;
                                }
                            } elseif ($destination_nom_type == $settings['nom_type_child_sku']) {
                                if ($var['name'] == 'model_clothes_name') {
                                    $vars[$var_key]['value'] = $source_nom->get('name');
                                    $vars_to_process--;
                                }
                            }
                        } elseif ($var['name'] == 'parentsku_name') {
                            $vars[$var_key]['value'] = trim("{$source_nom->get('code')} {$source_nom->get('name')}");
                            $vars_to_process--;
                        }

                        if ($vars_to_process < 1) {
                            break;
                        }
                    }

                    if ($vars_to_process === 0) {
                        $old_destination_nom = clone $destination_nom;
                        $old_destination_nom->sanitize();
                        $destination_nom->set('vars', $vars, true);
                        if ($destination_nom->save()) {
                            $new_destination_nom = Nomenclatures::searchOne($registry, [
                                'where'                  => ["n.id = {$destination_nom->get('id')}"],
                                'model_lang'             => $source_nom->get('model_lang'),
                                'skip_permissions_check' => true,
                            ]);
                            $get_old_vars = $registry->get('get_old_vars');
                            $registry->set('get_old_vars', true, true);
                            $new_destination_nom->getVars();
                            $registry->set('get_old_vars', $get_old_vars, true);
                            $history_id = Nomenclatures_History::saveData(
                                $registry,
                                [
                                    'action_type' => 'edit',
                                    'old_model'   => $old_destination_nom,
                                    'model'       => $destination_nom,
                                    'new_model'   => $new_destination_nom,
                                ]
                            );
                            if ($history_id) {
                                $updated = true;
                            }
                        }
                    }

                    $destination_nom->sanitize();
                }

                $hasFailedTrans = $db->HasFailedTrans();
                $db->CompleteTrans();
                if ($update_categories_is_requested) {
                    $registry['request']->set('update_categories', $update_categories, 'post', true);
                }
            } else {
                $result = true;
            }
        } else {
            $warnings[] = $this->i18n('updatedesignparentsku_invalid_type');
        }

        if ($updated && !$hasFailedTrans) {
            $registry['messages']->setMessage($this->i18n('updatedesignparentsku_success'));
            $result = true;
        }

        if (!$result && !$warnings) {
            $warnings[] = $this->i18n('updatedesignparentsku_failed');
        }
        if ($warnings) {
            foreach ($warnings as $warning) {
                $registry['messages']->setWarning($warning);
            }
        }
        $registry['messages']->insertInSession($registry);

        return $result;
    }

    /**
     * @param array $skuSale
     * @return string
     */
    public function calcSkuVaultSaleChecksum(array $skuSale): string
    {

        //remove the fields that are not used in order to calculate the checksum correctly
        //e.g. LastPrintedDate should not change the check sum
        $allowedAttributes = array(
            'Id',
            'Status',
            'SaleDate',
            'ProductionDeadline',
            'Notes',
            'Channel',
            'ChannelId',
            'Marketplace',
            'MerchantItems',
            'SaleItems',
            'MerchantKits',
            'SaleKits',
            'ProcessedItems',
            'ProductionItems'
        );
        foreach($skuSale as $attr => $value) {
            if (!in_array($attr, $allowedAttributes)) {
                unset($skuSale[$attr]);
            }
        }
        return hash_hmac('sha256', json_encode($skuSale), hash('sha256', 'SkuVault'));
    }
}
