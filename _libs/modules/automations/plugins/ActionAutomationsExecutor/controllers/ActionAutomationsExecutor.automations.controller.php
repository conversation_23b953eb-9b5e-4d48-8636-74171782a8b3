<?php

class ActionAutomationsExecutor_Automations_Controller extends Automations_Controller
{
    public static array $executedAutomationsExecutors = [];

    private const ERROR_LEVEL_WARNING = 'warning';
    private const ERROR_LEVEL_ERROR = 'error';

    /**
     * Executes automation actions for a collection of models based on specified parameters.
     *
     * @param array $params An array of parameters required to execute the automation. Includes:
     *    - Models_module (string): The name of the module for the models. Required.
     *    - models_controller (string|null): The controller associated with the models. Optional.
     *    - models_action (string): The action to be executed on the models. Required.
     *    - models_ids (string): A placeholder or list of model IDs to be operated on. Required.
     *    - models_model_lang (string|null): The language of the models. Defaults to the original model's language. Optional.
     *    - model (Model): The current model object. Must be an instance of Model. Required.
     *    - id (int): The automation ID. Required for recursion protection.
     * @return bool Returns true if the automation was successfully executed on all models, otherwise returns false.
     *              False is also returned in cases of validation errors, recursion detection, or if any action fails.
     *              If no models match the criteria, this method will return true by default as no action is required.
     * @throws Exception
     * @todo: Put the automation messages into the i18n table (not hardcoded).
     *        Show an additional message before them, so the user to know for which automation the messages are.
     */
    public function executeModelsAutomations($params): bool
    {
        $registry = $this->registry;
        $db = $registry['db'];
        $settings = $this->settings;

        $cancelActionOnFail = array_key_exists('cancel_action_on_fail', $settings) && $settings['cancel_action_on_fail'] === '1';
        $errorLevel = $this->getErrorLevel($cancelActionOnFail);

        if (empty($params['models_module'])) {
            $this->setError($errorLevel, 'Fill required param: models_module');
            return false;
        }
        $modelsModule = $params['models_module'];

        $modelsController = $params['models_controller']??'';

        if (empty($params['models_action'])) {
            $this->setError($errorLevel, 'Fill required param: models_action');
            return false;
        }
        $modelsAction = $params['models_action'];

        if (!array_key_exists('models_ids', $params)) {
            $this->setError($errorLevel, 'Fill required param: models_ids');
            return false;
        }
        if (empty($params['models_ids'])) {
            return true;
        }

        if (empty($params['model']) || !($params['model'] instanceof Model)) {
            $this->setError($errorLevel, 'Model is not set or is not an instance of Model!');
            return false;
        }

        $currentModel = $params['model'];
        $currentModelId = $currentModel->get('id');

        $currentAutomationId = $params['id'];

        // Recursion protection
        if (array_key_exists($currentModelId, self::$executedAutomationsExecutors)
                && array_key_exists($currentAutomationId, self::$executedAutomationsExecutors[$currentModelId])) {
            $this->setError($errorLevel, "Recursion detected for model \"{$currentModel->get('name')}\" and automation \"{$currentAutomationId}\"");
            return false;
        }
        self::$executedAutomationsExecutors[$currentModelId][$currentAutomationId] = $currentAutomationId;

        $modelsIds = $this->replaceModelPlaceholders($params['models_ids'], $currentModel);
        if (empty($modelsIds)) {
            return true;
        }

        $modelsModelLang = $params['models_model_lang']??$currentModel->get('model_lang');

        $modelsIds = $this->getVariableValue(
            $modelsIds,
            [
                'orig_str' => $modelsIds,
                'origin'   => [
                    'table'    => DB_TABLE_AUTOMATIONS,
                    'field'    => 'method',
                    'id'       => $currentAutomationId,
                    'model'    => $currentModel->modelName,
                    'model_id' => $currentModelId,
                ],
                strtolower($currentModel->modelName) => $currentModel
            ]
        );

        if (!is_string($modelsIds)) {
            $modelsIdsType = gettype($modelsIds);
            $this->setError($errorLevel, "The models_ids parameter must result to a string, but got: \"{$modelsIdsType}\"");
            return false;
        }

        if (empty($modelsIds)) {
            return true;
        }

        // Prepare factory name and check if exists
        $modelsFactoryName = $this->buildFactoryName($modelsModule, $modelsController);
        if (!class_exists($modelsFactoryName)) {
            $this->setError($errorLevel, "The factory class \"{$modelsFactoryName}\", built from the models_module \"{$modelsModule}\" and models_controller \"{$modelsController}\" does not exist!");
            return false;
        }

        // Get models alias
        /* @var $modelsFactoryName Model_Factory */
        $modelsAlias = $modelsFactoryName::getAlias($modelsModule, $modelsController);

        // Get models
        $modelsFilters = [
            'where'      => [
                "{$modelsAlias}.id IN ({$modelsIds})",
            ],
            'model_lang' => $modelsModelLang,
        ];
        $automationsUserWasCurrent = $registry['currentUser']->get('id') == PH_AUTOMATION_USER && isset($registry['originalUser']);
        $checkUserPermissions = array_key_exists('check_user_permissions', $settings) && $settings['check_user_permissions'] === '1';
        if (!$checkUserPermissions) {
            $modelsFilters['skip_permissions_check'] = true;
            $modelsFilters['skip_assignments'] = true;
        } else if ($automationsUserWasCurrent) {
            $this->setOriginalUserAsCurrent();
        }
        if ($checkUserPermissions) {
            $currentModule = $registry->get('module');
            $currentController = $registry->get('controller');
            $currentAction = $registry->get('action');
            $registry->set('module', $modelsModule, true);
            $registry->set('controller', $modelsController, true);
            $registry->set('action', 'search', true);
        }
        $models = $modelsFactoryName::search($registry, $modelsFilters);
        if ($checkUserPermissions) {
            $registry->set('module',     $currentModule,     true);
            $registry->set('controller', $currentController, true);
            $registry->set('action',     $currentAction,     true);
        }
        if ($checkUserPermissions && $automationsUserWasCurrent) {
            $this->setAutomationUserAsCurrent();
        }
        if (empty($models)) {
            return true;
        }

        $result = true;

        // Prepare parent automation
        require_once PH_MODULES_DIR . 'automations/plugins/ActionAutomationsExecutor/ParentAutomation.php';
        try {
            $parentAutomation = new ParentAutomation($params, $this->settings, $registry->get('parent_automation'));
        } catch (Exception $e) {
            $automationId = $params['id']??'';
            $this->setError($errorLevel, "Failed to create parent automation with ID: {$automationId}. Message: {$e->getMessage()}");
            return false;
        }
        $registry->set('parent_automation', $parentAutomation, true);

        $executeBeforeActions = array_key_exists('execute_before_actions', $settings) && $settings['execute_before_actions'] === '1';

        $executeInTransaction = array_key_exists('execute_in_transaction', $settings) && $settings['execute_in_transaction'] === '1';

        $checkAllSuccess = array_key_exists('check_all_success', $settings) && $settings['check_all_success'] === '1';

        if ($executeInTransaction) {
            $db->StartTrans();
        }

        // Execute models automations
        // TODO: Collect current messages, after the loop collect the automations messages, then set back the current messages, then set the current automation message (if any), then set the automations messages.
        //      This should work even if there are several levels of execution (i.e. the current method is used for some of the executed automations).
        //      Be careful with restoring the messages, preserve the positions, keys and some overlapping may appear.
        foreach ($models as $model) {
            $modelActionAutomationsResult = $this->executeActionAutomations(
                $model,
                $model,
                $modelsAction,
                $executeBeforeActions,
                $checkAllSuccess
            );
            if (!$modelActionAutomationsResult) {
                $result = false;

                if ($executeInTransaction) {
                    $db->FailTrans();
                    // No need to continue
                    break;
                }
            }
        }

        if ($result && $executeInTransaction && $db->HasFailedTrans()) {
            $result = false;
        }
        if ($executeInTransaction) {
            $db->CompleteTrans();
        }

        // Reset the current automation as parent, because it might be changed by the executeActionAutomations method.
        $registry->set('parent_automation', $parentAutomation, true);

        $msgOnFailSetting = "msg_on_fail_{$registry['lang']}";
        $msgOnFail = array_key_exists($msgOnFailSetting, $settings) ? $settings[$msgOnFailSetting] : '';

        $msgOnSuccessSetting = "msg_on_success_{$registry['lang']}";
        $msgOnSuccess = array_key_exists($msgOnSuccessSetting, $settings) ? $settings[$msgOnSuccessSetting] : '';

        // Current automation message (of it's set)
        if (!$result) {
            if ($msgOnFail !== '') {
                $this->setError($errorLevel, $msgOnFail);
            }
        } elseif ($msgOnSuccess !== '') {
            $this->setMessage($msgOnSuccess);
        }

        return $result;
    }

    private function getErrorLevel(bool $cancelActionOnFail): string
    {
        // If this is an action automation, or it's a before_action automation, but there is no cancel_action_on_fail to stop the action
        if (empty($this->before_action) || !$cancelActionOnFail) {
            return self::ERROR_LEVEL_WARNING;
        }

        return self::ERROR_LEVEL_ERROR;
    }

    private function setError(string $errorLevel, string $msg): void
    {
        $registry = $this->registry;

        if ($this->isCrontab()) {
            $this->executionErrors[] = $msg;
            $this->isExecuted = true;
            return;
        }

        /* @var $messages Messages */
        $messages = $registry->get('messages');
        $msgMethod = $errorLevel === self::ERROR_LEVEL_ERROR ? 'setError' : 'setWarning';
        $messages->$msgMethod($msg);
        $messages->insertInSession($registry);
    }

    private function setMessage(string $msg): void
    {
        $registry = $this->registry;

        if ($this->isCrontab()) {
            $this->executionMessages[] = $msg;
            $this->isExecuted = true;
            return;
        }

        /* @var $messages Messages */
        $messages = $registry->get('messages');
        $messages->setMessage($msg);
        $messages->insertInSession($registry);
    }

    private function isCrontab(): bool
    {
        return (bool) $this->registry->get('crontab_automations');
    }
}
