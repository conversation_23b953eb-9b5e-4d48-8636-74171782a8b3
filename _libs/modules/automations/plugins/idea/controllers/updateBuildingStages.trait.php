<?php

trait updateBuildingStages {
    public $automation_params;

    /**
     * Function to update the building stages in related documents
     *
     * @param array $params - arguments for the method, containing registry
     * @return boolean - result of the operation
     */
    public function updateBuildingStagesInRelatedDocuments(array $params): bool
    {
        $this->automation_params = $params;
        $get_old_vars = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);

        $this->processStagesUpdate();

        $this->registry->set('get_old_vars', $get_old_vars, true);
        $this->registry->set('edit_all', $edit_all, true);
        return true;
    }

    /**
     * Function to process the actual update of the stages
     *
     * @param $assoc_vars
     * @return array
     */
    private function processStagesUpdate() : void {
        // define the differences in the table
        $new_model = clone $this->automation_params['model'];
        $old_model = clone $this->automation_params['model']->get('old_model');

        $old_assoc_vars = $old_model->getAssocVars();
        $new_assoc_vars = $new_model->getAssocVars();

        // build the array with stages
        $new_stages = $this->getStages($new_assoc_vars);
        $old_stages = $this->getStages($old_assoc_vars);

        $added_stages = array_diff_key($new_stages, $old_stages);
        $deleted_stages = array_diff_key($old_stages, $new_stages);
        $current_stages = array();
        $matching_stages = array_intersect(array_keys($new_stages), array_keys($old_stages));
        foreach ($matching_stages as $mtch_stg) {
            if ($new_stages[$mtch_stg] != $old_stages[$mtch_stg]) {
                // complete the updated matching stages
                $current_stages[$mtch_stg] = $new_stages[$mtch_stg];
            }
        }
        $current_stages += $added_stages;
        $current_stages += array_fill_keys(array_keys($deleted_stages), '');

        if (empty($current_stages)) {
            // if the is nothing changed, exit
            return;
        }

        // prepare the filters to search the matching rows in the related documents
        $filters = array();
        foreach ($current_stages as $stg => $stg_date) {
            $filters[] = sprintf('(gt2.free_field4="%d" AND gt2.article_id="%d")', $new_model->get('id'), $stg);
        }

        $sql = sprintf('SELECT d.id, gt2.id as gt2_row' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                       ' ON (d.active=1 AND d.deleted_by=0 AND d.type="%d" AND d.substatus="%d" ' .
                       '     AND gt2.model="Document" AND gt2.model_id=d.id AND (%s))' . "\n",
            $this->settings['document_type_repayment_plan'],
            $this->settings['document_substatus_active'],
            implode(' OR ', $filters)
        );
        $rows = $this->registry['db']->GetAll($sql);

        $documents_ids = array_unique(array_column($rows, 'id'));
        $gt2_rows = array_column($rows, 'gt2_row');

        if (empty($documents_ids)) {
            // there are no documents to be updated
            return;
        }

        $filters = array(
            'where' => array('d.id IN (' . implode(',', $documents_ids) . ')'),
            'model_lang' => $this->registry['lang']
        );
        $documents_list = Documents::search($this->registry, $filters);

        $updated = 0;
        foreach ($documents_list as $document) {
            $document->unsanitize();
            $document->getVars();
            $gt2 = $document->getGT2Vars();
            $old_document = clone $document;

            $doc_gt2 = array_intersect(array_keys($gt2['values']), $gt2_rows);
            foreach ($doc_gt2 as $rw) {
                if (!array_key_exists($gt2['values'][$rw]['article_id'], $current_stages)) {
                    continue;
                }
                $gt2['values'][$rw]['article_code'] = $current_stages[$gt2['values'][$rw]['article_id']];
            }

            $document->set('grouping_table_2', $gt2, true);
            $document->calculateGT2();
            $document->set('table_values_are_set', true, true);

            $this->registry['db']->StartTrans();
            if ($document->saveGT2Vars()) {
                $filters = array(
                    'where'      => array('d.id = ' . $document->get('id')),
                    'model_lang' => $document->get('model_lang')
                );
                $document_new = Documents::searchOne($this->registry, $filters);
                $document_new->getVars();
                $params_history = array(
                    'action_type' => 'edit',
                    'old_model'   => $old_document,
                    'model'       => $document,
                    'new_model'   => $document_new
                );
                Documents_History::saveData($this->registry, $params_history);
                $updated++;
            } else {
                $this->registry['db']->FailTrans();
                $link = sprintf('%s/index.php?%s=documents&documents=view&view=%d',
                                $this->registry['config']->getParam('crontab', 'base_host'),
                                $this->registry['module_param'], $document->get('id'));
                $this->registry['messages']->setError(sprintf($this->i18n('error_automation_update_related_document_failed'), $link, $document->get('full_num')));
            }
            $this->registry['db']->CompleteTrans();
        }

        if ($updated) {
            $message = ($updated>1 ? 'message_automation_update_related_documents_successfully_plural' : 'message_automation_update_related_documents_successfully_singular');
            $this->registry['messages']->setMessage(sprintf($this->i18n($message), $updated));
        }
        $this->registry['messages']->insertInSession($this->registry);
    }

    /**
     * Function to prepare the stages in the required format from the assoc vars
     *
     * @param $assoc_vars
     * @return array
     */
    private function getStages($assoc_vars): array
    {
        $stages = array();
        $stages_ids = $assoc_vars[$this->settings['building_var_stage']]['value'] ?? array();
        $stages_dates = $assoc_vars[$this->settings['building_var_date']]['value'] ?? array();
        foreach ($stages_ids as $k => $n_stg) {
            if (isset($stages[$n_stg])) {
                continue;
            }
            $stages[$n_stg] = ($stages_dates[$k] ?? '');
        }
        return $stages;
    }

}
