<?php

/**
 * Automations to handle regular tasks creation in TANDER
 */
class Tander_Automations_Controller extends Automations_Controller {

    /**
     * Create regular tasks based on the customers tags
     *
     * @return bool - the result of the operation
     */
    public function createTasksFromCustomersTags($params) {
        require_once PH_MODULES_DIR . 'events/models/events.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';

        $settings = $this->getSettings($params);

        $tags_groups = preg_split('/\s*\,\s*/', $settings['included_tags_groups']);
        $tags_groups = array_filter($tags_groups);
        $customers_types = preg_split('/\s*\,\s*/', $settings['included_customers_types']);
        $customers_list = array();
        $documents_list_settings = array();
        $db = $this->registry['db'];
        $where_documents = array();
        $document_statuses_clauses = array();

        // get the tags list
        $tags_list = array();
        if (!empty($tags_groups)) {
            $sql = 'SELECT `id`, `section`' . "\n" .
                   'FROM ' . DB_TABLE_TAGS . "\n" .
                   'WHERE `active`=1 AND `deleted_by`=0 AND `model`="customers" AND `section` IN ("' . implode('","', $tags_groups) . '")' . "\n";
            $tags_list = $db->GetAssoc($sql);
        }

        // prepare filter for rows without completed customer
        $where_documents[] = sprintf('(gt2.article_deliverer="" AND gt2.free_field1 IN ("%s"))', implode('","', array_keys($tags_list)));

        if (!empty($tags_list) && !empty($customers_types)) {
            $sql = 'SELECT c.id, CONCAT(ci18n.name, " ", ci18n.lastname) as name, tm.tag_id as tag, c.type as type, c.group' . "\n" .
                   'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                   ' ON (ci18n.parent_id=c.id AND ci18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                   ' ON (tm.model_id=c.id AND tm.model="Customer" AND tm.tag_id IN ("' . implode('","', array_keys($tags_list)) . '"))' . "\n" .
                   'WHERE c.type IN ("' . implode('","', $customers_types) . '") AND c.subtype="normal" AND c.active=1 AND c.deleted_by=0' . "\n";
            $customers_for_tasks = $db->GetAll($sql);

            foreach ($customers_for_tasks as $cft) {
                if (!isset($customers_list[$cft['id']])) {
                    $customers_list[$cft['id']] = array(
                        'id'          => $cft['id'],
                        'name'        => $cft['name'],
                        'type'        => $cft['type'],
                        'group'       => $cft['group']
                    );
                }
                $customers_list[$cft['id']]['tags'][] = $cft['tag'];
                $where_documents[] = sprintf('(gt2.article_deliverer="%d" AND gt2.free_field1="%d")', $cft['id'], $cft['tag']);
            }

            $where_documents = array_unique($where_documents);
            $where_documents = array_values($where_documents);
        }

        if (!empty($settings['document_status'])) {
            $document_statuses_clauses[] = sprintf('CONCAT(d.status, "_", d.substatus)="%s"', $settings['document_status']);
        } else {
            $document_statuses_clauses[] = 'd.status IS NOT NULL';
        }

        if (!empty($where_documents)) {
            // get the vars for nomenclature periodicity
            $nom_vars = array(
                $settings['nomenclature_periodicity_executions'],
                $settings['nomenclature_periodicity_execution_period']
            );
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . $settings['nomenclature_periodicity_type'] . '" AND `name` IN ("' . implode('","',
                    $nom_vars) . '")';
            $nom_vars = $db->GetAssoc($sql);

            // get the documents settings
            $sql = 'SELECT d.id as doc_id, gt2.*, gt2i18n.*, n_cstm_rep.value as repetition, n_cstm_per.value as period' . "\n" .
                'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                ' ON (d.id=gt2.model_id AND gt2.model="Document" AND (' . implode(' OR ',
                    $where_documents) . '))' . "\n" .
                'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                ' ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                ' ON (nom.id=gt2i18n.free_text3)' . "\n" .
                'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as n_cstm_rep' . "\n" .
                ' ON (n_cstm_rep.model_id=nom.id AND n_cstm_rep.var_id="' . $nom_vars[$settings['nomenclature_periodicity_executions']] . '" AND (n_cstm_rep.lang="" OR n_cstm_rep.lang="' . $this->registry['lang'] . '"))' . "\n" .
                'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' as n_cstm_per' . "\n" .
                ' ON (n_cstm_per.model_id=nom.id AND n_cstm_per.var_id="' . $nom_vars[$settings['nomenclature_periodicity_execution_period']] . '" AND (n_cstm_per.lang="" OR n_cstm_per.lang="' . $this->registry['lang'] . '"))' . "\n" .
                'WHERE d.id IN (SELECT MAX(d.id) FROM ' . DB_TABLE_DOCUMENTS . ' d WHERE d.type="' . $settings['document_settings_doc'] . '"' . (!empty($document_statuses_clauses) ? sprintf(' AND (%s)',
                    implode(' OR ',
                        $document_statuses_clauses)) : '') . ' AND d.deleted_by=0 AND d.active=1 GROUP BY d.type)' . "\n";
            $doc_settings = $db->GetAll($sql);

            $fields_relations = array(
                'tag'                => 'free_field1',
                'customer'           => 'article_deliverer',
                'task_name'          => 'free_text2',
                'task_description'   => 'article_description',
                'repetition_nom'     => 'free_text3',
                'duration'           => 'quantity',
                'planned_time'       => 'free_text5',
                'start_date'         => 'article_second_code',
                'task_priority'      => 'free_field2',
                'task_status'        => 'free_field3',
                'assign_responsible' => 'free_field4',
                'assign_owner1'      => 'article_alternative_deliverer',
                'assign_owner2'      => 'article_id',
                'assign_owner3'      => 'article_height',
                'assign_owner4'      => 'article_width',
                'days_in_advance'    => 'article_code',
                'reminders'          => 'article_weight',
                'department'         => 'article_delivery_code',
                'group'              => 'article_trademark',
            );


            // go through doc settings and check for completed tags but empty customers
            $all_customers_list = array_combine(array_keys($doc_settings), array_column($doc_settings, $fields_relations['customer']));
            $all_customers_cleared = array_filter($all_customers_list);
            $empty_customer_rows = array_diff_key($all_customers_list, $all_customers_cleared);

            // process all the rows with empty customers
            if (!empty($empty_customer_rows)) {
                foreach ($empty_customer_rows as $empt_row_key => $selected_tag) {
                    $current_row_settings = $doc_settings[$empt_row_key];
                    $tagToFind =  $current_row_settings[$fields_relations['tag']];
                    $filteredCustomers = array_filter($customers_for_tasks, function($item) use ($tagToFind) {
                        return $item['tag'] == $tagToFind;
                    });
                    $tag_matching_customers = array_column($filteredCustomers, 'id');
                    foreach ($tag_matching_customers as $tag_matching_customer) {
                        $current_row_settings[$fields_relations['customer']] = $tag_matching_customer;
                        $doc_settings[] = $current_row_settings;
                    }
                    unset($doc_settings[$empt_row_key]);
                }
            }

            foreach ($doc_settings as $doc_setting) {
                $key = sprintf('%d_%d', $doc_setting['free_field1'], $doc_setting['article_deliverer']);
                $current_rows_settings = array(
                    'tag_section' => $tags_list[$doc_setting[$fields_relations['tag']]],
                    'document_id' => $doc_setting['doc_id'],
                    'repetition' => $doc_setting['repetition'],
                    'period' => $doc_setting['period'],
                    'current_task_date' => ''
                );

                foreach ($fields_relations as $fr_k => $fr) {
                    $current_rows_settings[$fr_k] = $doc_setting[$fr];
                }
                if (!empty($settings['allow_multiple_tasks_for_tag_customer_combo'])) {
                    if (!isset($documents_list_settings[$key])) {
                        $documents_list_settings[$key] = array();
                    }
                    $documents_list_settings[$key][] = $current_rows_settings;
                } else {
                    $documents_list_settings[$key] = array(0 => $current_rows_settings);
                }
            }

            // process the settings and check if we need tasks today
            $today = new DateTime();
            foreach ($documents_list_settings as $key => $included_tags) {
                foreach ($included_tags as $idx => $doc_tags) {
                    $task_date = new DateTime($doc_tags['start_date']);
                    $days_in_advance = new DateInterval('P' . ($doc_tags['days_in_advance'] ?: 0) . 'D');
                    $check_date = (clone $task_date)->sub($days_in_advance);
                    $period_start = clone $task_date;
                    $create_date = clone $check_date;

                    $periodicity_length = 1;
                    $periodicity_period = $doc_tags['period'];
                    switch ($periodicity_period) {
                        case 'week':
                            $periodicity_period = 'W';
                            break;
                        case 'month':
                            $periodicity_period = 'M';
                            break;
                        case 'trimester':
                            $periodicity_length = 3;
                            $periodicity_period = 'M';
                            break;
                        case 'year':
                            $periodicity_period = 'Y';
                            break;
                        default:
                            $periodicity_period = '';
                            break;
                    }

                    if (!$periodicity_period) {
                        // we don't have periodicity set so skip to next period
                        continue;
                    }
                    $period_length = new DateInterval(sprintf('P%d%s', $periodicity_length, $periodicity_period));

                    $prevent_infinite_loop_counter = 1;
                    while ($check_date->format('Y-m-d') <= $today->format('Y-m-d') && $prevent_infinite_loop_counter < 1000000) {
                        $prevent_infinite_loop_counter++;
                        $period_start = clone $task_date;
                        $create_date = clone $check_date;
                        $task_date->add($period_length);
                        $check_date = (clone $task_date)->sub($days_in_advance);
                    }

                    if ($period_start && $create_date->format('Y-m-d') <= $today->format('Y-m-d')) {
                        // divide period to equal parts
                        $period_end = (clone $period_start)->add($period_length);
                        $interval = $period_start->diff($period_end);

                        $interval_length = ceil($interval->days / $doc_tags['repetition']);

                        for ($j = 0; $j < $doc_tags['repetition']; $j++) {
                            $possible_task_date = (clone $period_start)->add(new DateInterval(sprintf('P%dD',
                                $j * $interval_length)));
                            $possible_task_creation_date = (clone $possible_task_date)->sub($days_in_advance);

                            if ($possible_task_creation_date->format('Y-m-d') == $today->format('Y-m-d')) {
                                $documents_list_settings[$key][$idx]['current_task_date'] = $possible_task_date->format('Y-m-d');
                            } elseif ($possible_task_creation_date->format('Y-m-d') > $today->format('Y-m-d')) {
                                break;
                            }
                        }
                    }
                }
            }

            foreach ($documents_list_settings as $key => $included_tags) {
                foreach ($included_tags as $idx => $doc_tags) {
                    if (empty($documents_list_settings[$key][$idx]['current_task_date'])) {
                        unset($documents_list_settings[$key][$idx]);
                    }
                }
                if (empty($documents_list_settings[$key])) {
                    unset($documents_list_settings[$key]);
                }
            }

            // go through all the customers and create the tasks
            foreach ($documents_list_settings as $list_tags) {
                foreach ($list_tags as $tags_settings) {
                    $db->StartTrans();

                    // Prepare the dataset for the task
                    $new_task_properties = array(
                        'type' => '',
                        'name' => '',
                        'status' => '',
                        'substatus' => 0,
                        'project' => 0,
                        'planned_start_date' => '',
                        'planned_finish_date' => '',
                        'planned_time' => '',
                        'customer' => '',
                        'severity' => '',
                        'description' => '',
                        'department' => '',
                        'group' => $customers_list[$tags_settings['customer']]['group'],
                        'active' => 1
                    );

                    // Prepare the start and the end date of the task
                    $task_start_date = $tags_settings['current_task_date'];
                    $task_end_date = new DateTime($task_start_date);
                    $task_end_date->add(new DateInterval(sprintf('P%dD', $tags_settings['duration'])));

                    // Set the individual data for the current new task
                    $new_task_properties['type'] = $settings['task_for_tag'];
                    $new_task_properties['customer'] = $tags_settings['customer'];
                    $new_task_properties['planned_start_date'] = $tags_settings['current_task_date'];
                    $new_task_properties['planned_finish_date'] = $task_end_date->format('Y-m-d');
                    $new_task_properties['name'] = $tags_settings['task_name'];
                    $new_task_properties['description'] = $tags_settings['task_description'];
                    $new_task_properties['severity'] = $tags_settings['task_priority'];
                    $new_task_properties['status'] = $tags_settings['task_status'];
                    $new_task_properties['planned_time'] = $tags_settings['planned_time'];
                    $new_task_properties['department'] = ($tags_settings['department'] ? $tags_settings['department'] : $settings['department_for_tag']);
                    $new_task_properties['group'] = ($tags_settings['group'] ? $tags_settings['group'] : ($settings['group_for_tag'] ? $settings['group_for_tag'] : $customers_list[$tags_settings['customer']]['group']));

                    // Prepare a model for the new task
                    $task = new Task($this->registry, $new_task_properties);

                    // Try to save the new task
                    if ($task->save()) {
                        // Write history
                        $old_task = new Task($this->registry);
                        $old_task->sanitize();
                        $filters_new_task = array(
                            'where' => array('t.id = \'' . $task->get('id') . '\''),
                            'model_lang' => $task->get('model_lang')
                        );
                        $new_task = Tasks::searchOne($this->registry, $filters_new_task);
                        $audit_parent = Tasks_History::saveData($this->registry, array(
                            'model' => $task,
                            'action_type' => 'add',
                            'new_model' => $new_task,
                            'old_model' => $old_task
                        ));
                        if (!$audit_parent) {
                            $this->executionErrors[] = sprintf($this->i18n('default_tasks_reminder_event_name'),
                                $new_task->get('id'));
                        }

                        // get the users which have to be assigned
                        $owners = array();
                        if (!empty(round($tags_settings['assign_owner1']))) {
                            $owners[] = $tags_settings['assign_owner1'];
                        }
                        if (!empty(round($tags_settings['assign_owner2']))) {
                            $owners[] = $tags_settings['assign_owner2'];
                        }
                        if (!empty(round($tags_settings['assign_owner3']))) {
                            $owners[] = $tags_settings['assign_owner3'];
                        }
                        if (!empty(round($tags_settings['assign_owner4']))) {
                            $owners[] = $tags_settings['assign_owner4'];
                        }
                        $owners = array_unique($owners);
                        $responsibles = array();
                        if (!empty($tags_settings['assign_responsible'])) {
                            $responsibles[] = $tags_settings['assign_responsible'];
                        }

                        if (!empty($owners) || !empty($responsibles)) {
                            // assign
                            $task = $new_task;
                            $old_task = clone $new_task;
                            $old_task->sanitize();
                            $old_task->set('assignments_owner', array(), true);
                            $old_task->set('assignments_responsible', array(), true);
                            $task->unsanitize();

                            if (!empty($owners)) {
                                $task->set('assignments_owner', $owners, true);
                            }
                            if (!empty($responsibles)) {
                                $task->set('assignments_responsible', $responsibles, true);
                            }

                            if ($task->assign(true)) {
                                $task->getAssignments('owner');
                                $task->getAssignments('responsible');
                                $audit_parent = Tasks_History::saveData($this->registry, array(
                                    'model' => $task,
                                    'action_type' => 'assign',
                                    'new_model' => $task,
                                    'old_model' => $old_task
                                ));
                                if ($audit_parent === false) {
                                    $this->executionErrors[] = sprintf($this->i18n('error_save_task_assign_history'),
                                        $task->get('id'));
                                }
                            } else {
                                $db->FailTrans();
                                $this->executionErrors[] = $this->i18n('error_assign_task_failed');
                            }
                        } else {
                            // send information that there is no one assigned to this task
                            $task_view_url = sprintf('%s/index.php?%s=tasks&tasks=view&view=%d',
                                $this->registry['config']->getParam('crontab', 'base_host'),
                                $this->registry['module_param'], $task->get('id'));
                            $this->executionWarnings[] = sprintf($this->i18n('warning_no_task_assignments'), $task_view_url,
                                $new_task->get('type_name'), $new_task->get('full_num'));
                        }

                        $remind_users = array_merge($owners, $responsibles);
                        $remind_users = array_unique($remind_users);
                        unset($new_task);
                        unset($old_task);

                        // Get the event type: reminder
                        $filters_event_type = array('where' => array('et.keyword = \'reminder\''), 'sanitize' => true);
                        $event_type = Events_Types::searchOne($this->registry, $filters_event_type);

                        // If there is such event type
                        if ($event_type) {
                            // Get the default duration
                            $duration = $event_type->getDefaultDuration();

                            foreach ($remind_users as $user_id) {
                                // Prepare the new event
                                $event = Events::buildModel($this->registry);
                                $event->set('id', null, true);
                                $event->set('name',
                                    sprintf($this->i18n('default_tasks_reminder_event_name'), $task->get('name')), true);
                                $event->set('description', '', true);
                                $event->set('event_start', $task->get('planned_start_date'), true);
                                $event->set('duration', $duration, true);
                                $event->set('event_end',
                                    date('Y-m-d H:i:00', strtotime($task->get('planned_start_date')) + 60 * $duration),
                                    true);
                                $event->set('type', $event_type->get('id'), true);
                                $event->set('availability', 'available', true);
                                $event->set('customer', $task->get('customer'), true);
                                $event->set('branch', $task->get('branch'), true);
                                $event->set('contact_person', $task->get('contact_person'), true);
                                $event->set('project', $task->get('project'), true);

                                // Try to save the new event
                                if ($event->save()) {
                                    // Save the relation between the event and the task (in this case the task is parent)
                                    $record = array(
                                        'link_to' => $task->get('id'),
                                        'origin' => 'task',
                                        'link_type' => 'parent'
                                    );
                                    $event->updateRelatives($record);

                                    // Prepare the request to be used for adding of a reminder
                                    $interval = '-' . ($tags_settings['reminders'] ? intval($tags_settings['reminders']) : '0') . ' days';
                                    $reminder_date = General::strftime('%Y-%m-%d %H:%M:%S',
                                        strtotime($interval, strtotime($task->get('planned_start_date'))));

                                    $this->registry['request']->set('reminder_type', 'email', 'all', true);
                                    $this->registry['request']->set('selected_panel', 'date', 'all', true);
                                    $this->registry['request']->set('reminder_date', $reminder_date, 'all', true);
                                    $this->registry['request']->set('custom_message', '', 'all', true);
                                    $this->registry['request']->set('reminder_user_id', $user_id, 'all', true);

                                    // Try to add a reminder
                                    if (!$event->remind()) {
                                        $db->FailTrans();
                                        $this->executionErrors[] = sprintf($this->i18n('error_add_reminder_failed'),
                                            $user_id);
                                    }
                                } else {
                                    $db->FailTrans();
                                    $this->executionErrors[] = sprintf($this->i18n('error_add_reminder_event_failed'),
                                        $user_id);
                                }
                            }
                        } else {
                            $db->FailTrans();
                            $this->executionErrors[] = $this->i18n('error_no_reminder_type');
                        }
                    } else {
                        $this->executionErrors[] = sprintf($this->i18n('error_add_task_failed'),
                            $customers_list[$tags_settings['customer']]['name'], $tags_settings['tag']);
                        $db->FailTrans();
                    }

                    // If the transaction has failed
                    if ($db->HasFailedTrans()) {
                        // Prepare an error
                        $this->executionErrors[] = sprintf($this->i18n('error_add_task_failed_unknown_reason'),
                            $tags_settings['customer'], $tags_settings['tag']);
                    }

                    // Complete the transaction for the current new task
                    $db->CompleteTrans();
                }
            }
        }

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /**
     * Function to remind the users for assigned documents
     *
     * @return bool - the result of the operation
     */
    public function documentReminders($params) {
        // get the types
        $registry = $this->registry;
        $db = $registry['db'];

        $types = preg_split('#\s*,\s*#', $this->settings['included_documents_types']);

        $vars = array($this->settings['remind_for'], $this->settings['remind_period'], $this->settings['remind_date'], $this->settings['remind_notes']);
        $sql = 'SELECT `name`, GROUP_CONCAT(`id`) FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type` IN ("' . implode('","', $types) . '") AND `name` IN ("' . implode('","', $vars) . '") GROUP BY `name`';
        $vars = $registry['db']->GetAssoc($sql);

        // get the documents
        $sql = 'SELECT d.id, DATE_FORMAT(' . $this->settings['date_field'] . ', "%Y-%m-%d") as date, d_rem_for.value as remind_for, ni18n.name as remind_for_name, d_rem_period.value as remind_period, d_rem_date.value as remind_date, d_rem_notes.value as remind_notes' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
               ' ON (di18n.parent_id=d.id AND di18n.lang="' . $registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_rem_for' . "\n" .
               ' ON (d_rem_for.var_id IN (' . (!empty($vars[$this->settings['remind_for']]) ? $vars[$this->settings['remind_for']] : '""') . ') AND d_rem_for.model_id=d.id AND (d_rem_for.lang="" OR d_rem_for.lang="' . $registry['lang'] . '"))' . "\n" .
               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
               ' ON (ni18n.parent_id=d_rem_for.value AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_rem_period' . "\n" .
               ' ON (d_rem_period.var_id IN (' . (!empty($vars[$this->settings['remind_period']]) ? $vars[$this->settings['remind_period']] : '""') . ') AND d_rem_period.model_id=d.id AND (d_rem_period.lang="" OR d_rem_period.lang="' . $registry['lang'] . '"))' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_rem_date' . "\n" .
               ' ON (d_rem_date.var_id IN (' . (!empty($vars[$this->settings['remind_date']]) ? $vars[$this->settings['remind_date']] : '""') . ') AND d_rem_date.model_id=d.id AND (d_rem_date.lang="" OR d_rem_date.lang="' . $registry['lang'] . '"))' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_rem_notes' . "\n" .
               ' ON (d_rem_notes.var_id IN (' . (!empty($vars[$this->settings['remind_notes']]) ? $vars[$this->settings['remind_notes']] : '""') . ') AND d_rem_notes.model_id=d.id AND (d_rem_notes.lang="" OR d_rem_notes.lang="' . $registry['lang'] . '"))' . "\n" .
               'WHERE d.type IN ("' . implode('","', $types) . '") AND d.active=1 AND d.deleted_by=0 AND d.status!="closed"' . "\n";
        $documents_info = $db->GetAssoc($sql);

        foreach ($documents_info as $idx => $doc) {
            if (empty($doc['date']) || empty($doc['remind_period']) || empty($doc['remind_date']) || $doc['date'] == '0000-00-00') {
                unset($documents_info[$idx]);
                continue;
            }

            $startDate_date_chk = DateTime::createFromFormat('Y-m-d', $doc['date']);
            $sd_dm = $startDate_date_chk->format('t');
            $first_date_remind_day = $sd_dm<=$doc['remind_date'] ? $sd_dm : $doc['remind_date'];
            $document_date = General::strftime('%Y-%m', strtotime($doc['date'])) . sprintf('-%02d', $first_date_remind_day);

            $startDate_date = DateTime::createFromFormat('Y-m-d', $document_date);
            $today_date = DateTime::createFromFormat('Y-m-d', date('Y-m-d'));
            if ($startDate_date == $today_date) {
                unset($documents_info[$idx]);
                continue;
            }

            $sM = $startDate_date->format('m');
            $sY = $startDate_date->format('Y');

            $tDL = $today_date->format('t');
            $tD = $today_date->format('d');
            $tM = $today_date->format('m');
            $tY = $today_date->format('Y');

            $year_remind = floor($doc['remind_period'] / 12);
            $month_remind =  $doc['remind_period'] % 12;

            $yearCriteria = (!$year_remind ? 0 : (($tY - $sY) - ($sM > $tM ? 1 : 0)) % $year_remind);
            $monthCriteria = (!$month_remind ? ($tM != $sM) : (($tY - $sY)*12 + ($tM - $sM)) % $doc['remind_period']);
            $dayCriteria = $doc['remind_date']<=$tDL ? ($doc['remind_date'] - $tD) : ($tD - $tDL);

            if ($yearCriteria || $monthCriteria || $dayCriteria) {
                unset($documents_info[$idx]);
                continue;
            }
        }

        if (empty($documents_info)) {
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }

        // get the email temaplate
        $filters = array('where' => array('e.id IN ("' . $this->settings['email_template'] . '")'), 'sanitize' => true);
        $mail = Emails::searchOne($this->registry, $filters);

        // get the models
        $filters = array('where' => array('d.id IN ("' . implode('","', array_keys($documents_info)) . '")'), 'sanitize' => false);
        $documents = Documents::search($this->registry, $filters);

        foreach ($documents as $document) {
            $assoc_vars = $document->getAssocVars();
            $assignments = $document->getAssignments('owner') + $document->getAssignments('responsible') + $document->getAssignments('observer') + $document->getAssignments('decision');

            //get the user to be notified
            $recipients = Users::search($this->registry, array('where' => array('u.id IN ("' . implode('","', array_keys($assignments)) . '")'), 'sanitize' => true));

            // set the emails
            $customer_email = array();
            foreach ($recipients as $recipient) {
                $customer_email[] = $recipient->get('firstname') . ' ' . $recipient->get('lastname') . '<' . $recipient->get('email') . '>';
            }

            if (empty($customer_email)) {
                continue;
            }

            // set the properties for the e-mail
            $document->set('body', $mail->get('body'), true);
            $document->set('email_subject', $documents_info[$document->get('id')]['remind_for_name'], true);
            $document->set('email_template', $mail->get('id'), true);
            $document->set('customer_email', $customer_email, true);

            if ($document->sanitized) {
                $document->unsanitize();
            }
            if (!$document->sendAsMail()) {
                $this->executionWarnings[] = 'Failed to send e-mail for document with ID ' . $document->get('id') . '!';
            }
        }

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }
}

?>
