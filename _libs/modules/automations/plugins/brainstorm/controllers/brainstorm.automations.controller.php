<?php

class Brainstorm_Automations_Controller extends Automations_Controller
{
    /**
     * Contains Jira epics, to prevent duplicate requests.
     */
    private $jira_epics = array();

    /**
     * Contains messages to be send to email
     */
    private $email_notifications = array();

    /**
     * Contains filter date from which Jira issues will be imported;
     */
    private $execution_interval = '';

    public function updateWorkedTime($params)
    {
        $registry = $this->registry;
        $sinhron_data = array();

        if (isset($this->settings['record_date'])) {
            // Sanitize record_date by removing all non-numeric characters, default to 1 if empty.
            $record_date_begin = (empty($this->settings['record_date'])) ? 1 : (int)str_replace(' ', '', $this->settings['record_date']);

            $sinhron_query = "
            SELECT * FROM TCExportData
            WHERE
              CAST(TimeBegin AS DATE) >= DATEADD(day, -{$record_date_begin}, CAST(getdate() AS DATE))
              AND
              CAST(TimeBegin AS DATE) < CAST(getdate() AS DATE)
              AND
              (
              	TimeEnd IS NULL
              	OR
              	(
              	  CAST(TimeEnd AS DATE) >= DATEADD(day, -{$record_date_begin}, CAST(getdate() AS DATE))
              	  AND
              	  CAST(TimeEnd AS DATE) < CAST(getdate() AS DATE)
              	)
              )";
        }

        // Decide whether to connect to DB using CURL or ADODB, depending on whether AD is run on live servers.
        if (isset($this->settings['is_test']) && $this->settings['is_test'] == '1') { // Dev + Test
            $url = 'https://tt.n-zoom.com/diag/brainstorm_mssql.php';
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, "query=$sinhron_query");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $sinhron_data = json_decode(curl_exec($ch), true);
            curl_close($ch);
        } else { // Live
            $db_settings = [
                'type' => $this->settings['mssql_type'],
                'host' => $this->settings['mssql_host'],
                'port' => $this->settings['mssql_port'],
                'user' => $this->settings['mssql_user'],
                'pass' => General::decrypt($this->settings['mssql_pass'], 'mssql_pass', 'xtea'),
                'name' => $this->settings['mssql_name'],
            ];
            $db = ADONewConnection($db_settings['type']);
            $connected = $db->Connect($db_settings['host'] . ',' . $db_settings['port'], $db_settings['user'], $db_settings['pass'], $db_settings['name']);
            if (!$connected) {
                $this->updateAutomationHistory($params, 0, 0);
                $this->executionErrors[] = $this->i18n('error_while_connecting');
                return false;
            }
            $db->SetFetchMode(ADODB_FETCH_ASSOC);
            $sinhron_data = $db->GetAll($sinhron_query);
        }

        // Fix the personal Numbers to fixed ten symbols length
        foreach ($sinhron_data as $sync_data_key => $sync_data) {
            if (!$sync_data['PersonalNumber']) {
                continue;
            }
            $sinhron_data[$sync_data_key]['PersonalNumber'] = str_pad($sync_data['PersonalNumber'], 10, "0", STR_PAD_LEFT);
        }

        /*
         * Log Synchron data
         */
        if (array_key_exists('log_synchron_data', $this->settings) && $this->settings['log_synchron_data'] === '1') {
            // Sort the data
            if (!empty($this->settings['log_synchron_data_ordered_by'])) {
                $sinhron_data_first_element = reset($sinhron_data);
                if (array_key_exists($this->settings['log_synchron_data_ordered_by'], $sinhron_data_first_element)) {
                    uasort($sinhron_data, function ($a, $b) {
                        return $a[$this->settings['log_synchron_data_ordered_by']] > $b[$this->settings['log_synchron_data_ordered_by']] ?: 0;
                    });
                }
            }

            // Log the event, query and data
            $log_event = __METHOD__ . ':' . 'log_synchron_data';
            $log_extra = [
                'synchron_query' => $sinhron_query,
                'synchron_data'  => $sinhron_data,
            ];
            $log_extra = print_r($log_extra, true);
            General::log($registry, $log_event, $log_extra);
        }

        if (empty($sinhron_data)) {
            $this->updateAutomationHistory($params, 0, 1);
            $this->executionWarnings[] = $this->i18n('warning_no_data');
            return true;
        }

        $egn_list = array_filter(array_unique(array_column($sinhron_data, 'PersonalNumber')));
        $clients = [];
        if (!empty($egn_list)) {
            $where_in = '("' . implode('", "', $egn_list) . '")';
            // Match customers with given UCN's
            $query = $registry['db']->prepare("
                 SELECT c.ucn AS identifier,
                        c.id,
                        TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS `name`
                   FROM " . DB_TABLE_CUSTOMERS . " AS c
                   JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                     ON (c.id = ci.parent_id
                       AND ci.lang = '{$registry['lang']}'
                       AND c.ucn IN {$where_in}
                       AND c.active = 1
                       AND c.deleted = 0
                       AND c.type = " . PH_CUSTOMER_EMPLOYEE . ")");
            $clients = $registry['db']->GetAssoc($query);

        }

        if (empty($clients)) {
            $this->updateAutomationHistory($params, 0, 1);
            $this->executionWarnings[] = $this->i18n('warning_no_clients_found');
            return true;
        }

        $results = array();
        // Format data in to easy to use array
        foreach ($sinhron_data as $record) {
            // If Sinhron's customers exist in our records
            if (array_key_exists(strval($record['PersonalNumber']), $clients)) {
                // Skip records without TimeBegin
                if (empty($record['TimeBegin'])) {
                    continue;
                }

                $date_time_begin = new DateTime($record['TimeBegin']);
                $date_time_end = (!empty($record['TimeEnd'])) ? new DateTime($record['TimeEnd']) : null;
                $date_begin = $date_time_begin->format('Y-m-d');
                $workday_end_time = clone $date_time_begin;
                $workday_end_time = $workday_end_time->setTime(18, 30);
                // Skip records with checkin time beyond 18:30 AND without checkout date
                if (empty($record['TimeEnd'])) {
                    if ($date_time_begin > $workday_end_time) {
                        continue;
                    }
                } else {
                    $date_end = $date_time_end->format('Y-m-d');
                    // Skip record with checkin and checkout in different days
                    if ($date_end != null && $date_begin != $date_end) {
                        if ($date_time_begin < $workday_end_time) {
                            $record['TimeEnd'] = '';
                        } else {
                            continue;
                        }
                    }
                }

                //Save record as result
                $results[$date_begin][$clients[$record['PersonalNumber']]['id']][] = $record;
            }
        }

        // Sort array dates ASC
        uksort($results, function ($a, $b) {
            return (new DateTime($a) < new DateTime($b)) ? -1 : 1;
        });

        // Filter, remove unnecessary records and sum users worked minutes
        foreach ($results as $date => $users) {
            foreach ($users as $user_id => $records) {
                // Sort records by TimeBegin OR TimeEnd ASC
                usort($results[$date][$user_id], function ($a, $b) {
                    if (!empty($a['TimeBegin']) && !empty($b['TimeBegin'])) {
                        return (new DateTime($a['TimeBegin']) < new DateTime($b['TimeBegin'])) ? -1 : 1;
                    } elseif (!empty($a['TimeEnd']) && !empty($b['TimeEnd'])) {
                        return (new DateTime($a['TimeEnd']) < new DateTime($b['TimeEnd'])) ? -1 : 1;
                    } else {
                        return 0;
                    }
                });

                // Unset consecutive records with TimeBegin less than 1 minute apart
//                if (count($records) > 1) {
//                    $previous_record_time_end = '';
//                    foreach ($results[$date][$user_id] as $record_index => $record) {
//                        $less_than = date('Y-m-d H:i:s', strtotime('-1 minute', strtotime($record['TimeBegin'])));
//                        if (!empty($previous_record_time_end) && ($previous_record_time_end > $less_than)) {
//                            unset($results[$date][$user_id][$record_index]);
//                            break;
//                        }
//                        $previous_record_time_end = date('Y-m-d H:i:s', strtotime($record['TimeEnd']));
//                    }
//                }

                // Set empty TimeEnd value to end of workday (18:30). Only affects last element of user records.
                $last_record_time_begin = end($results[$date][$user_id])['TimeBegin'];
                $last_record_time_end = end($results[$date][$user_id])['TimeEnd'];
                if (!empty($last_record_time_begin) && empty($last_record_time_end)) {
                    $last_record = array_pop($results[$date][$user_id]);
                    $date_begin = new DateTime($last_record_time_begin);
                    $date_end = new DateTime($date_begin->format('m/d/Y'));

                    $date_end->add(new DateInterval('PT18H30M'));
                    $last_record['TimeEnd'] = $date_end->format('Y-m-d\TH:i:s\Z');
                    array_push($results[$date][$user_id], $last_record);
                }

                foreach ($results[$date][$user_id] as $record_id => $record) {
                    // Remove records with empty TimeEnd, which are not at the end of the array
                    if (sizeof($results[$date][$user_id]) > 1) {
                        if (empty($results[$date][$user_id][$record_id]['TimeEnd'])) {
                            unset($results[$date][$user_id][$record_id]);
                            continue;
                        }
                    }

                    // Sum minutes worked for each user
                    if (!empty($record['TimeBegin']) && !empty($record['TimeEnd'])) {
                        $date_time_begin = new DateTime($record['TimeBegin']);
                        $date_time_end = new DateTime($record['TimeEnd']);

                        $date_diff = $date_time_begin->diff($date_time_end);
                        $minutes = $date_diff->days * 24 * 60;
                        $minutes += $date_diff->h * 60;
                        $minutes += $date_diff->i;
                        $results[$date][$user_id][$record_id]['sum_minutes'] = $minutes;
                    } else {
                        $results[$date][$user_id][$record_id]['sum_minutes'] = 0;
                    }
                }
            }

            $results_flat = array();
            // Create document insert array
            foreach ($results[$date] as $user_id => $records) {
                foreach ($records as $k => $record) {

                    $date_out = (!empty($record['TimeEnd'])) ? new DateTime($record['TimeEnd']) : false;
                    $date_in = (!empty($record['TimeBegin'])) ? new DateTime($record['TimeBegin']) : false;

                    $results_flat['employee_ucn'][] = $record['PersonalNumber'];
                    $results_flat['employee_name'][] = $clients[$record['PersonalNumber']]['name'];
                    $results_flat['hour_in'][] = ($date_in) ? $date_in->format('Y-m-d H:i:s') : null;
                    $results_flat['hour_out'][] = ($date_out) ? $date_out->format('Y-m-d H:i:s') : null;
                    $results_flat['hour_sum'][] = $record['sum_minutes'];
                    $results_flat['employee_id'][] = $user_id;
                }
            }

            $documentType = $params['start_model_type'];
            $filters = array(
                'where' => array(
                    "d.type = {$documentType}",
                    "d.date = '{$date}'"
                ),
                'skip_assignments' => true,
                'skip_permissions_check' => true
            );
            $action = 'edit';

            $workedTimeDocument = Documents::searchOne($registry, $filters);
            //if no Document found for the specified day we start building new one.
            if (!$workedTimeDocument) {
                $action = 'add';
                //start building new document
                $workedTimeDocument = new Document($registry, array('type' => $documentType));
                $registry->set('get_old_vars', true, true);
                $workedTimeDocument->getVars();
                $registry->set('get_old_vars', false, true);
                $oldDocument = clone $workedTimeDocument;
                $workedTimeDocument->set('date', $date);
                $filters = array(
                    'where' => array(
                        "dt.id = {$documentType}",
                    ),
                    'sanitize' => true,
                    'model_lang' => $registry['lang']
                );
                $docType = Documents_Types::searchOne($registry, $filters);
                $workedTimeDocument->set('name', $docType->get('default_name'));
                $workedTimeDocument->set('customer', $docType->get('default_customer'));
                $workedTimeDocument->set('group', $docType->getDefaultGroup());
                $workedTimeDocument->set('department', $docType->getDefaultDepartment());
            } else {
                //get the variables for audit
                $registry->set('get_old_vars', true, true);
                $workedTimeDocument->getVars();
                $oldDocument = clone $workedTimeDocument;
                $registry->set('get_old_vars', false, true);
            }

            // Update with missing data
            $fields_name = array_keys($results_flat);
            $addVars = $workedTimeDocument->get('vars');
            if (!empty($addVars)) {
                foreach ($addVars as &$var) {
                    if (in_array($var['name'], $fields_name)) {
                        $var['value'] = $results_flat[$var['name']];
                    }
                }
            }

            $workedTimeDocument->set('vars', $addVars, true);
            $registry['db']->StartTrans();
            if ($workedTimeDocument->save()) {
                $filters = array('where' => array('d.id = ' . $workedTimeDocument->get('id')), 'sanitize' => true, 'skip_assignments' => true, 'skip_permissions_check' => true);
                $new_model = Documents::searchOne($registry, $filters);
                $registry->set('get_old_vars', true, true);
                $new_model->getVars();
                $this->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');

                $history_id = Documents_History::saveData($registry,
                    array('model' => $workedTimeDocument,
                        'action_type' => $action,
                        'new_model' => $new_model,
                        'old_model' => $oldDocument));
                if (!$history_id) {
                    $registry['db']->FailTrans();
                }
            } else {
                $registry['db']->FailTrans();
            }

            $has_failed_trans = $registry['db']->HasFailedTrans();
            $registry['db']->CompleteTrans();
            if (!$has_failed_trans) {
                $this->updateAutomationHistory($params, $workedTimeDocument, 1);
            } else {
                $this->updateAutomationHistory($params, $workedTimeDocument, 0);
            }

            if ($has_failed_trans) {
                $this->executionErrors[] = $this->i18n('error_while_saving');
                return false;
            }
        }

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /**
     * Gets all new/updated issues from Jira, and saves their values in Nzoom tasks + timesheets
     */
    public function copyTasksFromJira($params)
    {

        $registry = $this->registry;
        $required_settings = array('username', 'api_token', 'domain', 'default_customer_id', 'default_planned_finish_date', 'content_text', 'period', 'import_period');
        $messages = $registry['messages'];

        // Functionality could be configured to work as other automation types in future.
        if ($params['automation_type'] != 'crontab') {
            $messages->setError('Automation isnt configured to be used as any type other than Crontab!');
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        // Check for missing required settings
        foreach ($required_settings as $required_setting) {
            if (!array_key_exists($required_setting, $this->settings)) {
                $this->executionErrors[] = "Required setting {$required_setting} not given!";
                $this->updateAutomationHistory($params, 0, 0);
                return false;
            }
        }

        $today = new DateTime(date("Y-m-d H:i"));
        $date_from = $this->_getImportDateFrom();
        $today->add(new DateInterval('PT1M')); // Fix to problem => https://jira.atlassian.com/browse/JRASERVER-31250
        $date_from->sub(new DateInterval('PT1M'));
        $pagination_filters = array(
            "jql" => "updated >= \"" . $date_from->format('Y-m-d H:i') . "\" and updated <= \"" . $today->format('Y-m-d H:i') . "\" order by updated asc",
//            "jql" => "key = DEV-1091",
            "maxResults" => 1,
            "startAt" => 0,
            "expand" => ['changelog']
        );

        // Get pagination data
        $maxResults = 100;
        $pagination = $this->_getIssuesFromJira($pagination_filters);
        $pages = ceil(($pagination['total']) / $maxResults);

        // Increase time limit when there are significantly more issues to import. (800 issues ~ 8 minutes)
        if ($pagination['total'] > 50) {
            set_time_limit($pagination['total'] * 5);
        }

        // Request for Jira issues using pagination
        for ($i = 0; $i < $pages; $i++) {
            $page = $i * $maxResults;
            $jira_filters = $pagination_filters;
            $jira_filters["maxResults"] = $maxResults;
            $jira_filters["startAt"] = $page;
            $results = $this->_getIssuesFromJira($jira_filters);
            $this->_processIssues($results['issues'], $params);
        }

        if ($this->registry['messages']) {
            foreach ($this->registry['messages'] as $message) {
                $this->executionWarnings[] = $message;
            }
        }

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /**
     * Calculate date from which we need to import Jira issues.
     *
     * @return DateTime
     * @throws Exception
     */
    private function _getImportDateFrom()
    {
        preg_match('/^([\d]*)([a-z]*)$/', $this->settings['import_period'], $matches);
        $record_period = $matches[1];
        $period_type = $matches[2];
        $record_date = '';

        if ($period_type == 'days') {
            $today = new DateTime(date("Y-m-d"));
            $record_date = $today->sub(new DateInterval("P" . $record_period . "D"));
        } else if ($period_type == 'minutes') {
            $today = new DateTime(date("Y-m-d H:i:s"));
            $record_date = $today->sub(new DateInterval("PT" . $record_period . "M"));
        }

        return $record_date;
    }

    /**
     * Handle import of Jira issues, their worklogs and related automations.
     *
     * @param $issues
     * @param $params
     * @return bool
     */
    private function _processIssues($issues, $params)
    {
        if ($issues) {
            $issue_names = $this->_getIssuesNames($issues);
            $problems = false;
            $issue_keys = array();

            // Create / Update task values
            foreach ($issues as $index => $issue) {
                $issue_keys[] = $issue['key'];
                $task = $this->_getTaskByEquipment($issue['key']);
                if ($task) {
                    if ($this->_shouldTaskUpdate($issue)) {
                        $new_result = $this->_updateTaskData($task, $issue);
                    }
                } else {
                    $new_result = $this->_createNewTask($issue);
                }

                if (!$new_result) {
                    $this->executionWarnings[$issue['key']] = 'Something went wrong while saving Jira issue ' . $issue['key'] . ' in Nzoom!';
                    $problems = true;
                }
            }

            // Get worklogs from Jira
            $worklogs = $this->_getIssuesWorklogs($issue_keys);
            if (!$worklogs) {
                $this->executionWarnings[] = 'No worklogs returned from Jira.';
            } else {
                // Save worklogs as timsheets
                $this->_saveWorklogs($worklogs, $issue_names);
            }

            if ($problems) {
                $this->updateAutomationHistory($params, 0, 0);
                return false;
            }
        } else {
            $this->executionWarnings[] = 'No issues returned from Jira';
        }

        // Send emails about failed automations
        if (!empty($this->email_notifications)) {
            $this->_sendFailedAutomationsNotification();
        }

        return true;
    }

    /**
     * Validate add task
     */
    public function validateAddTask($params)
    {
        $registry = &$this->registry;
        $error = false;

        // check if the current user is part of the selected department
        if ($this->settings['check_matching_user_department'] && $registry['request']->get('department')) {
            require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';

            if (!in_array($registry['request']->get('department'), $registry['originalUser']->get('departments'))) {
                $error = true;
                if (!empty($registry['originalUser']->get('departments'))) {
                    $sql = 'SELECT `name` FROM ' . DB_TABLE_DEPARTMENTS_I18N . ' WHERE `parent_id` IN (' . implode(',', $registry['originalUser']->get('departments')) . ') AND `lang`="' . $registry['lang'] . '"';
                    $users_departments_names = $registry['db']->GetCol($sql);
                    $registry['messages']->setError(sprintf($this->i18n('error_current_user_not_in_selected_department'), implode(', ', $users_departments_names)));
                } else {
                    $registry['messages']->setError($this->i18n('error_current_user_no_selected_department'));
                }
            }
        }

        // check the severity
        if (($params['model']->modelName == 'Task' && $registry['request']->get('severity') == 'veryheavy') ||
            ($params['model']->modelName == 'Document' && !empty($this->settings['doc_high_priority_var']) && $registry['request']->get($this->settings['doc_high_priority_var']) == $this->settings['doc_high_priority_value'])) {
            if (isset($this->settings['high_priority_users'])) {
                $high_priority_users = array_filter(preg_split('/\s*,\s*/', $this->settings['high_priority_users']));
                if (!in_array($registry['originalUser']->get('id'), $high_priority_users)) {
                    // get the type name
                    $sql = 'SELECT `name_plural` FROM ' . ($params['model']->modelName == 'Task' ? DB_TABLE_TASKS_TYPES_I18N : DB_TABLE_DOCUMENTS_TYPES_I18N) . ' WHERE `parent_id`="' . $params['model']->get('type') . '" AND `lang`="' . $registry['lang'] . '"';
                    $registry['messages']->setError(sprintf($this->i18n('error_no_rights_to_add_very_high_priority_tasks'), mb_strtolower($registry['db']->GetOne($sql))));
                    $error = true;
                }
            }
        }

        // check if the decision maker will be different than the current user
        if ($registry['originalUser']->get('employee')) {
            //get ids for additional variables
            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `name`="' . $this->settings['employee_manager_var'] . '" AND `model`="Customer" AND `model_type`=' . PH_CUSTOMER_EMPLOYEE;
            $manager_var_id = $registry['db']->GetOne($sql);

            $sql = 'SELECT `value` FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' WHERE `model_id`="' . $registry['originalUser']->get('employee') . '" AND `var_id`="' . $manager_var_id . '"' . "\n";
            $manager_id = $registry['db']->GetOne($sql);

            if ($manager_id == $registry['originalUser']->get('id')) {
                $registry['messages']->setError($this->i18n('error_employee_assignments_mismatch'));
                $error = true;
            }
        }

        if ($error) {
            $registry['messages']->insertInSession($this->registry);
        }

        return !$error;
    }

    /**
     * Validate assign task
     */
    public function validateAssignTask($params)
    {
        $registry = &$this->registry;
        $error = false;
        $current_assignments_owners = $registry['request']->get('assignments_owner');
        $current_assignments_decision = $registry['request']->get('assignments_decision');

        $params['model']->set('assignments_owner', $registry['request']->get('assignments_owner'), true);

        $matching_users = array_intersect($current_assignments_owners, $current_assignments_decision);
        if (!empty($matching_users)) {
            $error = true;

            // prepare text error
            $sql = 'SELECT CONCAT(`firstname`, " ", `lastname`) as name FROM ' . DB_TABLE_USERS_I18N . ' WHERE `parent_id` IN (' . implode(',', $matching_users) . ') AND `lang`="' . $registry['lang'] . '"' . "\n";
            $overlapping_users = $registry['db']->GetCol($sql);
            $registry['messages']->setError(sprintf($this->i18n('error_overlapping_owner_decision_users'), implode(', ', $overlapping_users)));
            $registry['messages']->insertInSession($this->registry);
        }

        return !$error;
    }

    /*
     * Automation to assign a decisionmaker and owner
     */
    public function assignDecisionmakerOwner($params)
    {
        $registry = $this->registry;
        $error = false;
        $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `name`="' . $this->settings['employee_manager_var'] . '" AND `model`="Customer" AND `model_type`=' . PH_CUSTOMER_EMPLOYEE;
        $manager_var_id = $registry['db']->GetOne($sql);

        $sql = 'SELECT `value` FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' WHERE `model_id`="' . $registry['originalUser']->get('employee') . '" AND `var_id`="' . $manager_var_id . '"' . "\n";
        $manager_id = $registry['db']->GetOne($sql);

        // assign the manager
        $new_model = $params['model'];
        $current_decision = $new_model->get('assignments_decision');

        if (!in_array($manager_id, $current_decision)) {
            // Assign the document prepare assignments params
            $assignments_params = array(
                'model_id' => $params['model_id'],
                'module' => $params['module'],
                'model' => $new_model,
                'new_assign_owner' => $registry['originalUser']->get('id'),
                'new_assign_decision' => $manager_id,
            );

            if (!$this->assign($assignments_params)) {
                $error = true;
                $registry['db']->FailTrans();
                $registry['messages']->setWarning($this->i18n('warning_assigning_responsible_as_decisionmaker_failed'));
                $registry['messages']->insertInSession($this->registry);
            }
            $registry['db']->CompleteTrans();
        }

        return !$error;
    }

    /**
     * Checks issue history against filters whether its relative task should update.
     * Important: Creating an issue isn't recorded as an action in the history, even though its visible on the page.
     *
     * @param array $issue
     * @param Task $task
     * @return bool
     */
    private function _shouldTaskUpdate($issue)
    {
        // List of which actions from history to ignore
        $filters = (explode(',', $this->settings['actions_to_ignore'])) ?? [];
        if (empty($filters)) {
            return true;
        }

        $issue_history = $issue['changelog']['histories'] ?? false;
        if ($issue_history) {
            $parsed_history = [];
            foreach ($issue_history as $changes) {
                $change_datetime = new DateTime($changes['created']);
                if ($change_datetime >= $this->execution_interval) { // Filter out changes before import date
                    foreach ($changes['items'] as $change) {
                        if (!in_array($change['field'], $filters)) {
                            $parsed_history[] = $change['field'];
                        }
                    }
                }
            }

            if (!empty($parsed_history)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Creates new task from given issue's data
     *
     * @param array $issue
     * @return Task|bool
     */
    private function _createNewTask($issue)
    {
        $task = new Task($this->registry);
        $old_task = clone $task;

        $task = $this->_setTaskVars($task, $issue, 'add');
        if ($task) {
            $task->getTags();

            $this->_setTaskStatus($task, $issue);
            $this->_setTaskTags($task, $issue);
            $this->_setTaskAssignments($task, $issue);

            // If issue is closed, run all other AD's
            if ($this->_getIssueSubStatus($issue) == 84) {
                $this->executeActionAutomations($old_task, $task, 'edit');
                if (!empty($this->registry['messages']->getWarnings())) {
                    foreach ($this->registry['messages']->getWarnings() as $k => $warning) {
                        $this->email_notifications[] = 'Nzoom task id:' . $task->get('id') . " | Jira issue: https://brainstormconsult.atlassian.net/browse/" . $issue['key'] . ' | ' . $warning;
                    }
                }
            }

            return $task;
        }
        return false;
    }

    /**
     * Updates given Task object's variables
     *
     * @param Task $task
     * @param array $issue
     * @return mixed
     */
    private function _updateTaskData($task, $issue)
    {
        $old_task = clone $task;

        // Set task's main vars
        $task = $this->_setTaskVars($task, $issue);
        $task->getTags();

        if ($task) {
            $this->_setTaskStatus($task, $issue);
            $this->_setTaskTags($task, $issue);
            $this->_setTaskAssignments($task, $issue);

            // If issue is closed, run all other AD's
            if ($this->_getIssueSubStatus($issue) == 84) {
                $filters = array('where' => array('t.id = ' . $task->get('id')),
                    'model_lang' => $task->get('model_lang'));
                $new_task = Tasks::searchOne($this->registry, $filters);
                $this->executeActionAutomations($old_task, $new_task, 'edit');
                if (!empty($this->registry['messages']->getWarnings())) {
                    foreach ($this->registry['messages']->getWarnings() as $k => $warning) {
                        $this->email_notifications[] = 'Nzoom task id:' . $task->get('id') . " | Jira issue: https://brainstormconsult.atlassian.net/browse/" . $issue['key'] . ' | ' . $warning;
                    }
                }
            }
            return $task;
        }
        return false;
    }

    /**
     * Sets a task's main vars, status and assignments.
     * Writes history only when changes have been made
     *
     * @param Task $task
     * @param array $issue
     * @param string $action add/edit
     * @return Task mixed
     */
    private function _setTaskVars($task, $issue, $action = 'edit')
    {
        // Track if there are any changes to save, so we dont spam history
        $old_task = clone $task;
        $has_changes = false;
        $vars = [
            'equipment' => $issue['key'],
            'customer' => $this->_getIssueCustomer($issue),
            'name' => $issue['fields']['summary'],
            'description' => $issue['fields']['description'],
            'type' => $this->metaData['start_model_type'],
            'planned_time' => $this->_getIssueTimeEstimate($issue),
            'severity' => $this->_getIssueSeverity($issue),
            'planned_finish_date' => $this->_getIssuePlannedFinishDate($issue)
        ];
        $task->unsetProperty('finish_date', true); //Prevent '0000-00-00 00:00:00' values

        // Dependant var
        if (!empty($issue['fields']['created'])) {
            $created = new DateTime($issue['fields']['created']);
            $vars['planned_start_date'] = $created->format('Y-m-d H:i:s');
        }

        // Save vars to task
        foreach ($vars as $var_name => $var_value) {
            if ($task->get($var_name) != $var_value) {
//                tra ce($var_name . ': ' . $task->get($var_name) . ' => ' . $var_value); // Debug
                $has_changes = true;
                $task->set($var_name, $var_value, true);
            }
        }

        // Save task and write history
        if ($has_changes && $task->save()) {
            $filters = array('where' => array('t.id = ' . $task->get('id')),
                'model_lang' => $task->get('model_lang'));
            $new_task = Tasks::searchOne($this->registry, $filters);
            Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => $action, 'new_model' => $new_task, 'old_model' => $old_task));

            return $new_task;
        }

        return $task;
    }

    /**
     * Sets a task's tags
     * Writes history only when changes have been made
     *
     * @param Task $task
     * @param array $issue
     */
    private function _setTaskTags($task, $issue)
    {
        $task->getTags();
        $old_task = clone $task;
        $old_task->getTags();
        $tags = $this->_getIssueTag($issue);
        $epic_name = $this->_getIssueEpicName($issue);

        // Merge issue tags with epic tag
        if ($epic_name) {
            $epic_tag_id = $this->_getMatchingEpicTag($epic_name);
            if ($epic_tag_id) {
                $tags[] = $epic_tag_id;
            }
        }

        $tags_filters = array('where' => array('t.section IN (17, 18)'));
        $jira_tags_ids = Tags::getIds($this->registry, $tags_filters);
        $current_tags = $task->get('tags');
        $current_jira_tags = array_intersect($jira_tags_ids, $current_tags); //Filter out non-jira tags

        // Check if issue tags differ from task's
        if (empty($current_tags) || array_diff($current_jira_tags, $tags) || array_diff($tags, $current_jira_tags)) {
            // Rewrite all tags from sections 17,18
            $task->deleteTags($jira_tags_ids);
            $task->set('tags', $tags, true);

            // Save
            if ($task->addTags($tags)) {
                //tag the payment document
                $old_task->getModelTagsForAudit();
                $old_task->sanitize();

                $filters = array('where' => array("t.id = {$task->get('id')}"));
                $new_task = Tasks::searchOne($this->registry, $filters);
                $new_task->getTags();
                $new_task->getModelTagsForAudit(); //Ive got no idea why. But at this point Im too afraid to ask.
                $new_task->sendTagNotification($old_task->get('tag_names_for_audit'));
                $new_task->sanitize();

                Tasks_History::saveData($this->registry,
                    array(
                        'model' => $task,
                        'action_type' => 'tag',
                        'new_model' => $new_task,
                        'old_model' => $old_task
                    )
                );
            }
        }
    }

    /**
     * Sets a task's assignments
     * Writes history only when changes have been made
     *
     * @param Task $task
     * @param array $issue
     */
    private function _setTaskAssignments($task, $issue)
    {
        $old_task = clone $task;
        $jira_acc_name = $issue['fields']['assignee']['key'] ?? '';
        $jira_acc_id = $issue['fields']['assignee']['accountId'] ?? '';
        $user = $this->_getMatchingNzoomUser($jira_acc_name, $jira_acc_id);
        $task->settings_assign = $this->registry['config']->getParamAsArray('tasks', 'assignment_types_' . $task->get('type'));
        $assignment_owners = array($user['id']);
        $current_assignments = $task->getAssignments();

        // Merge new and current assignments
        if (count($current_assignments) > 1) {
            foreach ($current_assignments as $assignment) {
                if (!isset($assignment_owners[$assignment['assigned_to']])) {
                    $assignment_owners[] = $assignment['assigned_to'];
                }
            }
        } else if (count($current_assignments) === 1 && array_key_exists($user['id'], $current_assignments)) {
            return; // If theare are no changes to be made to owners
        }

        // Set assignments
        $task->set('assignments_owner', $assignment_owners, true);
        if (!empty($this->settings['default_assignment_decision'])) {
            $task->set('assignments_decision', explode(',', $this->settings['default_assignment_decision']), true);
        }

        // Save assignments and audit history
        if ($task->assign(false, false)) {
            $new_task = Tasks::getOne($this->registry, ['where' => ["t.id = {$task->get('id')}"]], 'Tasks');
            Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'assign', 'new_model' => $new_task, 'old_model' => $old_task));
        }
    }

    /**
     * Saves Jira worklogs as Nzoom timesheets
     *
     * @param $worklog_data
     * @return bool
     */
    private function _saveWorklogs($worklog_data, $issue_names)
    {
        $timesheet_error = false;
        foreach ($worklog_data as $equipment_id => $author) {
            if (empty($author)) {
                continue;
            }

            foreach ($author as $index => $worklog) {
                // Skip if we cant find user
                $jira_acc_name = $worklog['author']['key'] ?? '';
                $jira_acc_id = $worklog['author']['accountId'] ?? '';
                $user = $this->_getMatchingNzoomUser($jira_acc_name, $jira_acc_id);
                if (!$user) {
                    continue;
                }

                // Get the id of the system task, which should already be created for the current document
                $task = $this->_getTaskByEquipment($equipment_id);
                if ($task) {
                    $start_date = new DateTime($worklog['started']);
                    $end_date = clone($start_date);
                    $end_date->add(new DateInterval('PT' . $worklog['timeSpentSeconds'] . 'S'));
                    $duration = trim($worklog['timeSpentSeconds'] / 60);

                    $timesheets_filters = array(
                        'where' => array(
                            "tt.task_id = '" . $task->get('id') . "'",
                            "tt.startperiod = '" . $start_date->format('Y-m-d H:i:s') . "'",
                            "tt.endperiod = '" . $end_date->format('Y-m-d H:i:s') . "'"
                        ),
                    );
                    // Skip existing timesheets
                    if (Tasks_Timesheets::searchOne($this->registry, $timesheets_filters)) {
                        continue;
                    }

                    // Prepare an array with parameters for the timesheet
                    $p = array();
                    $p['model_id'] = $task->get('id');
                    // Prepare some standard parameters
                    $p['resource'] = 'human';
                    $p['parent_module'] = 'tasks';
                    // Set the start and end date of the timesheet
                    $p['startperiod_period'] = $start_date->format('Y-m-d H:i:s');
                    $p['endperiod_period'] = $end_date->format('Y-m-d H:i:s');
                    $p['duration'] = $duration;
                    // Set the user and the office
                    $p['user_id'] = $user['id'];
                    $p['office'] = $user['office'];
                    // Set: activity, subject and content
                    $p['activity'] = 1;
                    $p['subject'] = '';
                    $p['content'] = 'Работа по ' . ((isset($issue_names[$equipment_id]) && !empty($issue_names[$equipment_id])) ? $issue_names[$equipment_id] : $equipment_id);

                    // ToDo: There's probably a better way to set these audit properties. But I couldnt find it.
                    $p['activity_name'] = 'Работа по задача';
                    $p['user_id_name'] = $user['username'];
                    $p['office_name'] = $user['office_name'];

                    // Build a new timesheet model
                    $timesheet = new Tasks_Timesheet($this->registry, $p);
                    // Try to save the timesheet
                    if ($timesheet->save() && $timesheet->get('id')) {
                        $timesheet->saveHistory($task);
                    } else {
                        $data = '';
                        foreach ($p as $k => $v) {
                            $data .= $k . '=>' . $v . '\n\r';
                        }
                        $this->executionWarnings[] = "Could not save timesheet with data: \n\r {$data}";
                        $timesheet_error = true;
                    }
                }
            }
        }

        if ($timesheet_error) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * Calculate planned finish date for Jira issue.
     * Because Jira doesnt always return this field, and its required in Nzoom, there's extra default options.
     *
     * @param $issue
     * @return string
     */
    private function _getIssuePlannedFinishDate($issue)
    {
        $default_date_days = $this->settings['default_planned_finish_date'];
        $due_date = (isset($issue['fields']['duedate']) && !empty($issue['fields']['duedate'])) ? $issue['fields']['duedate'] : '';

        if (!empty($due_date)) {
            $due_date = new DateTime($due_date);
            $due_date->add(new DateInterval('PT23H59M59S'));
            return $due_date->format('Y-m-d H:i:s');
        }

        if ($this->_getIssueSubStatus($issue) == 84 && !empty($issue['fields']['resolutiondate'])) { // If issue is closed, and contains "endDate field"
            $date = new DateTime($issue['fields']['resolutiondate']);
            $planned_finish_date = $date->format('Y-m-d H:i:s');
        } elseif (!empty($issue['fields']['created'])) { // Default to create_date + default_days
            $created = $issue['fields']['created'];
            $planned_finish_date = date('Y-m-d H:i:s', strtotime($created . " + {$default_date_days} days"));
        } else { // Default to now() + default_days
            $planned_finish_date = date('Y-m-d H:i:s', strtotime(date('Y-m-d') . " + {$default_date_days} days"));
        }

        return $planned_finish_date;
    }

    /**
     * Finds an Nzoom user ID, from given Jira account name and/or ID.
     *
     * @param string $jira_acc_name
     * @param string $jira_acc_id
     * @return array|bool
     */
    private function _getMatchingNzoomUser($jira_acc_name = '', $jira_acc_id = '')
    {
        if (empty($jira_acc_id) && empty($jira_acc_name)) {
            return false;
        }

        $user = '';
        // Search by Jira Acc Name in users
        if (!empty($jira_acc_name)) {
            $query = $this->registry['db']->prepare("
                SELECT u.id, u.office, CONCAT(ui18n.firstname, ' ', ui18n.lastname) as username, oi18n.name as office_name FROM " . DB_TABLE_USERS . " as u 
                  JOIN " . DB_TABLE_USERS_I18N . " AS ui18n
                    ON u.id = ui18n.parent_id
                    AND u.username = ?
                  JOIN " . DB_TABLE_OFFICES_I18N . " AS oi18n
                    ON u.office = oi18n.parent_id
            ");
            $user = $this->registry['db']->GetRow($query, $jira_acc_name);
        }

        // Search by Jira Acc Id in jira_account cstm field
        if (!empty($jira_acc_id) && empty($user)) {
            $query = $this->registry['db']->prepare("
                SELECT u.id, u.office, CONCAT(ui18n.firstname, ' ', ui18n.lastname) as username, oi18n.name as office_name FROM " . DB_TABLE_USERS . " AS u
                  JOIN " . DB_TABLE_CUSTOMERS . " AS c
                    ON u.employee = c.id
                  JOIN " . DB_TABLE_USERS_I18N . " AS ui18n
                    ON u.id = ui18n.parent_id
                  JOIN " . DB_TABLE_OFFICES_I18N . " AS oi18n
                    ON u.office = oi18n.parent_id
                  JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc
                    ON c.id = cc.model_id
                    AND cc.var_id = 57
                    AND cc.value = ?");
            $user = $this->registry['db']->GetRow($query, $jira_acc_id);
        }

        if (!empty($user)) {
            return $user;
        } else {
            $this->executionWarnings[$jira_acc_id] = "Jira user '" . $jira_acc_name . "' with profile (https://brainstormconsult.atlassian.net/people/" . $jira_acc_id . ") could not be matched to an Nzoom client.<br>";
            return false;
        }
    }

    /**
     * Get Nzoom severity corresponding to Jira's priority ID
     *
     * @param $issue
     * @return string|bool
     */
    private function _getIssueSeverity($issue)
    {
        $severity = false;

        if (!empty($issue['fields']['priority'])) {
            $issuePriority = $issue['fields']['priority']['id'];
            switch ($issuePriority) {
                case 1: // Highest
                    $severity = 'heavy'; // Changed per request
                    break;
                case 2: // High
                    $severity = 'heavy';
                    break;
                case 3: // Medium
                    $severity = 'normal';
                    break;
                case 4: // Low
                    $severity = 'light';
                    break;
                case 5: // Lowest
                    $severity = 'verylight';
                    break;
            }
        }

        return $severity;
    }

    /**
     * Converts Jira's worktime estimate in to Nzoom's format
     *
     * @param array $issue
     * @return int
     */
    private function _getIssueTimeEstimate($issue)
    {
        $original_estimate_seconds = (!empty($issue['fields']['timeoriginalestimate'])) ? $issue['fields']['timeoriginalestimate'] : 0;
        $original_estimate_minutes = 0;

        if ($original_estimate_seconds >= 60) {
            $original_estimate_minutes = floor($original_estimate_seconds / 60);
            $original_estimate_hours = floor($original_estimate_minutes / 60);
            $original_estimate_days = floor($original_estimate_hours / 8);

            // Convert 8 hours to 7.5 hours in minutes
            if ($original_estimate_days > 0) {
                $original_estimate_minutes -= 30 * $original_estimate_days;
            }
        }

        return $original_estimate_minutes;
    }

    /**
     * Get nZoom status by substatus id
     *
     * @param array $issue
     * @return string | bool
     */
    private function _getIssueStatus($issue)
    {
        $nzoom_status_id = $this->_getIssueSubStatus($issue);

        // Get status name by its ID
        if ($nzoom_status_id > 0) {
            $sql = "SELECT `status` FROM " . DB_TABLE_TASKS_STATUSES . " WHERE id = {$nzoom_status_id} AND task_type = 5";
            $nzoom_status = $this->registry['db']->getOne($sql);

            return $nzoom_status;
        }

        return false;
    }

    /**
     * Get nZoom sub-status by Jira ID
     *
     * @param array $issue
     * @return int
     */
    private function _getIssueSubStatus($issue)
    {
        $nzoom_status_id = 0;

        // Get corresponding Nzoom status ID
        if ($issue['fields']['status']) {
            $status = $issue['fields']['status']['id'];
            $resolution = $issue['fields']['resolution']['id'] ?? false;

            switch ($status) {
                case 10004: // tToDo
                    $nzoom_status_id = 85;
                    break;
                case 3: // In Progress
                    $nzoom_status_id = 79;
                    break;
                case 10025: // Testing
                    $nzoom_status_id = 86;
                    break;
                case 400: // Building
                    $nzoom_status_id = 80;
                    break;
                case 6: // Closed
                    $nzoom_status_id = 84; // За проверка
                    if ($resolution == '10001') {
                        $nzoom_status_id = 82; // Пропаднала
                    }
                    break;
            }
        }

        return $nzoom_status_id;
    }

    /**
     * Gets an epic's name, by requesting its issue by key.
     * Each Epic has its own Epic Issue, and on that issue we can find its name.
     *
     * @param array $issue
     * @return false|string
     */
    private function _getIssueEpicName($issue)
    {
        // Epic fields in issues are custom made, and have unique names
        $epic_field_key_id = 'customfield_10014';
        $epic_field_name_id = 'customfield_10011';

        if (isset($issue['fields'][$epic_field_key_id]) && !empty($issue['fields'][$epic_field_key_id])) {
            // Each epic seems to have its own issue, and on that issue we can find the epic's name.
            // The issue and epic dont always share the same name.
            $epic_issue_key = $issue['fields'][$epic_field_key_id];

            // Check whether given epic has been requested before, return stored result.
            if (isset($this->jira_epics[$epic_issue_key]) && !empty($this->jira_epics[$epic_issue_key])) {
                return $this->jira_epics[$epic_issue_key];
            }

            $jira_filters = array(
                "jql" => "key = {$epic_issue_key}",
                "maxResults" => 1
            );
            $results = $this->_getIssuesFromJira($jira_filters);

            $epic_name = $results['issues'][0]['fields'][$epic_field_name_id];
            if ($epic_name) {
                $this->jira_epics[$epic_issue_key] = $epic_name;
                return $epic_name;
            }
        } elseif (in_array('119', $this->_getIssueTag($issue)) && isset($issue['fields']['parent']['id']) && !empty($issue['fields']['parent']['id'])) { // If issue is of type "sub-task" get its epic from parent
            $parent_key = $issue['fields']['parent']['key'];
            $jira_filters = array(
                "jql" => "key = {$parent_key}",
                "maxResults" => 1
            );
            $parent_issue = $this->_getIssuesFromJira($jira_filters);
            return $this->_getIssueEpicName($parent_issue['issues'][0]); // Recursion 🤔
        }

        return false;
    }

    /**
     * Compares epic_name to relations from AD's settings
     *
     * @param string $epic_name
     * @param string $epic_id
     * @return false|int
     */
    private function _getMatchingEpicTag($epic_name = '', $epic_id = '')
    {
        $matches = explode(',', $this->settings['epic_to_tag_relations']);
        if ((empty($epic_name) && empty($epic_id)) || empty($matches) || empty($matches[0])) {
            return false;
        }

        foreach ($matches as $match) {
            $match = explode('=>', $match);
            list(0 => $epic, 1 => $tag_id) = $match;

            if (!empty($epic_name) && $epic_name == trim($epic)) {
                return $tag_id;
            }

            if (!empty($epic_id) && $epic_id == trim($tag_id)) {
                return $tag_id;
            }
        }

        return false;
    }

    /**
     * Returns corresponding Nzoom tag, by matching ID's
     *
     * @param array $issue
     * @return bool|string
     */
    private function _getIssueTag($issue)
    {
        $issue_type = $issue['fields']['issuetype']['id'];
        $tags = array();

        switch ($issue_type) {
            case '10003': // Story
                $tags[] = '115';
                break;
            case '10004': // Task
                $tags[] = '116';
                break;
            case '10000': // Epic
                $tags[] = '117';
                break;
            case '10006': // Bug
                $tags[] = '118';
                break;
            case '10005': // Sub-task
                $tags[] = '119';
                break;
            case '10015': // Lead
                $tags[] = '120';
                break;
            case '10013': // Improvement
                $tags[] = '121';
                break;
            case '10014': // New Feature
                $tags[] = '122';
                break;
            default:
                return false;
        }

        return $tags;
    }

    /**
     * Try to find an exact match for customer name, return default otherwise.
     *
     * @param array $issue
     * @return mixed
     */
    private function _getIssueCustomer($issue)
    {
        $labels = $issue['fields']['labels'];

        // Return default customer id
        if (!empty($labels)) {
            // Jira returns issue labels as array, currently there are no issues with multiple labels.
            $customer_name = $labels[0];
            $query = $this->registry['db']->prepare("
                SELECT `model_id` FROM customers_cstm AS cc
                  JOIN _fields_meta AS _fm
                    ON _fm.id = cc.var_id
                    AND _fm.name = 'jira_label'
                    AND cc.value = ?
                  JOIN customers as c
                    ON cc.`model_id` = c.`id`
                    AND c.type = 3
                  ");
            $customer_id = $this->registry['db']->getOne($query, $customer_name);

            if ($customer_id) {
                return $customer_id;
            }
        }
        return $this->settings['default_customer_id'];
    }


    /**
     * Gets task based on a predefined filter
     *
     * @param string $key Key corresponding to an issue in Jira
     * @return object Task
     */
    private function _getTaskByEquipment($key)
    {
        $filters = array(
            'where' => array(
                "t.type = 5",
                "t.equipment = '$key'",
                "t.active = 1",
                "t.deleted_by = 0"
            ),
            'skip_assignments' => true,
            'skip_permissions_check' => true
        );

        return Tasks::searchOne($this->registry, $filters);
    }

    /**
     * Gets all issues from Jira based on given filters
     *
     * @param array $filters Filters for selecting Jira issues
     * @return array|bool
     */
    private function _getIssuesFromJira($filters = "")
    {
        $ch = curl_init();

        $username = $this->settings['username'];
        $password = General::decrypt($this->settings['api_token'], 'jira_pass', 'xtea');
        $domain = $this->settings['domain'];

        //Set headers
        $headers = array(
            'Authorization: Basic ' . base64_encode("$username:$password"),
            'Accept: application/json',
            'Content-Type: application/json'
        );
        curl_setopt($ch, CURLOPT_URL, $domain . "/rest/api/2/search");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($filters));

        $results = json_decode(curl_exec($ch), true);
        $ch_error = curl_error($ch);
        curl_close($ch);
        if ($ch_error) {
            $this->executionErrors[] = "Error while connecting to Jira! Error: $ch_error";
            return false;
        }

        return $results;
    }

    /**
     * Gets all worklogs for given issue keys
     *
     * @param array $issue_keys
     * @return array|bool
     */
    private function _getIssuesWorklogs($issue_keys)
    {
        $ch = curl_init();
        $worklogs = array();

        $username = $this->settings['username'];
        $password = General::decrypt($this->settings['api_token'], 'jira_pass', 'xtea');
        $domain = $this->settings['domain'];

        //Set headers
        $headers = array(
            'Authorization: Basic ' . base64_encode("$username:$password"),
            'Accept: application/json',
            'Content-Type: application/json'
        );
        curl_setopt($ch, CURLOPT_URL, $domain . "/rest/api/2/search");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // Get all worklogs for each issue
        foreach ($issue_keys as $i => $key) {
            curl_setopt($ch, CURLOPT_URL, "$domain/rest/api/2/issue/$key/worklog");

            $worklog = json_decode(curl_exec($ch), true);
            $worklogs[$key] = $worklog['worklogs'];
        }

        $ch_error = curl_error($ch);
        curl_close($ch);
        if ($ch_error) {
            $this->executionErrors[] = "Error while connecting to Jira! Error: $ch_error";
            return false;
        }
        return $worklogs;
    }

    /**
     * Creates an assoc array of issue key => issue description (name)
     *
     * @param $issues
     * @return array
     */
    private function _getIssuesNames($issues)
    {
        $issue_names = array();
        foreach ($issues as $issue) {
            $issue_names[$issue['key']] = $issue['fields']['summary'];
        }
        return $issue_names;
    }

    /**
     * Sends an email for failed action type automation
     *
     * @return bool | void
     */
    private function _sendFailedAutomationsNotification()
    {
        if (empty($this->settings['failed_automations_send_to']) || empty($this->email_notifications)) {
            return false;
        }

        $template = 'failed_automations_jira';
        $message = '';
        $send_to = $this->settings['failed_automations_send_to'];

        $filters = array(
            'where' => array(
                "u.email = '{$send_to}'",
                "u.active = 1",
                "u.deleted_by = 0"
            ),
        );

        $user = Users::searchOne($this->registry, $filters);
        $name = $user->properties['display_name'] ?? '';

        $mailer = new Mailer($this->registry, $template);
        foreach ($this->email_notifications as $notification) {
            $message .= '<tr><td>' . $notification . '</td></tr>';
        }

        $mailer->placeholder->add('notifications', $message);
        $mailer->placeholder->add('to_email', $send_to);
        $mailer->placeholder->add('user_name', $name);

        //send email
        $mailer->send();
    }

    /**
     * Checks whether a task's status should be updated
     *
     * @param Task $task
     * @param int $sub_status
     * @return bool
     */
    private function _shouldStatusUpdate($task, $sub_status) {
        // If task has been manually changed to "Завършена", and the new status is "За Проверка"
        if ($sub_status == 84 && $task->get('substatus') == 81 && $task->get('status_modified_by') != -1) {
            return false;
        }

        // If task's current sub-status is the same
        if ($task->get('substatus') == $sub_status) {
            return false;
        }

        return true;
    }

    /**
     * Sets a task's status and substatus (could be split into two methods)
     *
     * @param Task $task
     * @param array $issue
     */
    private function _setTaskStatus ($task, $issue) {
        $old_task = clone $task;
        $status = $this->_getIssueStatus($issue);
        $sub_status = $this->_getIssueSubStatus($issue);

        if ($this->_shouldStatusUpdate($task, $sub_status)) {
            $task->set('status', $status, true);
            $task->set('substatus', $status . "_" . $sub_status, true);
            if ($task->setStatus()) {
                $filters = array('where' => array('t.id = ' . $task->get('id')), 'model_lang' => $task->get('model_lang'));
                $new_task = Tasks::searchOne($this->registry, $filters);
                Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'status', 'new_model' => $new_task, 'old_model' => $old_task));
            }
            $task->set('substatus', $sub_status, true);
        }
    }

    /**
     * Function to calculate the time (in minutes) which took the ticket to be accepted
     *
     * @param mixed $params
     * @return bool $result - result of the operation
     */
    public function calculateTicketAcceptTime($params) {
        $registry = &$this->registry;
        if (preg_match('#^a_#', $this->settings['calc_based_on_var'])) {
            // additional var
            $start_date = date('Y-m-d H:i:s', strtotime($params['model']->getVarValue(preg_replace('#^a_#', '', $this->settings['calc_based_on_var']))));
        } else {
            // basic var
            $start_date = $params['model']->get(preg_replace('#^b_#', '', $this->settings['calc_based_on_var']));
        }
        $time_worked = $this->_calculateMinutesBaseOnWorkTime($start_date, date('Y-m-d H:i:s'));

        // update the vars and the main fields
        $get_old_vars = $registry->get('get_old_vars');
        $registry->set('get_old_vars', true, true);
        $params['model']->getVars();
        $new_model = clone $params['model'];
        if (!empty($this->settings['clear_var'])) {
            $new_model->set($this->settings['clear_var'], '', true);
        }
        $vars = $new_model->get('vars');

        foreach ($vars as $idx => $var) {
            if ($var['name'] == $this->settings['update_var']) {
                $vars[$idx]['value'] = intval($vars[$idx]['value']) + $time_worked;
            } elseif (isset($this->settings['work_start_date']) && $var['name'] == $this->settings['work_start_date']) {
                $vars[$idx]['value'] = date('Y-m-d H:i:s');
            }
        }
        $new_model->set('vars', $vars, true);

        $result = $this->_simpleModelSave($new_model, $params['model']);
        $registry->set('get_old_vars', $get_old_vars, true);

        if (!$result) {
            // push the error in the session
            $registry['messages']->insertInSession($this->registry);
        }
        return $result;
    }

    /**
     * Function to recalculate the ticket deadline
     *
     * @param mixed $params
     * @return bool $result - result of the operation
     */
    public function recalculateTicketDeadline($params) {
        $registry = &$this->registry;
        $params['model']->unsanitize();
        if (!empty($this->settings['use_nomenclature_forseen'])) {
            // get the data from nomenclature
            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `name`="' . $this->settings['nomenclature_work_time_var'] . '" AND `model`="Nomenclature" AND `model_type`=' . $this->settings['use_nomenclature_forseen'];
            $nomenclature_worktime_var_id = $registry['db']->GetOne($sql);

            $sql = 'SELECT `value`' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS d_cstm' . "\n" .
                   'WHERE `model_id`="' . $params['model']->getValueByName($this->settings['forseen_working_time']) . '" AND `var_id`="' . $nomenclature_worktime_var_id . '"' . "\n";
            $forseen_time = $registry['db']->GetOne($sql);
        } else {
            $forseen_time = $params['model']->getValueByName($this->settings['forseen_working_time']);
        }

        // update the vars and the main fields
        $get_old_vars = $registry->get('get_old_vars');
        $registry->set('get_old_vars', true, true);
        $params['model']->getVars();
        $new_model = clone $params['model'];

        if ($forseen_time) {
            $worked_time = $params['model']->getValueByName($this->settings['working_time']);
            $time_left = intval($forseen_time) - intval($worked_time);
            $new_deadline = Calendars_Calendar::calculateBasedOnWorkTime($registry, date('Y-m-d H:i:s'), $time_left, $this->settings['working_day_start'], $this->settings['working_day_end']);
            $new_model->set($this->settings['update_var'], $new_deadline, true);
        }

        $vars = $new_model->get('vars');
        foreach ($vars as $idx => $var) {
            if ($this->settings['work_start_date'] && $var['name'] == $this->settings['work_start_date']) {
                $vars[$idx]['value'] = date('Y-m-d H:i:s');
                break;
            }
        }

        $new_model->set('vars', $vars, true);
        $result = $this->_simpleModelSave($new_model, $params['model']);
        $registry->set('get_old_vars', $get_old_vars, true);

        if (!$result) {
            // push the error in the session
            $registry['messages']->insertInSession($registry);
        }

        return $result;
    }

    /**
     * Function to calculate the start or the end of the period depending on the working time
     *
     * @param string $start_date - start period date
     * @param string $end_date - end period date
     * @return int - the calculate minutes between the two dates
     */
    private function _calculateMinutesBaseOnWorkTime($start_date, $end_date) {
        $ticket_added = Calendars_Calendar::calculateWorkTimePeriodDates($this->registry, $start_date, $this->settings['working_day_start'], $this->settings['working_day_end']);
        $ticket_accepted = Calendars_Calendar::calculateWorkTimePeriodDates($this->registry, $end_date, $this->settings['working_day_start'], $this->settings['working_day_end']);

        $working_days_count = Calendars_Calendar::getWorkingDays($this->registry, $ticket_added->format('Y-m-d'), $ticket_accepted->format('Y-m-d'));
        $working_days_count = ($working_days_count-2 > 0 ? $working_days_count-2 : 0);
        $time_worked = 0;

        list($start_hour, $start_minute) = explode(':', $this->settings['working_day_start']);
        $workday_start = (clone $ticket_added)->setTime($start_hour, $start_minute);
        list($end_hour, $end_minute) = explode(':', $this->settings['working_day_end']);
        $workday_end = (clone $ticket_added)->setTime($end_hour, $end_minute);

        if ($working_days_count) {
            // calculate the minutes of the working day
            $int = $workday_end->diff($workday_start);
            $time_worked += ($int->h * 60 + $int->i) * $working_days_count;
        }

        // calculate the minutes of the first day
        if ($ticket_added->format('Y-m-d') == $ticket_accepted->format('Y-m-d')) {
            $int = $ticket_accepted->diff($ticket_added);
            $time_worked += ($int->h * 60 + $int->i);
        } else {
            $first_day_work_start = (clone $workday_start)->setTime($ticket_added->format('H'), $ticket_added->format('i'));
            $int = $workday_end->diff($first_day_work_start);
            $time_worked += ($int->h * 60 + $int->i);

            // calculate the minutes of the last days
            $last_day_work_start = (clone $ticket_accepted)->setTime($workday_start->format('H'), $workday_start->format('i'));
            $int = $ticket_accepted->diff($last_day_work_start);
            $time_worked += ($int->h * 60 + $int->i);
        }

        return $time_worked;
    }

    /**
     * Function to save the model and history for it
     *
     * @param object Document $new_model - the new model which will be saved
     * @param object Document $old_model - old model
     * @return boolean $result - the result of the operation
     */
    private function _simpleModelSave($new_model, $old_model) {
        $new_model->unsanitize();
        $this->registry['db']->StartTrans();

        // Try to save the document
        if ($new_model->save()) {
            // If the vars are saved, then make history for the document
            $filters = array('where' => array('d.id = ' . $new_model->get('id')),
                             'model_lang' => $new_model->get('model_lang'),
                             'skip_assignments' => true,
                             'skip_permissions_check' => true);
            $new_model = Documents::searchOne($this->registry, $filters);
            $new_model->getVars();
            Documents_History::saveData($this->registry,
                array('model'       => $new_model,
                      'action_type' => 'edit',
                      'new_model'   => $new_model,
                      'old_model'   => $old_model));
        } else {
            $this->registry['messages']->setError($this->i18n('error_saving_documents_properties_failed'));
            $this->registry['db']->FailTrans();
        }
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }


    /**
     * Function to prepare the default values of the
     *
     * @param array $params - array of params for the automation
     * @return bool - result of the operation
     */
    public function completeTicketDefaultValues($params) {
        $this->_setTicketTerms($params['model'], $this->registry['request']->get($this->settings['ticket_priority_var']));
        return true;
    }

    /**
     * Defines ticket reaction and solution terms and sets them into model
     *
     * @param Document $document - ticket model
     * @param string $priority - value of selected priority option, normal by default
     */
    public function _setTicketTerms(Document &$document, $priority = '') {
        $assoc_vars = $document->getAssocVars();

        $document->unsetProperty('assoc_vars', true);
        $table_vars = $document->get('table_vars') ?: array();

        $var_name = $this->settings['ticket_priority_var'];
        $priority_options = $assoc_vars[$var_name]['options'] ?? array();
        array_walk($priority_options, function(&$a) { $a = $a['option_value']; });
        if (!$priority || !in_array($priority, $priority_options)) {
            $priority = $this->settings['default_priority'] ?: 'Normal';
        }
        $assoc_vars[$var_name]['value'] = $priority;
        foreach ($table_vars as $idx => $var) {
            if ($var['name'] === $var_name) {
                $table_vars[$idx]['value'] = $assoc_vars[$var_name]['value'];
                break;
            }
        }

        $selected_index = count($priority_options) - array_search($priority, $priority_options) - 1;
        // assume some reasonable limit (30 days) and exclude no-value values (times are in minutes)
        $max_time = 24*60*30;
        $start_datetime = $document->get('added') ?: date('Y-m-d H:i:s');

        foreach (array(
            $this->settings['ticket_reaction_time_var'] => 'deadline',
            $this->settings['ticket_solution_time_var'] => 'validity_term',
        ) as $var_name => $basic_var_name) {
            $var_options = $assoc_vars[$var_name]['options'] ?? array();
            if ($var_options) {
                // get the current var value
                $current_var_value = $assoc_vars[$var_name]['value'];

                if (!$current_var_value) {
                    // if none is find try to guess by priority
                    if (array_key_exists($selected_index, $var_options) && $var_options[$selected_index]['option_value'] < $max_time) {
                        $current_var_value = $var_options[$selected_index]['option_value'];
                    } else {
                        $current_var_value = '';
                        $document->set($basic_var_name, '', true);
                    }
                }

                if ($current_var_value) {
                    // calculate basic var datetime
                    $new_value = Calendars_Calendar::calculateBasedOnWorkTime($this->registry, $start_datetime, (int)$current_var_value, $this->settings['working_day_start'], $this->settings['working_day_end']);
                    $document->set($basic_var_name, $new_value, true);
                    if ($this->registry['request']->isPost()) {
                        $this->registry['request']->set($basic_var_name, $new_value, 'all', true);
                    }
                }

                $assoc_vars[$var_name]['value'] = $current_var_value;

                // set for display in interface
                foreach ($table_vars as $idx => $var) {
                    if ($var['name'] === $var_name) {
                        $table_vars[$idx]['value'] = $assoc_vars[$var_name]['value'];
                        break;
                    }
                }
            }
        }

        $document->set('table_vars', $table_vars, true);
        $document->set('vars', array_values($assoc_vars), true);
    }
}
