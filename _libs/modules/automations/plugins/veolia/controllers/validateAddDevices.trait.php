<?php

trait validateAddDevices {

    /**
     * Automation that validates if devices with the supplied data can be created
     *
     * @param $params - params for the automation
     * @return bool - result of the check
     */
    public function validateDevice($params): bool
    {
        $device_code = $this->registry['request']->get($this->settings['device_code']);

        if (empty($device_code)) {
            return true;
        }

        $nom_types = array_filter(preg_split('#\s*,\s*#', $this->settings['device_types']));
        $sql = 'SELECT id FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE `code`="' . $device_code . '" AND `active`=1 AND `deleted_by`=0 AND `type` IN ("' . implode('","', $nom_types) . '")' . "\n";
        $existing_nom = $this->registry['db']->GetOne($sql);
        $result = !$existing_nom;

        if (!$result) {
            $nom_link = sprintf(
                '%s?%s=nomenclatures&nomenclatures=view&view=%d',
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'],
                $existing_nom
            );
            $this->registry['messages']->setError(sprintf($this->i18n('error_device_code_not_unique'), $nom_link, $device_code));
            $this->registry['messages']->insertInSession($this->registry);
        }

        return $result;
    }

    /**
     * Automation to add the new devices
     *
     * @param $params - params for the automation
     * @return bool - result of the operation
     */
    public function addDevice($params): bool
    {
        $get_old_vars = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);
        $vars = $params['model']->getAssocVars();

        // prepare the device params
        $properties = array(
            'type'       => $vars[$this->settings['document_device_type']]['value'],
            'code'       => $vars[$this->settings['document_nom_code']]['value'],
            'name'       => $vars[$this->settings['document_nom_code']]['value'],
            'group'      => 1,
            'active'     => 1,
            'model_lang' => $this->registry['lang']
        );

        $new_device = new Nomenclature($this->registry, $properties);
        $new_device->getVars();
        $nom_vars = $new_device->getAssocVars();

        $nom_vars[$this->settings['nomenclature_status']]['value'] = $this->settings['nomenclature_nom_status_id'];
        $nom_vars[$this->settings['nomenclature_station']]['value'] = $vars[$this->settings['document_station']]['value'];
        $nom_vars[$this->settings['nomenclature_station_name']]['value'] = $vars[$this->settings['document_station_name']]['value'];
        $nom_vars[$this->settings['nomenclature_device_brand']]['value'] = $vars[$this->settings['document_device_brand']]['value'];
        $nom_vars[$this->settings['nomenclature_device_brand_name']]['value'] = $vars[$this->settings['document_device_brand_name']]['value'];
        $nom_vars[$this->settings['nomenclature_device_model']]['value'] = $vars[$this->settings['document_device_model']]['value'];
        $nom_vars[$this->settings['nomenclature_device_model_name']]['value'] = $vars[$this->settings['document_device_model_name']]['value'];
        $nom_vars[$this->settings['nomenclature_production_year']]['value'] = $vars[$this->settings['document_production_year']]['value'];
        $nom_vars[$this->settings['nomenclature_nominal_cost']]['value'] = $vars[$this->settings['document_nominal_cost']]['value'];
        $nom_vars[$this->settings['nomenclature_object_name']]['value'] = $vars[$this->settings['document_object_name']]['value'];
        $nom_vars[$this->settings['nomenclature_object_id']]['value'] = $vars[$this->settings['document_object_id']]['value'];
        $nom_vars[$this->settings['nomenclature_room_name']]['value'] = $vars[$this->settings['document_room_name']]['value'];
        $nom_vars[$this->settings['nomenclature_room_id']]['value'] = $vars[$this->settings['document_room_id']]['value'];
        $nom_vars[$this->settings['nomenclature_appliances_report_type']]['value'] = $vars[$this->settings['document_appliances_report_type']]['value'];
        if (isset($nom_vars[$this->settings['nomenclature_entrance']]) && $vars[$this->settings['document_entrance']]['value']) {
            $nom_vars[$this->settings['nomenclature_entrance']]['value'] = $vars[$this->settings['document_entrance']]['value'];
        }
        if (isset($nom_vars[$this->settings['nomenclature_hollander']]) && $vars[$this->settings['document_hollander']]['value']) {
            $sql = 'SELECT `name` FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' WHERE `parent_id`="' . $vars[$this->settings['document_hollander']]['value'] . '" AND `lang`="' . $this->registry['lang'] . '"' . "\n";

            $nom_vars[$this->settings['nomenclature_hollander']]['value'] = $vars[$this->settings['document_hollander']]['value'];
            $nom_vars[$this->settings['nomenclature_hollander_name']]['value'] = $this->registry['db']->GetOne($sql);
        }
        if (isset($nom_vars[$this->settings['nomenclature_seal_num']]) && isset($vars[$this->settings['document_seal_num']]['value'])) {
            $nom_vars[$this->settings['nomenclature_seal_num']]['value'] = $vars[$this->settings['document_seal_num']]['value'];
        }

        $new_device->set('assoc_vars', $nom_vars, true);
        $new_device->set('vars', array_values($nom_vars), true);

        $this->registry['db']->StartTrans();
        if ($new_device->save()) {
            $filters = array(
                'where'      => array('n.id = ' . $new_device->get('id')),
                'model_lang' => $new_device->get('model_lang')
            );
            $new_device = Nomenclatures::searchOne($this->registry, $filters);
            $old_device = new Nomenclature($this->registry);
            $old_device->set('type', $new_device->get('type'), true);

            $new_device->getVars();
            $old_device->getVars();

            $params_history = array(
                'action_type' => 'add',
                'old_model'   => $old_device,
                'model'       => $new_device,
                'new_model'   => $new_device
            );
            Nomenclatures_History::saveData($this->registry, $params_history);

            $nom_link = sprintf(
                '%s?%s=nomenclatures&nomenclatures=view&view=%d',
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'],
                $new_device->get('id')
            );
            $this->registry['messages']->setMessage(sprintf($this->i18n('message_new_device_added_successfully'), $nom_link, $new_device->get('code')));

            // run the automation that have to be executed on add action of the device
            $this->executeActionAutomations($old_device, $new_device, 'add');

            // update the source document with the added nomenclature
            $old_doc = clone $params['model'];
            $vars[$this->settings['document_device_id']]['value'] = $new_device->get('id');
            $vars[$this->settings['document_device_name']]['value'] = $new_device->get('name');

            $params['model']->set('vars', array_values($vars), true);
            $params['model']->unsanitize();
            if ($params['model']->save()) {
                $filters = array(
                    'where'      => array('d.id = ' . $params['model']->get('id')),
                    'model_lang' => $params['model']->get('model_lang')
                );
                $new_doc = Documents::searchOne($this->registry, $filters);

                $params_history = array(
                    'action_type' => 'add',
                    'old_model'   => $old_doc,
                    'model'       => $new_doc,
                    'new_model'   => $new_doc
                );
                Documents_History::saveData($this->registry, $params_history);
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_new_device_add_failed'));
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();
        $this->registry['messages']->insertInSession($this->registry);

        $this->registry->set('get_old_vars', $get_old_vars, true);
        $this->registry->set('edit_all', $edit_all, true);

        return $result;
    }
}
