<?php

use GuzzleHttp\Cookie\CookieJar;

include_once dirname(__DIR__) . "/models/hrZoomService.class.php";
include_once dirname(__DIR__) . "/exception/missingAutomationRequiredConfigException.model.php";

/**
 * Universal automation to handle notification tokens from FCM
 */
class AppNotifications_Automations_Controller extends Automations_Controller
{
    public const COMMENT_CHAR_LIMIT = 60;
    private array $mapping = [
        'owner' => 0,
        'responsible' => 1,
        'observer' => 2,
        'decision' => 3,
    ];
    private string $notificationSettingFormat = "cc1.`value` LIKE '%%%s,%s,%s,%s,1%%'";


    public function sendNotification($params)
    {
        if (!empty($this->settings['execute_as_original_user'])) {
            $this->setOriginalUserAsCurrent();
        }
        $this->module = $params['model']->getModule();
        $this->controller = $params['model']->getController() ?: '';
        $this->modelFactoryName = Model_Factory::getModelFactory($this->module, $this->controller);
        $this->action = empty($this->settings['customAction']) ? $this->registry->get('action') : $this->settings['customAction'];
        $this->modelType = $params['start_model_type'];
        $this->params = $params;
        try {
            $hrZoom = $this->_getHrzoom();
            $response = $hrZoom->authenticate($this->settings['user'], General::decrypt($this->settings['pass'], '_hrzoom_notifications_', 'xtea'));
        } catch (\Exception $e) {
            General::log($this->registry, __METHOD__, $e->getMessage());
            $this->updateAutomationHistory($params, $params['model'], 0);
            return false;
        }
        try {
            $model = $this->getModelFromDB($params['model']->get('id'));
            if ($params['model']->get('comment')) {
                $model->set('comment', $params['model']->get('comment'), true);
            }
        } catch (Exception $e) {
            $msg = $this->i18n('failed_getting_new_model');
            General::log($this->registry, __METHOD__, $msg);
            $this->registry['messages']->setError($msg);
            $this->updateAutomationHistory($params, $params['model'], 0);
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }
        $model->set('old_model', $params['model']->get('old_model'), true);
        if (empty($this->settings['notificationAssignments'])) {
            $msg = $this->i18n('missing_automation_assignments');
            General::log($this->registry, __METHOD__, $msg);
            $this->registry['messages']->setError($msg);
            $this->updateAutomationHistory($params, $model, 0);
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        if (empty($this->settings['notificationTemplate'])) {
            $msg = $this->i18n('missing_automation_notification_template');
            General::log($this->registry, __METHOD__, $msg);
            $this->registry['messages']->setError($msg);
            $this->updateAutomationHistory($params, $model, 0);
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        $notifTemplate = Emails::searchOne($this->registry, [
            'where' => [
                'e.id = ' . $this->settings['notificationTemplate'],
            ],
            'skip_permissions_check' => true,
        ]);
        $model->unsanitize();
        $placeholders = $model->getPatternsVars() + $model->getEmailsVars();

        $extender = new Extender();
        $extender->model_lang = $model->get('lang');
        $extender->module = $this->module;
        $extender->merge($placeholders);

        //add some extra placeholders
        $this->_addExtraStatusPlaceholders($model, $extender);
        $this->_addExtraCommentPlaceholders($model, $extender);

        $comment = $model->get('comment');
        $body = [
            'form_params' => [
                'subject' => $extender->expand($notifTemplate->get('subject')),
                'body' => $this->_fixNotifBody($extender->expand($notifTemplate->get('body'))),
                'notificationTokens' => [],
                'model' => $model,
                'comment' => $comment ?:null,
            ],
        ];
        $userFullnamesReceived = [];
        foreach (explode(',', $this->settings['notificationAssignments']) as $assignment) {
            $assignees = $this->_getAssignedUsers($assignment, $model);
            if (empty($assignees)) {
                continue;
            }
            $users = array_keys($assignees);
            $notifTokens = $this->_getAssigneeNotifTokens($assignment, implode(',', $users));
            if (empty($notifTokens)) {
                continue;
            }
            foreach ($notifTokens as $uid => $token) {
                if (in_array($token, $body['form_params']['notificationTokens'])) {
                    continue;
                }
                $body['form_params']['notificationTokens'][] = $token;
                $userFullnamesReceived[$uid] = $assignees[$uid]['assigned_to_name'];
            }
        }
        if (empty($body['form_params']['notificationTokens'])) {
            $msg = sprintf($this->i18n('no_tokens_found_single'), $model->get('id'));
            General::log($this->registry, __METHOD__, $msg);
            $this->updateAutomationHistory($params, $model, 1);
            return true;
        }
        try {
            $response = $hrZoom->sendPost("notif/send/{$model->get('id')}", $body, ['type' => $model->get('type'), 'module' => $this->module]);
        } catch (\Exception $e) {
            General::log($this->registry, __METHOD__, $e->getMessage());
            $this->updateAutomationHistory($params, $model, 0);
            $this->registry['messages']->setError($this->i18n('error_sending_mobile_push_notification'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }
        $msg = sprintf($this->i18n('success_sending_mobile_push_notification'), implode(', ', $userFullnamesReceived));
        General::log($this->registry, __METHOD__, $msg);
        $this->registry['messages']->setMessage($msg);
        $this->updateAutomationHistory($params, $model, 1);
        $this->registry['messages']->insertInSession($this->registry);
        return true;
    }


    public function sendOverdueDocumentNotification($params)
    {
        $this->module = empty($this->settings['customModule']) ? $params['module'] : $this->settings['customModule'];
        $this->action = empty($this->settings['customAction']) ? $this->registry->get('action') : $this->settings['customAction'];
        $this->modelType = $params['start_model_type'];
        $this->params = $params;
        try {
            $hrZoom = $this->_getHrzoom();
            $response = $hrZoom->authenticate($this->settings['user'], General::decrypt($this->settings['pass'], '_hrzoom_notifications_', 'xtea'));
        } catch (\Exception $e) {
            General::log($this->registry, __METHOD__, $e->getMessage());
            $this->executionErrors[] = $e->getMessage();
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        $searchDocsParams = [
            'd' => DB_TABLE_DOCUMENTS,
            'ah' => DB_TABLE_AUTOMATIONS_HISTORY,
            'docType' => $this->modelType,
            'date' => date('Y-m-d'),
            'autoId' => $params['id'],
        ];

        $query = <<<SQL
        SELECT d.id
        FROM {$searchDocsParams['d']} d
        WHERE d.deadline < NOW()
          AND d.`status` != 'closed'
          AND d.`type` = {$searchDocsParams['docType']}
        AND d.`active` = 1
        AND d.`deleted_by` = 0
        AND d.id NOT IN (
        SELECT ah.model_id
        FROM {$searchDocsParams['ah']} ah
        WHERE DATE_FORMAT(ah.added, '%Y-%m-%d') = '{$searchDocsParams['date']}' AND ah.parent_id = {$searchDocsParams['autoId']}
        )
        SQL;
        $db = $this->registry['db'];
        $docIds = $db->GetCol($query);
        if (empty($docIds)) {
            General::log($this->registry, __METHOD__, $this->i18n('no_overdue_docs_found'));
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }

        if (empty($this->settings['notificationAssignments'])) {
            $msg = $this->i18n('missing_automation_assignments');
            General::log($this->registry, __METHOD__, $msg);
            $this->executionErrors[] = $msg;
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        if (empty($this->settings['notificationTemplate'])) {
            $msg = $this->i18n('missing_automation_notification_template');
            General::log($this->registry, __METHOD__, $msg);
            $this->executionErrors[] = $msg;
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        $notifTemplate = Emails::searchOne($this->registry, [
            'where' => [
                'e.id = ' . $this->settings['notificationTemplate'],
            ],
            'skip_permissions_check' => true,
        ]);

        $docs = Documents::search($this->registry, [
            'where' => ['d.id IN (' . implode(',', $docIds) . ')']
        ]);
        $assignments = explode(',', $this->settings['notificationAssignments']);
        $extender = new Extender();
        foreach ($docs as $doc) {
            $extender->flush();
            $doc->unsanitize();
            $placeholders = $doc->getPatternsVars() + $doc->getEmailsVars();
            $extender->model_lang = $doc->get('lang');
            $extender->module = $params['module'];
            $extender->merge($placeholders);
            if ($doc instanceof Document) {
                $this->_addExtraStatusPlaceholders($doc, $extender);
            }
            $subject = $extender->expand($notifTemplate->get('subject'));
            $body = $this->_fixNotifBody($extender->expand($notifTemplate->get('body')));
            $body = [
                'form_params' => [
                    'subject' => $subject,
                    'body' => $body,
                    'notificationTokens' => [],
                    'model' => $doc,
                    'comment' => null,
                ],
            ];
            $userFullnamesReceived = [];
            foreach ($assignments as $assignment) {
                $assignees = $doc->get('assignments_' . $assignment);
                if (empty($assignees)) {
                    continue;
                }
                $users = array_keys($assignees);
                $notifTokens = $this->_getAssigneeNotifTokens($assignment, implode(',', $users));
                if (empty($notifTokens)) {
                    continue;
                }
                foreach ($notifTokens as $uid => $token) {
                    if (in_array($token, $body['form_params']['notificationTokens'])) {
                        continue;
                    }
                    $body['form_params']['notificationTokens'][] = $token;
                    $userFullnamesReceived[$uid] = $assignees[$uid]['assigned_to_name'];
                }

                $notifTokens = array_diff($notifTokens, $body['form_params']['notificationTokens']);
                array_push($body['form_params']['notificationTokens'], ...$notifTokens);

            }

            if (empty($body['form_params']['notificationTokens'])) {
                continue;
            }

            try {
                $response = $hrZoom->sendPost("notif/send/{$doc->get('id')}", $body, ['type' => $doc->get('type'), 'module' => $params['module']]);
            } catch (\Exception $e) {
                General::log($this->registry, __METHOD__, "DOC: {$doc->get('id')}, " . $e->getMessage());
                continue;
            }
            $msg = sprintf($this->i18n('success_sending_overdue_doc_mobile_push_notification'), $doc->get('full_num'), implode(', ',  $userFullnamesReceived));
            General::log($this->registry, __METHOD__, $msg);
            $this->updateAutomationHistory($params, $doc, 1);

        }

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    private function _getHrzoom()
    {
        if (empty($this->settings['host'])) {
            throw new MissingAutomationRequiredConfigException($this->i18n('missing_automation_host'));
        }

        if (empty($this->settings['userAgent'])) {
            throw new MissingAutomationRequiredConfigException($this->i18n('missing_automation_user_agent'));
        }

        if (empty($this->settings['cookiejarName'])) {
            throw new MissingAutomationRequiredConfigException($this->i18n('missing_automation_cookiejar_name'));
        }

        $cookieJar = $this->registry['session']->get('cookieJar', 'appNotifs');
        if (!$cookieJar) {
            $cookieJar = new CookieJar($this->settings['cookiejarName']);
            $this->registry['session']->set('cookieJar', $cookieJar, 'appNotifs');
        }

        return new HrZoomService($this->settings['host'], $this->settings['userAgent'], $cookieJar);
    }

    private function _prepareSettingsCheck($assignment)
    {

        return sprintf($this->notificationSettingFormat, $this->module, $this->modelType, $this->action, $assignment);

    }

    private function _fixNotifBody(string $notifBody)
    {
        $text = trim(html_entity_decode(strip_tags($notifBody), ENT_QUOTES, 'UTF-8'));
        $text = preg_replace('/[ ]{2,}/', ' ', $text);
        $text = preg_replace('/^\s*[\r\n]+/m', '', $text);
        return preg_replace('/^\s+/m', '', $text);
    }

    private function _addExtraStatusPlaceholders(Model $model, Extender &$extender)
    {
        $old_model = $model->get('old_model');
        if (!$old_model || !$model->get('status')) {
            return;
        }

        //make it useful for each module with statuses
        $i18nPrefix = strtolower(General::singular2plural($model->modelName));
        $extender->add('old_status', $this->i18n($i18nPrefix . '_status_' . $old_model->get('status')));
        $extender->add('status', $this->i18n($i18nPrefix . '_status_' . $model->get('status')));
        if ($model->get('status')) {
            $extender->add('old_substatus', ' (' . $old_model->get('substatus_name') . ')');
            $extender->add('substatus', ' (' . $model->get('substatus_name') . ')');
        }
    }

    private function _addExtraCommentPlaceholders(Model $model, Extender &$extender) {
        $comment = $model->get('comment');
        if (!$comment || !is_a($comment, Comment::class)) {
            return;
        }

        $content = $comment->get('content');
        if (mb_strlen($content) >= self::COMMENT_CHAR_LIMIT) {
            $content = mb_substr($content, 0, self::COMMENT_CHAR_LIMIT);
            $content .= "[...]";
        }
        $extender->add('content', $content);
        $extender->add('subject', $comment->get('subject'));
    }

    private function _getAssigneeNotifTokens($assignment, $employees)
    {

        $notificationTokenParams = [
            'cc' => DB_TABLE_CUSTOMERS_CSTM,
            'fm' => DB_TABLE_FIELDS_META,
            'c' => DB_TABLE_CUSTOMERS,
            'u' => DB_TABLE_USERS,
            'settingsCheck' => $this->_prepareSettingsCheck($assignment),
            'customerType' => '1',
            'employees' => $employees,
            'currUserID' => $this->registry['currentUser']->get('id'),
        ];
        $query = <<<SQL
            SELECT u.id, cc.`value`
            FROM  {$notificationTokenParams['c']} c
            JOIN {$notificationTokenParams['fm']} fm ON fm.model = 'Customer' AND fm.`name` = 'notification_user_token' AND c.`type` = '{$notificationTokenParams['customerType']}'
            JOIN {$notificationTokenParams['cc']} cc ON cc.var_id = fm.id AND cc.model_id = c.id AND cc.num = 1 AND cc.lang = ''
            JOIN {$notificationTokenParams['fm']} fm1 ON fm1.model = 'Customer' AND fm1.`name` = 'notification_user_settings' AND c.`type` = '{$notificationTokenParams['customerType']}'
            JOIN {$notificationTokenParams['cc']} cc1 ON cc1.var_id = fm1.id AND cc1.model_id = c.id AND cc1.num = 1 AND cc1.lang = ''
            JOIN {$notificationTokenParams['u']} u ON u.employee = c.id AND u.id IN ({$notificationTokenParams['employees']}) AND u.id != {$notificationTokenParams['currUserID']} AND u.employee != 0
            WHERE ({$notificationTokenParams['settingsCheck']})
            AND cc.`value` != '';

            SQL;
        return $this->registry['db']->GetAssoc($query);
    }

    private function _getAssignedUsers($assignment, $model)
    {
        if (!empty($this->settings['useNewlyAssignedUsers'])) {
            if (!empty($model->get('old_model'))) {
                $oldModel = $model->get('old_model');
                $prevAssigned = $oldModel->get('assignments_' . $assignment) ?: [];
                $newAssigned = $model->get('assignments_' . $assignment);
                return array_diff_key($newAssigned, $prevAssigned);
            }
        }
        return $model->get('assignments_' . $assignment);
    }
}
