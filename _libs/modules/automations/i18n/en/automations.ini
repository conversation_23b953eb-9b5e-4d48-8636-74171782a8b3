automations_errors_in_automation = Errors in automation%s:
automations_missing_required_settings = Required settings are missing!

automations_setbasicvar_no_vars = There are no variables into the settings of the automation!
automations_setbasicvar_vars_values_not_match = In the settings of the automation, the number of variables does not match the number of values ​​for them!
automations_setbasicvar_failed_to_set_value_for_var = Failed to set value for "%s"!

automations_setadditionalvar_no_vars = There are no variables into the settings of the automation!
automations_setadditionalvar_vars_values_not_match = In the settings of the automation, the number of variables does not match the number of values ​​for them!
automations_setadditionalvar_some_vars_not_found = Some of the variables, set into the settings of the automation, cannot be processed.
automations_setadditionalvar_failed_to_set_value_for_var = Failed to set value for "%s"!
automations_setadditionalvar_no_cstm_table = Cannot find a database table, where to save value for the additional variable.

automations_validateunique_no_unique_keys = No variables are set to check for uniqueness of the record.
automations_validateunique_no_cstm_table = No tables were found into the database where to check for additional vars.
automations_validateunique_unknown_unique_keys = Some of the vars set into the settings of the automatic action can't be defined as basic or additional.
automations_validateunique_basic_varst_not_exists = Some of the basic vars set to be checked from the automatic action were not found into the database.
automations_validateunique_additional_vars_not_exists = Some of the additional vars set to be checked from the automatic action were not found into the database.
automations_validateunique_duplicate_found = You can't add the record, because there is <a href="%s" target="_blank">%s</a> with data, which you want to save!
automations_validateunique_too_much_additional_vars = Too many additional vars set to check.

automations_sendtaggedasmail_missing_required_settings = Missing required settings for automation "%s"!
automations_sendtaggedasmail_missing_required_fields = Some of the fields, set into the settings of the automation "%s" are missing from the database!
automations_sendtaggedasmail_no_mail = For the <a href="%s" target="_blank">customer</a> of %s №%s the email is missing in the field "%s"!
automations_sendtaggedasmail_no_mail_pattern = The template set into the settings of the automation "%s" was not found into tha database!
automations_sendtaggedasmail_mail_failed = FAILED to send %s №%s!
automations_sendtaggedasmail_mail_success = Successfully send %s №%s!
automations_sendtaggedasmail_removetag_failed = Failed to remove tag "%s" for %s №%s from automation "%s"!
automations_sendtaggedasmail_removetag_success = Tag "%s" was removed for %s №%s from automation "%s"!
automations_sendtaggedasmail_incorrect_modul = Automation "%s" is not provided to work for the current module!

automation_create_models_row = &nbsp;at row %d
error_automation_create_models_settings_failed = Missing required_settings for automation %s!
error_automation_create_models_multiple_failed = Incorrect settings for multiple records creation in automation %s!
error_automation_create_models_invalid_field = Please, fill correct data at [row]!
error_automation_create_models_invalid_destination_field = Please, fill correct data in [field_name] field settings!
error_automation_create_models_failed = Error adding [model_name][row] in [source_name] [source_num]!
error_automation_create_models_history_failed = Error adding history for [model_name][row] in [source_name] [source_num]!
error_automation_create_models_assign_failed = Error adding assignments for [model_name][row] in [source_name] [source_num]!
error_automation_create_models_assign_history_failed = Error adding assignments history for [model_name][row] in [source_name] [source_num]!
error_automation_create_models = Errors adding [model_names]:
error_automation_create_models_multiple_edit_not_applicable = Multiple edit of records is not applicable
error_automation_create_models_edit_more_than_one = There are several records which could not be edited
error_automation_wrong_automation_type = This automation must be used only as a crontab!
error_automation_transfer_customer_type = Changing the type of customer %s failed! Please, contact nZoom support!
message_automation_create_models = Successfull [add_or_edit] of [model_names][model_nums]!

automations_sendmail_no_mails_from_settings = There are no settings for e-mail addresses!
automations_sendmail_no_mails = Can't define e-mail address to send mail!
automations_sendmail_no_email_template_settings = There is no mail template into the settings!
automations_sendmail_no_email_template_db = The mail template, set into the automatic action settings, doesn't exist in the database!
automations_sendmail_sending_mails_failed = An error occurred while sending the mails!

error_automations_copy_vars = Unsuccessful data copy from %s!
message_automations_copy_vars = Data was successfully copied from %s!

error_sms_not_sent = SMS was not sent to customer!
error_invalid_model = No record found
error_invalid_customer = <a href="%s" target="_blank"> Customer data </a> does not contain mobile number.
error_invalid_test_recipient = Specified test recipient (%s) has an invalid mobile number.

error_sending_sms_400 = Missing parameters
error_sending_sms_401 = IP Address Banned
error_sending_sms_402 = API Key Invalid
error_sending_sms_403 = User is not Active
error_sending_sms_404 = Service is not Active
error_sending_sms_405 = IP Address not allowed
error_sending_sms_406 = Over credit limit. Daily allowance.
error_sending_sms_407 = Over credit limit. Weekly allowance.
error_sending_sms_408 = Insufficient Balance
error_sending_sms_409 = Invalid MSISDN
error_sending_sms_410 = Text contains non latin characters
error_sending_sms_412 = Duplicate Request ID
error_sending_sms_413 = Invalid Request ID
error_sending_sms_414 = Template not found
error_sending_sms_415 = Timestamp for delayed execution is invalid
error_sending_sms_416 = MSISDN blacklisted
error_sending_sms_499 = Internal error XXX

error_sending_sms_general = An error occurred during SMS sending
msg_sms_sent = Customer (%s) has been sent notification: %s

asi_deal = Deal

invalid_automation_type = Wrong type %s for automatic action%s! The allowed types are: %s.

automation_setgt2_invalid_before_action_without_post = You can't use the automatic action as a before action, without sending data (POST)!
automation_setgt2_settings_gt2_vars_not_set = There are no settings for filling up a table!
automation_setgt2_settings_gt2_vars_count_invalid = Invalid settings count for filling up a table!
automation_setgt2_mismatch_rows = Mismatch result rows for filling up a table!
automation_setgt2_not_supporting_array_var_value_setting_with_something_else = Not supported opportunity for multiple rows if the setting contains something else than the var! Please use other approach!
automation_setgt2_failed_to_save = Failed to save table!
automation_setgt2_plain_vars_not_arrays = Multirow value can't be set for a table single vars!

automation_gridtomodels_not_supporting_array_var_value_setting_with_something_else = Multi-row data capability is not supported if the value setting contains anything other than the variable! Please use another approach!
automation_gridtomodels_multirow_source_vars_rows_count_mismatch = Different rows of data are obtained!
automation_gridtomodels_not_working_with_multirow_vars = The automation cannot fill multiline variables!
automation_gridtomodels_destination_model_save_failed_for_row = Failed to save record for row %d!
automation_gridtomodels_relation_id_failed_to_save = Failed to save links to created records!
