automations_errors_in_automation = <PERSON>hler in der Automatisierung%s:
automations_missing_required_settings = Erforderliche Einstellungen fehlen!

automations_setbasicvar_no_vars = Es gibt keine Vars in den Einstellungen des Automatisierungs!
automations_setbasicvar_vars_values_not_match = In den Einstellungen der Automatisierung, ist die Anzahl der Variablen, die nicht die Anzahl der Werte übereinstimmen für sie!
automations_setbasicvar_failed_to_set_value_for_var = Fehler beim Wert für Set "%s"!

automations_setadditionalvar_no_vars = Es gibt keine Variablen in den Einstellungen des Automatisierungs!
automations_setadditionalvar_vars_values_not_match = In den Einstellungen der Automatisierung, ist die Anzahl der Variablen, die nicht die Anzahl der Werte übereinstimmen für sie!
automations_setadditionalvar_some_vars_not_found = Einige der vars, in den Einstellungen der Automatisierung setzen, können nicht bearbeitet werden.
automations_setadditionalvar_failed_to_set_value_for_var = Fehler beim Wert für Set "%s"!
automations_setadditionalvar_no_cstm_table = Kann eine Datenbank-Tabelle nicht finden, wo die zusätzliche Variable schreiben.

automations_validateunique_no_unique_keys = Keine Variablen eingestellt um die Einzigartigkeit des Rekord zu überprüfen.
automations_validateunique_no_cstm_table = Keine Tabelle wurden in die Datenbank, wo Sie weitere Vars überprüfen gefunden.
automations_validateunique_unknown_unique_keys = Einige der in den Einstellungen der automatischen Aktion gesetzt Vars kann nicht als Grund-oder Zusatz definiert werden.
automations_validateunique_basic_varst_not_exists = Einige der grundlegenden Vars eingestellt, von der automatischen Maßnahmen überprüft werden nicht in der Datenbank gefunden.
automations_validateunique_additional_vars_not_exists = Einige der zusätzlichen Vars eingestellt, von der automatischen Maßnahmen überprüft werden nicht in der Datenbank gefunden.
automations_validateunique_duplicate_found = Sie können den Datensatz nicht hinzufügen, denn es gibt <a href="%s" target="_blank">%s</a> mit Daten, die Sie speichern möchten!
automations_validateunique_too_much_additional_vars = Zu viel zusätzliche Vars gesetzt, um zu überprüfen.

automations_sendtaggedasmail_missing_required_settings = Fehlen die erforderlichen Einstellungen für die Automatisierung "%s"!
automations_sendtaggedasmail_missing_required_fields = Einige der Felder, in die Einstellungen der Automatisierungs "%s" gesetzt werden aus der Datenbank fehlen!
automations_sendtaggedasmail_no_mail = Für die <a href="%s" target="_blank">Kunden</a> von %s №%s die E-Mail in das Feld "%s" fehlt!
automations_sendtaggedasmail_no_mail_pattern = Die in die Einstellungen des Automatisierungs gesetzt Vorlage "%s" wurde nicht in tha Datenbank gefunden!
automations_sendtaggedasmail_mail_failed = Nicht senden konnte %s №%s!
automations_sendtaggedasmail_mail_success = Erfolgreich senden %s №%s!
automations_sendtaggedasmail_removetag_failed = Fehler beim Tag zu entfernen "%s" s №%s von der Automatisierung "%s"!
automations_sendtaggedasmail_removetag_success = Tag "%s" wurde für %s №%s von der Automatisierung "%s" entfernt!
automations_sendtaggedasmail_incorrect_modul = Automation "%s" ist nicht vorgesehen, um für das aktuelle Modul zu arbeiten!

automation_create_models_row = &nbsp;in Ziele %d
error_automation_create_models_settings_failed = Fehlende erforderliche Einstellungen für die Automatisierung %s!
error_automation_create_models_multiple_failed = Falsche Einstellungen für mehrere Datensätze Schöpfung in der Automatisierung %s!
error_automation_create_models_invalid_field = Bitte füllen Korrektheit der Daten in [row]!
error_automation_create_models_invalid_destination_field = Bitte füllen Korrektheit der Daten in [field_name] Feldeinstellungen!
error_automation_create_models_failed = Fehler beim Hinzufügen [model_name][row] unter [source_name] [source_num]!
error_automation_create_models_history_failed = Fehler beim Hinzufügen einer Geschichte für [model_name][row] unter [source_name] [source_num]!
error_automation_create_models_assign_failed = Fehler beim Hinzufügen von Aufgaben für [model_name][row] unter [source_name] [source_num]!
error_automation_create_models_assign_history_failed = Fehler beim Hinzufügen von Aufgaben Geschichte nach [model_name][row] unter [source_name] [source_num]!
error_automation_create_models = Fehler Zugabe [model_names]:
error_automation_create_models_multiple_edit_not_applicable = Mehrfache Bearbeitung von Datensätzen ist nicht möglich
error_automation_create_models_edit_more_than_one = Es gibt mehrere Datensätze, die nicht bearbeitet werden konnten
error_automation_wrong_automation_type = Diese Automatisierung darf nur als Crontab verwendet werden!
error_automation_transfer_customer_type = Das Ändern des Kundentyps %s ist fehlgeschlagen! Bitte kontaktieren Sie den nZoom-Support!
message_automation_create_models = Erfolgreich [add_or_edit] von [model_names][model_nums]!

automations_sendmail_no_mails_from_settings = Es sind keine Einstellungen für E-Mail-Adressen!
automations_sendmail_no_mails = E-Mail-Adresse kann nicht definieren, um Mail senden!
automations_sendmail_no_email_template_settings = Es gibt kein Mail-Template in den Einstellungen!
automations_sendmail_no_email_template_db = Die E-Mail-Vorlage, in die automatische Aktion Einstellungen, existiert nicht in der Datenbank!
automations_sendmail_sending_mails_failed = Beim Versenden der Mails ist ein Fehler aufgetreten!

error_automations_copy_vars = Erfolglose Datenkopie von %s!
message_automations_copy_vars = Daten wurden erfolgreich von %s kopiert!

error_sms_not_sent = Keine SMS an den Client gesendet!
error_invalid_model = Kein Datensatz gefunden
error_invalid_customer = Die <a href="%s" target="_blank"> Kundendaten </a> verfügen nicht über ein Mobiltelefon.
error_invalid_test_recipient = Der eingestellte Testempfänger (% s) hat eine falsche Telefonnummer.

error_sending_sms_400 = Fehlende Parameter
error_sending_sms_401 = Gebannte IP-Adresse
error_sending_sms_402 = API-Schlüssel ungültig
error_sending_sms_403 = Benutzer ist nicht aktiv
error_sending_sms_404 = Dienst ist nicht aktiv
error_sending_sms_405 = IP Adresse nicht erlaubt
error_sending_sms_406 = Kreditlimit überschritten. Tagesgeld.
error_sending_sms_407 = Kreditlimit überschritten. Wöchentlicher Zuschuss.
error_sending_sms_408 = Unzureichende Balance
error_sending_sms_409 = Ungültige MSISDN
error_sending_sms_410 = Der Text enthält nicht lateinische Zeichen
error_sending_sms_412 = Doppelte Anforderungs-ID
error_sending_sms_413 = Ungültige Anforderungs-ID
error_sending_sms_414 = Vorlage nicht gefunden
error_sending_sms_415 = Zeitstempel für die verzögerte Ausführung ist ungültig
error_sending_sms_416 = MSISDN auf der schwarzen Liste
error_sending_sms_499 = Interner Fehler XXX

error_sending_sms_general = Es gab einen Fehler beim Senden
msg_sms_sent = Eine SMS-Nachricht wird an den Client gesendet (%s): %s

asi_deal = Deal

invalid_automation_type = Falscher Typ %s für automatische Aktion %s! Die erlaubten Typen sind: %s.

automation_setgt2_invalid_before_action_without_post = Sie können die automatische Aktion nicht als Vorher-Aktion verwenden, ohne Daten zu senden (POST)!
automation_setgt2_settings_gt2_vars_not_set = Es gibt keine Einstellungen zum Ausfüllen einer Tabelle!
automation_setgt2_settings_gt2_vars_count_invalid = Anzahl der ungültigen Einstellungen zum Ausfüllen einer Tabelle!
automation_setgt2_mismatch_rows = Nicht übereinstimmende Ergebniszeilen zum Füllen einer Tabelle!
automation_setgt2_not_supporting_array_var_value_setting_with_something_else = Nicht unterstützte Gelegenheit für mehrere Zeilen, wenn die Einstellung etwas anderes als die var enthält! Bitte andere Vorgehensweise verwenden!
automation_setgt2_failed_to_save = Fehler beim Speichern der Tabelle!
automation_setgt2_plain_vars_not_arrays = Mehrzeiliger Wert kann nicht für einzelne Variablen einer Tabelle gesetzt werden!

automation_gridtomodels_not_supporting_array_var_value_setting_with_something_else = Mehrzeilige Datenfähigkeit wird nicht unterstützt, wenn die Werteinstellung etwas anderes als die Variable enthält! Bitte verwenden Sie einen anderen Ansatz!
automation_gridtomodels_multirow_source_vars_rows_count_mismatch = Es werden unterschiedliche Datenzeilen erhalten!
automation_gridtomodels_not_working_with_multirow_vars = AutoAction kann keine mehrzeiligen Variablen füllen!
automation_gridtomodels_destination_model_save_failed_for_row = Datensatz für Zeile %d konnte nicht gespeichert werden!
automation_gridtomodels_relation_id_failed_to_save = Links zu erstellten Datensätzen konnten nicht gespeichert werden!
