<?php

/**
 *
 * @todo: On each execution of automation, prepare the $params['model'] from the database.
 *   This way we will skip the problem of loosing changes made from automation1 when executing automation2 after it.
 * @todo: Write history before, during and after automation esecution. Write more detailed history.
 *   This will be very useful for crontab automations.
 *
 */
class Automations_Controller extends Controller {

    public $name = '';

    public $executionMessages = array();

    public $executionErrors = array();

    public $executionWarnings = array();

    public $isExecuted = false;

    /**
     * Last execution date
     */
    public $lastExecutionDate = '';

    /**
     * Last execution date and time
     */
    public $lastExecutionTime = '';

    /**
     * Define whether start is checked by checkStart method
     */
    public $startIsChecked = false;

    /**
     * Next execution date
     */
    public $nextExecutionDate = '';

    /**
     * Next execution date and time
     */
    public $nextExecutionTime = '';

    /**
     * Before action automations can store original values from request here,
     * in case they need to be restored on failure of a subsequent automation
     * @var array
     */
    protected $originalRequestValues = array();

    /**
     * Automation settings (if available)
     */
    public $settings = array();

    /**
     * Name of flag from automation settings that specifies that automation
     * should be performed on the old model (by default it is performed on
     * the model)
     * @var string
     */
    const USE_OLD_MODEL = 'use_old_model';

    /**
     * Name of flag from automation settings that specifies that automation
     * should get fresh model from database)
     * @var string
     */
    const USE_UPDATED_MODEL = 'use_updated_model';

    /**
     * Execute - the main method used for execution of automations in background
     * mode (should_continue = 0)
     */
    public function execute() {

        $request = $this->registry['request'];

        //set system user as current user
        $this->registry->set('currentUser', $this->getAutomationUser(), true);

        //store the logged user
        $this->registry->set('originalUser', $this->getOriginalUser($request->get('user_id')), true);

        //set the corresponding module and controller (taken from request and previously from the automations table)
        $module = $request->get('automation_module');
        $controller = $request->get('automation_controller') ?: $request->get('automation_module');

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        if (is_readable($factory)) {
            require_once ($factory);
        } else {
            return;
        }

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        //get model
        $model = $factory_name::searchOne(
            $this->registry,
            array(
                'where' => array($alias . '.id = \'' . $request['id'] . '\''),
                'model_lang' => $request->get('model_lang')
            )
        );

        $this->registry->set('module', $module, true);
        $this->registry->set('controller', $controller, true);

        //check automations for this model
        $automation_id = $request['automation_id'];
        $this->performAutomation($model, (is_object($model) ? clone $model : $model), $automation_id);
    }

    /**
     * Loads custom additional i18n files or if parameter is empty, loads
     * all i18n files in the module's i18n folder
     *
     * @param array $files - path to files
     */
    public function loadI18NFiles($files = array()) {
        if (!$this->registry) {
            $translater = &$GLOBALS['translater'];
        } else {
            $translater = &$this->registry['translater'];
        }

        // If there are no files
        if (empty($files)) {
            // Load the lang files for the current module
            $i18n_dir = __DIR__ . '/../i18n/' . $this->registry['lang'] . '/';
            $lang_files = FilesLib::readDir($i18n_dir, false, 'files_only', 'ini', true);
            $translater->loadFile($lang_files, 'module');
        }

        $translater->loadFile($files, 'custom');
    }

    /**
     * Checks if there are any automations for single model (action, before_action or
     * before_viewer mode) and starts execution of the records found.
     * This method is used in direct "action" mode of the automations - they could be in foreground or background.
     *
     * @param Model $orig_model The current model that should be used for automations
     * @param Model $orig_old_model The previous version of the model used only for checking property conditions.
     *                              After some exceptional actions, old model is a different model - the source model
     *                              that the model was created from, allowing automations to be performed on either model.
     * @param int $automation_id Optional ID of the automation that should be executed in background mode (conditions not checked)
     * @param bool $checkAllSuccess Optional check if all automations executed successfully, false by default.
     * @return bool Result of the operation
     * @throws Exception If any errors occur during execution
     */
    public function performAutomation($orig_model, $orig_old_model, $automation_id = 0, bool $checkAllSuccess = false) {
        $allAutomationsSuccessful = true;

        //!!!IMPORTANT!!! we need to have user here
        if ($this->registry['module'] == 'crontab') {
            $user = $this->registry['currentUser'];
            if (empty($user) || get_class($user) != 'User') {
                $user = Users::searchOne($this->registry, array('where' => array('u.id = ' . PH_AUTOMATION_USER, 'u.hidden IS NOT NULL')));
                $this->registry->set('currentUser', $user->sanitize(), true);
            }
        }

        $db = $this->registry['db'];
        //get automation records
        $records = $this->getAutomations($orig_model);

        // real old model will be set to a different one from the input parameter only in case below
        $real_old_model = $orig_old_model;

        // check for automations to be perfored on old model, when:
        // - action automations are performed AND
        // - old model has id and it is different from id of model
        if (empty($this->before_action) && empty($this->before_viewer) &&
        $orig_old_model->get('id') && $orig_old_model->get('id') != $orig_model->get('id')) {
            // if models are different, automations on model will be executed
            // with a blank model for old model, as they should be when model has been added
            $model_name = $orig_model->modelName;
            $real_old_model = new $model_name($this->registry);
            $real_old_model->sanitize();

            // automations that should be performed on old model might be set
            // with its type as start_model_type so search for such as well
            if ($orig_old_model->get('type') && $orig_old_model->get('type') != $orig_model->get('type')) {
                $old_records = $this->getAutomations($orig_old_model);
                foreach ($old_records as $rid => $record) {
                    // only if automation should be executed on old model
                    if ($this->getAutomationSetting($record, self::USE_OLD_MODEL)) {
                        $records[$rid] = $record;
                    }
                }
                // sort all automations by position and id and execute them in that order
                $pos = array_map(function($r) { return $r['position']; }, $records);
                $ids = array_keys($records);
                array_multisort($pos, SORT_ASC, SORT_NUMERIC, $ids, SORT_ASC, SORT_NUMERIC, $records);
            }
        }

        if (!empty($records)) {
            if ($this->registry['currentUser']->get('id') != PH_AUTOMATION_USER) {
                //set the automations user
                $this->getOriginalUser();
                $this->setAutomationUserAsCurrent();
                $restoreCurrentUser = true;
            }

            //ids of automations
            $aids = array_keys($records);

            $i18n_file = PH_MODULES_DIR . 'automations/i18n/' . $this->registry['lang'] . '/automations.ini';
            $this->loadI18NFiles($i18n_file);

            $should_continue = 0;
            foreach ($records as $record) {
                if ($automation_id) {
                    //background mode
                    if ($record['id'] != $automation_id) {
                        continue;
                    } else {
                       //find background automation
                       $should_continue = $automation_id;
                       $automation_id = 0;
                    }
                }
                //skip dependent automation
                if ($record['depend'] > 0 && in_array($record['depend'], $aids)) {
                    continue;
                }

                $use_updated_model = $this->getAutomationSetting($record, self::USE_UPDATED_MODEL);

                // decide which models to use
                // check flag whether automation should be performed on old model (by default it is performed on model)
                if ($orig_old_model->get('id') && $orig_old_model->get('id') != $orig_model->get('id') && $use_updated_model) {
                    // the exception - models are different and flag is set
                    $model = $orig_old_model;
                    $old_model = clone $orig_old_model;
                } else {
                    // the regular case
                    $model = $orig_model;
                    $old_model = $real_old_model;
                }

                if ($use_updated_model) {
                    $updated_model = $this->getUpdatedModelFromDB($model);
                }

                if ($should_continue != $record['id']) {
                    $execute = $this->checkConditions($record['conditions'], ($use_updated_model ? $updated_model : $model), $old_model, $record['id']);
                } else {
                    //don't check first background automation
                    $execute = true;
                }

                if ($execute) {
                    if ($record['should_continue'] || $should_continue) {
                        // Encapsulate the old model into the model
                        $model->set('old_model', $old_model);
                        if ($use_updated_model) {
                            $updated_model->set('old_model', $old_model);
                        }

                        //execute automation method, wait for result
                        $result = $this->executeMethod($record, ($use_updated_model ? $updated_model : $model));

                        if ($result) {
                            //update automation history
                            $this->updateAutomationHistory($record, $model, $result);
                            //remove executed automations id
                            unset($aids[array_search($record['id'], $aids)]);

                            //set automation after action
                            if (!empty($record['after_action'])) {
                                //save the requested after action as previous
                                if ($this->registry['request']->get('after_action')) {
                                    $this->registry['request']->set('prev_after_action', $this->registry['request']->get('after_action'), 'post', true);
                                }

                                //parse the after action defs
                                //just set the after action and its options manually in the request
                                //the controller will get the after action from the request and will define it successfully
                                $after_action_defs = preg_split('/(\n|\r|\r\n)/', $record['after_action']);
                                foreach ($after_action_defs as $def) {
                                    if (trim($def) && !preg_match('/^#/', $def) && strpos($def, ':=')) {
                                        list($key, $value) = preg_split('/\s*\:=\s*/', $def);
                                        if (preg_match('#^\[#', $value)) {
                                            $property = preg_replace('#\[([^\]]*)\]#', '\\1', $value);
                                            $value = $old_model->get($property);
                                        }
                                        if (empty($this->before_action)) {
                                            $this->registry['request']->set($key, $value, 'post', true);
                                        }
                                    }
                                }
                            }
                        } else {
                            if ($checkAllSuccess) {
                                $allAutomationsSuccessful = false;
                            }

                            //check if we have to cancel action on automation fail
                            //IMPORTANT !!! for automation plugins only which are executed before the action
                            $after_action_defs = preg_split('/(\n|\r|\r\n)/', $record['after_action']);
                            foreach ($after_action_defs as $def) {
                                if ($def) {
                                    list($key, $value) = preg_split('/\s*\:=\s*/', $def);
                                    if (trim($key) == 'cancel_action_on_fail' && trim($value) > 0) {
                                        // clean up data set by previous successful automations
                                        $this->performAutomationCleanup();
                                        //return the original user
                                        $this->setOriginalUserAsCurrent();
                                        return false;
                                    }
                                }
                            }
                        }
                    } else {
                        $this->registry['messages']->setWarning($this->i18n('warning_automation'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //execute automation in the background
                        $url = sprintf('%s/index.php?%s=automations&id=%d&model_lang=%s&automation_id=%d&user_id=%d&automation_module=%s&automation_controller=%s',
                                $this->registry['config']->getParam('crontab', 'base_host'), $this->registry['module_param'],
                                $model->get('id'), $model->get('model_lang'), $record['id'],
                                $this->registry['originalUser']->get('id'), $record['module'], $record['controller']);

                        $serverProcessesHelper = new ServerProcessesHelper($this->registry['config']->getParam('crontab', 'base_host'));
                        $serverProcessesHelper->backgroundExecute($url, []);
                        break;
                    }
                }
            }
            //return the original user
            if (!empty($restoreCurrentUser)) {
                $this->setOriginalUserAsCurrent();
            }
        }

        return $allAutomationsSuccessful;
    }

    /**
     * Get automation setting
     *
     * @param array $record - array of automation settings (fetched from the DB)
     * @param string $flag - use_old_model, use_updated_model
     * @return boolean - true if flag is present and set to true, otherwise false
     */
    public function getAutomationSetting(array $record, string $flag) {
        if (!empty($record['settings'])) {
            if (is_array($record['settings'])) {
                return !empty($record['settings'][$flag]);
            } elseif (is_string($record['settings'])) {
                $settings_arr = General::parseSettings($record['settings'], '#^' . $flag . '$#');
                return !empty($settings_arr[$flag]);
            }
        }
        return false;
    }

    /**
     * Cleans up Messages and Request in Registry on failure of a before_action
     * automation
     */
    private function performAutomationCleanup() {
        // remove all previously set success messages as external transaction
        // will fail everything
        $this->registry['messages']->unset_vars('messages');

        // restore previous values in request which were kept before changing
        // them from within automations
        if (!empty($this->originalRequestValues) && is_array($this->originalRequestValues)) {
            foreach ($this->originalRequestValues as $key => $value) {
                $source = 'all';
                if ($this->registry['request']->isPost($key)) {
                    $source = 'post';
                } elseif ($this->registry['request']->isGet($key)) {
                    $source = 'get';
                }
                $this->registry['request']->set($key, $value, $source, true);
            }
        }
    }

    /**
     * Checks if there are any automations that should be executed in CRONTAB mode
     * This method is used in "crontab" mode of the automations.
     * It takes all crontab automations and executes them for ALL models found
     * in the the database matching the specified conditions.
     *
     * @return bool - the result of the operation
     *
     * TODO: Add error/warning with the ID of the automation,
     * before every automation that generated errors/warnings
     * so when sending execution errors we will see which automation generated them
     */
    public function performCrontabAutomation() {
        $db = $this->registry['db'];

        //get automation CRONTAB records from the database
        $records = $this->getCrontabAutomations();

        if (!empty($records)) {
            $i18n_file = PH_MODULES_DIR . 'automations/i18n/' . $this->registry['lang'] . '/automations.ini';
            $this->loadI18NFiles($i18n_file);

            //set system user as current user
            require_once PH_MODULES_DIR .  'users/models/users.factory.php';
            $filters = array('where' => array('u.id = ' . PH_AUTOMATION_USER,
                                              'u.hidden IS NOT NULL'),
                             'sanitize' => true);
            $user = Users::searchOne($this->registry, $filters);
            $user->unsanitize();
            $user->getGroups();
            $user->sanitize();
            $this->registry->set('currentUser', $user, true);
            $this->registry->set('crontab_automations', true, true);

            //start consecutive preparation of each crontab automation
            foreach ($records as $record) {
                $this->startIsChecked = false;
                if (preg_match('#.*condition\s*:=\s*1.*#m', $record['conditions']) && preg_match('#.*plugin\s*:=.*#m', $record['method'])) {
                    //when plugin with always-true condition is used, leave the
                    // plugin to get the models, do not get any model

                    //execute automation method
                    $result = $this->executeMethod($record, false);

                    //IMPORTANT!!!
                    //the plugin should record the automation history
                } else {
                    // get settings for the automation
                    $this->getSettings($record);

                    if ($this->checkStart($record['id'])) {

                        // parse conditions, collecting all values per key in indexed arrays
                        $record['conditions'] = General::parseSettings($record['conditions'], false, true);

                        //get model ids for the defined module, controller and model type
                        $model_ids = $this->getModelIds($record);

                        // no matching models, nothing to do
                        if (!$model_ids) {
                            continue;
                        }

                        //get history log for the model ids and the current automation row
                        $history_ids = $this->getCrontabHistoryInfo(array('id' => $record['id'], 'model_ids' => $model_ids));

                        //get history log for the model ids of a dependent automation row
                        $check_dependent = false;
                        if ($record['depend'] > 0 && isset($records[$record['depend']])) {
                            $check_dependent = true;
                            $depend_history_ids = $this->getCrontabHistoryInfo(array('id' => $record['depend'], 'model_ids' => $model_ids));
                        }

                        $module     = $record['module'];
                        $controller = $record['controller'] ? $record['controller'] : $record['module'];

                        // prepare factory name
                        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

                        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
                        require_once ($factory);

                        // prepare alias
                        $alias = $factory_name::getAlias($module, $controller);

                        foreach ($model_ids as $model_id) {
                            //check if this automation has already been executed
                            if (isset($history_ids[$record['id'] . '_' . $model_id])) {
                                continue;
                            }

                            //check if this dependent automation has already been performed for the model_id
                            if ($check_dependent && !isset($depend_history_ids[$record['depend'] . '_' . $model_id])) {
                                continue;
                            }

                            //get the model from the database
                            $model = $factory_name::searchOne(
                                $this->registry,
                                array(
                                    'where' => array($alias . '.' . ($module == 'layouts' ? 'layout_' : '') . 'id = ' . $model_id),
                                    'sanitize' => true
                                )
                            );

                            if (!$model) {
                                //do nothing if no model is found
                                continue;
                            }

                            //get the additional variables for some of the models
                            if ($model->checkForVariables()) {
                                $model->getVars();
                            }

                            // check the current automation conditions -
                            // only when WHERE conditions are not specified
                            $execute =
                                empty($record['conditions']['where']) ?
                                $this->checkConditions($record['conditions'], $model, (is_object($model) ? clone $model : $model), $record['id']) :
                                true;

                            if ($execute) {
                                //execute automation method
                                $result = $this->executeMethod($record, $model);

                                if ($result) {
                                    //update automation history
                                    $this->updateAutomationHistory($record, $model, $result);
                                }
                            }

                            //try to free some more memory
                            unset($model);
                        }
                    }
                }
            }
        } else {
            return true;
        }
    }

    /**
     * Gets automation records for single model (action, before_action or
     * before_viewer mode)
     *
     * @param Model $model - the current model that should be used for automations
     * @return array - list of all automation rows found in the DB
     */
    public function getAutomations($model) {
        if (empty($model)) {
            return array();
        }
        $db = $this->registry['db'];
        $where = array();
        $where[] = sprintf("module='%s'", $this->registry['module']);
        if ($this->registry['module'] != $this->registry['controller']) {
            $where[] = sprintf("controller='%s'", $this->registry['controller']);
        }
        if ($model->get('type')) {
            $model_type = $model->get('type');
            if (preg_match('#^advance_\d+$#', $model_type)) {
                $model_type = PH_FINANCE_TYPE_INVOICE;
            } elseif (preg_match('#^proforma_advance_\d+$#', $model_type)) {
                $model_type = PH_FINANCE_TYPE_PRO_INVOICE;
            }
            $where[] = "(start_model_type='0' OR start_model_type='" . $model_type . "')";
        } else {
            $where[] = "start_model_type='0'";
        }
        if (!empty($this->before_action)) {
            $where[] = "automation_type = 'before_action'";
        } elseif (!empty($this->before_viewer)) {
            $where[] = "automation_type = 'before_viewer'";
        } else {
            $where[] = "automation_type = 'action'";
        }
        $where[] =  "a.active = '1'";
        $where[] = "(parent_id is null OR num < nums OR nums = 0)";
        $query = 'SELECT a.id as aid, a.* ' . "\n" .
                 'FROM ' . DB_TABLE_AUTOMATIONS . ' as a ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_AUTOMATIONS_HISTORY .
                 ' as ah ON id=parent_id AND model_id="' . $model->get('id') . '"' . "\n" .
                 'WHERE ' . implode(' AND ', $where). "\n" .
                 'ORDER BY a.position';
        $records = $db->GetAssoc($query);

        return $records;
    }

    /**
     * Gets automation records for CRONTAB mode of the automations
     *
     * @return array - list of all automation rows found in the DB
     */
    public function getCrontabAutomations() {
        $db = $this->registry['db'];
        $where[] = "automation_type = 'crontab'";
        $where[] = "a.active = '1'";
        $query = 'SELECT a.id as aid, a.* ' . "\n" .
                 'FROM ' . DB_TABLE_AUTOMATIONS . ' as a ' . "\n" .
                 'WHERE ' . implode(' AND ', $where). "\n" .
                 'ORDER BY a.position';
        $records = $db->GetAssoc($query);

        return $records;
    }

    /**
     * Gets history of automation records for CRONTAB mode of the automations
     *
     * @param array $params - filters to get the certain records from the history
     * @return array - list of all automation log records found in the DB
     */
    public function getCrontabHistoryInfo($params) {
        $db = $this->registry['db'];
        if (isset($params['id'])) {
            $where[] = General::buildClause('ah.parent_id', $params['id']);
        }
        if (isset($params['model_ids'])) {
            $where[] = General::buildClause('ah.model_id', $params['model_ids']);
        }
        $where[] = "automation_type = 'crontab'";
        $where[] = "num >= nums AND nums > 0";
        $query = 'SELECT CONCAT(ah.parent_id, "_", ah.model_id) as idx, ah.num ' . "\n" .
                 'FROM ' . DB_TABLE_AUTOMATIONS_HISTORY . ' AS ah' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_AUTOMATIONS . ' AS a' . "\n" .
                 '  ON id=parent_id' . "\n" .
                 'WHERE ' . implode(' AND ', $where). "\n" ;
        $records = $db->GetAssoc($query);

        return $records;
    }

    /**
     * Gets result field from previously executed automation from the history of automations
     *
     * @param int $model_id - id of the model
     * @param int $automation_id - id of the automation row
     * @return string - the recorded result from the previously executed automation
     */
    public function getHistoryResult($model_id, $automation_id) {
        $db = $this->registry['db'];
        $where[] = 'parent_id =' . $automation_id;
        $where[] = 'model_id= ' . $model_id;
        $query = 'SELECT result ' . "\n" .
                 'FROM ' . DB_TABLE_AUTOMATIONS_HISTORY . ' as ah ' . "\n" .
                 'WHERE ' . implode(' AND ', $where). "\n" ;
        $record = $db->GetOne($query);

        return $record;
    }

    /**
     * Gets ids of all the models for CRONTAB mode of the automations
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return array - list of ids of the models found in the DB
     */
    public function getModelIds(&$params) {

        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];
        // include factory class file
        $factory_name = implode('_', array_map('ucfirst', explode('_',
            $module . ($module != $controller ? '_' . $controller : ''))));
        $factory = PH_MODULES_DIR . $module . '/models/' . $module .
            (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        require_once ($factory);

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        // make sure conditions are parsed
        if (!empty($params['conditions']) && !is_array($params['conditions'])) {
            $params['conditions'] = General::parseSettings($params['conditions'], false, true);
        }

        if (!empty($params['conditions']['where'])) {
            // build WHERE clause from search filters set in 'conditions'
            $where = $params['conditions']['where'];
            if (!empty($params['start_model_type'])) {
                array_unshift($where, "$alias.type = '{$params['start_model_type']}'");
            }
        } else {
            // use default search filters. these decrease the great amount of
            // models that might be expected for some large databases
            $where = array();
            switch ($module) {
            case 'documents':
                $where[] = "$alias.status != 'closed'";
                break;
            case 'tasks':
                $where[] = "$alias.status != 'finished'";
                break;
            case 'projects':
                $where[] = "($alias.status != 'finished' OR $alias.finished IS NULL)";
                break;
            }
            if (!($module == 'finance' && $controller == 'payments')) {
                $where[] = "$alias.active = 1";
            }
            if (!empty($params['start_model_type'])) {
                $where[] = "$alias.type = '{$params['start_model_type']}'";
            }
        }
        if ($module == 'layouts') {
            $sort = array("$alias.layout_id ASC");
        } else {
            $sort = array("$alias.id ASC");
        }
        if (!($module == 'finance' && $controller == 'payments')) {
            $sort[] = "$alias.active DESC";
        }

        // search model ids using Model_Factory::getIds method
        $filters = array(
            'where' => $where,
            'sort' => $sort,
        );
        if ($module == 'events') {
            // we need to find all events, even private ones
            $filters['participant_id'] = 'all';
        } else {
            // change action in order to be able to search by additional variables
            $this->registry->set('action', 'search', true);
        }

        if (!empty($params['conditions']['limit'])) {
            $limit = array_pop($params['conditions']['limit']);
            if (is_numeric($limit)) {
                $filters['limit'] = $limit;
            }
        }

        $records = $factory_name::getIds($this->registry, $filters);

        $this->registry->set('action', $this->action, true);

        if (!is_array($records) && $this->registry['db']->ErrorNo()) {
            // there must be some error in query, notify for error
            $this->executionErrors[] = "DB error: ({$this->registry['db']->ErrorNo()}) {$this->registry['db']->ErrorMsg()}";
            $this->isExecuted = true;
            $this->name = !empty($params['name']) ? $params['name'] . ' (' . $params['id'] . ')' : $params['id'];
            $this->sendExecutionResult();
        }

        return $records;
    }

    /**
     * Checks if the conditions of the current model are matching the automation criteria
     * The conditions are eval-ed as if they are a PHP code
     *
     * @param string|array $conditions - data from 'conditions' column of
     *     automations DB table as a string or as an array, when already parsed
     * @param Model $new_model  - the current model that should be used for automations
     * @param Model $old_model  - the previous version of the model that could be used only for check of conditions of its properties
     * @param int $record_id - id of record in db where conditions originate from (used for error logging)
     * @return bool - whether the conditions are matching the criteria or not (true/false)
     */
    public function checkConditions($conditions, $new_model, $old_model, $record_id = 0) {
        $result = true;

        if (!is_array($conditions)) {
            // parse only source rows that start with "condition := "
            // (ignore rows that start with "#" (commented), rows that don't
            // contain ":=" or rows that start with any other setting name)
            $conditions = General::parseSettings($conditions, '#^condition$#', true);
        }
        $conditions = !empty($conditions['condition']) ? $conditions['condition'] : array();
        //IMPORTANT: the lack of conditions means TRUE
        if (!$conditions) {
            return $result;
        }

        // Get the request
        $request = &$this->registry['request'];
        $requestIsPost = (int) $request->isPost();

        foreach ($conditions as $condition) {
            // extract all variable names part of the condition
            $vars = $this->extractVariables($condition);

            //prepare the basic and additional variables for replacement
            $search = array();
            $replace = array();
            foreach ($vars as $var) {
                // If the check is for the action
                if ($var == 'action') {
                    // Get the action
                    $search[] = '[action]';
                    $replace[] = $this->action;
                    continue;
                }

                // If we are checking if the request is post
                if ($var == 'request_is_post') {
                    // Get the request condition
                    $search[] = '[request_is_post]';
                    $replace[] = $requestIsPost;
                    continue;
                }

                if (preg_match('#^prev_.*$#', $var)) {
                    //variables for the old model
                    $value_source_model = 'old_model';
                    if (preg_match('#^prev_a_.*$#', $var)) {
                        //additional vars have prefix a_
                        $value_source_method = 'getPlainVarValue';
                        $var_name = preg_replace('#^prev_a_#', '', $var);
                    } else {
                        //basic vars have prefix b_
                        $value_source_method = 'get';
                        $var_name = preg_replace('#^prev_b_#', '', $var);
                    }

                    //$old_model->slashesEscape();
                } else {
                    //variables for the new model
                    $value_source_model = 'new_model';
                    if (preg_match('#^a_.*$#', $var)) {
                        //additional vars have prefix a_
                        $value_source_method = 'getPlainVarValue';
                        $var_name = preg_replace('#^a_#', '', $var);
                    } else {
                        //basic vars have prefix b_
                        $value_source_method = 'get';
                        $var_name = preg_replace('#^b_#', '', $var);
                    }

                    //$new_model->slashesEscape();
                }
                $search[] = '[' . $var . ']';
                $value = $$value_source_model->$value_source_method($var_name);
                if (!$$value_source_model->slashesEscaped) {
                    $value = General::slashesEscape($value);
                }
                if (is_array($value)) {
                    if (count($value) == 1) {
                        $replace_value = array_pop($value);
                    } else if (!empty($value)) {
                        $replace_value = 'array("' . implode('", "', $value) . '")';
                    } else {
                        $replace_value = '';
                    }
                } else if (is_object($value) && get_class($value) == 'File') {
                    $replace_value = $value->get('id');
                } else {
                    $replace_value = $value;
                }
                $replace[] = $replace_value;
            }

            //check condition
            $result = EvalString::evaluate(
                $this->registry,
                $condition,
                array(
                    'search'    => $search,
                    'replace'   => $replace,
                    'object'    => $this,
                    'new_model' => $new_model,
                    'old_model' => $old_model,
                    'origin'    => array(
                        'table' => DB_TABLE_AUTOMATIONS,
                        'field' => 'conditions',
                        'id'    => $record_id,
                    ),
                )
            );
            if (!$result) {
                break;
            }
        }

        // Strip properties of old and new model so they can be used
        // in automation methods without accumulated backslashes
        //if ($old_model->slashesEscaped) {
        //    $old_model->slashesStrip();
        //}
        //if ($new_model->slashesEscaped) {
        //    //$new_model->slashesStrip();
        //}

        return $result;
    }

    /**
     * Executes automation method with some arguments passed - registry, model_id, the model itself
     *
     * @param array $record - array of automation settings (fetched from the DB)
     * @param Model $model - the current model that should be used for automations
     * @return bool - result of the execution of the method
     */
    public function executeMethod($record, $model) {
        $source_method = $record['method'];

        //this is an empty method used just for redirection
        if (!$source_method) {
            return true;
        }

        $source_rows = preg_split('/(\n|\r|\r\n)/', $source_method);
        $source_args = $record;
        foreach ($source_rows as $s_row) {
            if (empty($s_row) || $s_row[0]=='#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);
            if ($key == 'method') {
                //method name
                $source_method = $value;
            } else {
                //arguments
                $source_args[$key] = $value;
            }
        }
        $source_args['model_id'] = ($model) ? $model->get('id') : false;
        $source_args['model'] = $model;

        $this->executionMessages = array();
        $this->executionWarnings = array();
        $this->executionErrors = array();
        //get automation name and id (or id if name is not set)
        $this->name = !empty($record['name']) ? $record['name'] . ' (' . $record['id'] . ')' : $record['id'];

        //set error handler and shutdown handler
        $shutdown = new Shutdown();
        $shutdown->register(array($this, 'onError'), 'fatal');
        set_error_handler(array($this, 'onError'));

        if (empty($source_args['plugin'])) {
            //check if the method exists
            if (method_exists($this, $source_method)) {
                //set isExecuted for sending of execution results
                $this->isExecuted = true;

                //get settings
                $this->getSettings($record);

                //execute standard method (not custom one from a plugin)
                $result = $this->$source_method($source_args);
            } else {
                return false;
            }
        } else {
            //plugin automation is requested
            $file = PH_MODULES_DIR . sprintf('automations/plugins/%s/controllers/%s.automations.controller.php',
                                             $source_args['plugin'], $source_args['plugin']);
            if (!file_exists($file)) {
                return false;
            }

            //load plugin i18n files
            $i18n_file = PH_MODULES_DIR . 'automations/plugins/' . $source_args['plugin'] .
                         '/i18n/' . $this->registry['lang'] . '/' . $source_args['plugin'] . '.ini';
            $this->loadI18NFiles($i18n_file);

            require_once $file;
            $controller = str_replace(' ', '_', ucwords(str_replace('_', ' ', $source_args['plugin']))) . '_Automations_Controller';
            /**
             * @var $controller Automations_Controller
             */
            $controller = new $controller($this->registry);
            $controller->getAutomationUser();
            $this->executionMessages = &$controller->executionMessages;
            $this->executionWarnings = &$controller->executionWarnings;
            $this->executionErrors = &$controller->executionErrors;
            //check if we have automation name in the language files
            if ($controller->i18n('custom_automation_name')) {
                $controller->name = $controller->i18n('custom_automation_name');
                $this->name = &$controller->name;
            } else {
                $controller->name = &$this->name;
            }
            //get settings for the controller
            $controller->getSettings($record);
            $this->settings = &$controller->settings;
            $controller->metaData = $source_args;
            if (!empty($this->before_action)) {
                $controller->before_action = true;
            }
            //check if the method exists
            if (method_exists($controller, $source_method)) {
                if (!$this->startIsChecked && !$controller->checkStart($record['id'])) {
                    $result = false;
                } else {
                    //execute plugin method
                    $result = $controller->$source_method($source_args);

                    // merge values to restore on failure but do not overwrite
                    // existing ones (oldest value should be used)
                    if (!empty($controller->originalRequestValues)) {
                        $this->originalRequestValues = $this->originalRequestValues + $controller->originalRequestValues;
                    }
                }

                //set isExecuted for sending of execution results
                $this->isExecuted = $controller->isExecuted;
            } else {
                return false;
            }
        }

        //send notification for the automation result
        $this->sendExecutionResult($record);

        //restore handlers
        restore_error_handler();
        $shutdown->unregister(-1);
        $shutdown = null;

        return $result;
    }

    /**
     * Function to change the error handler and shutdown handler with
     *
     * @param string $code - error code
     */
    public function onError($code = false) {
        // If error reporting is currently turned off or suppressed with @
        if (error_reporting() === 0) {
            // Skip current custom error reporting
            return;
        }

        $error = error_get_last();
        if (!is_null($error)) {
            //don't care for errors of calling non-static methods statically and for errors in the /ext folder.
            //if we need to care about these errors, we will have lots of work to do.
            if (!preg_match('#non-static|chmod#i', $error['message']) &&
                !preg_match('#(/inc/ext/|\\\inc\\\ext\\\)#', $error['file']) &&
                !preg_match('#^Declaration of .* should be compatible with that of .*$#', $error['message'])) {
                $this->executionWarnings[] = '<pre>' . print_r($error, true) . '</pre>';
            }
        }
        if ($code == 'fatal') {
            $this->executionErrors[] = '<pre>' . print_r($error, true) . '</pre>';
            $this->sendExecutionResult();
            exit;
        }

        return false;
    }

    /**
     * Send Notification email about automation execution
     *
     * @param array $automationRecord - automation record (array)
     * @return bool - result of operation
     */
    public function sendExecutionResult($automationRecord = null) {

        if (empty($this->settings['send_to_email'])) {
            return false;
        }

        $messages = $this->executionMessages;
        $warnings = $this->executionWarnings;
        $errors = $this->executionErrors;

        $notificationsTypes = ['messages', 'errors', 'warnings'];
        if (!empty($this->settings['send_to_notifications'])) {
            $notificationsTypes = preg_split('#\s*,\s*#', $this->settings['send_to_notifications']);
        }

        if (empty($this->isExecuted)) {
            //don't send emails for non-executed methods
            return false;
        }

        if (!is_array($errors)) {
            $errors = array($errors);
        }

        //prepare mailer
        $template = 'automation_results_notification';

        $model = new Model($this->registry);
        //set automation id as model type
        $model->set('type', $automationRecord['id'] ?? '', true);
        //set model name to be Automation
        $model->modelName = 'Automation';
        if (!$model->shouldSendEmail($template)) {
            return true;
        }

        $mailer = new Mailer($this->registry, $template, $model);

        $mailer->placeholder->add('automation_name', $this->name);
        $mailer->placeholder->add('installation', $this->registry['config']->getParam('sys', 'code'));

        //prepare placeholders
        if (empty($errors)) {
            if (!in_array('messages', $notificationsTypes) &&
                (empty($warnings) || !empty($warnings) && !in_array('warnings', $notificationsTypes))) {
                //do not notify of messages
                return true;
            }

            // process success messages
            $messages_success = $this->i18n('automation_success');
            $mailer->placeholder->add('automation_result', $messages_success);

            // complete the message
            if (in_array('messages', $notificationsTypes) && !empty($messages)) {
                $messages_success .= '<br /><br />' . $this->i18n('automation_messages') . ':<br />' . implode('<br />', $messages);
            }
            $mailer->placeholder->add('automation_result_detailed', $messages_success);

            // process warnings
            if (!empty($warnings) && in_array('warnings', $notificationsTypes)) {
                $mailer->placeholder->add('automation_errors', $this->i18n('automation_warnings') . ':<br />' . implode('<br />', $warnings));
            }
        } else {
            if (!in_array('errors', $notificationsTypes) &&
                (empty($warnings) || !empty($warnings) && !in_array('warnings', $notificationsTypes))) {
                //do not notify of errors
                return true;
            }

            // process errors
            $mailer->placeholder->add('automation_result', $this->i18n('automation_failed'));
            $mailer->placeholder->add('automation_result_detailed', $this->i18n('automation_failed'));
            $msg = $this->i18n('automation_errors') . ':<br />' . implode('<br />', $errors);

            // process warnings
            if (!empty($warnings) && in_array('warnings', $notificationsTypes)) {
                $msg .= '<br /><br />' . $this->i18n('automation_warnings') . ':<br />' . implode('<br />', $warnings);
            }
            $mailer->placeholder->add('automation_errors', $msg);
        }
        $mailer->placeholder->add('to_email', $this->settings['send_to_email']);

        //send email
        $result = $mailer->send();

        if (empty($result['erred'])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Parses automation settings and sets them as a property of model -
     * can be overwritten in the plugin controllers
     *
     * @param array $record - array of automation settings (fetched from the DB)
     * @return array - associative array with settings
     */
    public function getSettings($record) {

        $settings = array();
        if ($record['settings']) {
            $settings = General::parseSettings($record['settings']);
        }

        $this->settings = $settings;

        return $settings;
    }

    // TODO: checkRequiredSettings

    /**
     * Logs the execution of the automation in the history, so that each automation is counted
     *
     * @param array $record - array of automation settings (fetched from the DB)
     * @param object $model - the current model that should be used for automations
     * @param string $result - temporary result string stored in the automation history used by consecutive automations
     */
    public function updateAutomationHistory($record, $model, $result = '') {
        $db = $this->registry['db'];

        $id = 0;
        if (is_object($model)) {
            $id = $model->get('id');
        }

        $added = 'now()';
        if (!empty($record['log_date'])) {
            $added = '"' . $record['log_date'] . '"';
        }

        $query = 'INSERT INTO `' . DB_TABLE_AUTOMATIONS_HISTORY . '`' . "\n" .
                 '  SET `parent_id` = \'' . $record['id'] . '\',' . "\n" .
                 '    `model_id`    = \'' . $id . '\',' . "\n" .
                 '    `added`       = ' . $added . ',' . "\n" .
                 '    `num`         = \'1\',' . "\n" .
                 '    `result`      = "' . $result . '"' . "\n" .
                 '  ON DUPLICATE KEY UPDATE' . "\n" .
                 '    `added`  = ' . $added . ',' . "\n" .
                 '    `num`    = `num` + 1,' . "\n" .
                 '    `result` = "' . $result . '"';
        $db->Execute($query);
        if ($record['exclude']) {
            //insert exclude automations
            $exclude = preg_split('/\s*\,\s*/', $record['exclude']);
            foreach ($exclude as $exclude_id) {
                $query = 'INSERT INTO `' . DB_TABLE_AUTOMATIONS_HISTORY . '`' . "\n" .
                         '  SET `parent_id` = \'' . $exclude_id . '\',' . "\n" .
                         '    `model_id`    = \'' . $id . '\',' . "\n" .
                         '    `added`       = \'0000-00-00 00:00:00\',' . "\n" .
                         '    `num`         = \'1\'' . "\n" .
                         '  ON DUPLICATE KEY UPDATE' . "\n" .
                         '    `added` = \'0000-00-00 00:00:00\',' . "\n" .
                         '    `num`   = `num` + 1';
                $db->Execute($query);
            }
        }
    }

   /********************** STANDARD AUTOMATION METHODS **********************/

   /**
    * Forwards a document. In fact this method changes the department of a document
    *
    * @param array $params - arguments for the method, containing registry
    * @return bool - result of the execution of the method
    */
    public function forward($params) {
        // prepare some basics
        $registry   = $this->registry;
        $db         = $registry['db'];
        $id         = $params['model_id'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];
        $db_table   = constant('DB_TABLE_' . strtoupper($module . ($module != $controller ? '_' . $controller : '')));
        $model      = $params['model'];

        $new_department = !empty($params['new_department']) ? $params['new_department'] : '0';

        $set = array();
        $set['department']      = sprintf("`department`=%d", $new_department);
        if (in_array($module, array('documents', 'contracts', 'tasks'))) {
            $set['ownership']   = sprintf("`ownership`='%s'", ($new_department ? 'forwarded' : 'unforwarded'));
        }
        $set['modified']        = sprintf("`modified`=NOW()");
        $set['modified_by']     = sprintf("`modified_by`=%d", $this->registry['currentUser']->get('id'));

        $query = 'UPDATE ' . $db_table . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE id=\'' . $id . '\'';
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            return false;
        } else {
            // send notification to department manager
            // (only documents have such notification e-mail template for now)
            if ($module == 'documents' && $new_department) {
                $template = strtolower($model->modelName) . '_forward';
                //get ids of users who ignore this type of email
                $not_users = Users::getUsersNoSend($this->registry, $template);

                $query = 'SELECT d.manager AS manager_id, ' . "\n" .
                         '  u2.email AS manager_email, ' . "\n".
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) AS manager_name ' . "\n".
                         'FROM ' . DB_TABLE_DEPARTMENTS . ' AS d' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_USERS . ' AS u2' . "\n" .
                         '  ON (d.manager=u2.id AND u2.active=1 AND u2.deleted_by=0)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                         '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="' . $registry->get('lang') . '")' . "\n" .
                         'WHERE d.id = ' . $new_department;
                list($record) = $db->GetAll($query);

                if ($record['manager_email'] && !in_array($record['manager_id'], $not_users)) {
                    //send email
                    $model->unsanitize();
                    $model->sendNotification($template, $record['manager_email'], $record['manager_name']);
                    $model->sanitize();
                }
            }

            // write history
            $old_model = clone $model;
            $model->set('department', $new_department, true);

            // prepare factory name
            $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

            $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
            require_once ($factory);

            // prepare alias
            $alias = $factory_name::getAlias($module, $controller);

            $new_model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = ' . $id),
                    'sanitize' => true
                )
            );
            $history_class_name = $factory_name . '_History';
            $history_vars = array(
                'model'         => $model,
                'action_type'   => ($module == 'documents' ? 'forward' : 'edit'),
                'new_model'     => $new_model,
                'old_model'     => $old_model
            );

            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
            $audit_parent = $history_class_name::saveData($registry, $history_vars);

            return true;
        }
    }

    /**
     * Assigns users to the specified model e.g. assign of document or task
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function assign($params) {
        $registry   = $this->registry;
        $db         = $registry['db'];
        $id         = $params['model_id'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];

        //get module-specific i18n files for crontab automations
        if ($this->registry->get('crontab_automations')) {
            $i18n_files = array();
            $i18n_files[] = PH_MODULES_DIR . $module . '/i18n/' . $registry['lang'] . '/' . $module . '.ini';
            if ($module != $controller) {
                $i18n_files[] = PH_MODULES_DIR . $module . '/i18n/' . $registry['lang'] . '/' . $module . '_' . $controller . '.ini';
            }
            $registry['translater']->loadFile($i18n_files);
        }

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        require_once($factory);

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        // set flag to get assignments in old and new model - for audit
        $getAssignments_old = $registry->get('getAssignments');
        $registry->set('getAssignments', true, true);

        //get model
        $model = $factory_name::searchOne(
            $registry,
            array(
                'where' => array($alias . '.id = ' . $id),
                'model_lang' => $params['model']->get('model_lang')
            )
        );
        if ($model->modelName == 'Contract' && $model->get('subtype') != 'contract') {
            //get contract for annex
            $model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = ' . $model->get('parent_record')),
                    'model_lang' => $params['model']->get('model_lang')
                )
            );
        }
        $params['model'] = $model;

        $model->unsanitize();

        $old_model = clone $model;
        $old_model->sanitize();

        //flag defining whether the additional variables have been fetched from the DB
        $additional_variables_fetched = false;

        // get id of original user
        $originalUserId = !empty($registry['originalUser']) ? $registry['originalUser']->get('id') : 0;

        // store user info
        $all_ids = array();

        if ($model->modelName == 'Event') {

            $current_assignments = array();
            foreach($model->get('users_participants') as $participant) {
                $current_assignments['participant'][$participant['participant_id']] = $participant;
            }
            foreach($model->get('users_assignments') as $assignment) {
                if (!isset($current_assignments['participant'][$assignment['participant_id']])) {
                    $current_assignments['observer'][$assignment['participant_id']] = $assignment;
                }
            }

            //IMPORTANT: participants are always observer but might be observers with view permissions
            //           observers might not be participants
            $assignments = array();
            foreach(array('participant_view', 'participant_edit', 'observer_view', 'observer_edit') as $at) {
                //assignment type and access
                list($assign_type, $access) = explode('_', $at);

                if (!isset($params['new_assign_' . $at]) && !isset($params['assign_' . $at])) {
                    continue;
                }

                //IMPORTANT: the assign_ settings remove the existing assignments,
                //           while the new_assign_settings are applied to the existing overwriting them!!!
                //           if both are defined the new_assign settings are ignored
                $assign_settings = isset($params['assign_' .  $at]) ? $params['assign_' . $at] : $params['new_assign_' . $at];
                $assign_ids = $this->processAssignUsers($assign_settings, $model, $params['id']);

                //remove empty and duplicates
                $assign_ids = array_filter(array_unique($assign_ids));

                //get only active and not deleted users
                if ($assign_ids) {
                    $query = 'SELECT id' . "\n" .
                             'FROM ' . DB_TABLE_USERS . "\n" .
                             'WHERE id IN (' . implode(', ', $assign_ids) . ') AND active=1 AND deleted_by=0';
                    $assign_ids = $this->registry['db']->GetCol($query);
                }

                //IMPORTANT: the edit permission overwrites the view permissions for a user
                foreach($assign_ids as $user_id) {
                    $assignments[$assign_type][$user_id] = array(
                        'participant_id' => $user_id,
                        'ownership'      => $assign_type == 'participant' ? 'mine' : 'other',
                        'access'         => $access
                    );
                }
                if (isset($params['assign_' .  $at]) && isset($current_assignments[$assign_type])) {
                    //remove the current settings
                    unset($current_assignments[$assign_type]);
                }
            }

            //replace or merge with current assignments
            foreach(array('participant', 'observer') as $at) {
                if (empty($assignments[$at])) {
                    if (isset($current_assignments[$at])) {
                        $assignments[$at] = $current_assignments[$at];
                    }
                } elseif (!empty($current_assignments[$at])) {
                    //merge the assignments with the current ones (if assign_ setting is used the current assignments are reset)
                    foreach ($current_assignments[$at] as $user_id => $ass) {
                        if (!isset($assignments[$at][$user_id])) {
                            $assignments[$at][$user_id] = $ass;
                        }
                    }
                }
            }

            //prepare the final array
            $assignments_final = array();
            foreach(array('participant', 'observer') as $at) {
                if (!empty($assignments[$at])) {
                    foreach($assignments[$at] as $user_id => $ass) {
                        if (!isset($assignments_final[$user_id])) {
                            $assignments_final[$user_id] = $ass;
                        } else {
                            $assignments_final[$user_id]['access'] = $ass['access'];
                        }
                    }
                } elseif ($at == 'participant') {
                    //IMPORTANT: make sure there is at least one participant: the current user
                    $assignments_final[$originalUserId] = array(
                        'participant_id' => $originalUserId,
                        'ownership'      => 'mine',
                        'access'         => 'edit'
                    );

                }
            }
            $model->set('new_users', $assignments_final, true);
        } else {
            $assignment_types = array('owner', 'responsible', 'observer', 'decision');
            foreach ($assignment_types as $at) {
                //prepare assignments
                $current_assignments = ($model->get('assignments_' . $at)) ? array_keys($model->get('assignments_' . $at)) : array();
                if (isset($params['new_assign_' . $at]) || isset($params['assign_' . $at])) {
                    $assign_settings = isset($params['assign_' . $at]) ? $params['assign_' . $at] : $params['new_assign_' . $at];
                    $assign_ids = $this->processAssignUsers($assign_settings, $model, $params['id']);
                    foreach ($assign_ids as $k => $assign_id) {
                        if (empty($assign_id)) {
                            unset($assign_ids[$k]);
                        }
                    }
                    if (isset($params['assign_' . $at])) {
                        // do not merge REPLACE existing
                        $assignment_ids = array_unique($assign_ids);
                    } else {
                        // merge with EXISTING
                        $assignment_ids = array_unique(array_merge($assign_ids, $current_assignments));
                    }
                } else {
                    $assignment_ids = $current_assignments;
                }

                if ($assignment_ids) {
                    $query = 'SELECT id' . "\n" .
                             'FROM ' . DB_TABLE_USERS . "\n" .
                             'WHERE id IN (' . implode(', ', $assignment_ids) . ') AND active=1 AND deleted_by=0';
                    $assignment_ids = $this->registry['db']->GetCol($query);
                }

                $model->set('assignments_' . $at, $assignment_ids, true);
            }
        }

        $send_mail = true;
        if (isset($params['send_mail'])) {
            $send_mail = $params['send_mail'];
        }
        $model->assign($send_mail);

        if ($db->ErrorMsg()) {
            $result = false;
        } else {
            if (!empty($params['auto_confirm_participants']) && $model->modelName == 'Event') {
                $model->confirmAllParticipants();
            }

            $new_model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = ' . $id),
                    'sanitize' => true
                )
            );

            $history_class_name = $factory_name . '_History';
            $history_vars = array(
                'model'         => $model,
                'action_type'   => 'assign',
                'new_model'     => $new_model,
                'old_model'     => $old_model
            );

            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
            $audit_parent = $history_class_name::saveData($registry, $history_vars);

            $result = true;
        }

        // restore flag
        $registry->set('getAssignments', $getAssignments_old, true);

        return $result;
    }

    /**
     * Removes assignments of the specified model e.g. assign of document or task
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function removeAssignments($params) {
        $id         = $params['model_id'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];
        $model      = $params['model'];
        $removed_assignments = array();

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        require_once ($factory);

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        // set flag to get assignments in old and new model - for audit
        $getAssignments_old = $this->registry->get('getAssignments');
        $this->registry->set('getAssignments', true, true);

        //get the requested model from the database
        $old_model = $factory_name::searchOne(
            $this->registry,
            array(
                'where' => array($alias . '.id = ' . $id),
                'sanitize' => true
            )
        );

        $result = false;
        $errors = [];

        $assignmentSearchFilters = [
            'parent_id=' . $id,
        ];
        $dbTable = constant('DB_TABLE_' . strtoupper($factory_name) . '_ASSIGNMENTS');
        if (preg_match('#finance#i', $params['module'])) {
            $dbTable = DB_TABLE_FINANCE_DOCUMENTS_ASSIGNMENTS;
            $assignmentSearchFilters[] = 'model="' . General::plural2singular($factory_name) . '"';
        }

        if ($module == 'events') {
            $assignmentTypes = array(
                'participant' => 'mine',
                'observer' => 'other'
            );
            $participationDBField = 'participant_id';
            $assigmentTypeDBField = 'ownership';
        } else {
            $assignmentTypes = array(
                'owner'       => PH_ASSIGNMENTS_OWNER,
                'responsible' => PH_ASSIGNMENTS_RESPONSIBLE,
                'observer'    => PH_ASSIGNMENTS_OBSERVER,
                'decision'    => PH_ASSIGNMENTS_DECISION
            );
            $participationDBField = 'assigned_to';
            $assigmentTypeDBField = 'assignments_type';
        }

        foreach ($assignmentTypes as $at => $assignTypeValue) {
            $unassignExpression = $params['remove_assign_' . $at];
            if (empty($unassignExpression)) {
                continue;
            }
            $currentAssignmentSearchFilters = $assignmentSearchFilters;
            $currentAssignmentSearchFilters[] = sprintf('`%s`="%s"', $assigmentTypeDBField, $assignTypeValue);

            $sql = 'SELECT ' . $participationDBField . ' FROM ' . $dbTable . "\n" .
                   'WHERE ' . implode(' AND ', $currentAssignmentSearchFilters);
            $currentlyAssignedUsers = $this->registry['db']->GetCol($sql);
            if (preg_match("#\[all\]#", $unassignExpression)) {
                $usersToUnassign = $currentlyAssignedUsers;
            } else {
                $usersToUnassign = $this->processAssignUsers($unassignExpression, $model, $params['id']);
            }

            $usersToUnassign = array_intersect($usersToUnassign, $currentlyAssignedUsers);
            if (empty($usersToUnassign)) {
                continue;
            }
            $currentAssignmentSearchFilters[] = sprintf('`%s` IN (%s)', $participationDBField, implode(',', $usersToUnassign));

            $query = 'DELETE FROM '. $dbTable . "\n" .
                     'WHERE ' . implode(' AND ', $currentAssignmentSearchFilters);
            $this->registry['db']->Execute($query);
            $removed_assignments = array_merge($removed_assignments, $usersToUnassign);

            if ($this->registry['db']->ErrorMsg()) {
                // process warning message
                $errors[] = $this->i18n('warning_auto_remove_assignments_' . $at . '_failed');
            } else {
                $result = true;
            }
        }

        if ($result) {
            $new_model = $factory_name::searchOne(
                $this->registry,
                array(
                    'where' => array($alias . '.id = ' . $id),
                    'sanitize' => true
                )
            );

            $history_class_name = $factory_name . '_History';
            $history_vars = array(
                'model'                 => $model,
                'action_type'           => (in_array($module, array('documents', 'contracts', 'tasks')) ? 'remove_assignments' : 'assign'),
                'new_model'             => $new_model,
                'old_model'             => $old_model,
                'removed_assignments'   => $removed_assignments
            );

            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
            $audit_parent = $history_class_name::saveData($this->registry, $history_vars);
        }

        if (!empty($errors)) {
            foreach ($errors as $err) {
                $this->registry['messages']->setWarning($err);
            }
            $this->registry['messages']->insertInSession($this->registry);
        }

        // restore flag
        $this->registry->set('getAssignments', $getAssignments_old, true);

        return $result;
    }

    /**
     * Changes the status of the specified model e.g. document or task
     *
     * @param array $params - arguments for the method, containing status or substatus and registry
     * @return bool - result of the execution of the method
     */
    public function status($params) {
        // prepare some basics
        $registry   = $this->registry;
        $db         = $registry['db'];
        $id         = $params['model_id'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];
        $db_table   = constant('DB_TABLE_' . strtoupper($module . ($module != $controller ? '_' . $controller : '')));
        $model      = $params['model'];

        // make parameter required in order to continue automation
        // so that it is not checked everywhere whether it is set or not
        if (empty($params['new_status'])) {
            return false;
        }

        // cases when not to perform status change
        if ($module == 'contracts' && $model->get('subtype') != 'contract' ||
        $module == 'finance' && $controller == 'warehouses_documents' ||
        $module == 'events' && $model->get('type_keyword') == 'plannedtime') {
            return false;
        }

        // prepare a flag to check if a notification mail should be sent
        $send_mail = true;
        if (isset($params['send_mail'])) {
            $send_mail = $params['send_mail'];
        }
        // no notification sent on status change of financial documents
        // and events as there is no template
        if ($module == 'finance' || $module == 'events') {
            $send_mail = false;
        }

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        require_once ($factory);

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        if (!$this->registry->get('crontab_automations')) {
            // make sure model is fetched from db - without additional vars and without GT2
            $filters = array(
                'where' => array($alias . '.id = ' . $id),
                'sanitize' => true
            );
            if ($module == 'events') {
                $filters['where'][] = '(ea.access IS NULL OR ea.access IS NOT NULL)';
            }
            $model = $factory_name::searchOne($registry, $filters);
        }

        // get old model before making any modifications to model object
        $old_model = clone $model;
        // we need unsanitized model
        $model->unsanitize();

        //get module-specific i18n files for crontab automations
        if ($this->registry->get('crontab_automations')) {
            $i18n_files = array();
            $i18n_files[] = PH_MODULES_DIR . $module . '/i18n/' . $registry['lang'] . '/' . $module . '.ini';
            if ($module != $controller) {
                $i18n_files[] = PH_MODULES_DIR . $module . '/i18n/' . $registry['lang'] . '/' . $module . '_' . $controller . '.ini';
            }
            $registry['translater']->loadFile($i18n_files);
        }

        $has_unfinished_related_records = false;
        if ($module == 'projects' && $params['new_status'] == 'finished' &&
            !$model->checkCompletedRelatedRecords()) {
            $has_unfinished_related_records = true;
        } elseif (preg_match('#^(documents|tasks|contracts)$#', $module) &&
            preg_match('#^(finished|closed)$#', $params['new_status']) &&
            $model->get('requires_completed_minitasks') && $model->hasUncompletedMinitasks()) {
            $has_unfinished_related_records = true;
            $url = sprintf('%s/index.php?%s=%s&%s=communications&communications=%d&communication_type=minitasks',
                           $this->registry['config']->getParam('crontab', 'base_host'),
                           $this->registry['module_param'], $module, $module, $model->get('id'));
            $this->registry['messages']->setError(sprintf('<a href="%s">%s</a>',
                $url, $this->i18n('error_' . $module . '_uncompleted_minitasks')));
        }

        // check for non-finished planned time events
        if ($module == 'tasks' && $params['new_status'] == 'finished' &&
        $model->getPlannedTime(array('status' => array('planning', 'progress')))) {
            $has_unfinished_related_records = true;
            $url = sprintf('%s/index.php?%s=%s&%s=allocate&allocate=%d',
                           $this->registry['config']->getParam('crontab', 'base_host'),
                           $this->registry['module_param'], $module, $module, $model->get('id'));
            $this->registry['messages']->setError(sprintf('<a href="%s">%s</a>',
                $url, $this->i18n('error_invalid_status_plannedtime')));
        }

        if ($has_unfinished_related_records) {
            if (!$this->registry->get('crontab_automations')) {
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                foreach ($this->registry['messages']->getErrors() as $err) {
                    $this->executionErrors[] = $err;
                }
                $this->registry['messages']->flush();
            }
            return false;
        }

        if ($module == 'finance' && $controller == 'expenses_reasons') {
            $flag_error = false;

            if (in_array($params['new_status'], array('locked', 'finished')) &&
                in_array($model->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE,
                                                    PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE,
                                                    PH_FINANCE_TYPE_EXPENSES_INVOICE)) &&
                $model->get('accounting_period') && $model->get('accounting_period') != '0000-00-00') {

                // validate accounting period
                $accounting_period = General::strftime('%Y-%m', strtotime($model->get('accounting_period')));

                //get the day after which we cannot distribute the document for previous month
                $deny_num = $this->registry['config']->getParam('finance', 'deny_previous_month_after');
                //make the period we issue document for
                $issue_period = General::strftime('%Y-%m', strtotime($model->get('issue_date')));

                if ($accounting_period < $issue_period) {
                    $this->registry['messages']->setError($this->i18n('error_accounting_period_before_issue_date'),
                                                          'accounting_period');
                    $flag_error = true;
                }
                $allowed_period = array();
                if (!empty($deny_num) && $deny_num < General::strftime('%d', time())) {
                    //make the allowed period
                    $allowed_period['min'] = $allowed_period['max'] = General::strftime('%Y-%m', time());
                } else {
                    //make the allowed period
                    $allowed_period['max'] = General::strftime('%Y-%m', time());
                    if (!empty($deny_num)) {
                        $allowed_period['min'] = General::strftime('%Y-%m', strtotime('-1 month', strtotime($allowed_period['max'] . '-01')));
                    } else {
                        $months_back = 1;
                        $allowed_period['min'] = General::strftime('%Y-%m', strtotime('-' . $months_back . ' month', strtotime($allowed_period['max'] . '-01')));
                    }
                }
                if ($accounting_period < $allowed_period['min'] || $accounting_period > $allowed_period['max']) {
                    $this->registry['messages']->setError($this->i18n('error_accounting_period_out_of_allowed',
                                                                      array($model->getLayoutName('accounting_period'))),
                                                          'accounting_period');
                    $flag_error = true;
                }
            }

            if ($flag_error) {
                if (!$this->registry->get('crontab_automations')) {
                    $this->registry['messages']->insertInSession($this->registry);
                } else {
                    foreach ($this->registry['messages']->getErrors() as $err) {
                        $this->executionErrors[] = $err;
                    }
                    $this->registry['messages']->flush();
                }
                return false;
            }
        }

        $set = array();
        $set['status']              = sprintf("`status`='%s'", $params['new_status']);
        // if model has substatus property
        if ($module == 'projects') {
            if ($params['new_status'] == 'finished') {
                // finishing project - success or failed (by default set as success if no parameter specified)
                $set['finished']    = sprintf("`finished`=%d", (!isset($params['new_substatus']) || $params['new_substatus'] ? 1 : 0));
            }
        } elseif (!($module == 'finance' && in_array($controller, array('payments', 'transfers')))) {
            // if there is no set substatus the substatus is set to 0
            $set['substatus']       = sprintf("`substatus`=%d", (isset($params['new_substatus']) ? $params['new_substatus'] : 0));
        }
        $set['status_modified']     = sprintf("`status_modified`=now()");
        $set['status_modified_by']  = sprintf("`status_modified_by`='%s'", $this->registry['currentUser']->get('id'));
        $set['modified']            = sprintf("`modified`=now()");
        $set['modified_by']         = sprintf("`modified_by`='%s'", $this->registry['currentUser']->get('id'));

        if ($module == 'tasks') {
            //check task status
            $new_status = $params['new_status'];
            if ($new_status == 'progress' && !$model->get('started_by')) {
                $set['started_by']  = sprintf("started_by=%d", $this->registry['currentUser']->get('id'));
                $set['start_date']  = "start_date=now()";
            } elseif ($new_status == 'finished' && !$model->get('finish_by')) {
                $set['finish_by']   = sprintf("finish_by=%d", $this->registry['currentUser']->get('id'));
                $set['finish_date'] = "finish_date=now()";
            }
        }

        $db->StartTrans();

        // when financial documents are finished, they receive number
        // and specific fields are updated/set
        if ($module == 'finance' && $params['new_status'] == 'finished' && $model->get('status') != 'finished') {

            if ($controller == 'incomes_reasons') {
                // set user who last modifed record as 'issue_by' and set their 'invoice_code'
                $set['issue_by'] = sprintf("issue_by=%d", ($model->get('issue_by') ?: $model->get('modified_by')));

                if (in_array($model->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                                        PH_FINANCE_TYPE_PRO_INVOICE,
                                                        PH_FINANCE_TYPE_CREDIT_NOTICE,
                                                        PH_FINANCE_TYPE_DEBIT_NOTICE))) {
                    $invoice_code = $model->get('invoice_code');
                    if (!$invoice_code) {
                        $query = 'SELECT invoice_code FROM ' . DB_TABLE_USERS . ' WHERE id=\'' . $model->get('modified_by') . '\'';
                        $invoice_code = $db->GetOne($query);
                    }
                    $set['invoice_code'] = sprintf("invoice_code='%s'", $invoice_code);
                }
                if (!$model->get('issue_date')) {
                    $set['issue_date'] = "`issue_date` = NOW()";
                    $model->set('issue_date', General::strftime('%Y-%m-%d'), true);
                }

                if ($model->get('type') != PH_FINANCE_TYPE_PRO_INVOICE) {
                    if ($model->isDefined('fiscal_event_date_count')) {
                        if ($model->get('fiscal_event_date_count')) {
                            if ($model->get('periods_' . $model->get('fiscal_event_date_point'))) {
                                $date_start = $model->get('periods_' . $model->get('fiscal_event_date_point'));
                            } else {
                                $date_start = General::strftime('%Y-%m-%d');
                            }
                            if ($model->get('fiscal_event_date_period_type') == 'working') {
                                require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                                $fiscal_event_date = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date_start,
                                        $model->get('fiscal_event_date_count'), $model->get('fiscal_event_date_direction'));
                            } elseif ($model->get('fiscal_event_date_direction') == 'before') {
                                $fiscal_event_date = General::strftime('%Y-%m-%d',
                                    strtotime('-' . $model->get('fiscal_event_date_count') . ' day', strtotime($date_start)));
                            } else {
                                $fiscal_event_date = General::strftime('%Y-%m-%d',
                                    strtotime('+' . $model->get('fiscal_event_date_count') . ' day', strtotime($date_start)));
                            }
                            $set['fiscal_event_date'] = sprintf('`fiscal_event_date`= "%s"', $fiscal_event_date);
                        } else {
                            $set['fiscal_event_date'] = sprintf('`fiscal_event_date`= NOW()');
                        }
                    }
                }
                if (!isset($set['fiscal_event_date']) && !empty($set['issue_date'])) {
                    $set['fiscal_event_date'] = preg_replace('#issue_date#', 'fiscal_event_date', $set['issue_date']);
                }
                if ($model->isDefined('date_of_payment_count')) {
                    if ($model->get('date_of_payment_count') !== '') {
                        if ($model->get('date_of_payment_point') == 'issue') {
                            if ($model->get('periods_end')) {
                                $date_start = $model->get('periods_end');
                            } else {
                                $date_start = General::strftime('%Y-%m-%d');
                            }
                            if ($model->get('date_of_payment_period_type') == 'working') {
                                require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                                $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date_start,
                                    $model->get('date_of_payment_count'), 'after');
                            } else {
                                $date_of_payment = General::strftime('%Y-%m-%d',
                                    strtotime('+' . $model->get('date_of_payment_count') . ' day', $date_start));
                            }
                        } else {
                            $date_of_payment = array('count := ' . $model->get('date_of_payment_count'),
                                                     'period_type := ' . $model->get('date_of_payment_period_type'),
                                                     'period := day',
                                                     'direction := after',
                                                     'point := receive');
                            $date_of_payment = implode('\n', $date_of_payment);
                        }
                        $model->set('date_of_payment', $date_of_payment, true);
                    } else {
                        $model->set('date_of_payment', General::strftime('%Y-%m-%d'), true);
                    }
                    $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $model->get('date_of_payment'));
                }

                // set zero-amount finished documents as paid but only if
                // they are not annulled, inactive or correction documents
                if ($model->get('total') == 0 && !$model->get('annulled_by') && $model->isActivated() &&
                $model->get('type') != PH_FINANCE_TYPE_CORRECT_REASON) {
                    $set['payment_status'] = sprintf('payment_status="paid"');
                    $set['payment_status_modified'] = sprintf("payment_status_modified=now()");
                    $model->set('payment_status', 'paid', true);
                }

                //set fiscal_total and fiscal_total_vat
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $currency = $this->registry['config']->getParam('finance', 'fiscal_currency');
                if (empty($currency)) {
                    $currency = Finance_Currencies::getMain($this->registry);
                }
                $rate = Finance_Currencies::getRate($this->registry, $model->get('currency'), $currency);
                $fiscal_total = $rate * $model->get('total');
                $fiscal_total_vat = $rate * $model->get('total_vat');
                $set['fiscal_total'] = sprintf('`fiscal_total`= "%f"', $fiscal_total);
                $set['fiscal_total_vat'] = sprintf('`fiscal_total_vat`= "%f"', $fiscal_total_vat);

            } elseif ($controller == 'expenses_reasons') {
                if (!in_array($model->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE,
                                                         PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                    //update nomenclatures prices
                    $model->updateNomPrices();
                }
                if ((!$model->get('fiscal_event_date') || $model->get('fiscal_event_date') == '0000-00-00')
                  && in_array($model->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE,
                                                         PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE,
                                                         PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                    $set['fiscal_event_date'] = sprintf("fiscal_event_date='%s'", $model->get('issue_date'));
                }
            }

            // common functionality for all financial sub-modules - get num
            if (!$model->get('num')) {
                //set num
                $set['num'] = sprintf("num='%s'", $model->getNum());
                if (!$model->counter) {
                    $this->registry['messages']->setError($this->i18n('error_finance_' . $controller . '_no_counter',
                                                          array($model->get('type_name'), $model->get('company_name'), $model->get('office_name'))));
                    $db->FailTrans();
                } else {
                    if (is_object($model->counter)) {
                        $model->counter->increment();
                    } else {
                        // this happens when issue date is invalid
                        $db->FailTrans();
                    }
                }
            }

            if ($controller == 'incomes_reasons' && !empty($model->counter) && is_object($model->counter)) {
                $set['counter'] = sprintf("counter='%s'", $model->counter->get('id'));
            }

        } elseif ($module == 'contracts' && $params['new_status'] == 'closed' && $model->get('status') != 'closed') {
            if (!$model->get('num') || $model->get('num') == 'system') {
                //set num
                if ($model->get('subtype') == 'contract') {
                    $set['num'] = sprintf("num='%s'", $model->getNum());
                    if (!$model->counter) {
                        $this->registry['messages']->setError($this->i18n('error_contracts_no_counter',
                                                              array($model->get('type_name'), $model->get('company_name'))));
                        $db->FailTrans();
                    } else {
                        $model->counter->increment();
                    }
                } /* elseif ($model->get('status') != 'closed' && $model->get('subtype_status') == 'waiting') {
                    $set['num'] = sprintf("num='%s'", $model->getSubNum());
                    if ($model->get('date_end_subtype') && $model->get('date_end_subtype') != '0000-00-00' && $model->get('date_end_subtype') <= date('Y-m-d')) {
                        $set['subtype_status'] = 'subtype_status = "executed"';
                    } elseif ($model->get('date_start_subtype') <= date('Y-m-d')) {
                        $set['subtype_status'] = 'subtype_status = "started"';
                    }
                } */
            }
        }

        $query = 'UPDATE ' . $db_table . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE id=' . $id;
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $db->FailTrans();
        } else {
            // specific processing AFTER status change
            if ($module == 'finance' && $controller == 'incomes_reasons' &&
            $model->get('status') != 'finished' && $params['new_status'] == 'finished' &&
            in_array($model->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE))) {
                // update invoice/proforma payments if there are payments for parent reason
                // AFTER status of invoice/proforma has been changed to 'finished'!!!
                $model->updatePaymentsFromParent();
            } elseif ($module == 'contracts' &&
            $model->get('status') != $params['new_status'] && $params['new_status'] == 'closed') {
                if ($model->get('subtype') == 'contract' && !$model->saveOriginal()) {
                    $db->FailTrans();
                }
            }

            $comment = '';
            //check if comment should be added
            if (!empty($params['comment'])) {
                require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                $comment = new Comment($this->registry);

                $comment->set('content', $params['comment'], true);
                $comment->set('subject', $this->i18n($module . '_status_change_comment'), true);
                $comment->set('model', $model->modelName, true);
                $comment->set('model_id', $model->get('id'), true);
                $comment->set('is_portal', (!empty($params['is_portal']) ? '1' : '0'), true);
                $comment->set('skip_send_email', true, true);
                $comment->unsetProperty('id', true);

                if ($comment->save()) {
                    $comment->slashesStrip();
                }
            }

            $filters = array(
                'where' => array($alias . '.id = ' . $id),
            );

            if ($module == 'events') {
                if (in_array($params['new_status'], array('finished', 'unstarted', 'moved'))) {
                    //set pending participant assignments to denied
                    $factory_name::denyPendingParticipations($registry, array($id));
                }

                $filters['where'][] = '(ea.access IS NULL OR ea.access IS NOT NULL)';
            }

            $new_model = $factory_name::searchOne($registry, $filters);

            $history_class_name = $factory_name . '_History';
            $history_vars = array(
                'model'                 => $model,
                'action_type'           => 'status',
                'new_model'             => $new_model,
                'old_model'             => $old_model
            );

            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
            $audit_parent = $history_class_name::saveData($registry, $history_vars);

            if ($comment && $comment->get('id')) {
                $comment->saveHistory($new_model);
            }

            // if a notification mail should be sent
            if ($send_mail) {
                $new_model->set('comment', $comment, true);
                //send notification
                $this->sendStatusNotification($module, $new_model, $audit_parent);
            }
        }

        if (!$this->registry->get('crontab_automations')) {
            $this->registry['messages']->insertInSession($this->registry);
        } else {
            foreach ($this->registry['messages']->getErrors() as $err) {
                $this->executionErrors[] = $err;
            }
            $this->registry['messages']->flush();
        }

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        return $result;
    }

    /**
     * Transforms document using an existing transformation
     *
     * @param array $params - arguments for the method, registry
     * @return bool - result of the execution of the method
     */
    public function transform($params) {
        $registry = $this->registry;
        $db = $registry['db'];
        $request = $registry['request'];
        $id = $params['model_id'];
        $model = $params['model'];
        $modelName = strtolower(General::singular2plural($params['model']->modelName));

        $transform_id = $params['transform'];
        $filters = array('where' => array('t.id = ' . $transform_id,
                                          't.source_type = \'' . $model->get('type') . '\''),
                         'model_lang' => $request->get('model_lang'));
        require_once PH_MODULES_DIR . 'transformations/models/transformations.factory.php';
        $transformation = Transformations::searchOne($registry, $filters);

        $transform = $transformation->getAll();

        $model->set('transform_name', $transform['name'], true);
        $model->set('transform_type', $transform['transform_type'], true);
        $model->set('transform_method', $transform['method'], true);
        require_once PH_MODULES_DIR . $modelName . '/controllers/' . $modelName . '.controller.php';
        $controllerClass = ucfirst($modelName) . '_Controller';
        $doc_controller = new $controllerClass($this->registry);
        $this->registry->set('automation_transform', true, true);
        $result = $doc_controller->{$transform['method']}($model, $transform);
        $this->registry->set('automation_transform', false, true);

        if ($result) {
            return $model->get('transformed_id');
        } else {
            return false;
        }

    }

    /**
     * Generates PDF file for a document
     *
     * @param array $params - arguments for the method, registry
     * @return bool - result of the execution of the method
     */
    public function generate($params) {
        $registry = $this->registry;
        $db = $registry['db'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];

        ignore_user_abort();

        if (!empty($params['get_history_documet_id'])) {
            $id = $this->getHistoryResult($params['model_id'], $params['get_history_documet_id']);
            $filters = array('where' => array ('d.id = ' . $id),
                             'model_lang' => $params['model']->get('model_lang'));
            $document = Documents::searchOne($registry, $filters);
        } else {
            $id = $params['model_id'];
            $document = $params['model'];
            if ($document->isSanitized()) {
                $document->unsanitize();
            }
        }
        $pattern_id = $params['pattern_id'];
        $registry['request']->set('pattern', $pattern_id, true);
        $registry['request']->set('model_lang', $document->get('model_lang'), true);
        $patterns_vars = $document->getPatternsVars();
        $document->extender = new Extender();
        $document->extender->model_lang = $document->get('model_lang');
        $document->extender->module = $this->module;
        foreach ($patterns_vars as $key => $value) {
            $document->extender->add($key, $value);
        }
        $result = $document->generatePDF();

        if ($result) {
            $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

            $alias = $factory_name::getAlias($module, $controller);

            $new_model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = ' . $id),
                    'sanitize' => true
                )
            );

            $history_class_name = $factory_name . '_History';
            $history_vars = array(
                'model'          => $new_model,
                'action_type'    => 'generate',
                'pattern'        => $pattern_id,
                'generated_file' => $result,
            );

            require_once PH_MODULES_DIR . $params['module'] . '/models/' . $params['module'] . '.history.php';
            require_once PH_MODULES_DIR . $params['module'] . '/models/' . $params['module'] . '.audit.php';
            $audit_parent = $history_class_name::saveData($registry, $history_vars);
        }

        return $result;
    }

    /**
     * Adds tags to model
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function tag($params) {

        $registry   = $this->registry;
        $db         = $registry['db'];
        $id         = $params['model_id'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        require_once ($factory);

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        // get model
        $model = $factory_name::searchOne(
            $registry,
            array(
                'where' => array($alias . '.id = \'' . $id . '\''),
                'model_lang' => $params['model']->get('model_lang')
            )
        );

        $params['model'] = $model;

        $model->old_model = clone $model;
        $model->old_model->getModelTagsForAudit();
        $model->old_model->sanitize();

        // Get the new_tags from the automation settings
        $tags = !empty($params['new_tags']) ? $params['new_tags'] : array();

        // If the tags have some sub-variables (example: [a_my_tag])
        $sub_vars = $this->extractVariables($tags);
        // Get the values of the sub-variables
        foreach ($sub_vars as $sub_var) {
            // If there is a model and this is not a before_action automation
            if ($model && empty($this->before_action)) {
                // Get the var value from the model
                if (preg_match('#^a_.*$#', $sub_var)) {
                    // Additional vars have prefix a_
                    $value_source_method = 'getPlainVarValue';
                    $sub_var_name = preg_replace('#^a_#', '', $sub_var);
                } else {
                    // Basic vars have prefix b_
                    $value_source_method = 'get';
                    $sub_var_name = preg_replace('#^b_#', '', $sub_var);
                }
                // Get the sub-variable value from the new model
                $sub_var_value = $model->$value_source_method($sub_var_name);
            } else {
                // Get the sub-variable value from the request
                $sub_var_name = preg_replace('#^(a|b)_#', '', $sub_var);
                $sub_var_value = $registry['request']->get($sub_var_name);
                if (is_array($sub_var_value)) {
                    $sub_var_value = array_shift($sub_var_value);
                }
            }

            // escape both single and double quotes in replacement values
            // because we cannot know what to expect in expression
            $sub_var_value = General::slashesEscape($sub_var_value);

            // Replace the sub-variables with their values into the variable value
            $tags = str_replace('[' . $sub_var . ']', $sub_var_value, $tags);
        }

        // data for origin of evaluated settings - used for error logging
        $origin = array(
            'table' => DB_TABLE_AUTOMATIONS,
            'field' => 'method',
            'id' => $params['id'],
            'model' => $params['model']->modelName,
            'model_id' => $params['model']->get('id'),
        );

        // If the variable value should be eval-ed
        $tags = $this->getVariableValue(
            $tags,
            array(
                'orig_str' => $params['new_tags'],
                'origin' => $origin,
            )
        );

        if (!is_array($tags)) {
            //convert to array
            $tags = preg_split('#\s*,\s*#', $tags);
        }

        $model->addTags($tags);

        if ($db->ErrorMsg()) {
            return false;
        } else {
            $new_model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = \'' . $id . '\'')
                )
            );
            $new_model->getModelTagsForAudit();
            $new_model->sendTagNotification($model->old_model->get('tag_names_for_audit'));
            $new_model->sanitize();

            $history_class_name = $factory_name . '_History';
            $history_vars = array(
                'model'         => $model,
                'action_type'   => 'tag',
                'new_model'     => $new_model,
                'old_model'     => $model->old_model
            );

            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
            $audit_parent = $history_class_name::saveData($registry, $history_vars);

            return true;
        }
    }

    /**
     * Removes tags from model
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function removeTags($params) {

        $registry   = $this->registry;
        $db         = $registry['db'];
        $id         = $params['model_id'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        require_once ($factory);

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        // get model
        $model = $factory_name::searchOne(
            $registry,
            array(
                'where' => array($alias . '.id = \'' . $id . '\''),
                'model_lang' => $params['model']->get('model_lang')
            )
        );

        $params['model'] = $model;

        $model->old_model = clone $model;
        $model->old_model->getModelTagsForAudit();
        $model->old_model->sanitize();

        // Prepare a list of tags to be deleted
        $tags = !empty($params['tags']) ? $params['tags'] : array();

        // If the tags have some sub-variables (example: [a_my_tag])
        $sub_vars = $this->extractVariables($tags);
        // Get the values of the sub-variables
        foreach ($sub_vars as $sub_var) {
            // If there is a model and this is not a before_action automation
            if ($model && empty($this->before_action)) {
                // Get the var value from the model
                if (preg_match('#^a_.*$#', $sub_var)) {
                    // Additional vars have prefix a_
                    $value_source_method = 'getPlainVarValue';
                    $sub_var_name = preg_replace('#^a_#', '', $sub_var);
                } else {
                    // Basic vars have prefix b_
                    $value_source_method = 'get';
                    $sub_var_name = preg_replace('#^b_#', '', $sub_var);
                }
                // Get the sub-variable value from the new model
                $sub_var_value = $model->$value_source_method($sub_var_name);
            } else {
                // Get the sub-variable value from the request
                $sub_var_name = preg_replace('#^(a|b)_#', '', $sub_var);
                $sub_var_value = $registry['request']->get($sub_var_name);
                if (is_array($sub_var_value)) {
                    $sub_var_value = array_shift($sub_var_value);
                }
            }

            // escape both single and double quotes in replacement values
            // because we cannot know what to expect in expression
            $sub_var_value = General::slashesEscape($sub_var_value);

            // Replace the sub-variables with their values into the variable value
            $tags = str_replace('[' . $sub_var . ']', $sub_var_value, $tags);
        }

        // data for origin of evaluated settings - used for error logging
        $origin = array(
            'table' => DB_TABLE_AUTOMATIONS,
            'field' => 'method',
            'id' => $params['id'],
            'model' => $params['model']->modelName,
            'model_id' => $params['model']->get('id'),
        );

        // If the variable value should be eval-ed
        $tags = $this->getVariableValue(
            $tags,
            array(
                'orig_str' => $params['tags'],
                'origin' => $origin,
            )
        );


        if (!is_array($tags)) {
            //convert to array
            $tags = preg_split('#\s*,\s*#', $tags);
        }

        // Delete tags for the current model
        $model->deleteTags($tags);

        if ($db->ErrorMsg()) {
            return false;
        } else {
            $new_model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = \'' . $id . '\'')
                )
            );
            $new_model->getModelTagsForAudit();
            $new_model->sendTagNotification($model->old_model->get('tag_names_for_audit'));
            $new_model->sanitize();

            $history_class_name = $factory_name . '_History';
            $history_vars = array(
                'model'         => $new_model,
                'action_type'   => 'tag',
                'new_model'     => $new_model,
                'old_model'     => $model->old_model
            );

            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
            $audit_parent = $history_class_name::saveData($registry, $history_vars);

            return true;
        }
    }

    /**
     * Creates a reminder for certain document, task or project
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function reminder($params) {

        $registry = $this->registry;
        $db = $registry['db'];
        $id = $params['model_id'];
        $module     = $params['module'];
        $controller = !empty($params['controller']) ? $params['controller'] : $params['module'];

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        require_once ($factory);

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        // get model
        $model = $factory_name::searchOne(
            $registry,
            array(
                'where' => array($alias . '.id = \'' . $id . '\''),
                'model_lang' => $params['model']->get('model_lang')
            )
        );

        $params['model'] = $model;

        // gets reminder for model if such already exists
        $reminder = $this->registry['originalUser']->getReminderModel(
            array(
                'model_name' => strtolower($model->modelName),
                'model_id'   => $model->get('id')
            )
        );

        $saved_reminder = false;
        $reminder_date = General::strftime($this->i18n('date_iso'), strtotime(@$params['reminder_date']));

        require_once PH_MODULES_DIR . 'events/models/events.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';

        //get reminder event type
        //get the event type reminder
        $event_type = Events_Types::searchOne(
            $registry,
            array(
                'where' => array('et.keyword = \'reminder\''),
                'sanitize' => true
            )
        );

        if ($event_type) {
            $duration = $event_type->getDefaultDuration();

            if ($reminder && $reminder['reminder_event_id']) {
                //edit existing event
                $event = Events::searchOne(
                    $registry,
                    array(
                        'sanitize' => false,
                        'participant_id' => $this->registry['originalUser']->get('id'),
                        'where' => array('e.id = ' . $reminder['reminder_event_id'])
                    )
                );
                $name_label = $this->i18n($module . '_reminder_event_name');
                $name = $name_label ? sprintf($name_label, $model->get('name')) : $model->get('name');
                $event->set('name', $name, true);
                $event->set('description', (!empty($params['reminder_custom_message']) ? $params['reminder_custom_message'] : ''), true);
                $event->set('event_start', $reminder_date, true);
                $event->set('duration', $duration, true);
                $event->set('event_end', date('Y-m-d H:i:00', strtotime($reminder_date) + 60*$duration), true);
                if ($event->save()) {
                    $saved_reminder = true;
                }
            } else {
                //load the necessary i18n files
                $i18n_file = strtolower($module) . '/i18n/' . $registry['lang'] . '/' . $module . '.ini';
                $this->loadI18NFiles($i18n_file);

                //create new event
                $event = Events::buildModel($this->registry);
                $event->set('id', null, true);
                $event->set('name', sprintf($this->i18n($module . '_reminder_event_name'), $model->get('name')), true);
                $event->set('description', (!empty($params['reminder_custom_message']) ? $params['reminder_custom_message'] : ''), true);
                $event->set('event_start', $reminder_date, true);
                $event->set('duration', $duration, true);
                $event->set('event_end', date('Y-m-d H:i:00', strtotime($reminder_date) + 60*$duration), true);
                $event->set('type', $event_type->get('id'), true);
                $event->set('availability', 'available', true);

                switch (strtolower($model->modelName)) {
                case 'project':
                    $event->set('customer', $model->get('customer'), true);
                    $event->set('trademark', $model->get('trademark'), true);
                    $event->set('project', $model->get('id'), true);
                    break;
                case 'document':
                case 'task':
                default:
                    $event->set('customer', $model->get('customer'), true);
                    $event->set('branch', $model->get('branch'), true);
                    $event->set('contact_person', $model->get('contact_person'), true);
                    $event->set('trademark', $model->get('trademark'), true);
                    $event->set('project', $model->get('project'), true);
                }

                if ($event->save()) {
                    $record['link_to'] = $model->get('id');
                    $record['origin'] = strtolower($model->modelName);
                    $record['link_type'] = 'parent';
                    $event->updateRelatives($record);
                }
            }
        }
        if (!empty($event) && $event->get('id')) {
            //save reminder
            $this->registry['request']->set('reminder_type', (!empty($params['reminder_type'])) ?  $params['reminder_type'] : 'both', true);
            $this->registry['request']->set('reminder_user_id', $registry['originalUser']->get('id'), true);
            $this->registry['request']->set('reminder_date', $reminder_date, true);
            $this->registry['request']->set('custom_message', (!empty($params['reminder_custom_message'])) ?  $params['reminder_custom_message'] : '', true);
            $this->registry['request']->set('selected_panel', 'date', true);

            if ($event->remind()) {
                $saved_reminder = true;
            }
        }

        return $saved_reminder;
    }

    /**
     * Updates basic model variables (stored in main or i18n tables of module)
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function setBasicVar($params) {
        // Prepare some basics
        $registry     = &$this->registry;
        $messages     = &$registry['messages'];
        $after_action = General::parseSettings($params['after_action']);

        // If the automation is action or it's before_action but there is no cancel_action_on_fail
        if (empty($this->before_action) || empty($after_action['cancel_action_on_fail'])) {
            // Show warnings
            $msg_method = 'setWarning';
        } else {
            // Show errors
            $msg_method = 'setError';
        }

        // If there are no vars to set, then there is an error into the automation settings
        if (empty($params['var_name'])) {
            // Set error
            $this->loadI18NFiles();
            $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            $messages->$msg_method($this->i18n('automations_setbasicvar_no_vars'));
            $messages->insertInSession($registry);
            return false;
        }

        // Get the delimiter used for separating vars
        $delimiter = (isset($params['delimiter']) && $params['delimiter'] !== '' ? preg_quote($params['delimiter'], '/') : ';');

        // Get the variables names and values
        $var_names  = preg_split('/\s*' . $delimiter . '\s*/', $params['var_name']);
        $var_values = preg_split('/\s*' . $delimiter . '\s*/', $params['var_value']);

        // If vars dont match values
        if (count($var_names) != count($var_values)) {
            // Set error
            $this->loadI18NFiles();
            $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            $messages->$msg_method($this->i18n('automations_setbasicvar_vars_values_not_match'));
            $messages->insertInSession($registry);
            return false;
        }

        // Prepare some more basics
        $db          = &$registry['db'];
        $model       = '';
        $model_lang  = ($params['model']->get('model_lang') ?: $registry['lang']);

        // Use specific language for the model
        if (isset($params['model_lang'])) {
            // TODO: This is set incorrectly, it should be: $original_model_lang = $model_lang;
            // but the entire idea of having the model with original lang is useless
            // because there's no use to return it back to $params['model']
            // The next automation (executed after the current), will use the original instance,
            // no matter if we change $params['model']. You can try this by setting another automation
            // to be executed after the current and you will see, that the second automation has it's model_lang
            // no matter if we change $params['model']. It would matter if we only change the for example: $params['model']->set('model_lang', ...)
            // but that's not the case. So we could remove all this $original_model_lang, but currently there's no practical problem,
            // so I didn't removed it, to save time from this useless change.
            $original_model_lang = $params['model_lang'];
            $model_lang = $params['model_lang'];
        }

        // Use specific language for the interface
        if (isset($params['lang'])) {
            $original_lang = $registry['lang'];
            $registry->set('lang', $params['lang'], true);
        }

        // Get the module and the controller of the current model
        $module     = $params['module'];
        $controller = $params['controller'] ? $params['controller'] : $params['module'];

        // Build the names of the main and the i18n table
        $db_table      = constant('DB_TABLE_' . strtoupper($module . ($module != $controller ? '_' . $controller : '')));
        $db_table_i18n = constant('DB_TABLE_' . strtoupper($module . ($module != $controller ? '_' . $controller : '')) . '_I18N');

        // Get the tables columns names
        $db_table_columns      = $db->MetaColumnNames($db_table,      true);
        $db_table_i18n_columns = $db->MetaColumnNames($db_table_i18n, true);

        // If there is a model_id
        if (!empty($params['model_id'])) {
            // Get: id, module, controller
            $id = $params['model_id'];

            // Prepare the factory name
            $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

            // Include the factory
            $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
            require_once ($factory);

            // Prepare alias
            $alias = $factory_name::getAlias($module, $controller);

            // Get the new model from database, because model from $params could be built from request (i.e. incomplete)
            $model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = ' . $id),
                    'model_lang' => $model_lang
                )
            );

            // If there is a model
            if ($model) {
                // If we're using a specific language for the model
                if (isset($original_model_lang)) {
                    // Get the model with the original language, so we can restore it later into the $params array
                    $model_with_original_lang = $factory_name::searchOne(
                        $registry,
                        array(
                            'where' => array($alias . '.id = ' . $id),
                            'model_lang' => $original_model_lang
                        )
                    );
                    if ($model_with_original_lang) {
                        // Set the old model
                        $model_with_original_lang->unsanitize();
                        $model_with_original_lang->old_model = clone $model_with_original_lang;
                        $model_with_original_lang->old_model->sanitize();
                    } else {
                        $model_with_original_lang = $params['model'];
                    }
                }

                // Reset the current model from the parameters
                $params['model'] = $model;
            } else {
                // Restore the original language of the interface
                if (isset($original_lang)) {
                    $registry->set('lang', $original_lang, true);
                }

                // Exit with error
                $this->loadI18NFiles();
                $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
                $messages->$msg_method($this->i18n('error_technical_error_please_contact_nzoom_support'));
                $messages->insertInSession($registry);
                return false;
            }

            // Set the old model
            $model->unsanitize();
            $model->old_model = clone $model;
            $model->old_model->sanitize();
        }

        // If there is a model (i.e. if the database will be used)
        if ($model) {
            // Start a transaction
            $db->StartTrans();
        }

        // data for origin of evaluated settings - used for error logging
        $origin = array(
            'table' => DB_TABLE_AUTOMATIONS,
            'field' => 'method',
            'id' => $params['id'],
            'model' => $params['model']->modelName,
            'model_id' => $params['model']->get('id'),
        );

        // Go through each var
        foreach ($var_names as $var_key => $var_name) {
            // If the var can't be identified
            if (!in_array($var_name, $db_table_columns) && !in_array($var_name, $db_table_i18n_columns)) {
                // Skip this var
                continue;
            }

            // Prepare the var value
            $var_value = '';

            // If the var value should be null
            if (strtolower($var_values[$var_key]) == 'null') {
                // Set the var value to NULL
                $var_value = 'NULL';
            } else {
                // Get the var value from the automation settings
                $var_value = $var_values[$var_key];

                // If the variable has some sub-variables (example: [b_customer_name])
                $sub_vars = $this->extractVariables($var_value);
                // Get the values of the sub-variables
                foreach ($sub_vars as $sub_var) {
                    // If there is a model and this is not a before_action automation
                    if ($model && empty($this->before_action)) {
                        // Get the var value from the model
                        if (preg_match('#^a_.*$#', $sub_var)) {
                            // Additional vars have prefix a_
                            $value_source_method = 'getPlainVarValue';
                            $sub_var_name = preg_replace('#^a_#', '', $sub_var);
                        } elseif (preg_match('#^al_.*$#', $sub_var)) {
                            // FORMATTED Additional vars have prefix al_ (labels of dropdown options)
                            $value_source_method = 'getVarValue';
                            $sub_var_name = preg_replace('#^al_#', '', $sub_var);
                        } else {
                            // Basic vars have prefix b_
                            $value_source_method = 'get';
                            $sub_var_name = preg_replace('#^b_#', '', $sub_var);
                        }
                        // Get the sub-variable value from the new model
                        $sub_var_value = $model->$value_source_method($sub_var_name);
                    } else {
                        // Get the sub-variable value from the request
                        $sub_var_name = preg_replace('#^(a|al|b)_#', '', $sub_var);
                        $sub_var_value = $registry['request']->get($sub_var_name);
                        if (is_array($sub_var_value)) {
                            $sub_var_value = array_shift($sub_var_value);
                        }
                    }

                    // escape both single and double quotes in replacement values
                    // because we cannot know what to expect in expression
                    $sub_var_value = General::slashesEscape($sub_var_value);

                    // Replace the sub-variables with their values into the variable value
                    $var_value = str_replace('[' . $sub_var . ']', $sub_var_value, $var_value);
                }

                // If the variable value should be eval-ed
                $var_value = $this->getVariableValue(
                    $var_value,
                    array(
                        'orig_str' => $var_values[$var_key],
                        'origin' => $origin,
                        //use the object model to get variables with methods (e.g., getVarValue)
                        strtolower($params['model']->modelName) => $params['model']
                    )
                );

                // remove unnecessary backslashes after evaluation has been performed
                $var_value = General::slashesStrip($var_value);
            }

            // If there is a model and this is not a before_action automation
            if ($model && empty($this->before_action)) {
                // Prepare the var value
                if ($var_value != 'NULL') {
                    $var_value = '\'' . General::slashesEscape($var_value) . '\'';
                }

                // Set the basic var
                if (in_array($var_name, $db_table_columns)) {
                    $query = "
                        UPDATE `{$db_table}`
                          SET `{$var_name}` = {$var_value}
                          WHERE `id` = {$id}";
                    @$db->Execute($query);
                } elseif (in_array($var_name, $db_table_i18n_columns)) {
                    $query = "
                        UPDATE `{$db_table_i18n}`
                          SET `{$var_name}` = {$var_value}
                          WHERE `parent_id` = {$id}
                            AND `lang`      = '{$model_lang}'";
                    @$db->Execute($query);
                } else {
                    // This case should never happen
                    continue;
                }

                // If there is a database error
                $db_err = $db->ErrorMsg();
                if ($db_err) {
                    // Restore the original language of the interface
                    if (isset($original_lang)) {
                        $registry->set('lang', $original_lang, true);
                    }

                    // Exit with error
                    $this->loadI18NFiles();
                    if (isset($params['msg_err_' . $var_name])) {
                        $var_msg_txt = $params['msg_err_' . $var_name];
                    } else {
                        $var_msg_txt = sprintf($this->i18n('automations_setbasicvar_failed_to_set_value_for_var'), $model->getLayoutName($var_name, false));
                    }
                    if (isset($params['msg_key_' . $var_name])) {
                        $var_msg_key = $params['msg_key_' . $var_name];
                    } else {
                        $var_msg_key = $var_name;
                    }
                    // If the var is "code" then find the duplicated values (if any) and make the error message to be a link
                    if ($var_name == 'code' && preg_match('/Duplicate entry .*for key \'code\'/', $db_err)) {
                        if (in_array($var_name, $db_table_columns)) {
                            $db_tbl = $db_table;
                        } elseif (in_array($var_name, $db_table_i18n_columns)) {
                            $db_tbl = $db_table_i18n;
                        } else {
                            // This case should never happen
                            continue;
                        }
                        // TODO: If $db_tbl is an i18n table, then this query will return error, because the ID of the model will be in `parent_id`, not in `id`
                        $query = "
                            SELECT `id`
                              FROM `{$db_tbl}`
                              WHERE `{$var_name}` " . ($var_value == 'NULL' ? 'IS' : '=') . " {$var_value}
                              LIMIT 1";
                        $duplicate_model_id = $db->GetOne($query);
                        $var_msg_txt = sprintf('<a href="%s?%s=%s%s&%s=view&view=%d&model_lang=%s" target="_blank">%s</a>',
                            $_SERVER['PHP_SELF'],
                            $registry['module_param'],
                            $module,
                            ($module == $controller ? '' : '&controller=' . $controller),
                            $controller,
                            $duplicate_model_id,
                            $model_lang,
                            $var_msg_txt);
                        $var_msg_key = '';
                    }
                    $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
                    $messages->$msg_method($var_msg_txt, $var_msg_key);
                    $messages->insertInSession($registry);
                    $db->FailTrans();
                    break;
                }
            } else {
                // keep original value in order to restore on failure
                if (!array_key_exists($var_name, $this->originalRequestValues)) {
                    $this->originalRequestValues[$var_name] = $registry['request']->get($var_name);
                }
                // Use the request to set the basic var
                if ($var_value == 'NULL') {
                    $registry['request']->remove($var_name);
                } else {
                    $registry['request']->set($var_name, $var_value, 'all', true);
                }
            }
        }

        // If there is a model
        if ($model) {
            // Check if the transaction has failed
            $has_failed_trans = $db->HasFailedTrans();

            // Complete the transaction
            $db->CompleteTrans();

            // If the transaction has failed
            if ($has_failed_trans) {
                // Restore the original language of the interface
                if (isset($original_lang)) {
                    $registry->set('lang', $original_lang, true);
                }
                // Restore the model with the original language
                if (isset($model_with_original_lang)) {
                    $params['model'] = $model_with_original_lang;
                }

                // Fail the automation
                return false;
            } else {
                // Write history
                if (empty($this->before_action)) {
                    $filters    = array('where'    => array($alias . '.id = ' . $id),
                                        'sanitize' => true);
                    $new_model  = $factory_name::searchOne($registry, $filters);

                    $history_class_name = $factory_name . '_History';
                    $history_vars       = array('model'       => $new_model,
                                                'action_type' => 'edit',
                                                'new_model'   => $new_model,
                                                'old_model'   => $model->old_model);
                    require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
                    require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
                    $audit_parent = $history_class_name::saveData($registry, $history_vars);
                }
            }
        }

        // Restore the original language of the interface
        if (isset($original_lang)) {
            $registry->set('lang', $original_lang, true);
        }
        // Restore the model with the original language
        if (isset($model_with_original_lang)) {
            $params['model'] = $model_with_original_lang;
        }

        return true;
    }

    /**
     * Updates additional model variables (stored in *_cstm table of module)
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function setAdditionalVar($params) {
        // Prepare some basics
        $registry     = &$this->registry;
        $messages     = &$registry['messages'];
        $after_action = General::parseSettings($params['after_action']);

        // If the automation is action or it's before_action but there is no cancel_action_on_fail
        if (empty($this->before_action) || empty($after_action['cancel_action_on_fail'])) {
            // Show warnings
            $msg_method = 'setWarning';
        } else {
            // Show errors
            $msg_method = 'setError';
        }

        // If there are no vars to set, then there is an error in the automation settings
        if (empty($params['var_name'])) {
            // Set error
            $this->loadI18NFiles();
            $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            $messages->$msg_method($this->i18n('automations_setadditionalvar_no_vars'));
            $messages->insertInSession($registry);
            return false;
        }

        // Get the delimiter used for separating vars
        $delimiter = (isset($params['delimiter']) && $params['delimiter'] !== '' ? preg_quote($params['delimiter'], '/') : ';');

        // Get the variables names and values
        $var_names  = preg_split('/\s*' . $delimiter . '\s*/', $params['var_name']);
        $var_values = preg_split('/\s*' . $delimiter . '\s*/', $params['var_value']);

        // If vars don't match values
        if (count($var_names) != count($var_values)) {
            // Set error
            $this->loadI18NFiles();
            $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            $messages->$msg_method($this->i18n('automations_setadditionalvar_vars_values_not_match'));
            $messages->insertInSession($registry);
            return false;
        }

        // Get the module and the controller of the current model
        $module     = $params['module'];
        $controller = $params['controller'] ? $params['controller'] : $params['module'];

        // Get the cstm table name
        $db_table = 'DB_TABLE_' . strtoupper($module . ($module != $controller ? '_' . $controller : '')) . '_CSTM';
        if (!defined($db_table)) {
            // Set error
            $this->loadI18NFiles();
            $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            $messages->$msg_method($this->i18n('automations_setadditionalvar_no_cstm_table'));
            $messages->insertInSession($registry);
            return false;
        }
        $db_table = constant($db_table);

        // Prepare some more basics
        $db         = &$registry['db'];
        $model      = '';
        $model_lang = ($params['model']->get('model_lang') ?: $registry['lang']);
        $add_vars   = array();

        // Use specific language for the model
        if (isset($params['model_lang'])) {
            // TODO: This is set incorrectly, it should be: $original_model_lang = $model_lang;
            // but the entire idea of having the model with original lang is useless
            // because there's no use to return it back to $params['model']
            // The next automation (executed after the current), will use the original instance,
            // no matter if we change $params['model']. You can try this by setting another automation
            // to be executed after the current and you will see, that the second automation has it's model_lang
            // no matter if we change $params['model']. It would matter if we only change the for example: $params['model']->set('model_lang', ...)
            // but that's not the case. So we could remove all this $original_model_lang, but currently there's no practical problem,
            // so I didn't removed it, to save time from this useless change.
            $original_model_lang = $params['model_lang'];
            $model_lang = $params['model_lang'];
        }

        // Use specific language for the interface
        if (isset($params['lang'])) {
            $original_lang = $registry['lang'];
            $registry->set('lang', $params['lang'], true);
        }

        // If there is a model_id
        if (!empty($params['model_id'])) {
            // Get: id, module, controller
            $id = $params['model_id'];

            // Prepare the factory name
            $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

            // Include the factory
            $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
            require_once ($factory);

            // Prepare alias
            $alias = $factory_name::getAlias($module, $controller);

            // Get the new model from database, because model from $params could be built from request (i.e. incomplete)
            $model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = ' . $id),
                    'model_lang' => $model_lang
                )
            );

            // If there is a model
            if ($model) {
                // If we're using a specific language for the model
                if (isset($original_model_lang)) {
                    // Get the model with the original language, so we can restore it later into the $params array
                    $model_with_original_lang = $factory_name::searchOne(
                        $registry,
                        array(
                            'where' => array($alias . '.id = ' . $id),
                            'model_lang' => $original_model_lang
                        )
                    );
                    if ($model_with_original_lang) {
                        // Set the old model
                        $model_with_original_lang->unsanitize();
                        if (!$model_with_original_lang->isDefined('vars')) {
                            $get_old_vars = $registry->get('get_old_vars');
                            $registry->set('get_old_vars', true, true);
                            $model_with_original_lang->getVars();
                            $registry->set('get_old_vars', $get_old_vars, true);
                        }
                        $model_with_original_lang->old_model = clone $model_with_original_lang;
                        $model_with_original_lang->old_model->sanitize();
                    } else {
                        $model_with_original_lang = $params['model'];
                    }
                }

                // Reset the current model from the parameters
                $params['model'] = $model;

                // Set the old model
                $model->unsanitize();
                if (!$model->isDefined('vars')) {
                    $get_old_vars = $registry->get('get_old_vars');
                    $registry->set('get_old_vars', true, true);
                    $model->getVars();
                    $registry->set('get_old_vars', $get_old_vars, true);
                }
                $model->old_model = clone $model;
                $model->old_model->sanitize();

                // Get variable info
                $model_name = $model->modelName;
                $model_type = $model->get('type');
                if (empty($model_name) || empty($model_type)) {
                    // Restore the original language of the interface
                    if (isset($original_lang)) {
                        $registry->set('lang', $original_lang, true);
                    }
                    // Restore the model with the original language
                    if (isset($model_with_original_lang)) {
                        $params['model'] = $model_with_original_lang;
                    }

                    return true;
                } else {
                    $query = "
                        SELECT `fm`.`name`   AS `name`,
                            `fm`.`id`        AS `id`,
                            `fm`.`multilang` AS `multilang`,
                            `fi`.`content`   AS `label`
                          FROM `" . DB_TABLE_FIELDS_META . "` AS `fm`
                          JOIN `" . DB_TABLE_FIELDS_I18N . "` AS `fi`
                            ON (`fm`.`model`          = '{$model_name}'
                              AND `fm`.`model_type`   = '{$model_type}'
                              AND `fm`.`bb`           = 0
                              AND `fm`.`gt2`          = 0
                              AND `fm`.`name`         IN ('" . implode("', '", $var_names) . "')
                              AND `fi`.`parent_id`    = `fm`.`id`
                              AND `fi`.`content_type` = 'label'
                              AND `fi`.`lang`         = '{$model_lang}')";
                    $add_vars = $db->GetAssoc($query);
                }
            } else {
                // Restore the original language of the interface
                if (isset($original_lang)) {
                    $registry->set('lang', $original_lang, true);
                }

                // Exit with error
                $this->loadI18NFiles();
                $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
                $messages->$msg_method($this->i18n('error_technical_error_please_contact_nzoom_support'));
                $messages->insertInSession($registry);
                return false;
            }
        }

        // If there is no model (then the request will be used) or all vars are found
        if (!$model || count(array_intersect(array_keys($add_vars), $var_names)) == count($var_names)) {
            // If there is a model (i.e. if the database will be used)
            if ($model) {
                // Start a transaction
                $db->StartTrans();
            }

            // data for origin of evaluated settings - used for error logging
            $origin = array(
                'table' => DB_TABLE_AUTOMATIONS,
                'field' => 'method',
                'id' => $params['id'],
                'model' => $params['model']->modelName,
                'model_id' => $params['model']->get('id'),
            );

            // Go through each var
            foreach ($var_names as $var_key => $var_name) {
                // Prepare the var value
                $var_value = '';

                // If the var value should be null
                if (strtolower($var_values[$var_key]) == 'null') {
                    // Set the var value to NULL
                    $var_value = 'NULL';
                } else {
                    // Get the var value from the automation settings
                    $var_value = $var_values[$var_key];

                    // If the variable has some sub-variables (example: [b_customer_name])
                    $sub_vars = $this->extractVariables($var_value);
                    // Get the values of the sub-variables
                    foreach ($sub_vars as $sub_var) {
                        // If there is a model and this is not a before_action automation
                        if ($model && empty($this->before_action)) {
                            // Get the var value from the model
                            if (preg_match('#^a_.*$#', $sub_var)) {
                                // Additional vars have prefix a_
                                $value_source_method = 'getPlainVarValue';
                                $sub_var_name = preg_replace('#^a_#', '', $sub_var);
                            } elseif (preg_match('#^al_.*$#', $sub_var)) {
                                // FORMATTED Additional vars have prefix al_ (labels of dropdown options)
                                $value_source_method = 'getVarValue';
                                $sub_var_name = preg_replace('#^al_#', '', $sub_var);
                            } else {
                                // Basic vars have prefix b_
                                $value_source_method = 'get';
                                $sub_var_name = preg_replace('#^b_#', '', $sub_var);
                            }
                            // Get the sub-variable value from the new model
                            $sub_var_value = $model->$value_source_method($sub_var_name);
                        } else {
                            // Get the sub-variable value from the request
                            $sub_var_name = preg_replace('#^(a|b)_#', '', $sub_var);
                            $sub_var_value = $registry['request']->get($sub_var_name);
                            if (is_array($sub_var_value)) {
                                $sub_var_value = array_shift($sub_var_value);
                            }
                        }

                        // escape both single and double quotes in replacement values
                        // because we cannot know what to expect in expression
                        $sub_var_value = General::slashesEscape($sub_var_value);

                        // Replace the sub-variables with their values into the variable value
                        $var_value = str_replace('[' . $sub_var . ']', $sub_var_value, $var_value);
                    }

                    // If the variable value should be eval-ed
                    $var_value = $this->getVariableValue(
                        $var_value,
                        array(
                            'orig_str' => $var_values[$var_key],
                            'origin' => $origin,
                            //use the object model to get variables with methods (e.g., getVarValue)
                            strtolower($params['model']->modelName) => $params['model']
                        )
                    );

                    // remove unnecessary backslashes after evaluation has been performed
                    $var_value = General::slashesStrip($var_value);
                }

                // If there is a model and this is not a before_action automation
                if ($model && empty($this->before_action)) {
                    // Prepare the var value
                    if ($var_value != 'NULL') {
                        $var_value = '\'' . General::slashesEscape($var_value) . '\'';
                    }

                    // Set the additional var
                    $query = "
                        INSERT INTO `{$db_table}` (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES
                          ({$id}, {$add_vars[$var_name]['id']}, 1, {$var_value}, NOW(), {$registry['currentUser']->get('id')}, NOW(), {$registry['currentUser']->get('id')}, '" . ($add_vars[$var_name]['multilang'] ? $model_lang : '') . "')
                          ON DUPLICATE KEY UPDATE
                            `value`       = VALUES(`value`),
                            `modified`    = VALUES(`modified`),
                            `modified_by` = VALUES(`modified_by`)";
                    @$db->Execute($query);

                    // If there is a database error
                    $db_err = $db->ErrorMsg();
                    if ($db_err) {
                        // Restore the original language of the interface
                        if (isset($original_lang)) {
                            $registry->set('lang', $original_lang, true);
                        }

                        // Exit with error
                        $this->loadI18NFiles();
                        $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
                        if (isset($params['msg_err_' . $var_name])) {
                            $var_msg_txt = $params['msg_err_' . $var_name];
                        } else {
                            $var_msg_txt = sprintf($this->i18n('automations_setadditionalvar_failed_to_set_value_for_var'), $add_vars[$var_name]['label']);
                        }
                        if (isset($params['msg_key_' . $var_name])) {
                            $var_msg_key = $params['msg_key_' . $var_name];
                        } else {
                            $var_msg_key = $var_name;
                        }
                        $messages->$msg_method($var_msg_txt, $var_msg_key);
                        $messages->insertInSession($registry);
                        $db->FailTrans();
                        break;
                    }
                } else {
                    // keep original value in order to restore on failure
                    if (!array_key_exists($var_name, $this->originalRequestValues)) {
                        $this->originalRequestValues[$var_name] = $registry['request']->get($var_name);
                    }
                    // Use the request to set the additional var
                    if ($var_value == 'NULL') {
                        $registry['request']->remove($var_name);
                    } else {
                        $registry['request']->set($var_name, $var_value, 'all', true);
                    }
                }
            }

            // If there is a model
            if ($model) {
                // Check if the transaction has failed
                $has_failed_trans = $db->HasFailedTrans();

                // Complete the transaction
                $db->CompleteTrans();

                // If the transaction has failed
                if ($has_failed_trans) {
                    // Restore the original language of the interface
                    if (isset($original_lang)) {
                        $registry->set('lang', $original_lang, true);
                    }
                    // Restore the model with the original language
                    if (isset($model_with_original_lang)) {
                        $params['model'] = $model_with_original_lang;
                    }

                    // Fail the automation
                    return false;
                } else {
                    // Write history
                    if (empty($this->before_action)) {
                        $filters    = array('where'    => array($alias . '.id = ' . $id),
                                            'sanitize' => true);
                        $new_model  = $factory_name::searchOne($registry, $filters);
                        $registry->set('get_old_vars', true, true);
                        $new_model->getVars();
                        $registry->set('get_old_vars', $get_old_vars, true);

                        $history_class_name = $factory_name . '_History';
                        $history_vars       = array('model'       => $new_model,
                                                    'action_type' => 'edit',
                                                    'new_model'   => $new_model,
                                                    'old_model'   => $model->old_model);
                        require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.history.php';
                        require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.audit.php';
                        $audit_parent = $history_class_name::saveData($registry, $history_vars);
                    }
                }
            }
        } else {
            // Restore the original language of the interface
            if (isset($original_lang)) {
                $registry->set('lang', $original_lang, true);
            }
            // Restore the model with the original language
            if (isset($model_with_original_lang)) {
                $params['model'] = $model_with_original_lang;
            }

            // Set error
            $this->loadI18NFiles();
            $messages->$msg_method(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            $messages->$msg_method($this->i18n('automations_setadditionalvar_some_vars_not_found'));
            $messages->insertInSession($registry);
            return false;
        }

        // Restore the original language of the interface
        if (isset($original_lang)) {
            $registry->set('lang', $original_lang, true);
        }
        // Restore the model with the original language
        if (isset($model_with_original_lang)) {
            $params['model'] = $model_with_original_lang;
        }

        return true;
    }

    /**
     * Attach current trademark (nomenclature) to a customer
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function attachTrademarkToCustomer($params) {
        // Conditions to execute the automation:
        //   if the action is add, aqquick or edit
        //   and there's id and model for the current trademark (nomenclature)
        //   and the additional param customer_id_field is set
        if (in_array($this->registry['action'], array('add', 'addquick', 'edit'))
                && $params['model_id']
                && $params['model']
                && isset($params['customer_id_field'])
                && isset($params['set_as_default'])) {
            // If the action is edit
            if ($this->registry['action'] == 'edit') {
                // Delete the current relations of this trademark to any customer
                $query = 'DELETE FROM `' . DB_TABLE_CUSTOMERS_TRADEMARKS . '`' . "\n" .
                         '  WHERE `trademark_id` = \'' . $params['model_id'] . '\'';
                $this->registry['db']->Execute($query);

                // Check if there is any problem
                if ($this->registry['db']->ErrorMsg()) {
                    return false;
                }
            }

            // Get the customer fields for id
            $source_fields = array_filter(preg_split('/\s*,\s*/', trim($params['customer_id_field'])));
            $customers_list = array();
            foreach ($source_fields as $src) {
                $var_value = $params['model']->getVarValue($src);
                if (!is_array($var_value)) {
                    $customers_list[] = $var_value;
                } else {
                    $customers_list = array_merge($customers_list, $var_value);
                }
            }
            $customers_list = array_filter($customers_list);

            // If there is a customer id
            foreach ($customers_list as $cstm_id) {
                if (!empty($cstm_id)) {
                    if ($params['set_as_default']) {
                        // remove the default trademark if set_as_default option is checked
                        $query_update = 'UPDATE `' . DB_TABLE_CUSTOMERS_TRADEMARKS . '` SET `is_default`=0 WHERE `parent_id`=\'' . $cstm_id . '\'';
                        $this->registry['db']->Execute($query_update);

                        // Check if there's any problem
                        if ($this->registry['db']->ErrorMsg()) {
                            return false;
                        }
                    }

                    // Attach this trademark to that customer
                    $query = 'INSERT INTO `' . DB_TABLE_CUSTOMERS_TRADEMARKS . '` (`parent_id`, `trademark_id`, `is_default`) VALUES ' . "\n" .
                             '  (\'' . $cstm_id . '\', \'' . $params['model_id'] . '\', \'' . $params['set_as_default'] . '\')';
                    $this->registry['db']->Execute($query);

                    // Check if there is any problem
                    if ($this->registry['db']->ErrorMsg()) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * Sends email with custom email template
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function sendMail($params) {
        // Execute this automation only if it's not a before action (i.e. only if it is: action or crontab)
        if (!empty($this->before_action)) {
            return false;
        }

        // Prepare some basics
        $registry = &$this->registry;
        $messages = &$registry['messages'];
        $msg = '';
        $db = &$registry['db'];
        $settings = $this->getSettings($params);
        $this->loadI18NFiles();

        // Get the model
        $model = $params['model'];

        // Unsanitize the model
        $sanitize_after = false;
        if ($model->isSanitized()) {
            $model->unsanitize();
            $sanitize_after = true;
        }

        // Prepare settings
        // TODO:
        // attach_files_from_var := a_fome_files
        // attach_var_record_attached_files := last|b_customer;all|a_some_document
        // attach_var_record_generated_files := last|b_customer;all|a_some_document
        $email_settings = array(
            'assignments_types',
            'assignments_types_cc',
            'assignments_types_bcc',
            'departments',
            'departments_cc',
            'departments_bcc',
            'users',
            'users_cc',
            'users_bcc',
            'customers',
            'customers_cc',
            'customers_bcc',
            'emails',
            'emails_cc',
            'emails_bcc'
        );
        $array_settings = array(
            'attached_files_templates',
            'attach_files'
        );
        $array_settings = array_merge($email_settings, $array_settings);
        // Make custom parsing for some settings
        $array_settings = array_filter(array_intersect_key($settings, array_flip($array_settings)));
        foreach ($array_settings as $key => $value) {
            $settings[$key] = array_filter(preg_split('/\s*,\s*/', trim($value)));
        }
        $settings = array_filter($settings);

        // Check for email settings
        $email_settings = array_intersect_key($settings, array_flip($email_settings));
        if (count($email_settings) < 1) {
            $msg = $this->i18n('automations_sendmail_no_mails_from_settings') ? : false;
        } else if (empty($settings['email_template'])) {
            // Check for email template
            $msg = $this->i18n('automations_sendmail_no_email_template_settings') ? : false;
        } else {
            $query = "
                SELECT subject, body
                  FROM " . DB_TABLE_EMAILS_I18N . "
                  WHERE parent_id = '{$settings['email_template']}'
                  AND lang = '{$registry['lang']}'";
            $email_template = $db->GetRow($query);
            if (empty($email_template)) {
                $msg = $this->i18n('automations_sendmail_no_email_template_db') ? : false;
            }
        }

        if (!empty($msg)) {
            if ($params['automation_type'] == 'crontab') {
                $this->executionErrors[] = $msg;
            } else {
                $messages->setError($msg);
                $messages->insertInSession($registry);
            }
            return false;
        }

        // Prepare to collect mails
        $emails = array(
            'to'  => array(),
            'cc'  => array(),
            'bcc' => array()
        );
        $users = $emails;
        $recipients_types = array();
        foreach ($emails as $recipient_type => $v) {
            $recipients_types[$recipient_type] = ($recipient_type == 'to' ? '' : '_' . $recipient_type);
        }
        foreach ($recipients_types as $recipient_type => $recipient_type_suffix) {
            // Collect users from assignments
            if (!empty($settings['assignments_types' . $recipient_type_suffix])) {
                foreach ($settings['assignments_types' . $recipient_type_suffix] as $assignments_types) {
                    $assignments_types = $this->_getValue($model, $assignments_types);
                    foreach ($assignments_types as $assignment_type) {
                        $users[$recipient_type] = array_merge($users[$recipient_type], array_keys($model->getAssignments($assignment_type)));
                    }
                }
            }
            // Collect users from departments
            if (!empty($settings['departments' . $recipient_type_suffix])) {
                include_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
                $departments = array();
                foreach ($settings['departments' . $recipient_type_suffix] as $ds) {
                    $departments = array_merge($departments, $this->_getValue($model, $ds));
                    $users[$recipient_type] = array_merge($users[$recipient_type], Departments::getUsersIds($registry, array('where' => array("d.id IN ('" . implode("', '", $departments) . "')"))));
                }
            }
            // Collect users
            if (!empty($settings['users' . $recipient_type_suffix])) {
                foreach ($settings['users' . $recipient_type_suffix] as $us) {
                    $us = $this->_getValue($model, $us);
                    $users[$recipient_type] = array_merge($users[$recipient_type], $us);
                }
            }

            // Collect mails from users
            if (!empty($users[$recipient_type])) {
                $query = "
                    SELECT CONCAT(TRIM(CONCAT(ui.firstname, ' ', ui.lastname)), ' <', email, '>')
                      FROM " . DB_TABLE_USERS . " AS u
                      JOIN " . DB_TABLE_USERS_I18N . " AS ui
                      ON (ui.parent_id = u.id
                      AND ui.lang = '{$registry['lang']}')
                      WHERE deleted_by = 0
                      AND active = 1
                      AND id IN ('" . implode("', '", $users[$recipient_type]) . "')";
                $emails[$recipient_type] = array_merge($emails[$recipient_type], $db->GetCol($query));
            }

            // Collect mails from customers
            $customers_ids = array();
            if (!empty($settings['customers' . $recipient_type_suffix])) {
                foreach ($settings['customers' . $recipient_type_suffix] as $cs) {
                    $cs = $this->_getValue($model, $cs);
                    foreach ($cs as $c) {
                        if (!empty($c)) {
                            $customers_ids[] = $c;
                        }
                    }
                }
            }
            if (!empty($customers_ids)) {
                $filters_customers = array(
                    'where' => array(
                        "c.id IN ('" . implode("', '", $customers_ids) . "')"),
                    'skip_permissions_check' => true
                );
                $customers = Customers::search($registry, $filters_customers);
                if (!empty($customers)) {
                    foreach ($customers as $customer) {
                        unset($branches);

                        // Get the customer emails
                        // TODO: get the customers names
                        $customer_emails = $customer->get('email');
                        if (is_array($customer_emails)) {
                            $customer_emails = array_filter($customer_emails);
                            if (!empty($customer_emails)) {
                                $emails[$recipient_type] = array_merge($emails[$recipient_type], array_values($customer_emails));
                            }
                        }

                        // Get the customer branches emails
                        // TODO: get the branches names
                        if (!empty($settings["customers_{$recipient_type}_branches_to"])
                        || !empty($settings["customers_{$recipient_type}_branches_cc"])
                        || !empty($settings["customers_{$recipient_type}_branches_bcc"])) {
                            $customer->unsanitize();
                            $branches = $customer->getBranches();
                            foreach ($branches as $branch) {
                                $branch_emails = $branch->get('email');
                                if (is_array($branch_emails)) {
                                    $branch_emails = array_filter($branch_emails);
                                    if (!empty($branch_emails)) {
                                        if (!empty($settings["customers_{$recipient_type}_branches_to"])) {
                                            $emails['to'] = array_merge($emails['to'], array_values($branch_emails));
                                        }
                                        if (!empty($settings["customers_{$recipient_type}_branches_cc"])) {
                                            $emails['cc'] = array_merge($emails['cc'], array_values($branch_emails));
                                        }
                                        if (!empty($settings["customers_{$recipient_type}_branches_bcc"])) {
                                            $emails['bcc'] = array_merge($emails['bcc'], array_values($branch_emails));
                                        }
                                    }
                                }
                            }
                        }

                        // Get the customer contact persons emails
                        // TODO: get the contact persons names
                        if (!empty($settings["customers_{$recipient_type}_contact_persons_to"])
                        || !empty($settings["customers_{$recipient_type}_contact_persons_cc"])
                        || !empty($settings["customers_{$recipient_type}_contact_persons_bcc"])) {
                            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                            $customer->unsanitize();
                            if (!isset($branches)) {
                                $branches = $customer->getBranches();
                            }
                            if (!empty($branches)) {
                                $parents = array();
                                foreach ($branches as $br) {
                                    $parents[] = $br->get('id');
                                }

                                $filters = array(
                                    'where' => array(
                                        "c.subtype = 'contact'",
                                        'c.parent_customer IN (' . implode(', ', $parents) . ')',
                                        'c.active = 1'
                                    ),
                                    'sort' => array(
                                        'c.is_main DESC',
                                        "CONCAT_WS(' ', ci18n.name, ci18n.lastname) ASC",
                                        'c.id DESC'
                                    ),
                                    'model_lang' => $customer->get('model_lang'),
                                    'sanitize' => true,
                                    'skip_permissions' => true
                                );
                                if (!empty($settings["customers_{$recipient_type}_contact_persons_filter"])) {
                                    $contact_persons_filters = preg_split('/\s*,\s*/', $settings["customers_{$recipient_type}_contact_persons_filter"]);
                                    $contact_persons_filters_sql = array();
                                    if (in_array('main_contact', $contact_persons_filters)) {
                                        $contact_persons_filters_sql[] = 'c.is_main = 1';
                                    }
                                    if (in_array('financial_person', $contact_persons_filters)) {
                                        $contact_persons_filters_sql[] = 'c.admit_VAT_credit = 1';
                                    }
                                    if (in_array('other', $contact_persons_filters)) {
                                        $contact_persons_filters_sql[] = 'c.is_main != 1 AND c.admit_VAT_credit != 1';
                                    }
                                    if (!empty($contact_persons_filters_sql)) {
                                        $filters['where'][] = '(' . implode(' OR ', $contact_persons_filters_sql) . ')';
                                    }
                                }
                                $contact_persons = Customers_Contactpersons::search($registry, $filters);
                                foreach ($contact_persons as $contact_person) {
                                    $contact_person_emails = $contact_person->get('email');
                                    if (is_array($contact_person_emails)) {
                                        $contact_person_emails = array_filter($contact_person_emails);
                                        if (!empty($contact_person_emails)) {
                                            if (!empty($settings["customers_{$recipient_type}_contact_persons_to"])) {
                                                $emails['to'] = array_merge($emails['to'], array_values($contact_person_emails));
                                            }
                                            if (!empty($settings["customers_{$recipient_type}_contact_persons_cc"])) {
                                                $emails['cc'] = array_merge($emails['cc'], array_values($contact_person_emails));
                                            }
                                            if (!empty($settings["customers_{$recipient_type}_contact_persons_bcc"])) {
                                                $emails['bcc'] = array_merge($emails['bcc'], array_values($contact_person_emails));
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        $customer->sanitize();
                    }
                }
            }

            // Collect mails
            if (!empty($settings['emails' . $recipient_type_suffix])) {
                foreach ($settings['emails' . $recipient_type_suffix] as $es) {
                    $es = $this->_getValue($model, $es);
                    $emails[$recipient_type] = array_merge($emails[$recipient_type], $es);
                }
            }
        }

        // If there are no "to" mails
        if (empty($emails['to'])) {
            $msg = $this->i18n('automations_sendmail_no_mails');
            if ($params['automation_type'] == 'crontab') {
                if (empty($settings['skip_no_mails_execution_message'])) {
                    $this->executionErrors[] = $msg;
                }
            } else {
                if (empty($settings['skip_no_mails_user_message'])) {
                    $messages->setError($msg);
                    $messages->insertInSession($registry);
                }
            }
            return false;
        }

        // Get the unique emails
        // "cc" mails, which are already in "to", will not be used as "cc"
        // "bcc" mails, which are already in "to" or "cc", will not be used as "bcc"
        // TODO: Sometimes some email can exists twice or more, when it's set with name.
        //       Example settings:
        //         emails := <EMAIL>,Some Name <<EMAIL>>,<EMAIL>,<EMAIL>,<EMAIL>
        //       In this example <NAME_EMAIL> will be used once, but <EMAIL> will be used twice.
        //       To get the emails really unique, we need to extract the emails withoud the names.
        //       The same goes for array_diff, which is used to get one mail in only one group: to, cc, or bcc.
        $emails['to']  = array_unique(array_filter($emails['to']));
        $emails['cc']  = array_diff(array_unique(array_filter($emails['cc'])), $emails['to']);
        $emails['bcc'] = array_diff(array_unique(array_filter($emails['bcc'])), $emails['to'], $emails['cc']);

        // Prepare the mails data
        foreach ($recipients_types as $recipient_type => $recipient_type_suffix) {
            if (!empty($emails[$recipient_type])) {
                $model->set('customer_email' . $recipient_type_suffix, $emails[$recipient_type], true);
            }
        }
        $model->set('email_subject', $email_template['subject'], true);
        $model->set('body', $email_template['body'], true);
        $model->set('email_template', $settings['email_template'], true);
        $attached_files = array();
        if (!empty($settings['attached_files_templates'])) {
            $attached_files = array_merge($attached_files, array_map(function($a) {return 'pattern_' . $a;}, $settings['attached_files_templates']));
        }
        if (!empty($settings['attach_files'])) {
            $attached_files = array_merge($attached_files, array_map(function($a) {return 'file_' . $a;}, $settings['attach_files']));
        }
        $origins = array('generated', 'attached');
        foreach ($origins as $origin) {
            if (!empty($settings["attach_current_record_{$origin}_files"])) {
                $sql_select = '';
                if ($settings["attach_current_record_{$origin}_files"] == 'all') {
                    $sql_select = 'id';
                } else if ($settings["attach_current_record_{$origin}_files"] == 'last') {
                    $sql_select = 'MAX(id)';
                }
                if ($sql_select) {
                    $query = "
                        SELECT {$sql_select}
                          FROM " . DB_TABLE_FILES . "
                          WHERE model = '{$model->modelName}'
                            AND model_id = '{$model->get('id')}'
                            AND origin = '{$origin}'
                            AND !deleted";
                    $current_record_files = $db->GetCol($query);
                    $attached_files = array_merge($attached_files, array_map(function($a) {return 'file_' . $a;}, $current_record_files));
                }
            }
        }
        $model->set('attached_files', $attached_files, true);

        // Send the mails
        set_time_limit(0);
        $get_old_vars = $registry->get('get_old_vars');
        $registry->set('get_old_vars', true, true);
        if (empty($settings['not_system_email'])) {
            $model->set('set_system_email', true, true);
        }
        $mails = $model->sendAsMail();
        $registry->set('get_old_vars', $get_old_vars, true);

        // Sanitize the model
        if ($sanitize_after) {
            $model->sanitize();
        }

        // Check the result of sending the mail
        if (!empty($mails['erred'])) {
            $messages->setError($this->i18n('automations_sendmail_sending_mails_failed'));
            $messages->insertInSession($registry);
            $this->executionErrors[] = 'Sending mails failed!';
            return false;
        }

        return true;
    }

    /**
     * Get value from basic or additional var or return the value
     */
    private function _getValue($model, $value) {
        if (preg_match('/(b|a)_(.+)/', $value, $matches)) {
            // If this is a basic var
            if ($matches[1] == 'b') {
                $value = $model->get($matches[2]);
            } else {
                // If this is an additional var
                $value = $model->getPlainVarValue($matches[2]);
            }
        }
        if (is_string($value)) {
            $value = array($value);
        } else if (!is_array($value)) {
            $value = array();
        }

        $result = array();
        foreach ($value as $k => $v) {
            $result = array_merge($result, preg_split('/(,|;)/', $v));
        }

        return $result;
    }

    /**
     * Extracts placeholder variables from string - substrings enclosed in
     * square brackes but not starting with a single quote, a double quote or
     * a digit - those are probaby array keys
     *
     * @param string $subject - input string
     * @return array - extracted unique values
     */
    protected function extractVariables($subject) {
        preg_match_all("#\[(?!['\"\d])([^\[]+)\]#", $subject, $matches, PREG_PATTERN_ORDER);
        return array_unique($matches[1]);
    }

    /**
     * Performs evaluation of variable when in specific format
     *
     * @param string $var - variable value
     * @param array $params - params to pass to eval method - like db origin data
     * @return mixed - new value
     */
    protected function getVariableValue($var, array $params = array()) {
        $matches = array();
        // this setting contains php code that has to be evaluated
        if (preg_match('#^php\s*\((.*)\)$#s', $var, $matches)) {
            $var = EvalString::evaluate(
                $this->registry,
                $matches[1],
                array(
                    'object' => $this,
                ) + $params
            );
        }
        return $var;
    }

    /**
     * Sends notification for changed status of model to assignees
     *
     * @param string $module - module of automation
     * @param Model $new_model - model
     * @param mixed $audit_parent - id of history record or false
     */
    public function sendStatusNotification($module, $new_model, $audit_parent) {
        $model_name = General::plural2singular($module);
        $template = $model_name . '_status';

        if (!$new_model->shouldSendEmail($template)) {
            return true;
        }

        $table_assignments = constant('DB_TABLE_' . strtoupper($module) . '_ASSIGNMENTS');

        $not_users = Users::getUsersNoSend($this->registry, $template);
        $query = 'SELECT DISTINCT(u2.id),' . "\n" .
                 '  u2.email as assignment_email, ui18n2.firstname, ui18n2.lastname,' . "\n".
                 '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) AS assignment_name, u2.is_portal ' . "\n".
                 'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                 '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="' . $new_model->get('model_lang') . '")' . "\n" .
                 'JOIN ' . $table_assignments . ' AS ta' . "\n" .
                 '  ON ta.parent_id=\'' . $new_model->get('id') . '\' AND ' .
                 ($module == 'events' ?
                   '  ta.participant_type=\'user\' AND u2.id=ta.participant_id' :
                   '  (u2.id=ta.assigned_to' . ($module == 'projects' ? ' AND ta.assignments_type=\'Users\'' : '') . ')') . "\n" .
                 'WHERE u2.active=1 AND u2.deleted_by=0';
        $records = $this->registry['db']->GetAll($query);

        //prepare audit data
        $this->registry['request']->set('audit', $audit_parent, 'all', true);
        require_once PH_MODULES_DIR . $module . '/viewers/' . $module . '.audit.viewer.php';
        $audit_class_name = ucfirst($module) . '_Audit_Viewer';

        //for crontab automation - change routing parameters in registry
        $orig_module = $this->registry['module'];
        if ($this->registry->get('crontab_automations')) {
            $this->registry->set('module', $module, true);
        }

        $configViewer = new $audit_class_name($this->registry);
        $configViewer->templatesDir = $this->registry['theme']->templatesDir;
        $configViewer->setTemplate('_audit_email.html');

        //prepare the title for the audit table
        $audit_title = sprintf($this->i18n('record_changed_by_at'), $new_model->get('modified_by_name'), date('d.m.Y, H:i', strtotime($new_model->get('modified'))));
        $configViewer->data['audit_title'] = $audit_title;
        $configViewer->prepare();
        $audit = $configViewer->data['audit'];
        $statuses = array();
        foreach ($audit['vars'] as $var) {
            $statuses[$var['field_name']] = $var['label'];
            $statuses['old_' . $var['field_name']] = $var['old_value'];
        }
        $audit_data = $configViewer->fetch();
        $new_model->set('last_audit', $audit_data, true);

        if ($this->registry->get('crontab_automations')) {
            $this->registry->set('module', $orig_module, true);
        }

        // CHECK if there is audit data and compare the settings for each user from personal settings table
        $audit_data = preg_replace('/(\n|\r)/', '', $audit_data);
        // Setting 'document_edit_send_when_audit' is used for all modules, section is 'emails'!!!
        $users_send_always = Users::getUsersSendSettings($this->registry, 'document_edit_send_when_audit', 'emails');

        $mailer = null;
        foreach ($records as $record) {
            //check if the assignee wants to receive email
            if ($record['assignment_email']) {
                $send = false;
                if (in_array($record['id'], $not_users)) {
                    //the user does not want to receive notifications when the model is edited
                    continue;
                }

                // force sending of comment if no status has been changed but comment is added
                $force_send_comment_email = false;
                if ($new_model->get('comment') && !($record['is_portal'] && !$new_model->get('comment')->get('is_portal'))) {
                    $force_send_comment_email = true;
                }

                if (empty($audit_data) && in_array($record['id'], $users_send_always)) {
                    $send = true;
                } else if (!empty($audit_data) || $force_send_comment_email) {
                    $send = true;
                }
                if ($send) {
                    if (!$mailer) {
                        $mailer = new Mailer($this->registry, $template, $new_model);
                        $mailer->placeholder->merge($new_model->getNotificationEmailsVars($template));

                        //TODO when all placeholders are reworked to have 'source' value, assigning them one by one can be removed completely
                        $mailer->placeholder->add($model_name . '_id', $new_model->get('id'));
                        $mailer->placeholder->add($model_name . '_name', $new_model->get('name'));
                        $mailer->placeholder->add($model_name . '_type', $new_model->get('type_name'));
                        $mailer->placeholder->add($model_name . '_added_by', $new_model->get('added_by_name'));
                        $mailer->placeholder->add($model_name . '_num', ($new_model->isDefined('full_num') ? $new_model->get('full_num') : $new_model->get('num')));
                        if ($new_model->isDefined('code')) {
                            $mailer->placeholder->add($model_name . '_code', $new_model->get('code'));
                        }
                        $mailer->placeholder->add($model_name . '_info', $audit_data);
                        $mailer->placeholder->add('last_audit', $audit_data);
                        $mailer->placeholder->add('customer_name', $new_model->get('customer_name'));
                        $mailer->placeholder->add('modified_by_name', $new_model->get('modified_by_name'));

                        if (empty($statuses['status'])) {
                            $mailer->placeholder->add('old_status', $this->i18n($module . '_status_' . $new_model->get('status')));
                            $mailer->placeholder->add('status', $this->i18n($module . '_status_' . $new_model->get('status')));
                        } else {
                            $mailer->placeholder->add('old_status', (isset($statuses['old_status']) ? $statuses['old_status'] : ''));
                            $mailer->placeholder->add('status', $statuses['status']);
                        }
                        if (isset($statuses['old_substatus']) && $statuses['old_substatus'] != '-') {
                            $mailer->placeholder->add('old_substatus', ' (' . $statuses['old_substatus'] . ')');
                        } elseif (empty($statuses) && $new_model->get('substatus')) {
                            $mailer->placeholder->add('old_substatus', ' (' . $new_model->get('substatus_name') . ')');
                        }
                        if (isset($statuses['substatus']) && $statuses['substatus'] != '-') {
                            $mailer->placeholder->add('substatus', ' (' . $statuses['substatus'] . ')');
                        } elseif (empty($statuses) && $new_model->get('substatus')) {
                            $mailer->placeholder->add('substatus', ' (' . $new_model->get('substatus_name') . ')');
                        }
                    }

                    // set the user-specific placeholders
                    $mailer->placeholder->add('to_email', $record['assignment_email']);
                    $mailer->placeholder->add('user_name', $record['assignment_name']);

                    $comment = $new_model->get('comment');
                    if ($comment && $comment->get('content') && !($record['is_portal'] && !$comment->get('is_portal'))) {
                        $mailer->placeholder->add('model_id', $comment->get('id'));
                        $mailer->placeholder->add('model', 'Comment');
                        $mailer->placeholder->add($model_name . '_comment', nl2br($comment->get('content')));

                        $mailer->template['model_name'] = 'Comment';
                        $mailer->template['model_id'] = $comment->get('id');
                        $mailer->template['system_flag'] = 0;
                    } else {
                        // portal user should not get non-portal comment
                        $mailer->placeholder->flush('model_id');
                        $mailer->placeholder->flush('model');
                        $mailer->placeholder->flush($model_name . '_comment');

                        $mailer->template['model_name'] = $new_model->modelName;
                        $mailer->template['model_id'] = $new_model->get('id');
                        unset($mailer->template['system_flag']);
                    }

                    //send email
                    $result = $mailer->send();
                    if (!@in_array($record['assignment_email'], $result['erred'])) {

                    } else {

                    }
                }
            }
        }
    }

    /**
     * Check the start time and day of execution.
     * Use this method to prevent method from execution more than once a day.
     * Automation settings need to have at least one of the checked options
     * (which are checked inside this method) in order for the method to be fully executed
     * IMPORTANT: to guarantee start at certain hour include "start_time := 01:00" in the automation settings
     *
     * @param int $automation_id - the id of the automation that has been triggered
     * @return bool - true - execute the method, false - stop it
     */
    public function checkStart($automation_id = 0) {
        //Raise a flag to define whether this method has been called or not
        $this->startIsChecked = true;

        if (!$automation_id) {
            return false;
        }

        // if any of these parameters is applied then the checkStart method has to continue its execution
        $parameters_to_check = array('period', 'start_month', 'start_month_date', 'start_week_day', 'start_time', 'start_before', 'depends_on');
        if (!empty($this->settings)) {
            $available_settings = array_keys($this->settings);
        } else {
            $this->isExecuted = true;
            return true;
        }

        $options_to_check = array_intersect($available_settings, $parameters_to_check);
        if (empty($options_to_check)) {
            // if none of the desired options is set then this automation
            // has not been checked and will be directly executed
            $this->isExecuted = true;
            return true;
        }

        $now = new DateTime();

        //get the period setting
        $period = $this->settings['period'] ?? '';
        //ToDo: set period explicitly in the settings (no default value for period)
        if (empty($period)) {
            //the default period is one day
            $period = '1 day';
        }

        //check if the settings for period are correct
        //legal formats are: number day(s)|hour(s)|minutes(s):
        // 1 day
        // 3days
        // 60minutes
        // 1hour
        // 3 hours
        //the space between is optional
        preg_match('/^([\d]*)\s*(day|hour|minute)s?\s*$/i', $period, $matches);
        $executionPeriod = $matches[1] ?? 0;
        //remove leading zeros
        $executionPeriod = (int) ltrim($executionPeriod, '0');
        $executionPeriodType = $matches[2] ?? '';
        if ($executionPeriod > 0 && $executionPeriodType) {
            //get last execution date
            $query = 'SELECT added FROM ' . DB_TABLE_AUTOMATIONS_HISTORY . "\n" .
                'WHERE parent_id = ' . $automation_id . "\n" .
                'ORDER BY added DESC' . "\n" .
                'LIMIT 1';
            $lastExecution = $this->registry['db']->GetOne($query);

            if (!empty($lastExecution)) {
                $lastExecutionDateTime = new DateTime($lastExecution);
                $this->lastExecutionDate = $lastExecutionDateTime->format('Y-m-d');
                $this->lastExecutionTime = $lastExecutionDateTime->format('Y-m-d H:i:s');
                if ($lastExecutionDateTime > $now) {
                    // this should not happen as the history table records
                    // execution of automations in the past
                    return false;
                }

                if ($executionPeriodType == 'day') {
                    //IMPORTANT: if the period is in days - don't care about the time!
                    $nextExecutionDateTime = new DateTime($lastExecutionDateTime->format('Y-m-d'));
                    $duration = "P{$executionPeriod}D";
                    $nextExecutionDateTime->add(new DateInterval($duration));
                    $this->nextExecutionDate = $nextExecutionDateTime->format('Y-m-d');
                    $this->nextExecutionTime = $nextExecutionDateTime->format('Y-m-d H:i:s');
                    if ($nextExecutionDateTime->format('Y-m-d') > $now->format('Y-m-d')) {
                        //next date is in the future
                        return false;
                    }
                } else {
                    $nextExecutionDateTime = new DateTime($lastExecutionDateTime->format('Y-m-d H:i:s'));
                    //duration type is M (minutes) or H (hours)
                    $duration = "PT{$executionPeriod}" . strtoupper(substr($executionPeriodType, 0, 1));
                    $nextExecutionDateTime->add(new DateInterval($duration));
                    $this->nextExecutionDate = $nextExecutionDateTime->format('Y-m-d');
                    $this->nextExecutionTime = $nextExecutionDateTime->format('Y-m-d H:i:s');
                    if ($nextExecutionDateTime > $now) {
                        //next date is in the future
                        return false;
                    }
                }
            }
        }

        if (!empty($this->settings['start_month'])) {
            $currentMonth = $now->format('n');
            $startMonths = preg_split('#\s*,\s*#', $this->settings['start_month']);
            array_walk($startMonths, function (&$startMonth) {
                //remove leading zeroes
                $startMonth =  (int) ltrim($startMonth, '0');
            });
            if (!in_array($currentMonth, $startMonths)) {
                return false;
            }
        }
        if (!empty($this->settings['start_month_date'])) {
            $currentDayOfMonth = $now->format('j');
            $startMonthDates = preg_split('#\s*,\s*#', $this->settings['start_month_date']);
            array_walk($startMonthDates, function (&$startMonthDate) use ($now) {
                // there is an option for the last date of the month
                // not knowing which ones is: 31, 30, 28 or 29)
                if ($startMonthDate == 'last_date') {
                    $startMonthDate = $now->format('t');
                }
                //remove leading zeroes
                $startMonthDate = (int) ltrim($startMonthDate, '0');
            });
            if (!in_array($currentDayOfMonth, $startMonthDates)) {
                return false;
            }
        }
        if (!empty($this->settings['start_week_day'])) {
            $currentWeekDay = $now->format('N');
            $startWeekDays = preg_split('#\s*,\s*#', $this->settings['start_week_day']);
            array_walk($startWeekDays, function (&$startWeekDay) {
                //remove leading zeroes
                $startWeekDay =  (int) ltrim($startWeekDay, '0');
            });
            if (!in_array($currentWeekDay, $startWeekDays)) {
                return false;
            }
        }
        if (!empty($this->settings['start_time']) &&
            $this->settings['start_time'] > $now->format('H:i')) {
            //the time should be the same as in the settings
            return false;
        }
        if (!empty($this->settings['start_before']) &&
            $this->settings['start_before'] < $now->format('H:i')) {
            //the time should be before this setting
            return false;
        }

        if (!empty($this->settings['depends_on'])) {
            $query = 'SELECT id FROM ' . DB_TABLE_AUTOMATIONS . ' WHERE method LIKE "%' . $this->settings['depends_on'] . '%" AND `active`=1';
            $depends_on_id = $this->registry['db']->GetOne($query);
            if ($depends_on_id) {
                $query = 'SELECT DATE_FORMAT(added, "%Y-%m-%d") FROM ' . DB_TABLE_AUTOMATIONS_HISTORY . "\n" .
                         'WHERE parent_id = ' . $depends_on_id . "\n" .
                         'ORDER BY added DESC' . "\n" .
                         'LIMIT 1';
                $parent_execution_date = $this->registry['db']->GetOne($query);
                if ($parent_execution_date != $now->format('Y-m-d')) {
                    //parent method must be executed today
                    return false;
                }
            }
        }

        //set isExecuted for send execution results
        $this->isExecuted = true;

        return true;
    }

    /**
     * Validate for uniqueness
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function validateUnique($params) {
        $valid = true;

        if ($this->registry['request']->isPost() && in_array($this->registry['action'], array('add', 'edit', 'addquick'))) {
            $errors = array();
            $registry = &$this->registry;
            $messages = &$registry['messages'];
            $this->loadI18NFiles();
            if (empty($params['unique_keys'])) {
                $errors[] = $this->i18n('automations_validateunique_no_unique_keys');
            } else {
                $model = $params['model'];
                $db = &$registry['db'];
                $lang = ($model->get('model_lang') ?: $registry['lang']);
                $request = $registry['request'];

                $module     = $params['module'];
                $controller = $params['controller'] ? $params['controller'] : $params['module'];

                $db_tbl = strtoupper($module . ($module != $controller ? '_' . $controller : ''));
                $db_table = constant("DB_TABLE_{$db_tbl}");
                $db_table_i18n = constant("DB_TABLE_{$db_tbl}_I18N");

                $db_table_columns = $db->MetaColumnNames($db_table, true);
                $db_table_i18n_columns = $db->MetaColumnNames($db_table_i18n, true);

                $vars_basic = array();
                $vars_additional = array();
                $unique_keys = preg_split('/\s*;\s*/', $params['unique_keys']);
                $rules_or = array();
                foreach ($unique_keys as $u_keys) {
                    $rules_and = array();
                    $u_keys = preg_split('/\s*,\s*/', $u_keys);
                    foreach ($u_keys as $u_key) {
                        if (preg_match('/^b_.*$/', $u_key)) {
                            $u_key = preg_replace('/^b_/', '', $u_key);
                            if (in_array($u_key, $db_table_columns)) {
                                $alias = 't';
                            } else if (in_array($u_key, $db_table_i18n_columns)) {
                                $alias = 'ti18n';
                            } else {
                                $errors[] = $this->i18n('automations_validateunique_basic_varst_not_exists');
                            }
                            if (empty($errors)) {
                                //special behaviour for customer contact details
                                if ($module == 'customers' && $controller == 'customers') {
                                    $emptyCustomer = new Customer($this->registry);
                                    if (in_array($u_key, $emptyCustomer->contactParameters)) {
                                        $contact_clauses = array();
                                        foreach($request->get('link_types') as $idx => $ltype) {
                                            if ($ltype == $u_key) {
                                                $contact_data = $request->get('link');
                                                if (isset($contact_data[$idx])) {
                                                    $contact_value = trim($contact_data[$idx]);
                                                    //use REGEXP '(^|\n) *<contact_value> *(\\||\r?\n)', because the contact parameter
                                                    // column might contain several rows and notes (separated with pipe |)
                                                    $contact_clauses[] = "{$alias}.{$u_key} REGEXP '(^|\\n) *{$contact_value} *(\\\\||\\r?\\n|$)'";
                                                }
                                            }
                                        }
                                        if (!empty($contact_clauses)) {
                                            $rules_and[] = '(' . implode(' OR ', $contact_clauses) . ')';
                                            $vars_basic[] = $u_key;
                                        }
                                    }
                                    unset($emptyCustomer);
                                } else {
                                    $rules_and[] = "{$alias}.{$u_key} = '{$request->get($u_key)}'";
                                    $vars_basic[] = $u_key;
                                }
                            }
                        } else if (preg_match('/^a_.*$/', $u_key)) {
                            $u_key = preg_replace('/^a_/', '', $u_key);
                            $rules_and[] = "tc_{$u_key}.value = '{$request->get($u_key)}'";
                            $vars_additional[] = $u_key;
                        } else {
                            $errors[] = $this->i18n('automations_validateunique_unknown_unique_keys');
                            break;
                        }
                    }
                    if (!empty($rules_and)) {
                        $rules_or[] = $rules_and;
                    }
                }

                if (count($vars_additional) > 55) {
                    $errors[] = $this->i18n('automations_validateunique_too_much_additional_vars');
                }

                if (empty($errors)) {
                    if (empty($vars_basic) && empty($vars_additional)) {
                        $errors[] = $this->i18n('automations_validateunique_no_unique_keys');
                    } else {
                        if (!empty($vars_additional)) {
                            $db_table_cstm = "DB_TABLE_{$db_tbl}_CSTM";
                            if (!defined($db_table_cstm)) {
                                $errors[] = $this->i18n('automations_validateunique_no_cstm_table');
                            } else {
                                $db_table_cstm = constant($db_table_cstm);

                                $query = "
                                    SELECT name, GROUP_CONCAT(id)
                                      FROM " . DB_TABLE_FIELDS_META . "
                                      WHERE model = '{$model->modelName}'
                                        AND name IN ('" . implode("', '", $vars_additional) . "')
                                        AND 1 NOT IN (bb, gt2)";
                                if (!empty(intval($params['start_model_type']))) {
                                    $query .= " AND model_type = '{$params['start_model_type']}'";
                                }
                                $query .= ' GROUP BY `name`';
                                $vars_additional_ids = $db->GetAssoc($query);

                                if (count(array_intersect_key(array_flip($vars_additional), $vars_additional_ids)) != count($vars_additional)) {
                                    $errors[] = $this->i18n('automations_validateunique_additional_vars_not_exists');
                                }
                            }
                        }

                        if (empty($errors)) {
                            // Build the query to check for duplicates
                            $query = "
                                SELECT t.id, t.type
                                  FROM {$db_table} AS t
                                  JOIN {$db_table_i18n} AS ti18n
                                    ON (ti18n.parent_id = t.id
                                      AND ti18n.lang = '{$lang}')";

                            if (!empty($vars_additional_ids) && !empty($db_table_cstm)) {
                                foreach ($vars_additional_ids as $var_name => $var_id) {
                                    $query .= "
                                        JOIN {$db_table_cstm} AS tc_{$var_name}
                                          ON (tc_{$var_name}.model_id = t.id
                                            AND tc_{$var_name}.var_id IN ({$var_id}))";
                                }
                            }

                            $query .= "
                                  WHERE t.active = 1
                                    AND t.deleted_by = 0";
                            if (!empty(intval($params['start_model_type']))) {
                                $query .= " AND t.type = '{$params['start_model_type']}'";
                            }
                            if ($registry['action'] == 'edit') {
                                $query .= "
                                    AND t.id != '{$model->get('id')}'";
                            }
                            if (!empty($rules_or)) {
                                $query .= "
                                    AND (";
                                foreach ($rules_or as $rules_or_key => $rules_and) {
                                    $rules_or[$rules_or_key] = implode(' AND ', $rules_and);
                                }
                                $query .= implode(' OR ', $rules_or);
                                $query .= ")";
                            }
                            $query .= "
                                LIMIT 1";
                            $duplicate = $db->GetRow($query);

                            if (!empty($duplicate)) {
                                $valid = false;
                                $url = sprintf('%s?%s=%s%s&amp;%s=%s&amp;%s=%s&amp;model_lang=%s',
                                    $_SERVER['PHP_SELF'],
                                    $registry['module_param'], $module,
                                    ($controller != $module ? "&amp;{$registry['controller_param']}={$controller}" : '' ),
                                    $registry['action_param'], 'view',
                                    'view', $duplicate['id'], $lang);

                                $factoryClass = $model->getFactory();
                                $duplicateModel = $factoryClass::newModel($registry, $duplicate, $model->modelName);
                                $errors[] = sprintf($this->i18n('automations_validateunique_duplicate_found'), $url, $duplicateModel->getModelTypeName());
                            }
                        }
                    }
                }
            }

            if ($errors) {
                $valid = false;
                //use custom error message from the settings if available
                if(isset($this->settings['error_msg_' . $lang])) {
                    $messages->setError($this->settings['error_msg_' . $lang]);
                } else {
                    $messages->setError(sprintf($this->i18n('automations_errors_in_automation'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
                }
                foreach ($errors as $error) {
                    $messages->setError($error);
                }
            }
        }

        return $valid;
    }

    /**
     * Send model as mail when setting a tag
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function sendTaggedAsMail($params) {
        // This automation can be executed only when setting a tag
        $registry = &$this->registry;
        if (!in_array($registry['action'], array('tag', 'multitag'))) {
            return false;
        }

        // Automation is not successful until sending mail correctly
        $result = false;

        // Prepare some basics
        $lang = $registry['lang'];
        $db = &$registry['db'];
        $model = $params['model'];
        $messages = &$registry['messages'];
        $this->loadI18NFiles();
        $settings = General::parseSettings($params['settings']);

        // Make this automation work only for finance module
        if ($params['module'] != 'finance') {
            $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_incorrect_modul'), $params['name']));
        } else {
            // Check required settings
            $required_settings = array(
                'tag',
                'generate_pattern',
                'email_pattern',
                'field_cus_email'
            );
            if (count(array_intersect_key(array_filter($settings), array_flip($required_settings))) != count($required_settings)) {
                // Set error if any required setting is missing
                $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_missing_required_settings'), $params['name']));
            } else {
                // This automation can be executed only if the tag from the settings is set
                $requested_tags = $registry['request']->get('tags');
                $requested_tags = !empty($requested_tags) && is_array($requested_tags) ? $requested_tags : array();
                if ($registry['action'] == 'tag' && !in_array($settings['tag'], $requested_tags)
                        || $registry['action'] == 'multitag' && $registry['request']->get('tagsSelect') != $settings['tag']) {
                    return false;
                }

                // Get the customer type id
                $customer_type_id = $db->GetOne("SELECT `type` FROM " . DB_TABLE_CUSTOMERS . " WHERE id = '{$model->get('customer')}'");

                // Get the customer field for email
                $query = "
                    SELECT fm.id    AS id,
                        fmi.content AS name
                      FROM " . DB_TABLE_FIELDS_META . " AS fm
                      JOIN " . DB_TABLE_FIELDS_I18N . " AS fmi
                        ON (fmi.parent_id = fm.id
                          AND fmi.content_type = 'label'
                          AND fmi.lang = '{$lang}')
                      WHERE fm.model = 'Customer'
                        AND fm.model_type = '{$customer_type_id}'
                        AND fm.name = '{$settings['field_cus_email']}'";
                $field_customer_email = $db->GetRow($query);

                // If the customer field for email doesn't exist into the database
                if (empty($field_customer_email)) {
                    // Set error
                    $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_missing_required_fields'), $params['name']));
                } else {
                    // Get all emails of the customer
                    $query = "
                        SELECT value
                          FROM " . DB_TABLE_CUSTOMERS_CSTM . "
                          WHERE var_id = '{$field_customer_email['id']}'
                              AND model_id = '{$model->get('customer')}'";
                    $customer_emails = $db->GetOne($query);
                    if (!empty($settings['emails_separators'])) {
                        $emails_separators = str_split(preg_replace('/"([^"]+)"/', '$1', $settings['emails_separators']));
                        if (!empty($emails_separators)) {
                            $customer_emails = array_filter(array_map('trim', preg_split('/(' . implode('|', $emails_separators) . ')/', $customer_emails)));
                        } else {
                            $customer_emails = array($customer_emails);
                        }
                    } else {
                        $customer_emails = array($customer_emails);
                    }

                    // Get type name
                    $model_type_name = $model->get('type_name');
                    $model_type_name = mb_strtolower($model_type_name, mb_detect_encoding($model_type_name));

                    // If no customer email
                    if (empty($customer_emails)) {
                        // Set error
                        $url = "{$_SERVER['PHP_SELF']}?{$registry['module_param']}=customers&amp;customers=view&amp;view={$model->get('customer')}&amp;model_lang={$lang}";
                        $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_no_mail'), $url, $model_type_name, $model->get('num'), $field_customer_email['name']));
                    } else {
                        // Flip the original and current users
                        $this->setOriginalUserAsCurrent();

                        // Get the email pattern
                        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
                        $filters = array(
                            'where' => array("e.id = '{$settings['email_pattern']}'"),
                            'sanitize' => true,
                            'model_lang' => $lang
                        );
                        $mail = Emails::searchOne($registry, $filters);

                        // If no email pattern
                        if (empty($mail)) {
                            // Set error
                            $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_no_mail_pattern'), $params['name']));
                        } else {
                            // Send the email
                            $model->set('email_subject', $mail->get('subject'), true);
                            $model->set('body', $mail->get('body'), true);
                            $model->set('email_template', $mail->get('id'), true);
                            $to = array();
                            foreach ($customer_emails as $customer_email) {
                                $to[] = "{$model->get('customer_name')} <{$customer_email}>";
                            }
                            $model->set('customer_email', $to, true);
                            // TODO: optimization: use already generated invoice pdf (if any)
                            $model->set('attached_files', array("pattern_{$settings['generate_pattern']}"), true);
                            if ($model->isSanitized()) {
                                $model->unsanitize();
                            }
                            set_time_limit(0);

                            // set the old vars in case the automation is for multitag
                            // The first file then was with empty GT2 table. This fixes this problem.
                            $this->registry->set('get_old_vars', true, true);
                            $mails = $model->sendAsMail();
                            $this->registry->set('get_old_vars', false, true);

                            // If there is no problem with the email
                            if (empty($mails['erred'])) {
                                // Set that the automation was successfully executed
                                $result = true;

                                // Set success message
                                $messages->setMessage(sprintf($this->i18n('automations_sendtaggedasmail_mail_success'), $model_type_name, $model->get('num')));
                            } else {
                                // Set error
                                $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_mail_failed'), $model_type_name, $model->get('num')));
                            }
                        }

                        // Flip the original and current users
                        $this->setAutomationUserAsCurrent();
                    }
                }
            }
        }

        // Check the automation result
        if ($messages->getErrors()) {
            // Remove the tag
            if (!empty($settings['tag']) && (
                    $registry['action'] == 'tag' && in_array($settings['tag'], $registry['request']->get('tags'))
                        || $registry['action'] == 'multitag' && $registry['request']->get('tagsSelect') == $settings['tag'])) {
                $params['tags'] = $settings['tag'];
                $tag_name = $db->GetOne("SELECT name FROM " . DB_TABLE_TAGS_I18N . " WHERE parent_id = {$settings['tag']} AND lang = '{$lang}'");
                if ($this->removeTags($params)) {
                    // Set error msg that the tag WAS REMOVED (it was successfully removed, but it was removed because of errors with the current automation)
                    $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_removetag_success'), $tag_name, $model_type_name, $model->get('num'), $params['name']));
                } else {
                    // Set error message if the tag removal has failed
                    $messages->setError(sprintf($this->i18n('automations_sendtaggedasmail_removetag_failed'), $tag_name, $model_type_name, $model->get('num'), $params['name']));
                }
            }
        }
        $messages->insertInSession($registry);

        return $result;
    }

    /**
     * Process messages/errors for createModels automation
     * @param string $key - message key
     * @param array $params - params to be expanded
     * @param string $field - key to add message into
     */
    private function _createModelsMsg($key, $params = array(), $field = '') {
        if (!empty($this->isMulti) && !empty($params['row'])) {
            $params['row'] = $this->i18n('automation_create_models_row', $params['row']);
        } else {
            $params['row'] = '';
        }
        $err = $this->i18n($key, $params);
        if (!$err) {
            $err = $key;
        }
        if ($field) {
            $this->executionErrors[$field] = $err;
        } else {
            $this->executionErrors[] = $err;
        }
    }

    /**
     * Validate required settings in function of model that will be created
     * @param array $settings
     * @return bool - result of the operation
     */
    private function _createModelsValidate($settings) {
        if (empty($settings['create_model'])) {
            return false;
        }
        switch ($settings['create_model']) {
            case 'event':
                // check for event's required fields
                return !(
                    empty($settings['type']) || empty($settings['name']) ||
                    !isset($settings['recurrence_type']) ||
                    empty($settings['event_start']) || empty($settings['event_end'])
                );
                break;
            case 'task':
                // check for tasks' required fields
                return !(
                    empty($settings['type']) || empty($settings['name']) ||
                    empty($settings['customer']) || empty($settings['planned_start_date']) ||
                    empty($settings['planned_finish_date']) || empty($settings['department'])
                );
                break;
            case 'minitask':
                //check for minitasks' required fields
                return !(empty($settings['assigned_to']) || empty($settings['description']));
                break;
            default:
                return false;
                break;
        }
        return false;
    }

    /**
     * Automation for creation of tasks from different sources
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function createModels($params) {

        // Flag to check if the current automation is not a crontab automation
        $this->isCrontab = $params['automation_type'] == 'crontab';

        $this->loadI18NFiles();

        // settings NOT to be used as fields relations
        $skip = array('create_model', 'send_to_email', 'field_defining_multiple', 'edit_if_exists');

        // Check for model
        if (!$this->isCrontab && !isset($params['model'])) {
            return true;
        }
        $this->getSettings($params);
        $settings = $this->settings;
        // Check settings
        if (!$this->_createModelsValidate($settings)) {
            $this->_createModelsMsg('error_automation_create_models_settings_failed', array($params['name']));
            return false;
        }

        set_time_limit(0);

        // Prepare some basics
        $registry = &$this->registry;
        $db       = &$registry['db'];
        // for current model
        $sModel = $params['model']->unsanitize();
        $sOldModel = clone $sModel;
        $sModule = $params['model']->getModule();
        $sController = $params['model']->getController();
        if ($sController == $sModule) {
            $sController = '';
        }
        //check if we have additional variables for source model
        $sVars = array();
        if ($params['model']->checkForVariables()) {
            $sVars = $params['model']->getAssocVars();
        }

        // for new model
        $settings['create_model'] = str_replace(' ', '_', ucwords(str_replace('_', ' ', $settings['create_model'])));
        $dModule = explode('_', $settings['create_model'], 2);
        if (isset($dModule[1])) {
            $dController = General::singular2plural(strtolower($dModule[1]));
            $dModule = strtolower($dModule[0]);
        } else {
            $dController = '';
            $dModule = General::singular2plural(strtolower($dModule[0]));
        }

        //IMPORTANT: unload current model ini file to avoid collision of identical lang params (e.g. error_no_name in documents.ini and tasks.ini)
        //$file = PH_MODULES_DIR . $sModule . '/i18n/' . $this->registry['lang'] . '/' . General::singular2plural(strtolower($sModel->modelName)) . '.ini';
        //$this->registry['translater']->unloadFile($file);
        //load the requested model lang files - this will overwrite current lang params
        $file = PH_MODULES_DIR . $dModule . '/i18n/'.$this->registry['lang'].'/' . General::singular2plural(strtolower($settings['create_model'])) . '.ini';
        $this->registry['translater']->loadFile($file);

        // set current logged in user on the top of the iceberg
        // and preserve automation user for future use
        if ($this->registry['originalUser'] && empty($settings['do_as_automation_user'])) {
            $au = clone $this->registry['currentUser'];
            $this->registry->set('currentUser', clone $this->registry['originalUser'], true);
        }

        // set specific user as current
        // and preserve automation user for future use
        if (!empty($settings['do_as_user'])) {
            $au = clone $this->registry['currentUser'];

            $userId = $settings['do_as_user'];
            $matches = $this->extractVariables($settings['do_as_user']);
            if ($matches) {
                // get all fields used to build current destination field - REFLECTION
                foreach ($matches as $m) {
                    if (preg_match('#^a_#', $m)) {
                        $m = preg_replace('#^a_#', '', $m);
                        $userId = $sVars[$m]['value'];
                    } elseif (preg_match('#^b_#', $m)) {
                        $m = preg_replace('#^b_#', '', $m);
                        $userId = $sModel->get($m);
                    }
                }
            }
            if (is_numeric($userId)) {
                $user = Users::searchOne(
                    $this->registry,
                    array('where' => ["u.id = {$userId}", "u.hidden IS NOT NULL"])
                );
            }
            if (!$user) {
                $this->_createModelsMsg('automations_errors_in_automation', array($params['name']));
                return false;
            }
            $this->registry->set('currentUser', $user, true);
        }

        // we have to save current request in a secure place too
        $currentRequest = clone $this->registry['request'];
        $request = &$this->registry['request'];
        foreach ($request->getAll() as $k => $v) {
            // cleanup
            $request->remove($k);
        }
        // and some other values
        $currentModule = $this->registry['module'];
        $currentController = $this->registry['controller'];
        $currentAction = $this->registry['action'];

        // include new model controller and factory files
        $file = PH_MODULES_DIR . $dModule . '/models/' . $dModule . ($dController ? '.' . $dController : '') . '.factory.php';
        if (!file_exists($file)) {
            // huh! big problem
            $this->_createModelsMsg('automations_errors_in_automation', array($params['name']));
            return false;
        }
        require_once $file;
        $file = PH_MODULES_DIR . $dModule . '/controllers/' . $dModule . ($dController ? '.' . $dController : '') . '.controller.php';
        if (!file_exists($file)) {
            // huh! big problem again
            $this->_createModelsMsg('automations_errors_in_automation', array($params['name']));
            return false;
        }
        require_once $file;
        $dVars = array();
        if (!empty($settings['type'])) {
            //create model just to get its type defaults and additional variables(if any)
            $new = new $settings['create_model']($this->registry, array('type' => $settings['type']));
            //get the type of the new model
            $type = $new->getModelType();

            //check if we have additional variables for destination model
            if ($new->checkForVariables()) {
                $dVars = $params['model']->getAssocVars();
            }

            //set type defaults in the new model properties
            foreach ($type->getAll() as $k => $v) {
                if (preg_match('#^default_(.*)$#', $k, $match)) {
                    $request->set($match[1], $v, 'post', true);
                }
            }
        }
        // set user defaults in the new model's properties
        foreach ($this->registry['currentUser']->getAll() as $k => $v) {
            if (preg_match('#^default_(.*)$#', $k, $match)) {
                $request->set($match[1], $v, 'post', true);
            }
        }


        // check if we have field in the settings that will define
        // multiple records creation
        $multiple = '';
        if (isset($settings['field_defining_multiple'])) {
            $multiple = $settings['field_defining_multiple'];
            if (preg_match('#^\[a_[^\]]+\]#', $multiple)) {
                $multiple = preg_replace('#^\[a_([^\]]+)\]#', '$1', $multiple);
                $this->isMulti = true;
                // only fields in grouping table can define multiple records creation
                if (empty($sVars[$multiple]) || !$sVars[$multiple]['grouping'] || !is_array($sVars[$multiple]['value'])) {
                    $this->_createModelsMsg('error_automation_create_models_multiple_failed', array($params['name']));
                    return false;
                }
            } else {
                // we can not create multiple records from basic variable - no array
                $this->_createModelsMsg('error_automation_create_models_multiple_failed', array($params['name']));
                return false;
            }
        }

        $action = 'add';
        //check if edit mode is set
        //IMPORTANT: the automation should NOT be executed once per model if edit is set!
        //           the first execution creates a model and each subsequent execution edits it
        if (!empty($settings['edit_if_exists']) &&
            $params['nums'] != 1 &&
            !empty($settings['create_from_' . strtolower($sModel->modelName)])) {
            //check if the automation has been run already
            //do not execute edit mode if the automation has not run already (supposingly adding the record)
            $history_record = $this->getHistoryResult($params['model_id'], $params['id']);
            if (!empty($history_record)) {
                if (!empty($multiple)) {
                    //the edit mode is not applicable for multiple models created
                    $this->_createModelsMsg('error_automation_create_models_multiple_edit_not_applicable');
                    if (!$this->isCrontab) {
                        $error_msg = $this->i18n('error_automation_create_models_multiple_edit_not_applicable');
                        $message_method = !empty($this->before_action) ? 'setError' : 'setWarning';
                        $this->registry['messages']->$message_method($error_msg);
                        $this->registry['messages']->insertInSession($this->registry);
                    }

                    return false;
                }
                //try to find the relatives
                $relatives = array();
                switch ($settings['create_model']) {
                    case 'Event':
                        $query = 'SELECT parent_id FROM ' . DB_TABLE_EVENTS_RELATIVES . "\n" .
                                 'WHERE link_type="neutral" AND origin="' . strtolower($sModel->modelName) . '" AND link_to=' . $sModel->get('id');
                        $relatives = $this->registry['db']->GetCol($query);
                        break;
                    case 'Task':
                        $query = 'SELECT parent_id FROM ' . DB_TABLE_TASKS_RELATIVES . "\n" .
                                 'WHERE link_type="child" AND origin="' . strtolower($sModel->modelName) . '" AND link_to=' . $sModel->get('id');
                        $relatives = $this->registry['db']->GetCol($query);
                        break;
                    case 'Minitask':
                        $query = 'SELECT id FROM ' . DB_TABLE_MINITASKS . "\n" .
                                 'WHERE model="' . strtolower($sModel->modelName) . '" AND model_id=' . $sModel->get('id');
                        $relatives = $this->registry['db']->GetCol($query);
                        break;
                    default:
                        break;
                }
                if (!empty($relatives)) {
                    if (count($relatives) == 1) {
                        $destination_model_id = reset($relatives);
                        $action = 'edit';
                    } else {
                        //applicable only for single model creation/edition
                        $this->_createModelsMsg('error_automation_create_models_edit_more_than_one');
                        if (!$this->isCrontab) {
                            $error_msg = $this->i18n('error_automation_create_models_edit_more_than_one');
                            $message_method = !empty($this->before_action) ? 'setError' : 'setWarning';
                            $this->registry['messages']->$message_method($error_msg);
                            $this->registry['messages']->insertInSession($this->registry);
                        }
                        return false;
                    }
                } else {
                    //create a new model -> add action
                    //nothing to do in the this else
                }
            }

        }

        // remove settings that have to be skipped(not defining fields relations)
        foreach ($settings as $k => $v) {
            if (in_array($k, $skip)) {
                unset($settings[$k]);
            }
        }

        //load registry with what we need
        $this->registry->set('module', $dModule, true);
        $this->registry->set('controller', $dController ? $dController : $dModule, true);
        $this->registry->set('action', $action, true);
        if ($dModule == 'minitasks') {
            // minitasks exception
            $this->registry->set('action', 'ajax_save', true);
        }
        $this->registry->set('stop_redirects', true, true);

        // we will use model's controller for the dirty job
        $worker = ucfirst($dModule);
        if ($dController) {
            $worker .= '_' . str_replace(' ', '_', ucwords(str_replace('_', ' ', $dController)));
        }
        $worker .= '_Controller';
        $worker = new $worker($this->registry);

        if (!$multiple) {
            // simulation for multiple model creation
            // even if one model has to be created
            // less code - better readability
            $multiple = 'simulated_grouping_table';
            $sVars[$multiple] = array(
                'value' => array(1 => null),
                'grouping' => -1
            );
            if ($action == 'edit' && !empty($destination_model_id)) {
                //set destination id
                $settings['id'] = $destination_model_id;
                //remove the create_from_ModelName setting because the created model is already related
                unset($settings['create_from_' . strtolower($sModel->modelName)]);
            }
        }

        // data for origin of evaluated settings - used for error logging
        $origin = array(
            'table' => DB_TABLE_AUTOMATIONS,
            'field' => 'settings',
            'id' => $params['id'],
            'model' => $params['model']->modelName,
            'model_id' => $params['model']->get('id'),
        );

        //store the flag that defines whether the variables should be fetched from DB or POST
        $gov = $this->registry->get('get_old_vars');
        if (!empty($this->before_action) && $this->registry['request']->isPost()) {
            //in before action mode get the variables from POST
            $this->registry->set('get_old_vars', false, true);
        } else {
            //in any other case get the var values from DB
            $this->registry->set('get_old_vars', true, true);
        }

        //preserve messages
        $messages = clone $registry['messages'];

        $db->StartTrans();
        $modelNums = array();
        foreach ($sVars[$multiple]['value'] as $k => $v) {
            // iterate over settings(fields relations)
            $assignments = array();
            $allSource = array();
            foreach ($settings as $destination => $source) {
                if (preg_match('#^(validate(_message)?|create_condition)#', $destination)) {
                    // validation rule
                    continue;
                }
                //expand variables in each source field
                $matches = $this->extractVariables($source);
                if ($matches) {
                    foreach ($matches as $m) {
                        $val = '';
                        if (preg_match('#^a_#', $m)) {
                            // get values from additional variable
                            $val = preg_replace('#^a_#', '', $m);
                            if (!empty($sVars[$val]['value'])) {
                                if ($sVars[$val]['grouping'] == $sVars[$multiple]['grouping']) {
                                    // field in the same grouping table as the field that defines multiple records creation
                                    // get the value in the same row
                                    $val = $sVars[$val]['value'][$k];
                                } else {
                                    $val = $sVars[$val]['value'];
                                }
                            } else {
                                // no value for this variable or it does not exist
                                $val = '';
                            }
                        } elseif (preg_match('#^b_#', $m)) {
                            // get value from basic variable
                            $val = preg_replace('#^b_#', '', $m);
                            $val = $sModel->get($val);
                        } elseif (preg_match('#^assignments_#', $m)) {
                            $val = $sModel->getAssignments(preg_replace('#^assignments_#', '', $m));
                            $val = array_keys($val);
                        } elseif (preg_match('#^current_employee_user$#', $m)) {
                            $val = $this->getEmployeeUser($sModel);
                        } else {
                            // something different - for example [current_user] used in assign method
                            $val = '[' . $m .']';
                        }
                        $allSource[$m] = $val;
                        if (is_array($val)) {
                            if (empty($dVars[$destination]['grouping'])) {
                                // array source in non-array destination
                                $val = implode(', ', $val);
                            }
                        }
                        // escape both single and double quotes in replacement values
                        // because we cannot know what to expect in expression
                        $val = General::slashesEscape($val);
                        if (is_array($val)) {
                            // still array! duhh!  - array to array relation
                            // let's do some magic
                            // we have to expand each variable for each value(row)
                            $tmp = array();
                            if (!is_array($source)) {
                                $source = array($source);
                            }
                            foreach ($val as $t) {
                                foreach ($source as $s) {
                                    $tmp[] = preg_replace('#\[' . $m . '\]#', $t, $s);
                                }
                                $source = $tmp;
                                $tmp = array();
                            }
                            $source = $tmp;
                        } else {
                            // expand current variable in the source
                            $source = preg_replace('#\[' . $m . '\]#', $val, $source);
                        }
                    }
                    if (is_array($source) && count($source) == 1) {
                        // here if we have array to array relation - upper "if" statement
                        $source = reset($source);
                    }
                }
                // some magic again
                // just for less code
                if (!is_array($source)) {
                    $source = array($source);
                }
                $tmp = array();
                foreach ($source as $s) {
                    $tmp[] = $this->getVariableValue(
                        $s,
                        array(
                            'orig_str' => $settings[$destination],
                            'origin' => $origin,
                            //use the object model to get variables with methods (e.g., getVarValue)
                            strtolower($params['model']->modelName) => $params['model'],
                        )
                    );
                }

                // remove unnecessary backslashes after evaluation has been performed
                $tmp = General::slashesStrip($tmp);
                $source = $tmp;
                if (is_array($source) && count($source) == 1) {
                    $source = reset($source);
                }
                if (preg_match('#^(new_)?assign_#', $destination)) {
                    // special behaviour for assignments  - they will be processed later
                    $assignments[$destination] = $source;
                    continue;
                }
                $request->set($destination, $source, 'post', true);
            }

            if (!empty($settings['create_condition'])) {
                // it's time to check the conditions
                $num = 1;
                foreach ($allSource as $var => $vals) {
                    if (is_array($vals)) {
                        $allSource[$var] = array_values($vals);
                        if ($num < count($vals)) {
                            $num = count($vals);
                        }
                    }
                }
                for ($i = 0; $i < $num; $i++) {
                    $cond = $settings['create_condition'];
                    foreach ($allSource as $var => $vals) {
                        $val = '';
                        if (preg_match('#\[' . $var . '\]#', $cond)) {
                            if (is_array($vals) && isset($vals[$i])) {
                                $val = $vals[$i];
                            } elseif (!is_array($vals)) {
                                $val = $vals;
                            }
                            // escape both single and double quotes in replacement values
                            // because we cannot know what to expect in expression
                            $val = General::slashesEscape($val);
                            $cond = preg_replace('#\[' . $var . '\]#', $val, $cond);
                        }
                    }
                    $cond = EvalString::evaluate(
                        $registry,
                        $cond,
                        array(
                            'orig_str' => $settings['create_condition'],
                            'object' => $this,
                            'origin' => $origin,
                        ));
                    if (!$cond) {
                        continue 2;
                    }
                }
            }

            if (!empty($settings['validate'])) {
                $valid = $settings['validate'];
                $matches = $this->extractVariables($valid);
                foreach ($matches as $m) {
                    $valid = preg_replace('#\[' . $m . '\]#', '$req_data["' . $m . '"]', $valid);
                }
                $valid = EvalString::evaluate(
                    $registry,
                    $valid,
                    array(
                        'orig_str' => $settings['validate'],
                        'object' => $this,
                        'req_data' => $request->getAll('post'),
                        'origin' => $origin,
                    )
                );
                if (!$valid) {
                    if (!empty($settings['validate_message_' . $registry['lang']])) {
                        $this->_createModelsMsg(str_replace('[row]', $k, $settings['validate_message_' . $registry['lang']]));
                    } else {
                        $this->_createModelsMsg('error_automation_create_models_invalid_field', array('row' => $k));
                    }
                    $db->FailTrans();
                    continue;
                }
            }

            // yep! yep! hurayyyy!
            // we can create a new model....great
            $worker->execute();
            if (!$worker->actionCompleted) {
                $ph = array(
                    'row' => $k,
                    'model_name' => $this->i18n(strtolower($this->settings['create_model'])),
                    'source_name' => $sModel->get('name') ? $sModel->get('name') : $sModel->get('subject'),
                    'source_num' => $sModel->get('full_num') ? $sModel->get('full_num') : $sModel->get('num'),
                );
                $this->_createModelsMsg('error_automation_create_models_failed', $ph);
                $db->FailTrans();
                $errors = $this->registry['messages']->getErrors();
                // we will process error messages in function of message's key
                foreach ($errors as $e => $error) {
                    // search the erred field in relations and get matched source fields
                    if (!is_numeric($e)) {
                        if (isset($settings[$e])) {
                            $matches = $this->extractVariables($settings[$e]);
                            if ($matches) {
                                // get all fields used to build current destination field - REFLECTION
                                foreach ($matches as $m) {
                                    if (preg_match('#^a_#', $m)) {
                                        $m = preg_replace('#^a_#', '', $m);
                                        $ph = array('row' => $k, 'field_name' => $sVars[$m]['label']);
                                        $this->_createModelsMsg('error_automation_create_models_invalid_field', $ph, $m);
                                    } elseif (preg_match('#^b_#', $m)) {
                                        $m = preg_replace('#^b_#', '', $m);
                                        $layouts = $sModel->getLayoutsDetails();
                                        // IMPORTANT!!! No "row" for basic variables
                                        $ph = array('row' => '', 'field_name' => $layouts[$m]['name']);
                                        $this->_createModelsMsg('error_automation_create_models_invalid_field', $ph, $m);
                                    } else {
                                        // no relations for this field
                                        // just show the error
                                        $this->_createModelsMsg($error, array(), $e);
                                    }
                                }
                            } else {
                                // no relations for this field
                                // just show the error
                                $this->_createModelsMsg($error, array(), $e);
                            }
                        } else {
                            // no settings for this field
                            // just show the error
                            $this->_createModelsMsg($error, array(), $e);
                        }
                    } elseif ($e >= 0) {
                        // negative keys are just FAILED/SUCCESS
                        $this->_createModelsMsg($error, array(), $e);
                    }
                }
            } elseif (!empty($assignments)) {
                // prepare parameters for the assign automation method
                $assignments['model'] = $registry[strtolower($this->settings['create_model'])]->unsanitize();
                $assignments['model_id'] = $assignments['model']->get('id');
                $assignments['module'] = $dModule;
                $assignments['controller'] = $dController;
                if (strtolower($this->settings['create_model']) == 'event' && !empty($this->settings['auto_confirm_participants'])) {
                    $assignments['send_mail'] = false;
                    $assignments['auto_confirm_participants'] = $this->settings['auto_confirm_participants'];
                }

                if (!$this->assign($assignments)) {
                    $db->FailTrans();
                    $ph = array(
                        'row' => $k,
                        'model_name' => $this->i18n(strtolower($this->settings['create_model'])),
                        'source_name' => $sModel->get('name') ? $sModel->get('name') : $sModel->get('subject'),
                        'source_num' => $sModel->get('full_num') ? $sModel->get('full_num') : $sModel->get('num'),
                    );
                    $this->_createModelsMsg('error_automation_create_models_assign_failed', $ph);
                }
            }
            $worker->actionCompleted = false;
            if ($registry[strtolower($this->settings['create_model'])]) {
                $dModel = $registry[strtolower($this->settings['create_model'])];
                $num = $dModel->get('full_num') ? $dModel->get('full_num') : $dModel->get('num');
                if ($num) {
                    $modelNums[] = $num;
                }
            }
            $registry->remove(strtolower($this->settings['create_model']));
            $registry['messages']->flush();
        }

        //restore the flag that defines whether the variables should be fetched from DB or POST
        $this->registry->set('get_old_vars', $gov, true);

        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();
        if (!empty($dModel) && is_object($dModel) && $dModel->sanitized) {
            $dModel->unsanitize();
        }
        if (!$this->isCrontab) {
            // get type for the last created model - if we have one
            if (!empty($dModel) && is_object($dModel)) {
                $dModelType = $dModel->getModelType();
            } else {
                $dModelType = new Model($this->registry);
            }
            //singular view link
            if (empty($modelNums)) {
                if ($this->settings['create_model'] == 'minitask') {
                    $view_link = sprintf('%s?%s=%s&amp;%s=communications&amp;communications=%d&amp;communication_type=minitasks',
                        $_SERVER['PHP_SELF'],
                        $this->registry['module_param'],
                        $currentModule, $currentModule,
                        $sModel->get('id'));

                } else {
                    $view_link = sprintf('%s?%s=%s&amp;%s=view&amp;view=%d',
                        $_SERVER['PHP_SELF'],
                        $this->registry['module_param'],
                        $dModule, $dModule,
                        $dModel->get('id'));
                }
            }
            //restore messages
            $this->registry->set('messages', $messages, true);

            //placeholders for error/warning messages
            $ph = array(
                'add_or_edit' => mb_strtolower($action == 'add' ? $this->i18n('add') : $this->i18n('edit')),
                'model_names' => !empty($modelNums) ?
                    //plural
                    $dModelType->get('name_plural') ? $dModelType->get('name_plural') : $this->i18n(strtolower(General::singular2plural($this->settings['create_model']))) :
                    //singular
                    '<a href="' . $view_link . '" target="_blank">' . ($dModelType->get('name') ? $dModelType->get('name') : $this->i18n(strtolower($this->settings['create_model']))) . '</a>',
                'model_nums' => !empty($modelNums) ? ' (' . implode(', ', $modelNums) . ')' : '',
            );
            if ($result) {
                $this->registry['messages']->setMessage($this->i18n('message_automation_create_models', $ph));
            } else {
                $message_method = !empty($this->before_action) ? 'setError' : 'setWarning';
                $this->registry['messages']->$message_method($this->i18n('error_automation_create_models', $ph));
                foreach ($this->executionErrors as $k => $v) {
                    $this->registry['messages']->$message_method($v, !is_numeric($k) ? $k : '');
                }
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
        // restore originals
        $this->registry->set('module', $currentModule, true);
        $this->registry->set('controller', $currentController, true);
        $this->registry->set('action', $currentAction, true);
        $this->registry->set('request', $currentRequest, true);
        if (isset($au)) {
            $this->registry->set('currentUser', $au, true);
        }
        //Restore language file
        $file = PH_MODULES_DIR . $dModule . '/i18n/'.$this->registry['lang'].'/' . General::singular2plural(strtolower($this->settings['create_model'])) . '.ini';
        $this->registry['translater']->unloadFile($file);
        $file = PH_MODULES_DIR . $sModule . '/i18n/' . $this->registry['lang'] . '/' . General::singular2plural(strtolower($sModel->modelName)) . '.ini';
        $this->registry['translater']->loadFile($file);

        return $result;
    }

    /**
     * Prepares plugin automations controller model to be used from elsewhere
     *
     * @param Registry $registry - the main registry
     * @param array $params - params to search by - either by id, or by method name
     * @return Automations_Controller|false - plugin controller or false on failure
     */
    public static function getPluginController(&$registry, $params = array()) {
        $query = 'SELECT * FROM ' . DB_TABLE_AUTOMATIONS . ' WHERE method LIKE \'%plugin%\' AND ';
        if (!empty($params['id'])) {
            $query .= 'id = ' . intval($params['id']);
        } elseif (!empty($params['method'])) {
            $query .= 'method LIKE \'%' . General::slashesEscape($params['method']) . '%\'';
        } else {
            $query .= '0';
        }
        $query .= ' LIMIT 1';

        $record = $registry['db']->GetRow($query);
        $controller = false;
        if ($record) {
            $record['method'] = General::parseSettings($record['method']);
            if (!empty($record['method']['plugin']) && !empty($record['method']['method'])) {
                $filename = sprintf(
                    PH_MODULES_DIR . 'automations/plugins/%1$s/controllers/%1$s.automations.controller.php',
                    $record['method']['plugin']
                );
                if (file_exists($filename)) {
                    require_once $filename;

                    //load plugin i18n files
                    $i18n_file = sprintf(
                        PH_MODULES_DIR . 'automations/plugins/%1$s/i18n/%2$s/%1$s.ini',
                        $record['method']['plugin'], $registry['lang']
                    );
                    $registry['translater']->loadFile($i18n_file);

                    $controller = str_replace(' ', '_', ucwords(str_replace('_', ' ', $record['method']['plugin']))) . '_Automations_Controller';
                    if (method_exists($controller, $record['method']['method'])) {
                        $controller = new $controller($registry);
                        $controller->getSettings($record);
                        if ($record['automation_type'] == 'before_action') {
                            $controller->before_action = 1;
                        } elseif ($record['automation_type'] == 'before_viewer') {
                            $controller->before_viewer = 1;
                        }
                    } else {
                        $controller = false;
                    }
                }
            }
        }

        return $controller;
    }

    /**
     * Automation to write the history of modified variables into a grouping table
     */
    public function writeCustomHistory($params) {
        if (!$this->registry['request']->isPost()) {
            //should always work after post
            return true;
        }
        $settings = $this->getSettings($params);
        if (preg_match('#^add#', $this->registry->get('action')) && !empty($settings['history_log_skip_add'])) {
            //skip logging of history in add/addquick action
            return true;
        }
        $model = $params['model'];
        if (!$model->get('id')) {
            //no model specified
            return true;
        }
        if (!defined(strtoupper('DB_TABLE_' . General::singular2plural($model->modelName) . '_HISTORY'))) {
            //cannot find history db table
            return true;
        }

        //IMPORTANT: get the model from the database,
        // because the previous automation might change it and might not update it in $params['model']
        $factory_class = $model->getFactory();
        $factory_alias = $factory_class::getAlias($params['module'], $params['controller']);
        $filters = array(
            'where' => array(
                $factory_alias . '.id = ' . $model->get('id')
            ),
            'model_lang' => $model->get('model_lang')
        );
        $model = $factory_class::searchOne($this->registry, $filters);
        $model->unsanitize();

        $db = $this->registry['db'];

        /**
         * IMPORTANT: get the last audit IMMEDIATELY after the action so that if writeCustomHistory is executed more than once
         */
        static $last_history_log;
        if (empty($last_history_log)) {
            //get the date and time, user and employee of the last and last but one modifications
            // IMPORTANT: get this data from the db because the current user might not be the user modified the model
            $model_history_db_table = constant(strtoupper('DB_TABLE_' . General::singular2plural($model->modelName) . '_HISTORY'));
            $model_audit_db_table = constant(strtoupper('DB_TABLE_' . General::singular2plural($model->modelName) . '_AUDIT'));
            $query = 'SELECT h.*, TRIM(CONCAT(ui18n.firstname, " ", ui18n.lastname)) as user_name, ' . "\n" .
                     '       u.employee as employee_id, TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) as employee_name' . "\n" .
                     'FROM ' . $model_history_db_table . ' as h' .  "\n" .
                     'JOIN ' . DB_TABLE_USERS . ' as u' . "\n" .
                     ' ON h.user_id = u.id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' as ui18n'. "\n" .
                     ' ON h.user_id = ui18n.parent_id AND ui18n.lang="' . $model->get('model_lang') . '"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n'. "\n" .
                     ' ON u.employee = ci18n.parent_id AND ci18n.lang="' . $model->get('model_lang') . '"' . "\n" .
                     'WHERE model_id=' . $model->get('id') . ' ORDER BY h_id DESC LIMIT 2';
            $last_history_log = $db->GetRow($query);

            if (empty($last_history_log)) {
                //sorry, no history found
                return true;
            }
            $last_history_log['data'] = unserialize($last_history_log['data']);

            //get the audit data
            $query = 'SELECT a.field_name, a.* FROM ' . $model_audit_db_table . ' as a WHERE parent_id=' . $last_history_log['h_id'];
            $last_history_log['audit'] = $db->GetAssoc($query);

            if (empty($last_history_log['audit'])) {
                //no audit, no changes, no custom history
                return true;
            }
        }

        //prepare list of fields to check in audit
        $fields_to_log = array();
        $vars_relations = array();
        foreach($settings as $key => $value) {
            if (preg_match('#^history_log_var_(.*)$#', $key, $matches)) {
                if (preg_match('#(_old)$#', $matches[1])) {
                    $fields_to_log[] = preg_replace('#(_old)$#', '', $matches[1]);
                    $vars_relations[$matches[1]] = $value;
                } else {
                    $fields_to_log[] = $matches[1];
                    $vars_relations[$matches[1]] = $value;
                }
            }
        }

        //IMPORTANT: only auditable variables should be logged in custom history
        if (!array_intersect($fields_to_log, array_keys($last_history_log['audit']))) {
            //none of the fields to log have been modified
            return true;
        }

        //prepare predefined (system) variables
        $predefined_vars = array(
            //date and time
            'datetime_modified' => $last_history_log['h_date'],
            'date_modified' => General::strftime('%Y-%m-%d', $last_history_log['h_date']),
            'time_modified' => General::strftime('%H:%M', $last_history_log['h_date']),

            //user
            'user_modified' => $last_history_log['user_id'],
            'user_name_modified' => $last_history_log['user_name'],

            //employee
            'employee_modified' => $last_history_log['employee_id'],
            'employee_name_modified' => $last_history_log['employee_name'],
        );

        // get the variables as associative array from the DB
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $model_assoc_vars = $model->getAssocVars();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        //now get the variable values
        $values = array();
        foreach($settings as $setting => $val) {
            if (!preg_match('#^history_log_var_(.*)$#', $setting, $matches)) {
                continue;
            }
            $get_old = false;
            if (preg_match('#(_old)$#', $matches[1])) {
                $field_name = preg_replace('#(_old)$#', '', $matches[1]);
                $get_old = true;
            } else {
                $field_name = $matches[1];
            }

            if (isset($last_history_log['audit'][$field_name]) ||
                //IMPORTANT: the customer_name, project_name, etc. are not stored in the audit tables
                preg_match('#_name$#', $field_name) && isset($last_history_log['audit'][preg_replace('#_name$#', '', $field_name)])) {
                //the field is modified

                //define field for old or new value
                $audit_source_field = ($get_old) ? 'old_value' : 'field_value';
                if ($model && !$get_old) {
                    //try to get it from the model
                    if ($model->isDefined($field_name)) {
                        //basic var
                        if ($field_name == 'status' && !preg_match('#[a-z]+_[0-9]+#', $model->get($field_name))) {
                            //special behaviour for statuses
                            $field_value = $model->get('status');
                            if ($model->get('substatus')) {
                                $field_value .= '_' . $model->get('substatus');
                            }
                        } else {
                            $field_value = $model->get($field_name);
                        }
                    } else {
                        //additional var (without formatting, ignore permissions)
                        $field_value = $model->getPlainVarValue($field_name, true, true);
                    }
                    $values[$val] = $field_value;
                } else {
                    //get it from the audit table
                    if ($field_name == 'status') {
                        //special behaviour for statuses
                        $field_value = $last_history_log['audit'][$field_name][$audit_source_field];
                        if (!empty($last_history_log['audit']['substatus'][$audit_source_field])) {
                            $field_value .= '_' . $last_history_log['audit']['substatus'][$audit_source_field];
                        }
                        $values[$val] = $field_value;
                    } else {
                        $values[$val] = $last_history_log['audit'][$field_name][$audit_source_field];
                    }
                }

            } elseif (isset($predefined_vars[$field_name])) {
                //predefined (system) variable
                $values[$val] = $predefined_vars[$field_name];
            } elseif (!empty($settings['history_log_only_modified'])) {
                //the field is NOT modified AND only modified should be logged
                //log a dash or whatever is defined as setting
                $unmodified_value_string = !empty($settings['history_log_unmodified_value_string']) ? $settings['history_log_unmodified_value_string'] : '';
                if ($unmodified_value_string != '') {
                    //IMPORTANT: for radio/dropdowns/date/datetime/time always log empty value (because of the formatting of date fields and option values of radios and dropdowns)
                    if (isset($model_assoc_vars[$field_name])) {
                        //additional variables
                        $values[$val] = in_array($model_assoc_vars[$field_name]['type'], array('radio', 'dropdown', 'date', 'datetime', 'time')) ? '' : $unmodified_value_string;
                    } else {
                        $values[$val] = in_array($field_name, array('date', 'deadline', 'added', 'modified')) ? '' : $unmodified_value_string;
                    }
                }
            } else {
                //the variable is not stored in the audit table
                //try to get it from the model
                if ($model->isDefined($field_name)) {
                    //basic var
                    if ($field_name == 'status' && !preg_match('#[a-z]+_[0-9]+#', $model->get($field_name))) {
                        //special behaviour for statuses
                        $field_value = $model->get('status');
                        if ($model->get('substatus')) {
                            $field_value .= '_' . $model->get('substatus');
                        }
                    } else {
                        $field_value = $model->get($field_name);
                    }
                } else {
                    //additional var (without formatting, ignore permissions)
                    $field_value = $model->getPlainVarValue($field_name, true, true);
                }
                $values[$val] = $field_value;
            }
        }

        if (empty($values)) {
            //nothing to save
            return true;
        }

        // define which is the num for the new row of the history
        // get the last row with at least one variable filled
        $max_num = 0;
        foreach($values as $var_name => $var_value) {
            for($i=$max_num; $i <= count($model_assoc_vars[$var_name]['value']);$i++) {
                if (isset($model_assoc_vars[$var_name]['value'][$i]) && $model_assoc_vars[$var_name]['value'][$i] !== '') {
                    $max_num = $i;
                }
            }
        }
        $num_row = $max_num + 1;

        //set values in history grouping table
        foreach($values as $var_name => $var_value) {
            if (is_array($var_value)) {
                $key_v = array_search($var_name, $vars_relations);
                array_walk($var_value, array('self', '_processValueForCustomHistory'), $model_assoc_vars[$key_v]);
                $model_assoc_vars[$var_name]['value'][$num_row] = implode("\n", $var_value);
            } else {
                $model_assoc_vars[$var_name]['value'][$num_row] = $var_value;
            }
        }

        $new_model = clone $model;
        $this->registry->set('edit_all', true, true);
        $new_model->unsetProperty('old_model', true);
        $new_model->unsetProperty('assoc_vars', true);
        $new_model->set('vars', array_values($model_assoc_vars), true);
        $new_model->unsanitize();
        $result = $new_model->replaceVars();
        if ($result) {
            $new_model->slashesStrip();
            $history_class = $factory_class . '_History';
            $history_class::saveData(
                $this->registry,
                array(
                    'action_type' => $this->action,
                    'model' => $new_model,
                    'new_model' => $new_model,
                    'old_model' => $model,
                ));
            // set updated values into model used in automations
            $model->set('vars', array_values($model_assoc_vars), true);
            $model->set('assoc_vars', $model_assoc_vars, true);
        }
        $this->registry->set('edit_all', false, true);
        unset($new_model);

        return $result;
    }

    /**
     * Copies matching data from specified model into current model and saves it.
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function copyVars(array $params) {

        /** @var Model $model */
        $model = $params['model'];
        // model should be already saved in db and have additional variables but not be a financial record
        if (!$model->get('id') || strpos($model->modelName, 'Finance_') === 0 ||!$model->checkForVariables()) {
            return true;
        }

        // prepare necessary automation settings
        $this->getSettings($params);

        // source variables to ignore
        $this->settings['ignore_copy_vars'] = !empty($this->settings['ignore_copy_vars']) ? preg_split('#\s*,\s*#', $this->settings['ignore_copy_vars']) : array();
        // settings to copy vars with different name (for now only additional => additional)
        $this->settings['copy_vars'] = array(
            'aa' => array(),
        );
        $s_matches = $d_matches = array();
        foreach ($this->settings as $key => $value) {
            if (!is_string($value)) {
                continue;
            }
            if (preg_match('/^copy_(a|b|dt|usr)_(.+)/', $key, $s_matches) &&
            preg_match('/^([ab])_(type)*([0-9]+)*_*(.+)/', $value, $d_matches)) {
                //like copy_a_total := a_contracted_total
                $this->settings['copy_vars'][$s_matches[1].$d_matches[1]][$s_matches[2]] = $d_matches[4];
            }
        }
        $this->loadI18NFiles();

        $result = true;
        /** @var ADODB_mysqli $db */
        $db = &$this->registry['db'];
        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        if ($model->isSanitized()) {
            $model->unsanitize();
            $sanitize_after = true;
        }

        // define source model according to automation settings and current model data
        $source_model_name = !empty($this->settings['source_model_name']) ? $this->settings['source_model_name'] : '';
        $source_model_id = 0;
        $id_var = '';
        // if source model id var is specified in request, get it from there
        if ($this->registry['request']->get('source_model_id_var')) {
            $this->settings['source_model_id_var'] = $this->registry['request']->get('source_model_id_var');
        }
        if (!empty($this->settings['source_model_id_var'])) {
            $id_var = $this->settings['source_model_id_var'];

            // check if field is set as a property of model
            if ($model->isDefined($id_var)) {
                $source_model_id = $model->get($id_var);
                // define model name according to basic var
                switch ($id_var) {
                    case 'employee':
                        $source_model_name = 'Customer';
                        break;
                    case 'trademark':
                        $source_model_name = 'Nomenclature';
                        break;
                    case 'customer':
                    case 'project':
                    case 'contract':
                        $source_model_name = ucfirst($id_var);
                        break;
                }
            }
            // check if field is an additional variable of model
            if (!$source_model_id || !$source_model_name) {
                $dest_vars = $model->getAssocVars();
                if (array_key_exists($id_var, $dest_vars) && !empty($dest_vars[$id_var]['value'])) {
                    $source_model_id = $dest_vars[$id_var]['value'];
                    // define model name according to additional var
                    if (!empty($dest_vars[$id_var]['cstm_model'])) {
                        $source_model_name = $dest_vars[$id_var]['cstm_model'];
                    }
                }
            }
            if (is_array($source_model_id)) {
                $source_model_id = array_filter($source_model_id);
                $source_model_id = reset($source_model_id);
            }
            $source_model_id = filter_var(
                $source_model_id,
                FILTER_VALIDATE_INT,
                array(
                    'options' => array(
                        'default' => 0,
                        'min_range' => 1,
                    ),
                )
            );
        }

        if ($source_model_id && $source_model_name && class_exists($source_model_name) && is_subclass_of($source_model_name, 'Model')) {
            /** @var Model_Factory $source_model_factory */
            $source_model_factory = General::singular2plural($source_model_name);
            /** @var Model $source_model */
            $source_model = $source_model_factory::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        $source_model_factory::getAlias($source_model_factory, '') . '.id = ' . $source_model_id,
                    ),
                    // get data in the model lang of the destination model
                    'model_lang' => $model->get('model_lang'),
                ));
        }

        // collect only the destination variables that will be replaced
        // and set the source values into them
        $copy_vars = $multilang_copy_vars = $bb_copy_vars = array();

        // check for variables in source model
        if (!empty($source_model) && $source_model->checkForVariables()) {
            $source_vars = $source_model->getAssocVars(false, false);
            $dest_vars = $model->getAssocVars(false, false);
            foreach ($dest_vars as $var_name => $var) {
                $source_name = '';
                if (in_array($var_name, $this->settings['copy_vars']['aa']) && array_key_exists(array_search($var_name, $this->settings['copy_vars']['aa']), $source_vars)) {
                    //additional to additional
                    $source_name = array_search($var_name, $this->settings['copy_vars']['aa']);
                } elseif (array_key_exists($var_name, $source_vars) && !in_array($var_name, $this->settings['ignore_copy_vars'])) {
                    //same names
                    $source_name = $var_name;
                }
                if ($source_name) {
                    // do some value conversions where possible or ignore copying when not
                    if (!empty($source_vars[$source_name]['bb'])) {
                        // bb can be copied only to bb
                        if (!in_array('bb_group', $this->settings['ignore_copy_vars']) &&
                        !empty($var['bb']) &&
                        in_array($var['type'], array('group', 'gt2', 'config')) &&
                        $source_vars[$source_name]['type'] == $var['type']) {
                            // get the inner bb vars the way they are prepared for display in template
                            if (!isset($bb_vars_for_template)) {
                                $tmp_model = clone $model;
                                $tmp_model->unsetProperty('old_model', true);
                                $tmp_model->unsetProperty('assoc_vars', true);
                                $tmp_model->getVarsForTemplate(false);
                                $bb_vars_for_template = array();
                                foreach ($tmp_model->get('vars') as $var_for_template) {
                                    if (!empty($var_for_template['bb']) && $var_for_template['type'] != 'bb') {
                                        $bb_vars_for_template[$var_for_template['id']] = $var_for_template;
                                    }
                                }
                                unset($tmp_model);
                            }
                            // collect the mapping of group/gt2/config variables in bb
                            if (array_key_exists($var['id'], $bb_vars_for_template)) {
                                $bb_copy_vars[$source_vars[$source_name]['id']] = array(
                                    'meta_id' => $var['id'],
                                    'bb_num' => $var['bb'],
                                    'var' => $bb_vars_for_template[$var['id']],
                                );
                            }
                        }
                        continue;
                    } elseif (!empty($var['bb'])) {
                        // non-bb to bb - not possible
                        continue;
                    } elseif ($var['gt2']) {
                        // gt2 can be copied only to gt2 (the whole table)
                        if (!$source_vars[$source_name]['gt2'] || $var['type'] != 'gt2') {
                            continue;
                        }

                        // mark existing rows as deleted
                        array_walk($var['values'], function(&$row) { if ($row) { $row['deleted'] = '1'; } });
                        $var['values'] = $source_vars[$source_name]['values'] + array_filter($var['values']);

                        // check if only one of the records has file_upload fields
                        foreach ($source_vars[$source_name]['vars'] as $v_name => $v_info) {
                            if ($v_info['type'] == 'file_upload' xor (!empty($var['vars'][$v_name]) && $var['vars'][$v_name]['type'] == 'file_upload')) {
                                foreach ($var['values'] as $row_num => $row_values) {
                                    if (!empty($row_values)) {
                                        $var['values'][$row_num][$v_name] = '';
                                    }
                                }
                            }
                        }
                        $var['plain_values'] = $source_vars[$source_name]['plain_values'];
                        // add flag in order to save GT2 var
                        $this->registry['request']->set('gt2_requested', true, true);
                        // add flag that values are set in model and should not be taken from request
                        $model->set('table_values_are_set', true, true);
                    } else {
                        if (!array_key_exists('value', $source_vars[$source_name])) {
                            $source_vars[$source_name]['value'] = $source_vars[$source_name]['grouping'] || $source_vars[$source_name]['type'] == 'checkbox_group' ? array() : '';
                        }
                        if (!(($source_vars[$source_name]['grouping'] || $source_vars[$source_name]['type'] == 'checkbox_group') xor ($var['grouping'] || $var['type'] == 'checkbox_group'))) {
                            // source and destination values are of the same type (single or array)
                            $var['value'] = $source_vars[$source_name]['value'];
                        } elseif ($var['grouping'] || $var['type'] == 'checkbox_group') {
                            // single to array
                            $var['value'] = array($source_vars[$source_name]['value']);
                        } elseif ($source_vars[$source_name]['grouping']) {
                            // array to single
                            $var['value'] = reset($source_vars[$source_name]['value']);
                        }
                    }
                    //
                    $copy_vars[$var_name] = $var;
                    // collect multilang vars - corresponding source name and destination variable
                    if (!empty($var['multilang'])) {
                        $multilang_copy_vars[$source_name] = $copy_vars[$var_name];
                    }
                }
            }
        }

        // if any vars should be copied
        if ($copy_vars || $bb_copy_vars) {
            // make sure translations are up to date (because there is an auto-translate automation)
            $model->unsetProperty('translations', true);
            $translations = $model->getTranslations();
            $this->registry->set('edit_all', true, true);
            // variables that should be temporarily unset and then restored
            $unset_vars = array_fill_keys(array('layout_place_from', 'layout_place_to', 'translations', ), null);
            foreach ($unset_vars as $var_name => $not_important) {
                if ($model->isDefined($var_name)) {
                    $unset_vars[$var_name] = $model->get($var_name);
                    $model->unsetProperty($var_name, true);
                } else {
                    unset($unset_vars[$var_name]);
                }
            }

            // save multilang vars in current model lang and in any lang
            // source model has no translation (autotranslate them)
            $model->set('translations', array_unique(array_merge(array($model->get('model_lang')), array_diff($translations, $source_model->getTranslations()))), true);

            $db->StartTrans();

            $model->set('vars', array_values($copy_vars), true);
            $model->set('assoc_vars', $copy_vars, true);

            //escape the variables before saving
            $model->slashesEscape(true);

            // update only the copied additional vars
            if ($model->replaceVars()) {

                $model->unsetVars();
                $model->slashesStrip();

                // if bb values should be copied
                if ($bb_copy_vars) {
                    // get source data from db
                    $source_bb_vars = $source_model->getBB(array('model_id' => $source_model->get('id')));
                    $source_add_bb_vars = $source_model->getBBFields();
                    // multilang bb var in caption row (check only for matching names)
                    foreach ($source_add_bb_vars as $var_name => $var) {
                        if (array_key_exists($var_name, $dest_vars) && $dest_vars[$var_name]['multilang'] && $dest_vars[$var_name]['bb'] && !$dest_vars[$var_name]['grouping'] && !$dest_vars[$var_name]['gt2'] && !$dest_vars[$var_name]['configurator']) {
                            $multilang_copy_vars[$var_name] = $dest_vars[$var_name];
                        }
                    }

                    // delete all old bb data (including any plain vars)
                    $bb_num = reset($bb_copy_vars);
                    $bb_num = $bb_num['bb_num'];
                    $model->delBB(array(
                        'model_id' => $model->get('id'),
                        'model_type' => $model->get('type'),
                        'bb_num' => $bb_num,
                    ));

                    // insert new bb rows for the selected/possible variants
                    foreach ($source_bb_vars as $bb_row) {
                        // this variant should not be copied because there
                        // is no corresponding inner bb variable or it should be ignored
                        if (!array_key_exists($bb_row['meta_id'], $bb_copy_vars)) {
                            continue;
                        }
                        // save all data of a new bb row
                        $model->saveBB($bb_copy_vars[$bb_row['meta_id']] + array(
                            'bb_id' => '0',
                            'data' => $bb_row['params'],
                            'values' => array_map(
                                function($bb_var) use ($bb_row) {
                                    return !empty($bb_var['value']) && array_key_exists($bb_row['id'], $bb_var['value']) ?
                                    $bb_var['value'][$bb_row['id']] : '';
                                },
                                $source_add_bb_vars
                            ),
                        ));
                    }
                }

                // if source model has same translations as model,
                // multilang values in this language should be copied (not autotranslated)
                $copy_langs = array_intersect(array_diff($translations, array($model->get('model_lang'))), $source_model->getTranslations());

                if ($copy_langs && $multilang_copy_vars) {
                    // object to use for saving copied multilang data
                    $t_model = clone $model;
                    // save vars as if action is translate (only multilang ones and only in current model lang)
                    $t_model->set('translate_multilang', true, true);
                    // flag to use set GT2 values, not to auto-translate
                    $t_model->set('translation_values_are_set', true, true);

                    foreach ($copy_langs as $t_lang) {
                        $t_model->set('model_lang', $t_lang, true);
                        $t_model->set('translations', array($t_lang), true);

                        // get source vars in current lang and set them into model
                        $t_source_model = clone $source_model;
                        $t_source_model->unsetVars();
                        $t_source_model->set('model_lang', $t_lang, true);
                        $t_source_vars = $t_source_model->getAssocVars(false, false);
                        if ($bb_copy_vars) {
                            $t_source_vars = $t_source_model->getBBFields() + $t_source_vars;
                        }

                        foreach ($t_source_vars as $var_name => $var) {
                            if (array_key_exists($var_name, $multilang_copy_vars)) {
                                if ($var['gt2']) {
                                    // GT2 rows have already been saved for first model_lang - now get them and translate them
                                    $multilang_copy_vars[$var_name] = $t_model->getGT2Vars();

                                    // on translate copy the values of multilang variables
                                    // into the rows of the new model which have already been saved
                                    $new_gt2_values = array_values($multilang_copy_vars[$var_name]['values']);
                                    $old_gt2_values = array_values($var['values']);
                                    foreach ($new_gt2_values as $idx => $row) {
                                        if (empty($row) || empty($old_gt2_values[$idx])) {
                                            continue;
                                        }
                                        foreach ($multilang_copy_vars[$var_name]['vars'] as $gt2_var_name => $gt2_var) {
                                            if ($gt2_var['multilang']) {
                                                $new_gt2_values[$idx][$gt2_var_name] = $old_gt2_values[$idx][$gt2_var_name];
                                            }
                                        }
                                    }
                                    $multilang_copy_vars[$var_name]['values'] = array_combine($multilang_copy_vars[$var_name]['rows'], $new_gt2_values);
                                    $multilang_copy_vars[$var_name]['plain_values']['total_no_vat_reason_text'] = $var['plain_values']['total_no_vat_reason_text'];
                                    continue;
                                } elseif ($var['grouping']) {
                                    if (!empty($var['value']) && is_array($var['value'])) {
                                        // normalize row indexes, starting from 1 -
                                        // they should match indexes in db saved in first model lang
                                        $var['value'] = array_values($var['value']);
                                        array_unshift($var['value'], '');
                                        unset($var['value'][0]);
                                    }
                                } elseif ($var['bb']) {
                                    if ($var['grouping'] || $var['gt2'] || $var['configurator']) {
                                        // this should not happen just make sure data is correct
                                        unset($multilang_copy_vars[$var_name]);
                                        continue;
                                    }
                                    if (!empty($var['value']) && is_array($var['value'])) {
                                        // get inserted row ids using model in main lang
                                        $bb_ids = array_keys($model->getValues($var) ?: array());
                                        // combine ids of inserted rows as keys and copied values
                                        $var['value'] = count($bb_ids) == count($var['value']) ? array_combine($bb_ids, $var['value']) : array();
                                    }
                                }

                                if (!array_key_exists('value', $var)) {
                                    $var['value'] = $var['grouping'] || $var['bb'] || $var['type'] == 'checkbox_group' ? array() : '';
                                }
                                if (!(($multilang_copy_vars[$var_name]['grouping'] || $multilang_copy_vars[$var_name]['type'] == 'checkbox_group') xor ($var['grouping'] || $var['type'] == 'checkbox_group'))) {
                                    // source and destination values are of the same type (single or array)
                                    $multilang_copy_vars[$var_name]['value'] = $var['value'];
                                } elseif ($multilang_copy_vars[$var_name]['grouping'] || $multilang_copy_vars[$var_name]['type'] == 'checkbox_group') {
                                    // single to array
                                    $multilang_copy_vars[$var_name]['value'] = array($var['value']);
                                } elseif ($var['grouping']) {
                                    // array to single
                                    $multilang_copy_vars[$var_name]['value'] = reset($var['value']);
                                }
                            }
                        }
                        // set prepared multilang values for save
                        $t_model->set('vars', array_values($multilang_copy_vars), true);

                        if (!$t_model->replaceVars()) {
                            $db->FailTrans();
                        }
                    }

                    // restore state
                    $model->unsetProperty('translation_values_are_set', true);
                }

                // if model is a contract and GT2 values have been copied,
                // SYSFIT template should be updated as well (same as in edittab action)
                if ($model->modelName == 'Contract' && array_key_exists('group_table_2', $copy_vars) && !$model->createSysfit('edit')) {
                    $db->FailTrans();
                }

                // get up-to date values in model (these will be available in 'model' param of next automations)
                $model->getAssocVars(true, false);

                $old_model = clone $model;
                $old_model->set('assoc_vars', $dest_vars, true);
                $old_model->set('vars', array_values($dest_vars), true);

                /** @var History $history_name */
                $history_name = General::singular2plural($model->modelName) . '_History';
                $history_name::saveData(
                    $this->registry,
                    array(
                        'action_type' => 'edit',
                        'model' => $model,
                        'new_model' => $model,
                        'old_model' => $old_model,
                    ));
                // some history classes sanitize models
                if ($model->isSanitized()) {
                    $model->unsanitize();
                }
            } else {
                $db->FailTrans();
            }

            // get field label and value where source model is specified
            $source_model_label = '';
            if ($model->isDefined($id_var)) {
                $source_model_label = $model->getLayoutName($id_var, false);
                if ($source_model_label && $model->get("{$id_var}_name")) {
                    $source_model_label .= ': ' . $model->get("{$id_var}_name");
                }
            }
            if (!$source_model_label && !empty($dest_vars)) {
                if (array_key_exists($id_var, $dest_vars) && !empty($dest_vars[$id_var]['label'])) {
                    $source_model_label = $dest_vars[$id_var]['label'];
                }
                foreach ($dest_vars as $var) {
                    if ($var['type'] == 'autocompleter' && !empty($var['autocomplete']['id_var']) && $var['autocomplete']['id_var'] == $id_var) {
                        $source_model_label = $var['label'] . ': ' . $var['value'];
                        break;
                    }
                }
            }

            // if action has failed, set vars in their initial state in model
            if ($db->HasFailedTrans()) {
                $model->set('assoc_vars', $dest_vars, true);
                $model->set('vars', array_values($dest_vars), true);

                $this->registry['messages']->setWarning(sprintf($this->i18n('error_automations_copy_vars', array($source_model_label))));
                foreach ($this->registry['messages']->getErrors() as $key => $error) {
                    $this->registry['messages']->setWarning($error, $key);
                }
                $this->registry['messages']->unset_vars('errors');
            } else {
                $this->registry['messages']->setMessage(sprintf($this->i18n('message_automations_copy_vars', array($source_model_label))));
            }
            $this->registry['messages']->insertInSession($this->registry);

            // restore state
            unset($this->registry['edit_all']);
            foreach ($unset_vars as $var_name => $var_value) {
                $model->set($var_name, $var_value, true);
            }

            // get result and complete transaction
            $result = !$db->HasFailedTrans();
            $db->CompleteTrans();
        }

        // restore state
        if (!empty($sanitize_after)) {
            $model->sanitize();
        }
        if (!$gov) {
            $this->registry->remove('get_old_vars');
        }

        return $result;
    }

    /**
     * Automation to translate models automatically
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function autoTranslate(array $params) {

        /** @var Model $model */
        $model = $params['model'];
        // model should be already saved in db; skip some actions that do not provide a model
        if (!$model->get('id') || in_array($this->registry['action'], array('audit', 'calculate'))) {
            return true;
        }

        //check if the model has been translated
        $translations = $model->getTranslations();
        $langs = preg_split('#\s*,\s*#', $this->registry['config']->getParam('i18n', 'model_langs'));
        // we need to make sure that model has a translation in the source language
        $source_model_lang = $translations ? $translations[0] : $model->get('model_lang');

        sort($translations);
        sort($langs);
        $translate_to = array_diff($langs, $translations);

        if (empty($translations) || empty($translate_to)) {
            //nothing to translate to
            return true;
        }

        // temporarily set some data in request which might be already present (on save, for example)
        $orig_values = array();
        foreach (array('model_lang') as $orig) {
            if ($this->registry['request']->isRequested($orig)) {
                $method_key = $this->registry['request']->isPost($orig) ? 'post' : 'get';
                $orig_values[$method_key][$orig] = $this->registry['request']->get($orig, $method_key);
            }
        }

        //construct the factory name
        /** @var Model_Factory $factory_name */
        $factory_name = General::singular2plural($model->modelName);

        $module = $model->getModule();
        $controller = $model->getController();

        //get the alias
        $alias = $factory_name::getAlias($module, $controller);

        //compose the filters
        $filters = array(
            'where' => array(
                $alias . '.id = ' . $model->get('id'),
            ),
            'model_lang' => $source_model_lang,
        );

        //get the model from the db in the language of the model object from $params
        $model = $factory_name::searchOne($this->registry, $filters);

        if ($model->isSanitized()) {
            $model->unsanitize();
        }

        // do some specific processing in order to be able to save data
        switch ($model->modelName) {
            case 'User':
                $model->unsetProperty('password', true);
                break;
            case 'Nomenclature':
                //do not update the categories upon translation
                $this->registry['request']->remove('update_categories');
                break;
        }

        // all data from $request (POST) should be ignored when settind values
        // of additional variables, no matter what the current action is
        $gov = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);

        //change the action to 'translate' because some of the validate methods use this as a flag
        // IMPORTANT: when action is "translate" multilang additional vars are saved in current model lang
        // for all models that have additional variables
        $action = $this->registry['action'];
        $this->registry->set('action', 'translate', true);
        // we need to be able to translate readonly layouts
        $this->registry->set('edit_all', true, true);

        foreach ($translate_to as $lang) {
            // model lang to be translated into
            $model->set('model_lang', $lang, true);
            // plugin autocompleters might be using model_lang from request, so set it there as well
            $this->registry['request']->set('model_lang', $lang, 'get', true);

            $model->unsetVars();
            // if model is a non-financial record with additional variables
            if ($model->checkForVariables()) {
                // prepare auto-translated (or blank) values of multilang variables
                $model->getVars();
            } elseif (strpos($model->modelName, 'Finance_') === 0 && $model->get('type') != 0) {
                // if model has no GT2, method will just return
                $model->getGT2Vars();
            }

            // old model is needed for history and audit
            $old_model = clone $model;

            if ($model->save()) {
                // model properties have been escaped for saving in db
                $model->slashesStrip();

                //show corresponding message
                $message_key = '';
                foreach (array($model->modelName, $factory_name) as $name_key) {
                    if ($this->registry['translater']->isSetParam('', 'message_' . strtolower($name_key) . '_translate_success')) {
                        $message_key = 'message_' . strtolower($name_key) . '_translate_success';
                        break;
                    }
                }
                if ($message_key) {
                    $this->registry['messages']->setMessage($this->i18n($message_key, array($old_model->getModelTypeName())), '', 100);
                }

                //save history for translation in each language
                $history_class = $factory_name . '_History';
                if (class_exists($history_class)) {
                    $filters = array(
                        'where' => array(
                            $alias . '.id = ' . $model->get('id'),
                        ),
                        'model_lang' => $lang,
                    );
                    /** @var Model $new_model */
                    $new_model = $factory_name::searchOne($this->registry, $filters);
                    if ($new_model->checkForVariables()) {
                        $new_model->getVars();
                    } elseif (strpos($new_model->modelName, 'Finance_') === 0 && $new_model->get('type') != 0) {
                        $new_model->getGT2Vars();
                    }

                    $history_class::saveData(
                        $this->registry,
                        array(
                            'model' => $model,
                            'action_type' => 'translate',
                            'new_model' => $new_model,
                            'old_model' => $old_model,
                        ));
                    // some history classes sanitize models
                    if ($model->isSanitized()) {
                        $model->unsanitize();
                    }
                }
            } else {
                $message_key = '';
                foreach (array($model->modelName, $factory_name) as $name_key) {
                    if ($this->registry['translater']->isSetParam('', 'error_' . strtolower($name_key) . '_translate_failed')) {
                        $message_key = 'error_' . strtolower($name_key) . '_translate_failed';
                        break;
                    }
                }
                if ($message_key) {
                    $this->registry['messages']->setError($this->i18n($message_key, array($old_model->getModelTypeName())), '', 100);
                }
            }
        }
        $this->registry['messages']->insertInSession($this->registry);

        //restore the previous action
        $this->registry->set('action', $action, true);
        $this->registry->set('edit_all', $edit_all, true);
        $this->registry->set('get_old_vars', $gov, true);

        // restore data in request
        $this->registry['request']->remove('model_lang');
        if ($orig_values) {
            foreach ($orig_values as $method_key => $ov) {
                foreach ($ov as $k => $v) {
                    $this->registry['request']->set($k, $v, $method_key, true);
                }
            }
        }

        // update translations property
        $params['model']->set('translations', $langs, true);

        return true;
    }

    /**
     * Sends SMS using different providers defined in settings of the automation (sms_provider)
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function sendSMS($params) {
        $model = $params['model'];
        $result = true;

        if ($params['automation_type'] == 'crontab') {
            //this might be started as plugin
            //mark the method as executed so that the execution results are sent
            $this->isExecuted = true;
        }

        if (empty($params['model'])) {
            $error = $this->i18n('error_sms_not_sent') . ' ' . $this->i18n('error_invalid_model');
            if ($params['automation_type'] != 'crontab') {
                $this->registry['messages']->setError($error);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->executionErrors[] = $error;
            }
            return false;
        }

        if (method_exists($this, '_prepareSMSBody')) {
            //this might be started as plugin so that some of the _prepareSMSBody is in the plugin
            if (!$this->_prepareSMSBody($params)) {
                if ($params['automation_type'] == 'crontab') {
                    $this->executionErrors = $this->registry['messages']->getErrors();
                }
                return false;
            }
        }

        //check the validity of the test recipient
        if (isset($this->settings['test_recipient']) && !preg_match('#^0(8|9)[0-9]{8}$#', $this->settings['test_recipient'])) {
            $error = $this->i18n('error_sms_not_sent') . ' ' . $this->i18n('error_invalid_test_recipient', $this->settings['test_recipient']);
            if ($params['automation_type'] != 'crontab') {
                $this->registry['messages']->setError($error);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->executionErrors[] = $error;
            }
            return false;

        }

        //recipient of the SMS
        //get the customer
        $recipient = '';
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
        if ($params['model']->modelName != 'Customer') {
            $filters = array('where' => array('c.id = ' . $model->get('customer')), 'sanitize' => true);
            $customer = Customers::searchOne($this->registry, $filters);
        } else {
            $customer = clone $model;
        }
        if ($customer) {
            if ($customer->get('is_company')) {
                //company

                //try to get mobile from the contact selected in the order
                if ($model->get('contact_person')) {
                    $filters = array('where' => array('c.id = ' . $model->get('contact_person')), 'sanitize' => true);
                    $selected_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);
                    $recipient = $this->_getMobile($selected_contact_person);
                }

                //try to get data from contacts (starting from the main one) in the branch selected in the order
                if (!$recipient && $model->get('branch')) {
                    $filters = array('where' => array('c.parent_customer = ' . $model->get('branch')), 'sort' => array('c.is_main DESC', 'ci18n.name', 'ci18n.lastname'), 'sanitize' => true);
                    $branch_contact_persons = Customers_Contactpersons::search($this->registry, $filters);
                    foreach ($branch_contact_persons as $branch_contact_person) {
                        $recipient = $this->_getMobile($branch_contact_person);
                        if ($recipient) {
                            break;
                        }
                    }

                    if (!$recipient) {
                        //try to get data from the selected branch
                        $filters = array('where' => array('c.id = ' . $model->get('branch')), 'sanitize' => true);
                        $selected_branch = Customers_Branches::searchOne($this->registry, $filters);
                        $recipient = $this->_getMobile($selected_branch);
                    }
                }

                //try to get mobile from the customer itself
                if (!$recipient) {
                    $recipient = $this->_getMobile($customer);
                }
            } else {
                //person
                $recipient = $this->_getMobile($customer);
            }
        }

        if (!$recipient) {
            //invalid recipient
            $error = $this->i18n('error_sms_not_sent') . ' ' .
                sprintf($this->i18n('error_invalid_customer'), PH_BASE_URL . '?' .
                    $this->registry['module_param'] . '=customers&customers=edit&edit=' .
                $customer->get('id')
            );
            if ($params['automation_type'] != 'crontab') {
                $this->registry['messages']->setError($error);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->executionErrors[] = $error;
            }
            return false;
        }

        //body of the SMS
        $sms_content = $this->settings['sms_template'];

        if (isset($this->placeholders)) {
            if (!is_array($this->placeholders)) {
                $this->placeholders = array($this->placeholders);
            }
            if (count($this->placeholders) > 0) {
                if (preg_match('#%[a-z]+#', $sms_content)) {
                    $sms_content = vsprintf($sms_content, $this->placeholders);
                } else {
                    foreach ($this->placeholders as $find => $replace) {
                        $sms_content = str_replace('[' . $find . ']', $replace, $sms_content);
                    }
                }
            }
        }

        //compose the SMS
        $sms = array(
            'model_id' => $model->get('id'),
            'model_name' => $model->modelName,
            'recipient_name' => $model->modelName == 'Customer' ? trim($model->get('name') . ' ' . $model->get('lastname')) : $model->get('customer_name'),
            //IMPORTANT: the mobile number should be passed in 08XXXXXXXX format
            'recipient_number' => $recipient,
            'content' => $sms_content,
            'send_status' => 'Unsent',
            'dlr_status' => '',
            'dlr_date' => '',
            'test_mode' => !empty($this->settings['test_mode']),
            'test_recipient' => !empty($this->settings['test_recipient']) ? $this->settings['test_recipient'] : '',
            'notes' => '',
        );

        if (!SMS::log($this->registry, $sms) || empty($sms['id'])) {
            return false;
        }

        //send SMS
        //do send sms now and process the result
        if (!empty($this->settings['test_mode'])) {
            //bypass the real send to test comments
            $sms_result = true;
        } else {
            $sms_provider = !empty($this->settings['sms_provider']) ? $this->settings['sms_provider'] : '';
            switch($sms_provider) {
                case 'netfinity':
                default:
                    $sms_result = SMS::sendSMSNetfinity($this->settings, $sms);
            }
        }
        if ($sms_result === true) {
            //successfully sent SMS
            $sms_status = 'Sent';
            if (!empty($this->settings['test_mode'])) {
                $sms_status = 'Unsent (test mode)';
            }
        } else {
            //some error occurred
            if ($this->i18n('error_sending_sms_' . $sms_result)) {
                $error = $this->i18n('error_sending_sms_' . $sms_result);
            } else {
                $error = $this->i18n('error_sending_sms_general');
            }
            $error = $this->i18n('error_sms_not_sent') . ' ' . $error;
            if ($params['automation_type'] != 'crontab') {
                $this->registry['messages']->setError($error);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->executionErrors[] = $error;
            }
            return false;
        }

        //add comment to the model
        if (isset($sms['id'])) {
            require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
            $comment = new Comment($this->registry);

            $comment->set('content', 'Sent to: ' . $recipient . "\n" . $sms['content'], true);
            //IMPORTANT: the subject contains the id of the SMS sent
            //           the sms id is used to check the DLR (delivery status)
            $comment->set('subject', sprintf('SMS(%s): %s', $sms['id'], $sms_status), true);
            $comment->set('model', $model->modelName, true);
            $comment->set('model_id', $model->get('id'), true);

            if ($comment->save()) {
                $comment->saveHistory($model);
            }
        }

        //update the SMS status
        $sms['send_status'] = $sms_status;
        if ($sms_status == 'Sent') {
            $sms['dlr_status'] = 'sent';
        }
        SMS::log($this->registry, $sms);

        if ($params['automation_type'] != 'crontab') {
            $this->registry['messages']->setMessage($this->i18n('msg_sms_sent', array($recipient, $sms['content'])));
            $this->registry['messages']->insertInSession($this->registry);
        }

        return $result;
    }

    /**
     * Gets the first found mobile phone from a customer, branch or contact_person
     *
     * @param Customer|Customers_Branch|Customers_Contactperson $customer - customer, branch or contact_person object
     * @return string - the mobile phone found
     */
    private function _getMobile($customer) {
        if (!$customer) {
            return '';
        }

        //mobile phones should begin with 08 or 09 and consist of 10 digits
        $regexp = '#^0(8|9)[0-9]{8}$#';

        $contact_types = array('gsm', 'phone', 'fax');
        foreach($contact_types as $ctype) {
            if ($customer->get($ctype) && is_array($customer->get($ctype))) {
                foreach($customer->get($ctype) as $contact) {
                    //remove all characters that are not digits
                    $contact = preg_replace('#[^0-9]#', '', $contact);
                    if (preg_match($regexp, $contact)) {
                        return $contact;
                    }
                }
            }
        }

        //nothing found
        return '';
    }

    /**
     * Gets the user corresponding to the employee in the designated source
     *
     * @param mixed $model - the designated source: model or list of employee ids
     * @return mixed $user - the user id or array of user ids
     */
    public function getEmployeeUser($source) {
        if (empty($source)) {
            return '';
        }

        if (is_object($source) && is_a($source, 'Model')) {
            //check if the source is model - get its employee property
            $employee = $source->get('employee');
        } elseif (is_numeric($source)) {
            //the source is numeric
            $employee = $source;
        } elseif (is_array($source)) {
            //the source is array
            $employee = $source;
        } else {
            //CSV
            $employee = preg_split('#\s*,\s*#', $source);
            //get only numeric values
            $employee = array_filter($employee, 'is_numeric');
        }

        if (empty($employee)) {
            if (is_array($employee)) {
                return array();
            } else {
                return '';
            }
        }

        if (is_array($employee)) {
            //array
            $user = $this->registry['db']->GetCol('SELECT id FROM ' . DB_TABLE_USERS . ' WHERE employee IN (' . implode(', ', $employee) . ')');
        } else {
            //scalar
            $user = $this->registry['db']->GetOne('SELECT id FROM ' . DB_TABLE_USERS . ' WHERE employee = ' . $employee);
        }

        return $user;
    }

    /**
     * To be used in before action when we need to fail the action so the cancel_action_on_fail := 1 to be triggered.
     * If we trigger the before action with our conditions and we don`t need to make custom method we can use this method.
     * If action_cancel_error_message_*lang* is used in settings we show special error for the occasion.
     * @param $params
     * @return bool
     */
    public function cancelAction($params) {
        if(isset($this->settings['action_cancel_error_message_' . $this->registry['lang']]))
        {
            $this->registry['messages']->setError($this->settings['action_cancel_error_message_' . $this->registry['lang']]);
        }
        return false;
    }

    /**
     * Hides layouts from given model. To be used in before view automations
     *
     * @param $params - arguments for the method, containing status or substatus and registry
     * @return bool
     */
    public function hideLayouts($params) {
        $layouts = explode(',', $params['layouts']);
        $model = $params['model'];

        //closure function to filter array of layouts
        $filterLayouts = function ($durtyLayouts) use ($layouts) {
            if (is_array($durtyLayouts)) {
                return array_filter($durtyLayouts, function ($modelLayout) use ($layouts) {
                    return !in_array($modelLayout, $layouts);
                });
            }
        };
        $viewLayouts = $model->get('layouts_view');
        $editLayouts = $model->get('layouts_edit');
        //if layouts are not set we get them
        if (!is_array($viewLayouts) || !is_array($editLayouts)) {
            list($viewLayouts, $editLayouts) = $model->getPermittedLayouts();
        }
        $model->set('layouts_view', $filterLayouts($viewLayouts), true);
        $model->set('layouts_edit', $filterLayouts($editLayouts), true);

        return true;
    }

    /**
     * Get model from the database or the model itself if no ids
     *
     * @param Model $model - model
     * @return Model $updated_model
     */
    private function getUpdatedModelFromDB($model)
    {
        if (!$model->get('id')) {
            return $model;
        }

        $module = $this->registry['module'];
        $controller = $this->registry['controller'];

        // prepare factory name
        $factory_name = implode('_', array_map('ucfirst', explode('_', $module . ($module != $controller ? '_' . $controller : ''))));

        $factory = PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';
        if (is_readable($factory)) {
            require_once($factory);
        } else {
            return $model;
        }

        // prepare alias
        $alias = $factory_name::getAlias($module, $controller);

        //get model
        $updated_model = $factory_name::searchOne(
            $this->registry,
            array(
                'where' => array($alias . '.id = \'' . $model->get('id') . '\''),
                'model_lang' => $model->get('lang')
            )
        );

        return $updated_model;
    }


    /**
     * Synchronizes system document's or project's system task status and customer
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    private function syncSystemTask($params)
    {
        $model = $params['model'];

        if (!$model->get('id')) {
            return false;
        }

        $module = $this->registry['module'];
        if (!in_array($module, ['documents', 'projects'])) {
            //works only for documents and projects
            return true;
        }
        $factoryClass = ucfirst($module);

        //get fresh copy of the model
        $alias = $factoryClass::getAlias($this->registry['module'],  $this->registry['controller']);
        $model = $factoryClass::searchOne(
            $this->registry,
            array(
                'where' => array("{$alias}.id = {$model->get('id')}")
            )
        );

        $modelStatus = $model->get('status');

        $system_task_id = $factoryClass::getSystemTask($this->registry, $model->get('id'));
        if ($system_task_id) {
            /**
             * @var $task Task
             */
            $task = Tasks::searchOne($this->registry, array('where' => array('t.id = ' . $system_task_id, 't.type = ' . PH_TASK_SYSTEM_TYPE)));

            if (!in_array($modelStatus, ['closed', 'finished']) &&
                $task && $task->get('status') == 'finished') {
                //open the status of task
                $task->set('status', 'progress', true);
                $task->setStatus();
            }

            if ($model->get('customer') != $task->get('customer')) {
                //change the customer of the task
                $task->set('customer', $model->get('customer'), true);
                //bypass the validation (do not use save)
                $task->slashesEscape();
                $task->edit();
            }
        }

        return true;
    }

    /*
     * Function to process the var value when preparing the data for the custom history
     */
    public function _processValueForCustomHistory(&$item, $key, $var) {
        //get the variable value and format it (if necessary)
        switch ($var['type']) {
            case 'date':
                $item = General::strftime($this->i18n('date_short'), strtotime($item));
                break;
            case 'datetime':
                $item = General::strftime($this->i18n('date_mid'), strtotime($item));
                break;
            case 'dropdown':
            case 'radio':
                if (!is_array($item)) {
                    $option = $item;

                    if (isset($var['options'])) {
                        foreach ($var['options'] as $opt) {
                            if ($opt['option_value'] == $option) {
                                $item = $opt['label'];
                                break;
                            }
                        }
                    } elseif (isset($var['optgroups'])) {
                        foreach ($var['optgroups'] as $optgroup) {
                            foreach ($optgroup as $opt) {
                                if ($opt['option_value'] == $option) {
                                    $item = $opt['label'];
                                    break;
                                }
                            }
                        }
                    }
                }
                break;
            case 'file_upload':
                if (!is_object($item) || $item->get('deleted_by')) {
                    $item = '';
                } else {
                    $item = $item->get('name') ?: $item->get('filename');
                }
                break;
            case 'autocompleter':
                // do not process the var - the visible field will be used so use the value as it is
            default:
                break;
        }
    }

    /**
     * Set messages for automation setGT2, depending automation type
     *
     * @param $params - the automation params
     * @param $msgs - messages, if any
     * @param $original_lang - the original interface lang, if such
     * @param $model_with_original_lang - the model with original lang, if such
     *
     * @return false
     */
    private function setGT2Messages($params, $msgs = [], $original_lang = null, $model_with_original_lang = null) {
        // Restore the original language of the interface
        if ($original_lang) {
            $this->registry->set('lang', $original_lang, true);
            $this->loadI18NFiles();
        }
        // Restore the model with the original language
        if ($model_with_original_lang) {
            $params['model'] = $model_with_original_lang;
        }

        // Set messages
        $automation_name = empty($params['name']) ? '' : ' "' . $params['name'] . '"';
        if ($params['automation_type'] !== 'crontab') {
            array_unshift($msgs, sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
        }
        if ($params['automation_type'] == 'action') {
            foreach ($msgs as $key => $msg) {
                $this->registry['messages']->setWarning($msg, (is_int($key) ? '' : $key));
            }
            $this->registry['messages']->insertInSession($this->registry);
        } else if ($params['automation_type'] == 'crontab') {
            foreach ($msgs as $msg) {
                $this->executionErrors[] = $msg;
            }
        } else {
            foreach ($msgs as $key => $msg) {
                $this->registry['messages']->setError($msg, (is_int($key) ? '' : $key));
            }
        }

        return false;
    }

    /**
     * Set GT2
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool         - result of the execution of the method
     */
    public function setGT2($params) {
        // Prepare some basics
        $registry = $this->registry;
        // $request  = $registry['request'];
        $messages = $registry['messages'];
        $msgs     = [];
        $db       = $registry['db'];

        // Get the automation name
        $automation_name = empty($params['name']) ? '' : ' "' . $params['name'] . '"';

        // Allow only action and crontab execution
        // TODO: Implement before_action.
        // The main thing to do that is to clear all GT2 vars into the request, and set them again.
        // The collection and so on is maybe ready (check the code again).
        // For clean up and setting values we may use $gt2_columns.
        $allowed_automation_types = [/*'before_action', */'action', 'crontab'];
        if (!in_array($params['automation_type'], $allowed_automation_types)) {
            $msgs[] = sprintf(
                $this->i18n('invalid_automation_type'),
                $params['automation_type'],
                $automation_name,
                implode(', ', $allowed_automation_types)
            );
        // } elseif ($params['automation_type'] == 'before_action' && !$request->isPost()) {
        //     $msgs[] = $this->i18n('automation_setgt2_invalid_before_action_without_post');
        }
        if ($msgs) {
            array_unshift($msgs, sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
            foreach ($msgs as $msg) {
                $messages->setError($msg);
            }
            return false;
        }

        // IMPORTANT: Get settings as multiple (all settings values are arrays
        $settings = General::parseSettings($params['settings'], '', true);

        /*
         * Validate and process settings
         */
        $gt2_vars_settings_count_invalid = false;
        $gt2_vars_settings_count = null;
        $gt2_vars_values = [];
        $gt2_plain_vars_values = [];
        $gt2_columns = array_map('strtolower', array_keys($db->MetaColumns(DB_TABLE_GT2_DETAILS, true)));
        $gt2_columns = array_merge($gt2_columns, array_map('strtolower', array_keys($db->MetaColumns(DB_TABLE_GT2_DETAILS_I18N, true))));
        foreach ($settings as $setting_name => $setting_values) {
            if (strpos($setting_name, 'gt2_var_') === 0) {
                // Get the GT2 var name
                $gt2_var = substr($setting_name, 8);

                // Check if it's existing GT2 var
                if (!in_array($gt2_var, $gt2_columns)) {
                    continue;
                }

                // Get the setting value for this var
                $gt2_vars_values[$gt2_var] = $setting_values;

                // Check if all sets of settings for GT2 vars match their count
                $setting_values_count = count($setting_values);
                if ($gt2_vars_settings_count == null) {
                    $gt2_vars_settings_count = $setting_values_count;
                } else if ($setting_values_count !== $gt2_vars_settings_count) {
                    $gt2_vars_settings_count_invalid = true;
                    break;
                }
            } elseif (strpos($setting_name, 'gt2_plain_var_') === 0) {
                $gt2_plain_var = substr($setting_name, 14);
                $gt2_plain_vars_values[$gt2_plain_var] = reset($setting_values);
            }
        }
        // Set message
        if (!$gt2_vars_values && !$gt2_plain_vars_values) {
            // If there are no GT2 var settings
            $msgs[] = $this->i18n('automation_setgt2_settings_gt2_vars_not_set');
        } elseif ($gt2_vars_settings_count_invalid) {
            // If settings sets count don't match
            $msgs[] = $this->i18n('automation_setgt2_settings_gt2_vars_count_invalid');
        }
        // If there are any messages
        if ($msgs) {
            return $this->setGT2Messages($params, $msgs);
        }

        // Get some model basics
        $module     = $params['module'];
        $controller = $params['controller'] ?: $module;
        $model      = '';
        $model_lang = ($params['model']->get('model_lang') ?: $registry['lang']);

        // Use specific language for the model
        if (isset($settings['model_lang'])) {
            // TODO: The entire idea of having the model with original lang is useless
            // because there's no use to return it back to $params['model']
            // The next automation (executed after the current), will use the original instance,
            // no matter if we change $params['model']. You can try this by setting another automation
            // to be executed after the current and you will see, that the second automation has it's model_lang
            // no matter if we change $params['model']. It would matter if we only change the for example: $params['model']->set('model_lang', ...)
            // but that's not the case. So we could remove all this $original_model_lang, but currently there's no practical problem,
            // so I didn't removed it, to save time from this useless change.
            $original_model_lang = $model_lang;
            $model_lang = $settings['model_lang'];
        }

        // Use specific language for the interface
        if (isset($settings['lang'])) {
            $original_lang = $registry['lang'];
            $registry->set('lang', $settings['lang'], true);
        }

        // If there is a model_id
        if (!empty($params['model_id'])) {
            // Get: id, module, controller
            $id = $params['model_id'];

            // Prepare the factory
            $factory_name = $params['model']->getFactory();

            // Prepare alias
            $alias = $factory_name::getAlias($module, $controller);

            // Get the model from the database
            $model = $factory_name::searchOne(
                $registry,
                array(
                    'where' => array($alias . '.id = ' . $id),
                    'model_lang' => $model_lang
                )
            );

            // If there is a model
            if ($model) {
                // If we're using a specific language for the model
                if (isset($original_model_lang)) {
                    // Get the model with the original language, so we can restore it later into the $params array
                    $model_with_original_lang = $factory_name::searchOne(
                        $registry,
                        array(
                            'where' => array($alias . '.id = ' . $id),
                            'model_lang' => $original_model_lang
                        )
                    );
                    if ($model_with_original_lang) {
                        // Set the old model
                        $model_with_original_lang->unsanitize();
                        if (!$model_with_original_lang->isDefined('vars')) {
                            $get_old_vars = $registry->get('get_old_vars');
                            $registry->set('get_old_vars', true, true);
                            $model_with_original_lang->getVars();
                            $registry->set('get_old_vars', $get_old_vars, true);
                        }
                        $model_with_original_lang->old_model = clone $model_with_original_lang;
                        $model_with_original_lang->old_model->sanitize();
                    } else {
                        $model_with_original_lang = $params['model'];
                    }
                }

                // Reset the current model from the parameters
                $params['model'] = $model;

                // Load the additional vars
                $model->unsanitize();
                $get_old_vars = $registry->get('get_old_vars');
                $registry->set('get_old_vars', true, true);
                if (!$model->isDefined('vars')) {
                    $model->getVars();
                }
                $gt2 = $model->getGT2Vars();
                $registry->set('get_old_vars', $get_old_vars, true);

                // Set the old model
                $model->old_model = clone $model;
                $model->old_model->sanitize();
            } else {
                return $this->setGT2Messages($params, [$this->i18n('error_technical_error_please_contact_nzoom_support')], $original_lang??null);
            }
        }

        // data for origin of evaluated settings - used for error logging
        $origin = [
            'table'    => DB_TABLE_AUTOMATIONS,
            'field'    => 'method',
            'id'       => $params['id'],
            'model'    => $params['model']->modelName,
            'model_id' => $params['model']->get('id'),
        ];

        /*
         * Prepare GT2 values
         */
        $gt2_values = [];
        $raw_gt2_vars_values = $gt2_vars_values;
        $gt2_vars_values_length = [];
        // Replace vars with values (replace placeholders like [b_...], [a_...])
        foreach ($gt2_vars_values as $gt2_var_name => $gt2_var_values) {
            foreach ($gt2_var_values as $gt2_var_value_index => $gt2_var_value) {
                $var_value = $gt2_var_value;

                // If the variable has some sub-variables (example: [b_customer_name])
                $sub_vars = $this->extractVariables($var_value);

                // Get the values of the sub-variables
                if ($sub_vars) {
                    foreach ($sub_vars as $sub_var) {
                        // If there is a model and this is action or crontab
                        if ($model && in_array($params['automation_type'], ['action', 'crontab'])) {
                            // Get the var value from the model
                            if (preg_match('#^a_.*$#', $sub_var)) {
                                // Additional vars have prefix a_
                                $value_source_method = 'getPlainVarValue';
                                $sub_var_name = preg_replace('#^a_#', '', $sub_var);
                            } elseif (preg_match('#^al_.*$#', $sub_var)) {
                                // FORMATTED Additional vars have prefix al_ (labels of dropdown options)
                                $value_source_method = 'getVarValue';
                                $sub_var_name = preg_replace('#^al_#', '', $sub_var);
                            } else {
                                // Basic vars have prefix b_
                                $value_source_method = 'get';
                                $sub_var_name = preg_replace('#^b_#', '', $sub_var);
                            }
                            // Get the sub-variable value from the new model
                            $sub_var_value = $model->$value_source_method($sub_var_name);
                        // } else {
                        //     // Get the sub-variable value from the request
                        //     $sub_var_name = preg_replace('#^(a|b)_#', '', $sub_var);
                        //     $sub_var_value = $registry['request']->get($sub_var_name);
                        }

                        // escape both single and double quotes in replacement values
                        // because we cannot know what to expect in expression
                        $sub_var_value = General::slashesEscape($sub_var_value);

                        // Replace the sub-variables with their values into the variable value
                        if (!is_array($sub_var_value)) {
                            $var_value = str_replace("[{$sub_var}]", $sub_var_value, $var_value);
                        } elseif ("[{$sub_var}]" === $gt2_var_value) {
                            $var_value = $sub_var_value;
                        } else {
                            return $this->setGT2Messages(
                                $params,
                                [$this->i18n('automation_setgt2_not_supporting_array_var_value_setting_with_something_else')],
                                $original_lang??null,
                                $model_with_original_lang??null
                            );
                        }
                    }
                }

                // If the variable value should be eval-ed
                if (!is_array($var_value)) {
                    $var_value = $this->getVariableValue(
                        $var_value,
                        array(
                            'orig_str' => $raw_gt2_vars_values[$gt2_var_name][$gt2_var_value_index],
                            'origin' => $origin,
                            //use the object model to get variables with methods (e.g., getVarValue)
                            strtolower($params['model']->modelName) => $params['model']
                        )
                    );
                } else {
                    /*
                     * Just get the GT2 var value length
                     */
                    $gt2_set_var_index = $gt2_var_value_index;
                    $gt2_set_var_length = count($var_value);

                    // If we already know that for a given set and it's given var there is an array value with length x
                    // and we are currently setting value length for another var of the same set, which is with different length
                    // then set error for this
                    if (array_key_exists($gt2_set_var_index, $gt2_vars_values_length) && $gt2_vars_values_length[$gt2_set_var_index] !== $gt2_set_var_length) {
                        return $this->setGT2Messages(
                            $params,
                            [$this->i18n('automation_setgt2_mismatch_rows')],
                            $original_lang??null,
                            $model_with_original_lang??null
                        );
                    }
                    $gt2_vars_values_length[$gt2_set_var_index] = $gt2_set_var_length;
                }

                // remove unnecessary backslashes after evaluation has been performed
                $var_value = General::slashesStrip($var_value);

                $gt2_vars_values[$gt2_var_name][$gt2_var_value_index] = $var_value;
            }
        }

        /*
         * Prepare the GT2 rows values
         */
        foreach ($gt2_vars_values as $gt2_var_name => $gt2_var_values) {
            $i = 0;
            foreach ($gt2_var_values as $gt2_var_value_index => $gt2_var_value) {
                // "Stretch" the value
                // If the current value is not array
                // and the current set ($gt2_var_value_index) has any array value for any of the GT2 vars
                // then "stretch" the current value as array, by repeating this single value for every GT2 row for the current set ($gt2_var_value_index)
                if (!is_array($gt2_var_value) && array_key_exists($gt2_var_value_index, $gt2_vars_values_length)) {
                    $gt2_var_value = array_fill(0, $gt2_vars_values_length[$gt2_var_value_index], $gt2_var_value);
                }

                // Set GT2 rows values
                if (is_array($gt2_var_value)) {
                    foreach ($gt2_var_value as $gfv) {
                        // The first time here we create the GT2 rows and fill the first column for them
                        // After that we fill all other columns, under the protection for rows match, made by the check in the begining of the current foreach
                        $gt2_values[$i++][$gt2_var_name] = $gfv;
                    }
                } else {
                    $gt2_values[$i++][$gt2_var_name] = $gt2_var_value;
                }
            }
        }

        // Prepare the plain values
        $gt2_plain_values = [];
        $raw_gt2_plain_vars_values = $gt2_plain_vars_values;
        foreach ($gt2_plain_vars_values as $gt2_plain_var_name => $gt2_plain_var_value) {
            $plain_var_value = $gt2_plain_var_value;

            // If the variable has some sub-variables (example: [b_customer_name])
            $sub_vars = $this->extractVariables($plain_var_value);

            // Get the values of the sub-variables
            if ($sub_vars) {
                foreach ($sub_vars as $sub_var) {
                    // If there is a model and this is action or crontab
                    if ($model && in_array($params['automation_type'], ['action', 'crontab'])) {
                        // Get the var value from the model
                        if (preg_match('#^a_.*$#', $sub_var)) {
                            // Additional vars have prefix a_
                            $value_source_method = 'getPlainVarValue';
                            $sub_var_name = preg_replace('#^a_#', '', $sub_var);
                        } else if (preg_match('#^al_.*$#', $sub_var)) {
                            // FORMATTED Additional vars have prefix al_ (labels of dropdown options)
                            $value_source_method = 'getVarValue';
                            $sub_var_name = preg_replace('#^al_#', '', $sub_var);
                        } else {
                            // Basic vars have prefix b_
                            $value_source_method = 'get';
                            $sub_var_name = preg_replace('#^b_#', '', $sub_var);
                        }
                        // Get the sub-variable value from the new model
                        $sub_var_value = $model->$value_source_method($sub_var_name);
                        // } else {
                        //     // Get the sub-variable value from the request
                        //     $sub_var_name = preg_replace('#^(a|b)_#', '', $sub_var);
                        //     $sub_var_value = $registry['request']->get($sub_var_name);
                    }

                    // escape both single and double quotes in replacement values
                    // because we cannot know what to expect in expression
                    $sub_var_value = General::slashesEscape($sub_var_value);

                    // Replace the sub-variables with their values into the variable value
                    if (!is_array($sub_var_value)) {
                        $plain_var_value = str_replace("[{$sub_var}]", $sub_var_value, $plain_var_value);
                    } else {
                        return $this->setGT2Messages(
                            $params,
                            [$this->i18n('automation_setgt2_plain_vars_not_arrays')],
                            $original_lang??null,
                            $model_with_original_lang??null
                        );
                    }
                }
            }

            // If the variable value should be eval-ed
            $plain_var_value = $this->getVariableValue(
                $plain_var_value,
                array(
                    'orig_str' => $raw_gt2_plain_vars_values[$gt2_plain_var_name],
                    'origin' => $origin,
                    //use the object model to get variables with methods (e.g., getVarValue)
                    strtolower($params['model']->modelName) => $params['model']
                )
            );

            if (is_array($plain_var_value)) {
                return $this->setGT2Messages(
                    $params,
                    [$this->i18n('automation_setgt2_plain_vars_not_arrays')],
                    $original_lang??null,
                    $model_with_original_lang??null
                );
            }

            // remove unnecessary backslashes after evaluation has been performed
            $plain_var_value = General::slashesStrip($plain_var_value);

            $gt2_plain_values[$gt2_plain_var_name] = $plain_var_value;
        }

        /*
         * Save the GT2
         */
        if ($model && in_array($params['automation_type'], ['action', 'crontab'])) {
            $db->StartTrans();
            $model->set('table_values_are_set', true, true);
            $old_model = clone $model;
            $old_model->sanitize();

            //add merging settings if any
            //the merge of rows is within the GT2 save procedure (just before calculation)
            $gt2['merge_rows_by_unique_columns'] = $this->settings['merge_rows_by_unique_columns'] ?? '';
            $gt2['merge_rows_sum_columns'] = $this->settings['merge_rows_sum_columns'] ?? '';
            $gt2['merge_rows_concat_columns'] = $this->settings['merge_rows_concat_columns'] ?? '';

            if ($gt2_values) {
                $gt2['values'] = $gt2_values;
            }
            if ($gt2_plain_values) {
                $gt2['plain_values'] = $gt2_plain_values;
            }
            if ($other_errors = $messages->getErrors()) {
                $messages->unset_vars('errors');
            }
            $msgs = [];
            if (!$model->saveGT2Vars($gt2)) {
                if ($msgs = $messages->getErrors()) {
                    $messages->unset_vars('errors');
                }
                array_unshift($msgs, $this->i18n('automation_setgt2_failed_to_save'));
            }
            foreach ($other_errors as $other_error_key => $other_error) {
                $messages->setError($other_error, (is_int($other_error_key) ? '' : $other_error_key));
            }
            if ($msgs) {
                return $this->setGT2Messages(
                    $params,
                    $msgs,
                    $original_lang??null,
                    $model_with_original_lang??null
                );
            }
        // } else {
        //     // Set into request
        //     $gt2_request_values = [];
        //     foreach ($gt2_values as $gt2_values_row) {
        //         foreach ($gt2_values_row as $gt2_value_var_name => $gt2_value) {
        //             $gt2_request_values[$gt2_value_var_name][] = $gt2_value;
        //         }
        //     }
        //     foreach ($gt2_request_values as $gt2_request_var_name => $gt2_request_var_value) {
        //         // keep original value in order to restore on failure
        //         if (!array_key_exists($gt2_request_var_name, $this->originalRequestValues)) {
        //             $this->originalRequestValues[$gt2_request_var_name] = $request->get($gt2_request_var_name);
        //         }
        //         $request->set($gt2_request_var_name, $gt2_request_var_value, 'all', true);
        //     }
        }

        // If there is a model
        if ($model) {
            // Check if the transaction has failed
            $has_failed_trans = $db->HasFailedTrans();

            // If the transaction has failed
            if ($has_failed_trans) {
                return $this->setGT2Messages(
                    $params,
                    [],
                    $original_lang??null,
                    $model_with_original_lang??null
                );
            } else {
                // Write history
                if (in_array($params['automation_type'], ['action', 'crontab'])) {
                    $filters    = array('where'    => array($alias . '.id = ' . $id),
                                        'sanitize' => true);
                    $new_model  = $factory_name::searchOne($registry, $filters);
                    $registry->set('get_old_vars', true, true);
                    $new_model->getVars();
                    $registry->set('get_old_vars', $get_old_vars, true);

                    $history_class_name = $factory_name . '_History';
                    $history_vars       = array('model'       => $new_model,
                                                'action_type' => 'edit',
                                                'new_model'   => $new_model,
                                                'old_model'   => $model->old_model);
                    $history_id = $history_class_name::saveData($registry, $history_vars);
                    if (!$history_id) {
                        return $this->setGT2Messages(
                            $params,
                            [],
                            $original_lang??null,
                            $model_with_original_lang??null
                        );
                    }
                }
            }

            // Complete the transaction
            $db->CompleteTrans();
        }

        // Restore the original language of the interface
        if (isset($original_lang)) {
            $registry->set('lang', $original_lang, true);
        }
        // Restore the model with the original language
        if (isset($model_with_original_lang)) {
            $params['model'] = $model_with_original_lang;
        }

        return true;
    }

    /**
     * Create models and fill some of their single vars based on grid described from the automation settings
     *
     * @param $params
     *
     * @todo Add ability to remove relatives when related record id is removed from the current source model.
     * @return bool
     */
    public function gridToModels($params) {
        // Prepare some basics
        $registry = $this->registry;
        $messages = $registry['messages'];
        $has_saved_models = false;
        $has_activated_models = false;
        $has_deactivated_models = false;

        // Get the automation name
        $automation_name = empty($params['name']) ? '' : ' "' . $params['name'] . '"';

        // Allow only action and crontab execution
        $allowed_automation_types = ['action', 'crontab'];
        if (!in_array($params['automation_type'], $allowed_automation_types)) {
            $this->setAutomationMessages($params, [
                sprintf(
                    $this->i18n('invalid_automation_type'),
                    $params['automation_type'],
                    $automation_name,
                    implode(', ', $allowed_automation_types)
                )
            ]);
            return false;
        }

        // Che current model is the source model
        $source_model = $params['model'];

        /*
         * Settings: get, process and validate
         */
        // Get the automation settings
        $settings = $this->getSettings($params);
        // Get the vars settings
        $destination_vars_sources = [];
        foreach ($settings as $setting_name => $setting_value) {
            if (strpos($setting_name, 'b_') === 0 || strpos($setting_name, 'a_') === 0) {
                $destination_vars_sources[$setting_name] = $setting_value;
            }
        }
        // Validate required settings
        if (empty($settings['module']) || empty($settings['type']) || empty($destination_vars_sources)) {
            $this->setAutomationMessages($params, [$this->i18n('automations_missing_required_settings')]);
            return false;
        }

        // Get destination factory and alias
        $destination_factory_name = $this->buildFactoryName($settings['module'], $settings['controller']??'');
        if (!class_exists($destination_factory_name)) {
            $this->setAutomationMessages($params, [$this->i18n('error_technical_error_please_contact_nzoom_support')]);
            return false;
        }
        $destination_alias = $destination_factory_name::getAlias($settings['module'], $settings['controller']?:$settings['module']);
        // Get source factory and alias
        $source_factory_name = $source_model->getFactory();
        $source_alias = $source_factory_name::getAlias($source_model->getModule(), $source_model->getController());

        // Execute the automation as original user
        if (!empty($settings['execute_as_original_user'])) {
            $this->setOriginalUserAsCurrent();
        }

        // If relation var is defined
        if (!empty($settings['relation_id_var'])) {
            // Get current relation IDs
            $relation_id_var_value = $source_model->getPlainVarValue($settings['relation_id_var']);
            if (is_array($relation_id_var_value)) {
                $relation_id_var_value = array_values($relation_id_var_value);
            } else {
                $relation_id_var_value = [$relation_id_var_value];
            }
            $relation_id_var_value_old = $relation_id_var_value;

            /*
             * Maintain activity
             */
            if (!empty($settings['activate_and_deactivate'])) {
                // Prepare to collect models for activation
                $activate_destination_models_ids = [];

                // Mark models for deactivation if they are from deleted rows
                $deactivate_destination_models_ids = [];
                $source_relatives_tree = $source_model->getRelativesTree();
                foreach ($source_relatives_tree as $rel) {
                    // If in this relation the parent is the current source model
                    if (array_key_exists('parent_model_name', $rel)
                            && $rel['parent_model_name'] == $source_factory_name::$modelName
                            && array_key_exists('parent_id', $rel)
                            && $rel['parent_id'] == $source_model->get('id')
                            // and the child is one of the destination models
                            && array_key_exists('model_name', $rel)
                            && $rel['model_name'] == $destination_factory_name::$modelName
                            && array_key_exists('type_id', $rel)
                            && $rel['type_id'] == $settings['type']
                            // and it's active
                            && array_key_exists('active', $rel)
                            && !empty($rel['active'])
                            // but it doesn't exists into the relation var
                            && array_key_exists('id', $rel)
                            && !in_array($rel['id'], $relation_id_var_value)) {
                        // Collect it for deactivation
                        $deactivate_destination_models_ids[] = $rel['id'];
                    }
                }
            }
        }

        // data for origin of evaluated settings - used for error logging
        $origin = [
            'table'    => DB_TABLE_AUTOMATIONS,
            'field'    => 'settings',
            'id'       => $params['id'],
            'model'    => $source_model->modelName,
            'model_id' => $source_model->get('id'),
        ];

        // Prepare to count destination models
        $destination_models_count = 0;
        // Prepare to collect destination vars values
        $destination_vars_values = [];

        // Replace vars with values (replace placeholders like [b_...], [a_...])
        foreach ($destination_vars_sources as $destination_var => $source) {
            // Prepare to process the source value
            $source_value = $source;

            // If the variable has some sub-variables (example: [b_customer_name])
            $source_sub_vars = $this->extractVariables($source_value);

            // Get the values of the sub-variables
            if ($source_sub_vars) {
                foreach ($source_sub_vars as $source_sub_var) {
                    // Get the sub-variable value from the model
                    list($source_sub_var_method, $source_sub_var_name) = self::getVarMethodAndName($source_sub_var);
                    $source_sub_var_value = $source_model->$source_sub_var_method($source_sub_var_name);

                    // escape both single and double quotes in replacement values
                    // because we cannot know what to expect in expression
                    $source_sub_var_value = General::slashesEscape($source_sub_var_value);

                    // Replace the sub-variables with their values
                    if (!is_array($source_sub_var_value)) {
                        $source_value = str_replace("[{$source_sub_var}]", $source_sub_var_value, $source_value);
                    } else if ("[{$source_sub_var}]" === $source_value) {
                        $source_value = $source_sub_var_value;
                    } else {
                        $this->setAutomationMessages($params, [$this->i18n('automation_gridtomodels_not_supporting_array_var_value_setting_with_something_else')]);

                        // Return the automations user
                        if (!empty($settings['execute_as_original_user'])) {
                            $this->setAutomationUserAsCurrent();
                        }

                        return false;
                    }
                }
            }

            // Eval source value
            if (!is_array($source_value)) {
                $source_value = $this->getVariableValue(
                    $source_value,
                    [
                        'orig_str'                           => $source,
                        'origin'                             => $origin,
                        //use the object model to get variables with methods (e.g., getVarValue)
                        strtolower($source_model->modelName) => $source_model
                    ]
                );
                if (!is_array($source_value)) {
                    $source_value = [$source_value];
                }
            }

            // remove unnecessary backslashes after evaluation has been performed
            $source_value = General::slashesStrip($source_value);

            /*
             * Count rows
             *
             * Check how many destination models we should add.
             * If the source do not generate any row, then we won't add any destination models.
             */
            $source_value_rows_count = count($source_value);
            if ($source_value_rows_count > 1) {
                if ($destination_models_count === 0 || $destination_models_count === 1) {
                    $destination_models_count = $source_value_rows_count;
                } elseif ($destination_models_count > 1 && $source_value_rows_count !== $destination_models_count) {
                    $this->setAutomationMessages($params, [$this->i18n('automation_gridtomodels_multirow_source_vars_rows_count_mismatch')]);

                    // Return the automations user
                    if (!empty($settings['execute_as_original_user'])) {
                        $this->setAutomationUserAsCurrent();
                    }

                    return false;
                }
            } elseif ($source_value_rows_count === 1) {
                if ($destination_models_count === 0) {
                    $destination_models_count = 1;
                }
            } else {
                // We can't use an empty array, so skip it
                continue;
            }

            // Ensure that the source values array is normalized
            $source_value = array_values($source_value);

            // Collect the data
            $destination_vars_values[$destination_var] = $source_value;
        }

        // If there are models tobe created/updated
        if ($destination_models_count) {
            // Get the destination type model
            $sample_destination_model = $destination_factory_name::newModel($registry, ['type' => $settings['type']], $destination_factory_name::$modelName);
            $destination_model_type = $sample_destination_model->getModelType();

            // Prepare destionation model default basic vars
            $destination_model_params_default = [
                'type' => $destination_model_type->get('id'),
            ];
            foreach ($destination_model_type->getAll() as $destination_model_type_var_name => $destination_model_type_var_value) {
                if (strpos($destination_model_type_var_name, 'default_') === 0) {
                    $destination_model_default_param_name = substr($destination_model_type_var_name, 8);
                    $destination_model_params_default[$destination_model_default_param_name] = $destination_model_type_var_value;
                }
            }
            $destination_model_params_default['group']      = $destination_model_type->getDefaultGroup();
            $destination_model_params_default['department'] = $destination_model_type->getDefaultDepartment();

            // Define non single vars types
            $vars_non_single_types = [
                'bb'           => null,
                'grouping'     => null,
                'gt2'          => null,
                'configurator' => null
            ];

            // Start transaction
            $registry['db']->StartTrans();

            /*
             * Add/edit destination models
             */
            $errors_before_save = $messages->getErrors();
            $warnings_before_save = $messages->getWarnings();
            // Process as many destination models as there are source data rows
            for ($i = 0; $i < $destination_models_count; $i++) {
                // Get the current row destination model id
                $relation_id = '';
                if (!empty($settings['relation_id_var']) && array_key_exists($i, $relation_id_var_value) && !empty($relation_id_var_value[$i])) {
                    $relation_id = $relation_id_var_value[$i];
                }

                // If there are conditions for the rows tobe processed and for the current row they failed
                if (array_key_exists('condition', $settings) && $settings['condition'] !== '' && !$this->checkGridToModelsCondition($settings['condition'], $source_model, $i, $params['id'])) {
                    // If there's a destination model id for the current row
                    if ($relation_id) {
                        if (!empty($settings['activate_and_deactivate'])) {
                            // Prepare it for deactivation
                            $deactivate_destination_models_ids[] = $relation_id;
                        }

                        if (!empty($settings['relation_id_var']) && !empty($settings['relation_id_remove_on_no_condition'])) {
                            // Remove it from this row
                            $relation_id_var_value[$i] = '';
                        }
                    }
                    continue;
                }

                // If there's a destination model id for the current row and we should maintain activity
                if ($relation_id && !empty($settings['activate_and_deactivate'])) {
                    // Prepare it for activation
                    $activate_destination_models_ids[] = $relation_id;
                }

                // Set the default destination model basic vars
                $destination_model_params = $destination_model_params_default;
                // Prepare to collect additional vars
                $destination_model_additional_vars = [];
                // Set basic and additional vars values
                foreach ($destination_vars_values as $destination_var => $source_values) {
                    // Get data from the corresponding row or from the only single row
                    if (array_key_exists($i, $source_values)) {
                        $source_value = $source_values[$i];
                    } elseif (count($source_values) === 1 && array_key_exists(0, $source_values)) {
                        $source_value = $source_values[0];
                    } else {
                        $this->setAutomationMessages($params, [$this->i18n('error_technical_error_please_contact_nzoom_support')]);

                        // Return the automations user
                        if (!empty($settings['execute_as_original_user'])) {
                            $this->setAutomationUserAsCurrent();
                        }

                        return false;
                    }

                    // Set additional or basic var
                    if (strpos($destination_var, 'b_') === 0) {
                        $destination_var = substr($destination_var, 2);
                        $destination_model_params[$destination_var] = $source_value;
                    } elseif (strpos($destination_var, 'a_') === 0) {
                        $destination_var = substr($destination_var, 2);
                        $destination_model_additional_vars[$destination_var] = $source_value;
                    }
                }

                /*
                 * Get or create destination model and set its basic vars values
                 */
                $save_destination_model = false;
                if ($relation_id) {
                    $destinationModelAction = 'edit';
                    $destination_model_search_params = [
                        'where'      => ["{$destination_alias}.id = {$relation_id}"],
                        'model_lang' => $source_model->get('model_lang')
                    ];
                    if (array_key_exists('edit_filters_sql', $settings) && $settings['edit_filters_sql'] !== '') {
                        $destination_model_search_params['where'][] = $settings['edit_filters_sql'];
                    }
                    $destination_model = $destination_factory_name::searchOne($registry, $destination_model_search_params);
                    if ($destination_model) {
                        $destination_old_model = clone $destination_model;
                        foreach ($destination_model_params as $destination_model_basic_var_name => $destination_model_basic_var_value) {
                            // Mark to save destination model if there is any real change of basic vars set by the automation settings
                            // TODO: compare 2022-07-18 00:00:00 as equal to 2022-07-18 or use some setting for this comparison
                            if (array_key_exists("b_{$destination_model_basic_var_name}", $destination_vars_values) && (string)$destination_model->get($destination_model_basic_var_name) !== (string)$destination_model_basic_var_value) {
                                $save_destination_model = true;
                            }
                            $destination_model->set($destination_model_basic_var_name, $destination_model_basic_var_value, true);
                        }
                    }
                } else {
                    $destinationModelAction = 'add';
                    $save_destination_model = true;
                    $destination_model = $destination_factory_name::newModel($registry, $destination_model_params, $destination_factory_name::$modelName);
                    $destination_old_model = $destination_factory_name::newModel($registry, ['type' => $destination_model_type->get('id')], $destination_factory_name::$modelName);
                }

                // If there is a destination model (there might not be, if it exists but not replying to the conditions from $settings['edit_filters_sql'])
                if ($destination_model) {
                    /*
                     * Set destination model additional vars
                     */
                    $get_old_vars = $registry->get('get_old_vars');
                    $registry->set('get_old_vars', true, true);
                    $destination_model->getVars();
                    $destination_old_model->getVars();
                    $registry->set('get_old_vars', $get_old_vars, true);
                    $destination_model_vars = $destination_model->get('vars');
                    foreach ($destination_model_vars as $destination_model_var_index => $destination_model_var) {
                        // Skip this var if she's not prepared tobe set
                        if (!array_key_exists($destination_model_var['name'], $destination_model_additional_vars)) {
                            continue;
                        }

                        // The automation works only with single row destination vars
                        $var_non_single_types = array_filter(array_intersect_key($destination_model_var, $vars_non_single_types));
                        if (!empty($var_non_single_types)) {
                            $this->setAutomationMessages($params, [$this->i18n('automation_gridtomodels_not_working_with_multirow_vars')]);

                            // Return the automations user
                            if (!empty($settings['execute_as_original_user'])) {
                                $this->setAutomationUserAsCurrent();
                            }

                            return false;
                        }

                        // Skip this var if her value is the same as the one prepared tobe set
                        // TODO: compare 2022-07-18 00:00:00 as equal to 2022-07-18 or use some setting for this comparison
                        if ((string)$destination_model_var['value'] === (string)$destination_model_additional_vars[$destination_model_var['name']]) {
                            continue;
                        }

                        // Set the destination model additional var value
                        $destination_model_vars[$destination_model_var_index]['value'] = $destination_model_additional_vars[$destination_model_var['name']];
                        // Mark that there are changes tobe saved
                        $save_destination_model = true;
                    }

                    // If there are changes of the model's basic or additional vars
                    if ($save_destination_model) {
                        $destination_model->set('vars', $destination_model_vars, true);
                        if ($destination_model->save()) {
                            $has_saved_models = true;
                            if ($destinationModelAction === 'add') {
                                if (!isset($new_destination_models_ids)) {
                                    $new_destination_models_ids = [];
                                }
                                $new_destination_models_ids[$destination_model->get('id')] = $destination_model->get('id');
                            }
                            if (!empty($settings['relation_id_var'])) {
                                // If there is no such row for the current relation or there is already other ID (not the current model)
                                if (!array_key_exists($i, $relation_id_var_value) || !empty($relation_id_var_value[$i]) && $relation_id_var_value[$i] != $destination_model->get('id')) {
                                    /*
                                     * Exit with error
                                     */
                                    // Remove current action errors and warnings
                                    $messages->unset_vars('errors');
                                    $messages->unset_vars('warnings');
                                    // Restore errors and warnings not from the automation
                                    $this->setMessages('errors', $errors_before_save);
                                    $this->setMessages('warnings', $warnings_before_save);
                                    // Set automation custom message
                                    $this->setAutomationMessages($params, [$this->i18n('error_technical_error_please_contact_nzoom_support')]);

                                    // Return the automations user
                                    if (!empty($settings['execute_as_original_user'])) {
                                        $this->setAutomationUserAsCurrent();
                                    }

                                    return false;
                                }

                                // Set the relation ID
                                $relation_id_var_value[$i] = $destination_model->get('id');
                            }

                            // Write history
                            $filters_new_model = [
                                'where' => ["{$destination_alias}.id = {$destination_model->get('id')}"],
                            ];
                            $destination_new_model = $destination_factory_name::searchOne($registry, $filters_new_model);
                            $get_old_vars = $registry->get('get_old_vars');
                            $registry->set('get_old_vars', true, true);
                            $destination_new_model->getVars();
                            $registry->set('get_old_vars', $get_old_vars, true);
                            $history_class_name = $destination_factory_name . '_History';
                            $history_vars = [
                                'action_type' => $destinationModelAction,
                                'old_model'   => $destination_old_model,
                                'model'       => $destination_model,
                                'new_model'   => $destination_new_model,
                            ];
                            $history_id = $history_class_name::saveData($registry, $history_vars);
                            $afterSaveSuccess = (bool)$history_id;
                            if ($afterSaveSuccess) {
                                // TODO: collect successfully added documents

                                $afterSaveSuccess = $this->executeActionAutomations($destination_old_model, $destination_new_model, $destinationModelAction);
                            }
                            if (!$afterSaveSuccess) {
                                // Fail the transaction
                                $registry['db']->FailTrans();

                                // Remove current action errors and warnings
                                $messages->unset_vars('errors');
                                $messages->unset_vars('warnings');
                                // Restore errors and warnings not from the automation
                                $this->setMessages('errors', $errors_before_save);
                                $this->setMessages('warnings', $warnings_before_save);
                                // Set automation custom message
                                $this->setAutomationMessages($params, [$this->i18n('error_technical_error_please_contact_nzoom_support')]);

                                // Return the automations user
                                if (!empty($settings['execute_as_original_user'])) {
                                    $this->setAutomationUserAsCurrent();
                                }

                                return false;
                            }
                        } else {
                            /*
                             * Destination model save failed
                             */
                            // Remove current action errors and warnings
                            $messages->unset_vars('errors');
                            $messages->unset_vars('warnings');
                            // Restore errors and warnings not from the automation
                            $this->setMessages('errors', $errors_before_save);
                            $this->setMessages('warnings', $warnings_before_save);
                            // Set automation custom message
                            $this->setAutomationMessages($params, [sprintf($this->i18n('automation_gridtomodels_destination_model_save_failed_for_row'), $i+1)]);

                            // Return the automations user
                            if (!empty($settings['execute_as_original_user'])) {
                                $this->setAutomationUserAsCurrent();
                            }

                            return false;
                        }
                    }
                }
            }

            $sanitize_after = false;
            if (!empty($settings['relation_id_var']) || !empty($settings['add_relatives_child'])) {
                if ($sanitize_after = $source_model->isSanitized()) {
                    $source_model->unsanitize();
                }
            }
            /*
             * Save relation IDs
             */
            if (!empty($settings['relation_id_var']) && array_diff($relation_id_var_value, $relation_id_var_value_old)) {
                $get_old_vars = $registry->get('get_old_vars');
                $registry->set('get_old_vars', true, true);
                $source_model->getVars();
                $registry->set('get_old_vars', $get_old_vars, true);
                $source_model_vars = $source_model->get('vars');
                $source_old_model = clone $source_model;
                $save_source_model_relation_id_var = false;
                foreach ($source_model_vars as $source_model_var_key => $source_model_var) {
                    if ($source_model_var['name'] == $settings['relation_id_var']) {
                        $var_non_single_types = array_filter(array_intersect_key($source_model_var, $vars_non_single_types));
                        // If the relation var is a single var
                        if (empty($var_non_single_types)) {
                            if (count($relation_id_var_value) === 1) {
                                $source_model_var['value'] = reset($relation_id_var_value);
                            } else {
                                if ($params['automation_type'] == 'action') {
                                    $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                                    $messages->setWarning($this->i18n('automation_gridtomodels_relation_id_failed_to_save'));
                                } else {
                                    $this->executionErrors[] = $this->i18n('automation_gridtomodels_relation_id_failed_to_save');
                                }

                                // Return the automations user
                                if (!empty($settings['execute_as_original_user'])) {
                                    $this->setAutomationUserAsCurrent();
                                }

                                return false;
                            }
                        } else {
                            if (count($relation_id_var_value) === count($source_model_var['value'])) {
                                $source_model_var['value'] = $relation_id_var_value;
                            } else {
                                if ($params['automation_type'] == 'action') {
                                    $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                                    $messages->setWarning($this->i18n('automation_gridtomodels_relation_id_failed_to_save'));
                                } else {
                                    $this->executionErrors[] = $this->i18n('automation_gridtomodels_relation_id_failed_to_save');
                                }

                                // Return the automations user
                                if (!empty($settings['execute_as_original_user'])) {
                                    $this->setAutomationUserAsCurrent();
                                }

                                return false;
                            }
                        }
                        $source_model_vars[$source_model_var_key] = $source_model_var;
                        $save_source_model_relation_id_var = true;
                        break;
                    }
                }
                if ($save_source_model_relation_id_var) {
                    $source_model->set('vars', $source_model_vars, true);
                    if ($source_model->save()) {
                        $has_saved_models = true;

                        // Write history
                        $filters_new_model = [
                            'where' => ["{$source_alias}.id = {$source_model->get('id')}"],
                        ];
                        $source_new_model = $source_factory_name::searchOne($registry, $filters_new_model);
                        $get_old_vars = $registry->get('get_old_vars');
                        $registry->set('get_old_vars', true, true);
                        $source_new_model->getVars();
                        $registry->set('get_old_vars', $get_old_vars, true);
                        $history_class_name = $source_factory_name . '_History';
                        $history_vars = [
                            'action_type' => 'edit',
                            'old_model'   => $source_old_model,
                            'model'       => $source_model,
                            'new_model'   => $source_new_model,
                        ];
                        $history_id = $history_class_name::saveData($registry, $history_vars);
                        if (!$history_id) {
                            if ($params['automation_type'] == 'action') {
                                $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                                $messages->setWarning($this->i18n('error_technical_error_please_contact_nzoom_support'));
                            } else {
                                $this->executionErrors[] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                            }
                            if (!empty($sanitize_after)) {
                                $source_model->sanitize();
                            }

                            // Return the automations user
                            if (!empty($settings['execute_as_original_user'])) {
                                $this->setAutomationUserAsCurrent();
                            }

                            return false;
                        } else {
                            // TODO: maybe some success message
                        }
                    } else {
                        if ($params['automation_type'] == 'action') {
                            $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                            $messages->setWarning($this->i18n('error_technical_error_please_contact_nzoom_support'));
                        } else {
                            $this->executionErrors[] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                        }
                        if (!empty($sanitize_after)) {
                            $source_model->sanitize();
                        }

                        // Return the automations user
                        if (!empty($settings['execute_as_original_user'])) {
                            $this->setAutomationUserAsCurrent();
                        }

                        return false;
                    }
                }
            }

            /*
             * Add child relatives
             */
            // TODO: Add processing for more modules, not only "documents"
            // If it's set to add relatives (from the settings)
            // and there are new destination models
            // and the current module is "documents"
            if (!empty($settings['add_relatives_child']) && !empty($new_destination_models_ids) && $source_model->getModule() === 'documents') {
                // Add the new destination models as
                $source_model->set('referers', $new_destination_models_ids, true);
                if (!$this->updateRelatives($source_model, ['delete' => false])) {
                    if ($params['automation_type'] == 'action') {
                        $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                        $messages->setWarning($this->i18n('error_technical_error_please_contact_nzoom_support'));
                    } else {
                        $this->executionErrors[] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                    }

                    // Return the automations user
                    if (!empty($settings['execute_as_original_user'])) {
                        $this->setAutomationUserAsCurrent();
                    }

                    return false;
                }
            }

            // Sanitize the source model (if it was sanitized)
            if (!empty($sanitize_after)) {
                $source_model->sanitize();
            }

            // Complete the transaction
            $has_failed_trans = $registry['db']->HasFailedTrans();
            $registry['db']->CompleteTrans();
            if ($has_failed_trans) {
                if ($params['automation_type'] == 'action') {
                    $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                    $messages->setWarning($this->i18n('error_technical_error_please_contact_nzoom_support'));
                } else {
                    $this->executionErrors[] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                }

                // Return the automations user
                if (!empty($settings['execute_as_original_user'])) {
                    $this->setAutomationUserAsCurrent();
                }

                return false;
            }
        }

        /*
         * Deactivate/activate destination models
         */
        if (!empty($settings['activate_and_deactivate'])) {
            if (!empty($deactivate_destination_models_ids)) {
                if (method_exists($destination_factory_name, 'getIds')) {
                    // Deactivate only active models
                    $deactivate_destination_models_ids_list = implode(', ', $deactivate_destination_models_ids);
                    $deactivate_destination_models_params = [
                        'where' => [
                            "{$destination_alias}.id IN({$deactivate_destination_models_ids_list})",
                            "{$destination_alias}.active = 1",
                        ]
                    ];
                    if (array_key_exists('edit_filters_sql', $settings) && $settings['edit_filters_sql'] !== '') {
                        $deactivate_destination_models_params['where'][] = $settings['edit_filters_sql'];
                    }
                    $deactivate_destination_models_ids = $destination_factory_name::getIds($registry, $deactivate_destination_models_params);
                }
                if (!empty($deactivate_destination_models_ids)) {
                    if (method_exists($destination_factory_name, 'changeStatus') && $destination_factory_name::changeStatus($registry, $deactivate_destination_models_ids, 'deactivate')) {
                        $has_deactivated_models = true;
                        $deactivate_destination_models_ids_list = implode(', ', $deactivate_destination_models_ids);
                        $filters = ['where' => ["{$destination_alias}.id IN ({$deactivate_destination_models_ids_list})"],];
                        $deactivate_destination_models = $destination_factory_name::search($registry, $filters);
                        foreach ($deactivate_destination_models as $deactivate_destination_model) {
                            $history_class_name = $destination_factory_name . '_History';
                            $history_class_name::saveData(
                                $registry,
                                [
                                    'model'       => $deactivate_destination_model,
                                    'action_type' => 'deactivate'
                                ]
                            );
                        }
                    } else {
                        if ($params['automation_type'] == 'action') {
                            $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                            $messages->setWarning($this->i18n('error_technical_error_please_contact_nzoom_support'));
                        } else {
                            $this->executionErrors[] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                        }

                        // Return the automations user
                        if (!empty($settings['execute_as_original_user'])) {
                            $this->setAutomationUserAsCurrent();
                        }

                        return false;
                    }
                }
            }
            if (!empty($activate_destination_models_ids)) {
                if (method_exists($destination_factory_name, 'getIds')) {
                    // Activate only inactive models
                    $activate_destination_models_ids_list = implode(', ', $activate_destination_models_ids);
                    $activate_destination_models_params = [
                        'where' => [
                            "{$destination_alias}.id IN({$activate_destination_models_ids_list})",
                            "{$destination_alias}.active = 0",
                        ]
                    ];
                    if (array_key_exists('edit_filters_sql', $settings) && $settings['edit_filters_sql'] !== '') {
                        $activate_destination_models_params['where'][] = $settings['edit_filters_sql'];
                    }
                    $activate_destination_models_ids = $destination_factory_name::getIds($registry, $activate_destination_models_params);
                }
                if (!empty($activate_destination_models_ids)) {
                    if (method_exists($destination_factory_name, 'changeStatus') && $destination_factory_name::changeStatus($registry, $activate_destination_models_ids, 'activate')) {
                        $has_activated_models = true;
                        $activate_destination_models_ids_list = implode(', ', $activate_destination_models_ids);
                        $filters = ['where' => ["{$destination_alias}.id IN ({$activate_destination_models_ids_list})"],];
                        $activate_destination_models = $destination_factory_name::search($registry, $filters);
                        foreach ($activate_destination_models as $activate_destination_model) {
                            $history_class_name = $destination_factory_name . '_History';
                            $history_class_name::saveData(
                                $registry,
                                [
                                    'model'       => $activate_destination_model,
                                    'action_type' => 'activate'
                                ]
                            );
                        }
                    } else {
                        if ($params['automation_type'] == 'action') {
                            $messages->setWarning(sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
                            $messages->setWarning($this->i18n('error_technical_error_please_contact_nzoom_support'));
                        } else {
                            $this->executionErrors[] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                        }

                        // Return the automations user
                        if (!empty($settings['execute_as_original_user'])) {
                            $this->setAutomationUserAsCurrent();
                        }

                        return false;
                    }
                }
            }
        }

        /*
         * Show success messages (from settings)
         */
        if ($has_saved_models && $params['automation_type'] === 'action' && !empty($settings["message_success_saved_models_{$registry->get('lang')}"])) {
            $messages->setMessage($settings["message_success_saved_models_{$registry->get('lang')}"]);
        }
        if ($has_activated_models && $params['automation_type'] === 'action' && !empty($settings["message_success_activated_models_{$registry->get('lang')}"])) {
            $messages->setMessage($settings["message_success_activated_models_{$registry->get('lang')}"]);
        }
        if ($has_deactivated_models && $params['automation_type'] === 'action' && !empty($settings["message_success_deactivated_models_{$registry->get('lang')}"])) {
            $messages->setMessage($settings["message_success_deactivated_models_{$registry->get('lang')}"]);
        }
        $messages->insertInSession($registry);

        // Return the automations user
        if (!empty($settings['execute_as_original_user'])) {
            $this->setAutomationUserAsCurrent();
        }

        return true;
    }

    /**
     * Check conditions for processing a grid row from automation gridToModels()
     *
     * @param string $condition - the condition (like automations conditions), where multirow vars values are taken from the current grid row
     * @param object $model - the model from which to get vars values, set into the condition
     * @param $value_index - the current row index of the grid
     * @param $record_id - the automation ID, used for logs
     *
     * @return bool|mixed
     */
    private function checkGridToModelsCondition($condition, $model, $value_index, $record_id) {
        // extract all variable names part of the condition
        $vars = $this->extractVariables($condition);

        //prepare the basic and additional variables for replacement
        $search = array();
        $replace = array();
        foreach ($vars as $var) {
            if (strpos($var, 'a_') === 0) {
                $method = 'getPlainVarValue';
            } elseif (strpos($var, 'b_') === 0) {
                $method = 'get';
            } else {
                continue;
            }
            $var_name = substr($var, 2);

            $model->slashesEscape();
            $search[] = '[' . $var . ']';
            $value = $model->$method($var_name);
            if (is_array($value)) {
                $value = array_values($value);
                if (count($value) == 1) {
                    $replace_value = array_pop($value);
                } elseif(array_key_exists($value_index, $value)) {
                    $replace_value = $value[$value_index];
                } else {
                    $replace_value = '';
                }
            } elseif (is_object($value) && get_class($value) == 'File') {
                $replace_value = $value->get('id');
            } else {
                $replace_value = $value;
            }
            $replace[] = $replace_value;
        }

        //check condition
        $result = EvalString::evaluate(
            $this->registry,
            $condition,
            [
                'search'  => $search,
                'replace' => $replace,
                'object'  => $this,
                'model'   => $model,
                'origin'  => [
                    'table' => DB_TABLE_AUTOMATIONS,
                    'field' => 'settings',
                    'id'    => $record_id,
                ],
            ]
        );

        if ($model->slashesEscaped) {
            $model->slashesStrip();
        }

        return $result;
    }

    /**
     * Get the var name and the method to get her value
     * Example: for b_name we get: ['get', 'name']
     * Example: for a_some_var we get: ['getPlainVarValue', 'some_var']
     *
     * @param $var_name_with_prefix - var name with prefix, example: b_name or a_some_var
     *
     * @return array - array with two elements: first is the method, second is the name
     * @todo make such method into the Model class
     */
    private static function getVarMethodAndName($var_name_with_prefix) {
        // Get the var value from the model
        if (preg_match('#^a_.*$#', $var_name_with_prefix)) {
            // Additional vars have prefix a_
            $method = 'getPlainVarValue';
            $name = preg_replace('#^a_#', '', $var_name_with_prefix);
        } else if (preg_match('#^al_.*$#', $var_name_with_prefix)) {
            // FORMATTED Additional vars have prefix al_ (labels of dropdown options)
            $method = 'getVarValue';
            $name = preg_replace('#^al_#', '', $var_name_with_prefix);
        } else {
            // Basic vars have prefix b_
            $method = 'get';
            $name = preg_replace('#^b_#', '', $var_name_with_prefix);
        }

        return [$method, $name];
    }

    /**
     * Update relatives for given model (child relatives)
     *
     * @param $model - the parent model, with properties, required to chreate relatives
     * @param $params - all specific parameters for creating relatives
     *
     * @todo Add processing for more modules
     *
     * @return void
     */
    private function updateRelatives($model, $params = []) {
        $module = $model->getModule();
        if (in_array($module, ['documents'])) {
            if (array_key_exists('delete', $params)) {
                return $model->updateRelatives($params['delete']);
            } else {
                return $model->updateRelatives();
            }
        }

        return false;
    }

    /**
     * Set messages, depending automation type
     *
     * @param $params - the automation params
     * @param $msgs - messages, if any
     *
     * @return void
     */
    private function setAutomationMessages($params, $msgs = []) {
        $automation_name = empty($params['name']) ? '' : ' "' . $params['name'] . '"';
        if ($params['automation_type'] !== 'crontab') {
            array_unshift($msgs, sprintf($this->i18n('automations_errors_in_automation'), $automation_name));
        }
        if ($params['automation_type'] == 'action') {
            foreach ($msgs as $key => $msg) {
                $this->registry['messages']->setWarning($msg, (is_int($key) ? '' : $key));
            }
            $this->registry['messages']->insertInSession($this->registry);
        } else if ($params['automation_type'] == 'crontab') {
            foreach ($msgs as $msg) {
                $this->executionErrors[] = $msg;
            }
        } else {
            foreach ($msgs as $key => $msg) {
                $this->registry['messages']->setError($msg, (is_int($key) ? '' : $key));
            }
        }
    }

    /**
     * Set a list of messages
     * Useful for restoring messages collected before some action, which generates it's own messages.
     *
     * @param string $type - errors, warnings, messages
     * @param array $msgs - messages
     *
     * @return void
     */
    private function setMessages($type, $msgs) {
        foreach ($msgs as $key => $msg) {
            $this->registry['messages']->set($type, $msg, $key);
        }
    }

    /**
     * Function to process the assigments list for auto assign users
     *
     * @param string $assign_expression
     * @param $model
     * @param int $automation_id
     * @return array
     */
    private function processAssignUsers($assign_expression, $model, $automation_id): array
    {
        // If the expression have some sub-variables (example: [a_my_test])
        $source_expression = $assign_expression;
        $sub_vars = $this->extractVariables($assign_expression);

        // Get the values of the sub-variables
        foreach ($sub_vars as $sub_var) {
            //replace current user id
            if ($sub_var == 'current_user') {
                $sub_var_value = !empty($this->registry['originalUser']) ? $this->registry['originalUser']->get('id') : 0;
            } elseif ($sub_var == 'current_employee_user') {
                $sub_var_value = $this->getEmployeeUser($model);
            } else {
                // replace the vars forom the model if any
                if ($model && empty($this->before_action)) {
                    // Get the var value from the model
                    if (preg_match('#^a_.*$#', $sub_var)) {
                        // Additional vars have prefix a_
                        $value_source_method = 'getPlainVarValue';
                        $sub_var_name = preg_replace('#^a_#', '', $sub_var);
                    } else {
                        // Basic vars have prefix b_
                        $value_source_method = 'get';
                        $sub_var_name = preg_replace('#^b_#', '', $sub_var);
                    }
                    // Get the sub-variable value from the new model
                    $sub_var_value = $model->$value_source_method($sub_var_name);
                } else {
                    // Get the sub-variable value from the request
                    $sub_var_name = preg_replace('#^(a|b)_#', '', $sub_var);
                    $sub_var_value = $this->registry['request']->get($sub_var_name);
                    if (is_array($sub_var_value)) {
                        $sub_var_value = array_shift($sub_var_value);
                    }
                }
            }

            // escape both single and double quotes in replacement values
            // because we cannot know what to expect in expression
            $sub_var_value = General::slashesEscape($sub_var_value);
            if (is_array($sub_var_value)) {
                $sub_var_value = array_filter($sub_var_value);
                $sub_var_value = implode(', ', $sub_var_value);
            }

            // Replace the sub-variables with their values into the variable value
            $assign_expression = str_replace('[' . $sub_var . ']', $sub_var_value, $assign_expression);
        }

        // process the departments
        if (preg_match_all('#department_\d+#', $assign_expression, $matches)) {
            $users = $deps = array();
            foreach ($matches[0] as $d) {
                $dep_id = intval(str_replace('department_', '', $d));
                if ($dep_id) {
                    $deps[] = $dep_id;
                }
            }
            if (!empty($deps)) {
                $query = 'SELECT CONCAT("department_", department_id), GROUP_CONCAT(parent_id)' . "\n" .
                         'FROM ' . DB_TABLE_USERS_DEPARTMENTS . ' WHERE department_id IN (' . implode(', ', $deps) . ')' . "\n" .
                         'GROUP BY department_id';
                $deps = $this->registry['db']->GetAssoc($query);
            }
            foreach ($deps as $replacement => $replacement_val) {
                $assign_expression = preg_replace('#' . $replacement . '#', $replacement_val, $assign_expression);
            }
        }

        // data for origin of evaluated settings - used for error logging
        $origin = array(
            'table' => DB_TABLE_AUTOMATIONS,
            'field' => 'method',
            'id' => $automation_id,
            'model' => $model->modelName,
            'model_id' => $model->get('id'),
        );

        // If the variable value should be eval-ed
        $new_assignees = $this->getVariableValue(
            $assign_expression,
            array(
                'orig_str' => $source_expression,
                'origin' => $origin,
            )
        );

        if (!is_array($new_assignees)) {
            //convert to array
            $new_assignees = array_unique(array_filter(preg_split('#\s*,\s*#', $new_assignees)));
        }
        return $new_assignees;
    }

   /**
    * Transfer a customer (defined in a model var) to a different type
    *
    * @param array $params - arguments for the method, containing registry
    * @return bool - result of the execution of the method
    */
    public function transferCustomer($params) {
        // Prepare the factory
        $factory_name = $params['model']->getFactory();

        // Prepare alias
        $alias = $factory_name::getAlias($params['model']->getModule(), $params['model']->getController());

        //get the model from the DB
        $db_model = $factory_name::searchOne(
            $this->registry,
            array(
                'where' => array($alias . '.id = \'' . $params['model']->get('id') . '\''),
                'model_lang' => $params['model']->get('model_lang')
            )
        );

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $db_model->getVars();
        $db_model->getAssocVars();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        // find the customer based on var
        $value_source_method = '';
        $sub_var_name = '';
        if (preg_match('#^a_.*$#', $this->settings['source_customer_var'])) {
            // Additional vars have prefix a_
            $value_source_method = 'getPlainVarValue';
            $sub_var_name = preg_replace('#^a_#', '', $this->settings['source_customer_var']);
        } elseif (preg_match('#^b_.*$#', $this->settings['source_customer_var'])) {
            // Basic vars have prefix b_
            $value_source_method = 'get';
            $sub_var_name = preg_replace('#^b_#', '', $this->settings['source_customer_var']);
        }

        if (!$value_source_method || !$sub_var_name) {
            return false;
        }

        $customersToTransfer = $db_model->$value_source_method($sub_var_name);
        if (is_array($customersToTransfer)) {
            $customersToTransfer = array_filter($customersToTransfer);
        } else {
            $customersToTransfer = [$customersToTransfer];
        }

        if (empty($customersToTransfer)) {
            return false;
        }

        $filters_customers = array(
            'where' => array(
                "c.id IN ('" . implode("','", $customersToTransfer) . "')"),
            'skip_permissions_check' => true
        );
        $customers = Customers::search($this->registry, $filters_customers);

        // check customer type for transfer
        $transferComplete = true;
        $customersTypes = [];
        $this->registry['db']->StartTrans();
        foreach ($customers as $customer) {
            if (empty($this->settings['transfer_type_customer_' . $customer->get('type')])) {
                continue;
            }
            $newType = $this->settings['transfer_type_customer_' . $customer->get('type')];

            // check for matching transfer types
            if (!isset($customersTypes[$customer->get('type')])) {
                $filters = array(
                    'where' => array(
                        'ct.id = ' . $customer->get('type'),
                        'ct.deleted IS NOT NULL'
                    ),
                    'sanitize' => true
                );
                $customersTypes[$customer->get('type')] = Customers_Types::searchOne($this->registry, $filters);
            }

            if (empty($customersTypes[$customer->get('type')])) {
                continue;
            }

            $transfersTypesIds = $customersTypes[$customer->get('type')]->get('transfer_types');
            if (!in_array($newType, $transfersTypesIds)) {
                continue;
            }

            // perform the transfer
            $old_customer = clone $customer;
            $this->registry['request']->set('to_type', $newType, 'all', true);
            if ($customer->transfer()) {
                $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                 'skip_permissions_check' => true,
                                 'model_lang' => $params['model']->get('model_lang'));
                $new_customer = Customers::searchOne($this->registry, $filters);
                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'transfer', 'new_model' => $new_customer, 'old_model' => $old_customer));
            } else {
                $this->registry['db']->FailTrans();
                $transferComplete = false;
            }
            $this->registry['request']->remove('to_type');
            unset($old_customer);
        }
        if ($this->registry['db']->HasFailedTrans()) {
            $this->registry['messages']->setError($this->i18n('error_automation_transfer_customer_type',
                array(trim(sprintf('%s %s', $customer->get('name'), $customer->get('lastname'))))));
        }
        $this->registry['db']->CompleteTrans();

        return $transferComplete;
    }

    /**
     * Copy data to related customer
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function copyDataToCustomer($params) : bool
    {
        // Prepare the factory and take the model from the DB
        $factory_name = $params['model']->getFactory();

        // Prepare alias
        $alias = $factory_name::getAlias($params['model']->getModule(), $params['model']->getController());

        //get the model from the DB
        $db_model = $factory_name::searchOne(
            $this->registry,
            array(
                'where' => array($alias . '.id = \'' . $params['model']->get('id') . '\''),
                'model_lang' => $params['model']->get('model_lang')
            )
        );

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $db_model->getVars();
        $db_model->getAssocVars();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        // find the customer based on var
        $value_source_method = '';
        $sub_var_name = '';
        if (preg_match('#^a_.*$#', $this->settings['source_customer_var'])) {
            // Additional vars have prefix a_
            $value_source_method = 'getPlainVarValue';
            $sub_var_name = preg_replace('#^a_#', '', $this->settings['source_customer_var']);
        } elseif (preg_match('#^b_.*$#', $this->settings['source_customer_var'])) {
            // Basic vars have prefix b_
            $value_source_method = 'get';
            $sub_var_name = preg_replace('#^b_#', '', $this->settings['source_customer_var']);
        }

        if (!$value_source_method || !$sub_var_name) {
            return true;
        }

        $customersToEdit = $params['model']->$value_source_method($sub_var_name);
        if (is_array($customersToEdit)) {
            $customersToEdit = array_filter($customersToEdit);
        } else {
            $customersToEdit = [$customersToEdit];
        }

        // get all the source vars
        $copySettings = [];
        foreach ($this->settings as $setting_name => $setting_value) {
            if (!preg_match('#^copy_var_(a|b)_(.*)$#', $setting_name, $matches)) {
                // skip this - we need only the matching settings
                continue;
            }
            $value_source_method = '';
            if ($matches[1] == 'a') {
                // Additional vars have prefix a_
                $value_source_method = 'getPlainVarValue';
            } elseif ($matches[1] == 'b') {
                // Basic vars have prefix b_
                $value_source_method = 'get';
            }

            if (!$value_source_method) {
                continue;
            }
            $copySettings[$setting_value] = $db_model->$value_source_method($matches[2]);
        }

        if (empty($copySettings)) {
            return true;
        }

        $get_old_vars = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);

        $filters_customers = array(
            'where' => array(
                "c.id IN ('" . implode("','", $customersToEdit) . "')"),
            'skip_permissions_check' => true
        );
        $customers = Customers::search($this->registry, $filters_customers);
        foreach ($customers as $customer) {
            $customer->getVars();
            $assocVars = $customer->getAssocVars();
            $old_customer = clone $customer;

            $modelChanged = false;
            foreach ($copySettings as $copyToVar => $newVarValue) {
                if (preg_match('#^a_(.*)$#', $copyToVar, $matches)) {
                    if (isset($assocVars[$matches[1]])) {
                        $assocVars[$matches[1]]['value'] = $newVarValue;
                        $modelChanged = true;
                    }
                } elseif (preg_match('#^b_(.*)$#', $copyToVar, $matches)) {
                    if ($customer->isDefined($matches[1])) {
                        $customer->set($matches[1], $newVarValue, true);
                        $modelChanged = true;
                    }
                }
            }

            if (!$modelChanged) {
                continue;
            }

            $customer->set('vars', array_values($assocVars), true);
            $customer->set('plain_vars', null, true);
            $customer->set('assoc_vars', null, true);
            $customer->unsanitize();

            $this->registry['db']->StartTrans();
            if ($customer->save()) {
                $filters = array(
                    'where' => array(
                        'c.id = ' . $customer->get('id')
                    ),
                    'model_lang' => $customer->get('model_lang')
                );
                $new_customer = Customers::searchOne($this->registry, $filters);
                $new_customer->getVars();
                Customers_History::saveData($this->registry, array('model' => $customer, 'action_type' => 'edit', 'new_model' => $new_customer, 'old_model' => $old_customer));
            } else {
                $this->registry['db']->FailTrans();
            }

            $this->registry['db']->CompleteTrans();
        }
        $this->registry->set('get_old_vars', $get_old_vars, true);
        $this->registry->set('edit_all', $edit_all, true);

        return true;
    }

    protected function replaceModelPlaceholders(string $value, Model $model): string
    {
        $subVars = $this->extractVariables($value);

        // Get the values of the sub-variables
        foreach ($subVars as $subVar) {
            // If there is a model and this is not a before_action automation
            if (empty($this->before_action)) {
                // Get the var value from the model
                if (preg_match('#^a_.*$#', $subVar)) {
                    // Additional vars have prefix a_
                    $valueSourceMethod = 'getPlainVarValue';
                    $subVarName = preg_replace('#^a_#', '', $subVar);
                } elseif (preg_match('#^al_.*$#', $subVar)) {
                    // FORMATTED Additional vars have prefix al_ (labels of dropdown options)
                    $valueSourceMethod = 'getVarValue';
                    $subVarName = preg_replace('#^al_#', '', $subVar);
                } else {
                    // Basic vars have prefix b_
                    $valueSourceMethod = 'get';
                    $subVarName = preg_replace('#^b_#', '', $subVar);
                }
                // Get the sub-variable value from the new model
                $subVarValue = $model->$valueSourceMethod($subVarName);
            } else {
                // Get the sub-variable value from the request
                $subVarName = preg_replace('#^(a|al|b)_#', '', $subVar);
                $subVarValue = $this->registry['request']->get($subVarName);
                if (is_array($subVarValue)) {
                    $subVarValue = array_shift($subVarValue);
                }
            }

            // escape both single and double quotes in replacement values
            // because we cannot know what to expect in expression
            $subVarValue = General::slashesEscape($subVarValue);

            // Replace the sub-variables with their values into the variable value
            $value = str_replace('[' . $subVar . ']', $subVarValue, $value);
        }

        return $value;
    }
}
