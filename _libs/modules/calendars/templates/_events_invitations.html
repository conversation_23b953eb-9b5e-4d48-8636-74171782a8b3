<form method="post" action="{$smarty.server.REQUEST_URI|escape}">
<table cellpadding="1" cellspacing="0" border="0" class="t_table" width="100%">
  <tr>
    <td colspan="4" class="t_caption">
      <div class="t_caption_title">{#events_invitations#|escape}</div>
    </td>
  </tr>
  <tr>
    <td class="hcenter"><a href="#" onclick="setRadioButtons('confirmed_status'); return false;" class="t_caption2_title" title="{#events_status_confirm#|escape}">{#yes#}</a></td>
    <td class="hcenter"><a href="#" onclick="setRadioButtons('not_sure_status'); return false;" class="t_caption2_title" title="{#events_status_not_sure#|escape}">?</a></td>
    <td class="hcenter"><a href="#" onclick="setRadioButtons('denied_status'); return false;" class="t_caption2_title" title="{#events_status_deny#|escape}">{#no#}</a></td>
    <td></td>
  </tr>
  {foreach name='unconfirmed_list' from=$invitations item='event'}
  {include file=`$templatesDir`_events_info.html event=$event->getAll() assign='event_info'}
  <tr>
    <td class="hcenter">
      <input type="radio" name="user_status[{$event->get('id')}]" value="confirmed" id="confirmed_status_{$event->get('id')}" checked="checked" title="{#events_status_confirm#|escape}" />
    </td>
    <td class="hcenter">
      {if $event->get('type_keyword') != 'plannedtime'}
      <input type="radio" name="user_status[{$event->get('id')}]" value="not_sure" id="not_sure_status_{$event->get('id')}" title="{#events_status_not_sure#|escape}" />
      {/if}
    </td>
    <td class="hcenter">
      {if $event->get('type_keyword') != 'plannedtime'}
      <input type="radio" name="user_status[{$event->get('id')}]" value="denied" id="denied_status_{$event->get('id')}" title="{#events_status_deny#|escape}" />
      {/if}
    </td>
    <td {help text_content=$event_info label_content=$event->get('event_start')|date_format:#date_calendar_day_short#|escape popup_only=1}>
      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=view&amp;view={$event->get('id')}">{$event->get('name')|mb_truncate:35:'...':true|escape}</a> {if $event->get('allday_event')}{$event->get('event_start')|date_format:"%d %b. %Y"|escape} ({if $event->get('allday_event') == -1}{$event->get('duration')} {if abs($event->get('duration')) != 1}{#minutes#}{else}{#minute#}{/if}{else}{#events_allday_event#|mb_lower}{/if}){else}{$event->get('event_start')|date_format:"%d %b. %Y, %H:%M"|escape}{/if}
    </td>
  </tr>
  {/foreach}
  <tr>
    <td colspan="4">
      <br />
      &nbsp;<input type="submit" name="send" value="{#reply#}" class="button" />
    </td>
  </tr>
  <tr>
    <td class="t_footer" colspan="4"></td>
  </tr>
</table>
</form>
