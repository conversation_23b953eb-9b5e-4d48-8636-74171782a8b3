{assign var='table_cols' value=8}
{capture assign='current_day_of_week'}{if $monday_start && $calendar->day_of_week == 0}7{else}{$calendar->day_of_week}{/if}{/capture}
{assign var='week_num' value=$calendar->day->week}
{assign var='week' value=$calendar->month->weeks.$week_num}
{assign var='first_week_day_index' value=$week.0}
{assign var='last_week_day_index' value=$week.6}
{assign var='first_week_day' value=$calendar->month->days.$first_week_day_index->dateISO}
{assign var='last_week_day' value=$calendar->month->days.$last_week_day_index->dateISO}
{math assign='day_width' equation='98/x' x=7 format='%d'}
<table border="0" cellpadding="0" cellspacing="0" class="nz-calendar-tbl nz-week-simple">
  <thead>
    <tr class="nz-calendar-tbl-head">
      <th  class="nz-calendar_caption" colspan="{$table_cols}">
        <div class="">{$first_week_day|date_format:#date_calendar_day#} - {$last_week_day|date_format:#date_calendar_day#} ({#week#} {#num#}{$week_num})</div>
      </th>
    </tr>
    <tr  class="nz-calendar-tbl-head">
      <th class="nz-calendar-tbl__weeknum nz-tooltip-trigger nz-tooltip-autoinit"width="2%"
          data-tooltip-content="{#calendars_week_number#|escape}"
          data-tooltip-position="panel: bottom left at: top center">{#num#}</th>
      {if !$monday_start}
        <th class="nz-calendar-tbl__weekend" width="{$day_width}%">{$smarty.config.weekday_0}</th>
      {/if}

      {section name=weekdays loop=7 start=1}
        {capture assign='week_label'}weekday_{$smarty.section.weekdays.index}{/capture}
        <th class="{if $smarty.section.weekdays.iteration >= 6}nz-calendar-tbl__weekend{/if}" width="{$day_width}%">{$smarty.config.$week_label}</th>
      {/section}
      {if $monday_start}
        <th class="nz-calendar-tbl__weekend" width="{$day_width}%">{$smarty.config.weekday_0}</th>
      {/if}
    </tr>
  </thead>
  <tbody>
    <tr class="nz-calendar-tbl-body">
      <td class="nz-calendar-tbl__weeknum">
        {assign var='week_num' value=$calendar->day->week}
        {assign var='week' value=$calendar->month->weeks.$week_num}
        {assign var='w_day' value=$week.0}
        {capture assign='week_num_help_id'}cal_week_simple_num_{$week_num}{/capture}
        <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=week&amp;date={$calendar->month->days.$w_day->year}-{$calendar->month->days.$w_day->month}-{$calendar->month->days.$w_day->day}"
           class="nz-tooltip-trigger nz-tooltip-autoinit"
           data-tooltip-element="#{$week_num_help_id}"
           data-tooltip-position="panel: bottom left at: top center"
          >{$week_num}</a>
        <div id="{$week_num_help_id}" class="nz-tooltip-content nz-tooltip-notch__bottom-left">
          {#calendars_view_event#} <b>{#week#} {#num#}{$week_num}</b>
        </div>
      </td>
      {foreach name='week_days' from=$week key="weekDay" item="day"}
        {assign var="day_num" value=$calendar->month->days.$day->day}
        {assign var="month_num" value=$calendar->month->days.$day->month}
        <td class="nz-calendar-tbl-day{if $calendar->month->days.$day->isToday()} nz--active{/if}{if $calendar->month->days.$day->month != $calendar->month->getMonth()} nz--disabled{/if}{if $calendar->month->days.$day->isWeekend()} nz-calendar-tbl__weekend{/if}">
          {capture assign='tooltip_pos'}{if $weekDay<2}left{elseif $weekDay>4}right{else}center{/if}{/capture}
          <div class="nz-calendar-tbl__daynum">
            {capture assign='daynum_help_content'}daynum_help_tooltip_{$day_num}{/capture}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=calendars&amp;calendars=day&amp;date={$calendar->month->days.$day->year}-{$calendar->month->days.$day->month}-{$calendar->month->days.$day->day}"
               class="nz-tooltip-trigger nz-tooltip-autoinit"
               data-tooltip-element="#{$daynum_help_content}"
               data-tooltip-position="panel: bottom {$tooltip_pos} at: top center"
            >{$calendar->month->days.$day->day|string_format:'%d'}</a>
            <div id="{$daynum_help_content}" class="nz-tooltip-content nz-tooltip-notch__bottom-{$tooltip_pos}">
              {#calendars_view_event#} <b>{$calendar->month->days.$day->timestamp|date_format:#date_calendar_day#}</b>
            </div>
          </div>

          <div class="nz-calendar-_controls">
            {capture assign='addevent_help_content'}addevent_help_tooltip_{$day_num}{/capture}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=events&amp;events=add&amp;date={$calendar->month->days.$day->year}-{$calendar->month->days.$day->month}-{$calendar->month->days.$day->day}%20{$smarty.now|date_format:'%T'}"
               class="nz-icon-button nz-tooltip-trigger nz-tooltip-autoinit"
               data-tooltip-element="#{$addevent_help_content}"
               data-tooltip-position="panel: bottom {$tooltip_pos} at: top center"
            >{$theme->getIconForAction('addevent')}</a>
            <div id="{$addevent_help_content}" class="nz-tooltip-content nz-tooltip-notch__bottom-{$tooltip_pos}">
              {#calendars_add_event#} <b>{$calendar->month->days.$day->timestamp|date_format:#date_calendar_day#}</b>
            </div>
          </div>

          {include file=`$templatesDir`_events_week_simple.html num_events=$months_count_events.$month_num.$day_num day=$calendar->month->days.$day}
        </td>
      {/foreach}
    </tr>
  </tbody>
</table>
