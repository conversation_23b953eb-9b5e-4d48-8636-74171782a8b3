<?php

/**
 * Customers_Contactperson model class
 */
Class Customers_Contactperson extends Model {
    public $modelName = 'Customers_Contactperson';

    /**
     * Contact parameters saved in the DB as arrays
     */
    public $contactParameters = array('phone', 'fax', 'gsm', 'email', 'web', 'skype', 'othercontact');

    /**
     * Contact parameters to save in add mode
     */
    public $predefinedPersonContactParameters = array('phone', 'gsm', 'email');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
        if ($this->origin == 'database') {
            $this->getContacts();
        } elseif ($this->origin == 'request') {
            $this->getPostContacts();
        }
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - performed action with model
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if ($this->get('web')) {
            foreach ($this->get('web') as $index => $web) {
                if (!Validator::validUrl($web)) {
                    $this->raiseError('error_invalid_url', 'web|' . $index);
                    $this->raiseError('error_invalid_contact_data', 'link', -1, array(mb_strtolower($this->i18n('customers_contacts'), 'UTF-8')));
                }
            }
        }

        if ($this->get('email')) {
            foreach ($this->get('email') as $index => $email) {
                if (!Validator::validEmail($email)) {
                    $this->raiseError('error_invalid_email', 'email|' . $index);
                    $this->raiseError('error_invalid_contact_data', 'link', -1, array(mb_strtolower($this->i18n('customers_contacts'), 'UTF-8')));
                }
            }
        }

        if (!$this->get('parent_customer_id')) {
            $this->raiseError($this->getBranchLabels('error_no_parent_customer_specified'), 'parent_customer_id');
        }
        if (!$this->get('parent_branch')) {
            $this->raiseError($this->getBranchLabels('error_no_parent_branch_specified'), 'parent_branch');
        }

        if (!$this->get('lastname')) {
            $this->raiseError('error_no_lastname_specified', 'lastname');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        if ($this->get('custom_modified')) {
            //this is used to provide correct modification date for the record
            //when we sync with outlook
            $set['added'] = sprintf("added='%s'", $this->get('custom_modified'));
        } else {
            $set['added'] = sprintf("added=now()");
        }
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_CUSTOMERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new customer's contact person base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        if ($this->get('set_main')) {
            $query2 = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                      'SET is_main = 0' . "\n" .
                      'WHERE parent_customer = "' . $this->get('parent_branch') . '"' . "\n" .
                      '  AND id != "' . $this->get('id') . '"' . "\n";
            $db->Execute($query2);
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        if ($this->get('set_main')) {
            $query2 = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                      'SET is_main = 0' . "\n" .
                      'WHERE parent_customer = "' . $this->get('parent_branch') . '"' . "\n" .
                      '  AND id != "' . $this->get('id') . '"' . "\n";
            $db->Execute($query2);
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('lastname')) {
            $update['lastname']  = sprintf("lastname='%s'", $this->get('lastname'));
        }
        if ($this->isDefined('notes')) {
            $update['notes']  = sprintf("notes='%s'", $this->get('notes'));
        }
        if ($this->isDefined('position')) {
            $update['position']  = sprintf("position='%s'", $this->get('position'));
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);
            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing customer i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->get('main_contact')) {
            $set['is_main'] = sprintf("is_main=%d", 1);
            $this->set('set_main', 1);
        } else {
            $set['is_main'] = sprintf("is_main=%d", 0);
            $this->set('set_main', 0);
        }
        if ($this->get('financial_person')) {
            $set['admit_VAT_credit'] = sprintf("admit_VAT_credit=%d", 1);
        } else {
            $set['admit_VAT_credit'] = sprintf("admit_VAT_credit=%d", 0);
        }
        if ($this->isDefined('postal_code')) {
            $set['postal_code'] = sprintf("postal_code='%s'", $this->get('postal_code'));
        }
        if ($this->isDefined('parent_branch')) {
            $set['parent_branch'] = sprintf("parent_customer='%s'", $this->get('parent_branch'));
        }
        if ($this->isDefined('assign_user')) {
            $set['assign_user'] = sprintf("assigned=%d", $this->get('assign_user'));
        }
        if ($this->isDefined('salutation')) {
            $set['salutation']      = sprintf("salutation='%s'", $this->get('salutation'));
        }
        if ($this->isDefined('permission')) {
            $set['permission'] = sprintf("permission='%s'", $this->get('permission'));
        }

        foreach ($this->contactParameters as $contact_param) {
            if ($this->get($contact_param)) {
                $contact_data = array();
                $contact_notes = $this->get($contact_param . '_note');
                foreach ($this->get($contact_param) as $idx => $contact) {
                    $contact_data[] = sprintf("%s%s", $contact, (!empty($contact_notes[$idx])) ? '|' . $contact_notes[$idx] : '');
                }
                $set[$contact_param] = sprintf("%s='%s'", $contact_param, implode("\n", $contact_data));
            } else {
                $set[$contact_param] = sprintf("%s=''", $contact_param);
            }
        }

        $set['subtype']         = sprintf("subtype='%s'", 'contact');
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        $set['group']           = sprintf("`group`=%d", 1);
        if ($this->get('custom_modified')) {
            //this is used to provide correct modification date for the record
            //when we sync with outlook
            $set['modified']    = sprintf("modified='%s'", $this->get('custom_modified'));
        } else {
            $set['modified']    = sprintf("modified=now()");
        }
        $set['modified_by']     = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Get customers contacts and sets them to model
     */
    public function getContacts() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        foreach ($this->contactParameters as $contact_param) {
            if ($this->get($contact_param)) {
                $contact_rows = explode("\n", $this->get($contact_param));
                $contacts = array();
                $contact_notes = array();
                foreach ($contact_rows as $idx => $contact_data) {
                    if (strpos($contact_data, '|') !== false) {
                        list($contact, $contact_note) = explode("|", $contact_data, 2);
                        $contacts[$idx] = $contact;
                        $contact_notes[$idx] = $contact_note;
                    } else {
                        $contacts[$idx] = $contact_data;
                        $contact_notes[$idx] = "";
                    }
                }
                $this->set($contact_param, $contacts, true);
                $this->set($contact_param . '_note', $contact_notes, true);
            }
        }
    }

    /**
     * Get customers contacts from post and sets them to model
     */
    public function getPostContacts() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];
        $links = $this->registry['request']['link'];
        if (empty($links)) {
            $links = array();
        }
        $link_notes = $this->registry['request']['link_notes'];
        if (empty($link_notes)) {
            $link_notes = array();
        }
        $link_types = $this->registry['request']['link_types'];

        $contacts = array();
        $contact_notes = array();
        $records = array();
        $inserted_links = array();
        foreach ($links as $index => $link) {
            if (!isset($link_types[$index]) || !isset($inserted_links[$link_types[$index]])) {
                $inserted_links[$link_types[$index]] = array();
            }
            if (trim($link) && !in_array($link, $inserted_links[$link_types[$index]]) && !empty($link)) {
                $inserted_links[$link_types[$index]][] = $link;
                $contacts[$link_types[$index]][$index] = $link;
                $contact_notes[$link_types[$index]][$index] = $link_notes[$index];
            }
        }

        foreach ($contacts as $key => $recs) {
            $this->set($key , $recs, true);
        }
        foreach ($contact_notes as $key => $recs) {
            $this->set($key . '_note' , $recs, true);
        }
    }

    /**
     * Gets translations of model
     *
     * @return array - array of langs
     */
    public function getTranslations() {
        if (!$this->get('id')) {
            return array();
        }

        if ($this->isDefined('translations')) {
            return $this->get('translations');
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT ci18n.lang ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (c.id=ci18n.parent_id)'. "\n";

        //where clause
        $sql['where'] = 'WHERE c.id=' . $this->get('id') . "\n";

        $sql['order'] = 'ORDER BY ci18n.translated' . "\n";

        $query = implode("\n", $sql);

        $records = $this->registry['db']->GetCol($query);

        if ($records) {
            $this->set('translations', $records, true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $records;
    }
}

?>
