<?php

class Communications_Controller extends Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'ajax_load_communication_add_panel':
                $this->_loadCommunicationAddPanel();
                break;
            case 'ajax_list_communications':
                $this->_listCommunications();
                break;
            case 'ajax_save_communication_comment':
                $this->_saveCommunicationComment();
                break;
            case 'ajax_send_communication_email':
                $this->_sendCommunicationEmail();
                break;
            case 'ajax_change_email':
                $this->_changeEmail();
                break;
            case 'ajax_prepare_resend':
                $this->_prepareResendOptions();
                break;
            case 'ajax_resend':
                $this->_resend();
                break;
            case 'ajax_get_communications_info':
                $this->_getCommunicationsInfo();
                break;
        }
    }

    /**
     * Function to list communications in the communications screen
     */
    private function _listCommunications() {
        $request = $this->registry['request'];
        //get the requested model ID
        $id = intval($request->get('model_id'));

        $module = $request->get('module');

        $module_specific_data = $this->includeRequiredClasses($module, $id);
        $filters = $module_specific_data['filters'];
        $factory_class_name = $module_specific_data['factory_class_name'];

        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }

        $current_model = $factory_class_name::searchOne($this->registry, $filters);

        if (!$this->registry->isRegistered('communication') && $current_model) {
            $this->registry->set('communication', $current_model->sanitize());
        }

        if ($request->get('communication_type')) {
            $this->registry->set('communication_type', $request->get('communication_type'), true);
        }

        $this->registry->set('ajax_listing', true, true);

        require_once $this->viewersDir . 'communications.viewer.php';
        $this->viewer = new Communications_Viewer($this->registry);
        $this->viewer->getModel();

        if ($request->get('communication_type') == 'minitasks') {
            $this->viewer->template = '_communication_minitasks_list_panel.html';
        } else {
            $this->viewer->template = '_communication_list_panel.html';
        }
        $this->viewer->setFrameset($this->viewer->template);

        $this->viewer->prepare();

        if (router::isJSONRequired()) {
            //send data as JSON when required by the HTTP_ACCEPT header
            header('Content-Type: application/json');
            print json_encode(
                array(
                    'pagination' => $this->viewer->data['pagination'],
                    'models' => $this->viewer->data['communications'],
                )
            );
            exit;
        }

        $this->viewer->display();
        exit;
    }

    /**
     * Function to load panel for adding communication records
     */
    private function _loadCommunicationAddPanel() {
        $request = $this->registry['request'];

        $model_id = intval($request->get('model_id'));
        $type_record = $request->get('type_record');
        $action = $request->get('action');
        $module = $request->get('module');
        $communication_type = $request->get('communication_type');

        $module_specific_data = $this->includeRequiredClasses($module, $model_id);
        $filters = $module_specific_data['filters'];
        $factory_class_name = $module_specific_data['factory_class_name'];

        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }

        $model_name = General::plural2singular($factory_class_name);

        // takes the current model
        $current_model = $factory_class_name::searchOne($this->registry, $filters);
        if (!$current_model || !$current_model->checkPermissions($communication_type . '_add')) {
            $messageViewer = new Viewer($this->registry);
            $messageViewer->setFrameset('message.html');
            $messageViewer->data['items'] = array($this->i18n(!$current_model ? 'error_right_notallowed' : 'error_no_access_to_model'));
            $messageViewer->data['display'] = 'error';

            $result_operation = array(
                'result'    => 0,
                'messages'  => $messageViewer->fetch()
            );
            print json_encode($result_operation);
            exit;
        }

        if ($type_record == 'comment') {
            if ($request->get('edit_id')) {
                require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                $comment = Comments::searchOne(
                    $this->registry,
                    array(
                        'where' => array('c.id = ' . $request->get('edit_id')),
                        'sanitize' => true
                    )
                );
            }

            // sets the viewer and its variables
            $this->viewer = new Viewer($this->registry);
            $this->viewer->setFrameset('_communication_comments_panel.html');

            if (!empty($comment)) {
                //check last modified date
                $check_date = time() - strtotime($comment->get('modified')) < 60*PH_COMMENTS_EDIT_INTERVAL;
                //check owner
                $check_user = $comment->get('added_by') == $this->registry['currentUser']->get('id');

                if ($check_date && $check_user) {
                    $this->viewer->data['comment_id'] = $comment->get('id');
                    $this->viewer->data['comment_subject'] = $comment->get('subject');
                    $this->viewer->data['comment_content'] = $comment->get('content');
                    $this->viewer->data['comment_is_portal'] = $comment->get('is_portal');
                    $this->viewer->data['comment_parent_id'] = $comment->get('parent_id');
                    $this->viewer->data['dont_notify'] = $request->get('dont_notify');
                } else {
                    if (!$check_user) {
                        $this->registry['messages']->setError($this->i18n('error_communications_comments_edit_owner'));
                    }
                    if (!$check_date) {
                        $this->registry['messages']->setError($this->i18n('error_communications_comments_edit_expired'));
                    }

                    $message_method = 'getErrors';
                    $display_messages = 'error';
                    $operation_result = 0;

                    $messageViewer = new Viewer($this->registry);
                    $messageViewer->setFrameset('message.html');
                    $messageViewer->data['items'] = $this->registry['messages']->$message_method();
                    $messageViewer->data['display'] = $display_messages;

                    $result_operation = array(
                        'result'    => $operation_result,
                        'messages'  => $messageViewer->fetch()
                    );

                    print json_encode($result_operation);
                    exit;
                }
            }

            $this->viewer->data['model_id'] = $model_id;
            $module_single = General::plural2singular($module);
            $module_elements = explode('_', $module_single);
            foreach ($module_elements as $k => $me) {
                $module_elements[$k] = ucfirst($me);
            }
            $this->viewer->data['model_name'] = implode('_', $module_elements);
            $this->viewer->data['communication_session_param'] = 'communication_list_panel';
            $this->viewer->data['communication_type'] = $communication_type;
            $this->viewer->data['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
        } elseif ($type_record == 'email') {
            $this->viewer = new Viewer($this->registry);
            $this->viewer->setFrameset('_communication_emails_panel.html');

            $this->viewer->data['model_id'] = $model_id;
            $this->viewer->data['communication_type'] = $communication_type;

            if ($request->get('reply')) {
                $replyData = $this->getReplyData($request->get('reply'), $current_model);
                $mailCode = $request->get('reply');
            } elseif ($request->get('replyAll')) {
                $replyData = $this->getReplyData($request->get('replyAll'), $current_model, true);
                $mailCode = $request->get('replyAll');
            }

            $mail_body = $subject = '';
            $recipients = array(
                'to'  => array(),
                'cc'  => array(),
                'bcc' => array(),
            );
            if (!empty($replyData)) {

                $mail_body = $replyData['content'];
                if ($mail_body) {
                    $mail_body = "<p></p><hr /><blockquote cite=\"{$mailCode}\">{$replyData['content']}</blockquote>";
                }
                $subject = $replyData['subject'];
                $subject = $this->prependReplySubject($subject, $current_model);
                $origins = ['to', 'cc', 'bcc'];
                foreach($origins as $type) {
                    if (!empty($replyData[$type])) {
                        foreach ($replyData[$type] as $e) {
                            $recipients[$type][] = trim(sprintf('%s <%s>', $e['name'], $e['email']));
                        }
                    }
                }
            }
            if ($request->get('mail_body')) {
                $mail_body = $request->get('mail_body');
            }
            if ($request->get('subject')) {
                $subject = $request->get('subject');
            }

            // prepare the base autocompleter definitions
            $base_autocompleter_definitions = array(
                'type'                  => 'autocompleter',
                'autocomplete_type'     => 'customers',
                'exclude_oldvalues'     => true,
                'autocomplete'      => array(
                    'type'          => 'customers',
                    'url'           => sprintf('%s?%s=%s&%s=ajax_email_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'customers', 'customers'),
                    'suggestions'   => '<name> <<email>>',
                    'buttons_hide' => 'search',
                    'buttons' => 'clear',
                    'clear' => 1,
                    'min_chars' => 3
                )
            );

            // Prepare the autocompleter for recipient to mail
            $autocomplete_email = $base_autocompleter_definitions;
            $autocomplete_email['custom_id'] = 'customer_email';
            $autocomplete_email['name'] = 'customer_email';
            $autocomplete_email['autocomplete']['fill_options'] = array(
                '$customer_email_autocomplete => <name> <<email>>',
                '$customer_email => <name> <<email>>'
            );

            // Prepare the autocompleter for recipient cc mail
            $autocomplete_email_cc = $base_autocompleter_definitions;
            $autocomplete_email_cc['custom_id'] = 'customer_email_cc';
            $autocomplete_email_cc['name'] = 'customer_email_cc';
            $autocomplete_email_cc['autocomplete']['fill_options'] = array(
                '$customer_email_cc_autocomplete => <name> <<email>>',
                '$customer_email_cc => <name> <<email>>'
            );

            // Prepare the autocompleter for recipient bcc mail
            $autocomplete_email_bcc = $base_autocompleter_definitions;
            $autocomplete_email_bcc['custom_id'] = 'customer_email_bcc';
            $autocomplete_email_bcc['name'] = 'customer_email_bcc';
            $autocomplete_email_bcc['autocomplete']['fill_options'] = array(
                '$customer_email_bcc_autocomplete => <name> <<email>>',
                '$customer_email_bcc => <name> <<email>>'
            );

            $this->viewer->data['autocomplete_email'] = $autocomplete_email;
            $this->viewer->data['autocomplete_email_cc'] = $autocomplete_email_cc;
            $this->viewer->data['autocomplete_email_bcc'] = $autocomplete_email_bcc;

            // check for customer e-mail address
            $selected_customer = '';
            if ($current_model->modelName == 'Customer') {
                $selected_customer = clone $current_model;
                $selected_customer->sanitize();
            } elseif ($current_model->get('customer')) {
                require_once(PH_MODULES_DIR . 'customers/models/customers.factory.php');
                // get the selected customer
                $customer_filters = array(
                    'where' => array(
                        'c.id = \'' . $current_model->get('customer') . '\''
                    ),
                    'model_lang' => $current_model->get('model_lang'),
                    'sanitize'   => true
                );
                $selected_customer = Customers::searchOne($this->registry, $customer_filters);
            }

            if ($selected_customer && empty($recipients['to'])) {
                // get the e-mail addresses of the customer and all of its branches and contact persons
                $customer_mail_contacts = $selected_customer->getAllEmails();

                // inspects the e-mail and sets default only if it is exactly one
                $default_sent_mail = '';
                $default_sent_mail_recipient_name = '';
                foreach ($customer_mail_contacts as $recp_name => $cmc) {
                    foreach ($cmc as $contact_details) {
                        if (empty($default_sent_mail)) {
                            $default_sent_mail = $contact_details['label'];
                            if (preg_match('#\(branch_' . $selected_customer->get('main_branch_id') . '\)#', $contact_details['option_value'])) {
                                $default_sent_mail_recipient_name = $selected_customer->get('name');
                            } else {
                                $default_sent_mail_recipient_name = $recp_name;
                            }
                        } elseif ($default_sent_mail!=$contact_details['label']) {
                            $default_sent_mail = '';
                            break 2;
                        }
                    }
                }

                if ($default_sent_mail) {
                    // structure the e-mail address in a readable version
                    $default_sent_mail = sprintf('%s <%s>', $default_sent_mail_recipient_name, $default_sent_mail);
                    $default_sent_mail = trim($default_sent_mail);
                }
                $recipients['to'][] = $default_sent_mail;

                // if customer has financial persons with emails, display toggle for their selection
                if ($selected_customer->get('is_company') && $customer_mail_contacts &&
                    array_filter($customer_mail_contacts, function($a) {
                        return array_filter($a, function($b) {
                            return !empty($b['class_name']) && preg_match('#financial_person#', $b['class_name']);
                        });
                    })) {
                    $this->viewer->data['customer_toggle_financial_persons'] = $selected_customer->get('id');
                }
            }
            unset($selected_customer);

            $mails = $current_model->getEmailTemplates();

            $mail_options = array();
            foreach ($mails as $mail) {
                $mail_options[$mail->get('id')] = array(
                    'label' => $mail->get('subject'),
                    'option_value' => $mail->get('id'),
                    'active_option' => $mail->isActivated(),
                );
            }
            $mail_template = $this->registry['request']->get('mail_template');
            if ($mail_template && !array_key_exists($mail_template, $mail_options)) {
                array_unshift(
                    $mail_options,
                    array(
                        'label' => $mail_template,
                        'option_value' => $mail_template,
                        'active_option' => '1',
                    )
                );
            }
            $this->viewer->data['emails_templates'] = $mail_options;

            //prepare generate options
            require_once(PH_MODULES_DIR . 'patterns/models/patterns.factory.php');
            $filters = array(
                'where' => array(
                    'p.model = \'' . $model_name . '\'',
                    'p.model_type = \'' . $current_model->get('type') . '\'',
                    'p.active = 1',
                    'p.list = 0'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $current_model->get('model_lang'),
                'sanitize' => true
            );
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // if model has an id and it is an invoice or a proforma invoice
            if ($current_model->get('id') && preg_match('#Finance_Incomes_Reason#i', $current_model->modelName) &&
              in_array($current_model->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE))) {
                // if model has a print form then search only patterns for print form
                if ($current_model->checkForPrintForm()) {
                    $filters['where'][] = 'p.for_printform = 1';
                } else {
                    $filters['where'][] = 'p.for_printform = 0';
                }
            }
            // if model is a handover, filter patterns by direction
            if ($current_model->get('id') && $current_model->modelName == 'Finance_Warehouses_Document' &&
            $current_model->get('type') == PH_FINANCE_TYPE_HANDOVER) {
                $filters['where'][] = 'p.handover_direction IN ("' . $current_model->get('direction') . '", "both")';
            }
            // if model has company, filter patterns by company
            if ($current_model->get('company') &&
            preg_match('#^Finance_((Incomes|Expenses)_Reason|Warehouses_Document|Payment)$#i', $current_model->modelName)) {
                $filters['where'][] = '(p.company = \'' . $current_model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            $files = array();
            if (method_exists($current_model, 'getFiles')) {
                $files = $current_model->getFiles();
            }

            $files_options = array();
            if (isset($files['generated'])) {
                $og = $this->i18n('attachments_genfiles_title');
                foreach ($files['generated'] as $file) {
                    if (file_exists($file['path'])) {
                        $label = $file['name'];
                        if ($file['filename'] != $file['name']) {
                            $label .= ' (' . $file['filename'] . ')';
                        } elseif (!empty($file['description'])) {
                            $label .= ' (' . $file['description'] . ')';
                        }
                        $files_options[$og][] = array(
                            'label' => $label,
                            'option_value' => 'file_' . $file['id']
                        );
                    }
                }
            }
            if (isset($files['attachments'])) {
                $og = $this->i18n('attachments_files_title');
                foreach ($files['attachments'] as $file) {
                    if (file_exists($file['path'])) {
                        $label = $file['name'];
                        if ($file['filename'] != $file['name']) {
                            $label .= ' (' . $file['filename'] . ')';
                        } elseif (!empty($file['description'])) {
                            $label .= ' (' . $file['description'] . ')';
                        }
                        $files_options[$og][] = array(
                            'label' => $label,
                            'option_value' => 'file_' . $file['id']
                        );
                    }
                }
            }
            if ($patterns) {
                $og = $this->i18n('communications_generate_from_pattern');
                foreach ($patterns as $pattern) {
                    $files_options[$og][] = array(
                        'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                        'option_value' => 'pattern_' . $pattern->get('id')
                    );
                }
            }

            $this->viewer->data['files_options'] = $files_options;

            // prepare default receipt_email
            $default_receipt_email = '';
            if ($this->registry['currentUser']->get('email') != $this->registry['config']->getParam('emails', 'replyto_email') &&
            $this->registry['config']->getParam('emails', 'sender_custom_user_emails')) {
                $default_receipt_email = $this->registry['config']->getParam('emails', 'sender_custom_user_emails');
            } else {
                $default_receipt_email = $this->registry['currentUser']->get('email');
            }
            $this->viewer->data['default_receipt_email'] = $default_receipt_email;

            //PLACEHOLDERS
            $email = new Email($this->registry, array('model' => $current_model->modelName, 'model_type' => $current_model->get('type'), 'model_lang' => $current_model->get('model_lang')));

            //prepare the fck editor
            $editor = new Editor($this->registry, 'body', array('placeholders' => $email->getPlaceholders(), 'module' => 'emails'));
            $editor->setContent($mail_body);
            $this->viewer->data['editor_content'] = $editor->create();

            //whether attach signature option will be checked by default
            $this->viewer->data['attach_signature'] = $this->registry['config']->getParam('emails', 'attach_signature');

            $this->viewer->data['subject'] = $subject;

            $this->viewer->data['recipients'] = $recipients;
        }

        if (!empty($this->viewer)) {
            $this->viewer->data['current_model'] = $current_model->sanitize();
            $content = $this->viewer->fetch();
            print json_encode($content);
        }

        exit;
    }

    /**
     * Function to add comments when in communication screen
     *
     * @var string $mailId - mail code or ID
     * @var Model $model - the model
     * @var bool $replyAll - whether to reply all
     * @return array $data - reply data
     */
    private function getReplyData($mailId, $model, $replyAll = false) {
        $filters = array(
            'communication_type' => 'emails'
        );

        // check if $mailId is numeric and (if numeric) check if it is integer
        // because it is possible code (taken with uniqid) to have only digits
        if (is_numeric($mailId) && intval($mailId) == $mailId) {
            //id
            $filters['communication_id'] = $mailId;
        } else {
            //code
            $filters['code'] = $mailId;
        }
        $communications_record = Communications::searchCommunications($this->registry, $filters);
        if (empty($communications_record)) {
            return array();
        }

        $communications_record = reset($communications_record);
        $data = array(
            'subject' => $communications_record['subject'],
            'content' => $communications_record['content'],
            'to' => array(),
            'cc' => array(),
        );

        $origins = ['to'];
        if ($replyAll) {
            $origins[] = 'cc';
            $origins[] = 'bcc';
        }
        $excludeSender = $excludeOriginalSender = '';
        if ($communications_record['status'] == 'received') {
            $data['to'][] = array(
                'email' => $communications_record['from'],
                'name' => ''
            );
            //get the sender data (from, reply to)
            $senderData = $model->getSenderData();
            $excludeSender = $senderData['from_email'];
            $excludeOriginalSender = $communications_record['from'];
        }
        foreach($communications_record['to'] as $to) {
            if (in_array($to['origin'], $origins) &&
                $to['email'] != $excludeSender &&
                $to['email'] != $excludeOriginalSender) {
                $data[$to['origin']][] = array(
                    'email' => $to['email'],
                    'name' => $to['name']
                );
            }
        }

        return $data;
    }

    /**
     * Function to add comments when in communication screen
     */
    private function _saveCommunicationComment() {
        $request = $this->registry['request'];

        require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';

        $comment = Comments::buildModel($this->registry);
        if ($request->get('comment_id')) {
            $comment->set('id', $request->get('comment_id'), true);
        }

        if ($request->get('parent_id')) {
            $comment->set('parent_id', $request->get('parent_id'), true);
        }

        if ($request->get('dont_notify_edit')) {
            $comment->set('skip_send_email', true, true);
        }

        //use the parent model as model for automations (Document, Customer, Task, etc. not Comment)
        $model = $comment->getParentModel();
        $this->model = $model;
        $this->old_model = clone $model;
        $comment_old = null;
        if ($request->get('comment_id')) {
            $comment_old = Comments::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        "c.id = '{$request->get('comment_id')}'",
                    ),
                    'sanitize' => true,
                )
            );
        }

        if ($comment->save()) {

            $history_saved = $comment->saveHistory(null, $comment_old);

            //show corresponding message
            $this->registry['messages']->setMessage(
                $this->i18n('message_communications_comments_' . ($request->get('comment_id') ? 'edit' : 'add') . '_success'), '', -1);
            $this->registry['messages']->setMessage(sprintf($this->i18n('message_communications_comments_edit_interval'), PH_COMMENTS_EDIT_INTERVAL));

            $message_method = 'getMessages';
            $display_messages = 'message';
            $operation_result = 1;
        } else {
            //some error occurred
            //show corresponding error(s)
            $this->registry['messages']->setError($this->i18n(
                'error_communications_comments_' . ($request->get('comment_id') ? 'edit' : 'add') . '_failed'), '', -1);

            $message_method = 'getErrors';
            $display_messages = 'error';
            $operation_result = 0;
        }

        //set comment object to the model, so it can be used in automations
        $this->model->set('comment', $comment, true);

        $result_operation = array(
            'result'    => $operation_result,
        );

        if (router::isJSONRequired()) {
            $result_operation[$display_messages . 's']  = array_values($this->registry['messages']->$message_method());
        } else {
            $messageViewer = new Viewer($this->registry);
            $messageViewer->setFrameset('message.html');
            $messageViewer->data['items'] = $this->registry['messages']->$message_method();
            $messageViewer->data['display'] = $display_messages;

            $result_operation['messages'] = $messageViewer->fetch();
        }
        if ($operation_result) {
            $this->actionCompleted = true;
            if ($request->get('inline_add')) {

                list(, $pagination) = Communications::searchCommunications(
                    $this->registry,
                    array(
                        'id' => $model->get('id'),
                        'model' => $model->modelName,
                        'archive' => $model->get('archived_by'),
                        'communication_type' => 'comments',
                        'sort' => array('id DESC'),
                        'paginated' => true,
                        'display' => 1,
                    )
                );
                $result_operation['total'] = $pagination['total'];

                if ($request->get('history_total') && !empty($history_saved)) {
                    list(, $result_operation['history_total']) = History::getData(
                        $this->registry,
                        array(
                            'model' => $model,
                            'history_activity' => 1,
                            'limit' => 1,
                        )
                    );
                }
            } elseif (!router::isJSONRequired()) {
                // set messages in session to be displayed when communications list is reloaded
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        print json_encode($result_operation);
        $this->registry->set('exit_after', true, true);
    }

    /**
     * Function to send emails when in communication screen
     */
    private function _sendCommunicationEmail() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = intval($request->get('model_id'));

        $properties = $request->isPost() ? $request->getAll('post') : array();
        $module = $request->get('module');

        $module_specific_data = $this->includeRequiredClasses($module, $id, true);
        $filters = $module_specific_data['filters'];
        $factory_class_name = $module_specific_data['factory_class_name'];
        $history_class_name = $module_specific_data['history_class_name'];

        $current_model = $factory_class_name::searchOne($this->registry, $filters);
        if ($current_model) {
            foreach ($properties as $key => $value) {
                if (!$current_model->isDefined($key)) {
                    $current_model->set($key, $value, true);
                }
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_right_notallowed'));
        }

        $this->model = $current_model;
        $this->old_model = clone $current_model;
        $error = true;
        if ($current_model) {
            $sendResult = $current_model->sendAsMail();
            if ($sendResult !== false && empty($sendResult['erred'])) {
                //show message 'message_communications_email_success'
                $this->registry['messages']->setMessage($this->i18n('message_communications_email_success'), '', -1);

                if ($history_class_name !== false) {
                    $audit_parent = $history_class_name::saveData(
                        $this->registry,
                        array(
                            'model' => $current_model,
                            'action_type' => 'email',
                            'new_model' => $current_model,
                            'old_model' => $current_model));
                }

                $this->notifyEmailSent($current_model);

                $message_method = 'getMessages';
                $display_messages = 'message';
                $operation_result = 1;
                $error = false;
            }
        }
        if ($error) {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_communications_email_failed'), '', -1);

            $message_method = 'getErrors';
            $display_messages = 'error';
            $operation_result = 0;
        }

        $result_operation = array(
            'result'    => $operation_result,
        );

        if (router::isJSONRequired()) {
            $result_operation[$display_messages . 's']  = array_values($this->registry['messages']->$message_method());
        } else {
            $messageViewer = new Viewer($this->registry);
            $messageViewer->setFrameset('message.html');
            $messageViewer->data['items'] = $this->registry['messages']->$message_method();
            $messageViewer->data['display'] = $display_messages;

            $result_operation['messages'] = $messageViewer->fetch();
        }

        if ($operation_result) {
            $this->actionCompleted = true;
            if ($request->get('inline_add')) {
                list(, $pagination) = Communications::searchCommunications(
                    $this->registry,
                    array(
                        'id' => $current_model->get('id'),
                        'model' => $current_model->modelName,
                        'archive' => $current_model->get('archived_by'),
                        'communication_type' => 'emails',
                        'sort' => array('id DESC'),
                        'paginated' => true,
                        'display' => 1,
                    )
                );
                $result_operation['total'] = $pagination['total'];

                if ($request->get('history_total') && !empty($audit_parent)) {
                    list(, $result_operation['history_total']) = History::getData(
                        $this->registry,
                        array(
                            'model' => $current_model,
                            'history_activity' => 1,
                            'limit' => 1,
                        )
                    );
                }
            } elseif (!router::isJSONRequired()) {
                // set messages in session to be displayed when communications list is reloaded
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        print json_encode($result_operation);
        $this->registry->set('exit_after', true, true);
    }

    /**
     * Function to change the email body depending on the selected template
     */
    private function _changeEmail() {
        $request = $this->registry['request'];

        if ($request->get('email_content')) {
            //get the requested model ID
            $id = intval($request->get($this->action));
            $module = $request->get('module');

            $module_specific_data = $this->includeRequiredClasses($module, $id);
            $factory_class_name = $module_specific_data['factory_class_name'];

            if ($factory_class_name) {
                $filters = $module_specific_data['filters'];
                if ($model_lang = $this->registry['request']->get('model_lang')) {
                    $filters['model_lang'] = $model_lang;
                }

                $current_model = $factory_class_name::searchOne($this->registry, $filters);

                $mail = $request->get('email_content');
                $mails = $current_model ? $current_model->getEmailTemplates() : array();
                $mail_body = '';
                foreach ($mails as $email) {
                    if ($email->get('id') == $mail) {
                        $mail_body = $email->get('body');
                        break;
                    }
                }

                echo $mail_body;
            }
            exit();
        } else {
            exit();
        }
    }

    /**
     * Function to manage filters and include the needed classes
     *
     * @param string $module - current module(+controller)
     * @param string $model_id - id of current model
     * @param bool $include_history_classes - whether to prepare data for history class
     * @return array - prepared data
     */
    private function includeRequiredClasses($module, $model_id, $include_history_classes = false) {
        $results = array(
            'filters'            => array(),
            'factory_class_name' => '',
            'history_class_name' => ''
        );
        switch ($module) {
            case 'contracts':
                require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                $results['filters']['where'] = array(
                    'co.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Contracts';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
                    require_once PH_MODULES_DIR . 'contracts/models/contracts.audit.php';
                    $results['history_class_name'] = 'Contracts_History';
                }
                break;
            case 'documents':
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                $results['filters']['where'] = array(
                    'd.id = ' . $model_id
                );
                $results['filters']['where'][] = 'd.archive = \'all\'';

                $results['factory_class_name'] = 'Documents';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                    require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
                    $results['history_class_name'] = 'Documents_History';
                }
                break;
            case 'projects':
                require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                $results['filters']['where'] = array(
                    'p.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Projects';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'projects/models/projects.history.php';
                    require_once PH_MODULES_DIR . 'projects/models/projects.audit.php';
                    $results['history_class_name'] = 'Projects_History';
                }
                break;
            case 'finance_budgets':
                require_once PH_MODULES_DIR . 'finance/models/finance.budgets.factory.php';
                $results['filters']['where'] = array(
                    'fb.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Finance_Budgets';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.budgets.audit.php';
                    require_once PH_MODULES_DIR . 'finance/models/finance.budgets.history.php';
                    $results['history_class_name'] = 'Finance_Budgets_History';
                }
                break;
            case 'finance_warehouses_documents':
                require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
                $results['filters']['where'] = array(
                    'fwd.id = ' . $model_id
                );
                $this->registry->set('no_GT2', true, true);
                $results['factory_class_name'] = 'Finance_Warehouses_Documents';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.audit.php';
                    require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php';
                    $results['history_class_name'] = 'Finance_Warehouses_Documents_History';
                }
                break;
            case 'finance_transfers':
                require_once PH_MODULES_DIR . 'finance/models/finance.transfers.factory.php';
                $results['filters']['where'] = array(
                    'ft.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Finance_Transfers';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.transfers.audit.php';
                    require_once PH_MODULES_DIR . 'finance/models/finance.transfers.history.php';
                    $results['history_class_name'] = 'Finance_Transfers_History';
                }
                break;
            case 'finance_payments':
                require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
                $results['filters']['where'] = array(
                    'fp.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Finance_Payments';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.payments.audit.php';
                    require_once PH_MODULES_DIR . 'finance/models/finance.payments.history.php';
                    $results['history_class_name'] = 'Finance_Payments_History';
                }
                break;
            case 'finance_incomes_reasons':
                require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
                $results['filters']['where'] = array(
                    'fir.id = ' . $model_id
                );
                $this->registry->set('no_GT2', true, true);
                $results['factory_class_name'] = 'Finance_Incomes_Reasons';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php';
                    require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
                    $results['history_class_name'] = 'Finance_Incomes_Reasons_History';
                }
                break;
            case 'finance_expenses_reasons':
                require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
                $results['filters']['where'] = array(
                    'fer.id = ' . $model_id
                );
                $this->registry->set('no_GT2', true, true);
                $results['factory_class_name'] = 'Finance_Expenses_Reasons';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.audit.php';
                    require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php';
                    $results['history_class_name'] = 'Finance_Expenses_Reasons_History';
                }
                break;
            case 'customers':
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $results['filters']['where'] = array(
                    'c.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Customers';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                    $results['history_class_name'] = 'Customers_History';
                }
                break;
            case 'events':
                require_once PH_MODULES_DIR . 'events/models/events.factory.php';
                $results['filters']['where'] = array(
                    'e.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Events';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'events/models/events.audit.php';
                    require_once PH_MODULES_DIR . 'events/models/events.history.php';
                    $results['history_class_name'] = 'Events_History';
                }
                break;
            case 'tasks':
                require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
                $results['filters']['where'] = array(
                    't.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Tasks';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.audit.php';
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.history.php';
                    $results['history_class_name'] = 'Tasks_History';
                }
                break;
            case 'nomenclatures':
                require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                $results['filters']['where'] = array(
                    'n.id = ' . $model_id
                );
                $results['factory_class_name'] = 'Nomenclatures';
                if ($include_history_classes) {
                    require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.audit.php';
                    require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
                    $results['history_class_name'] = 'Nomenclatures_History';
                }
                break;
            case 'announcements':
                require_once PH_MODULES_DIR . 'announcements/models/announcements.factory.php';
                $results['filters']['where'] = array(
                    'a.id = ' . $model_id,
                    'a.search_archive = \'all\''
                );
                $results['factory_class_name'] = 'Announcements';
                if ($include_history_classes) {
                    $results['history_class_name'] = false;
                }
                break;
        }

        return $results;
    }

    /**
     * Function to prepare the mini panel for resending mails
     */
    public function _prepareResendOptions() {
        $request = $this->registry['request'];

        $filters = array(
            'model'              => $request->get('communication_module'),
            'id'                 => intval($request->get('model_id')),
            'communication_id'   => $request->get('communication_id'),
            'communication_type' => General::singular2plural($request->get('communication_type'))
        );
        $communications_record = Communications::searchCommunications($this->registry, $filters);
        $communications_record = reset($communications_record);

        $resent_data_list = array();
        foreach ($communications_record['to'] as $com_id => $com_rec) {
            $resent_data_list[] = array(
                'id'    => $com_id,
                'label' => trim(sprintf('%s <%s>', $com_rec['name'], $com_rec['email']))
            );
        }

        //prepare data for the template
        $this->viewer = new Viewer($this->registry);
        $this->viewer->data['resent_info'] = $resent_data_list;
        $this->viewer->data['communication_model_id'] = ($request->get('dashlet_id') ? $request->get('dashlet_id') : $request->get('model_id'));
        $this->viewer->setFrameset('_communication_resend.html');

        if (router::isJSONRequired()) {
            //send data as JSON when required by the HTTP_ACCEPT header
            header('Content-Type: application/json');
            print json_encode(
                array(
                    'models' => $this->viewer->data
                )
            );
            exit;
        }

        $this->viewer->display();
        exit;
    }

    /**
     * Function resend selected e-mails
     */
    public function _resend() {
        $request = $this->registry['request'];

        $resend_mail_ids = $request->get('resend_mail_ids');

        $result = Communications::resendEmails($this->registry, $resend_mail_ids);

        echo(json_encode(implode("\n", $result)));
        exit;
    }

    /**
     * Prepares info popup content for last communication records for model
     */
    private function _getCommunicationsInfo() {

        $registry = &$this->registry;
        $request = &$registry['request'];

        if ($request->isRequested('model_id') && $request->isRequested('real_module') && $request->isRequested('real_controller') &&
            $request->get('communication_type') && in_array($request->get('communication_type'), array('comments', 'emails'))) {

            $module = $request->get('real_module');
            $controller = $request->get('real_controller');
            $model_id = $request->get('model_id');
            $communication_type = $request->get('communication_type');

            // prepare model name and alias
            if ($module != $controller) {
                $model_name = General::plural2singular(implode('_', array_map('ucfirst', explode('_', $module . '_' . $controller))));
            } else {
                $model_name = General::plural2singular($module);
            }

            //get the model to check permissions
            $model_factory_name = General::singular2plural($model_name);
            require_once PH_MODULES_DIR . $module . '/models/' . $module . (($module != $controller) ? '.' . $controller : '') . '.factory.php';

            $alias = $model_factory_name::getAlias($module, $controller);

            $filters = array(
                'where' => array(
                    "{$alias}.id = '{$model_id}'",
                ),
            );
            if ($module != 'finance') {
                $filters['where'][] = "{$alias}.deleted IS NOT NULL";
            }
            if ($module == 'announcements') {
                $filters['where'][] = "{$alias}.search_archive = 'all'";
            }
            if ($request->get('archive') == 1) {
                $filters['where'][] = "{$alias}.archive = 'archive'";
            }
            $model = $model_factory_name::searchOne($registry, $filters);

            $no_permissions = true;
            if (is_object($model) && $model->checkPermissions($communication_type)) {
                $no_permissions = false;

                if ($communication_type == 'comments') {
                    //get the comments
                    require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                    $filters = array(
                        'where' => array(
                            'model = "' . $model->modelName . '"',
                            'model_id = ' . $model->get('id'),
                        ),
                        'sort' => array('added DESC'),
                        'limit' => $request->get('display') ?: PH_LAST_RECORDS_INFO,
                        'sanitize' => true,
                        'archive' => $model->get('archived_by'),
                    );
                    list($communications, $pagination) = Comments::pagedSearch($this->registry, $filters);
                } else {
                    require_once(PH_MODULES_DIR . 'communications/models/communications.factory.php');
                    list($communications, $pagination) = Communications::searchCommunications(
                        $this->registry,
                        array(
                            'id' => $model->get('id'),
                            'model' => $model->modelName,
                            'archive' => $model->get('archived_by'),
                            'communication_type' => $communication_type,
                            'sort' => array('added DESC'),
                            'paginated' => true,
                            'display' => $request->get('display') ?: PH_LAST_RECORDS_INFO,
                        )
                    );
                    $communications = Communications::createModels($this->registry, $communications, 'Model', true);
                }
            }

            if (router::isJSONRequired()) {
                //send data as JSON when required by the HTTP_ACCEPT header
                header('Content-Type: application/json');
                print json_encode(
                    array(
                        'pagination' => !empty($pagination) ? $pagination : array(),
                        'models' => !empty($communications) ? $communications : array()
                    )
                );
                exit;
            }

            //prepare data for the template
            $this->viewer = new Viewer($registry);
            $this->viewer->data[$communication_type] = !empty($communications) ? $communications : array();
            $this->viewer->data['no_permissions'] = $no_permissions;
            $this->viewer->setFrameset("_{$communication_type}_info.html");
            $this->viewer->display();
            exit;
        }

        return true;
    }

    /*
     * Function to notify the assigned users (to a certain model) that a new e-mail is sent
     */
    public function notifyEmailSent($model) {
        // load lang files of module, in case some of the properties of model are taken from labels
        $model->unsanitize();
        $model_factory_name = General::singular2plural(strtolower($model->get('model')));
        $model_factory_name = explode('_', $model_factory_name, 2);
        $module = $model_factory_name[0];
        $controller = isset($model_factory_name[1]) ? $model_factory_name[1] : $model_factory_name[0];
        $i18n_files = array();
        $i18n_files[] = PH_MODULES_DIR . $module . '/i18n/' . $model->registry['lang'] . '/' . $module . '.ini';
        if ($module != $controller) {
            $i18n_files[] = PH_MODULES_DIR . $module . '/i18n/' . $model->registry['lang'] . '/' . $module . '_' . $controller . '.ini';
        }
        $model->registry['translater']->loadFile($i18n_files);

        $model->registry->set('getAssignments', true, true);
        switch ($model->get('model')) {
            case 'Document':
                $template = "document_email_add";
                $model->getAssignments();
                $model->getAssignments('responsible');
                $model->getAssignments('observer');
                $model->getAssignments('decision');

                $assignments_owner = $model->get('assignments_owner');
                $assignments_responsible = $model->get('assignments_responsible');
                $assignments_observer = $model->get('assignments_observer');
                $assignments_decision = $model->get('assignments_decision');
                foreach ($assignments_responsible as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                foreach ($assignments_observer as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                foreach ($assignments_decision as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                $users_ids = array_keys($assignments_owner);
                if (count($users_ids)) {
                    $query = 'SELECT u.id, u.email, CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as name FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                             '  ON (u.id=ui18n2.parent_id AND ui18n2.lang="' . $model->registry['lang'] . '")' . "\n" .
                             ' WHERE u.id in (' . implode(',', $users_ids) . ') AND active=1';
                    $emails = $model->registry['db']->GetAssoc($query);

                    $this->sendNotifications($template, $emails, $model);
                }
                break;
            case 'Contract':
                $template = "contract_email_add";
                $model->getAssignments();
                $model->getAssignments('responsible');
                $model->getAssignments('observer');
                $model->getAssignments('decision');

                $assignments_owner = $model->get('assignments_owner');
                $assignments_responsible = $model->get('assignments_responsible');
                $assignments_observer = $model->get('assignments_observer');
                $assignments_decision = $model->get('assignments_decision');
                foreach ($assignments_responsible as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                foreach ($assignments_observer as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                foreach ($assignments_decision as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                $users_ids = array_keys($assignments_owner);
                if (count($users_ids)) {
                    $query = 'SELECT u.id, u.email, CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as name FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                             '  ON (u.id=ui18n2.parent_id AND ui18n2.lang="' . $model->registry['lang'] . '")' . "\n" .
                             ' WHERE u.id in (' . implode(',', $users_ids) . ') AND active=1';
                    $emails = $model->registry['db']->GetAssoc($query);
                    $this->sendNotifications($template, $emails, $model);
                }
                break;
            case 'Task':
                $model->registry->set('getAssignmentsResponsible', true, true);
                $model->registry->set('getAssignmentsDecision', true, true);
                $model->registry->set('getAssignmentsObserver', true, true);
                $model->registry->set('getAssignmentsOwner', true, true);

                $template = "task_email_add";
                $assignments_owner = $model->get('assignments_owner');
                $assignments_responsible = $model->get('assignments_responsible');
                $assignments_observer = $model->get('assignments_observer');
                $assignments_decision = $model->get('assignments_decision');
                foreach ($assignments_responsible as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                foreach ($assignments_observer as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                foreach ($assignments_decision as $k => $val) {
                    if (!isset($assignments_owner[$k])) {
                        $assignments_owner[$k] = $val;
                    }
                }
                $users_ids = array_keys($assignments_owner);
                if (count($users_ids)) {
                    $query = 'SELECT u.id, u.email, CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as name FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                             '  ON (u.id=ui18n2.parent_id AND ui18n2.lang="' . $model->registry['lang'] . '")' . "\n" .
                             ' WHERE u.id in (' . implode(',', $users_ids) . ') AND active=1';
                    $emails = $model->registry['db']->GetAssoc($query);
                    $this->sendNotifications($template, $emails, $model);
                }
                break;
            case 'Project':
                $template = "project_email_add";
                $model->getAssignments();
                $assignments = $model->get('users_assignments');

                $manager_id = $model->get('manager');
                if ($manager_id) {
                    $query = 'SELECT CONCAT(firstname, " ", lastname) AS assigned_to_name FROM ' . DB_TABLE_USERS_I18N .
                             ' WHERE parent_id = ' . $manager_id;
                    $record = $model->registry['db']->GetOne($query);
                    if (!isset($assignments[$manager_id])) {
                        $assignments[$manager_id] = array('assigned_to_name' => $record);
                    }
                }

                $users_ids = array_keys($assignments);

                if (count($users_ids)) {
                    $query = 'SELECT u.id, u.email, CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as name FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                             '  ON (u.id=ui18n2.parent_id AND ui18n2.lang="' . $model->registry['lang'] . '")' . "\n" .
                             ' WHERE u.id in (' . implode(',', $users_ids) . ') AND active=1';
                    $emails = $model->registry['db']->GetAssoc($query);
                    $this->sendNotifications($template, $emails, $model);
                }
                break;
            case 'Announcement':
                $template = "announcement_email_add";
                $model->getAssignments();
                $assignments = $model->get('users_assignments');

                $owner_id = $model->get('added_by');
                if ($owner_id) {
                    $query = 'SELECT CONCAT(firstname, " ", lastname) AS assigned_to_name FROM ' . DB_TABLE_USERS_I18N .
                             ' WHERE parent_id = ' . $owner_id;
                    $record = $model->registry['db']->GetOne($query);
                    if (!isset($assignments[$owner_id])) {
                        $assignments[$owner_id] = array('assigned_to_name' => $record);
                    }
                }

                $users_ids = array_keys($assignments);

                if (count($users_ids)) {
                    $query = 'SELECT u.id, u.email, CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as name FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                             '  ON (u.id=ui18n2.parent_id AND ui18n2.lang="' . $model->registry['lang'] . '")' . "\n" .
                             ' WHERE u.id in (' . implode(',', $users_ids) . ') AND active=1';
                    $emails = $model->registry['db']->GetAssoc($query);
                    $this->sendNotifications($template, $emails, $model);
                }
                break;
            case 'Event':
                $template = "event_email_add";
                $model->getAssignments();
                $assignments = $model->get('users_assignments');
                $assignments = array_filter($assignments, function ($a) {
                    return !empty($a['active']);
                });

                $model->unsanitize();
                if (count($assignments)) {
                    $emails = array();
                    foreach ($assignments as $key => $assignee) {
                        $emails[$key]['email'] = $assignee['email'];
                        $emails[$key]['name'] = $assignee['assigned_to_name'];
                    }
                    $this->sendNotifications($template, $emails, $model);
                }
                break;
            case 'Finance_Budget':
                // only notify users when adding free comments
                if (!$model->isDefined('budget')) {
                    $template = "finance_budget_email_add";
                    $model->getAssignments();
                    $assignments = $model->get('user_assignments');
                    $assignments = array_filter($assignments, function ($a) {
                        return !empty($a['active']);
                    });

                    if (count($assignments)) {
                        $emails = array();
                        foreach ($assignments as $key => $assignee) {
                            $emails[$key]['email'] = $assignee['email'];
                            $emails[$key]['name'] = $assignee['assigned_to_name'];
                        }
                        $this->sendNotifications($template, $emails, $model);
                    }
                }
                break;
        }
    }

    /**
     * Send emails to the assigned users to the object (Document, Contract, Task, Project, Announcement, Event, Finance_Budget)
     *
     * @param string $template - name of the email template used for the email body
     * @param array $emails - associative list of recipients (id => email address)
     * @param Model $object - model object - Document, Contract, Task, Project, Announcement, Event, Finance_Budget
     * @return integer - the number of emails sent
     */
    public function sendNotifications($template, $emails, $object) {
        if (!$object->shouldSendEmail($template)) {
            return true;
        }

        // If the current comment is non portal, then only non portal users should receive a notification
        if (!$this->registry['currentUser']->get('is_portal') && (!$object->isDefined('is_portal') || !$object->get('is_portal'))
                && is_array($emails) && !empty($emails)) {
            $query = 'SELECT `id`' . "\n" .
                     '  FROM `' . DB_TABLE_USERS . '`' . "\n" .
                     '  WHERE `id` IN (\'' . implode('\', \'', array_keys($emails)) . '\')' . "\n" .
                     '    AND `is_portal` = \'0\'';
            $non_portal_users = $this->registry['db']->GetCol($query);
            $emails = array_intersect_key($emails, array_flip($non_portal_users));
        }
        // strip slashes so they are not sent in e-mail
        $object->slashesStrip();

        $current_user_id = $this->registry['currentUser']->get('id');

        $recipients_success = array();
        $recipients_failed = array();

        $not_users = Users::getUsersNoSend($this->registry, $template);

        foreach ($emails as $k => $val) {
            if ($k != $current_user_id && !in_array($k, $not_users)) {
                // send email
                $mailer = new Mailer($this->registry, $template, $object);

                $mailer->template['model_name'] = $object->modelName;
                $mailer->template['model_id'] = $object->get('id');
                $mailer->template['system_flag'] = 1;

                $mailer->placeholder->add('to_email', $val['email']);
                $mailer->placeholder->add('user_name', $val['name']);

                $object->unsanitize();
                $mailer->placeholder->merge($object->getNotificationEmailsVars($template));
                $object->sanitize();

                $mailer->placeholder->add('model_id', $object->get('model_id'));
                $mailer->placeholder->add('model', $object->i18n($object->get('model')));
                $mailer->placeholder->add('email_subject', $object->get('email_subject'));
                $mailer->placeholder->add('added_by_name', $this->registry['currentUser']->get('firstname') . ' ' .
                                                           $this->registry['currentUser']->get('lastname'));
                $mailer->placeholder->add('user_name', $val['name']);

                $model_words = explode('_', General::singular2Plural(strtolower($object->modelName)), 2);
                if (count($model_words) > 1) {
                    $url_mc = $model_words[0] . '&controller=' . $model_words[1] . '&' . $model_words[1];
                } else {
                    $url_mc = $model_words[0] . '&' . $model_words[0];
                }

                $model_view_url = sprintf(
                    '%s/index.php?%s=%s=view&view=%d',
                    $this->registry['config']->getParam('crontab', 'base_host'),
                    $this->registry['module_param'],
                    $url_mc,
                    $object->get('id'));
                $mailer->placeholder->add('model_view_url', $model_view_url);

                $url_email = sprintf('communications&communications=%d&communication_type=emails', $object->get('id'));
                $view_email_url = sprintf(
                    '%s/index.php?%s=%s=%s',
                    $this->registry['config']->getParam('crontab', 'base_host'),
                    $this->registry['module_param'],
                    $url_mc,
                    $url_email);
                $mailer->placeholder->add('communication_emails_url', $view_email_url);

                switch ($object->modelName) {
                    case 'Document':
                        // special placeholders for documents
                        $mailer->placeholder->add('document_added_by', $object->get('added_by_name'));
                        $mailer->placeholder->add('document_view_url', $model_view_url);
                        $mailer->placeholder->add('document_name', $object->get('name'));
                        $mailer->placeholder->add('document_num', $object->getDocFullNum());
                        $mailer->placeholder->add('customer_name', $object->get('customer_name'));
                        $mailer->placeholder->add('document_type', $object->get('type_name'));
                        break;
                    case 'Contract':
                        // special placeholders for contracts
                        $mailer->placeholder->add('contract_num', $object->get('num'));
                        $mailer->placeholder->add('customer_name', $object->get('customer_name'));
                        $mailer->placeholder->add('contract_added_by', $object->get('added_by_name'));
                        $mailer->placeholder->add('contract_name', $object->get('name'));
                        $mailer->placeholder->add('contract_type', $object->get('type_name'));
                        break;
                    case 'Project':
                        // special placeholders for projects
                        $mailer->placeholder->add('project_name', $object->get('name'));
                        $mailer->placeholder->add('project_added_by', $object->get('added_by_name'));
                        $mailer->placeholder->add('project_code', $object->get('code'));
                        $mailer->placeholder->add('project_num', $object->get('num'));
                        $mailer->placeholder->add('project_type', $object->get('type_name'));
                        $mailer->placeholder->add('customer_name', $object->get('customer_name'));
                        break;
                    case 'Task':
                        // special placeholders for tasks
                        $mailer->placeholder->add('task_id', $object->get('id'));
                        $mailer->placeholder->add('task_equipment', $object->get('equipment'));
                        $mailer->placeholder->add('task_notes', $object->get('notes'));
                        $mailer->placeholder->add('task_description', $object->get('description'));
                        $mailer->placeholder->add('task_name', $object->get('name'));
                        $mailer->placeholder->add('customer_name', $object->get('customer_name'));
                        $mailer->placeholder->add('task_added_by', $object->get('added_by_name'));
                        $mailer->placeholder->add('task_modified_by', $object->get('modified_by'));
                        $mailer->placeholder->add('task_num', $object->getTaskFullNum());
                        $mailer->placeholder->add('task_type', $object->get('type_name'));
                        $mailer->placeholder->add('task_severity', $object->get('severity'));
                        $mailer->placeholder->add('task_project', $object->get('project_name'));
                        $mailer->placeholder->add('task_task_field', $object->get('task_field'));
                        break;
                    case 'Announcement':
                        // special placeholders for announcements
                        $mailer->placeholder->add('announcement_subject', $object->get('subject'));
                        $mailer->placeholder->add('announcement_type', $object->get('type_name'));
                        $mailer->placeholder->add('announcement_added_by_name', $object->get('added_by_name'));
                        break;
                    case 'Event':
                        // special placeholders for event
                        $mailer->placeholder->add('customer_name', $object->get('customer_name'));
                        $mailer->placeholder->add('event_name', $object->get('name'));
                        $mailer->placeholder->add('event_added_by', $object->get('added_by_name'));
                        $mailer->placeholder->add('event_type', $object->get('type_name'));
                        $mailer->placeholder->add('event_subject', $object->get('subject'));
                        break;
                    case 'Finance_Budget':
                        // special placeholders for finance budget
                        $mailer->placeholder->add('budget_name', $object->get('company_name') . ' ' . $object->get('year'));
                        $mailer->placeholder->add('finance_budget_added_by', $object->get('added_by_name'));
                        break;
                }

                $result = $mailer->send();
                if (!@in_array($val['email'], $result['erred'])) {
                    $recipients_success[] = $val['name'];
                } else {
                    $recipients_failed[] = $val['name'];
                }
            }
        }
        if (!empty($recipients_success)) {
            $message = str_replace('[recipient]', implode(', ', $recipients_success), $this->i18n('message_email_success_email_sent_to'));
            $this->registry['messages']->setMessage($message);
        }
        if (!empty($recipients_failed)) {
            $message = str_replace('[recipient]', implode(', ', $recipients_failed), $this->i18n('error_email_failed_send_to'));
            $this->registry['messages']->setWarning($message);
        }

        return count($recipients_success);
    }

    /**
     * Prepends a prefix to a subject if it doesn't already start with it
     * Can replace properties from the model
     *
     * @param string $subject The original subject
     * @param Model $model The model to get properties from (optional)
     * @return string The subject with prefix prepended if needed
     */
    public function prependReplySubject(string $subject, Model $model):string {
        $prefix = $this->getReplyPrefix($model);

        $extender = new Extender();
        $placeholders = $extender->getPlaceholdersUsed($prefix);
        if ($placeholders) {
            foreach ($placeholders as $placeholder => $placeholderData) {
                if ($model->isDefined($placeholder)) {
                    $extender->add($placeholder, $model->get($placeholder));
                }
            }
            $prefix = $extender->expand($prefix);
        }

        // Check if the subject already starts with the prefix
        if (preg_match('#^' . preg_quote($prefix) . '#', $subject)) {
            return $subject;
        }

        return $prefix . ' ' . $subject;
    }

    /**
     * Returns the email reply prefix based on model type and configuration
     *
     * Checks configuration in following order (first found is used):
     * 1. Model type specific prefix (e.g. default_reply_prefix_document_invoice)
     * 2. Model specific prefix (e.g. default_reply_prefix_document)
     * 3. Global default prefix
     * 4. 'Re' as fallback
     *
     * @param Model $model The model to get the reply prefix for
     * @return string The reply prefix to use
     */
    private function getReplyPrefix(Model $model): string {
        $superDefaultPrefix = 'Re';
        $defaultPrefix = $this->registry['config']->getParam('emails', 'default_reply_prefix')?:null;
        $defaultPrefixModel = $this->registry['config']->getParam('emails', 'default_reply_prefix_' . strtolower($model->modelName))?:null;
        $defaultPrefixModelType = $this->registry['config']->getParam('emails', 'default_reply_prefix_' . strtolower($model->modelName) . '_' . $model->get('type'))?:null;

        return $defaultPrefixModelType ?? $defaultPrefixModel ?? $defaultPrefix ?? $superDefaultPrefix;
    }
}

?>
