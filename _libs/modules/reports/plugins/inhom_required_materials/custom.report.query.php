<?php
    Class Inhom_Required_Materials Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            define('DOCUMENT_TYPE_ID', 19);
            define('DOCUMENT_TYPE2_ID', 28);
            define('DETAILS', 'details');
            define('SKETCH', 'sketch');
            define('QUANTITY', 'details_num');
            define('PRODUCT', 'articles_name');

            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            //sql to take the ids of the needed additional vars 
            $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_ID . ' AND (fm.name="' . DETAILS . '" OR fm.name="' . SKETCH . '" OR fm.name="' . QUANTITY . '" OR fm.name="' . PRODUCT . '") ORDER BY fm.position';
            $var_ids = $registry['db']->GetAll($sql_for_add_vars);

            $detail_id = '';
            $sketch_id = '';
            $detail_quantity_id = '';
            $articles_name_id = '';

            //assign the ids to vars
            foreach ($var_ids as $vars) {
                if ($vars['name'] == DETAILS) {
                    $detail_id = $vars['id'];
                } else if ($vars['name'] == SKETCH) {
                    $sketch_id = $vars['id'];
                } else if ($vars['name'] == QUANTITY) {
                    $detail_quantity_id = $vars['id']; 
                }  else if ($vars['name'] == PRODUCT) {
                    $articles_name_id = $vars['id']; 
                }
            }

            //sql to take all rows of documents_cstm table where the needed employee is find
            $sql['select']    =  'SELECT d.id AS id, d.active AS active, ' . "\n" .
                                 '  d.full_num AS full_num, dt.direction AS direction, ' . "\n" . 
                                 '  DATE_FORMAT(d.added, "%Y-%m-%d") AS date_added,' . "\n" . 
                                 '  d_cstm_details.value AS detail, d_cstm_sketch.value AS sketch,' . "\n" . 
                                 '  d_cstm_detail_quantity.value AS detail_quantity, d_cstm_product_name.value AS product_name' . "\n";
            $sql['from']      =     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                    'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                    '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_details' . "\n" .
                                    '  ON (d_cstm_details.model_id=d.id AND d_cstm_details.var_id="' . $detail_id . '")' . "\n" . 
                                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sketch' . "\n" .
                                    '  ON (d_cstm_sketch.model_id=d.id AND d_cstm_sketch.var_id="' . $sketch_id . '" AND d_cstm_details.num=d_cstm_sketch.num)' . "\n" . 
                                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_detail_quantity' . "\n" .
                                    '  ON (d_cstm_detail_quantity.model_id=d.id AND d_cstm_detail_quantity.var_id="' . $detail_quantity_id . '" AND d_cstm_details.num=d_cstm_detail_quantity.num)' . "\n" . 
                                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_product_name' . "\n" .
                                    '  ON (d_cstm_product_name.model_id=d.id AND d_cstm_product_name.var_id="' . $articles_name_id . '")' . "\n";
            $sql['where']  =  'WHERE d.deleted_by=0 AND d.type="' . DOCUMENT_TYPE_ID . '"';

            if (!empty($filters['from_date'])) {
                $sql ['where'] .= ' AND d.added >= "' . $filters['from_date'] . '"' . "\n";

            }
            if (!empty($filters['to_date'])) {
                $sql ['where'] .= ' AND d.added <= "' . $filters['to_date'] . '"' . "\n";
            }

            $sql['order'] = ' ORDER BY d.added' . "\n";

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);

            //sql to take the ids of the needed additional vars for the 
            // documents of the second type
            $sql_for_add_vars_type2 = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE2_ID . ' AND (fm.name="' . DETAILS . '" OR fm.name="' . SKETCH . '" OR fm.name="' . QUANTITY . '" OR fm.name="' . PRODUCT . '") ORDER BY fm.position';
            $var_ids_2 = $registry['db']->GetAll($sql_for_add_vars_type2);

            $detail_id_2 = '';
            $sketch_id_2 = '';
            $detail_quantity_id_2 = '';
            $articles_name_id_2 = '';

            //assign the ids to vars
            foreach ($var_ids_2 as $vars) {
                if ($vars['name'] == DETAILS) {
                    $detail_id_2 = $vars['id'];
                } else if ($vars['name'] == SKETCH) {
                    $sketch_id_2 = $vars['id'];
                } else if ($vars['name'] == QUANTITY) {
                    $detail_quantity_id_2 = $vars['id']; 
                }  else if ($vars['name'] == PRODUCT) {
                    $articles_name_id_2 = $vars['id']; 
                }
            }

            //sql to take all rows of documents_cstm table where the needed employee is find
            $sql_1['select']    =  'SELECT d.id AS id, d.active AS active, ' . "\n" .
                                 '  d.full_num AS full_num, dt.direction AS direction, ' . "\n" . 
                                 '  DATE_FORMAT(d.added, "%Y-%m-%d") AS date_added,' . "\n" . 
                                 '  d_cstm_details.value AS detail, d_cstm_sketch.value AS sketch,' . "\n" . 
                                 '  d_cstm_detail_quantity.value AS detail_quantity, d_cstm_product_name.value AS product_name' . "\n";
            $sql_1['from']      =   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                        'JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                                        '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_details' . "\n" .
                                        '  ON (d_cstm_details.model_id=d.id AND d_cstm_details.var_id="' . $detail_id_2 . '")' . "\n" . 
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sketch' . "\n" .
                                        '  ON (d_cstm_sketch.model_id=d.id AND d_cstm_sketch.var_id="' . $sketch_id_2 . '" AND d_cstm_details.num=d_cstm_sketch.num)' . "\n" . 
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_detail_quantity' . "\n" .
                                        '  ON (d_cstm_detail_quantity.model_id=d.id AND d_cstm_detail_quantity.var_id="' . $detail_quantity_id_2 . '" AND d_cstm_details.num=d_cstm_detail_quantity.num)' . "\n" . 
                                        'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_product_name' . "\n" .
                                        '  ON (d_cstm_product_name.model_id=d.id AND d_cstm_product_name.var_id="' . $articles_name_id_2 . '")' . "\n";
            $sql_1['where']  =  'WHERE d.deleted_by=0 AND d.type="' . DOCUMENT_TYPE2_ID . '"';

            if (!empty($filters['from_date'])) {
                $sql_1 ['where'] .= ' AND d.added >= "' . $filters['from_date'] . '"' . "\n";

            }
            if (!empty($filters['to_date'])) {
                $sql_1 ['where'] .= ' AND d.added <= "' . $filters['to_date'] . '"' . "\n";
            }

            $sql_1['order'] = ' ORDER BY d.added' . "\n";

            //search basic details with current lang parameters
            $query_type2 = implode("\n", $sql_1);
            $records_type2 = $registry['db']->GetAll($query_type2);

            foreach ($records_type2 as $recs_t2) {
                $records[] = $recs_t2;
            }

            if (!empty($filters['paginate'])) {
                $results = array($records, 0);
            } else {
                $results = $records;
            }

            return $results;
        }
    }
?>