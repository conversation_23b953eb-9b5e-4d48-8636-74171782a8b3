<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
<body>
<table border="1" cellpadding="0" cellspacing="0">
  <tr>
    <td align="center" style="background-color: #DFDFDF; vertical-align: middle; width: 200px;" rowspan="2"><b>{#employee#|escape}</b></td>
    <td align="center" style="background-color: #DFDFDF; vertical-align: middle;" colspan="3"><b>{#reports_education#|escape}</b></td>
    <td align="center" style="background-color: #DFDFDF; vertical-align: middle; width: 350px;" rowspan="2"><b>{#reports_skills#|escape}</b></td>
    <td align="center" style="background-color: #DFDFDF; vertical-align: middle; width: 230px;" rowspan="2"><b>{#reports_experience#|escape}</b></td>
  </tr>
  <tr>
    <td align="center" style="background-color: #DFDFDF; vertical-align: middle; width: 60px;"><b>{#reports_type#|escape}</b></td>
    <td align="center" style="background-color: #DFDFDF; vertical-align: middle; width: 230px;"><b>{#reports_school_name#|escape}</b></td>
    <td align="center" style="background-color: #DFDFDF; vertical-align: middle; width: 230px;"><b>{#reports_qualification#|escape}</b></td>
  </tr>
  {foreach from=$reports_results.employees item=employee}
    {foreach from=$employee.educations item='education' name='foreacheducations'}
      <tr>
        {if $smarty.foreach.foreacheducations.first}
          {capture assign='employee_educations_count'}{if is_array($employee.educations)}{$employee.educations|@count}{else}0{/if}{/capture}
          <td {if $employee_educations_count gt 1}rowspan="{$employee_educations_count}"{/if} style="vertical-align: middle;">{$employee.name|escape|default:"&nbsp;"}</td>
        {/if}
        <td style="vertical-align: middle;">{$education.step_school|escape|default:"&nbsp;"}</td>
        <td style="vertical-align: middle;">{$education.school_name|escape|nl2br|default:"&nbsp;"}</td>
        <td style="vertical-align: middle;">{$education.qualification|escape|nl2br|default:"&nbsp;"}</td>
        {if $smarty.foreach.foreacheducations.first}
          <td {if $employee_educations_count gt 1}rowspan="{$employee_educations_count}"{/if} style="vertical-align: middle;">{$employee.skills|escape|nl2br|default:"&nbsp;"}</td>
          <td {if $employee_educations_count gt 1}rowspan="{$employee_educations_count}"{/if} style="vertical-align: middle;">
            {foreach from=$employee.experiences item='experience' name='experiences'}
              {if !$smarty.foreach.experiences.first}<br />{/if}
              {$experience.experience|escape|nl2br|default:"&nbsp;"} ({$experience.experience_years|escape|default:"&nbsp;"} {#reports_experience_years#})
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
        {/if}
      </tr>
    {foreachelse}
      <tr>
        <td style="vertical-align: middle;">{$employee.name|escape|default:"&nbsp;"}</td>
        <td style="vertical-align: middle;">&nbsp;</td>
        <td style="vertical-align: middle;">&nbsp;</td>
        <td style="vertical-align: middle;">&nbsp;</td>
        <td style="vertical-align: middle;">{$employee.skills|escape|nl2br|default:"&nbsp;"}</td>
        <td style="vertical-align: middle;">
          {foreach from=$employee.experiences item='experience' name='experiences'}
            {if !$smarty.foreach.experiences.first}<br />{/if}
            {$experience.experience|escape|nl2br|default:"&nbsp;"} ({$experience.experience_years|escape|default:"&nbsp;"} {#reports_experience_years#})
          {foreachelse}
            &nbsp;
          {/foreach}
        </td>
      </tr>
    {/foreach}
  {/foreach}
</table>
</body>
</html>