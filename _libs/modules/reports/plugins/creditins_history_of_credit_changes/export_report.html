<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
</head>
<body>
  <h1>{#reports_table_title#} {$report_filters.period_from.value|date_format:#date_short#} - {$report_filters.period_from.additional_filter.value|date_format:#date_short#}</h1>
  <table border="1" cellpadding="0" cellspacing="0">
    <tr>
      <th width="25">{#num#}</th>
      <th width="80">{#reports_th_contract_num_date#}</th>
      <th width="200">{#reports_th_customer_name_ucn#}</th>
      <th width="130">{#reports_th_change_type#}</th>
      <th width="100">{#reports_th_rate_change#}</th>
      <th width="100">{#reports_th_guarantor_fee#}</th>
      <th width="200">{#reports_th_note#}</th>
    </tr>
    {foreach from=$reports_results.change_types item='change_type' key='change_type_num'}
      {foreach from=$change_type.contracts item='contract' key='contract_id'}
        {capture assign='contract_changes_count'}{if is_array($contract.changes)}{$contract.changes|@count}{else}0{/if}{/capture}
        {foreach from=$contract.changes item='change' name='changes'}
          <tr>
            {if $smarty.foreach.changes.first}
              <td align="center" style="vertical-align: middle;" rowspan="{$contract_changes_count}">{counter}</td>
              <td align="center" style="vertical-align: middle; mso-number-format: '\@';" rowspan="{$contract_changes_count}">
                {$contract.num|escape}<br />
                ({$contract.date|date_format:#date_short#})
              </td>
              <td align="center" style="vertical-align: middle; mso-number-format: '\@';" rowspan="{$contract_changes_count}">
                {capture assign='client'}{$contract.customer_name|escape} ({$contract.customer_ucn}){/capture}
                <a target="_blank" href="{$smarty.const.PH_SERVER_BASE}{$smarty.const.PH_BASE_URL}index.php?{$module_param}=reports&amp;reports=generate_report&amp;report_type=creditins_customer_file&amp;client={$contract.customer_id}&amp;client_autocomplete={$client|escape:'url'}">{$contract.customer_name|escape}<br />({$contract.customer_ucn})</a>
              </td>
              {capture assign='change_type_color'}change_type_color_{$change_type_num}{/capture}
              {assign var='change_type_color' value=$change_type_color|upper}
              <td align="center" style="vertical-align: middle; mso-number-format: '\@'; color: {$smarty.const.$change_type_color};" rowspan="{$contract_changes_count}">
                {capture assign='change_type_name'}reports_change_type_{$change_type_num}{/capture}
                <b>{$smarty.config.$change_type_name}</b>
              </td>
            {/if}
            <td align="center" style="vertical-align: middle; mso-number-format: '\@';">
              {if $change_type_num eq 1}
                {#reports_rate_change_decreased#}:<br />
                <b>{$change.rate_change|number_format:'2':'.':' '}</b>
              {elseif $change_type_num eq 3}
                {#reports_rate_change_increased#}:<br />
                <b>{$change.rate_change|number_format:'2':'.':' '}</b>
              {else}
                -
              {/if}
            </td>
            <td align="center" style="vertical-align: middle; mso-number-format: '\@';">
              {if $change_type_num eq 1}
                {#reports_guarantor_fee_decreased#}:<br />
                <b>{$change.guarantor_fee|number_format:'2':'.':' '}</b>
              {elseif $change_type_num eq 3}
                {#reports_guarantor_fee_increased#}:<br />
                <b>{$change.guarantor_fee|number_format:'2':'.':' '}</b>
              {else}
                -
              {/if}
            </td>
            <td align="center" style="vertical-align: middle; mso-number-format: '\@';">
              {if $change_type_num eq 1 || $change_type_num eq 2}
                {#reports_repayment_date#}: {$change.date|date_format:#date_short#}<br />
                {#reports_last_payment_amount#}: {$change.last_payment|number_format:'2':'.':' '}
              {elseif $change_type_num eq 3}
                {#reports_extension_date#}: {$change.date|date_format:#date_short#}<br />
                {#reports_maturity_extended_installment#}: {$change.maturity_extended_install|date_format:#date_short#}
              {elseif $change_type_num eq 4}
                {#reports_date_of_filing#}: {$change.date|date_format:#date_short#}<br />
                <a href="{$smarty.const.PH_SERVER_BASE}{$smarty.const.PH_BASE_URL}index.php?{$module_param}=projects&amp;projects=view&amp;view={$change.cp_id}" target="_blank">{#reports_link_to_a_case#}</a>
              {elseif $change_type_num eq 5}
                {#reports_repayment_date#}: {$change.date|date_format:#date_short#}<br />
                <a href="{$smarty.const.PH_SERVER_BASE}{$smarty.const.PH_BASE_URL}index.php?{$module_param}=projects&amp;projects=view&amp;view={$change.cp_id}" target="_blank">{#reports_link_to_a_case#}</a>
              {/if}
            </td>
          </tr>
        {/foreach}
      {/foreach}
    {/foreach}
  </table>
</body>
</html>