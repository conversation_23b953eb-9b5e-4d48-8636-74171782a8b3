<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1">
      <tr>
        <th width="20">{#num#}</th>
        <th width="160">{#reports_th_patient#}</th>
        <th class="100">{#reports_th_phone#}</th>
        <th class="100">{#reports_th_gsm#}</th>
        <th class="140">{#reports_th_email#}</th>
        <th class="60">{#reports_th_added_on#}</th>
        <th class="40">{#reports_th_visits_count#}</th>
        <th class="60">{#reports_th_latest_visit#}</th>
        <th width="130">{#reports_th_clinic#}</th>
        <th width="65">{#reports_th_visit_date#}</th>
        <th width="130">{#reports_th_sheet#}</th>
        <th width="195">{#reports_th_procedure_cosmetics#}</th>
        <th width="70">{#reports_th_payment#}</th>
        <th width="90">{#reports_th_price#}</th>
        <th width="35">{#reports_th_quantity#}</th>
        <th width="90">{#reports_th_total_procedures#}</th>
        <th width="90">{#reports_th_total_cosmetics#}</th>
        <th width="90">{#reports_th_total#}</th>
      </tr>
      {foreach from=$reports_results.patients key='patient_id' item='patient' name='patients'}
        {foreach from=$patient.sheets key='sheet_id' item='sheet' name='sheets'}
          {foreach from=$sheet.procedures item='procedure' name='procedures'}
            <tr>
              {if $smarty.foreach.sheets.first && $smarty.foreach.procedures.first}
                <td rowspan="{$patient.rowspan}" style="vertical-align: middle;">{counter}</td>
                <td rowspan="{$patient.rowspan}" style="vertical-align: middle;">{$patient.name|escape}</td>
                <td rowspan="{$patient.rowspan}">
                  <a style="mso-number-format:\@;" target="_blank" href="tel:{$patient.phone|escape}">{$patient.phone|default:"&nbsp;"}</a>
                </td>
                <td style="mso-number-format:\@;" rowspan="{$patient.rowspan}">
                  <a style="mso-number-format:\@;" target="_blank" href="tel:{$patient.gsm|escape}">{$patient.gsm|default:"&nbsp;"}</a>
                </td>
                <td style="mso-number-format:\@;" rowspan="{$patient.rowspan}">
                  <a target="_blank" href="mailto:{$patient.email|escape}">{$patient.email|default:"&nbsp;"}</a>
                </td>
                <td rowspan="{$patient.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">{$patient.added_on|escape|default:"&nbsp;"}</td>
                <td rowspan="{$patient.rowspan}" >{if is_array($patient.sheets)}{$patient.sheets|@count}{else}0{/if}</td>
                <td rowspan="{$patient.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">{$patient.latest_visit|escape|default:"&nbsp;"}</td>
              {/if}
              {if $smarty.foreach.procedures.first}
                <td rowspan="{$sheet.rowspan}" style="vertical-align: middle;">{$sheet.clinic_name|escape}</td>
                <td rowspan="{$sheet.rowspan}" align="center" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">{$sheet.visit_date|escape|date_format:#date_short#}</td>
                <td rowspan="{$sheet.rowspan}" align="center" style="vertical-align: middle;">{$sheet.num|escape}</td>
              {/if}
              <td>{$procedure.name|escape}</td>
              <td>{$procedure.payment|escape}</td>
              <td style="mso-number-format:'0\.00';">{$procedure.price|escape|default:0|string_format:"%.2f"}</td>
              <td>{$procedure.quantity|escape|default:0|number_format:0}</td>
              {if $smarty.foreach.procedures.first}
                <td rowspan="{$sheet.rowspan}">{$sheet.total_procedures|escape|default:0|string_format:"%.2f"}</td>
                <td rowspan="{$sheet.rowspan}">{$sheet.total_cosmetics|escape|default:0|string_format:"%.2f"}</td>
                <td rowspan="{$sheet.rowspan}">{$sheet.total|escape|default:0|string_format:"%.2f"}</td>
              {/if}
            </tr>
          {/foreach}
        {/foreach}
        <tr>
          <th align="right" colspan="7">{#reports_th_total_patient#} {$patient.name|escape|default:"&nbsp;"}:</th>
          <td>{$patient.total_procedures|escape|default:0|string_format:"%.2f"}</td>
          <td>{$patient.total_cosmetics|escape|default:0|string_format:"%.2f"}</td>
          <td>{$patient.total|escape|default:0|string_format:"%.2f"}</td>
        </tr>
      {foreachelse}
        <tr>
          <td colspan="12"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>