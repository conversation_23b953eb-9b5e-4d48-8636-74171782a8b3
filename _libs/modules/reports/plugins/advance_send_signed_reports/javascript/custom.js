Event.observe(window, 'load', function () {
    'use strict';

    let grid1;
    const reportsGenerated = $('reports_generated');

    /**
     * Table columns
     */
    const columnDefinitions = [
        {
            field: 'counter',
            headerText: '#',
            template: '#counter',
            disableHtmlEncode: false,
            visible: true,
            width: 45
        },
        {
            field: 'send_report',
            headerText: '',
            template: '#templateSendReport',
            disableHtmlEncode: false,
            visible: true,
            width: 50
        },
        {
            type: 'string',
            field: 'full_num',
            headerText: i18n['report_full_num'],
            template: '#templateDocumentNum',
            disableHtmlEncode: false,
            visible: true,
            width: 130
        },
        {
            type: 'string',
            field: 'system_made_by',
            headerText: i18n['report_system_made_by'],
            disableHtmlEncode: false,
            visible: true,
            width: 130
        },
        {
            type: 'date',
            field: 'date',
            headerText: i18n['report_document_date'],
            disableHtmlEncode: false,
            format: {type: "date", format: "dd.MM.yyyy"},
            visible: true,
            width: 90
        },
        {
            type: 'string',
            field: 'assignor_name',
            headerText: i18n['report_assignor_name'],
            template: '#templateAssignor',
            disableHtmlEncode: false,
            visible: true
        },
        {
            type: 'string',
            field: 'customer_name',
            headerText: i18n['report_customer_name'],
            template: '#templateCustomer',
            disableHtmlEncode: false,
            visible: true
        },
        {
            type: 'string',
            field: 'bank_name',
            headerText: i18n['report_bank_name'],
            template: '#templateBank',
            disableHtmlEncode: false,
            visible: true
        },
        {
            field: 'signed_report',
            headerText: i18n['report_signed_report'],
            template: '#templateSignedReport',
            disableHtmlEncode: false,
            visible: true,
            width: 110
        }
    ];

    /**
     * Excel columns
     */
    const exportColumnDefinitions = [
        {field: 'counter', headerText: '#', width: 100},
        {field: 'assignor_name', headerText: i18n['report_assignor_name'], width: 300},
        {field: 'full_num', headerText: i18n['report_full_num'], width: 200},
        {field: 'date', headerText: i18n['report_document_date'], width: 150},
        {field: 'customer_name', headerText: i18n['report_customer_name'], width: 300},
    ];

    /**
     * Initialize EJ2 table
     */
    function initTable() {
        // Apply styles to grid div
        const gridDiv = document.querySelector('#advance_send_signed_reports_show_report_results .grid');
        if (!gridDiv) {
            return;
        }
        gridDiv.style.display = 'none';
        gridDiv.style.marginTop = '10px';

        // Initialize grid
        grid1 = new window.ej.grids.Grid({
            dataSource: [],
            gridLines: 'Both',
            allowSorting: true,
            enableStickyHeader: true,
            width: '100%',
            allowTextWrap: true,
            textWrapSettings: {wrapMode: 'Header'},
            columns: columnDefinitions,
            excelQueryCellInfo: handleExcelCellInfo,
            excelHeaderQueryCellInfo: handleExcelHeaderCellInfo,
            load: handleNoRecordsFound,
            rowspanData: [{
                criteria: 'id',
                spanColumns: ['customer_name', 'assignor_name', 'custom_num', 'full_num', 'date', 'currency'],
            }],
            dataBound: onDataBoundTbl,
            allowExcelExport: true,
            allowResizing: true,
            toolbar: ['ExcelExport'],
        });

        grid1.appendTo(gridDiv);

        // Handle toolbar actions
        grid1.toolbarClick = handleExcelExport;

        // Hide loading after export
        grid1.excelExportComplete = hideLoading;
    }

    /**
     * Handle no records found message
     */
    function handleNoRecordsFound() {
        grid1.localeObj.currentLocale.EmptyRecord = i18n['report_no_records_found'];
    }
    /**
     * Excel cell info
     *
     * @param args
     */
    function handleExcelCellInfo(args) {
        args.style = args.style || {};
        args.style.wrapText = true;

        if (args.column.field === 'invoices') {
            args.value = args.data['invoices'].map(invoice => invoice.invoice_name).join("\n");
        }
    }

    /**
     * Excel header cell info
     *
     * @param args
     */
    function handleExcelHeaderCellInfo(args) {
        if (args.cell.index === 10) {
            args.cell.value = i18n['report_income_reason'];
        }
        args.style.fontSize = 15;
    }

    /**
     * Excel export
     */
    function handleExcelExport(args) {
        if (args.item.id.match(/_excelexport$/)) {
            const date = new Date();
            const exportFileName = `Report_dokladi_${date.getFullYear()}_${date.getMonth() + 1}_${date.getDate()}.xlsx`;

            showLoading();
            grid1.excelExport({
                fileName: exportFileName,
                columns: exportColumnDefinitions
            });
        }
    }

    function onDataBoundTbl() {
        // ej2Helper.grid.applyRowspan(this, this.rowspanData);
        // this.autoFitColumns();
    }

    /**
     * Returns json response/data
     *
     * @param pushHistory
     * @returns {Promise<void>}
     */
    async function getJsonData(pushHistory) {
        showLoading();

        const reports = $$('[name="reports"]')[0];
        reports.value = 'ajax_generate';

        const url = env.base_url + '?' + Form.serialize(reportsGenerated);

        try {
            const response = await fetch(url, {credentials: 'same-origin'});
            const data = await response.json();

            if (data.error) {
                alert(data.error);
                return;
            }

            updateTableData(grid1, data.table);

            const gridDiv = document.querySelector('#advance_send_signed_reports_show_report_results .grid');
            gridDiv.style.display = '';

            const formDataParams = collectFormData(reportsGenerated);

            if (pushHistory) {
                history.pushState(formDataParams, 'title', data.url);
            }
        } catch (error) {
            alert(i18n['report_technical_error']);
        } finally {
            hideLoading();
        }
    }

    /**
     * Collect form data
     *
     * @param form
     * @returns {{}}
     */
    function collectFormData(form) {
        const formData = new FormData(form);
        const formDataParams = {};

        for (const [key, value] of formData.entries()) {
            formDataParams[key] = value;
        }

        $$('#reports_generated [name$="_oldvalue"]').forEach(element => {
            formDataParams[element.name] = element.value;
        });

        return formDataParams;
    }

    /**
     * Update table data
     *
     * @param grid
     * @param data
     */
    function updateTableData(grid, data) {
        // Cleanup
        grid.dataSource = [];

        // Process results
        data.forEach(function (value) {
            grid.columns.forEach(function (column) {
                const columnFormat = column.format;
                if (columnFormat && columnFormat.type
                    && (columnFormat.type === 'date' || columnFormat.type === 'datetime')) {

                    const fieldValue = value[column.field];
                    value[column.field] = fieldValue ? new Date(fieldValue) : null;
                }
            });
            grid.dataSource.push(value);
        });

        // Refresh grid
        grid.refresh();
    }

    /*
     * Event listener to manage the browser BACK button
     */
    window.addEventListener('popstate', async function (event) {
        let current_filter;
        for (let i in event.state) {
            if (!event.state.hasOwnProperty(i)) {
                continue;
            }
            current_filter = $$(`[name = "${i}"]`);
            let currentFilterLength = current_filter.length;
            if (currentFilterLength) {
                // special case for radio buttons
                if (current_filter[0].type === 'radio') {
                    for (let b = 0; b < currentFilterLength; b++) {
                        if (current_filter[b].value === event.state[i]) {
                            current_filter[b].checked = true;
                            break;
                        }
                    }
                } else {
                    current_filter[0].value = event.state[i];
                }
            }
        }

        // load the data
        getJsonData(false).then();
    });

    if ($('advance_send_signed_reports_show_report_results')) {
        reportsGenerated.onsubmit = null;

        reportsGenerated.addEventListener('submit', async (e) => {
            e.preventDefault();

            const dateFrom = document.querySelector('[name="from_date"]').value.trim();
            const dateTo = document.querySelector('[name="to_date"]').value.trim();

            if (!dateFrom || !dateTo) {
                alert(i18n['report_required_field_period_empty']);
                return;
            }

            getJsonData(true).then();
        });

        initTable();

        // check if the for has to be autoloaded
        getJsonData(false).then();
    }
});

/**
 * Check if
 *
 * @returns {boolean}
 */
function processCheckedReports() {
    'use strict';

    const checkedReports = document.querySelectorAll('input[type="checkbox"]:checked');
    if (checkedReports.length === 0) {
        alert(i18n['report_message_not_checked_reports']);
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'ajax_get_send_report_panel');
    url.searchParams.set('report_type', $('report_type').value);

    fetch(url)
        .then(response => response.json())
        .then(data => {
            lb = new lightbox({
                content: data['content'],
                title: data['title'],
                width: '450px'
            });
            lb.activate();
        })
        .catch(error => {
            alert('Error: location "' + t.statusText + '" was not found.');
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}

/**
 * Opens light box
 * @param reportId
 */
function openSendEmailsPanel(reportId) {
    'use strict';

    Effect.Center('loading');
    Effect.Appear('loading');

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'ajax_get_send_report_panel');
    url.searchParams.set('report_type', $('report_type').value);
    url.searchParams.set('report_id', reportId);

    fetch(url)
        .then(response => response.json())
        .then(data => {
            lb = new lightbox({
                content: data['content'],
                title: data['title'],
                width: '800px'
            });
            lb.activate();
        })
        .catch(error => {
            alert('Error: ' + error);
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}

/**
 * Send emails button action
 *
 * @returns {boolean}
 */
function sendEmails() {
    'use strict';

    const emailField = document.querySelector('#send_emails_field').value;
    const checkboxes = document.querySelectorAll('input[name="choose_file[]"]:checked');

    if (emailField.trim() === '') {
        alert(i18n['report_no_emails_included']);
        return false;
    }

    if (checkboxes.length === 0) {
        alert(i18n['report_no_attached_files_checked']);
        return false;
    }

    const formattedEmails = emailField.split(/[,;\s\n]+/);

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'reports');
    url.searchParams.set('reports', 'send_email');
    url.searchParams.set('report_type', $('report_type').value);
    url.searchParams.set('report_id', $('hidden_report_id').value);
    url.searchParams.set('email_content', $('content').value);
    url.searchParams.set('email_subject', $('email_subject').value);

    for (let checkbox of checkboxes) {
        url.searchParams.append(checkbox.name, checkbox.value);
    }
    for (let email of formattedEmails) {
        url.searchParams.append('email[]', email);
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            // There is some kind of error
            const errorMessage = data.erred.length === 0 ? i18n[data.code] : data.message;

            if (data.erred.length !== 0) {
                alert(errorMessage);
                return;
            }

            alert(i18n[data.code]);
            window.open(
                env.base_url + '?' + env.module_param + '=reports&report_type=' + $('report_type').value,
                '_self'
            );
        })
        .catch(error => {
            alert('Error: ' + error);
        })
        .finally(() => {
            Effect.Fade('loading');
        });
}

/**
 * Init CK editor
 * @param theme
 */
function initCommentsCKEditor(theme) {
    CKEDITOR.replace('content', {
        customConfig: 'comments_config.js',
        enterMode: CKEDITOR.ENTER_BR,
        skin: theme
    });
}
