<?php

/**
 * Array containing description of all filters
 *
 * @var array $registry
 */

return [
    // DEFINE SCRIPTS
    'custom_scripts' => [
        'custom_id' => 'custom_scripts',
        'name' => 'custom_scripts',
        'custom_template' => PH_MODULES_DIR . 'reports/plugins/' .
            $registry['report_type']['name'] . '/custom_scripts.html',
        'type' => 'custom_filter',
    ],
    // DEFINE DATE FROM FILTER
    'from_date' => [
        'custom_id' => 'from_date',
        'name' => 'from_date',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
        'type' => 'custom_filter',
        'required' => 1,
        'width' => 65,
        'additional_filter' => 'to_date',
        'label' => $this->i18n('filter_date_filter'),
        'help' => $this->i18n('filter_date_filter'),
    ],
    // DEFINE DATE TO FILTER
    'to_date' => [
        'custom_id' => 'to_date',
        'name' => 'to_date',
        'type' => 'date',
        'width' => 65,
        'label' => $this->i18n('to'),
        'help' => $this->i18n('filter_to_date'),
    ],
    // DEFINE SYSTEM MADE BY
    'system_made_by' => [
        'custom_id' => 'system_made_by',
        'name' => 'system_made_by',
        'type' => 'dropdown',
        'required' => 0,
        'label' => $this->i18n('filter_system_made_by'),
        'help' => $this->i18n('filter_system_made_by'),
        'skip_please_select' => true,
        'options' => [
            [
                'label' => $this->i18n('filter_system_made_by_all'),
                'option_value' => '1,2'
            ],
            [
                'label' => $this->i18n('filter_system_made_by_alvis'),
                'option_value' => '1'
            ],
            [
                'label' => $this->i18n('filter_system_made_by_nzoom'),
                'option_value' => '2'
            ],
        ],
    ],
    // DEFINE ASSIGNOR AUTOCOMPLETER
    'assignor' => [
        'custom_id' => 'assignor',
        'name' => 'assignor',
        'type' => 'custom_filter',
        'actual_type' => 'autocompleter',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'autocomplete_type' => 'customers',
        'autocomplete' => [
            'type' => 'customers',
            'url' => sprintf(
                '%s?%s=%s&%s=ajax_select',
                $_SERVER['PHP_SELF'],
                $registry['module_param'],
                'customers',
                'customers'
            ),
            'suggestions' => '<name> <lastname>',
            'fill_options' => [
                '$assignor_autocomplete  => <name> <lastname>',
                '$assignor_oldvalue      => <name> <lastname>',
                '$assignor               => <id>',
            ],
            'filters' => [
                '<type>' => strval(ASSIGNOR_TYPES),
            ],
            'clear' => 1,
            'buttons_hide' => 'search',
        ],
        'autocomplete_buttons' => 'clear',
        'label' => $this->i18n('filter_assignor_name'),
        'help' => $this->i18n('filter_assignor_name'),
    ],
    // DEFINE CUSTOMER AUTOCOMPLETER
    'customer' => [
        'custom_id' => 'customer',
        'name' => 'customer',
        'type' => 'custom_filter',
        'actual_type' => 'autocompleter',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'autocomplete_type' => 'customers',
        'autocomplete' => [
            'type' => 'customers',
            'url' => sprintf(
                '%s?%s=%s&%s=ajax_select',
                $_SERVER['PHP_SELF'],
                $registry['module_param'],
                'customers',
                'customers'
            ),
            'suggestions' => '<name> <lastname>',
            'fill_options' => [
                '$customer_autocomplete  => <name> <lastname>',
                '$customer_oldvalue      => <name> <lastname>',
                '$customer               => <id>',
            ],
            'filters' => [
                '<type>' => strval(CUSTOMER_TYPES),
            ],
            'clear' => 1,
            'buttons_hide' => 'search',
        ],
        'autocomplete_buttons' => 'clear',
        'label' => $this->i18n('filter_customer_name'),
        'help' => $this->i18n('filter_customer_name'),
    ],
    // DEFINE CLIENT AUTOCOMPLETER
    'bank' => [
        'custom_id' => 'bank',
        'name' => 'bank',
        'type' => 'custom_filter',
        'actual_type' => 'autocompleter',
        'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
        'autocomplete_type' => 'customers',
        'autocomplete' => [
            'type' => 'customers',
            'url' => sprintf(
                '%s?%s=%s&%s=ajax_select',
                $_SERVER['PHP_SELF'],
                $registry['module_param'],
                'customers',
                'customers'
            ),
            'suggestions' => '<name> <lastname>',
            'fill_options' => [
                '$bank_autocomplete  => <name> <lastname>',
                '$bank_oldvalue      => <name> <lastname>',
                '$bank               => <id>',
            ],
            'filters' => [
                '<type>' => strval(BANK_TYPES),
            ],
            'clear' => 1,
            'buttons_hide' => 'search',
        ],
        'autocomplete_buttons' => 'clear',
        'label' => $this->i18n('filter_bank_name'),
        'help' => $this->i18n('filter_bank_name'),
    ],
];
