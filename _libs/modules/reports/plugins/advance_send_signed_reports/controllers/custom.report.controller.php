<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

/**
 * Report: send signed reports
 */
class Custom_Report_Controller extends Reports_Controller
{
    /**
     * Email template id
     */
    private const EMAIL_TEMPLATE = 1001;

    /**
     * Generic action dispatcher routing
     * according to the requested action
     *
     * Params:
     * ajax_generate - submit filter
     * ajax_get_send_report_panel - Display panel
     * send_email - on email send
     *
     * @throws Exception
     */
    public function execute()
    {
        switch ($this->action) {
            case 'ajax_generate':
                $this->generateReport();
                break;
            case 'ajax_get_send_report_panel':
                $this->sendEmailsPanel();
                break;
            case 'send_email':
                $this->sendEmail();
                break;
            default:
                parent::execute();
        }
    }

    /**
     * Entry point for the report
     *
     * @return void
     * @throws Exception
     */
    private function generateReport(): void
    {
        $this->getReportMainData();

        // take the filters
        $this->setAction('generate_report');
        $this->registry['request']->set('reports', 'generate_report', 'all', true);
        $this->registry->set('generated_report', 1, true);
        $this->_index();
        $filters = Reports::saveSearchParams(
            $this->registry,
            $this->registry->get('filters_values'),
            'reports_' . $this->report . '_'
        );

        $redirect_parameters = $this->getRedirectParams();
        $redirect_url = sprintf(
            '%s://%s%sindex.php?d=%s',
            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
            $_SERVER['HTTP_HOST'],
            PH_BASE_URL,
            General::encodeUrlData($redirect_parameters)
        );

        if (empty($filters['from_date']) || empty($filters['to_date'])) {
            echo json_encode([
                'table' => [],
                'url' => $redirect_url,
            ]);
            exit;
        }

        $reports = $this->getReportData($filters);

        if (empty(array_filter($reports))) {
            echo json_encode([
                'table' => [],
                'url' => $redirect_url,
            ]);
            exit;
        }

        $reports = $this->getReportsFiles($reports, true);

        $reports = array_values($reports);
        echo json_encode([
            'table' => $reports,
            'url' => $redirect_url,
        ]);
        exit;
    }

    /**
     * Get reports
     *
     * @param array $filters
     *
     * @return array
     */
    private function getReportData(array $filters): array
    {
        // set model lang filter
        // default model language is the interface language
        $model_lang = (!empty($filters['model_lang'])) ? $filters['model_lang'] : $this->registry['lang'];

        $sql = [
            'select' => '',
            'from' => '',
            'where' => '',
            'sort' => '',
        ];
        $tableReferences = [
            'd' => DB_TABLE_DOCUMENTS,
            'ci18n' => DB_TABLE_CUSTOMERS_I18N,
            'dc' => DB_TABLE_DOCUMENTS_CSTM,
            'dc_bank' => DB_TABLE_DOCUMENTS_CSTM,
            'ci18n_assignor' => DB_TABLE_CUSTOMERS_I18N,
            'ci18n_bank' => DB_TABLE_CUSTOMERS_I18N,
            'dc_made_by' => DB_TABLE_DOCUMENTS_CSTM,

            'ea' => DB_TABLE_DOCUMENTS_CSTM,
            'be' => DB_TABLE_DOCUMENTS_CSTM,
            'cc' => DB_TABLE_CUSTOMERS_CSTM,

            'role_user' => DB_TABLE_DOCUMENTS_CSTM,
            'role_type' => DB_TABLE_DOCUMENTS_CSTM,
            'u' => DB_TABLE_USERS,

            'ds' => DB_TABLE_DOCUMENTS_STATUSES,
            'tm' => DB_TABLE_TAGS_MODELS,
        ];


        $varsFromReportSettings = $this->getAdditionalVarsInfo();
        $varIdForAssignor = !empty($varsFromReportSettings[DOCUMENT_VAR_ASSIGNOR_ID])
            ? $varsFromReportSettings[DOCUMENT_VAR_ASSIGNOR_ID]
            : '';
        $varIdForBank = !empty($varsFromReportSettings[DOCUMENT_VAR_BANK_ID])
            ? $varsFromReportSettings[DOCUMENT_VAR_BANK_ID]
            : '';
        $varIdSystemMadeBy = !empty($varsFromReportSettings[DOCUMENT_VAR_SYSTEM_MADE_BY_ID])
            ? $varsFromReportSettings[DOCUMENT_VAR_SYSTEM_MADE_BY_ID]
            : '';
        $systemMadeBy = $filters['system_made_by'] ?? constant('DOCUMENT_VAR_SYSTEM_MADE_BY');
        $clientEmail = $varsFromReportSettings[DOCUMENT_VAR_EMAIL_ADDRESS];
        $bankEmployeeEmail = $varsFromReportSettings[DOCUMENT_VAR_BANK_EMPLOYEE_EMAIL];
        $emailBankOffice = $this->getSingleVarInfo(
            'customer',
            3,
            DOCUMENT_VAR_EMAIL_BANK_OFFICE
        );

        $recordingRoleId = $varsFromReportSettings[DOCUMENT_VAR_RECORDING_ROLE_ID];
        $recordindRoleType = $varsFromReportSettings[DOCUMENT_VAR_RECORDING_ROLE_TYPE];
        $documentStatus = !empty(constant("DOCUMENT_STATUS"))
            ? preg_split('#\s*,\s*#', constant("DOCUMENT_STATUS"))
            : [];
        $countDocStatuses = count($documentStatus) ?? 0;
        $documentSubStatus = !empty(constant("DOCUMENT_SUB_STATUS"))
            ? preg_split('#\s*,\s*#', constant("DOCUMENT_SUB_STATUS"))
            : [];
        $countDocSubStatuses = count($documentSubStatus) ?? 0;
        $searchActive = preg_split('#\s*,\s*#', constant("REPORT_SEARCH_ACTIVE")) ?? [];
        $countSearchActive = count($searchActive) ?? 0;
        $reportDocTypes = preg_split('#\s*,\s*#', constant("REPORT_DOCUMENTS_TYPES")) ?? [];
        $countReportDocTypes = count($reportDocTypes) ?? 0;
        $reportTagId = constant("REPORT_TAG_ID");

        $sql['select'] = <<<SQL
                        SELECT
                            d.id AS idx,
                            d.id,
                            d.full_num,
                            d.date,
                            d.customer,
                            ds.name AS sub_status,
                            CONCAT(ci18n.name, " ", ci18n.lastname) AS customer_name,
                            dc.value AS assignor_id,
                            CONCAT(ci18n_assignor.name, " ", ci18n_assignor.lastname) AS assignor_name,
                            dc_bank.value AS bank_id,
                            CONCAT(ci18n_bank.name, " ", ci18n_bank.lastname) AS bank_name,
                            ea.value AS email_address,
                            be.value AS bank_employee_email,
                            cc.value AS email_bank_office,
                            role_user.value AS role_user,
                            role_type.value AS role_type_id,
                            u.id AS user_id,
                            CASE
                                WHEN dc_made_by.value = 1
                                    THEN "Alvis"
                                WHEN dc_made_by.value = 2
                                    THEN "nZoom"
                                ELSE "1,2"
                            END AS system_made_by
                        SQL;
        $sql['from'] = <<<SQL
                        FROM
                            {$tableReferences['d']} AS d
                        JOIN {$tableReferences['ci18n']} AS ci18n
                            ON (ci18n.parent_id = d.customer AND ci18n.lang = "$model_lang")
                        JOIN {$tableReferences['dc_made_by']} AS dc_made_by
                            ON (dc_made_by.model_id = d.id
                                AND dc_made_by.var_id = "$varIdSystemMadeBy"
                                AND dc_made_by.value IN ({$systemMadeBy}))
                        LEFT JOIN {$tableReferences['dc']} AS dc
                            ON (dc.model_id = d.id AND dc.var_id = "$varIdForAssignor")
                        LEFT JOIN {$tableReferences['ci18n_assignor']} AS ci18n_assignor
                            ON (ci18n_assignor.parent_id = dc.value AND ci18n_assignor.lang = "$model_lang")
                        LEFT JOIN {$tableReferences['dc_bank']} AS dc_bank
                            ON (dc_bank.model_id = d.id AND dc_bank.var_id = "$varIdForBank")
                        LEFT JOIN {$tableReferences['cc']} AS cc
                            ON cc.model_id = dc_bank.value AND cc.var_id = "{$emailBankOffice['email_bank_office']}"
                        LEFT JOIN {$tableReferences['ci18n_bank']} AS ci18n_bank
                            ON (ci18n_bank.parent_id = dc_bank.value AND ci18n_bank.lang = "$model_lang")
                        LEFT JOIN {$tableReferences['ea']} AS ea
                            ON (ea.model_id = dc.model_id AND ea.var_id = "{$clientEmail}")
                        LEFT JOIN {$tableReferences['be']} AS be
                            ON (be.model_id = dc.model_id AND be.var_id = "{$bankEmployeeEmail}")
                        LEFT JOIN {$tableReferences['role_type']} AS role_type
                            ON (role_type.model_id = dc.model_id AND role_type.var_id = "{$recordindRoleType}"
                            AND role_type.value = 4)
                        LEFT JOIN {$tableReferences['role_user']} AS role_user
                            ON (role_user.model_id = dc.model_id AND role_user.var_id = "{$recordingRoleId}"
                            AND role_type.num = role_user.num)
                        LEFT JOIN {$tableReferences['u']} AS u
                            ON u.employee = role_user.value
                        LEFT JOIN {$tableReferences['ds']} AS ds
                            ON (d.status = ds.status AND d.substatus = ds.id)
                            AND d.type = ds.doc_type
                        LEFT JOIN {$tableReferences['tm']} AS tm
                            ON (tm.model_id = d.id AND tm.model = 'Document'
                            AND tm.tag_id = {$reportTagId})
                        SQL;

        $where = [
            'd.deleted_by = 0',
            'tm.tag_id IS NULL',
        ];

        if ($countSearchActive !== 0) {
            $where[] = 'd.active ' . (($countSearchActive == 1)
                    ? '= "' . $searchActive[0] . '"'
                    : 'IN("' . implode('", "', $searchActive) . '")');
        }

        if ($countReportDocTypes !== 0) {
            $where[] = 'd.type ' . (($countReportDocTypes == 1)
                    ? '= "' . $reportDocTypes[0] . '"'
                    : 'IN("' . implode('", "', $reportDocTypes) . '")');
        }

        if ($countDocStatuses !== 0) {
            $where[] = 'd.status ' . (($countDocStatuses == 1)
                    ? '= "' . $documentStatus[0] . '"'
                    : 'IN("' . implode('", "', $documentStatus) . '")');
        }

        if ($countDocSubStatuses !== 0) {
            $where[] = 'd.substatus ' . (($countDocSubStatuses == 1)
                    ? '= "' . $documentSubStatus[0] . '"'
                    : 'IN("' . implode('", "', $documentSubStatus) . '")');
        }

        if (!empty($filters['report_id'])) {
            $where[] = 'd.id = "' . $filters['report_id'] . '"';
        }
        if (!empty($filters['from_date'])) {
            $where[] = 'd.date >= "' . date('Y-m-d', strtotime($filters['from_date'])) . '"';
        }
        if (!empty($filters['to_date'])) {
            $where[] = 'd.date <= "' . date('Y-m-d', strtotime($filters['to_date'])) . '"';
        }
        if (!empty($filters['assignor'])) {
            $assignorIds = array_filter($filters['assignor']);
            if (!empty($assignorIds)) {
                $where[] = 'dc.value IN("' . implode('", "', $assignorIds) . '")';
            }
        }
        if (!empty($filters['customer'])) {
            $customerIds = array_filter($filters['customer']);
            if (!empty($customerIds)) {
                $where[] = 'd.customer IN("' . implode('", "', $customerIds) . '")';
            }
        }
        if (!empty($filters['bank'])) {
            $bankIds = array_filter($filters['bank']);
            if (!empty($bankIds)) {
                $where[] = 'dc_bank.value IN("' . implode('", "', $bankIds) . '")';
            }
        }

        $sql['where'] = 'WHERE ' . implode(' AND ', $where);
        $sql['sort'] = 'ORDER BY d.date DESC, d.id ASC';

        $query = implode("\n", $sql);

        $reports = $this->registry['db']->GetAssoc($query) ?? [];

        $counter = 1;
        $result = [];
        $restrictedRoles = explode(',', RESTRICTED_ROLES);

        foreach ($reports as $report) {
            if (
                (in_array($this->registry['currentUser']->get('role'), $restrictedRoles)
                    && $report['user_id'] != $this->registry['currentUser']->get('id'))
            ) {
                continue;
            }

            $report['counter'] = $counter++;
            $report['files'] = [];
            $result[$report['id']] = $report;
        }

        return $result;
    }

    /**
     * Load report data
     *
     * @return void
     * @throws Exception
     */
    private function getReportMainData(): void
    {
        $report = $this->getReportType();

        if (empty($report)) {
            $this->redirect($this->module, '', ['report_type' => ''], '');
        }
        $report = $report['name'];
        $this->report = $report;

        // load plugin i18n files
        $i18n_file = sprintf(
            '%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini'
        );
        $this->report_lang_file = $i18n_file;
        $this->registry['translater']->loadFile($this->report_lang_file);
        Reports::getReportSettings($this->registry, $report);
    }

    /**
     * Getting the report name and source
     *
     * @return array - report name and origin where it was defined from, or an empty array if not found
     * @throws Exception
     */
    public function getReportType(): array
    {
        $request = &$this->registry['request'];

        if ($request->isRequested('report_type')) {
            $report_type = [
                'name' => $request->get('report_type'),
                'source' => 'request',
            ];
        } elseif ($this->registry->get('report_type')) {
            $report_type = $this->registry->get('report_type');
        } else {
            $report_type = [];
        }
        $this->registry->set('report_type', $report_type, true);

        return $report_type;
    }

    /**
     * Get redirect params
     *
     * @return array
     */
    private function getRedirectParams(): array
    {
        $redirectParameters = [];
        foreach ($this->registry['request']->getAll() as $post_var => $post_value) {
            if (is_array($post_value)) {
                foreach ($post_value as $key => $val) {
                    $redirectParameters[] = [
                        'param' => sprintf('%s[%s]', $post_var, $key),
                        'value' => $val,
                    ];
                }
            } else {
                $redirectParameters[] = [
                    'param' => $post_var,
                    'value' => $post_value,
                ];
            }
        }

        return $redirectParameters;
    }

    /**
     * Get additional vars name and id
     *
     * @return array
     */
    private function getAdditionalVarsInfo(): array
    {
        $reportSettings = [
            DOCUMENT_VAR_ASSIGNOR_ID,
            DOCUMENT_VAR_BANK_ID,
            DOCUMENT_VAR_SYSTEM_MADE_BY_ID,
            DOCUMENT_VAR_DOC_TYPE_CATEGORY,
            DOCUMENT_VAR_ATTACHED_DOC_FILE,
            DOCUMENT_VAR_ATTACHED_DOC,
            DOCUMENT_VAR_EMAIL_ADDRESS,
            DOCUMENT_VAR_BANK_EMPLOYEE_EMAIL,
            DOCUMENT_VAR_RECORDING_ROLE_ID,
            DOCUMENT_VAR_RECORDING_ROLE_TYPE,
            DOCUMENT_VAR_LAST_REPORT,
            DOCUMENT_VAR_DOC_ISSUED_BY,
        ];

        $sql = 'SELECT
                    `name`,
                    `id`
                FROM
                    ' . DB_TABLE_FIELDS_META . '
                WHERE
                    `model` = "Document"
                    AND `name` IN ("' . implode('", "', $reportSettings) . '")
                    AND `model_type` IN (' . SEARCH_VARS_IN_MODELS . ')';

        return $this->registry['db']->GetAssoc($sql) ?? [];
    }

    /**
     * Get single var info
     *
     * @param string $model
     * @param int    $modelType
     * @param string $varName
     *
     * @return array
     */
    private function getSingleVarInfo(string $model, int $modelType, string $varName): array
    {
        if (trim($model) === '') {
            return [];
        }

        $modelName = ucfirst(strtolower($model));
        $var = trim($varName);
        $sql = 'SELECT
                    `name`,
                    `id`
                FROM
                    `_fields_meta`
                WHERE
                    `model` = "' . $modelName . '"
                    AND model_type = ' . $modelType . '
                    AND name = "' . $var  . '"';

        return $this->registry['db']->GetAssoc($sql) ?? [];
    }

    /**
     * Displays panel
     *
     * @return void
     * @throws Exception
     */
    private function sendEmailsPanel(): void
    {
        $this->getReportMainData();
        // prepare viewer for the user to select the required container
        // build the viewer
        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $reportId = $this->registry['request']->get('report_id');
        $reports = $this->getReportData(['report_id' => $reportId]);
        $reports = $this->getReportsFiles($reports);
        $report = reset($reports);
        $emailsToSend = [
            $report['email_address'],
            $report['bank_employee_email'],
            $report['email_bank_office'],
        ];
        $emailsToSendList = implode(', ', array_filter(array_unique($emailsToSend)));

        $filters = [
            'where' => [
                'e.id = ' . self::EMAIL_TEMPLATE,
            ],
            'sanitize' => true,
        ];
        $mail = Emails::searchOne($this->registry, $filters);
        $body = $this->parseEmailTemplateBodyVars($mail->get('body'));

        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_send_report_panel.html';
        $viewer->data['current_report'] = $report;
        $viewer->data['emails'] = [
            'list' => $emailsToSendList,
        ];

        $emailSubject = $this->generateMailSubject($report);
        $viewer->data['email'] = [
            'template' => $body,
            'subject' => $emailSubject,
        ];

        $operation_result = [
            'content' => $viewer->fetch(),
            'title' => $this->i18n('report_send_report_mail_title'),
        ];

        echo json_encode($operation_result);
        exit;
    }

    /**
     * Generate email subject
     *
     * @param array $report
     *
     * @return string
     */
    private function generateMailSubject(array $report): string
    {
        $reportId = $report['id'] ?? '';
        $sql = <<<SQL
            SELECT
                GROUP_CONCAT(gdin.article_deliverer_name SEPARATOR '_') AS subject
            FROM gt2_details AS gd
            LEFT JOIN gt2_details_i18n AS gdin
                ON gd.id = gdin.parent_id
            WHERE
                gd.model = 'Document'
                AND gd.model_id = {$reportId}
                AND gdin.lang = 'bg';
        SQL;
        $result = $this->registry['db']->GetOne($sql) ?? '';

        return rtrim(implode(
            '_',
            [
                'Адванс',
                rtrim(implode('_', explode(' ', $report['bank_name'] ?? '')), '_'),
                rtrim(implode('_', explode(' ', $report['customer_name'] ?? '')), '_'),
                $result,
                date('d.m.Y'),
            ]
        ), '_');
    }

    /**
     * Get report files
     *
     * @param array $reports
     * @param bool  $showSignedFileOnly
     *
     * @return array
     */
    private function getReportsFiles(array $reports, bool $showSignedFileOnly = false): array
    {
        $reportIds = array_column($reports, 'id') ?? [];
        $reportIdsAsString = !empty(array_filter($reportIds)) ? implode(', ', $reportIds) : '';
        $signedDocumentOptionValue = SIGNED_ATTACHED_FILE_OPTION_ID;
        $additionalVars = $this->getAdditionalVarsInfo();

        $aliases = [
            'd' => DB_TABLE_DOCUMENTS,
            'dc' => DB_TABLE_DOCUMENTS_CSTM,
            'dc2' => DB_TABLE_DOCUMENTS_CSTM,
            'dc3' => DB_TABLE_DOCUMENTS_CSTM,
            'dc4' => DB_TABLE_DOCUMENTS_CSTM,
            'dc5' => DB_TABLE_DOCUMENTS_CSTM,
        ];
        $sql = 'SELECT
                    dc2.value AS signed_file_id,
                    dc3.value AS attached_file_description,
                    dc.value AS signed_file,
                    d.id,
                    dc4.value AS last_report_version,
                    dc5.value AS doc_issued_by
                FROM ' . $aliases['d'] . ' AS d ';
        $sql .= 'JOIN ' . $aliases['dc'] . ' AS dc
                    ON d.id = dc.model_id
                    AND dc.var_id = "' . $additionalVars[DOCUMENT_VAR_DOC_TYPE_CATEGORY] . '" ';
        $sql .= 'JOIN ' . $aliases['dc2'] . ' AS dc2
                    ON d.id = dc2.model_id
                    AND dc2.var_id = "' . $additionalVars[DOCUMENT_VAR_ATTACHED_DOC_FILE] . '" ';
        $sql .= 'AND dc.num = dc2.num
                AND dc2.value != ""
            JOIN ' . $aliases['dc3'] . ' AS dc3
                ON dc2.model_id = dc3.model_id
                AND dc3.var_id = "' . $additionalVars[DOCUMENT_VAR_ATTACHED_DOC] . '"
                AND dc3.num = dc2.num ';
        $sql .= 'LEFT JOIN ' . $aliases['dc4'] . ' AS dc4
                    ON d.id = dc4.model_id
                    AND dc4.var_id = "' . $additionalVars[DOCUMENT_VAR_LAST_REPORT] . '" ';
        $sql .= 'LEFT JOIN ' . $aliases['dc5'] . ' AS dc5
                    ON d.id = dc5.model_id
                    AND dc5.var_id = "' . $additionalVars[DOCUMENT_VAR_DOC_ISSUED_BY] . '"
                    AND dc5.num = dc2.num ';

        $sql .= 'WHERE d.id in (' . $reportIdsAsString . ') ';

        $documentStatus = !empty(constant("DOCUMENT_STATUS"))
            ? preg_split('#\s*,\s*#', constant("DOCUMENT_STATUS"))
            : [];
        $countDocStatuses = count($documentStatus) ?? 0;
        $documentSubStatus = !empty(constant("DOCUMENT_SUB_STATUS"))
            ? preg_split('#\s*,\s*#', constant("DOCUMENT_SUB_STATUS"))
            : [];
        $countDocSubStatuses = count($documentSubStatus) ?? 0;
        $searchActive = preg_split('#\s*,\s*#', constant("REPORT_SEARCH_ACTIVE")) ?? [];
        $countSearchActive = count($searchActive) ?? 0;
        $reportDocTypes = preg_split('#\s*,\s*#', constant("REPORT_DOCUMENTS_TYPES")) ?? [];
        $countReportDocTypes = count($reportDocTypes) ?? 0;

        $sql .= 'AND d.deleted_by = 0 ';

        if ($countSearchActive !== 0) {
            $sql .= 'AND d.active ' . (($countSearchActive == 1)
                    ? '= "' . $searchActive[0] . '" '
                    : 'IN("' . implode('", "', $searchActive) . '") ');
        }

        if ($countReportDocTypes !== 0) {
            $sql .= 'AND d.type ' . (($countReportDocTypes == 1)
                    ? '= "' . $reportDocTypes[0] . '" '
                    : 'IN("' . implode('", "', $reportDocTypes) . '") ');
        }

        if ($countDocStatuses !== 0) {
            $sql .= 'AND d.status ' . (($countDocStatuses == 1)
                    ? '= "' . $documentStatus[0] . '" '
                    : 'IN("' . implode('", "', $documentStatus) . '") ');
        }

        if ($countDocSubStatuses !== 0) {
            $sql .= 'AND d.substatus ' . (($countDocSubStatuses == 1)
                    ? '= "' . $documentSubStatus[0] . '" '
                    : 'IN("' . implode('", "', $documentSubStatus) . '") ');
        }

        if ($showSignedFileOnly) {
            $sql .= 'AND dc.value = "' . $signedDocumentOptionValue . '" ';
        }

        $filesIds = $this->registry['db']->GetAssoc($sql) ?? [];

        if ($showSignedFileOnly === false && constant('SHOW_ONLY_ISSUED_BY') === '1') {
            $filesIds = array_filter($filesIds, function ($value) {
                return $value['doc_issued_by'] === '1';
            });
        }

        if (empty($filesIds)) {
            return $reports;
        }

        $addLastReportVersionFile = (int) constant('ADD_LAST_REPORT_VERSION_FILE');
        $filesLastVersion = array_unique(array_filter(array_column($filesIds, 'last_report_version')));
        $combinedAllFiles = array_merge(array_keys($filesIds), $filesLastVersion);

        $filters = [
            'where' => [
                'f.model = "Document"',
                'f.id IN (' . implode(', ', $combinedAllFiles) . ')',
                'f.model_id IN (' . implode(', ', array_values($reportIds)) . ')',
            ],
        ];

        $attachedFiles = Files::search($this->registry, $filters);

        foreach ($attachedFiles as $key => $file) {
            $fileSize = file_exists($file->get('path'))
                ? $this->formatFileSize(filesize($file->get('path')))
                : $this->formatFileSize(0);

            $reports[$file->get('model_id')]['files'][] = [
                'id' => $file->get('id'),
                'filename' => $file->get('filename'),
                'file_description' => isset($filesIds[$file->get('id')])
                    ? $filesIds[$file->get('id')]['attached_file_description']
                    : $file->get('description'),
                'path' => $file->get('path'),
                'extension' => $file->getIconName(),
                'exists' => !$file->get('not_exist'),
                'model_id' => $file->get('model_id'),
                'file_size' => $fileSize,
                'is_signed' => isset($filesIds[$file->get('id')])
                    ? (constant('SIGNED_ATTACHED_FILE_OPTION_ID') == $filesIds[$file->get('id')]['signed_file'])
                    : false,
            ];

            // Check if the report is made by Alvis and if the last report version file should be added
            // If the last report version file should not be added, remove it from the array
            if (intval($reports[$file->get('model_id')]['system_made_by']) === 1 && $addLastReportVersionFile === 0) {
                if (in_array($file->get('id'), $filesLastVersion)) {
                    unset($reports[$file->get('model_id')]['files'][$key]);
                }
            }
        }

        return $reports;
    }

    /**
     * Format file size
     *
     * @param int $bytes
     *
     * @return string
     */
    private function formatFileSize(int $bytes): string
    {
        // If the input is less than 0, return an empty string or error.
        if ($bytes < 0) {
            return 'Invalid size';
        }

        // Define size units
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

        // Calculate the index for the unit
        $unitIndex = 0;

        // Divide the bytes by 1024 until it's less than 1024
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }

        // Format the number to two decimal places and append the appropriate unit
        return round($bytes, 2) . ' (' . $units[$unitIndex] . ')';
    }

    /**
     * Sends email
     *
     * @throws Exception
     */
    private function sendEmail(): void
    {
        $this->getReportMainData();

        $this->validateRequestFields();

        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';

        $reportId = $this->registry['request']->get('report_id') ?? '';
        $fileIdsToAttachForSend = $this->registry['request']->get('choose_file') ?? [];
        $emails = $this->registry['request']->get('email') ?? [];

        $report = Documents::searchOne($this->registry, [
            'where' => [
                'd.id = ' . $reportId,
            ],
        ]);
        $report->set('customer_email', $emails);
        $sender = [
            'name' => $this->registry['currentUser']->get('firstname')
                . ' ' . $this->registry['currentUser']->get('lastname'),
            'mail' => $this->registry['currentUser']->get('email'),
        ];

        foreach ($fileIdsToAttachForSend as $key => $id) {
            $fileIdsToAttachForSend[$key] = 'file_' . $id;
        }
        $report->set('attached_files', $fileIdsToAttachForSend);

        $filters = [
            'where' => [
                'e.id = ' . self::EMAIL_TEMPLATE,
            ],
            'sanitize' => true,
        ];
        $mail = Emails::searchOne($this->registry, $filters);

        if (empty($mail)) {
            echo json_encode([
                'code' => 'report_mail_not_sent',
                'erred' => [''],
                'message' => $this->i18n('report_mail_not_sent'),
            ]);
            exit;
        }

        $body = $this->registry['request']->get('email_content') ?? '';
        $report->set('body', $body, true);
        $report->set('email_subject', $this->registry['request']->get('email_subject'), true);
        $report->set('email_template', $mail->get('id'), true);
        $extra = 'report := ' . $report->get('type');
        $report->set('mail_additional_extra', $extra, true);
        $report->set('custom_sender', $sender['mail'], true);
        $report->set('custom_from_name', $sender['name'], true);


        $this->registry['db']->StartTrans();
        $result = $report->sendAsMail();

        if (count($emails) == count($result['sent'])) {
            $this->addReportHistory($report);

            $operationResult = [
                'code' => empty($result['erred'])
                    ? 'report_mail_successfully_sent'
                    : 'report_mail_failed_receiver',
                'erred' => $result['erred'],
                'message' => empty(array_filter($result['erred']))
                    ? $this->i18n('report_mail_successfully_sent')
                    : sprintf(
                        $this->i18n('report_mail_failed_receiver'),
                        implode(', ', $result['erred'])
                    ),
            ];
        } else {
            $this->registry['db']->FailTrans();

            $operationResult = [
                'code' => 'report_mail_not_sent',
                'erred' => $result['erred'],
                'message' => $this->i18n('report_mail_not_sent'),
            ];
        }

        $this->registry['db']->CompleteTrans();

        echo json_encode($operationResult);
        exit;
    }

    /**
     * Validate request fields
     *
     * @return void
     */
    private function validateRequestFields(): void
    {
        $reportId = $this->registry['request']->get('report_id') ?? '';
        $fileIdsToAttachForSend = $this->registry['request']->get('choose_file') ?? [];
        $emails = $this->registry['request']->get('email') ?? [];

        if (trim($reportId) === '') {
            echo json_encode([
                'code' => 'report_not_provided',
                'erred' => [''],
                'message' => $this->i18n('report_not_provided'),
            ]);
            exit;
        }
        if (empty(array_filter($fileIdsToAttachForSend))) {
            echo json_encode([
                'code' => 'report_attached_files_not_provided',
                'erred' => [''],
                'message' => $this->i18n('report_attached_files_not_provided'),
            ]);
            exit;
        }
        if (empty(array_filter($emails))) {
            echo json_encode([
                'code' => 'report_no_emails_included',
                'erred' => [''],
                'message' => $this->i18n('report_no_emails_included'),
            ]);
            exit;
        }
        if (!Validator::validEmail($emails)) {
            echo json_encode([
                'code' => 'report_invalid_email_provided',
                'erred' => [''],
                'message' => $this->i18n('report_invalid_email_provided'),
            ]);
            exit;
        }
    }

    /**
     * Parse email template body vars
     * and replace them with the actual values
     * from the report
     *
     * @param string $emailBody
     *
     * @return string
     */
    private function parseEmailTemplateBodyVars(string $emailBody): string
    {
        $reportId = $this->registry['request']->get('report_id');
        $filters = [
            'where' => [
                'd.id = ' . $reportId,
            ],
        ];
        $report0 = Documents::searchOne($this->registry, $filters);
        $report0->getVars();
        $patterns_vars = $report0->getPatternsVars();
        $report0->extender = new Extender();
        $report0->extender->model_lang = $report0->get('model_lang');
        $report0->extender->module = $report0->module;
        foreach ($patterns_vars as $key => $value) {
            $report0->extender->add($key, $value);
        }
        $report0->extender->add('add_signature', $this->registry['currentUser']->get('signature'));

        return $report0->extender->expand($emailBody) ?? '';
    }

    /**
     * Add report history
     *
     * @param Document $report
     *
     * @return void
     */
    private function addReportHistory(Document $report): void
    {
        Documents_History::saveData(
            $this->registry,
            [
                'model' => $report,
                'action_type' => 'email',
                'old_model' => $report,
                'new_model' => $report,
            ]
        );

        $report->getTags();
        $report->getModelTagsForAudit();
        $oldReport = clone($report);

        $report->set('tags', array_merge($report->get('tags'), [REPORT_TAG_ID]), true);

        if ($report->updateTags(['skip_permissions' => true])) {
            // get the updated event
            $filters = [
                'where' => [
                    'd.id = "' . $report->get('id') . '"',
                ],
                'model_lang' => $this->registry['lang'],
            ];
            $newReport = Documents::searchOne($this->registry, $filters);
            $newReport->getModelTagsForAudit();
            $newReport->sanitize();
            Documents_History::saveData(
                $this->registry,
                [
                    'model' => $newReport,
                    'action_type' => 'tag',
                    'new_model' => $newReport,
                    'old_model' => $oldReport,
                ]
            );
        }
    }
}
