<tr style="display:none;">
    <td class="labelbox">
        <label for="{$filter_settings.custom_id|default:$filter_settings.name}" style="white-space: nowrap;">{help
            label_content=$filter_settings.label text_content=$filter_settings.help}</label>
    </td>
    <td {if $filter_settings.required} class="required">{#required#}{else}>&nbsp;{/if}</td>
    <td nowrap="nowrap" style="vertical-align: top;">
        <input type="hidden" disabled="disabled" value="{$smarty.const.DEADLINE_SOURCE}" id="deadline_source"/>

        <script id="counter" type="text/x-jsrender">
            {literal}${counter}{/literal}
        </script>
        <script id="templateSendReport" type="text/x-jsrender">
            <img src="{$theme->imagesUrl}send_email.png" onclick="openSendEmailsPanel({literal}${id}{/literal})" width="16" height="16" alt="{#title_send_report#}" title="{#title_send_report#}" border="0" style="vertical-align: middle;" />
        </script>
        <script id="templateDocumentNum" type="text/x-jsrender">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={literal}${id}{/literal}" style="font-size: 13px;" target="_blank">
              {literal}${full_num}{/literal}
            </a>
            <br>
            {literal}${sub_status}{/literal}
        </script>

        <script id="templateAssignor" type="text/x-jsrender">
            {literal}${if(assignor_id)}{/literal}
                <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${assignor_id}{/literal}" style="font-size: 13px;" target="_blank">
                  {literal}${assignor_name}{/literal}
                </a>
            {literal}${/if}{/literal}
        </script>

        <script id="templateCustomer" type="text/x-jsrender">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${customer}{/literal}" style="font-size: 13px;" target="_blank">
              {literal}${customer_name}{/literal}
            </a>
        </script>
        <script id="templateBank" type="text/x-jsrender">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={literal}${bank_id}{/literal}" style="font-size: 13px;" target="_blank">
              {literal}${bank_name}{/literal}
            </a>
        </script>
        <script id="templateSignedReport" type="text/x-jsrender">
            {literal}${for (file of files)}{/literal}
                {literal}${if(file.is_signed)}{/literal}
                  {literal}${if(file.exists)}{/literal}
                      <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=viewfile&amp;viewfile={literal}${file.model_id}{/literal}&amp;file={literal}${file.id}{/literal}"
                      target="_blank">
                  {literal}${/if}{/literal}
                      <img border="0" width="14" height="14" src="{$theme->imagesUrl}{literal}${file.extension}{/literal}.png" alt="{literal}${file.filename}{/literal}" title="{literal}${file.filename}{/literal}" class="{literal}${if(file.exists)}pointer${else}dimmed${/if}{/literal}" />
                  {literal}${if(file.exists)}{/literal}
                  </a>
                  {literal}${/if}{/literal}
                {literal}${/if}{/literal}
            {literal}${/for}{/literal}
        </script>
    </td>
</tr>
