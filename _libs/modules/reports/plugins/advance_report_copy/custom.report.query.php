<?php

class Advance_Report_Copy extends Reports
{
    private static Registry $registry;

    private static array $modelTypes = [
        'report' => [2],
        'report13' => [13],
        'land' => [15, 16, 17, 14],
        'level2' => [18, 19, 20, 21, 22, 23, 24],
        'level3' => [41, 42, 25, 26, 27, 28, 29, 30, 31],
    ];
    /*private static $varIds = [
        'order_date' => '1402',
        'created_from_document_id' => '1413',
        'object_identifyer' => [
            'land' => [1906, 2006, 19206, 20206, 21206, 22206, 23206],
            'level2' => [1906, 2006, 19206, 20206, 21206, 22206, 23206],
            'level3' => [1906, 2006, 19206, 20206, 21206, 22206, 23206],
        ],
        'report_name_id' => [
            'land' => [13251, 14245, 15227, 1809],
            'level2' => [17280, 18268, 19268, 20284, 21287, 22269, 23265],
            'level3' => [24271, 25271, 26269, 27271, 28272, 29266, 0266, 4206, 4302],
        ],
    ];*/

    private static array $reportValidation = [
        'orderDate_interval' => '12 MONTH',
        'substatuses' => [6, 38],
    ];

    private static $fieldVarIds;
    private static $searchParams;
    private static $searchMapStatic = [
        [
            'document_type' => 'report',
            'var_type' => 'basic',
            'group' => false,
            'field_name' => 'full_num',
        ],
        [
            'document_type' => 'report',
            'var_type' => 'additional',
            'field_name' => 'award_date',
            'group' => false,
            //'varId' => [580]
        ],
        [
            'document_type' => 'report',
            'var_type' => 'additional',
            'field_name' => 'award_date_from',
            'group' => false,
            //'varId' => [580]
        ],
        [
            'document_type' => 'report',
            'var_type' => 'additional',
            'field_name' => 'award_date_to',
            'group' => false,
            //'varId' => [580]
        ],
        [
            'document_type' => 'report',
            'var_type' => 'additional',
            'field_name' => 'loan_applicant_id',
            'group' => false,
            //'varId' => [800942]
        ],
        [
            'document_type' => 'report',
            'var_type' => 'additional',
            'field_name' => 'assignor_id',
            'group' => false,
        ],
        [
            'document_type' => 'report',
            'var_type' => 'additional',
            'field_name' => 'bank_id',
            'group' => false,
            // 'varId' => [205]
        ],
        /*'object_identifier' => [
            'document_type' => 'report',
            'var_type' => 'additional',
            'field_name' => 'object_identifier',
            'group' => false,
            // 'varId' => [1101225]
        ],*/
        [
            'document_type' => 'land',
            'var_type' => 'additional',
            'field_name' => 'identity_type',
            'group' => false,
            // 'varId' => [13204, 1604, 1704, 1804]
        ],
        [
            'document_type' => 'level2',
            'var_type' => 'additional',
            'field_name' => 'object_identifier',
            'group' => false,
            // 'varId' => [13204, 1604, 1704, 1804]
        ],
        [
            'document_type' => 'level3',
            'var_type' => 'additional',
            'field_name' => 'object_id',
            'group' => false,
            // 'varId' => [13204, 1604, 1704, 1804]
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'object_name_type_id',
            'group' => true,
            // 'varId' => [1434],// group
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'populated_place_id',
            'group' => false,
            //'varId' => [1448],
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'analog_quarter_id',
            'group' => false,
            //'varId' => [1448],
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'full_adress',
            'group' => false,
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'street_id',
            'group' => false,
            //'varId' => [1455],
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'street_number',
            'group' => false,
            //'varId' => [1456],
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'created_from_document_id',
            'group' => false,
            //'varId' => [14555],
        ],
        [
            'document_type' => 'report13',
            'var_type' => 'additional',
            'field_name' => 'object_subtype_id',
            'group' => false,
            //'varId' => [14555],
        ],
    ];

    private static $bbVariantKeys = [
        '_fourteen',
        '_fifteen',
        '_sixteen',
        '_seventeen',
        '_eighteen',
        '_twentyseven',
        '_twentythree',
    ];

    private static ?array $searchMap;

    private static ?ADODB_mysqli $db;

    private static array $reportDatavarMap = [
        'evaluators' => ['var_name' => 'evaluators', 'source' => 'report', 'num' => 1, 'group' => false],
        'assignor_id' => ['var_name' => 'assignor_id', 'source' => 'report', 'num' => 1, 'group' => false],
        'assignor_name' => ['var_name' => 'assignor_name', 'source' => 'report', 'num' => 1, 'group' => false],
        'bank_id' => ['var_name' => 'bank_id', 'source' => 'report', 'num' => 1, 'group' => false],
        'bank_name' => ['var_name' => 'bank_name', 'source' => 'report', 'num' => 1, 'group' => false],
        'loan_applicant_id' => ['var_name' => 'loan_applicant_id', 'source' => 'report', 'num' => 1, 'group' => false],
        'loan_applicant_name' => ['var_name' => 'loan_applicant_name', 'source' => 'report', 'num' => 1, 'group' => false],
        'order_date' => ['var_name' => 'order_date', 'source' => 'report1', 'num' => 1, 'group' => false],
        // address
        'populated_place' => ['var_name' => 'populated_place', 'source' => 'report1', 'num' => 1, 'group' => false],
        'municipality_name' => ['var_name' => 'municipality_name', 'source' => 'report1', 'num' => 1, 'group' => false],
        'region_name' => ['var_name' => 'region_name', 'source' => 'report1', 'num' => 1, 'group' => false],
        'analog_quarter_name' => ['var_name' => 'analog_quarter_name', 'source' => 'report1', 'num' => 1, 'group' => false],
        'street_name' => ['var_name' => 'street_name', 'source' => 'report1', 'num' => 1, 'group' => false],
        'apartment_building' => ['var_name' => 'apartment_building', 'source' => 'report1', 'num' => 1, 'group' => false],
        'entrance' => ['var_name' => 'entrance', 'source' => 'report1', 'num' => 1, 'group' => false],
        'street_number' => ['var_name' => 'street_number', 'source' => 'report1', 'num' => 1, 'group' => false],
        'address_more_desc' => ['var_name' => 'address_more_desc', 'source' => 'report1', 'num' => 1, 'group' => false],
        // object_group_table
        'object_name_type' => ['var_name' => 'object_name_type', 'source' => 'report1', 'num' => null, 'group' => true],
        'object_subtype_name' => ['var_name' => 'object_subtype_name', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'object_name_kind' => ['var_name' => 'object_name_kind', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'object_name_type_id' => ['var_name' => 'object_name_type_id', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'object_name_subtype' => ['var_name' => 'object_name_subtype', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'land_ops' => ['var_name' => 'land_ops', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'level_one_element_id' => ['var_name' => 'level_one_element_id', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'level_two_element_id' => ['var_name' => 'level_two_element_id', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'level_three_element_id' => ['var_name' => 'level_three_element_id', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'market_approach' => ['var_name' => 'market_approach', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'market_approach_id' => ['var_name' => 'market_approach_id', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'revenue_approach' => ['var_name' => 'revenue_approach', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'revenue_approach_id' => ['var_name' => 'revenue_approach_id', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'costly_approach' => ['var_name' => 'costly_approach', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
        'costly_approach_id' => ['var_name' => 'costly_approach_id', 'source' => 'report1', 'num' => 'dc_object_name_type.num', 'group' => true],
    ];
    private static array $currencyRates = [];

    private static function getSearchMap($searchParams = null): array
    {
        if (!isset(self::$searchMap)) {
            self::$searchMap = self::$searchMapStatic;
        };
        if ($searchParams) {
            // fetch varIds for fields from DB, and add them to searchMap
            foreach (self::getVarIds(array_keys($searchParams)) as $key => $value) {
                foreach (self::$searchMap as $searchMapKey => $searchMapValue) {
                    if ($searchMapValue['field_name'] == $key) {
                        self::$searchMap[$searchMapKey]['varId'] = $value;
                        break;
                    }
                }
            }
        }
        return self::$searchMap;
    }

    /**
     * @return array
     */
    private static function getSearchParamsFromInput(): array
    {
        if (isset(self::$searchParams)) {
            return self::$searchParams;
        }
        /** @var Request $request */
        $request = self::getRegistry()->get('request');
        $search = [];
        foreach (self::getSearchMap() as $searchKey => $searchMap) {
            $searchValue = $request->get($searchMap['field_name']);
            if ($searchValue) {
                $search[$searchMap['field_name']] = $searchValue;
            }
        }
        return self::$searchParams = $search;
    }

    private static function getVarIds($search): array
    {
        $filteredSearch = $search;
        $return = [];
        if (self::$fieldVarIds) {
            // filter out the search to find missing fields
            foreach ($search as $varName) {
                if (isset(self::$fieldVarIds[$varName])) {
                    unset($filteredSearch[$varName]);
                    $return[] = self::$fieldVarIds[$varName];
                }
            }

            // If all searched field already have varIds
            if (empty($filteredSearch)) {
                return $return;
            }
        }

        // get missing varIds
        $searchMap = self::getSearchMap();
        $db = self::getDb();
        $tbl = DB_TABLE_FIELDS_META;
        foreach ($filteredSearch as $varName) {
            foreach ($searchMap as $value) {
                if ($value['field_name'] == $varName) {
                    $sField = $value;
                    break;
                }
            }
            // skip basic fields
            if ($sField['var_type'] == 'basic') {
                continue;
            }
            $modelid = implode(",", self::$modelTypes[$sField['document_type']]);
            $realVarName = $varName;
            if ($varName === 'award_date_from' || $varName === 'award_date_to') {
                $realVarName = 'award_date';
            }
            $fields = $db->execute("SELECT id, name FROM `{$tbl}` WHERE `name` = '{$realVarName}' AND `model_type` IN ({$modelid})")->getAssoc();
            if (empty($fields)) {
                throw new Exception("Field {$varName} not found in DB");
            }
            self::$fieldVarIds[$varName] = array_keys($fields);
            $return[$varName] = self::$fieldVarIds[$varName];
        }
        // return only requested fields
        return $return;
    }

    public static function buildQuery(&$registry, $filters = array())
    {
        self::setRegistry($registry);

        $page = $registry['request']->get('page')?:0;
        $display = $registry['request']->get('display')?:(defined('REPORT_PER_PAGE') ? REPORT_PER_PAGE : 10);

        $search = self::getSearchParamsFromInput();
        $identityFilters = array_intersect_key($search, array_flip(['identity_type', 'object_identifier', 'object_id']));

        $includeArchivedRecords = defined('FORCE_INCLUDE_ARCHIVED') && FORCE_INCLUDE_ARCHIVED;
        $includeArchivedRecords = $registry['request']->get('include_archived') || $includeArchivedRecords;
        if (empty($identityFilters)) {
            // Paging is done when searching for reports
            $reportIdsList = self::searchReports($search, $display, $page, (bool) $includeArchivedRecords);
            $foundRows = self::getLastFoundRows();
        } else {
            $reportIdsFromIdentity = self::fetchReportsFromIdentifier($identityFilters, $search, $includeArchivedRecords);
            // No paging for exact matches
            $reportIdsList = $reportIdsFromIdentity;
            $foundRows = count($reportIdsList);
        }

        // No results found, short-circuit
        if (empty($reportIdsList)) {
            return [[], 0];
        }

        // Fetch the data to be shown in the report for the found Ids
        $results = self::prepResults(self::fetchReportData($reportIdsList, $includeArchivedRecords), $registry['module_param']);
        $target_report = [
            'type' => 'autocomplete',
            'name' => 'target_report_name',
            'label' => 'reports_target_report_label',
        ];
        $target_report['autocomplete'] = [
            'url' => sprintf('%s?%s=%s&%s=%s', $_SERVER['PHP_SELF'], $registry['module_param'], 'documents', 'documents', 'ajax_select'),
            'min_chars' => 3,
            'suggestions' => '[<full_num>] <name>',
            'type' => 'documents',
            'fill_options' => [
                '$target_report => <id>',
                '$target_report_name => [<full_num>] <name>',
            ],
            'filters' => ['<type>' => '13'],
            'clear' => 1,
        ];
        $results['additional_options'] = ['target_report' => $target_report];
        return [$results, $foundRows];
    }

    /**
     * Generate Join for *_cstm table
     * @param string $table
     * @param string $alias
     * @param string|null $modelId
     * @param string|int|array|null $varId
     * @param string|null $value
     * @param string|null $lang
     * @param int|null $num
     * @return string
     */
    private static function generateJoinCstm(string $table,
                                             string $alias,
                                             string $modelId = null,
                                                    $varId = null,
                                                    $value = null,
                                             string $lang = null,
                                                    $num = null
    ): string
    {
        $ons = [];

        if ($modelId !== null) {
            $ons[] = "`{$alias}`.model_id=$modelId";
        }

        if ($varId !== null) {
            if (is_array($varId)) {
                $varIdsStr = implode(',', $varId);
                $ons[] = "`{$alias}`.var_id in ({$varIdsStr})";
            } else {
                $ons[] = "`{$alias}`.var_id = {$varId}";
            }
        }

        if ($lang !== null) {
            $ons[] = "`{$alias}`.lang='{$lang}'";
        }

        if ($num !== null) {
            if (is_numeric($num)) {
                $num = "'{$num}'";
            }
            $ons[] = "`{$alias}`.num={$num}";
        }

        if ($value !== null) {
            if (is_array($value)) {
                $value = implode(',', $value);
                $value = " IN ({$value})";
            } elseif (substr($value, 0, 2) == 'IN') {
                $value = ' ' . $value;
            } elseif (!in_array(substr($value, 0, 1), ['=','<','>'])) {
                $value = '=' . $value;
            }

            $ons[] = "`{$alias}`.value{$value}";
        }

        $onStr = implode(' AND ', $ons);

        return "INNER JOIN `{$table}` AS `{$alias}`\n" . ($onStr ? "\tON ({$onStr})\n" : '');
    }

    /**
     * @param string $varName
     * @param string $modelType
     * @param string|null $modelId
     * @param string|int|null $num
     * @return string
     */
    private static function generateJoinDocumentCstmVar(
        string $varName,
        string $modelType,
        string $modelId = null,
        $num = null,
        $isArchive = false
    ): string
    {
        $ons = [];
        $table = ($isArchive ? 'archive_' : '') . DB_TABLE_DOCUMENTS_CSTM;
        $alias = "dc_{$varName}";

        $onStr = self::generateCstmConditions($alias, $varName, $modelId, $modelType, $num);
        return "LEFT JOIN `{$table}` AS `{$alias}`\n" . ($onStr ? "\tON ({$onStr})\n" : '');
    }

    private static function generateSubqueryDocumentCstmVar(
        string $varName,
        string $modelType,
        string $modelId = null,
        int    $num = null,
        bool $isArchive = false
    ): string
    {
        $table = ($isArchive ? 'archive_' : '') . DB_TABLE_DOCUMENTS_CSTM;
        $alias = "dc_{$varName}";
        $onStr = self::generateCstmConditions($alias, $varName, $modelId, $modelType, $num);
        $whereStr = $onStr ? "\tWHERE {$onStr}\n" : '';
        return <<<SQL
            (SELECT GROUP_CONCAT(`{$alias}`.value)  FROM `{$table}` AS `{$alias}`\n
            {$whereStr}\tGROUP BY $modelId) as `{$varName}`
            SQL;
    }

    /**
     * @param string $identifier
     * @param string $modelType
     * @return array
     */
    public static function searchDocumentByPropertyIdentifier(string $identifier, string $modelType, $varName, $search, bool $includeArchivedRecords): array
    {
        $alvisReportType = 13;
        $propertyModelTypes = implode(',', self::$modelTypes[$modelType]);
        $report_validSubstatuses = implode(',', self::$reportValidation['substatuses']);

        list($awardDateJoin, $awardDatejoinData) = self::getAwardDateJoin($search);
        $sql = self::getSqlSearchByIdentifier($propertyModelTypes, $varName, $awardDateJoin, $alvisReportType, $report_validSubstatuses);

        $data = [$identifier, ...$awardDatejoinData];

        if ($includeArchivedRecords) {
            $sql .= "\n UNION ALL \n";
            $sql .= self::getSqlSearchByIdentifier(
                $propertyModelTypes,
                $varName,
                $awardDateJoin,
                $alvisReportType,
                $report_validSubstatuses,
                true);
            $data = array_merge($data, $data);
        }

        $db = self::getDb();
        $stmt = $db->prepare($sql);
        $documentsData = $db->getAll($stmt, $data);

        return array_column($documentsData, 'id');
    }

    public static function setRegistry(Registry $registry)
    {
        self::$registry = $registry;
    }

    public static function getRegistry(): Registry
    {
        return self::$registry;
    }

    public static function getDb(): ADODB_mysqli
    {
        if (!isset(self::$db)) {
            self::$db = self::getRegistry()->get('db');
        }
        return self::$db;
    }

    /**
     * @return array
     */
    private static function fetchReportsFromIdentifier(array $search, $fullSearch, bool $includeArchivedRecords): array
    {
        $searchMap = self::getSearchMap($search);

        $reportsFromIdentity = [];
        foreach ($search as $searchKey => $searchvalue) {
            if (empty($searchvalue)) {
                continue;
            }
            foreach ($searchMap as $sField) {
                if ($searchKey === $sField['field_name'] && in_array($sField['document_type'], ['land', 'level3', 'level2'])) {
                    $reportsFromIdentity[] = self::searchDocumentByPropertyIdentifier(
                        $searchvalue,
                        $sField['document_type'],
                        $sField['field_name'],
                        $fullSearch,
                        $includeArchivedRecords);
                }
            }
        }

        return array_merge([], ...$reportsFromIdentity);
    }

    private static function getSearchReportsSelect($search, bool $isArchive=false): array
    {
        $alvisReportType = 13;

        $tblPrefix = $isArchive ? 'archive_' : '';
        $tbl = [
            'd' => $tblPrefix . DB_TABLE_DOCUMENTS,
            'dc' => $tblPrefix . DB_TABLE_DOCUMENTS_CSTM,
            'fm' => DB_TABLE_FIELDS_META,
        ];

        $searchMap = self::getSearchMap($search);

        $joins = [];
        $joinsData = [];
        $wheres = [];
        $wheresData = [];
        $mainReportType = DOCUMENT_REPORT;
        $joins[] = self::generateJoinCstm(
            $tbl['dc'],
            'dc_created_from_document_id',
            null,
            "(SELECT id FROM `{$tbl['fm']}` WHERE `name`='created_from_document_id' and model_type='{$alvisReportType}')",
            'd.id',
            '',
            '1');
        $joins[] = "INNER JOIN `{$tbl['d']}` AS `d13`
                   ON (dc_created_from_document_id.model_id=d13.id)";

        list($awardDateJoin, $awardDatejoinData) = self::getAwardDateJoin($search, $isArchive);
        $joins[] = $awardDateJoin;
        $joinsData = array_merge($joinsData, $awardDatejoinData);
        /** @var User $currentUser */
        $currentUser = self::getRegistry()->get('currentUser');
        $currentEmployeeId = $currentUser->get('employee');

        $unlimitedRoles = self::getUnlimitedRolesSetting();
        $currentUserRole = self::getUserRealRole($currentUser);
        if (!in_array($currentUserRole, $unlimitedRoles)) {
            $allowedReportRoles = self::getLimitedRoleSetting($currentUser);
            if (empty($allowedReportRoles)) {
                $allowedReportRoles = [2];
            }
            $joins[] = self::generateJoinCstm(
                $tbl['dc'],
                "dc_recording_role_id",
                'd.id',
                "(SELECT id FROM `{$tbl['fm']}` WHERE `model` = 'Document' AND `name`='recording_role_id' and model_type='{$mainReportType}')",
                $currentEmployeeId,
                '',
                null);
            $joins[] = self::generateJoinCstm(
                $tbl['dc'],
                "dc_recording_role_type",
                'd.id',
                "(SELECT id FROM `{$tbl['fm']}` WHERE `model` = 'Document' AND `name`='recording_role_type' and model_type='{$mainReportType}')",
                $allowedReportRoles,
                '',
                'dc_recording_role_id.num');
        }
        $searchFlag = false;
        foreach ($search as $searchKey => $searchvalue) {
            foreach ($searchMap as $value) {
                if ($value['field_name'] !== $searchKey) {
                    continue;
                }
                $sField = $value;

                if ($sField['var_type'] === 'basic') {
                    $wheres[] = "d.{$searchKey}=?";
                    $wheresData[] = $searchvalue;
                    $searchFlag = true;
                } elseif ($searchKey === 'award_date_from' || $searchKey === 'award_date_to') {
                    $searchFlag = true;
                    // skip award_date_from and award_date_to fields as they are already handled above
                } elseif ($sField['document_type'] === 'report') {
                    $num = $sField['group'] ? '1' : null;
                    $joins[] = self::generateJoinCstm(
                        $tbl['dc'],
                        "dc_{$searchKey}",
                        'd.id',
                        $sField['varId'],
                        '?',
                        '',
                        $num);
                    $joinsData[] = $searchvalue;
                    $searchFlag = true;
                } elseif ($sField['document_type'] === 'report13') {
                    $num = $sField['group'] ? '1' : null;
                    $joins[] = self::generateJoinCstm(
                        $tbl['dc'],
                        "dc_{$searchKey}",
                        'd13.id',
                        $sField['varId'],
                        '?',
                        '',
                        $num);
                    $joinsData[] = $searchvalue;
                    $searchFlag = true;
                }
            }
        }

        if (!$searchFlag) {
            return [];
        }

        $joinstr = implode("\n", $joins);
        $data = $joinsData;
        $data[] = DOCUMENT_REPORT;
        $wherestr = implode(" AND ", $wheres);
        if (!empty($wherestr)) {
            $wherestr = ' AND ' . $wherestr;
            $data = array_merge($data, $wheresData);
        }
        $options = $isArchive ? '':' SQL_CALC_FOUND_ROWS';
        $substatuses = implode(', ', self::$reportValidation['substatuses']);
        $sql_reports = <<<SQL
            SELECT{$options}  `d13`.id as id, dc_award_date.value as award_date
                FROM `{$tbl['d']}` AS `d`
                {$joinstr}
                WHERE d.type=? AND d.active=1 AND d.deleted_by=0 AND d.substatus IN ({$substatuses}){$wherestr}
            SQL;

        return ['sql' => $sql_reports, 'data' => $data];
    }

    /**
     * @param $search
     * @param $display
     * @param $page
     * @param bool $includeArchivedRecords
     * @return array
     */
    private static function searchReports($search, $display, $page, bool $includeArchivedRecords=false): array
    {
        $select = self::getSearchReportsSelect($search, false);
        if(empty($select['sql'])){
            return [];
        }
        $sql_reports = $select['sql'];
        $data = $select['data'];

        if ($includeArchivedRecords) {
            $archiveSelect = self::getSearchReportsSelect($search, $includeArchivedRecords);
            $sql_reports .= "\nUNION ALL ({$archiveSelect['sql']})";
            $data = array_merge($data, $archiveSelect['data']);
        }

        $lCount = $display;
        $lStart = $page * $lCount;

        $sql_reports .= "\nORDER BY award_date DESC";
        $sql_reports .= "\nLIMIT {$lStart}, {$lCount}";

        $db = self::getDb();
        $reportStatement = $db->prepare($sql_reports);
        $reports_list = $db->execute($reportStatement, $data)->getAll();
        return array_column($reports_list, 'id');
    }

    /**
     * @param string $alias
     * @param string $varName
     * @param string $modelId
     * @param string $modelType
     * @param int|string|null $num
     * @return array
     */
    private static function generateCstmConditions(string $alias, string $varName, ?string $modelId, string $modelType, $num = null): string
    {
        $tblFieldsmeta = DB_TABLE_FIELDS_META;

        $ons = [];
        if ($modelId !== null) {
            $ons[] = "`{$alias}`.model_id=$modelId";
        }
        $ons[] = "`{$alias}`.var_id IN (SELECT id FROM `{$tblFieldsmeta}` WHERE name = '{$varName}' and model_type={$modelType})";

        $ons[] = "`{$alias}`.lang=''";

        if ($num !== null) {
            $ons[] = "`{$alias}`.num={$num}";
        }

        $onStr = implode(' AND ', $ons);
        return $onStr;
    }

    private static function getFetchReportDataSelect(array $reportIdsList, bool $isArchive = false): string
    {
        $tblPrefix = $isArchive?'archive_':'';
        $tbl = [
            'documents' => $tblPrefix . DB_TABLE_DOCUMENTS,
            'documents_cstm' => $tblPrefix . DB_TABLE_DOCUMENTS_CSTM,
            'files' => $tblPrefix . DB_TABLE_FILES,
            'bb' => $tblPrefix . DB_TABLE_BB,
        ];

        $reportIdsStr = implode(',', $reportIdsList);

        $JOINS = [];
        $fields = [];
        # Report PDF
        $fields[] = "files_last_report.id as last_report_id";
        $fields[] = "files_last_report.filename as last_report_filename";
        $fields[] = "files_last_report.path as last_report_filepath";
        $JOINS[] = self::generateJoinDocumentCstmVar('last_report', 2, '`report`.id', 1, $isArchive);
        $JOINS[] = <<<SQL
            LEFT JOIN `{$tbl['files']}` as `files_last_report`
                   ON `files_last_report`.`id` =  dc_last_report.value
            SQL;

        $groups = [
            '`report1`.`id`',
            '`report`.`id`',
            '`files_last_report`.`id`'
        ];

        $varsMap = self::$reportDatavarMap;
        foreach ($varsMap as $varName => $varData) {
            $tblName = "`dc_{$varName}`";
            $fieldNameSrc = "{$tblName}.`value`";
            $groups[] = "{$tblName}.`var_id`";
            $fields[] = ($varData['group'] ? "GROUP_CONCAT({$fieldNameSrc})" : $fieldNameSrc) . " as `{$varName}`";
            $JOINS[] = self::generateJoinDocumentCstmVar($varName, "`{$varData['source']}`.type", "`{$varData['source']}`.id", $varData['num'], $isArchive);
        }

        $fields[] = self::generateSubqueryDocumentCstmVar('document_relat_type', '`report1`.type', '`report1`.id', $isArchive);
        $fields[] = self::generateSubqueryDocumentCstmVar('document_relat_name', '`report1`.type', '`report1`.id', $isArchive);
        $fields[] = self::generateSubqueryDocumentCstmVar('document_relat_id', '`report1`.type', '`report1`.id', $isArchive);
        $fields[] = self::generateSubqueryDocumentCstmVar('document_relat_active', '`report1`.type', '`report1`.id', $isArchive);

        $fieldsStr = implode(",\n       ", $fields);
        $joinsStr = implode("\n", $JOINS);
        $groupsStr = implode(", ", $groups);

        $sql = <<<SQL
            SELECT report.id as report_id,
                   report1.id as report1_id,
                   report.full_num as full_num,
                   {$fieldsStr},
                   (SELECT GROUP_CONCAT(`bb`.params SEPARATOR '~~~SEPARATOR~~~')  FROM `{$tbl['bb']}` AS `bb` WHERE bb.model = 'Document' AND bb.model_type = report.type AND bb.model_id = report.id GROUP BY bb.model_id) as bb_params
            FROM `{$tbl['documents']}` as `report1`
            INNER JOIN `{$tbl['documents_cstm']}` as `dc_created_from` ON `dc_created_from`.`var_id` = 1413 AND `report1`.id = `dc_created_from`.model_id AND dc_created_from.`lang`='' AND dc_created_from.`num`=1
            LEFT JOIN `{$tbl['documents']}` as `report` ON `report`.`id` = `dc_created_from`.`value`
            {$joinsStr}
            WHERE `report1`.`id` IN ({$reportIdsStr})
            GROUP BY {$groupsStr}
            SQL;

        return $sql;
    }

    /**
     * @param array $reportIdsList
     * @param bool $includeArchivedRecords
     * @return array
     */
    private static function fetchReportData(array $reportIdsList, bool $includeArchivedRecords = false): array
    {
        $sql = self::getFetchReportDataSelect($reportIdsList, false);

        if ($includeArchivedRecords) {
            $archiveSql = self::getFetchReportDataSelect($reportIdsList, true);
            $sql .= "\nUNION ALL ($archiveSql)";
        }

        $sql .= "\nORDER BY `order_date` DESC";

        $results = self::getDb()->execute($sql)->getAll();

        return $results;
    }

    private static function getLastFoundRows()
    {
        $db = self::getDb();
        $sql_pagination = <<<SQL
            SELECT FOUND_ROWS() as total
            SQL;
        $paginationStatement = $db->prepare($sql_pagination);
        $pagination = $db->execute($paginationStatement)->getAll();
        return $pagination[0]['total']??0;
    }

    /**
     * @param array $search
     * @return array
     */
    private static function getAwardDateJoin(array $search, $isArchive = false): array
    {
        // Fake search, to force getting the varIds of the award_date
        $searchMap = self::getSearchMap([
            'award_date_from' => '',
            'award_date_to' => ''
        ]);
        $award_date_field = null;
        foreach ($searchMap as $value) {
            if ($value['field_name'] === 'award_date_from') {
                $award_date_field = $value;
                break;
            }
        }
        $joinsData = [];
        if (isset($search['award_date_from']) && isset($search['award_date_to'])) {
            $awardDateValue = ">=? AND `dc_award_date`.`value`<=?";
            $joinsData[] = $search['award_date_from'];
            $joinsData[] = $search['award_date_to'];
        } elseif (isset($search['award_date_from'])) {
            $awardDateValue = '>=?';
            $joinsData[] = $search['award_date_from'];
        } elseif (isset($search['award_date_to'])) {
            $awardDateValue = '<=?';
            $joinsData[] = $search['award_date_to'];
        } else {
            // No award_date range filter, but JOIN it in the SELECT for the results
            $awardDateValue = null;
            $joinsData = [];
        }


        $join = self::generateJoinCstm(
            ($isArchive ? 'archive_' : '') . DB_TABLE_DOCUMENTS_CSTM,
            "dc_award_date",
            'd.id',
            $award_date_field['varId'],
            $awardDateValue,
            '',
            1);
        return [$join, $joinsData];
    }

    /** Givven e bb params array it returns the same array with normalized keys
     * (with no thirteen, fourteen, etc)
     * @param $params
     * @return void
     */
    private static function normalizeBbKeys(array $params): array
    {
        $removeSubstrings = static function ($key) {
            return str_replace(self::$bbVariantKeys, '', $key);
        };
        $keys = array_keys($params);
        $normalizedKeys = array_map($removeSubstrings, array_keys($params));
        return array_combine($normalizedKeys, array_values($params));
    }

    /** Given an array of BB params it returns an array of BB params indexed by alvis_id
     * and with normalized inner array keys
     * @param $BbParams
     * @return array
     */
    private static function prepNormalizedBBsByAlvisId($BbParams): array
    {
        $objectBb = [];
        foreach ($BbParams as $vbb) {
            if (empty($vbb)) {
                continue;
            }
            $params = self::normalizeBbKeys(unserialize($vbb));
            $params['market_value_eur'] = self::convertCurrency($params['market_value'], 'BGN', 'EUR', 8);
            $params['price_square_meter_eur'] = self::convertCurrency($params['price_square_meter'], 'BGN', 'EUR', 8);
            $objectBb[$params['alvis_id']] = $params;
        }
        return $objectBb;
    }

    private static function getCurrencyRate(string $fromCurrency, string $toCurrency): float
    {
        $rateKey = "{$fromCurrency}_{$toCurrency}";
        if (isset(self::$currencyRates[$rateKey])) {
            return self::$currencyRates[$rateKey];
        }

        $registry = self::getRegistry();
        $rate = Finance_Currencies::getRate($registry, $fromCurrency, $toCurrency);
        return self::$currencyRates[$rateKey] = $rate;
    }

    private static function convertCurrency($value, $from, $to, $acuracy = 8): float
    {
        $rate = self::getCurrencyRate($from, $to);
        return (float) bcmul($rate, $value, $acuracy);
    }

    /** Given a data for a single row, it parses and processes the data for showing.
     * Returns the processed array
     * @param $data
     * @return array
     */
    private static function prepObjectsInSingleResult($data): array
    {
        $objectNameType = explode(',', $data['object_name_type']);
        $objectNameKind = explode(',', $data['object_name_kind']);
        $objectSubtypeName = explode(',', $data['object_subtype_name']);
        $objectNameTypeId = explode(',', $data['object_name_type_id']);
        $objectNameSubtype = explode(',', $data['object_name_subtype']);
        $objectLevelTreeId = explode(',', $data['level_three_element_id']);
        $BbParams = explode('~~~SEPARATOR~~~', $data['bb_params'] ?? '');

        $objectBb = self::prepNormalizedBBsByAlvisId($BbParams);
        $objectsList = [];
        foreach ($objectNameType as $kObj => $vObj) {
            $objectsList[] = [
                'object_name_type' => $vObj,
                'object_name_kind' => $objectNameKind[$kObj],
                'object_subtype_name' => $objectSubtypeName[$kObj],
                'object_name_type_id' => $objectNameTypeId[$kObj],
                'object_name_subtype' => $objectNameSubtype[$kObj],
                'object_bb_params' => $objectBb[$objectLevelTreeId[$kObj]] ?? null,
            ];
        }
        return $objectsList;
    }

    /** Giwen an array from the query it parses all results and prepairs them for showing.
     * It returns the array with the processed results.
     * @param array $results
     * @param $module_param
     * @return array
     */
    private static function prepResults(array $results, $module_param): array
    {
        foreach ($results as $k => $v) {
            $results[$k]['last_report_not_exist'] = !is_file($v['last_report_filepath']);
            $results[$k]['last_report_url'] = $results[$k]['last_report_not_exist']
                ? null
                : 'http' . (empty($_SERVER['HTTPS']) ? '' : 's') . "://{$_SERVER['HTTP_HOST']}{$_SERVER['PHP_SELF']}?{$module_param}=documents&documents=viewfile&viewfile={$v['report_id']}&file={$v['last_report_id']}";
            $results[$k]['objects'] = self::prepObjectsInSingleResult($v);
            unset($results[$k]['bb_params']);
        }
        return $results;
    }

    /**
     * @param User $currentUser
     * @return array|string[]
     */
    private static function getLimitedRoleSetting(User $currentUser): array
    {
        $currentUserRole = self::getUserRealRole($currentUser);
        $limitedRoleSettingName = "ROLES_LIMITED_{$currentUserRole}";

        return defined($limitedRoleSettingName) ? preg_split('/\s*,\s*/', constant($limitedRoleSettingName)) : [];

    }

    /**
     * @return array|false|string[]
     */
    private static function getUnlimitedRolesSetting()
    {
        $unlimitedRoles = preg_split('/\s*,\s*/', defined('ROLES_WITH_NO_LIMITATIONS') ? ROLES_WITH_NO_LIMITATIONS : '');
        return $unlimitedRoles;
    }

    /**
     * @param User $currentUser
     * @return false|mixed
     */
    private static function getUserRealRole(User $currentUser)
    {
        $currentUserRole = $currentUser->get('real_role');
        if (!$currentUserRole) {
            $currentUserRole = $currentUser->get('role');
        }
        return $currentUserRole;
    }

    /**
     * @param string $propertyModelTypes
     * @param $varName
     * @param $awardDateJoin
     * @param int $alvisReportType
     * @param string $report_validSubstatuses
     * @return string
     */
    private static function getSqlSearchByIdentifier(string $propertyModelTypes, $varName, $awardDateJoin, int $alvisReportType, string $report_validSubstatuses,bool $archivedRecords=false): string
    {
        $tblPrefix = $archivedRecords ? 'archive_' : '';
        $tbl = [
            'd' => $tblPrefix . DB_TABLE_DOCUMENTS,
            'dc' => $tblPrefix . DB_TABLE_DOCUMENTS_CSTM,
            'fm' => DB_TABLE_FIELDS_META,
        ];

        if ($archivedRecords) {
            $awardDateJoin = str_replace("`" . DB_TABLE_DOCUMENTS . "`", "`{$tbl['d']}`", $awardDateJoin);
            $awardDateJoin = str_replace("`" . DB_TABLE_DOCUMENTS . "`.", "`{$tbl['d']}`.", $awardDateJoin);
            $awardDateJoin = str_replace("`" . DB_TABLE_DOCUMENTS_CSTM . "`", "`{$tbl['dc']}`", $awardDateJoin);
            $awardDateJoin = str_replace("`" . DB_TABLE_DOCUMENTS_CSTM . "`.", "`{$tbl['dc']}`.", $awardDateJoin);
        }

        $sql = <<<SQL
        SELECT dc_report_name_id.value as id
            FROM `{$tbl['d']}` AS report1
            INNER JOIN `{$tbl['dc']}` AS `dc_object_identifier`
              ON report1.id = `dc_object_identifier`.`model_id`
                AND report1.`type` IN ({$propertyModelTypes})
                AND report1.active = '1'
                AND `dc_object_identifier`.var_id IN (SELECT id FROM `{$tbl['fm']}` WHERE name = '$varName' and model_type IN ({$propertyModelTypes}))
                AND `dc_object_identifier`.value=?
                AND `dc_object_identifier`.lang=''
                AND `dc_object_identifier`.num = '1'
            INNER JOIN `{$tbl['dc']}` AS dc_report_name_id
              ON dc_report_name_id.model_id = `dc_object_identifier`.`model_id`
                AND dc_report_name_id.var_id IN (SELECT id FROM `{$tbl['fm']}` WHERE name = 'report_name_id' and model_type IN ({$propertyModelTypes}))
                AND dc_report_name_id.lang=''
                AND dc_report_name_id.num = '1'
            INNER JOIN `{$tbl['dc']}` as `dc_created_from`
              ON `dc_created_from`.`var_id` = 1413
                AND `dc_report_name_id`.value = `dc_created_from`.model_id
                AND dc_created_from.`lang`=''
                AND dc_created_from.`num`=1
            INNER JOIN `{$tbl['d']}` as `d`
              ON `d`.`id` = `dc_created_from`.`value`
            {$awardDateJoin}

            INNER JOIN `{$tbl['dc']}` AS `dc_report_2`
              ON `dc_report_2`.model_id = dc_report_name_id.value
                AND `dc_report_2`.var_id = (SELECT id FROM `{$tbl['fm']}` WHERE name = 'created_from_document_id' and model_type={$alvisReportType})
                AND `dc_report_2`.lang=''
                AND `dc_report_2`.num = '1'
            INNER JOIN `{$tbl['d']}` AS `report_2`
              ON `report_2`.id = `dc_report_2`.value
                AND `report_2`.`active` = '1'
                AND `report_2`.substatus IN ({$report_validSubstatuses})
        SQL;
        return $sql;
    }
}
?>
