<?php

/**
 * Turnover_Comparison common report
 *
 * @see Sales
 */
class Turnover_Comparison extends Reports {

    /**
     * Selected currency for report results
     * @var string
     */
    private static $currency = '';

    /**
     * Conversion rates into selected report currency
     * @var array
     */
    private static $conversion_rates = array();

    /**
     * Gets the settings from the reports table and applies report-specific processing
     *
     * @param Registry $registry - the main registry
     * @param string $report_name - if not specified, report name is taken from class name
     * @return array - parsed and processed report settings
     */
    public static function getReportSettings(&$registry, $report_name = '') {

        $settings = parent::getReportSettings($registry, $report_name ?: strtolower(get_class()));

        // additional processing:

        /* start types processing */

        // groups of financial types
        $settings['fir_types_groups'] = array('invoices', 'reasons');

        // additional filtering of financial types
        foreach ($settings['fir_types_groups'] as $types_group) {
            $setting_key = "fir_types_{$types_group}";
            if (isset($settings[$setting_key])) {
                if ($settings[$setting_key] == 'all' || $settings[$setting_key] === '') {
                    unset($settings[$setting_key]);
                } elseif ($settings[$setting_key] == 'none' || $settings[$setting_key] === '0') {
                    $settings[$setting_key] = array('0');
                } else {
                    $settings[$setting_key] = preg_split('#\s*,\s*#', $settings[$setting_key]);
                }
            }
        }

        $fir_types_invoices = array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE);

        // contains all included system types for invoices search
        $settings['fir_types_invoices'] = isset($settings['fir_types_invoices']) ? array_values(array_intersect($fir_types_invoices, $settings['fir_types_invoices'])) : $fir_types_invoices;

        $fdt_filters = array(
            'where' => array(
                "fdt.model = 'Finance_Incomes_Reason'",
                'fdt.id > ' . PH_FINANCE_TYPE_MAX,
                'fdt.active = 1',
            ),
        );
        $fir_types_reasons = Finance_Documents_Types::getIds($registry, $fdt_filters);

        // contains all included user-defined types for reasons search
        $settings['fir_types_reasons'] = isset($settings['fir_types_reasons']) ? array_values(array_intersect($fir_types_reasons, $settings['fir_types_reasons'])) : $fir_types_reasons;

        // default group of types to search by
        // value will be 'invoices' unless search by invoices is off and search by reasons in on
        $settings['default_fir_types'] = 'invoices';
        foreach ($settings['fir_types_groups'] as $types_group) {
            if (!empty($settings["fir_types_{$types_group}"])) {
                $settings['default_fir_types'] = $types_group;
                break;
            }
        }

        /* end types processing */

        $filter_definitions = array(
            'fir_types' => array(
                'required' => true,
            ),
            'company' => array(
            ),
            'office' => array(
            ),
            'employee' => array(
                'allowed_multiple' => true,
            ),
            'department' => array(
                'allowed_multiple' => true,
            ),
            'article' => array(
                'allowed_multiple' => true,
                'default_multiple' => true,
                'group_by' => true,
            ),
            'article_category' => array(
                'allowed_multiple' => true,
                'group_by' => true,
            ),
            'customer' => array(
                'allowed_multiple' => true,
                'default_multiple' => true,
                'group_by' => true,
            ),
            'trademark' => array(
                'allowed_multiple' => true,
                'group_by' => true,
            ),
            'tag' => array(
                'allowed_multiple' => true,
                'default_multiple' => true,
            ),
            'currency' => array(
                'required' => true,
            ),
        );

        // enabled optional filters
        $settings['enabled_filters'] =
            isset($settings['enabled_filters']) && $settings['enabled_filters'] != 'all' ?
            preg_split('#\s*,\s*#', $settings['enabled_filters']) :
            array_keys($filter_definitions);

        // multiple filters (displayed as a grouping table, not as a single field)
        $settings['allowed_multiple_filters'] =
            array_keys(array_filter($filter_definitions, function($a) { return !empty($a['allowed_multiple']); }));
        $settings['multiple_filters'] =
            isset($settings['multiple_filters']) ?
            array_intersect(
                preg_split('#\s*,\s*#', $settings['multiple_filters']),
                $settings['allowed_multiple_filters']
            ) :
            array_keys(array_filter($filter_definitions, function($a) { return !empty($a['default_multiple']); }));

        // grouping filters
        $settings['grouping_filters'] = array_keys(array_filter($filter_definitions, function($a) { return !empty($a['group_by']); }));

        // default period duration for period from-to filters (0 = disabled auto-selection of period)
        $settings['period_default_num_months'] = isset($settings['period_default_num_months']) ? intval($settings['period_default_num_months']) : 0;

        // employee customer type
        $settings['employee_types'] = strval(PH_CUSTOMER_EMPLOYEE);

        // required filters
        $settings['required_filters'] = array('period_from', 'period_to', 'period2_from', 'period2_to', 'fir_types');

        return $settings;
    }

    /**
     * Performs query with specified filter values and returns found results
     *
     * @param Registry $registry - the main registry
     * @param array $filters - values of report filters to search with
     * @return mixed[]|[][] - found results with/without pagination
     */
    public static function buildQuery(Registry &$registry, array $filters = array()) {
        // set the model lang filter
        $model_lang = (!empty($filters['model_lang']) ? $filters['model_lang'] : $registry['lang']);

        // get the database object
        $db = &$registry['db'];

        // prepare the array for the final results
        $final_results = array();

        // get the report settings in array var (for easier use)
        $settings = self::getReportSettings($registry);

        if (empty($filters['fir_types']) || !in_array($filters['fir_types'], $settings['fir_types_groups'])) {
            $filters['fir_types'] = $settings['default_fir_types'];
        }
        $tz = new DateTimeZone('UTC');

        // check required filters
        if (array_diff_key(array_flip($settings['required_filters']), array_filter($filters))) {
            $registry['messages']->setError(
                $registry['translater']->translate('error_reports_required_filters'),
                implode(
                    ', ',
                    array_keys(
                        array_diff_key(
                            array_flip($settings['required_filters']),
                            array_filter($filters)
                        )
                    )
                )
            );
            return self::exitReport($registry, $filters, $final_results, false);
        } elseif ($filters['period_to'] < $filters['period_from'] || $filters['period2_to'] < $filters['period2_from']) {
            // end of periods should not be before their start
            if ($filters['period_to'] < $filters['period_from']) {
                $registry['messages']->setError(
                    $registry['translater']->translate('error_reports_invalid_period'),
                    'period_from'
                );
            }
            if ($filters['period2_to'] < $filters['period2_from']) {
                $registry['messages']->setError(
                    $registry['translater']->translate('error_reports_invalid_period'),
                    'period2_from'
                );
            }
            return self::exitReport($registry, $filters, $final_results, false);
        } elseif (
            // the number of years and months in the periods should be the same
            date_diff(date_add(date_create($filters['period_to'], $tz), new DateInterval('P1D')), date_create($filters['period_from'], $tz))->format('%y-%m') !=
            date_diff(date_add(date_create($filters['period2_to'], $tz), new DateInterval('P1D')), date_create($filters['period2_from'], $tz))->format('%y-%m')
        ) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_equal_periods'));
            return self::exitReport($registry, $filters, $final_results, false);
        }

        // process and sanitize filters to make their use easier
        foreach ($filters as $name => $value) {
            if (in_array($name, $settings['allowed_multiple_filters'])) {
                $filters[$name] = array_filter(array_unique(filter_var(
                    $filters[$name],
                    FILTER_VALIDATE_INT,
                    array(
                        'flags' => FILTER_FORCE_ARRAY,
                        'options' => array(
                            'default' => 0,
                            'min_range' => 1,
                        ),
                    ))));
            }
        }
        if (!empty($filters['currency'])) {
            self::$currency = $filters['currency'];
        }

        // apply filters
        $where = array(
            "`fir`.`annulled_by`     = '0'",
            "`fir`.`active`          = '1'",
            "`fir`.`status`          = 'finished'",
        );

        // process types group filter and matching types
        $types = array();
        if (!empty($filters['fir_types']) && !empty($settings["fir_types_{$filters['fir_types']}"])) {
            if (empty($filters[$filters['fir_types']])) {
                // all possible types from settings
                $types = $settings["fir_types_{$filters['fir_types']}"];
            } else {
                // intersect selected with possible types
                $types = array_intersect($filters[$filters['fir_types']], $settings["fir_types_{$filters['fir_types']}"]);
            }
        }
        if (empty($types)) {
            $types = array(0);
        }
        $where[] = 'fir.type IN (' . implode(', ', $types) . ')';

        // apply permissions
        foreach (array('company', 'office') as $name) {
            $allowed_values = $registry['currentUser']->get('finance_' . General::singular2plural($name)) ?: array(0);
            $filters[$name] = empty($filters[$name]) ? $allowed_values : array_intersect($filters[$name], $allowed_values);
        }
        // company, office, employee, department, customer
        foreach (array('company', 'office', 'employee', 'department', 'customer') as $name) {
            if (!empty($filters[$name])) {
                $where[] = "fir.{$name} IN (" . implode(', ', $filters[$name]) . ")";
            }
        }

        // Filter: first period
        $where['period_from'] = "'{$filters['period_from']}' <= `fir`.`issue_date`";
        $where['period_to'] = "`fir`.`issue_date` <= '{$filters['period_to']}'";

        // define additional grouping+ordering of results
        $group_by = $order_by = array();
        if (!empty($filters['customer']) || !empty($filters['trademark'])) {
            $group_by['customer'] = '`fir`.`customer`';
            $order_by['customer'] = 'customer_name ASC';
        }
        if (!empty($filters['article']) || !empty($filters['article_category'])) {
            $group_by['article'] = '`gd`.`article_id`';
            $order_by['article'] = 'article_name ASC';
        }
        $group_key = empty($group_by) ? "''" : "CONCAT_WS('^', " . implode(', ', $group_by) . ")";

        // Get the records
        $sql = array();

        $sql['select'] = "
            SELECT {$group_key} AS `key`,
                `fir`.`currency`                                      AS `currency`,
                DATE_FORMAT(`fir`.`issue_date`, '%Y-%m')              AS `month`,
                SUM((1 + (`fir`.`total_surplus_percentage` - `fir`.`total_discount_percentage`) / 100) * `gd`.`subtotal_with_discount`) AS `subtotal_with_discount`,
                SUM((1 + (`fir`.`total_surplus_percentage` - `fir`.`total_discount_percentage`) / 100) * `gd`.`subtotal_with_vat_with_discount`) AS `subtotal_with_vat_with_discount`";
        $sql['from'] = "
              FROM `" . DB_TABLE_FINANCE_INCOMES_REASONS . "` AS `fir`";
        $sql['join_gd'] = "
              JOIN `" . DB_TABLE_GT2_DETAILS . "` AS `gd`
                ON `gd`.`model`             = 'Finance_Incomes_Reason'
                  AND `gd`.`model_id`       = `fir`.`id`" .
            (!empty($filters['article']) ? "
                  AND `gd`.`article_id`     IN ('" . implode("', '", $filters['article']) . "')" : '');
        if (!empty($group_by['customer'])) {
            $sql['select'] .= ",
                `fir`.`customer`                                      AS `customer`,
                TRIM(CONCAT(`ci18n`.`name`, ' ', `ci18n`.`lastname`)) AS `customer_name`";
            $sql['join_ci18n'] = "
              LEFT JOIN `" . DB_TABLE_CUSTOMERS_I18N . "` AS `ci18n`
                ON `ci18n`.`parent_id`      = `fir`.`customer`
                  AND `ci18n`.`lang`        = '{$model_lang}'";
        }
        if (!empty($group_by['article'])) {
            $sql['select'] .= ",
                `gd`.`article_id`                                     AS `article_id`,
                TRIM(`ni18n`.`name`)                                  AS `article_name`,
                GROUP_CONCAT(DISTINCT TRIM(`gdi18n`.`article_name`) ORDER BY `gdi18n`.`parent_id` DESC SEPARATOR '^^^') AS `gt2_article_name`";
            $sql['join_gdi18n'] = "
              LEFT JOIN `" . DB_TABLE_NOMENCLATURES_I18N . "` AS `ni18n`
                ON `ni18n`.`parent_id`      = `gd`.`article_id`
                  AND `ni18n`.`lang`        = '{$model_lang}'
              LEFT JOIN `" . DB_TABLE_GT2_DETAILS_I18N . "` AS `gdi18n`
                ON `gdi18n`.`parent_id`     = `gd`.`id`
                  AND `gdi18n`.`lang`       = '{$model_lang}'";
        }
        if (!empty($filters['tag'])) {
            $sql['join_tm'] = "
              JOIN `" . DB_TABLE_TAGS_MODELS . "` AS `tm`
                ON `tm`.`model`             = 'Customer'
                  AND `tm`.`model_id`       = `fir`.`customer`
                  AND `tm`.`tag_id`         IN ('" . implode("', '", $filters['tag']) . "')";
        }
        if (!empty($filters['trademark'])) {
            $sql['join_ct'] = "
              JOIN `" . DB_TABLE_CUSTOMERS_TRADEMARKS . "` AS `ct`
                ON `ct`.`parent_id`         = `fir`.`customer`
                  AND `ct`.`trademark_id`   IN ('" . implode("', '", $filters['trademark']) . "')";
        }
        if (!empty($filters['article_category'])) {
            // use LEFT JOIN or STRAIGHT_JOIN because when table contains a single row (only "All" category)
            // (and maybe when nomenclatures are few and incomes reasons are many)
            // query optimizer puts it first and search becomes much slower when other tables are large
            $sql['join_nc'] = "
              LEFT JOIN `" . DB_TABLE_NOM_CATS . "` AS `nc`
                ON `nc`.`model`             = 'Nomenclature'
                  AND `nc`.`parent_id`      = `gd`.`article_id`
                  AND `nc`.`cat_id`         IN ('" . implode("', '", $filters['article_category']) . "')";
            $where[] = "`nc`.`cat_id`         IN ('" . implode("', '", $filters['article_category']) . "')";
        }
        $sql['where'] = "
              WHERE " . (!empty($where) ? implode("
                AND ", $where) : '1');
        $sql['group_by'] = "
            GROUP BY " . implode(', ', $group_by + array(
                'month' => "DATE_FORMAT(`fir`.`issue_date`, '%Y-%m')",
                'currency' => '`fir`.`currency`',
            ));
        $sql['order_by'] = "
            ORDER BY " . implode(', ', $order_by + array(
                "`month` ASC",
            ));

        $query = implode($sql);
        $records = $db->GetAll($query);

        // Filter: second period
        $where['period_from'] = "'{$filters['period2_from']}' <= `fir`.`issue_date`";
        $where['period_to'] = "`fir`.`issue_date` <= '{$filters['period2_to']}'";

        $sql['where'] = "
              WHERE " . (!empty($where) ? implode("
                AND ", $where) : '1');

        $query = implode($sql);
        $records2 = $db->GetAll($query);

        $final_results['totals'] = $final_results['data'] = $final_results['period2'] = $final_results['period1'] = array();
        $final_results['group_by'] = !empty($group_by) ? array_combine(array_keys($group_by), array_keys($group_by)) : array();

        self::prepareCaptions($registry, new DateTime($filters['period_from'], $tz), new DateTime($filters['period_to'], $tz), $final_results['period1']);
        self::prepareCaptions($registry, new DateTime($filters['period2_from'], $tz), new DateTime($filters['period2_to'], $tz), $final_results['period2']);

        $grouping_columns = array_flip(array('customer', 'customer_name', 'article_id', 'article_name', 'gt2_article_name'));

        self::prepareResults($registry, $records, $grouping_columns, $final_results['period1']);
        self::prepareResults($registry, $records2, $grouping_columns, $final_results['period2']);

        $get_grouping_columns = function($a) use ($grouping_columns) { return array_intersect_key($a, $grouping_columns); };
        $rows = array_map($get_grouping_columns, $final_results['period1']['data']) + array_map($get_grouping_columns, $final_results['period2']['data']);
        if (!empty($group_by['article'])) {
            foreach ($rows as $key => &$row) {
                // order name changes reverse-chronologically, if there were such in both periods
                if (!empty($final_results['period1']['data'][$key]['gt2_article_name']) && !empty($final_results['period2']['data'][$key]['gt2_article_name'])) {
                    if ($filters['period_from'] < $filters['period2_from']) {
                        $row['gt2_article_name'] = array_merge($final_results['period2']['data'][$key]['gt2_article_name'], $final_results['period1']['data'][$key]['gt2_article_name']);
                    } else {
                        $row['gt2_article_name'] = array_merge($final_results['period1']['data'][$key]['gt2_article_name'], $final_results['period2']['data'][$key]['gt2_article_name']);
                    }
                    $row['gt2_article_name'] = array_unique($row['gt2_article_name']);
                }
                // exclude current name
                if (!empty($row['article_name'])) {
                    $row['gt2_article_name'] = array_diff($row['gt2_article_name'], array($row['article_name']));
                }
                $row['gt2_article_name'] = implode("\n", $row['gt2_article_name']);
            }
            unset($row);
        }
        uasort($rows, function($a, $b) use ($group_by) {
            if (array_key_exists('customer', $group_by) && $a['customer_name'] != $b['customer_name']) {
                return $a['customer_name'] < $b['customer_name'] ? -1 : 1;
            }
            if (array_key_exists('article', $group_by) && $a['article_name'] != $b['article_name']) {
                return $a['article_name'] < $b['article_name'] ? -1 : 1;
            }
        });
        foreach (array('period1', 'period2') as $period) {
            $final_results['totals'][$period] = $final_results[$period]['row_blank'];
            $subtotal_amounts = array_diff(array_keys($final_results[$period]['row_blank']), array('months'));
            foreach ($rows as $key => &$row) {
                $row[$period] = array();
                if (array_key_exists($key, $final_results[$period]['data'])) {
                    $row[$period] = array_diff_key($final_results[$period]['data'][$key], $grouping_columns);
                    unset($final_results[$period]['data'][$key]);
                } else {
                    $row[$period] = $final_results[$period]['row_blank'];
                }
                // calculate relative difference of total with VAT for periods
                if ($period == 'period2') {
                    // if second amount is 0 - do not calculate difference
                    if (bccomp($row['period2']['subtotal_with_vat_with_discount'], 0, 2) == 0) {
                        $row['difference'] = '';
                    } else {
                        $row['difference'] = ($row['period2']['subtotal_with_vat_with_discount'] - $row['period1']['subtotal_with_vat_with_discount']) / $row['period2']['subtotal_with_vat_with_discount'] * 100;
                    }
                }
                // add to aggregate rows
                foreach ($row[$period]['months'] as $month => $st) {
                    $final_results['totals'][$period]['months'][$month] += $st;
                }
                foreach ($subtotal_amounts as $st) {
                    $final_results['totals'][$period][$st] += $row[$period][$st];
                }
            }
            unset($final_results[$period]['currency']);
            unset($final_results[$period]['data']);
            unset($final_results[$period]['row_blank']);
        }
        unset($row);

        $final_results['data'] = $rows;

        // calculate relative difference of total with VAT for periods
        // if second amount is 0 - do not calculate difference
        if (bccomp($final_results['totals']['period2']['subtotal_with_vat_with_discount'], 0, 2) == 0) {
            $final_results['totals']['difference'] = '';
        } else {
            $final_results['totals']['difference'] = ($final_results['totals']['period2']['subtotal_with_vat_with_discount'] - $final_results['totals']['period1']['subtotal_with_vat_with_discount']) / $final_results['totals']['period2']['subtotal_with_vat_with_discount'] * 100;
        }

        /*********** chart start **********/

        $chart = new Chart($registry, 'spline', $filters['report_type'], 1, false);

        $categories = array_values($final_results['period1']['months']);
        if (reset($final_results['period1']['months']) != reset($final_results['period2']['months'])) {
            if (count($categories) < 12) {
                foreach (array_keys($final_results['period1']['months']) as $idx => $month_key) {
                    $categories[$idx] .= ' ' . substr($month_key, 0, 4);
                }
            }
            foreach (array_values($final_results['period2']['months']) as $idx => $month) {
                $categories[$idx] .= "<br />" . $month;
            }
            if (count($categories) < 12) {
                foreach (array_keys($final_results['period2']['months']) as $idx => $month_key) {
                    $categories[$idx] .= ' ' . substr($month_key, 0, 4);
                }
            }
        } elseif (count($categories) < 12) {
            foreach (array_keys($final_results['period1']['months']) as $idx => $month_key) {
                $categories[$idx] .= ' ' . substr($month_key, 0, 4);
            }
            foreach (array_keys($final_results['period2']['months']) as $idx => $month_key) {
                $categories[$idx] .= '/' . substr($month_key, 0, 4);
            }
        }

        $chart_params = array(
            'chart' => array(
                'backgroundColor' => '#ffffff',
                'borderWidth'     => 0,
                'height'          => 600,
                'width'           => (count($categories) > 12 ? count($categories) : 12) * 100,
            ),
            'tooltip' => array(
                'formatter' => 'function() { return this.point.name + \', \' + Highcharts.numberFormat(this.y, 2); }'
            ),
            'title' => array(
                'text' => '',
            ),
            'xAxis' => array(
                'categories' => $categories,
                'labels' => array(
                    'rotation' => 0,
                ),
            ),
            'yAxis' => array(
                //'min' => 0,
                'title' => array(
                    'text' => $registry['translater']->translate('reports_th_subtotal_with_discount'),
                ),
            ),
            'series' => array()
        );

        $iteration = 0;
        foreach ($final_results['data'] as $key => $row) {
            foreach (array('period1', 'period2') as $period) {

                $grouping_name = array();
                if (!empty($final_results['group_by']['customer'])) {
                    $grouping_name[] = $row['customer_name'];
                }
                if (!empty($final_results['group_by']['article'])) {
                    $grouping_name[] = $row['article_name'];
                }
                $grouping_name = implode(', ', $grouping_name);

                $chart_params['series'][$iteration]['name'] =
                    $final_results[$period]['year_caption'] .
                    ($grouping_name ? ', ' . $grouping_name : '');

                $chart_params['series'][$iteration]['data'] = array();
                foreach ($row[$period]['months'] as $month_key => $month_amount) {
                    $chart_params['series'][$iteration]['data'][] = array(
                        'y' => $month_amount,
                        'name' => $final_results[$period]['months'][$month_key] .
                            (count($row[$period]['months']) <= 12 ? ' ' . substr($month_key, 0, 4) : '') .
                            ($grouping_name ? ', ' . $grouping_name : ''),
                    );
                }
                $iteration++;
            }
        }

        if ($chart->prepareChart($chart_params)) {
            $final_results['additional_options']['chart_1'] = $chart;
        }
        /*********** chart end **********/

        return self::exitReport($registry, $filters, $final_results, true);
    }

    /**
     * Prepares year and month captions of periods for final results
     *
     * @param Registry $registry - the main registry
     * @param DateTime $period_from_date - start date of period
     * @param DateTime $period_to_date - end date of period
     * @param array $results - array to set prepared data into
     */
    private static function prepareCaptions(Registry &$registry, DateTime $period_from_date, DateTime $period_to_date, array &$results) {
        // year caption
        $results['year_caption'] = sprintf(
            '%s %s',
            $registry['translater']->translate('year'),
            $period_from_date->format('Y') . ($period_from_date->format('Y') != $period_to_date->format('Y') ? '-' . $period_to_date->format('Y') : '')
        );
        // month captions
        $num_months = date_diff(date_add($period_to_date, new DateInterval('P1D')), $period_from_date);
        $num_months = $num_months->format('%y') * 12 + $num_months->format('%m') + intval($num_months->format('%d') > 0);
        $date = $period_from_date;
        $format = $num_months > 12 ? '%b %Y' : '%B';
        if (!array_key_exists('months', $results)) {
            $results['months'] = array();
        }
        while ($num_months > 0) {
            $results['months'][$date->format('Y-m')] = General::strftime($format, $date->getTimestamp(), true);
            $date = $date->add(new DateInterval('P1M'));
            $num_months--;
        }
    }

    /**
     * Processes data according to grouping fields and builds structure of final results
     *
     * @param Registry $registry - the main registry
     * @param array $records - found data
     * @param array $grouping_columns - fields corresponding to or related to fields that data is grouped by
     * @param array $results - processed results
     */
    private static function prepareResults(Registry &$registry, array $records, array $grouping_columns, array &$results) {
        if (empty($results['months'])) {
            return;
        }

        // here will be stored the row data
        $results['data'] = array();

        // initial amounts for each result row
        $results['row_blank'] = array(
            'months' => array_fill_keys(array_keys($results['months']), 0),
            'subtotal_with_discount' => 0,
            'subtotal_with_vat_with_discount' => 0,
        );

        foreach ($records as $key => &$record) {
            $record['subtotal_with_discount'] = $record['subtotal_with_discount'] * self::getConversionRate($registry, $record['currency']);
            $record['subtotal_with_vat_with_discount'] = $record['subtotal_with_vat_with_discount'] * self::getConversionRate($registry, $record['currency']);

            if (!array_key_exists($record['key'], $results['data'])) {
                // initial structure of one period from a row of final results
                $results['data'][$record['key']] = array_intersect_key($record, $grouping_columns) + $results['row_blank'];
                if (array_key_exists('gt2_article_name', $record)) {
                    $results['data'][$record['key']]['gt2_article_name'] = array();
                    if (empty($record['article_name']) || $record['article_name'] != $record['gt2_article_name']) {
                        $results['data'][$record['key']]['gt2_article_name'] = explode('^^^', $record['gt2_article_name']);
                    }
                }
            }
            if (array_key_exists($record['month'], $results['data'][$record['key']]['months'])) {
                // month sum per row
                $results['data'][$record['key']]['months'][$record['month']] += $record['subtotal_with_discount'];
                // totals per row
                $results['data'][$record['key']]['subtotal_with_discount'] += $record['subtotal_with_discount'];
                $results['data'][$record['key']]['subtotal_with_vat_with_discount'] += $record['subtotal_with_vat_with_discount'];
            }
        }
        unset($record);
    }

    /**
     * Gets conversion rate between specified currency and selected report currency for
     * current date (or latest available)
     *
     * @param Registry $registry - the main registry
     * @param string $source_currency - source currency
     * @return float - conversion rate
     */
    private static function getConversionRate(Registry $registry, $source_currency) {
        if (!array_key_exists($source_currency, self::$conversion_rates)) {
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            if (!self::$currency) {
                self::$currency = Finance_Currencies::getMain($registry);
            }
            self::$conversion_rates[$source_currency] = Finance_Currencies::getRate($registry, $source_currency, self::$currency);
        }
        return self::$conversion_rates[$source_currency];
    }

    private static function exitReport(&$registry, $filters, $final_results, $success = false, $error = '') {
        if (!$success) {
            $final_results['additional_options']['failed'] = true;
            $final_results['additional_options']['dont_show_export_button'] = true;
            if (!empty($error)) {
                $final_results['additional_options']['error'] = $registry['translater']->translate($error);
            }
        }

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }
        return $results;
    }
}

?>
