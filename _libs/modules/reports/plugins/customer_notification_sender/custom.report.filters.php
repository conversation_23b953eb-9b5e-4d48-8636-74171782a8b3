<?php

class Custom_Report_Filters extends Report_Filters {

    /**
     * @var Registry
     */
    private static $registry;

    /**
     * Defining filters for the certain type of report
     *
     * @param Registry $registry - the main registry
     * @return array - filter definitions of report
     */
    function defineFilters(Registry &$registry) {

        self::$registry = &$registry;

        // $filters - array containing description of all filters
        $filters = array();

        /** @var Customer_Notification_Sender $report_class */
        $report_class = Reports::getPluginFactory($this->reportName);

        $settings = $report_class::getReportSettings($registry);

        /**                   !!! IMPORTANT !!!!
         *  We need this variable for each report we will use to send emails
         */
        $filters['custom_generate'] = array(
            'custom_id' => 'custom_generate',
            'name' => 'custom_generate',
            'type' => 'hidden',
            'hidden' => true,
            'value' => 1,
        );

        $filters['notification_type'] = array(
            'name' => 'notification_type',
            'type' => 'dropdown',
            'required' => true,
            'options' => array_map(
                function($a) use ($registry) {
                    return array(
                        'option_value' => $a,
                        'label' => $registry['translater']->translate("reports_send_{$a}"),
                    );
                },
                $settings['notification_types']
            ),
            'label' => $this->i18n('reports_notification_type'),
        );

        $filters['query'] = array(
            'name' => 'query',
            'type' => 'radio',
            'required' => true,
            'options' => $report_class::getQueryOptions($registry),
            'label' => $settings['query_label'],
        );
        if (count($filters['query']['options']) <= $settings['query_options_align_horizontal_max_num']) {
            $filters['query']['options_align'] = 'horizontal';
        }

        // add handle to enter edit mode of query filters
        if ($registry['currentUser'] && ($registry['currentUser']->get('role') == PH_ROLES_ADMIN || $registry['currentUser']->get('id') == PH_USERS_ADMIN)) {
            $filters['query_button'] = array(
                'name' => 'query_button',
                'type' => 'button',
                'label' => $registry['translater']->translate('edit') . ' ' . $registry['translater']->translate('options'),
                'source' => array(
                    'onclick' => 'Customer_Notification_Sender.editQueryFilters(this);',
                ),
            );
        }

        // change the confirmation text by defining a more specific message
        $custom_scripts = array(
            array(
                'type' => 'inline',
                'src' => "
                    if ($('notification_type') && $('notification_type').value == 'sms') {
                        i18n['messages']['confirm_send_as_mail_reports'] = '{$this->i18n('reports_sms_provider_limitations')}\\n\\n' + i18n['messages']['confirm_send_as_mail'];
                    }
                ",
            ),
        );
        $this->loadDefaultFilter($registry, $filters, 'scripts', array('custom_scripts' => $custom_scripts));

        return $filters;
    }

    /**
     * Process some filters that depend on the request or on each other
     *
     * @param array $filters - the report filters
     * @return array - report filters after processing
     */
    /* function processDependentFilters(array &$filters) {
        $registry = &self::$registry;

        if ($registry['generated_report']) {
        }

        return $filters;
    } */
}
