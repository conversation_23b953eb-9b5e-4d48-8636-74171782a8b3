<?php

//use PhpOffice\PhpWord\PhpWord;
//use PhpOffice\PhpWord\Writer\Word2007;

use Nzoom\Pdf\Processor;

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';
include_once __DIR__ . '/PdfTrait.php';

/**
 *
 */
class Custom_Report_Controller extends Reports_Controller {
    use PdfTrait;

    /**
     *
     */
    private const TYPE_SIGNITURE_SUPER = 1;
    /**
     *
     */
    private const TYPE_SIGNITURE_EVAL = 2;

    /**
     *
     */
    public const MADE_BY_ALVIS = 1;

    /**
     *
     */
    private const COST_APPROACH_TYPE = 41;

    /**
     *
     */
    private const MARKET_APPROACH_TYPE = 33;

    /**
     *
     */
    private const REVENUE_APPROACH_TYPE = 36;

    /**
     * @var PhpWord|null
     */
    private $PHPWord;

    /**
     * Generic action dispatcher routing
     * according to the requested action
     *
     * @throws Exception
     */
    public function execute(): void
    {
        switch ($this->action) {
            case 'generate':
                $this->generate();
                break;
            default:
                parent::execute();

                echo json_encode([
                    'error' => [
                        'type' => 'input',
                        'field' => 'reports',
                        'value' => $this->action,
                        'message' => "Unsupported action! The supported action is: 'generate'!",
                    ]
                ]);
                exit;
        }
    }

    /**
     * Generates a docx file and convert it to a PDF file
     * via CloudConvert or other tools defined in the settings table
     *
     * @throws Exception
     */
    private function generate()
    {
        ini_set('max_execution_time', 600);
        ini_set('max_input_time', 600);
        ini_set('memory_limit', '2G');
        ini_set('error_reporting', 0);

        $tempDir = PH_RESOURCES_DIR . 'reports';

        $this->validateGetParams();

        $reportId = $_GET['report_id'];
        $viewData = [];

        $this->validateUploadedFile();

        $uploadedFile = $this->uploadFile($tempDir);

        $originalFileName = $uploadedFile['original_file_name'];
        $extension = $uploadedFile['extension'];
        $sourceFilePath = $uploadedFile['source_file_path'];

        $this->validateFileExtension($extension);

        $report0 = Documents::searchOne($this->registry, [
            'where' => [
                "a__created_document_id = '{$reportId}'" ,
            ],
            'model_lang' => $this->registry['lang'],
        ]);

        if (!$report0) {
            echo json_encode([
                'error' => [
                    'type' => 'input',
                    'message' => "Report document doesn't exist!",
                ],
            ]);
            unlink($sourceFilePath);
            exit;
        }

        $report0->getVars();

        if (empty($report0->extender)) {
            $report0->extender = new Extender();
        }

        $recording_role_group = self::getGT($report0, 'recording_role_group');

        $supervisorsIds = [];
        $signersIds = [];
        foreach ($recording_role_group['recording_role_type']['value'] as $k => $v) {
            switch ($v) {
                case 2: // supervisor
                    $supervisorsIds[] = $recording_role_group['recording_role_id']['value'][$k];
                    break;
                case 5: // Signer
                    $signersIds[] = $recording_role_group['recording_role_id']['value'][$k];
                    break;
            }
        }

        $supervisorsModels = [];
        $signersModels = [];
        if (count($supervisorsIds)) {
            $supervisorsModels = Customers::search($this->registry, [
                'where' => [
                    "c.id IN (" . implode(',', $supervisorsIds) . ")",
                ],
                'model_lang' => $this->registry['lang'],
            ]);
        }

        if (count($signersIds)) {
            $signersModels = Customers::search($this->registry, [
                'where' => [
                    "c.id IN (" . implode(',', $signersIds) . ")",
                ],
                'model_lang' => $this->registry['lang'],
            ]);
        }

        // PHPWord 103617
        $PHPWord = $this->getPhpword();

        // Start signing a final report
        $docxTemplateFinal = $PHPWord->loadTemplate($sourceFilePath);
        $extender = new Extender();

        try {
            $superSignaturesXml = $this->getSignatureXml(
                $supervisorsModels,
                self::TYPE_SIGNITURE_SUPER,
                $docxTemplateFinal,
                1000
            );
            $evaluatorSignaturesXml = $this->getSignatureXml(
                $signersModels,
                self::TYPE_SIGNITURE_EVAL,
                $docxTemplateFinal,
                2000
            );
        } catch (Exception $e) {
            echo json_encode([
                'error' => [
                    'type' => 'internal',
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                ],
            ]);
            unlink($sourceFilePath);
            exit;
        }

        $extender->add('supervisor_sign', $superSignaturesXml);
        $extender->add('evaluator_sign', $evaluatorSignaturesXml);
        // End for signatures

        $reports = $this->getComparativeTableReports($reportId);
        $comparativeObjectsIds = array_column($reports, 'comparative_object_id');
        $filesIds = $this->getNomenclatureObjectsFiles($comparativeObjectsIds);
        $attachedFiles = $this->getAttachedFiles($filesIds) ?? [];

        if (!empty($attachedFiles)) {
            foreach ($reports as $report) {
                $this->addToFiles((int) $report['related_doc_type'], (int) $report['comparative_object_id']);
            }

            $this->sortAttachedFilesByComparativeTableType($attachedFiles);
        }
        if (empty($attachedFiles)) {
            try {
                $this->addCertificateToTheEnd($this->getAdvanceCertificate(), $docxTemplateFinal);
            } catch (Exception $e) {
                echo json_encode([
                    'error' => [
                        'type' => 'internal',
                        'message' => $e->getMessage(),
                        'code' => $e->getCode(),
                    ],
                ]);
                unlink($sourceFilePath);
                exit;
            }
        }

        $docxTemplateFinal->_documentXML = $extender->expand($docxTemplateFinal->_documentXML);

        $finalFilePath = str_replace($extension, 'final.' . $extension, $sourceFilePath);

        $docxTemplateFinal->save($finalFilePath);

        // Final version
        if (!$this->convertDocxToPdf($finalFilePath)) {
            echo json_encode([
                'error' => [
                    'type' => 'convert',
                    'message' => "A problem occurred while converting to pdf - final version",
                    'file' => 'final',
                ]
            ]);
            unlink($sourceFilePath);
            exit;
        }
        if ($this->hasBrokenFiles) {
            $response = [
                'type' => 'broken_files',
                'message' => "A problem occurred while converting to pdf",
            ];

            if (!empty($this->brokenFiles)) {
                $response['file'] = $this->brokenFiles;
            }
            echo json_encode([
                'error' => $response,
            ]);
            exit;
        }

        if ($this->isCreatedByAlvis($report0)) {
            $finalPdfFile = str_replace($extension, 'pdf', $finalFilePath);

            $this->prependFileForMerging($finalPdfFile);

            $outputFinalPdfFile = preg_replace('#\.pdf$#', '.processed.pdf', $finalPdfFile);

            $this->processFinalVersion($outputFinalPdfFile, $originalFileName, $report0);
            unlink($finalPdfFile);
        }
        unlink($finalFilePath);

        // Draft version
        $draftFilePath = str_replace($extension, 'draft.' . $extension, $sourceFilePath);
        copy($sourceFilePath, $draftFilePath);
        $this->addWatermark($draftFilePath);

        $convertDraftDocxToPdf = $this->convertDocxToPdf($draftFilePath);

        if (!$convertDraftDocxToPdf) {
            echo json_encode([
                'error' => [
                    'type' => 'convert',
                    'message' => "A problem occurred while converting to pdf - draft version",
                    'file' => 'draft',
                ]
            ]);
            unlink($finalFilePath);
            unlink($sourceFilePath);
            exit;
        }

        if ($this->isCreatedByAlvis($report0)) {
            $this->processDraftVersion($draftFilePath, $extension, $originalFileName, $report0);
        }
        unlink($draftFilePath);


        $this->attachPdfToReport($report0, $sourceFilePath, 'source_report', '', $originalFileName);
        $hasSavedVars = $report0->saveVars();

        if (!$hasSavedVars) {
            echo json_encode([
                'error' => [
                    'type' => 'convert',
                    'message' => "A problem occurred while converting to pdf",
                ]
            ]);
            unlink($sourceFilePath);
            exit;
        }

        $viewData['status'] = 'success';

        if ($this->containsMissingFiles()) {
            $viewData['message'] = 'Successfully merged pdf, but some files are missing';
            $viewData['missing_files'] = $this->missingFiles;
        }

        $documentVars = $report0->get('vars');
        foreach ($documentVars as $k => $v) {
            if (in_array($v['name'], ['source_report', 'draft_report', 'last_report'])) {
                if (!empty($documentVars[$k]['value']) && !is_array($documentVars[$k]['value'])) {
                    $viewData[$v['name']] = $documentVars[$k]['value'];
                }
                break;
            }
        }

        echo json_encode($viewData);
        exit;
    }

    /**
     * Gets advance certificate image path
     *
     * @return string
     * @throws Exception
     */
    private function getAdvanceCertificate(): string
    {
        $ownCompanyModel = Customers::searchOne($this->registry, [
            'where' => [
                "c.id = 23",
            ],
            'model_lang' => $this->registry['lang'],
        ]);

        $ownCompanyModel->getAllVars();
        $ownVars = $ownCompanyModel->get('plain_vars');
        $certificate_advance = null;
        foreach ($ownVars as $k => $v) {
            if ($v['name'] == 'certificate_advance') {
                $certificate_advance = $v['value'] ?? null;
                break;
            }
        }
        if (!$certificate_advance || !is_a($certificate_advance, File::class)) {
            throw new Exception('Certificate advance not found!');
        }

        return realpath($certificate_advance->get('path'));
    }

    /**
     * @param string           $certificateImagePath
     * @param PHPWord_Template $docxTemplate
     *
     * @return void
     */
    private function addCertificateToTheEnd(string $certificateImagePath, PHPWord_Template $docxTemplate)
    {
        $rId = $docxTemplate->addImage($certificateImagePath);
        $viewer = new Viewer($this->registry);

        $viewer->setFrameset(realpath(__DIR__ . '/../resources/certificate.xml'));
        $viewer->data['rId'] = 'rId' . $rId;

        $certificateXml = $viewer->fetch();
        $endOfContent_pos = strrpos($docxTemplate->_documentXML, '<w:sectPr');

        $docxTemplate->_documentXML = substr_replace(
            $docxTemplate->_documentXML,
            $certificateXml,
            $endOfContent_pos,
            0
        );
    }

    /**
     * @param $documentPath
     *
     * @return void
     * @throws Exception
     */
    private function addWatermark($documentPath)
    {
        $w_ns = 'http://schemas.openxmlformats.org/wordprocessingml/2006/main';
        $v_ns = 'urn:schemas-microsoft-com:vml';
        $w10_ns = 'urn:schemas-microsoft-com:office:word';
        $o_ns = 'urn:schemas-microsoft-com:office:office';
        $r_ns = 'http://schemas.openxmlformats.org/officeDocument/2006/relationships';

        $za = new ZipArchive();
        $za->open($documentPath);

        $ct = new SimpleXMLElement($za->getFromName('[Content_Types].xml'));
        $newHeaderOverride1 = $ct->addChild('Override');
        $newHeaderOverride1['PartName'] = '/word/header_draft.xml';
        $newHeaderOverride1['ContentType'] = 'application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml';
        $za->addFromString('[Content_Types].xml', $ct->asXML());

        $dxr = new SimpleXMLElement($za->getFromName('word/_rels/document.xml.rels'));
        $newRelationship = $dxr->addChild('Relationship');
        $headerId = 'rId' . count($dxr->Relationship);
        $newRelationship['Id'] = $headerId;
        $newRelationship['Type'] = 'http://schemas.openxmlformats.org/officeDocument/2006/relationships/header';
        $newRelationship['Target'] = 'header_draft.xml';
        $za->addFromString('word/_rels/document.xml.rels', $dxr->asXML());

        $za->addFromString('word/header_draft.xml', file_get_contents(__DIR__ . '/../resources/header_draft.xml'));

        $documentXml = new SimpleXMLElement($za->getFromName('word/document.xml'));
        foreach ($documentXml->xpath('//w:sectPr') as $wsectPr) {
            $wheaderReference1 = $wsectPr->addChild('headerReference', null, $w_ns);
            $wheaderReference1['w:type'] = 'first';
            $wheaderReference1['r:id'] = $headerId;
            $wheaderReference2 = $wsectPr->addChild('headerReference', null, $w_ns);
            $wheaderReference2['w:type'] = 'default';
            $wheaderReference2['r:id'] = $headerId;
        }
        $za->addFromString('word/document.xml', $documentXml->asXML());

        $za->close();
    }

    /**
     * @return PhpWord
     */
    private function getPhpword(): ?PhpWord
    {
        if (is_null($this->PHPWord)) {
            require_once PH_PHPWORD_DIR . 'PHPWord.php';
            $this->PHPWord = new PhpWord();
        }
        return $this->PHPWord;
    }

    /**
     * @param $report0
     * @param $filePath
     * @param $docVarName
     * @param $sufix
     * @param $filename
     *
     * @return void
     * @throws Exception
     */
    private function attachPdfToReport($report0, $filePath, $docVarName, $sufix='', $filename = null)
    {
        $extention = substr($filePath, strrpos($filePath, '.')+1);

        $revision = Files::getLatestRevision($this->registry, [
            'model' => $report0->modelName,
            'model_id' => $report0->get('id'),
            'origin' => 'attached',
        ]);
        $report0->set('rev', $revision, true);
        $report0->set('export_format', $extention, true);

        $baseFileName = $report0->composeOutputFileName($extention) . $sufix;
        $destinationPath = $report0->composeFileSystemPath($baseFileName);

        rename($filePath, $destinationPath);
        //$fileName = $baseFileName . '.' . $extention;

        $filename = substr($filename, 0, strrpos($filename, '.')+1). $extention;
        $file = new File($this->registry, [
            'model'       => $report0->modelName,
            'model_id'    => $report0->get('id'),
            'filename'    => $filename ? $filename : $report0->get('full_num'),
            'name'        => $filename ? $filename : $report0->get('full_num'),
            'path'        => $destinationPath,
            'origin'      => 'attached',
            'revision'    => $revision,
        ]);
        $fileId = $file->save();

        if($fileId) {
            if (!$report0->isDefined('vars')) {
                $report0->getVars();
            }
            $documentVars = $report0->get('vars');
            foreach ($documentVars as $k => $v) {
                if ($v['name'] == $docVarName) {
                    if(!empty($v['value'])) {
                        Files::delete($this->registry, $v['value']->get('id'));
                    }
                    $documentVars[$k]['value'] = $fileId;
                    break;
                }
            }

            $this->registry->set('edit_all', true, true);
            $report0->set('vars', $documentVars, true);
        }
    }


    /**
     * @param array            $imagesArray
     * @param PHPWord_Template $docx_template
     * @param int              $idxSeed
     * @param int              $x
     * @param int              $y
     *
     * @return string
     * @throws Exception
     */
    private function getDocxImageXml(
        array $imagesArray,
        PHPWord_Template $docx_template,
        int $idxSeed,
        int $x = 0,
        int $y = 0
    ): string {
        $superSignatureXml = '';
        $idx = $idxSeed;
        foreach ($imagesArray as $k => $v) {
            $viewer = new Viewer($this->registry);
            $viewer->setFrameset(realpath(__DIR__ . '/../resources/signiture.xml'));
            $imgPath = $v->get('path');

            $resolution = self::getResolution($imgPath);
            $dimensions = getimagesize($imgPath);
            // 1cm * 360000 => EMU
            // 300dpi / 2.54cm = 118dpc (dots per centimeter) /  but 120 matches better with real mesurments
            $imgUnitConv = 360000 / 120;
            $width = bcmul($dimensions[0], 900000 / $resolution[0], 0);
            $height = bcmul($dimensions[1], 900000 / $resolution[1], 0);
            $rid = $docx_template->addImage($imgPath);
            $viewer->data['image'] = array(
                'index' => $idx++,
                'rId' => $rid,
                'filename' => $v->get('filename'),
            );
            $viewer->data['x'] = $x;
            $viewer->data['y'] = $y;
            $viewer->data['width'] = $width;
            $viewer->data['height'] = $height;
            $superSignatureXml .= $viewer->fetch();
            $x = $x + $width * 3 / 4;
            $y = $y + $height * 3 / 4;
        }

        return $superSignatureXml;
    }

    /**
     * Returns the dpi resolution of an image in a form of array [horizontal, vertical]
     * @param $imgPath
     * @return array of resolutions
     * @throws Exception
     */
    private static function getResolution($imgPath): array
    {
        $imgFilename = basename($imgPath);
        $imgFilenameParts = explode('.', $imgFilename);
        $ext = strtolower($imgFilenameParts[count($imgFilenameParts)-1]);
        switch($ext){
            case 'gif':
                $image = imagecreatefromgif($imgPath);
                break;
            case 'png':
                $image = imagecreatefrompng($imgPath);
                break;
            case 'jpg':
            case 'jpeg':
                $image = imagecreatefromjpeg($imgPath);
                break;
            case 'bmp':
                $image = imagecreatefrombmp($imgPath);
                break;
            default:
                throw new Exception("Unsupported file format '{$ext}'");
        }
        $resolutions = imageresolution($image);
        imagedestroy($image);
        return $resolutions;
    }

    /**
     * Finds and returns the signature file from the customer model
     *
     * @param Customer $model
     * @param int      $type One of the TYPE_SIGNITURE_* constants
     *
     * @return array
     * @throws Exception
     */
    private static function getSignitureFromCustomer(Customer $model, int $type): array
    {
        $certificate_group = self::getGT($model, 'sertificate_group');

        switch ($type) {
            case self::TYPE_SIGNITURE_SUPER:
                $signiture_colName = 'file_signature';
                break;
            case self::TYPE_SIGNITURE_EVAL:
                $signiture_colName = 'signature_file';
                break;
            default:
                throw new Exception(
                    "Unsupported type '{$type}' given! Use one of the '"
                    . __CLASS__ . "::TYPE_SIGNITURE_...' constants",
                    5201
                );
        }
        $signature = [];
        foreach ($certificate_group['type_sertificate_id']['value'] as $k => $v) {
            if ($v != 265) { // Недвижими имоти
                continue;
            }
            $f = $certificate_group[$signiture_colName]['value'][$k];
            if (!is_object($f) || !empty($f->get('deleted_by')) || !empty($f->get('not_exist'))) {
                continue;
            }
            $signature[] = $f;
            $f = null;
        }

        return $signature;
    }

    /**
     * Finds and returns the GT variables that are part of one GT with the given name
     *
     * @param Model       $model
     * @param string|null $gtName
     *
     * @return array
     */
    private static function getGT(Model $model, ?string $gtName = null): array
    {
        $groups = [];
        $gt = [];
        $groupings = [];
        foreach ($model->get('group_vars') as $v) {
            $groups[$v['grouping']][$v['name']] = $v;

            if ($v['type'] == 'group') {
                $groupings[$v['grouping']] = $v['name'];
            }
        }
        foreach ($groups as $k => $v) {
            if ($gtName && $gtName != $groupings[$k]) {
                continue;
            }
            $gt[$groupings[$k]] = $v;
        }

        return $gtName ? $gt[$gtName] : $gt;
    }

    /**
     *  Returns a unique filename that is available in a specific directory
     *  Throws Exception if it can't create the directory or if it can't find a
     *  unique name in 1000 atempts
     *
     *  The returned string is guaranteed to be a filename that is not present
     *  in the Directory.
     *
     * @param string      $dirPath
     * @param string      $extension
     * @param string|null $prefix
     *
     * @return string
     *@throws Exception
     */
    private function getUniqueFilename(string $dirPath, string $extension, ?string $prefix = null): string
    {
        $dirPath = rtrim($dirPath, ' /\\');
        if (!is_dir($dirPath)) {
            if (!mkdir($dirPath, 0777, true)) {
                throw new Exception("Unable to create necesary directory '{$dirPath}'", 5301);
            }
        }

        // Watchdog to prevent endless loop
        $wd = 1000;
        do {
            if ($wd-- < 0) {
                throw new Exception("Unable to find an available unique name in '{$dirPath}' for $wd trys!", 5302);
            }
            $uid = uniqid($prefix);
            $filename = $uid . '.' .  $extension;
        } while (is_file($dirPath . DIRECTORY_SEPARATOR . $filename) || $wd < 0);

        return $filename;
    }

    /**
     * Gets related analog ids and their type, comparative table id
     *
     * @param string $reportId
     *
     * @return array
     */
    private function getComparativeTableReports(string $reportId): array
    {
        $aliases = [
            'd' => DB_TABLE_DOCUMENTS,
            'dc' => DB_TABLE_DOCUMENTS_CSTM,
            'dc2' => DB_TABLE_DOCUMENTS_CSTM,
            'dc3' => DB_TABLE_DOCUMENTS_CSTM,
        ];
        $comparativeTableTypes = implode(', ', $this->comparativeTableTypes);

        $sql = <<<SQL
            SELECT
                dc.value AS related_doc_type,
                dc2.value AS related_doc,
                dc3.value AS comparative_object_id
            FROM {$aliases['d']} AS d
            INNER JOIN {$aliases['dc']} AS dc
                ON d.id = dc.model_id
                AND dc.var_id = '1483'
                AND dc.value IN ({$comparativeTableTypes})
            INNER JOIN {$aliases['dc2']} AS dc2
                ON d.id = dc2.model_id
                AND dc2.var_id = '1485'
                AND dc.num = dc2.num
            INNER JOIN {$aliases['dc3']} AS dc3
        ON dc3.model_id = dc2.value
            AND dc3.var_id IN (3720, 4217, 32214)
            WHERE d.id = '{$reportId}'
            ORDER BY dc.value DESC, dc3.num;
        SQL;

        return $this->registry['db']->getAll($sql) ?? [];
    }

    /**
     * Returns file id from analog_file var field in nomenclatures with type 84 and 91
     *
     * @param array $nomenclatureIds
     *
     * @return array
     */
    private function getNomenclatureObjectsFiles(array $nomenclatureIds): array
    {
        $ids = implode(',', array_filter($nomenclatureIds));
        /**
         * ORDER BY FIND_IN_SET(nc.model_id, '{$ids}')
         * it is important to ensure the order of the rows in the table of analogs
         */
        $sql = <<<SQL
            SELECT
                nc.value AS file_id
            FROM
                nom AS n,
                nom_cstm AS nc
            WHERE n.id = nc.model_id
            AND n.active = 1
                AND n.deleted_by = 0
                AND n.`type` IN (84, 91)
                AND nc.var_id IN (9049, 18305)
            AND nc.model_id IN ({$ids})
            ORDER BY FIND_IN_SET(nc.model_id, '{$ids}')
        SQL;

        $result = $this->registry['db']->getAll($sql) ?? [];

        return array_column($result, 'file_id');
    }

    /**
     * Retrieves attached files based on the provided file IDs.
     * It is IMPORTANT to sort the files with FIND_IN_SET()
     * so that the sequence structure of the analogs and their files can be preserved
     *
     * @param array $fileIds Array of file IDs to retrieve.
     *
     * @return array List of files matching the given IDs in the specified order.
     */
    private function getAttachedFiles(array $fileIds): array
    {
        $fileIdsCSVFormat = implode(',', array_filter($fileIds));
        $filter = [
            'where' => [
                'f.id IN(' . $fileIdsCSVFormat . ')',
            ],
            'sort' => [
                'FIND_IN_SET(f.id, "' . $fileIdsCSVFormat . '")',
            ],
        ];

        return Files::search($this->registry, $filter) ?? [];
    }

    /**
     * Sort attached files by their comparative table type
     *
     * @param array $attachedFiles
     *
     * @return void
     */
    private function sortAttachedFilesByComparativeTableType(array $attachedFiles): void
    {
        $processor = new Processor();
        $files = $this->getFiles() ?? [];
        $collectedFiles = [];

        foreach ($files as $relatedDocType => $comparativeObjects) {
            foreach ($attachedFiles as $file) {
                if (in_array($file->get('model_id'), $files[$relatedDocType])) {
                    if (file_exists($file->get('path'))) {
                        $collectedFiles[$relatedDocType][$file->get('model_id')][$file->get('id')] = $file->get('path');

                        if (!$processor->isValid($file->get('path'))) {
                            $this->setHasBrokenFiles(true);
                            $this->brokenFiles[] = $file->get('filename');
                        }
                    } else {
                        $this->setHasMissingFiles(true);
                        $this->missingFiles[] = $file->get('filename');
                    }
                }
            }
        }
        $this->setCollectedFilesForMerging($collectedFiles);
    }

    /**
     * Validate GET params
     * Check if report_id is present
     *
     * @return void
     */
    public function validateGetParams(): void
    {
        if (!array_key_exists('report_id', $_GET)) {
            echo json_encode([
                'error' => [
                    'type' => 'input',
                    'field' => 'report_id',
                    'message' => 'Required GET field is missing!',
                ],
            ]);
            exit;
        }
    }

    /**
     * Validate uploaded file
     * Check if there is a file
     * Check if there is an error with the file
     *
     * @return void
     */
    public function validateUploadedFile(): void
    {
        if (!array_key_exists('source_file', $_FILES)) {
            echo json_encode([
                'error' => [
                    'type' => 'input',
                    'field' => 'source_file',
                    'message' => "Missing file upload",
                ],
            ]);
            exit;
        }
        if ($_FILES['source_file']['error']) {
            echo json_encode([
                'error' => [
                    'type' => 'fileupload',
                    'message' => 'File upload error!',
                    'code' => $_FILES['source_file']['error'],
                ],
            ]);
            exit;
        }
    }

    /**
     * Validate file extension
     *
     * @param string $extension
     *
     * @return void
     */
    public function validateFileExtension(string $extension): void
    {
        $extensions = [
            'docx',
        ];
        if (!in_array(strtolower($extension), $extensions)) {
            echo json_encode([
                'error' => [
                    'type' => 'fileextension',
                    'message' => "Unsupported file extension '{$extension}'!",
                    'code' => $_FILES['source_file']['error'],
                ],
            ]);
            exit;
        }
    }

    /**
     * Generate xml signature
     *
     * @param array            $models
     * @param int              $signatureType
     * @param PHPWord_Template $docxTemplate
     * @param int              $id
     *
     * @return string
     * @throws Exception
     */
    private function getSignatureXml(array $models, int $signatureType, PHPWord_Template $docxTemplate, int $id): string
    {
        if (empty(array_filter($models))) {
            return '';
        }

        $n = 0;
        $xml = '';
        foreach ($models as $k => $v) {
            $v->getAllVars();
            $signature = self::getSignitureFromCustomer($v, $signatureType);
            if (!empty($signature)) {
                $xml .= $this->getDocxImageXml($signature, $docxTemplate, $id += 10, 0, $n * 120000);
                $n += 1;
            }
        }

        return $xml;
    }

    /**
     * Convert docx file to pdf
     *
     * @param string $sourceFilePath
     *
     * @return bool
     */
    private function convertDocxToPdf(string $sourceFilePath): bool
    {
        $doc2pdf_settings = $this->registry['config']->getSectionParams('docx2pdf');

        return Converter::convert([
            'type' => 'docx2pdf',
            'source' => $sourceFilePath,
            'docx2pdf' => $doc2pdf_settings,
        ]);
    }

    /**
     * Save uploaded file
     * Returns original file name, file extension and file path
     *
     * @param string $tempDir
     *
     * @return array
     */
    public function uploadFile(string $tempDir): array
    {
        $originalFileName = $_FILES['source_file']['name'];
        $extension = substr($originalFileName, strrpos($_FILES['source_file']['name'], '.') + 1);

        try {
            $sourceFileTempName = $this->getUniqueFilename($tempDir, $extension);
        } catch (Exception $e) {
            return [];
        }

        $sourceFilePath = $tempDir . DIRECTORY_SEPARATOR . $sourceFileTempName;
        move_uploaded_file($_FILES['source_file']['tmp_name'], $sourceFilePath);
        chmod($sourceFilePath, 0777);

        return [
            'original_file_name' => $originalFileName,
            'extension' => $extension,
            'source_file_path' => $sourceFilePath
        ];
    }

    /**
     * Process PDF final version
     *
     * @param string   $finalPdfFile
     * @param string   $originalFileName
     * @param Document $report0
     *
     * @return void
     * @throws Exception
     */
    private function processFinalVersion(
        string $finalPdfFile,
        string $originalFileName,
        Document $report0
    ): void {
        try {
            $this->merge($finalPdfFile);
            $this->addCertificateToPdf($this->getAdvanceCertificate(), $finalPdfFile);
        } catch (Exception $e) {
            echo json_encode([
                'error' => [
                    'type' => 'merge',
                    'message' => "A problem occurred while merging pdf - final version",
                    'file' => 'final',
                    'code' => $e->getCode(),
                    'actual_error' => $e->getMessage(),
                ]
            ]);
            unlink($finalPdfFile);
            exit;
        }

        $this->attachPdfToReport($report0, $finalPdfFile, 'last_report', '', $originalFileName);
    }

    /**
     * Process draft version PDF
     *
     * @param string   $draftFilePath
     * @param string   $extension
     * @param string   $originalFileName
     * @param Document $report0
     *
     * @return void
     * @throws Exception
     */
    private function processDraftVersion(
        string $draftFilePath,
        string $extension,
        string $originalFileName,
        Document $report0
    ): void {
        $draftPdfFile = substr($draftFilePath, 0, strlen($draftFilePath) - strlen($extension)) . 'pdf';
        $draftFileName = substr($originalFileName, 0, strlen($originalFileName) - 4) . 'draft.pdf';
        $outputDraftPdfFile = preg_replace('#\.pdf$#', '.processed.draft.pdf', $draftPdfFile);

        $this->prependFileForMerging($draftPdfFile);

        try {
            $this->merge($outputDraftPdfFile);
        } catch (Exception $e) {
            echo json_encode([
                'error' => [
                    'type' => 'merge',
                    'message' => "A problem occurred while merging pdf - draft version",
                    'file' => 'draft',
                    'code' => $e->getCode(),
                    'actual_error' => $e->getMessage(),
                ]
            ]);
            unlink($outputDraftPdfFile);
            exit;
        }
        $this->attachPdfToReport($report0, $outputDraftPdfFile, 'draft_report', '', $draftFileName);

        unlink($draftPdfFile);
    }

    /**
     * Check if a report is created by Alvis
     *
     * @param Document $report
     *
     * @return bool
     */
    private function isCreatedByAlvis(Document $report): bool
    {
        $varsAssoc = $report->getAssocVars();

        return $varsAssoc['system_made_by']['value'] == self::MADE_BY_ALVIS;
    }
}
