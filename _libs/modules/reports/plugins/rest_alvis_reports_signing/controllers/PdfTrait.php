<?php

use Nzoom\Pdf\Processor;
use setasign\Fpdi\Fpdi;
use setasign\Fpdi\PdfParser\PdfParserException;
use setasign\Fpdi\PdfReader\PdfReaderException;

/**
 * PDF Processing Trait for REST Alvis Reports Signing
 *
 * This trait provides comprehensive PDF processing capabilities for the REST Alvis Reports
 * signing system. It handles PDF merging, validation, watermarking, and certificate
 * attachment operations specifically designed for comparative analysis reports.
 *
 * Key Features:
 * - PDF file merging with validation
 * - File integrity checking (missing/broken files detection)
 * - Draft watermarking capabilities
 * - Certificate attachment to final reports
 * - Support for comparative table types (market, revenue, cost)
 *
 * @package RestAlvisReportsSigning
 * <AUTHOR> Development Team
 * @version 1.0
 * @since   PHP 8.0
 *
 * @uses    Nzoom\Pdf\Processor For advanced PDF processing operations
 * @uses    setasign\Fpdi\Fpdi For low-level PDF manipulation
 */
trait PdfTrait
{
    // ========================================
    // FILE INTEGRITY TRACKING PROPERTIES
    // ========================================

    /**
     * Flag indicating whether any files are missing from the expected file set
     *
     * This property tracks the state of file availability during the PDF processing
     * workflow. When set to true, it indicates that one or more expected PDF files
     * could not be found at their specified paths, which may affect the completeness
     * of the final merged document.
     *
     * @var bool Default: false (no missing files detected)
     */
    private bool $hasMissingFiles = false;

    /**
     * Flag indicating whether any files are corrupted or invalid
     *
     * This property tracks the integrity of PDF files during validation. When set
     * to true, it indicates that one or more PDF files failed validation checks,
     * are corrupted, or cannot be properly processed. This helps ensure document
     * quality and prevents inclusion of damaged files in the final output.
     *
     * @var bool Default: false (no broken files detected)
     */
    private bool $hasBrokenFiles = false;

    // ========================================
    // FILE INTEGRITY STATUS METHODS
    // ========================================

    /**
     * Check if any PDF files are corrupted or failed validation
     *
     * This method provides a read-only check for the broken files status.
     * It's typically used before attempting PDF operations to ensure all
     * files are in a valid state for processing.
     *
     * @return bool True if broken files were detected, false otherwise
     */
    public function containsBrokenFiles(): bool
    {
        return $this->hasBrokenFiles;
    }

    /**
     * Set the broken files status flag
     *
     * This method is called during file validation to mark when corrupted
     * or invalid PDF files are encountered. Setting this to true will
     * typically prevent the merge operation from proceeding.
     *
     * @param bool $hasBrokenFiles True to indicate broken files detected
     *
     * @return void
     */
    public function setHasBrokenFiles(bool $hasBrokenFiles): void
    {
        $this->hasBrokenFiles = $hasBrokenFiles;
    }

    /**
     * Check if any expected PDF files are missing from the file system
     *
     * This method provides a read-only check for the missing files status.
     * It's used to determine if all required files are available before
     * attempting to merge them into a final document.
     *
     * @return bool True if missing files were detected, false otherwise
     */
    public function containsMissingFiles(): bool
    {
        return $this->hasMissingFiles;
    }

    /**
     * Set the missing files status flag
     *
     * This method is called during file collection to mark when expected
     * PDF files cannot be found at their specified paths. This helps
     * track incomplete document sets.
     *
     * @param bool $hasMissingFiles True to indicate missing files detected
     *
     * @return void
     */
    public function setHasMissingFiles(bool $hasMissingFiles): void
    {
        $this->hasMissingFiles = $hasMissingFiles;
    }

    // ========================================
    // REPORT TYPE CONFIGURATION
    // ========================================

    /**
     * Comparative table analog types mapping for report categorization
     *
     * This array defines the supported report types for comparative analysis:
     * - 41: Cost analysis reports (financial cost comparisons)
     * - 33: Market analysis reports (market trend comparisons)
     * - 36: Revenue analysis reports (revenue performance comparisons)
     *
     * These type IDs are used throughout the system to categorize and organize
     * PDF files by their analytical purpose, ensuring proper grouping during
     * the merge process.
     *
     * @var array<int> Array of supported comparative table type IDs
     */
    private array $comparativeTableTypes = [41, 33, 36];

    // ========================================
    // FILE COLLECTION AND ORGANIZATION
    // ========================================

    /**
     * Collection of PDF file paths ready for merging operations
     *
     * This array contains the absolute file paths of all PDF files that have
     * been validated and are ready to be merged into a single document. Files
     * are added to this collection after passing integrity checks and validation.
     * The order of files in this array determines their order in the final merged PDF.
     *
     * @var array<string> Array of validated PDF file paths for merging
     */
    private array $collectedFilesForMerging = [];

    /**
     * Get the collection of PDF files ready for merging
     *
     * This method returns the current state of the collectedFilesForMerging
     * array, providing read-only access to inspect the files that will be
     * processed in the next merge operation. The returned array preserves
     * the processing order and can be used for validation, logging, or
     * user interface display.
     *
     * @return array<string> Array of PDF file paths in processing order
     */
    public function getCollectedFilesForMerging(): array
    {
        return $this->collectedFilesForMerging;
    }

    /**
     * Set the collection of PDF files ready for merging
     *
     * This method replaces the entire current file collection with a new set
     * of PDF files, providing a clean slate for batch operations. The method
     * is designed for scenarios where you need to completely change the
     * processing context or start a new operation with different files.
     *
     * @param array<string> $collectedFilesForMerging Array of PDF file paths to set as the processing collection
     *
     * @return void
     */
    public function setCollectedFilesForMerging(array $collectedFilesForMerging): void
    {
        $this->collectedFilesForMerging = $collectedFilesForMerging;
    }

    /**
     * Add a single PDF file to the collection for merging
     *
     * This method appends a single file to the existing collection, maintaining
     * the current order while adding new files to the end of the processing queue.
     * It's ideal for building file collections incrementally or adding files
     * based on dynamic conditions.
     *
     * @param string $file PDF file path to add to the processing collection
     *
     * @return void
     */
    public function addFileForMerging(string $file): void
    {
        $this->collectedFilesForMerging[] = $file;
    }

    /**
     * Prepend a single PDF file to the collection for merging
     *
     * This method adds a single file to the beginning of the existing collection,
     * effectively changing the order of processing. It's useful when you need
     * to prioritize certain files or when the order of files is critical for
     * the final document structure.
     *
     * @param string $file PDF file path to prepend to the processing collection
     *
     * @return void
     */
    public function prependFileForMerging(string $file): void
    {
        array_unshift($this->collectedFilesForMerging, $file);
    }

    /**
     * Remove the first PDF file from the collection for merging
     *
     * This method removes the first file from the processing collection,
     * effectively changing the order of processing. It's useful when you need
     * to remove the first file from the collection, such as when it's a cover
     * page that needs to be processed separately.
     *
     * @return void
     */
    public function removeFirstFileForMerging(): void
    {
        array_shift($this->collectedFilesForMerging);
    }

    /**
     * Organized file storage by report type
     *
     * This multi-dimensional array organizes PDF files by their comparative
     * table type. Each key corresponds to a report type ID (41, 33, 36) and
     * contains an array of file paths for that specific type. This structure
     * allows for type-specific processing and ensures proper categorization
     * during document generation.
     *
     * Structure:
     * - 41 => [array of cost analysis PDF files]
     * - 33 => [array of market analysis PDF files]
     * - 36 => [array of revenue analysis PDF files]
     *
     * @var array<int, array<string>> Multi-dimensional array of files by type
     */
    private array $files = [
        // Cost analysis files
        41 => [],
        // Market analysis files
        33 => [],
        // Revenue analysis files
        36 => [],
    ];

    // ========================================
    // FILE ORGANIZATION METHODS
    // ========================================

    /**
     * Get the organized file collection by report type
     *
     * This method returns the current state of the files array, providing
     * read-only access to inspect the files that have been organized by
     * their comparative table type. The returned array preserves the
     * type-specific organization and can be used for validation, logging,
     * or user interface display.
     *
     * @param int|null $key Optional report type ID to retrieve specific files
     *
     * @return array<int, array<string>> Multi-dimensional array of files by type
     *                                   or specific array of files for a given type
     */
    public function getFiles(?int $key = null): array
    {
        if ($key !== null) {
            return $this->files[$key] ?? [];
        }
        return $this->files;
    }

    /**
     * Add a file to the organized file collection by report type
     *
     * This method adds a file to the specific array within the files collection
     * that corresponds to the provided report type ID. It's used to build the
     * organized file structure incrementally, typically based on the report type
     * of each file as determined during the processing workflow.
     *
     * @param int $relatedDocType      Report type ID (41, 33, 36)
     * @param int $comparativeObjectId Comparative object ID associated with the file
     *
     * @return void
     */
    public function addToFiles(int $relatedDocType, int $comparativeObjectId): void
    {
        $this->files[$relatedDocType][] = $comparativeObjectId;
    }

    // ========================================
    // ERROR TRACKING COLLECTIONS
    // ========================================

    /**
     * Collection of file paths that could not be found
     *
     * This array maintains a record of all PDF files that were expected but
     * could not be located at their specified paths. Each entry contains the
     * full file path that was attempted. This information is used for error
     * reporting and debugging missing file issues.
     *
     * @var array<string> Array of missing file paths
     */
    private array $missingFiles = [];

    /**
     * Collection of file paths that failed validation or are corrupted
     *
     * This array maintains a record of all PDF files that were found but
     * failed validation checks, are corrupted, or cannot be properly processed.
     * Each entry contains the full file path and may include additional error
     * information. This helps track document quality issues.
     *
     * @var array<string> Array of broken/invalid file paths
     */
    private array $brokenFiles = [];

    // ========================================
    // CORE PDF PROCESSING METHODS
    // ========================================

    /**
     * Merge multiple PDF files into a single consolidated document
     *
     * This method performs the core PDF merging operation using the enhanced
     * Nzoom PDF Processor. It takes all files from the collectedFilesForMerging
     * array and combines them into a single PDF document at the specified output path.
     *
     * The merge process includes:
     * - Validation of input files before merging
     * - Proper error handling and exception propagation
     * - Cleanup of temporary file references after successful merge
     *
     * Note: Draft file watermarking functionality is currently disabled pending
     * implementation fixes. See mergeDraftAnalogFiles() method for details.
     *
     * @param string $filePath Absolute path where the merged PDF should be saved
     *
     * @return void
     * @throws Exception When no files are available for merging or when the merge operation fails
     *
     * @see Processor::merge() For the underlying merge implementation
     * @see mergeDraftAnalogFiles() For draft watermarking (currently disabled)
     */
    private function merge(string $filePath): void
    {
        // Validate that we have files to merge
        if (empty($this->getCollectedFilesForMerging())) {
            throw new Exception('No files to merge');
        }

        try {
            // Initialize the PDF processor with our file collection
            $pdfMerge = new Processor();
            $pdfMerge->setFiles($this->flatCollectedFilesForMerging($this->getCollectedFilesForMerging()));

            // Perform the merge operation
            $pdfMerge->merge($filePath);
        } catch (RuntimeException | Exception $e) {
            throw new Exception($e->getMessage());
        }

        // TODO: Fix this commented code so it works. Add watermark to draft files
        // Draft watermarking is currently disabled due to implementation issues
        // The mergeDraftAnalogFiles method needs to be updated to work with the new
        // PDF processor architecture before this functionality can be re-enabled
        //
        // if (strpos(basename($fileName), 'draft') !== false) {
        //     try {
        //         $this->mergeDraftAnalogFiles($fileName);
        //     } catch (PdfParserException | PdfReaderException $e) {
        //         echo $e->getMessage();
        //         return;
        //     }
        // }

        // Clean up the first element (legacy behavior - may need review)
        $this->removeFirstFileForMerging();
    }

    /**
     * Flatten a multi-dimensional array of file paths
     *
     * This utility method recursively flattens a multi-dimensional array of
     * file paths into a single-dimensional array. It's used to transform the
     * organized file structure (by report type) into a format suitable for
     * the PDF processor's setFiles() method, which expects a simple array
     * of file paths.
     *
     * @param array $array Multi-dimensional array of file paths
     *
     * @return array<string> Flattened array of file paths
     */
    public function flatCollectedFilesForMerging(array $array): array
    {
        $paths = [];

        foreach ($array as $value) {
            if (is_array($value)) {
                $paths = array_merge($paths, $this->flatCollectedFilesForMerging($value));
            } elseif (is_string($value)) {
                $paths[] = $value;
            }
        }

        return $paths;
    }

    // ========================================
    // DRAFT WATERMARKING METHODS (DISABLED)
    // ========================================

    /**
     * Merge draft analog files with watermark overlay (CURRENTLY DISABLED)
     *
     * This method was designed to process draft PDF files by adding watermarks
     * to indicate their draft status. However, the implementation is currently
     * incomplete and disabled due to integration issues with the new PDF processor
     * architecture.
     *
     * Original intended functionality:
     * - Skip files already marked as drafts
     * - Process each page of non-draft files
     * - Add "DRAFT" watermark overlay to each page
     * - Output the watermarked result to the specified file
     *
     * TODO: Complete implementation requirements:
     * 1. Update to work with Nzoom\Pdf\Processor instead of legacy Pdf_Processor
     * 2. Implement proper page iteration and watermark application
     * 3. Add error handling for watermarking operations
     * 4. Test watermark positioning and appearance
     * 5. Integrate with the main merge workflow
     *
     * @param string $fileName Output file path for the watermarked PDF
     *
     * @return void
     *
     * @todo       Implement proper watermarking with new PDF processor architecture
     * @deprecated This method is currently disabled and needs refactoring
     */
    private function mergeDraftAnalogFiles(string $fileName): void
    {
        // Initialize FPDI for low-level PDF manipulation
        // Note: This should eventually be replaced with Nzoom\Pdf\Processor
        $pdfFile = new Fpdi();

        // Process each file in the collection
        foreach ($this->getCollectedFilesForMerging() as $pdf) {
            // Skip files that are already marked as drafts
            if (strpos(basename($pdf), 'draft') !== false) {
                continue;
            }

            // TODO: Implement the following watermarking logic:
            // 1. Set source file and get page count
            // 2. Iterate through each page
            // 3. Import page template
            // 4. Apply watermark overlay
            // 5. Position and style the watermark text

            // $pageCount = $pdfFile->setSourceFile($pdf);
            // for ($pageNumber = 1; $pageNumber <= $pageCount; $pageNumber++) {
            //     $pdfFile->AddPage();
            //     $templateId = $pdfFile->importPage($pageNumber);
            //     $pdfFile->useTemplate($templateId);
            //     $pdfFile->SetFont('Times', 'B', 100);
            //     $pdfFile->SetTextColor(255, 0, 0);
            //     $pdfFile->SetXY(400, 30);
            //     $this->addWatermarkToPdf(55, 600, 100, 'DRAFT', $pdfFile);
            // }
        }

        // Output the processed file
        $pdfFile->Output('F', $fileName);

        // Clean up resources and reset state
        unset($pdfFile);
        $this->setCollectedFilesForMerging([]);
    }

    // ========================================
    // WATERMARK UTILITY METHODS
    // ========================================

    /**
     * Add rotated watermark text to a PDF page using FPDI
     *
     * This utility method applies a rotated text watermark to a PDF page using
     * low-level FPDI operations. It handles coordinate transformation and rotation
     * calculations to position the watermark text at the specified angle and location.
     *
     * The method performs the following operations:
     * - Calculates default positioning if coordinates are not specified
     * - Handles rotation state management for the PDF canvas
     * - Applies coordinate transformation matrix for text rotation
     * - Renders the watermark text at the calculated position
     *
     * Mathematical transformations:
     * - Converts angle from degrees to radians
     * - Calculates cosine and sine for rotation matrix
     * - Applies coordinate system transformation
     * - Positions text relative to the rotation center
     *
     * @param int       $angle   Rotation angle in degrees (default: 55°)
     * @param int       $x       X coordinate for watermark (-1 for current position)
     * @param int       $y       Y coordinate for watermark (-1 for current position)
     * @param string    $text    Text content for the watermark
     * @param Fpdi|null $pdfFile FPDI instance to apply watermark to
     *
     * @return void
     *
     * @todo Consider migrating to Nzoom\Pdf\Processor for consistency
     * @see  mergeDraftAnalogFiles() For usage context (currently disabled)
     */
    private function addWatermarkToPdf(
        int $angle = 55,
        int $x = -1,
        int $y = -1,
        string $text = '',
        ?Fpdi $pdfFile = null
    ): void {
        // Use current position if coordinates not specified
        if ($x == -1) {
            $x = $pdfFile->x;
        }
        if ($y == -1) {
            $y = $pdfFile->y;
        }

        // Reset any existing rotation state
        if ($pdfFile->angle != 0) {
            $pdfFile->_out('Q');
        }

        // Set the new rotation angle
        $pdfFile->angle = $angle;

        // Apply rotation transformation if angle is specified
        if ($angle != 0) {
            // Convert degrees to radians for trigonometric calculations
            $angle *= M_PI / 180;
            $c = cos($angle);
            $s = sin($angle);

            // Calculate transformation coordinates
            $cx = $x * $pdfFile->k;
            $cy = ($pdfFile->h - $y) * $pdfFile->k;

            // Output PDF transformation matrix for rotation
            $pdfFile->_out(
                sprintf(
                    'q %.5f %.5f %.5f %.5f %.2f %.2f cm 1 0 0 1 %.2f %.2f cm',
                    $c,    // Cosine component
                    $s,    // Sine component
                    -$s,   // Negative sine component
                    $c,    // Cosine component
                    $cx,   // X translation
                    $cy,   // Y translation
                    -$cx,  // Negative X translation
                    -$cy   // Negative Y translation
                )
            );
        }

        // Render the watermark text at the transformed position
        $pdfFile->Text(0, 0, $text);
    }

    // ========================================
    // CERTIFICATE ATTACHMENT METHODS
    // ========================================

    /**
     * Append a customer certificate image to the end of a final PDF report
     *
     * This method adds a certificate page to an existing PDF document by:
     * 1. Loading the existing PDF and preserving all original pages
     * 2. Copying each page with its original dimensions and orientation
     * 3. Adding a new page containing the certificate image
     * 4. Scaling the certificate image to fill the entire page
     * 5. Saving the enhanced PDF back to the original file location
     *
     * The certificate is added as the final page of the document, maintaining
     * the integrity of the original report content while providing official
     * certification or validation imagery.
     *
     * Image handling:
     * - Certificate image is scaled to match the full page dimensions
     * - Aspect ratio may be altered to fit the page completely
     * - Supports common image formats (PNG, JPEG, etc.)
     *
     * Error handling:
     * - PDF parsing errors are propagated to the caller
     * - File I/O errors during save operations are handled by FPDI
     * - Invalid image files may cause runtime exceptions
     *
     * @param string $certificateImagePath Absolute path to the certificate image file
     * @param string $pdfFilePath          Absolute path to the PDF file to modify
     *
     * @return void
     * @throws PdfParserException When the PDF file cannot be parsed or is corrupted
     * @throws PdfReaderException When the PDF file cannot be read or accessed
     *
     * @see Fpdi::setSourceFile() For PDF loading implementation
     * @see Fpdi::Image() For image embedding functionality
     */
    private function addCertificateToPdf(string $certificateImagePath, string $pdfFilePath): void
    {
        // Initialize FPDI for PDF manipulation
        $fpdi = new Fpdi();

        // Load the existing PDF and get page count
        $pageCount = $fpdi->setSourceFile($pdfFilePath);

        // Copy all existing pages to preserve original content
        for ($i = 1; $i <= $pageCount; $i++) {
            // Import the page template
            $templateId = $fpdi->importPage($i);

            // Get original page dimensions and orientation
            $size = $fpdi->getTemplateSize($templateId);

            // Create new page with original dimensions
            $fpdi->AddPage($size['orientation'], [$size['width'], $size['height']]);

            // Apply the original page content
            $fpdi->useTemplate($templateId);
        }

        // Add a new page for the certificate
        $fpdi->AddPage();

        // Embed the certificate image to fill the entire page
        // Note: This scales the image to page dimensions, potentially altering aspect ratio
        $fpdi->Image(
            $certificateImagePath,    // Image file path
            0,                        // X position (left edge)
            0,                        // Y position (top edge)
            $fpdi->GetPageWidth(),    // Width (full page width)
            $fpdi->GetPageHeight()    // Height (full page height)
        );

        // Save the enhanced PDF back to the original file location
        $fpdi->Output($pdfFilePath, 'F');
    }
}
