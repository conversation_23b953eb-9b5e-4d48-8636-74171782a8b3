<?php

use PhpOffice\PhpSpreadsheet\IOFactory;

class Custom_Report_Controller extends Reports_Controller {

    public $current_report = '';
    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'custom_send':
                $this->_customSend();
                break;
            case 'edit_orders':
                $this->_editOrders();
                break;
            default:
                parent::execute();
        }
    }

    /*
     * Function to create debit note in the selected invoice
     */
    public function _customSend() {
        set_time_limit(0);
        $this->getReportSettings();

        // include the report class
        require_once PH_MODULES_DIR . "reports/plugins/" . $this->current_report['name'] . "/custom.report.query.php";

        // form the name of the required report class
        $report_name_elements = explode('_', $this->current_report['name']);
        foreach ($report_name_elements as $key => $element) {
            $report_name_elements[$key] = ucfirst($element);
        }
        $report_class_name = implode ('_', $report_name_elements);

        $report_class_name::$registry = $this->registry;
        $report_class_name::$filters = array('order_ids' => $this->registry['request']->get('order_ids'));

        // get reports results
        $report_results = $report_class_name::getResults();

        $this->registry['db']->StartTrans();
        $operation_result = $this->processDelivererEmail($report_results);

        if ($operation_result['result']) {
            foreach ($operation_result['messages'] as $msg) {
                $this->registry['messages']->setMessage($msg);
            }
            $this->registry['messages']->insertInSession($this->registry);
        } else {
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();

        echo(json_encode($operation_result));
        exit;
    }

    /*
     * Function to edit the dates of the selected orders
     */
    public function _editOrders()
    {
        $success_update = 0;
        $failed_updates = array();
        $this->getReportSettings();

        $order_ids = array_flip($this->registry['request']->get('order_ids'));
        $glazing_dates = $this->registry['request']->get('glazing_date');
        $deliverers = $this->registry['request']->get('deliverer');
        $deliverers_names = $this->registry['request']->get('deliverer_autocomplete');
        $glazings_to_update = array_intersect_key($glazing_dates, $order_ids);
        $deliverers = array_intersect_key($deliverers, $order_ids);

        $orders = Documents::search($this->registry, [
            'where' => ["d.id IN (" . implode(',', array_keys($glazings_to_update)) . ")"]
        ]);

        $gov = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);

        foreach ($orders as $order) {

            $order->unsanitize();
            $assoc_vars = $order->getAssocVars();

            $update_model = false;

            if (isset($assoc_vars[DOCUMENTS_GLAZING_DATE]) &&
                $assoc_vars[DOCUMENTS_GLAZING_DATE]['value'] != $glazings_to_update[$order->get('id')]) {
                $assoc_vars[DOCUMENTS_GLAZING_DATE]['value'] = $glazings_to_update[$order->get('id')];
                $update_model = true;
            }
            if (isset($assoc_vars[DOCUMENTS_SUPPLIER]) &&
                $assoc_vars[DOCUMENTS_SUPPLIER]['value'] != $deliverers[$order->get('id')]) {
                $assoc_vars[DOCUMENTS_SUPPLIER]['value'] = $deliverers[$order->get('id')];
                $assoc_vars[DOCUMENTS_SUPPLIER_NAME]['value'] = $deliverers_names[$order->get('id')];
                $update_model = true;
            }

            if (!$update_model) {
                // nothing to update
                continue;
            }

            $old_order = clone $order;
            set_time_limit(10);
            $this->registry['db']->StartTrans();
            $assoc_vars[DOCUMENTS_GLAZING_DATE]['value'] = $glazings_to_update[$order->get('id')];
            $order->set('assoc_vars', $assoc_vars, true);
            $order->set('vars', array_values($assoc_vars), true);

            if ($order->save()) {
                $filters = array(
                    'where' => array('d.id = ' . $order->get('id')),
                    'model_lang' => $order->get('model_lang')
                );
                $new_order = Documents::searchOne($this->registry, $filters);
                $new_order->getVars();

                $params_history = array(
                    'action_type' => 'edit',
                    'old_model'   => $old_order,
                    'model'       => $new_order,
                    'new_model'   => $new_order
                );
                Documents_History::saveData($this->registry, $params_history);
                $success_update++;
            } else {
                $failed_updates[] = sprintf($this->i18n('warning_reports_warning_order_edit_failed'), $order->get('full_num'));
                $this->registry['db']->FailTrans();
            }
            $this->registry['db']->CompleteTrans();
        }

        if ($success_update) {
            $this->registry['messages']->setMessage(sprintf($this->i18n('message_reports_orders_updated_successfully'), $success_update));
        }
        foreach ($failed_updates as $upd) {
            $this->registry['messages']->setWarning($upd);
        }

        $this->registry->set('get_old_vars', $gov, true);
        $this->registry->set('edit_all', $edit_all, true);
        $this->registry['messages']->insertInSession($this->registry);
        echo(json_encode(array()));
        exit;
    }

    /**
     * @return void
     */
    private function getReportSettings(): void {
        $this->current_report = $this->getReportType();

        if (empty($this->current_report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $this->report_name = $this->current_report['name'];

        //load plugin i18n files
        $i18n_files = array();
        $i18n_files[] = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $this->report_name,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');

        // Load the documents main i18n file
        $i18n_files[] = sprintf('%s%s%s%s',
            PH_MODULES_DIR,
            'documents/i18n/',
            $this->registry['lang'],
            '/documents.ini');
        $this->registry['translater']->loadFile($i18n_files);

        Reports::getReportSettings($this->registry, $this->report_name);
    }

    /**
     * @param $report_results
     * @return array $operation_result
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    private function processDelivererEmail($report_results): array
    {
        // get request and all post params
        $operation_result = array(
            'result'   => true,
            'messages' => array()
        );

        if (empty($report_results)) {
            $operation_result['result'] = false;
            $operation_result['messages'][] = $this->registry['translater']->translate('error_no_reports_results');
            return $operation_result;
        }

        if (!EMAIL_ID) {
            $operation_result['result'] = false;
            $operation_result['messages'][] = $this->registry['translater']->translate('error_no_set_email');
            return $operation_result;
        }

        $filters = array(
            'where' => array('e.id = ' . EMAIL_ID),
            'sanitize' => true
        );
        $mail = Emails::searchOne($this->registry, $filters);
        if (!$mail) {
            $operation_result['result'] = false;
            $operation_result['messages'][] = $this->registry['translater']->translate('error_no_set_email');
            return $operation_result;
        }

        // prepare the excel file
        $template_file_path = dirname(__FILE__) . "/../_deliverer_glazing_orders_template.xlsx";
        $inputFileType = IOFactory::identify($template_file_path);
        $reader = IOFactory::createReader($inputFileType);

        $template = $reader->load($template_file_path);
        $sheet = $template->getActiveSheet();

        $row = 4;
        $first_row = reset($report_results);
        $deliverer_name = $first_row['deliverer_name'];
        $deliverer_id = $first_row['deliverer'];
        $deliverer_emails = [];
        foreach ($first_row['deliverer_email'] as $email) {
            $deliverer_emails[] = $deliverer_name . ' <' . $email . '>';
        }

        $included_glazings = false;
        uasort($report_results, function ($a, $b) { return $a['full_num'] > $b['full_num'] ? 1 : -1; } );
        $glazing_notes = $this->registry['request']->get('glazing_note');

        foreach ($report_results as $rep_res) {
            if (empty($rep_res['glazings'])) {
                // skip if there are no glazings
                continue;
            }
            $included_glazings = true;
            $sheet->setCellValue("A1", $deliverer_name);
            $iteration = 1;
            foreach ($rep_res['glazings'] as $glazing) {
                $sheet->setCellValue("A{$row}", $rep_res['full_num']);
                $sheet->setCellValue("B{$row}", $glazing['glazing_code']);
                $date = '';
                if (preg_match('#\d{4}-\d{2}-\d{2}#', $rep_res['glazing_date'])) {
                    $date = new DateTime($rep_res['glazing_date']);
                    $date = $date->format('d.m.Y');
                }
                $sheet->setCellValue("C{$row}", $glazing['length']);
                $sheet->setCellValue("D{$row}", $glazing['height']);
                $sheet->setCellValue("E{$row}", $glazing['count_num']);
                $sheet->setCellValue("F{$row}", sprintf('%.2f', round($glazing['area'], 2)));
                $sheet->setCellValue("G{$row}", ($rep_res['type'] == DOCUMENTS_TYPE_ACCESSORIES_ORDER ? '' :implode('/', array_filter([$rep_res['custom_num'], $glazing['module'], $glazing['cell']]))));
                $sheet->setCellValue("H{$row}", $glazing['module_note']);
                $sheet->setCellValue("I{$row}", $date);

                $row_index = sprintf('%d_%d', $rep_res['id'], $glazing['row_num']);
                $sheet->setCellValue("J{$row}", (isset($glazing_notes[$row_index]) ? $glazing_notes[$row_index] : ''));

                if ($iteration == count($rep_res['glazings'])) {
                    $row++;
                    $sheet->setCellValue("E{$row}", $rep_res['total_count']);
                    $sheet->getStyle("E{$row}")->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_RED);
                    $sheet->getStyle("E{$row}")->getFont()->setBold(true);
                    $sheet->getStyle("E{$row}")->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
                    $sheet->setCellValue("F{$row}", sprintf('%.2f', round($rep_res['total_area'], 2)));
                    $sheet->getStyle("F{$row}")->getFont()->getColor()->setARGB(\PhpOffice\PhpSpreadsheet\Style\Color::COLOR_RED);
                    $sheet->getStyle("F{$row}")->getFont()->setBold(true);
                    $sheet->getStyle("F{$row}")->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
                    $row++;
                }
                $row++;
                $iteration++;
            }
        }

        $tmp_name = '';
        try {
            if ($included_glazings) {
                $sheet->getStyle('A3:J' . $sheet->getHighestRow())->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
                $sheet->setSelectedCell('A1');

                // Replace any bad characters from the filename
                $filename = Transliterate::convert($deliverer_name);
                $filename = preg_replace('#[\(\)\&\.\'\"]#', '', trim($filename));

                // Replace spaces with _ and add the XLS extension
                $filename = preg_replace('#\s+#', '_', $filename);
                $tmp_name = sprintf('%s_%s.xlsx', date('Y-m-d'), $filename);

                $writer = IOFactory::createWriter($template, $inputFileType);
                $writer->save($tmp_name);
                $template->disconnectWorksheets();
                unset($template);
            }
        } catch (Exception $e) {
            $operation_result['result'] = false;
            $operation_result['messages'][] = $this->registry['translater']->translate('error_reports_export_failed');
        }

        if (!$operation_result['result']) {
            // return error
            return ($operation_result);
        }

        // get the customer model
        // main panel translations
        $filters = array(
            'where' => array('c.id = ' . $deliverer_id),
            'model_lang' => $this->registry['lang']
        );
        $customer = Customers::searchOne($this->registry, $filters);

        $files_to_attach_to_customer = array();
        if ($tmp_name) {
            $files_to_attach_to_customer[] = array(
                'filename' => $tmp_name,
                'tmp_name' => $tmp_name,
            );
        }

        foreach ($report_results as $rep_res) {
            if (empty($rep_res['files'])) {
                continue;
            }
            foreach ($rep_res['files'] as $fl) {
                if ($fl->get('not_exist')) {
                    continue;
                }
                $files_to_attach_to_customer[] = array(
                    'filename' => $fl->get('filename'),
                    'tmp_name' => $fl->get('path')
                );
            }
        }

        $attached_files = [];
        foreach ($files_to_attach_to_customer as $new_file) {
            $file = array(
                'name' => $new_file['filename'],
                'type' => mime_content_type($new_file['tmp_name']),
                'tmp_name' => $new_file['tmp_name'],
                'size' => filesize($new_file['tmp_name'])
            );
            $file_params = array(
                'name' => $new_file['filename'],
                'filename' => $new_file['filename'],
                'permission' => 'all'
            );

            $new_file = Files::attachFile($this->registry, $file, $file_params, $customer);
            if (!$new_file) {
                $operation_result['result'] = false;
                break;
            }
            $attached_files[] = 'file_' . $new_file;
        }
        if ($tmp_name) {
            unlink($tmp_name);
        }

        if (!$operation_result['result']) {
            $operation_result['messages'][] = sprintf($this->registry['translater']->translate('error_attach_file_to_customer_failed'), $deliverer_name);
            return $operation_result;
        }

        $customer->getPatternsVars();
        $customer->extender = new Extender();
        $customer->extender->model_lang = $customer->get('model_lang');
        $customer->extender->add('report_order_nums', implode(', ', array_column($report_results, 'full_num')));

        $customer->set('attached_files', $attached_files);
        $customer->set('customer_email', $deliverer_emails, true);
        $customer->set('body', $customer->extender->expand($mail->get('body')), true);
        $customer->set('email_template', $mail->get('id'), true);

        $customer->set('custom_replyto', $this->registry['currentUser']->get('email'), true);
        $customer->set('custom_replyto_name', ($this->registry['currentUser']->get('display_name') ?
            $this->registry['currentUser']->get('display_name') :
            $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname')), true);

        $this->setAutomationUserAsCurrent();
        $customer->set('custom_sender', $this->registry['config']->getParam('emails', 'from_email'), true);
        $customer->set('custom_from_name', ($this->registry['currentUser']->get('display_name') ?
            $this->registry['currentUser']->get('display_name') :
            $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname')), true);
        if (!$customer->sendAsMail()) {
            $operation_result['result'] = false;
            $operation_result['messages'][] = sprintf($this->registry['translater']->translate('error_sending_email'), $deliverer_name);
            return $operation_result;
        }
        $this->setOriginalUserAsCurrent();

        Customers_History::saveData(
            $this->registry,
            array(
                'model' => $customer,
                'action_type' => 'email',
                'old_model' => $customer,
                'new_model' => $customer,
            )
        );

        // go through all the orders and tag them
        $filters = array('where'      => array('d.id IN (' . implode(',', array_keys($report_results)) . ')'),
                         'model_lang' => $this->registry['lang']);
        $documents_list = Documents::search($this->registry, $filters);

        foreach ($documents_list as $doc_model) {
            $old_document = clone $doc_model;
            $old_document->getModelTagsForAudit();
            $old_document->sanitize();

            $doc_model->unsanitize();
            $doc_model->getTags();
            $tags = $doc_model->get('tags');
            $tags[] = TAG_SENT_TO_DELIVERER;
            $doc_model->set('tags', $tags, true);

            if ($doc_model->updateTags(array('skip_permissions' => true))) {
                // get the updated document
                $filters = array('where'      => array('d.id="' . $doc_model->get('id') . '"'),
                                 'model_lang' => $this->registry['lang']);
                $new_document = Documents::searchOne($this->registry, $filters);
                $new_document->getModelTagsForAudit();
                $new_document->sanitize();

                Documents_History::saveData($this->registry, array('model' => $new_document, 'action_type' => 'tag', 'new_model' => $new_document, 'old_model' => $old_document));
            } else {
                $operation_result['result'] = false;
                $operation_result['messages'][] = sprintf($this->registry['translater']->translate('error_tag_order'), $doc_model->get('full_num'));
            }
        }

        if ($operation_result['result']) {
            $operation_result['messages'][] = sprintf($this->registry['translater']->translate('message_success_email_sent_to_deliverer'), $deliverer_name);
        }

        return $operation_result;
    }


}

?>
