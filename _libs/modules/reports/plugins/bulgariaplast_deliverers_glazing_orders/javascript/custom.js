/**
 * custom javascript messages
 */
if (env.current_lang == 'bg') {
    i18n['messages']['error_select_at_least_order'] = 'Моля, изберете поне една поръчка!';
    i18n['messages']['error_select_at_least_one_order_to_edit'] = 'Моля, изберете поне една поръчка за редакция!';
    i18n['messages']['error_select_orders_only_one_customer'] = 'Моля, изберете поръчки към един единствен доставчик!';
    i18n['messages']['error_not_completed_email'] = 'За избрания доставчик няма попълнен e-mail адрес!';1
} else {
    i18n['messages']['error_select_at_least_order'] = 'Please, select at least one order!';
    i18n['messages']['error_select_at_least_one_order_to_edit'] = 'Please, select at least one order to edit!';
    i18n['messages']['error_select_orders_only_one_customer'] = 'Please, select orders for only one deliverer!';
    i18n['messages']['error_not_completed_email'] = 'The selected deliverer does not have an e-mail address!';
}


function toggleAllCheckboxes(element) {
    button_source = element.id.replace(/select_all_/, '');
    let checkboxes = document.querySelectorAll('[type=checkbox][id^=' + button_source + '_]');
    for (i=0; i<checkboxes.length; i++) {
        checkboxes[i].checked = element.checked;
    }
}

function sendFiles(element) {
    var form = element.form;

    let selected_fields = document.querySelectorAll('[type=checkbox][id^=ord_]:checked');
    if (!selected_fields.length) {
        alert(i18n['messages']['error_select_at_least_order']);
        return false;
    }

    // check if there is only one deliverer selected
    let deliverers = [];
    let emails = [];
    for (i=0; i<selected_fields.length; i++) {
        let order_id = selected_fields[i].id.replace(/ord_/, '');
        let deliverer_field = document.querySelector('#dlvr_' + order_id);
        if (deliverer_field && !in_array(deliverer_field.value, deliverers)) {
            deliverers.push(deliverer_field.value);
        }
        let email_fields = document.querySelectorAll('span[id^="email_' + order_id + '_"]');
        for (j=0; j<email_fields.length; j++) {
            if (email_fields[j] && email_fields[j].innerHTML.trim()) {
                emails.push(email_fields[j].innerHTML.trim());
            }
        }
    }

    if (deliverers.length>1) {
        alert(i18n['messages']['error_select_orders_only_one_customer']);
        return false;
    }
    if (!emails.length) {
        alert(i18n['messages']['error_not_completed_email']);
        return false;
    }

    // if no rows are selected no action could be taken
    // prepare ajax options
    preventResubmit(form, true);
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=custom_send&report_type=' + $('report_type').value;

    fetch(url, { method: 'POST', body: new FormData(form)})
        .then(response => response.json())
        .then(data => {
            if (data.result) {
                window.open(env.base_url + '?' + env.module_param + '=reports&report_type=' + $('report_type').value, '_self');
            } else {
                Effect.Fade('loading');
                alert(data['messages'].join("\n"));
                enableResubmit(element);
            }
        })
        .catch((err) => {
            alert('Error 404: location "' + err.toString() + '" was not found.');
            Effect.Fade('loading');
            enableResubmit(element);
        });
}

/*
 * Function to reenable the submit of the Forms
 */
function enableResubmit(element) {
    buttons = element.form.getElementsByTagName('button');
    for(var j = 0; j < buttons.length; j++) {
        removeClass(buttons[j], 'button_inactive');
        buttons[j].disabled = false;
    }
    hideLoading();
}

/*
 * Function that will edit the selected orders by submiting the form with ajax
 */
function editOrders(element) {
    var form = element.form;

    let selected_fields = document.querySelectorAll('[type=checkbox][id^=ord_]:checked');
    if (!selected_fields.length) {
        alert(i18n['messages']['error_select_at_least_one_order_to_edit']);
        return false;
    }

    // if no rows are selected no action could be taken
    // prepare ajax options
    preventResubmit(form, true);
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=reports&reports=edit_orders&report_type=' + $('report_type').value;

    fetch(url, { method: 'POST', body: new FormData(form)})
        .then(response => response.json())
        .then(data => {
            window.open(env.base_url + '?' + env.module_param + '=reports&report_type=' + $('report_type').value, '_self');
        })
        .catch((err) => {
            alert('Error 404: location "' + err.toString() + '" was not found.');
            Effect.Fade('loading');
            enableResubmit(element);
        });
}
