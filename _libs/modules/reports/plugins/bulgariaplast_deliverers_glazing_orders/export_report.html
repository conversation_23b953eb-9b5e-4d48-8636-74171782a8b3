<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  {literal}
    <style type="text/css">
      br { mso-data-placement: same-cell; }
    </style>
  {/literal}
</head>
<body>
  {if $reports_results}
    <table border="1">
      <tr>
        <td width="250"><strong>{#reports_deliverer#|escape}</strong></td>
        <td width="100"><strong>{#reports_order_num#|escape}</strong></td>
        <td width="80"><strong>{#reports_deliverer_num#|escape}</strong></td>
        <td width="80"><strong>{#reports_order_date#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_date#|escape}</strong></td>
        <td width="200"><strong>{#reports_customer#|escape}</strong></td>
        <td width="150"><strong>{#reports_city#|escape}</strong></td>
        <td width="150"><strong>{#reports_destination#|escape}</strong></td>
        <td width="150"><strong>{#reports_glazing_code#|escape}</strong></td>
        <td width="250"><strong>{#reports_glazing_name#|escape}</strong></td>
        <td width="150"><strong>{#reports_glazing_note#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_length#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_height#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_module#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_cell#|escape}</strong></td>
        <td width="150"><strong>{#reports_glazing_module_note#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_count#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_area#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_total_count#|escape}</strong></td>
        <td width="80"><strong>{#reports_glazing_total_area#|escape}</strong></td>
        <td width="200"><strong>{#reports_deliverer_email#|escape}</strong></td>
      </tr>
      {foreach from=$reports_results item='order'}
        {foreach from=$order.glazings item='glazing' name='glz'}
          <tr>
            <td style='mso-number-format: "\@";'>
              {$order.deliverer_name|escape}
            </td>
            <td style='mso-number-format: "\@";'>
              {$order.full_num|escape|default:#no_number#}
            </td>
            <td style='mso-number-format: "\@";'>
              {$order.custom_num|escape|default:#no_number#}
            </td>
            <td style="mso-number-format:'dd\.mm\.yyyy'">
              {$order.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'dd\.mm\.yyyy'">
              {$order.glazing_date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td style='mso-number-format: "\@";'>
              {$order.customer_name|escape|default:#no_number#}
            </td>
            <td style="mso-number-format:'\@';">
              {$order.city_name|escape}
            </td>
            <td style="mso-number-format:'\@';">
              {$order.destination_name|escape}
            </td>
            <td style='mso-number-format: "\@";'>
              {$glazing.glazing_code|escape|default:"&nbsp;"}
            </td>
            <td style='mso-number-format: "\@";'>
              {$glazing.glazing_name|escape|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'@';">
              {$glazing.note|escape|nl2br|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'0';">
              {$glazing.length|escape|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'0';">
              {$glazing.height|escape|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'0';">
              {$glazing.module|escape|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'0';">
              {$glazing.cell|escape|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'@';">
              {$glazing.module_note|escape|nl2br|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'0';">
              {$glazing.count_num|escape|default:"&nbsp;"}
            </td>
            <td style="mso-number-format:'0\.00';">
              {$glazing.area|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}
            </td>
            {if $smarty.foreach.glz.first}
              <td rowspan="{$order.rowspan}" style="mso-number-format:'0'; vertical-align: middle;">
                {if $smarty.foreach.glz.first}
                  <strong style="color: red;">{$order.total_count|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}</strong>
                {else}
                  &nbsp;
                {/if}
              </td>
              <td style="mso-number-format:'0\.00'; vertical-align: middle;" rowspan="{$order.rowspan}" >
                {if $smarty.foreach.glz.first}
                  <strong style="color: red;">{$order.total_area|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}</strong>
                {else}
                  &nbsp;
                {/if}
              </td>
            {/if}
            <td style="mso-number-format:'\@';">
              {foreach from=$order.deliverer_email item='dlv_email' name='de'}
                {$dlv_email|escape}</span>{if !$smarty.foreach.de.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
          </tr>
        {foreachelse}
          <td style='mso-number-format: "\@";'>
            {$order.deliverer_name|escape}
          </td>
          <td style='mso-number-format: "\@";'>
            {$order.full_num|escape|default:#no_number#}
          </td>
          <td style='mso-number-format: "\@";'>
            {$order.custom_num|escape|default:#no_number#}
          </td>
          <td style="mso-number-format:'dd\.mm\.yyyy'">
            {$order.date|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:'dd\.mm\.yyyy'">
            {$order.glazing_date|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td style='mso-number-format: "\@";'>
            {$order.customer_name|escape|default:#no_number#}
          </td>
          <td style="mso-number-format:'\@';">
            {$order.city_name|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:'\@';">
            {$order.destination_name|escape}
          </td>
          <td style='mso-number-format: "\@";' colspan="10">
            <i>{#reports_no_standard_glazings#|escape}</i>
          </td>
          <td rowspan="{$order.rowspan}" style="mso-number-format:'0'; vertical-align: middle;">
            <strong style="color: red;">{$order.total_count|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}</strong>
          </td>
          <td style="mso-number-format:'0\.00'; vertical-align: middle;" rowspan="{$order.rowspan}" >
            <strong style="color: red;">{$order.total_area|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}</strong>
          </td>
          <td style="mso-number-format:'\@';">
            {foreach from=$order.deliverer_email item='dlv_email' name='de'}
              {$dlv_email|escape}</span>{if !$smarty.foreach.de.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="21">
            <span style="color: red;">{#reports_no_results#|escape}</span>
          </td>
        </tr>
      {/foreach}
    </table>
  {/if}
</body>
</html>
