<?php

class Custom_Report_Filters extends Report_Filters {

    private static $registry = array();

    /**
     * Defining filters for the certain type report
     */
    function defineFilters(&$registry) {
        self::$registry = &$registry;

        // Prepare an array for the filters
        $filters = array();

        $settings = Reports::getReportSettings($registry);

        //DEFINE 'Deliverer' Filter
        $deliverer_types_included = array_filter(preg_split('/\s*,\s*/', CUSTOMER_DELIVERER_TYPE));
        $filters['deliverer'] = [
            'custom_id' => 'deliverer',
            'name'      => 'deliverer',
            'type'      => 'autocompleter',
            'autocomplete_buttons' => 'clear',
            'width'     => '222',
            'autocomplete' => array(
                'type' => 'customers',
                'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                'suggestions' => '<name> <lastname>',
                'buttons_hide' => 'search',
                'fill_options' => array(
                    '$deliverer               => <id>',
                    '$deliverer_autocomplete  => <name> <lastname>',
                    '$deliverer_oldvalue      => <name> <lastname>',
                ),
                'filters' => array(
                    '<type>' => ' IN (' . implode(',', $deliverer_types_included) . ')',
                    '<tag>'  => strval(TAG_DELIVERING_GLAZINGS)
                ),
                'clear' => 1
            ),
            'label'     => $this->i18n('reports_deliverer'),
            'help'      => $this->i18n('reports_deliverer')
        ];

        // Load a delivery date from/to filter
        $filters['glazing_date_from'] = array (
            'name'              => 'glazing_date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'glazing_date_to',
            'required'          => true,
            'first_filter_label'=> $this->i18n('reports_glazing_date'),
            'label'             => $this->i18n('reports_glazing_date')
        );
        $filters['glazing_date_to'] = array (
            'name' => 'glazing_date_to'
        );

        // Load a delivery date from/to filter
        $filters['delivery_date_from'] = array (
            'name'              => 'delivery_date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'delivery_date_to',
            'first_filter_label'=> $this->i18n('reports_delivery_date_from'),
            'label'             => $this->i18n('reports_delivery_date_from')
        );
        $filters['delivery_date_to'] = array (
            'name' => 'delivery_date_to'
        );

        // Load a order date from/to filter
        $filters['order_date_from'] = array (
            'name'              => 'order_date_from',
            'type'              => 'custom_filter',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_date_from_to.html',
            'additional_filter' => 'order_date_to',
            'first_filter_label'=> $this->i18n('reports_order_date_from'),
            'label'             => $this->i18n('reports_order_date_from')
        );
        $filters['order_date_to'] = array (
            'name' => 'order_date_to'
        );

        $filters['office'] = array(
            'type'              => 'custom_filter',
            'actual_type'       => 'dropdown',
            'custom_template'   => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'custom_id' => 'office',
            'name' => 'office',
            'label' => $this->i18n('reports_office'),
            'options' => Dropdown::getOffices(array($registry))
        );
        foreach ($filters['office']['options'] as $k => $office) {
            if (!$office['active_option']) {
                unset($filters['office']['options'][$k]);
            }
        }

        //DEFINE 'City' Filter
        $nom_types_included = array_filter(preg_split('/\s*,\s*/', NOMENCLATURES_TYPES_CITY));
        $filters['city'] = [
            'custom_id' => 'city',
            'name'      => 'city',
            'type'      => 'autocompleter',
            'autocomplete_buttons' => 'clear',
            'width'     => '222',
            'autocomplete' => array(
                'type' => 'customers',
                'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                'suggestions' => '<name> <lastname>',
                'buttons_hide' => 'search',
                'fill_options' => array(
                    '$city               => <id>',
                    '$city_autocomplete  => <name> <lastname>',
                    '$city_oldvalue      => <name> <lastname>',
                ),
                'filters' => array(
                    '<type>' => ' IN (' . implode(',', $nom_types_included) . ')'
                ),
                'clear' => 1
            ),
            'label'     => $this->i18n('reports_city'),
            'help'      => $this->i18n('reports_city')
        ];

        //DEFINE 'Destination' Filter
        $nom_types_included = array_filter(preg_split('/\s*,\s*/', NOMENCLATURES_TYPES_DESTINATION));
        $filters['destination'] = [
            'custom_id' => 'destination',
            'name'      => 'destination',
            'type'      => 'autocompleter',
            'autocomplete_buttons' => 'clear',
            'width'     => '222',
            'autocomplete' => array(
                'type' => 'nomenclatures',
                'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'nomenclatures', 'nomenclatures'),
                'suggestions' => '<name> <lastname>',
                'buttons_hide' => 'search',
                'fill_options' => array(
                    '$destination               => <id>',
                    '$destination_autocomplete  => <name> <lastname>',
                    '$destination_oldvalue      => <name> <lastname>',
                ),
                'filters' => array(
                    '<type>' => ' IN (' . implode(',', $nom_types_included) . ')'
                ),
                'clear' => 1
            ),
            'label'     => $this->i18n('reports_destination'),
            'help'      => $this->i18n('reports_destination')
        ];

        //DEFINE ORDER NUM FILTER
        $filters['order_num'] = array (
            'custom_id'       => 'order_num',
            'name'            => 'order_num',
            'type'            => 'custom_filter',
            'actual_type'     => 'text',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'label'           => $this->i18n('reports_order_num'),
            'help'            => $this->i18n('reports_order_num')
        );

        //DEFINE DELIVERER NUM FILTER
        $filters['deliverer_num'] = array (
            'custom_id'       => 'deliverer_num',
            'name'            => 'deliverer_num',
            'type'            => 'custom_filter',
            'actual_type'     => 'text',
            'custom_template' => PH_MODULES_DIR . 'reports/templates/default_filter_multiple.html',
            'label'           => $this->i18n('reports_deliverer_num'),
            'help'            => $this->i18n('reports_deliverer_num')
        );

        //DEFINE 'Customer' Filter
        $customers_types_included = array_filter(preg_split('/\s*,\s*/', CUSTOMERS_TYPES_FILTER));
        $filters['customer'] = [
            'custom_id' => 'customer',
            'name'      => 'customer',
            'type'      => 'autocompleter',
            'autocomplete_buttons' => 'clear',
            'width'     => '222',
            'autocomplete' => array(
                'type' => 'customers',
                'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $registry['module_param'], 'customers', 'customers'),
                'suggestions' => '<name> <lastname>',
                'buttons_hide' => 'search',
                'fill_options' => array(
                    '$customer               => <id>',
                    '$customer_autocomplete  => <name> <lastname>',
                    '$customer_oldvalue      => <name> <lastname>',
                ),
                'filters' => array(
                    '<type>' => ' IN (' . implode(',', $customers_types_included) . ')'
                ),
                'clear' => 1
            ),
            'label'     => $this->i18n('reports_customer'),
            'help'      => $this->i18n('reports_customer')
        ];


        // DEFINE FILTER: report by documents
        $documents_types_included = array_filter(preg_split('/\s*,\s*/', DOCUMENTS_ORDERS));
        $fdt = Dropdown::getDocumentsTypes(array($registry));
        foreach ($fdt as $k => $option) {
            if (!in_array($option['option_value'], $documents_types_included)) {
                unset($fdt[$k]);
            }
        }

        $filters['documents_types'] = array(
            'name'    => 'documents_types',
            'type'    => 'dropdown',
            'options' => $fdt,
            'first_option_label' => $this->i18n('all'),
            'label'   => $this->i18n('reports_documents_types'),
            'help'    => $this->i18n('reports_documents_types')
        );

        return $filters;
    }

    function processDependentFilters(&$filters) {
        if (!self::$registry->get('generated_report')) {
            $date_from = new DateTime('monday this week');
            $filters['glazing_date_from']['value'] = $date_from->format('Y-m-d');
            $date_to = new DateTime('sunday this week');
            $filters['glazing_date_to']['value'] = $date_to->format('Y-m-d');
        }

        $unset_filters = array();
        foreach ($filters as $name => $filter) {
            if (!empty($filter['additional_filter']) && isset($filters[$filter['additional_filter']])) {
                $filters[$name]['additional_filter'] = $filters[$filter['additional_filter']];
                $unset_filters[] = $filter['additional_filter'];
            }
        }

        foreach ($unset_filters as $unset_fltr) {
            unset($filters[$unset_fltr]);
        }

        return $filters;
    }

}

?>
