{if $reports_results}
  <table border="0" cellpadding="5" cellspacing="0" class="t_table reports_table">
    <tr class="reports_title_row reports_title_centered_middle">
      <td class="t_border"><div>
        <input type="checkbox" id="select_all_ord" title="{#reports_select_all_checkboxes#|escape}" onchange="toggleAllCheckboxes(this)" />
      </div></td>
      <td width="250" class="t_border"><div style="width: 250px;">{#reports_deliverer#|escape}</div></td>
      <td width="100"  class="t_border"><div style="width: 100px;">{#reports_order_num#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_deliverer_num#|escape}</div></td>
      <td width="80" class="t_border"><div style="width: 80px;">{#reports_order_date#|escape}</div></td>
      <td width="80" class="t_border"><div style="width: 80px;">{#reports_glazing_date#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 200px;">{#reports_customer#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 150px;">{#reports_city#|escape}</div></td>
      <td width="200" class="t_border"><div style="width: 150px;">{#reports_destination#|escape}</div></td>
      <td width="150" class="t_border"><div style="width: 150px;">{#reports_glazing_code#|escape}</div></td>
      <td width="250" class="t_border"><div style="width: 250px;">{#reports_glazing_name#|escape}</div></td>
      <td width="150"  class="t_border"><div style="width: 150px;">{#reports_glazing_note#|escape}</div></td>
      <td width="80" class="t_border"><div style="width: 80px;">{#reports_glazing_length#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_glazing_height#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_glazing_module#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_glazing_cell#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 150px;">{#reports_glazing_module_note#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width: 80px;">{#reports_glazing_count#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width:  80px;">{#reports_glazing_area#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width:  80px;">{#reports_glazing_total_count#|escape}</div></td>
      <td width="80"  class="t_border"><div style="width:  80px;">{#reports_glazing_total_area#|escape}</div></td>
      <td width="200"  class="t_border"><div style="width:  200px;">{#reports_glazing_files#|escape}</div></td>
      <td width="200"><div style="width: 200px;">{#reports_deliverer_email#|escape}</div></td>
    </tr>
    {foreach from=$reports_results item='order'}
      {cycle values='t_odd1 t_odd2,t_even1 t_even2' assign=row_class}
      {foreach from=$order.glazings item='glazing' name='glz'}
        <tr class="{$row_class}">
          {if $smarty.foreach.glz.first}
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              <input type="checkbox" name="order_ids[]" value="{$order.id}" id="ord_{$order.id}" />
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {include file="input_autocompleter.html"
                       var=$reports_additional_options.deliverer_ac
                       width=222
                       standalone=true
                       eq_indexes=true
                       index=$order.id
                       name=$reports_additional_options.deliverer_ac.name
                       custom_id=$reports_additional_options.deliverer_ac.custom_id
                       label=$reports_additional_options.deliverer_ac.label
                       help=$reports_additional_options.deliverer_ac.help
                       value=$order.deliverer
                       value_autocomplete=$order.deliverer_name
                       autocomplete=$reports_additional_options.deliverer_ac.autocomplete
                       autocomplete_var_type='basic'
                       autocomplete_type=$reports_additional_options.deliverer_ac.autocomplete_type
                       autocomplete_buttons = $reports_additional_options.deliverer_ac.autocomplete_buttons
              }
              <input type="hidden" name="deliverer_ids[]" value="{$order.deliverer}" id="dlvr_{$order.id}" />
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$order.id}" target="_blank">{$order.full_num|escape|default:#no_number#}</a>
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.custom_num|escape|default:#no_number#}
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.date|date_format:#date_short#|escape|default:"&nbsp;"}
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {include file='input_date.html'
                name='glazing_date'
                value=$order.glazing_date
                eq_indexes=true
                index=$order.id
                standalone=true
              }
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$order.customer}" target="_blank">{$order.customer_name|escape}</a>
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.city_name|escape}
            </td>
            <td class="hleft t_border" rowspan="{$order.rowspan}">
              {$order.destination_name|escape}
            </td>
          {/if}
          <td class="hleft t_border">
            {$glazing.glazing_code|escape|default:"&nbsp;"}
          </td>
          <td class="hleft t_border">
            {$glazing.glazing_name|escape|default:"&nbsp;"}
          </td>
          <td class="hleft t_border">
            {capture assign=note_glz_index}{$order.id}_{$glazing.row_num}{/capture}
            {include file='input_textarea.html'
              name='glazing_note'
              value=$glazing.note
              eq_indexes=true
              height=25
              index=$note_glz_index
              standalone=true
            }
          </td>
          <td class="hright t_border">
            {$glazing.length|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border">
            {$glazing.height|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border">
            {$glazing.module|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border">
            {$glazing.cell|escape|default:"&nbsp;"}
          </td>
          <td class="t_border">
            {$glazing.module_note|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border">
            {$glazing.count_num|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border">
            {$glazing.area|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}
          </td>
          {if $smarty.foreach.glz.first}
            <td class="hright t_border" rowspan="{$order.rowspan}">
              {$order.total_count|escape|default:"&nbsp;"}
            </td>
            <td class="hright t_border" rowspan="{$order.rowspan}">
              {$order.total_area|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}
            </td>
            <td class="hleft" rowspan="{$order.rowspan}">
              {foreach from=$order.files item='file' name='fl'}
                {if !empty($file) && is_object($file) && !$file->get('deleted_by')}
                  {if !$file->get('not_exist')}
                    <a href="{$file->getFileURL('viewfile')}" target="_blank">
                      <img
                        src="{$theme->imagesUrl}{$file->getIconName()}.png"
                        width="16" height="16"
                        border="0"
                        alt="{#open#|escape}"
                        title=""
                        {popup text=$file->get('filename')|escape caption=#file#}
                      />
                    </a>{if !$smarty.foreach.fl.last}<br />{/if}
                  {else}
                    <img
                      src="{$theme->imagesUrl}{$file->getIconName()}.png"
                      width="16" height="16"
                      border="0"
                      alt="{#view#|escape}"
                      class="pointer dimmed"
                    />
                  {/if}
                {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
            <td class="hleft" rowspan="{$order.rowspan}">
              {foreach from=$order.deliverer_email item='dlv_email' name='de'}
                <span id="email_{$order.id}_{$smarty.foreach.de.iteration}">{$dlv_email|escape}</span>{if !$smarty.foreach.de.last}<br />{/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            </td>
          {/if}
        </tr>
      {foreachelse}
        <tr class="{$row_class}">
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            <input type="checkbox" name="order_ids[]" value="{$order.id}" id="ord_{$order.id}" />
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$order.deliverer}" target="_blank">{$order.deliverer_name|escape}</a>
            <input type="hidden" name="deliverer_ids[]" value="{$order.deliverer}" id="dlvr_{$order.id}" />
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$order.id}" target="_blank">{$order.full_num|escape|default:#no_number#}</a>
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            {$order.custom_num|escape|default:#no_number#}
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            {$order.date|date_format:#date_short#|escape|default:"&nbsp;"}
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            {include file='input_date.html'
              name='glazing_date'
              value=$order.glazing_date
              eq_indexes=true
              index=$order.id
              standalone=true
            }
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$order.customer}" target="_blank">{$order.customer_name|escape}</a>
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            {$order.city_name|escape}
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}">
            {$order.destination_name|escape}
          </td>
          <td class="hleft t_border" rowspan="{$order.rowspan}" colspan="10">
            <i>{#reports_no_standard_glazings#|escape}</i>
          </td>
          <td class="hright t_border" rowspan="{$order.rowspan}">
            {$order.total_count|escape|default:"&nbsp;"}
          </td>
          <td class="hright t_border" rowspan="{$order.rowspan}">
            {$order.total_area|round:"2"|string_format:"%.2f"|escape|default:"&nbsp;"}
          </td>
          <td class="hleft" rowspan="{$order.rowspan}">
            {foreach from=$order.files item='file' name='fl'}
              {if !empty($file) && is_object($file) && !$file->get('deleted_by')}
                {if !$file->get('not_exist')}
                  <a href="{$file->getFileURL('viewfile')}" target="_blank">
                    <img
                      src="{$theme->imagesUrl}{$file->getIconName()}.png"
                      width="16" height="16"
                      border="0"
                      alt="{#open#|escape}"
                      title=""
                      {popup text=$file->get('filename')|escape caption=#file#}
                    />
                  </a>{if !$smarty.foreach.fl.last}<br />{/if}
                {else}
                  <img
                    src="{$theme->imagesUrl}{$file->getIconName()}.png"
                    width="16" height="16"
                    border="0"
                    alt="{#view#|escape}"
                    class="pointer dimmed"
                  />
                {/if}
              {/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td class="hleft" rowspan="{$order.rowspan}">
            {foreach from=$order.deliverer_email item='dlv_email' name='de'}
              <span id="email_{$order.id}_{$smarty.foreach.de.iteration}">{$dlv_email|escape}</span>{if !$smarty.foreach.de.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
        </tr>
      {/foreach}
    {foreachelse}
      <tr class="{$row_class}">
        <td class="hleft t_border error" colspan="21">
          {#reports_no_results#|escape}
        </td>
      </tr>
    {/foreach}
  </table>

  <br />
  <button type="submit" name="edit_documents" class="button" onclick="return editOrders(this);">{#edit#|escape}</button>
  <button type="submit" name="send_documents" class="button" onclick="return sendFiles(this);">{#reports_send_file#|escape}</button>
  {if $export_permission}
    <button type="button" class="button reports_export_button" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=export&amp;report_type={$report_type}&amp;pattern=';">{#export_report#|escape}</button>
  {/if}
{else}
  <span><h1 style="color:red">{#reports_no_results#|escape}</h1></span>
{/if}
