<?php

Class Bulgariaplast_Deliverers_Glazing_Orders Extends Reports {

    public static $registry;
    public static $filters = array();

    public static function buildQuery(&$registry, $filters = array()) {
        // Prepare the array for the final results
        $final_results = array();
        self::$registry = $registry;
        self::$filters = $filters;

        // check the filters
        if (empty(self::$filters['glazing_date_from']) || empty(self::$filters['glazing_date_to'])) {
            return self::exitReport($final_results, false, 'error_reports_required_filters');
        }

        // check the period
        $glazing_date_from = new DateTime(self::$filters['glazing_date_from']);
        $glazing_date_to = new DateTime(self::$filters['glazing_date_to']);
        $check_date = clone $glazing_date_from;
        $check_date->add(new DateInterval('P' . GLAZING_DATE_MAX_PERIOD_MONTHS . 'M'));
        if ($check_date<$glazing_date_to) {
            return self::exitReport($final_results, false, 'error_glazing_date_period_too_long');
        }

        $final_results = self::getResults();
        $final_results['additional_options']['dont_show_export_button'] = true;

        // prepare the deliverers autocompleter
        $report_type = self::$registry->get('report_type');
        require_once PH_MODULES_DIR . 'reports/models/report.filters.php';
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report_type['name'] . '/custom.report.filters.php';
        $defineCustomFilters = new Custom_Report_Filters(self::$registry, $report_type['name']);
        $report_filters = $defineCustomFilters->defineFilters(self::$registry);
        $final_results['additional_options']['deliverer_ac'] = $report_filters['deliverer'];

        return self::exitReport($final_results, true);
    }

    private static function exitReport($final_results, $success = false, $error = '') {
        if (!$success) {
            $final_results['additional_options']['failed'] = true;
            $final_results['additional_options']['dont_show_export_button'] = true;
            if (!empty($error)) {
                if ($error == 'error_glazing_date_period_too_long') {
                    self::$registry['messages']->setError(sprintf(self::$registry['translater']->translate($error), GLAZING_DATE_MAX_PERIOD_MONTHS));
                } else {
                    self::$registry['messages']->setError($final_results['additional_options']['error'] = self::$registry['translater']->translate($error));
                }
                self::$registry['messages']->insertInSession(self::$registry);
            }
        }

        if (!empty(self::$filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }
        return $results;
    }

    /**
     * @return array
     */
    public static function getResults(): array {
        $final_results = array();
        $documents_types_included = array_filter(preg_split('/\s*,\s*/', DOCUMENTS_ORDERS));
        $substatuses_ignored = array_filter(preg_split('/\s*,\s*/', IGNORE_SUBSTATUSES));

        if (!empty(self::$filters['documents_types'])) {
            $documents_types_included = [self::$filters['documents_types']];
        }

        $add_vars = [
            DOCUMENTS_GLAZING,
            DOCUMENTS_SUPPLIER,
            DOCUMENTS_GLAZING_LENGTH,
            DOCUMENTS_GLAZING_HEIGHT,
            DOCUMENTS_GLAZING_COUNT,
            DOCUMENTS_GLAZING_AREA,
            DOCUMENTS_GLAZING_DATE,
            DOCUMENTS_FILE,
            DOCUMENTS_GLAZING_DATE,
            DOCUMENTS_GLAZING_MODULE,
            DOCUMENTS_GLAZING_CELL,
            DOCUMENTS_GLAZING_NOTE,
            DOCUMENTS_GLAZING_CURVED,
            DOCUMENTS_GLAZING_MODULE_NOTE,
            DOCUMENTS_DELIVERY_CITY,
            DOCUMENTS_DELIVERY_DESTINATION,
        ];

        $sql_for_add_vars = 'SELECT fm.name, fm.id, fm.model_type FROM ' . DB_TABLE_FIELDS_META . ' AS fm
                             WHERE fm.model="Document" AND fm.model_type IN ("' . implode('","',
                $documents_types_included) . '") AND fm.name IN ("' . implode('","', $add_vars) . '")';
        $add_vars_tmp = self::$registry['db']->GetAll($sql_for_add_vars);
        $add_vars = array();
        foreach ($add_vars_tmp as $av) {
            if (!isset($av['name'])) {
                $add_vars[$av['name']] = array();
            }
            $add_vars[$av['name']][$av['model_type']] = $av['id'];
        }

        $where = array();
        $glz_where = array();
        $city_search = false;
        $destination_search = false;
        $where[] = 'd.active=1';
        $where[] = 'd.deleted_by=0';
        $where[] = "d.type IN ('" . implode("','", $documents_types_included) . "')";

        if (!empty($substatuses_ignored)) {
            $where[] = "d.substatus NOT IN ('" . implode("','", $substatuses_ignored) . "')";
        }
        if (!empty(self::$filters['order_ids'])) {
            $where[] = "d.id IN ('" . implode("','", self::$filters['order_ids']) . "')";
        }
        if (!empty(self::$filters['delivery_date_from'])) {
            $where[] = "d.deadline >= '" . self::$filters['delivery_date_from'] . " 00:00:00'";
        }
        if (!empty(self::$filters['delivery_date_to'])) {
            $where[] = "d.deadline <= '" . self::$filters['delivery_date_to'] . " 23:59:59'";
        }
        if (!empty(self::$filters['glazing_date_from'])) {
            $glz_where[] = "(d_cstm_glz_date.value >= '" . self::$filters['glazing_date_from'] . "' OR d_cstm_glz_date.value='')";
        }
        if (!empty(self::$filters['glazing_date_to'])) {
            $glz_where[] = "(d_cstm_glz_date.value <= '" . self::$filters['glazing_date_to'] . "' OR d_cstm_glz_date.value='')";
        }
        if (!empty(self::$filters['order_date_from'])) {
            $where[] = "d.date >= '" . self::$filters['order_date_from'] . " 00:00:00'";
        }
        if (!empty(self::$filters['order_date_to'])) {
            $where[] = "d.date <= '" . self::$filters['order_date_to'] . " 23:59:59'";
        }
        if (!empty(self::$filters['customer'])) {
            $where[] = "d.customer='" . self::$filters['customer'] . "'";
        }
        if (!empty(self::$filters['city'])) {
            $city_search = true;
        }
        if (!empty(self::$filters['destination'])) {
            $destination_search = true;
        }
        $offices = array_filter(self::$filters['office'] ?? array());
        if (!empty($offices)) {
            $where[] = "d.office IN ('" . implode("','", $offices) . "')";
        }
        $full_nums = array_filter(self::$filters['order_num'] ?? array());
        if (!empty($full_nums)) {
            $full_num_clauses = array();
            foreach ($full_nums as $full_num) {
                $full_num_clauses[] = 'd.full_num LIKE "%' . $full_num . '%"';
            }
            $where[] = '(' . implode(' OR ', $full_num_clauses) . ')';
        }
        $custom_nums = array_filter(self::$filters['deliverer_num'] ?? array());
        if (!empty($custom_nums)) {
            $custom_num_clauses = array();
            foreach ($custom_nums as $custom_num) {
                $custom_num_clauses[] = 'd.custom_num LIKE "%' . $custom_num . '%"';
            }
            $where[] = '(' . implode(' OR ', $custom_num_clauses) . ')';
        }

        // prepare the customers rights ids
        $rights = self::$registry['currentUser']->get('rights');
        $current_rights_where = array();
        foreach ($documents_types_included as $p_type) {
            $current_right = isset($rights['documents' . $p_type]['list']) ? $rights['documents' . $p_type]['list'] : '';

            //additional 'where' for hiding not allowed models
            if (!self::$registry['currentUser']->get('id') || !$current_right) {
                continue;
            }
            if ($current_right == 'all') {
                if (self::$registry['currentUser']->get('is_portal')) {
                    if (self::$registry['currentUser']->get('default_customer')) {
                        $current_rights_where[] = sprintf("((d.user_permissions like('%%,%d,%%') OR d.added_by=%d OR d.customer='%d') AND d.type='%d')",
                            self::$registry['currentUser']->get('id'),
                            self::$registry['currentUser']->get('id'),
                            self::$registry['currentUser']->get('default_customer'),
                            $p_type
                        );
                    } else {
                        $current_rights_where[] = "0";
                    }
                } else {
                    $current_rights_where[] = "d.type='" . $p_type . "'";
                }
            } elseif ($current_right == 'mine') {
                $current_rights_where[] = sprintf("((d.user_permissions like('%%,%d,%%') OR d.added_by=%d OR d.customer='%d') AND d.type='%d')",
                    self::$registry['currentUser']->get('id'),
                    self::$registry['currentUser']->get('id'),
                    self::$registry['currentUser']->get('default_customer'),
                    $p_type
                );
            } elseif ($current_right == 'group') {
                $user_groups = self::$registry['currentUser']->get('groups');
                $user_departments = self::$registry['currentUser']->get('departments');
                $current_rights_where[] = sprintf("((d.user_permissions like('%%,%d,%%') OR d.added_by=%d%s%s) AND d.type='%d')",
                    self::$registry['currentUser']->get('id'),
                    self::$registry['currentUser']->get('id'),
                    (count($user_groups) ? ' OR d.`group` IN (' . implode(',', $user_groups) . ')' : ''),
                    (count($user_departments) ? ' OR d.department in (' . implode(',', $user_departments) . ')' : ''),
                    $p_type
                );
            } elseif ($current_right == 'none') {
                $current_rights_where[] = "0";
            }
        }
        if (!empty($current_rights_where)) {
            $where[] = '(' . implode(' OR ' . "\n", $current_rights_where) . ')';
        }

        $union_queries = array();
        $exclude_tags = array_filter(preg_split('/\s*,\s*/', TAG_SENT_TO_DELIVERER));
        foreach ($documents_types_included as $doc_id) {
            $union_queries[] = 'SELECT d.id as idx, d.id, d.full_num, d.custom_num, d.date, d.deadline, d_cstm_glz_date.value as glazing_date, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name,' . "\n" .
                               '       dcstm_gl_spl.value as deliverer, CONCAT(ci18n_spl.name, " ", ci18n_spl.lastname) as deliverer_name, c_spl.email as deliverer_email, d.type, d_cstm_city.value as city, nomi18n.name as city_name, ' . "\n" .
                               '       d_cstm_destination.value as destination, nomi18n_des.name as destination_name ' . "\n" .
                               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                               ' ON (ci18n.parent_id=d.customer AND ci18n.lang="' . self::$registry['lang'] . '" AND ' . implode(' AND ',
                                   $where) . ')' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                               '  ON (tm.model="Document" AND tm.model_id=d.id AND tm.tag_id IN ("' . implode('","', $exclude_tags) . '"))' . "\n" .
                               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_spl' . "\n" .
                               ' ON (dcstm_gl_spl.model_id=d.id AND dcstm_gl_spl.var_id=' . (!empty($add_vars[DOCUMENTS_SUPPLIER][$doc_id]) ? $add_vars[DOCUMENTS_SUPPLIER][$doc_id] : '""') . (!empty(self::$filters['deliverer']) ? ' AND dcstm_gl_spl.value="' . self::$filters['deliverer'] . '"' : '') . ')' . "\n" .
                               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_glz_date' . "\n" .
                               ' ON (d_cstm_glz_date.model_id=d.id AND d_cstm_glz_date.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_DATE][$doc_id]) ? $add_vars[DOCUMENTS_GLAZING_DATE][$doc_id] : '""') . (!empty($glz_where) ? ' AND ' . implode(' AND ', $glz_where) : '') . ')' . "\n" .
                               'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' AS c_spl' . "\n" .
                               ' ON (c_spl.id=dcstm_gl_spl.value)' . "\n" .
                               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_spl' . "\n" .
                               ' ON (ci18n_spl.parent_id=dcstm_gl_spl.value AND ci18n_spl.lang="' . self::$registry['lang'] . '")' . "\n" .
                               (!empty($city_search) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_city' . "\n" .
                               ' ON (d_cstm_city.model_id=d.id AND d_cstm_city.var_id=' . (!empty($add_vars[DOCUMENTS_DELIVERY_CITY][$doc_id]) ? $add_vars[DOCUMENTS_DELIVERY_CITY][$doc_id] : '""') . (!empty($city_search) ? ' AND d_cstm_city.value="' . self::$filters['city'] . '"' : '') . ')' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                               ' ON (nomi18n.parent_id=d_cstm_city.value AND nomi18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                               (!empty($destination_search) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_destination' . "\n" .
                               ' ON (d_cstm_destination.model_id=d.id AND d_cstm_destination.var_id=' . (!empty($add_vars[DOCUMENTS_DELIVERY_DESTINATION][$doc_id]) ? $add_vars[DOCUMENTS_DELIVERY_DESTINATION][$doc_id] : '""') . (!empty($destination_search) ? ' AND d_cstm_destination.value="' . self::$filters['destination'] . '"' : '') . ')' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n_des' . "\n" .
                               ' ON (nomi18n_des.parent_id=d_cstm_destination.value AND nomi18n_des.lang="' . self::$registry['lang'] . '")' . "\n" .
                               'WHERE tm.tag_id IS NULL' . "\n";
        }
        if (empty($union_queries)) {
            return array();
        }
        $sql = '(' . implode(') UNION ALL (', $union_queries) . ')';

/*        $sql = 'SELECT d.id as idx, d.id, d.full_num, d.custom_num, d.date, d.deadline, d_cstm_glz_date.value as glazing_date, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name,' . "\n" .
               '       dcstm_gl_spl.value as deliverer, CONCAT(ci18n_spl.name, " ", ci18n_spl.lastname) as deliverer_name, c_spl.email as deliverer_email' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
               ' ON (ci18n.parent_id=d.customer AND ci18n.lang="' . self::$registry['lang'] . '" AND ' . implode(' AND ',
                   $where) . ')' . "\n" .
               'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
               '  ON (tm.model="Document" AND tm.model_id=d.id AND tm.tag_id IN ("' . implode('","', $exclude_tags) . '"))' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_spl' . "\n" .
               ' ON (dcstm_gl_spl.model_id=d.id AND dcstm_gl_spl.var_id IN (' . (!empty($add_vars[DOCUMENTS_SUPPLIER]) ? implode(',', $add_vars[DOCUMENTS_SUPPLIER]) : '""') . ')' . (!empty(self::$filters['deliverer']) ? ' AND dcstm_gl_spl.value="' . self::$filters['deliverer'] . '"' : '') . ')' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_glz_date' . "\n" .
               ' ON (d_cstm_glz_date.model_id=d.id AND d_cstm_glz_date.var_id IN (' . (!empty($add_vars[DOCUMENTS_GLAZING_DATE]) ? implode(',', $add_vars[DOCUMENTS_GLAZING_DATE]) : '""') . ')' . (!empty($glz_where) ? ' AND ' . implode(' AND ', $glz_where) : '') . ')' . "\n" .
               'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' AS c_spl' . "\n" .
               ' ON (c_spl.id=dcstm_gl_spl.value)' . "\n" .
               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_spl' . "\n" .
               ' ON (ci18n_spl.parent_id=dcstm_gl_spl.value AND ci18n_spl.lang="' . self::$registry['lang'] . '")' . "\n" .
               'WHERE tm.tag_id IS NULL' . "\n";*/
        $documents_results = self::$registry['db']->GetAssoc($sql);

        if (empty($documents_results)) {
            return array();
        }

        // prepare articles list
        $sql_for_add_vars = 'SELECT fm.id, fm.model_type FROM ' . DB_TABLE_FIELDS_META . ' AS fm
                             WHERE fm.model="Nomenclature" AND fm.name="' . NOMENCLATURE_SUBTYPE_VAR . '"';
        $nom_vars = self::$registry['db']->GetAssoc($sql_for_add_vars);

        $glazings_list = array();
        if (!empty($nom_vars)) {
            // get the nomenclatures that are glazings
            $sql = 'SELECT n.id FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   ' INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm ON n.type IN ('. implode(',', $nom_vars) .') AND n.active=1 AND n.deleted_by=0 AND n_cstm.model_id=n.id AND n_cstm.var_id IN (' . implode(',', array_keys($nom_vars)) . ') AND n_cstm.value IN (' . NOMENCLATURE_SUBTYPE_ID . ')';
            $glazings_list = self::$registry['db']->GetCol($sql);
            $glazings_list = array_values(array_unique($glazings_list));
        }

        $query_extra_data = array();
        foreach ($documents_types_included as $doc_type_inc) {
            $query_extra_data[] = 'SELECT d.id, nom.id as glazing, nom.code as glazing_code, ni18n.name as glazing_name, dcstm_gl.num as row_num, dcstm_gl_l.value as length, dcstm_gl_h.value as height,' . "\n" .
                   '       dcstm_gl_c.value as count_num, dcstm_gl_a.value as area, dcstm_gl_mod.value as module, dcstm_gl_cl.value as cell, dcstm_gl_nt.value as note, dcstm_gl_mnt.value as module_note, dcstm_gl_cv.value as curved ' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl' . "\n" .
                   ' ON (dcstm_gl.model_id=d.id AND dcstm_gl.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING][$doc_type_inc] : '""') . ' AND d.id IN (' . implode(',',
                       array_keys($documents_results)) . ') AND dcstm_gl.value IN (' . (!empty($glazings_list) ? implode(',', $glazings_list) : '""') . ') AND d.type="' . $doc_type_inc . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                   ' ON (nom.id=dcstm_gl.value)' . "\n" .
                   'INNER JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                   ' ON (ni18n.parent_id=nom.id AND ni18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_l' . "\n" .
                   ' ON (dcstm_gl_l.model_id=d.id AND dcstm_gl_l.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_LENGTH][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_LENGTH][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_l.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_h' . "\n" .
                   ' ON (dcstm_gl_h.model_id=d.id AND dcstm_gl_h.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_HEIGHT][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_HEIGHT][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_h.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_c' . "\n" .
                   ' ON (dcstm_gl_c.model_id=d.id AND dcstm_gl_c.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_COUNT][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_COUNT][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_c.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_a' . "\n" .
                   ' ON (dcstm_gl_a.model_id=d.id AND dcstm_gl_a.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_AREA][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_AREA][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_a.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_mod' . "\n" .
                   ' ON (dcstm_gl_mod.model_id=d.id AND dcstm_gl_mod.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_MODULE][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_MODULE][$doc_type_inc] : '""') . ' AND dcstm_gl_mod.num=dcstm_gl.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_cl' . "\n" .
                   ' ON (dcstm_gl_cl.model_id=d.id AND dcstm_gl_cl.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_CELL][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_CELL][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_cl.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_nt' . "\n" .
                   ' ON (dcstm_gl_nt.model_id=d.id AND dcstm_gl_nt.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_NOTE][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_NOTE][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_nt.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_mnt' . "\n" .
                   ' ON (dcstm_gl_mnt.model_id=d.id AND dcstm_gl_mnt.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_MODULE_NOTE][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_MODULE_NOTE][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_mnt.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_cv' . "\n" .
                   ' ON (dcstm_gl_cv.model_id=d.id AND dcstm_gl_cv.var_id=' . (!empty($add_vars[DOCUMENTS_GLAZING_CURVED][$doc_type_inc]) ? $add_vars[DOCUMENTS_GLAZING_CURVED][$doc_type_inc] : '""') . ' AND dcstm_gl.num=dcstm_gl_cv.num)' . "\n";
        }
        $glazing_results = self::$registry['db']->GetAll('(' . implode(') UNION ALL (', $query_extra_data) . ')');

        // get the files if any
        $sql = 'SELECT dcstm_gl_fl.value as file, d.id' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_gl_fl' . "\n" .
               ' ON (d.id IN ("' . implode('","', array_keys($documents_results)) . '") AND dcstm_gl_fl.model_id=d.id AND dcstm_gl_fl.var_id IN ("' . (!empty($add_vars[DOCUMENTS_FILE]) ? implode('","', $add_vars[DOCUMENTS_FILE]) : '') . '") AND dcstm_gl_fl.value!="")' . "\n";
        $files_results = self::$registry['db']->GetAssoc($sql);

        $files_data = array();
        if (!empty($files_results)) {
            $filters_files = array(
                'model_lang' => self::$registry['lang'],
                'where' => array('f.id IN (' . implode(',', array_keys($files_results)) . ')')
            );
            $files_found = Files::search(self::$registry, $filters_files);
            foreach ($files_found as $fl) {
                $files_data[$fl->get('id')] = $fl;
            }
        }

        foreach ($glazing_results as $gl_res) {
            if (!isset($documents_results[$gl_res['id']])) {
                continue;
            }
            if (!isset($final_results[$gl_res['id']])) {
                $final_results[$gl_res['id']] = $documents_results[$gl_res['id']];
                $final_results[$gl_res['id']]['rowspan'] = 0;
                $final_results[$gl_res['id']]['glazings'] = array();
                $final_results[$gl_res['id']]['files'] = array();
                $final_results[$gl_res['id']]['total_area'] = 0;
                $final_results[$gl_res['id']]['total_count'] = 0;

                // process the files
                $files_for_order = array_keys($files_results, $gl_res['id']);
                foreach ($files_for_order as $file_id) {
                    if (isset($files_data[$file_id])) {
                        $final_results[$gl_res['id']]['files'][] = $files_data[$file_id];
                    }
                }

                // process the contacts emails
                $email_rows = array_filter(explode("\n", $final_results[$gl_res['id']]['deliverer_email']));
                $email_deliverer = array();
                foreach ($email_rows as $idx => $contact_data) {
                    $email_data = explode("|", $contact_data, 2);
                    $email_deliverer[] = $email_data[0];
                }
                $final_results[$gl_res['id']]['deliverer_email'] = $email_deliverer;
            }
            if (empty($gl_res['curved'])) {
                $final_results[$gl_res['id']]['total_area'] += floatval($gl_res['area']);
                $final_results[$gl_res['id']]['total_count'] += floatval($gl_res['count_num']);
                $final_results[$gl_res['id']]['glazings'][] = $gl_res;
            }
            $final_results[$gl_res['id']]['rowspan'] = count($final_results[$gl_res['id']]['glazings']);
            $final_results[$gl_res['id']]['rowspan'] = ($final_results[$gl_res['id']]['rowspan'] ?: 1);
        }

        uasort($final_results, function($a, $b) {
            // Handle empty or null dates
            if (empty($a['glazing_date'])) return -1;
            if (empty($b['glazing_date'])) return 1;

            // Compare dates
            return strtotime($a['glazing_date']) - strtotime($b['glazing_date']);
        });

        return $final_results;
    }
}

?>
