/**
 * custom javascript messages
 */
if (env.current_lang == 'bg') {
    i18n['messages']['error_no_salary_documents'] = 'Нито един от показаните служители няма отворен фиш за заплата!';
    i18n['messages']['warning_salary_documents'] = 'Информацията за служителите ще бъде записана само за тези, които имат предварително създадени фишове за заплата.\nСигурни ли сте, че искате да продължите?';
} else {
    i18n['messages']['error_no_salary_documents'] = 'There are no employees with opened salary documents!';
    i18n['messages']['warning_salary_documents'] = 'The information will be completed only for the users who have a salary document created.\nWould you like to continue?';
}


function completeInSalaryDocument(element) {
    var completed_salary_documents = 0;
    var salary_documents_inputs = $$('input[id^="salary_document_"][type="hidden"]');

    for (var j=0; j<salary_documents_inputs.length; j++) {
        if (salary_documents_inputs[j].value) {
            completed_salary_documents++;
        }
    }

    if (!completed_salary_documents) {
        alert(i18n['messages']['error_no_salary_documents']);
        return false;
    }

    if (!confirm(i18n['messages']['warning_salary_documents'])) {
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');
    form = element.form;

    var url = env.base_url + '?' + env.module_param + '=reports&reports=ajax_complete_in_documents&report_type=' + $('report_type').value;

    fetch(url, {
            credentials: 'same-origin'
        }
    )
        .then(response => response.json())
        .then(data => {
            if (data.result) {
                window.open(env.base_url + '?' + env.module_param + '=reports&report_type=' + $('report_type').value, '_self');
            } else {
                Effect.Fade('loading');
                alert(data['messages'].join("\n"));
            }
        })
        .catch((err) => {
            alert('Error 404: location "' + err.toString() + '" was not found.');
            Effect.Fade('loading');
        });
}
