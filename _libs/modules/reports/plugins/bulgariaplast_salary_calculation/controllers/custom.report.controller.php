<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Name of the report
     */
    public $current_report_name = 'bulgariaplast_salary_calculation';
    public $success_changes = [];
    public $failed_changes = [];

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
            case 'ajax_complete_in_documents':
                $this->_completeInSalaryDocuments();
                break;
            default:
                parent::execute();
        }
    }

    /**
     * Add incomes reason of "Sale" type to selected orders
     */
    public function _completeInSalaryDocuments() {
        $this->loadReportsI18NFromDb();
        $operation_result = array(
            'result'   => true
        );

        // Get the model lang
        $model_lang = (empty($filters['model_lang']) ? $this->registry['lang'] : $filters['model_lang']);

        // Prepare some report defaults and get the report type name
        $report_type = $this->prepareReportDefaults($this->registry);

        // include the report class
        $plugin_dir = PH_MODULES_DIR . "reports/plugins/{$report_type}/";
        $report_class_name = implode('_', array_map('ucfirst', explode('_', $report_type)));
        $report_class_path = "{$plugin_dir}custom.report.query.php";
        $filters = $this->registry['session']->get('reports_' . $report_type . '_report');

        require_once $report_class_path;
        $employees_data = $report_class_name::prepareMainReportData($this->registry, $filters);
        $getOldVars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $editAll = $this->registry->get('edit_all');
        $this->registry->set('edit_all', true, true);

        foreach ($employees_data as $employee_data) {
            if (!$employee_data['salary_document']) {
                continue;
            }
            $this->updateSalaryDocument($employee_data);
        }
        $this->registry->set('edit_all', $editAll, true);
        $this->registry->set('get_old_vars', $getOldVars, true);

        // process messages
        $session_insert = false;
        if (!empty($this->success_changes)) {
            $links = [];
            foreach ($this->success_changes as $success) {
                $links[] = sprintf('<a href="%s://%s%sindex.php?%s=documents&documents=view&view=%d" target="_blank">%s</a>',
                                   (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                   $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'],
                                   $success['document'],
                                   $success['employee']
                );
            }
            $session_insert = true;
            $this->registry['messages']->setMessage(sprintf($this->i18n('message_reports_salary_document_updated_successfully'), implode(', ', $links)));
        }
        // process errors
        if (!empty($this->failed_changes)) {
            $links = [];
            foreach ($this->failed_changes as $fail) {
                $links[] = sprintf('<a href="%s://%s%sindex.php?%s=documents&documents=view&view=%d" target="_blank">%s</a>',
                    (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                    $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'],
                    $fail['document'],
                    $fail['employee']
                );
            }
            $session_insert = true;
            $this->registry['messages']->setError(sprintf($this->i18n('error_reports_salary_document_updated_failed'), implode(', ', $links)));
        }
        if ($session_insert) {
            $this->registry['messages']->insertInSession($this->registry);
        }

        print json_encode($operation_result);
        exit;
    }

    public function updateSalaryDocument($employee_data) : void
    {
        // Get the selected documents
        $doc_filters = array('where'      => array('d.id=' . $employee_data['salary_document']),
            'model_lang' => $this->registry['lang'],
            'sanitize'   => false);
        $salary_document = Documents::searchOne($this->registry, $doc_filters);
        $salary_document->getVars();

        $old_salary_document = clone $salary_document;
        $assoc_vars = $salary_document->getAssocVars();

        // map of data - left: the var in the model; right:the key in the result array
        $varsDataMap = [
            DOC_SALARY_WORKED_HOURS => 'worked_hours',
            DOC_SALARY_POSITION_NAME => 'position',
            DOC_SALARY_POSITION => 'position_id',
            DOC_SALARY_WORKED_DAYS => 'worked_days',
            DOC_SALARY_DAILY_PAYMENT => 'daily_payment',
            DOC_SALARY_OVERTIME => 'overtime',
            DOC_SALARY_OVERTIME_PAYMENT => 'overtime_payment',
            DOC_SALARY_WORKED_DAYS_PAYMENT => 'worked_days_payment',
            DOC_SALARY_PAID_LEAVE => 'days_off_paid_payment',
            DOC_SALARY_PAID_DAYS_OFF_TOTAL => 'paid_days_off_total',
            DOC_SALARY_WORK_TRIP_DAYS => 'work_trip_days',
            DOC_SALARY_WORK_TRIP_SUM => 'work_trip_sum',
            DOC_SALARY_DELIVERIES_WO_FAILURES => 'deliveries_wo_failures',
            DOC_SALARY_DELIVERIES_WO_FAILURES_SUM => 'deliveries_wo_failures_sum',
            DOC_SALARY_BONUS => 'bonus',
            DOC_SALARY_BONUS_FULL => 'bonus_full',
            DOC_SALARY_BONUS_WORKED => 'bonus_worked',
            DOC_SALARY_COMPENSATION => 'compensation',
            DOC_SALARY_TOTAL_POSITIVE => 'total_positive',
            DOC_SALARY_DELIVERIES_W_FAILURES => 'deliveries_w_failures',
            DOC_SALARY_DELIVERIES_W_FAILURES_SUM => 'deliveries_w_failures_sum',
            DOC_SALARY_PENALTIES => 'penalties',
            DOC_SALARY_LOANS => 'loans',
            DOC_SALARY_LOANS_LEFT => 'loans_left',
            DOC_SALARY_PENALTIES_CAT => 'penalties_cat',
            DOC_SALARY_ADVANCE => 'advance',
            DOC_SALARY_BONUS_FULL_SATURDAY => 'bonus_full_saturday',
            DOC_SALARY_BONUS_TRANSPORT => 'bonus_transport',
            DOC_SALARY_BONUS_DESCRIPTION => 'bonus_description',
            DOC_SALARY_OTHER_CHARGES => 'other_charges',
            DOC_SALARY_PENALTIES_CHECKING => 'penalties_checking',
            DOC_SALARY_PENALTIES_UNPAID_LEAVE => 'penalties_no',
            DOC_SALARY_DESCRIPTION_FINES => 'penalties_description',
            DOC_SALARY_TOTAL_NEGATIVE => 'total_negative',
            DOC_SALARY_TOTAL => 'total'
        ];

        $integer_format = [DOC_SALARY_WORKED_HOURS, DOC_SALARY_WORKED_DAYS,
                           DOC_SALARY_WORK_TRIP_DAYS, DOC_SALARY_DELIVERIES_WO_FAILURES,
                           DOC_SALARY_OVERTIME, DOC_SALARY_DELIVERIES_W_FAILURES, DOC_SALARY_TOTAL];
        $textFormat = [DOC_SALARY_DESCRIPTION_FINES, DOC_SALARY_POSITION_NAME, DOC_SALARY_POSITION, DOC_SALARY_BONUS_DESCRIPTION];

        foreach ($assoc_vars as $k => $var) {
            if (!isset($varsDataMap[$k])) {
                continue;
            }
            if (in_array($k, $textFormat)) {
                $assoc_vars[$k]['value'] = $employee_data[$varsDataMap[$k]];
                continue;
            }
            $round = 2;
            $format = '%.2f';
            if (in_array($k, $integer_format)) {
                $round = 0;
                $format = '%d';
            }
            $assoc_vars[$k]['value'] = sprintf($format, round($employee_data[$varsDataMap[$k]], $round));
        }

        $salary_document->set('assoc_vars', $assoc_vars, true);
        $salary_document->set('vars', array_values($assoc_vars), true);
        $salary_document->set('department', $employee_data['department_id'], true);

        $this->registry['db']->StartTrans();
        if ($salary_document->save()) {
            $filters = array(
                'where' => array('d.id = ' . $salary_document->get('id'))
            );
            $new_salary_document = Documents::searchOne($this->registry, $filters);
            $new_salary_document->getVars();

            // write history
            Documents_History::saveData($this->registry, [
                'model' => $new_salary_document,
                'action_type' => 'edit',
                'new_model'   => $new_salary_document,
                'old_model'   => $old_salary_document
            ]);

            $this->success_changes[] = [
                'employee' => $employee_data['name'],
                'document' => $new_salary_document->get('id'),
            ];
        } else {
            $this->failec_changes[] = [
                'employee' => $employee_data['name'],
                'document' => $salary_document->get('id'),
            ];

        }
        $this->registry['db']->CompleteTrans();
    }
}

?>
