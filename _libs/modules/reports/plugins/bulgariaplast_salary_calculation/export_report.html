<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    {literal}
    <style type="text/css">
        br {
            mso-data-placement: same-cell;
        }
        td.rotate_text {
            height: 170px;
            width: 35px;
            white-space: nowrap;
            vertical-align: middle;
            text-align: center;
            mso-rotate:90;
            writing-mode: sideways-lr;
            text-orientation: sideways;
        }
        td.presence_form_holiday {
            text-align: center;
            vertical-align: bottom;
            width: 25px;
        }
        td.presence_form_holiday_salary-col-red,
        td.presence_form_holiday_salary-col-sick
        {
            text-align: center;
            vertical-align: bottom;
            width: 25px;
            color: #FF0000;
        }
        td.presence_form_holiday_salary-col-blue {
            text-align: center;
            vertical-align: bottom;
            width: 25px;
            color: #0000FF;
        }
        td.presence_form_holiday_salary-col-green {
            text-align: center;
            vertical-align: bottom;
            width: 25px;
            color: #498F53;
        }
        .present_form_76_bordered td {
            border: thin solid black;
        }
        .present_form_76_bordered_total td {
            border: thin solid black;
            background-color: #98BCFF;
        }
        .row_bordered_data_row td {
            vertical-align: middle;
            border: thin solid black;
            height: 35px;
        }
        .row_bordered_data_row_salary-row-no-contract td {
            vertical-align: middle;
            border: thin solid black;
            background-color: #F4C38D;
            height: 35px;
        }
        .row_bordered_data_row_salary-row-no-contract_salary-inactive-row td {
            vertical-align: middle;
            border: thin solid black;
            background-color: #F4C38D;
            font-style: italic;
            height: 35px;
        }
        .row_bordered_data_row_salary-inactive-row td {
            vertical-align: middle;
            border: thin solid black;
            font-style: italic;
            height: 35px;
        }
        .working_time {
          color: #000000;
        }
    </style>

    {/literal}
  </head>
  <body>
    <table cellpadding="0" cellspacing="0">
      <tr class="present_form_76_bordered">
        {if in_array('name', $reports_additional_options.visible_columns)}
          <td style="text-align: center; vertical-align: middle; background-color: #C9C9C9;" rowspan="2">{#reports_three_names#|escape}</td>
        {/if}
        {if in_array('department', $reports_additional_options.visible_columns)}
          <td style="text-align: center; vertical-align: middle; background-color: #C9C9C9;" rowspan="2">{#reports_department#|escape}</td>
        {/if}
        {if in_array('position', $reports_additional_options.visible_columns)}
          <td style="text-align: center; vertical-align: middle; background-color: #C9C9C9;" rowspan="2">{#reports_position#|escape}</td>
        {/if}
        {if in_array('days_of_month', $reports_additional_options.visible_columns)}
          <td style="text-align: center; vertical-align: middle; background-color: #C9C9C9;" colspan="{$reports_additional_options.days_in_month|escape|default:0}">{#reports_month_days#|escape}</td>
        {/if}
        {if in_array('days_off_paid', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_days_off_paid#|escape}</td>
        {/if}
        {if in_array('days_off_unpaid', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_days_off_unpaid#|escape}</td>
        {/if}
        {if in_array('sick_leave', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_sick_leave#|escape}</td>
        {/if}
        {if in_array('worked_hours', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_worked_hours#|escape}</td>
        {/if}
        {if in_array('worked_days', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_worked_days#|escape}</td>
        {/if}
        {if in_array('month_salary', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_month_salary#|escape}</td>
        {/if}
        {if in_array('daily_payment', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_daily_payment#|escape}</td>
        {/if}
        {if in_array('overtime', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_overtime#|escape}</td>
        {/if}
        {if in_array('overtime_payment', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_overtime_payment#|escape}</td>
        {/if}
        {if in_array('worked_days_payment', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_overtime_days#|escape}</td>
        {/if}
        {if in_array('sick_payment', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_sickness_payment#|escape}</td>
        {/if}
        {if in_array('paid_days_off_per_contract', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_paid_days_off_per_contract#|escape}</td>
        {/if}
        {if in_array('days_off_paid_payment', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_paid_days_off#|escape}</td>
        {/if}
        {if in_array('work_trip_days', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_working_vacation_days#|escape}</td>
        {/if}
        {if in_array('work_trip_sum', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_working_vacation_sum#|escape}</td>
        {/if}
        {if in_array('deliveries_wo_failures', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_deliveries_wo_problems_count#|escape}</td>
        {/if}
        {if in_array('deliveries_wo_failures_sum', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_deliveries_wo_problems_sum#|escape}</td>
        {/if}
        {if in_array('bonus', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_bonus#|escape}</td>
        {/if}
        {if in_array('bonus_full', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_bonus_full_month#|escape}</td>
        {/if}
        {if in_array('bonus_full_saturday', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_bonus_full_month_with_saturdays#|escape}</td>
        {/if}
        {if in_array('bonus_worked', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_bonus_working#|escape}</td>
        {/if}
        {if in_array('bonus_transport', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_bonus_transport#|escape}</td>
        {/if}
        {if in_array('compensation', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_compensation#|escape}</td>
        {/if}
        {if in_array('other_charges', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_other_charges#|escape}</td>
        {/if}
        {if in_array('total_positive', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_total_positive#|escape}</td>
        {/if}
        {if in_array('deliveries_w_failures', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_deliveries_w_problems_count#|escape}</td>
        {/if}
        {if in_array('deliveries_w_failures_sum', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_deliveries_w_problems_sum#|escape}</td>
        {/if}
        {if in_array('penalties', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_penalties#|escape}</td>
        {/if}
        {if in_array('penalties_checking', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_penalties_checking#|escape}</td>
        {/if}
        {if in_array('penalties_no', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_penalties_no#|escape}</td>
        {/if}
        {if in_array('loans', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_loans#|escape}</td>
        {/if}
        {if in_array('penalties_cat', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_penalties_cat#|escape}</td>
        {/if}
        {if in_array('advance', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_advance#|escape}</td>
        {/if}
        {if in_array('vouchers', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_vouchers#|escape}</td>
        {/if}
        {if in_array('bank_payment', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_bank_payment#|escape}</td>
        {/if}
        {if in_array('total_negative', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_total_negative#|escape}</td>
        {/if}
        {if in_array('total', $reports_additional_options.visible_columns)}
          <td style="background-color: #C9C9C9;" class="rotate_text" rowspan="2">{#reports_total#|escape}</td>
        {/if}
      </tr>
      {if in_array('days_of_month', $reports_additional_options.visible_columns)}
        <tr class="present_form_76_bordered">
          {foreach from=$reports_additional_options.days_list item=dl}
            <td style="text-align: center; vertical-align: middle; background-color: #C9C9C9;">{$dl.label|escape}</td>
          {/foreach}
        </tr>
      {/if}
      {counter start=0 name='item_counter' print=false}
      {foreach from=$reports_results item=employee}
        <tr class="row_bordered_data_row{if !$employee.working_contract}_salary-row-no-contract{/if}{if $employee.working_contract_ended}_salary-inactive-row{/if}">
          {if in_array('name', $reports_additional_options.visible_columns)}
            <td>{$employee.name|escape|default:"&nbsp;"}</td>
          {/if}
          {if in_array('department', $reports_additional_options.visible_columns)}
            <td>{$employee.department|escape|default:"&nbsp;"}</td>
          {/if}
          {if in_array('position', $reports_additional_options.visible_columns)}
            <td>{$employee.position|escape|default:"&nbsp;"}</td>
          {/if}
          {if in_array('days_of_month', $reports_additional_options.visible_columns)}
            {foreach from=$employee.days item=day}
              <td class="presence_form_holiday{if $day.color}_{$day.color}{/if}" style="width: 45px; {if !$day.working} background-color: #FFFDC0;{/if}">{$day.presence}</td>
            {/foreach}
          {/if}
          {if in_array('days_off_paid', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0'; background-color: #FFFDC0;">{if $employee.days_off_paid}{$employee.days_off_paid|escape|default:0}{/if}</td>
          {/if}
          {if in_array('days_off_unpaid', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0'; background-color: #FFFDC0;">{if $employee.days_off_unpaid}{$employee.days_off_unpaid|escape|default:0}{/if}</td>
          {/if}
          {if in_array('sick_leave', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0'; background-color: #FFFDC0;">{if $employee.sick}{$employee.sick|escape|default:0}{/if}</td>
          {/if}
          {if in_array('worked_hours', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '\@'; background-color: #FFFDC0;">{if $employee.worked_hours}{$employee.worked_hours_formatted|escape|default:0}{/if}</td>
          {/if}
          {if in_array('worked_days', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0'; background-color: #FFFDC0;">{if $employee.worked_days}{$employee.worked_days|escape|default:0}{/if}</td>
          {/if}
          {if in_array('month_salary', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; background-color: #FFFDC0;">{if $employee.month_salary}{$employee.month_salary|escape|default:0}{/if}</td>
          {/if}
          {if in_array('daily_payment', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.daily_payment}{$employee.daily_payment|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('overtime', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '\@';">{if $employee.overtime_minutes}{$employee.overtime_formatted|escape|default:0}{/if}</td>
          {/if}
          {if in_array('overtime_payment', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.overtime_payment}{$employee.overtime_payment|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('worked_days_payment', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; background-color: #FFDAB9;"><strong>{if $employee.worked_days_payment}{$employee.worked_days_payment|string_format:"%.2f"|default:"0.00"}{/if}</strong></td>
          {/if}
          {if in_array('sick_payment', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; color: #FF0000;">{if $employee.sick_payment}{$employee.sick_payment|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('paid_days_off_per_contract', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; color: #0000FF;">{if $employee.paid_days_off_per_contract}{$employee.paid_days_off_per_contract|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('days_off_paid_payment', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; color: #0000FF;">{if $employee.days_off_paid_payment}{$employee.days_off_paid_payment|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('work_trip_days', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0'; color: #498F53;">{if $employee.work_trip_days}{$employee.work_trip_days|escape|default:0}{/if}</td>
          {/if}
          {if in_array('work_trip_sum', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; color: #498F53;">{if $employee.work_trip_sum}{$employee.work_trip_sum|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('deliveries_wo_failures', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0';">{if $employee.deliveries_wo_failures}{$employee.deliveries_wo_failures|escape|default:0}{/if}</td>
          {/if}
          {if in_array('deliveries_wo_failures_sum', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.deliveries_wo_failures_sum}{$employee.deliveries_wo_failures_sum|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('bonus', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; color: #FF0000;">{if $employee.bonus}{$employee.bonus|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('bonus_full', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.bonus_full}{$employee.bonus_full|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('bonus_full_saturday', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.bonus_full_saturday}{$employee.bonus_full_saturday|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('bonus_worked', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.bonus_worked}{$employee.bonus_worked|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('bonus_transport', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.bonus_transport}{$employee.bonus_transport|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('compensation', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.compensation}{$employee.compensation|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('other_charges', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.other_charges}{$employee.other_charges|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('total_positive', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; background-color: #FFDAB9;"><strong>{if $employee.total_positive}{$employee.total_positive|string_format:"%.2f"|default:"0.00"}{/if}</strong></td>
          {/if}
          {if in_array('deliveries_w_failures', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0';">{if $employee.deliveries_w_failures}{$employee.deliveries_w_failures|escape|default:0}{/if}</td>
          {/if}
          {if in_array('deliveries_w_failures_sum', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.deliveries_w_failures_sum}{$employee.deliveries_w_failures_sum|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('penalties', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.penalties}{$employee.penalties|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('penalties_checking', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.penalties_checking}{$employee.penalties_checking|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('penalties_no', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00'; color: #FF0000;">{if $employee.penalties_no}{$employee.penalties_no|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('loans', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.loans}{$employee.loans|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('penalties_cat', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.penalties_cat}{$employee.penalties_cat|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('advance', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.advance}{$employee.advance|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('vouchers', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.vouchers}{$employee.vouchers|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('bank_payment', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.bank_payment}{$employee.bank_payment|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('total_negative', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0\.00';">{if $employee.total_negative}{$employee.total_negative|string_format:"%.2f"|default:"0.00"}{/if}</td>
          {/if}
          {if in_array('total', $reports_additional_options.visible_columns)}
            <td style="width: 55px; mso-number-format: '0'; background-color: #FFDAB9;"><strong>{if $employee.total}{$employee.total|string_format:"%d"|default:"0"}{/if}</strong></td>
          {/if}
        </tr>
      {foreachelse}
        <tr class="present_form_76_bordered">
          <td colspan="{$reports_additional_options.total_colspan}">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}

      {if in_array('total', $reports_additional_options.visible_columns)}
        <tr class="present_form_76_bordered_total">
          {if in_array('name', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('department', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('position', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('days_of_month', $reports_additional_options.visible_columns)}
            <td colspan="{$reports_additional_options.days_in_month|escape|default:0}">&nbsp;</td>
          {/if}
          {if in_array('days_off_paid', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0';"><strong>{$reports_additional_options.totals.total_days_off_paid|string_format:"%d"|default:"0"}</strong></td>
          {/if}
          {if in_array('days_off_unpaid', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0';"><strong>{$reports_additional_options.totals.total_days_off_unpaid|string_format:"%d"|default:"0"}</strong></td>
          {/if}
          {if in_array('sick_leave', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0';"><strong>{$reports_additional_options.totals.total_days_sick|string_format:"%d"|default:"0"}</strong></td>
          {/if}
          {if in_array('worked_hours', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0';"><strong>{$reports_additional_options.totals.total_worked_hours_formatted|default:"00:00"}</strong></td>
          {/if}
          {if in_array('worked_days', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0';"><strong>{$reports_additional_options.totals.total_worked_days|string_format:"%d"|default:"0"}</strong></td>
          {/if}
          {if in_array('month_salary', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('daily_payment', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('overtime', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('overtime_payment', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('worked_days_payment', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('sick_payment', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('paid_days_off_per_contract', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0\.00'; color: color: #0000FF;;"><strong>{$reports_additional_options.totals.total_paid_days_off_per_contract|string_format:"%.2f"|default:"0.00"}</strong></td>
          {/if}
          {if in_array('days_off_paid_payment', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0\.00'; color: color: #0000FF;;"><strong>{$reports_additional_options.totals.total_days_off_paid_payment|string_format:"%.2f"|default:"0.00"}</strong></td>
          {/if}
          {if in_array('work_trip_days', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('work_trip_sum', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('deliveries_wo_failures', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('deliveries_wo_failures_sum', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('bonus', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('bonus_full', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('bonus_full_saturday', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('bonus_worked', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0\.00';"><strong>{$reports_additional_options.totals.total_bonus_worked|string_format:"%.2f"|default:"0.00"}</strong></td>
          {/if}
          {if in_array('bonus_transport', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('compensation', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('other_charges', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('total_positive', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0\.00';"><strong>{$reports_additional_options.totals.total_total_positive|string_format:"%.2f"|default:"0.00"}</strong></td>
          {/if}
          {if in_array('deliveries_w_failures', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('deliveries_w_failures_sum', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('penalties', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('penalties_checking', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('penalties_no', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('loans', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('penalties_cat', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('advance', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('vouchers', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('bank_payment', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('total_negative', $reports_additional_options.visible_columns)}
            <td>&nbsp;</td>
          {/if}
          {if in_array('total', $reports_additional_options.visible_columns)}
            <td style="mso-number-format: '0';"><strong>{$reports_additional_options.totals.total|string_format:"%d"|default:"0"}</strong></td>
          {/if}
        </tr>
      {/if}
    </table>
    <h3 style="text-decoration: underline; margin-bottom: 5px;">{#reports_legend#|escape}</h3>
    {foreach from=$reports_additional_options.legend item=leg name=lg}
      <strong>{$leg.code|escape}</strong> - <i>{$leg.name|escape}</i>{if !$smarty.foreach.lg.last}<br />{/if}
    {/foreach}
  </body>
</html>
