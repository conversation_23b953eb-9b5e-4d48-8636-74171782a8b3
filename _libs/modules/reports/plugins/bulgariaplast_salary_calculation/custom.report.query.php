<?php
    Class Bulgariaplast_Salary_Calculation Extends Reports {
        private static $registry;
        private static $lang = '';
        private static $filters = [];
        private static $additional_data = [];

        public static function buildQuery(&$registry, $filters = array())
        {
            $final_results = self::prepareMainReportData($registry, $filters);
            uasort($final_results, array('self', 'sortEmployees'));
            self::defineVisibleColumns($registry);

            $final_results['additional_options'] = self::$additional_data;
            $registry->set('hide_export_button', true, true);
            if (!empty(self::$filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        /**
         * Function to get the basic information for the employees
         *
         * @param DateTime $contract_date_from
         * @param DateTime $contract_date_to
         * @return array
         */
        private static function getEmployeesMainData(
            DateTime $contract_date_from,
            DateTime $contract_date_to
        ): array {
            // get all the needed vars info for the employee
            $employee_vars = array(EMPLOYEE_VAR_SALARY_MONTH, EMPLOYEE_VAR_SALARY_DAY, EMPLOYEE_VAR_BONUS_PER_DAY, EMPLOYEE_VAR_POSITION,
                                   EMPLOYEE_VAR_BONUS_PER_MONTH, EMPLOYEE_VAR_BONUS_FULL_MONTH, EMPLOYEE_VAR_OVERTIME_COEFFICIENT,
                                   EMPLOYEE_VAR_PAID_LEAVE, EMPLOYEE_VAR_BONUS_FULL_SATURDAY, EMPLOYEE_VAR_BONUS_TRANSPORT);

            if (defined('FILTERS_SHOW_OFFICE') && !empty(FILTERS_SHOW_OFFICE)) {
                $employee_vars[] = EMPLOYEE_VAR_OFFICE;
            }

            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Customer" AND `model_type`="' . PH_CUSTOMER_EMPLOYEE . '" AND `name` IN ("' . implode('","',
                    $employee_vars) . '")' . "\n";
            $employee_vars = self::$registry['db']->GetAssoc($sql);

            $contract_types = [CONTRACT_WORKING_CONTRACT_TYPE, CONTRACT_NO_WORKING_CONTRACT_TYPE];

            // get the needed employees
            $sql_employees = array(
                'select' => 'SELECT CONCAT(c.id, "_", co.id) as idx, c.id, CONCAT(ci18n.name, " ", ci18n.lastname) as name, co.active as active_contract, co.type as contract_type,' . "\n" .
                            ' co.date_start as contract_start, co.date_validity as contract_end, c.active, di18n.name as department, c.department as department_id, ' . "\n" .
                            ' c_cstm_sm.value as salary_month, c_cstm_sd.value as salary_day, c_cstm_bd.value as bonus_per_day,' . "\n" .
                            ' c_cstm_bm.value as bonus_per_month, c_cstm_bf.value as bonus_full_month, c_cstm_bfs.value as bonus_full_saturday, ' . "\n" .
                            ' c_cstm_bt.value as bonus_transport, "" as salary_document, tm.tag_id as standard_working_time, ' . "\n" .
                            ' c_cstm_oc.value as overtime_coefficient, c_cstm_pl.value as paid_leave, c_cstm_pos.value as position_id, ni18n.name as position' . "\n",
                'from' => 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                    'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                    ' ON (ci18n.parent_id=c.id AND ci18n.lang="' . self::$lang . '")' . "\n" .
                    'INNER JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                    ' ON (co.type IN ("' . implode('","', $contract_types) . '") AND co.active=1 AND co.deleted_by=0
                                  AND co.annulled_by=0 AND co.customer=c.id AND co.subtype="contract"
                                  AND co.date_start<="' . $contract_date_to->format('Y-m-d') . '"
                                  AND (co.date_validity IS NULL OR co.date_validity="0000-00-00" OR co.date_validity>="' . $contract_date_from->format('Y-m-d') . '"))' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_sm' . "\n" .
                    ' ON (c_cstm_sm.model_id=c.id AND c_cstm_sm.var_id="' . ($employee_vars[EMPLOYEE_VAR_SALARY_MONTH] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_sd' . "\n" .
                    ' ON (c_cstm_sd.model_id=c.id AND c_cstm_sd.var_id="' . ($employee_vars[EMPLOYEE_VAR_SALARY_DAY] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_bd' . "\n" .
                    ' ON (c_cstm_bd.model_id=c.id AND c_cstm_bd.var_id="' . ($employee_vars[EMPLOYEE_VAR_BONUS_PER_DAY] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_bm' . "\n" .
                    ' ON (c_cstm_bm.model_id=c.id AND c_cstm_bm.var_id="' . ($employee_vars[EMPLOYEE_VAR_BONUS_PER_MONTH] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_bf' . "\n" .
                    ' ON (c_cstm_bf.model_id=c.id AND c_cstm_bf.var_id="' . ($employee_vars[EMPLOYEE_VAR_BONUS_FULL_MONTH] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_oc' . "\n" .
                    ' ON (c_cstm_oc.model_id=c.id AND c_cstm_oc.var_id="' . ($employee_vars[EMPLOYEE_VAR_OVERTIME_COEFFICIENT] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_bfs' . "\n" .
                    ' ON (c_cstm_bfs.model_id=c.id AND c_cstm_bfs.var_id="' . ($employee_vars[EMPLOYEE_VAR_BONUS_FULL_SATURDAY] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_bt' . "\n" .
                    ' ON (c_cstm_bt.model_id=c.id AND c_cstm_bt.var_id="' . ($employee_vars[EMPLOYEE_VAR_BONUS_TRANSPORT] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_pl' . "\n" .
                    ' ON (c_cstm_pl.model_id=c.id AND c_cstm_pl.var_id="' . ($employee_vars[EMPLOYEE_VAR_PAID_LEAVE] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_pos' . "\n" .
                    ' ON (c_cstm_pos.model_id=c.id AND c_cstm_pos.var_id="' . ($employee_vars[EMPLOYEE_VAR_POSITION] ??  '') . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                    ' ON (ni18n.parent_id=c_cstm_pos.value AND ni18n.lang="' . self::$lang . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                    ' ON (tm.model_id=c.id AND tm.model="Customer" AND tm.tag_id="' . EMPLOYEE_TAG_STANDARD_WORKING_TIME . '")' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS di18n' . "\n" .
                    ' ON (di18n.parent_id=c.department AND di18n.lang="' . self::$lang . '")' . "\n",
                'where' => 'WHERE c.type="' . PH_CUSTOMER_EMPLOYEE . '" AND c.deleted_by=0',
                'order' => 'ORDER BY c.active DESC, CONCAT(ci18n.name, " ", ci18n.lastname) ASC',
            );

            // get the needed employees
            if (!empty(self::$filters['office'])) {
                $sql_employees['from'] .= "\n" . 'INNER JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_o' . "\n" .
                    ' ON (c_cstm_o.model_id=c.id AND c_cstm_o.var_id="' . ((!empty(defined('FILTERS_SHOW_OFFICE') && !empty(FILTERS_SHOW_OFFICE) && $employee_vars[EMPLOYEE_VAR_OFFICE])) ? $employee_vars[EMPLOYEE_VAR_OFFICE] : '') . '" AND c_cstm_o.value="' . self::$filters['office'] . '")';
            }

            // check for department filter
            if (!empty(self::$filters['department'])) {
                $departments = Departments::getTreeDescendantsIds(self::$registry, self::$filters['department']);
                $sql_employees['where'] .= ' AND c.department IN ("' . implode('","', $departments) . '")';
            }

            // check for position filter
            if (!empty(self::$filters['position'])) {
                $sql_employees['where'] .= ' AND c_cstm_pos.value IN ("' . self::$filters['position'] . '")';
            }

            // check for team filter
            if (!empty(self::$filters['employee'])) {
                $employees = array_filter(self::$filters['employee']);
                if (!empty($employees)) {
                    // get the data for the team
                    $sql_employees['where'] .= ' AND c.id IN ("' . implode('","', $employees) . '")';
                }
            }

            // get the employees list
            return self::$registry['db']->GetAssoc(implode("\n", $sql_employees));
        }

        /**
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getDaysOff($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            // get days off
            $days_off_vars = array(DOC_DAYS_OFF_VAR_DAY_START, DOC_DAYS_OFF_VAR_DAY_END, DOC_DAYS_OFF_VAR_TYPE, DOC_DAYS_OFF_AVAILABLE);
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_DAYS_OFF_TYPE . '" AND `name` IN ("' . implode('","',
                    $days_off_vars) . '")' . "\n";
            $days_off_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d.customer as employee, d_cstm_start_date.value as start_date, d_cstm_end_date.value as end_date,
                           d_cstm_type.value as kind, d_cstm_avb_pdo.value as available_paid_days_off' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_start_date' . "\n" .
                   ' ON (d_cstm_start_date.model_id=d.id AND d_cstm_start_date.var_id="' . (!empty($days_off_vars[DOC_DAYS_OFF_VAR_DAY_START]) ? $days_off_vars[DOC_DAYS_OFF_VAR_DAY_START] : '') . '" AND d_cstm_start_date.value<="' . $last_day_of_month->format('Y-m-d') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_end_date' . "\n" .
                   ' ON (d_cstm_end_date.model_id=d.id AND d_cstm_end_date.var_id="' . (!empty($days_off_vars[DOC_DAYS_OFF_VAR_DAY_END]) ? $days_off_vars[DOC_DAYS_OFF_VAR_DAY_END] : '') . '" AND d_cstm_end_date.value>="' . $first_day_of_month->format('Y-m-d') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type' . "\n" .
                   ' ON (d_cstm_type.model_id=d.id AND d_cstm_type.var_id="' . (!empty($days_off_vars[DOC_DAYS_OFF_VAR_TYPE]) ? $days_off_vars[DOC_DAYS_OFF_VAR_TYPE] : '') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_avb_pdo' . "\n" .
                   ' ON (d_cstm_avb_pdo.model_id=d.id AND d_cstm_avb_pdo.var_id="' . (!empty($days_off_vars[DOC_DAYS_OFF_AVAILABLE]) ? $days_off_vars[DOC_DAYS_OFF_AVAILABLE] : '') . '")' . "\n" .
                   'WHERE d.type="' . DOC_DAYS_OFF_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.customer IN ("' . implode('","',
                       $employees_ids) . '")' . "\n";

            if (!empty(DOC_DAYS_OFF_STATUSES_TO_INCLUDE)) {
                $statuses_to_search = array_filter(preg_split('#\s*,\s*#', DOC_DAYS_OFF_STATUSES_TO_INCLUDE));
                if (!empty($statuses_to_search)) {
                    $sql .= ' AND CONCAT(d.status, "_", d.substatus) IN ("' . implode('","', $statuses_to_search) . '")' . "\n";
                }
            }
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getWorktrips($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            // get worktrips
            $days_off_vars = array(DOC_WORKTRIP_EMPLOYEE, DOC_WORKTRIP_END_DATE, DOC_WORKTRIP_START_DATE);
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_WORKTRIP_TYPE . '" AND `name` IN ("' . implode('","',
                    $days_off_vars) . '")' . "\n";
            $days_off_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d_cstm_wt_emp.value as employee, d_cstm_wt_start.value as start_date, d_cstm_wt_end.value as end_date, IF(d_cstm_wt_start.value=d_cstm_wt_end.value, "day", "overnight") as `type`' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_wt_emp' . "\n" .
                   ' ON (d_cstm_wt_emp.model_id=d.id AND d_cstm_wt_emp.var_id="' . (!empty($days_off_vars[DOC_WORKTRIP_EMPLOYEE]) ? $days_off_vars[DOC_WORKTRIP_EMPLOYEE] : '') . '" AND d_cstm_wt_emp.value IN ("' . implode('","', $employees_ids) . '"))' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_wt_start' . "\n" .
                   ' ON (d_cstm_wt_start.model_id=d.id AND d_cstm_wt_start.var_id="' . (!empty($days_off_vars[DOC_WORKTRIP_START_DATE]) ? $days_off_vars[DOC_WORKTRIP_START_DATE] : '') . '" AND d_cstm_wt_start.value<="' . $last_day_of_month->format('Y-m-d') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_wt_end' . "\n" .
                   ' ON (d_cstm_wt_end.model_id=d.id AND d_cstm_wt_end.var_id="' . (!empty($days_off_vars[DOC_WORKTRIP_END_DATE]) ? $days_off_vars[DOC_WORKTRIP_END_DATE] : '') . '" AND d_cstm_wt_end.value>="' . $first_day_of_month->format('Y-m-d') . '")' . "\n" .
                   'WHERE d.type="' . DOC_WORKTRIP_TYPE . '" AND d.active=1 AND d.deleted_by=0' . "\n";
            if (!empty(DOC_WORKTRIP_STATUS)) {
                $sql .= ' AND d.substatus="' . DOC_WORKTRIP_STATUS . '"';
            }
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * Get sickness documents
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getSicknessDocuments($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            // get the sickness days
            $sickness_vars = array(DOC_SICKENSS_VAR_DAY_START, DOC_SICKENSS_VAR_DAY_END);
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_SICKENSS_TYPE . '" AND `name` IN ("' . implode('","', $sickness_vars) . '")' . "\n";
            $sickness_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d.employee as employee, d_cstm_start_date.value as start_date, d_cstm_end_date.value as end_date' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_start_date' . "\n" .
                   ' ON (d_cstm_start_date.model_id=d.id AND d_cstm_start_date.var_id="' . (!empty($sickness_vars[DOC_SICKENSS_VAR_DAY_START]) ? $sickness_vars[DOC_SICKENSS_VAR_DAY_START] : '') . '" AND d_cstm_start_date.value<="' . $last_day_of_month->format('Y-m-d') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_end_date' . "\n" .
                   ' ON (d_cstm_end_date.model_id=d.id AND d_cstm_end_date.var_id="' . (!empty($sickness_vars[DOC_SICKENSS_VAR_DAY_END]) ? $sickness_vars[DOC_SICKENSS_VAR_DAY_END] : '') . '" AND d_cstm_end_date.value>="' . $first_day_of_month->format('Y-m-d') . '")' . "\n" .
                   'WHERE d.type="' . DOC_SICKENSS_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.employee IN ("' . implode('","', $employees_ids) . '")' . "\n";

            if (!empty(DOC_SICKENSS_STATUSES_TO_EXCLUDE)) {
                $statuses_to_exclude = array_filter(preg_split('#\s*,\s*#', DOC_SICKENSS_STATUSES_TO_EXCLUDE));
                if (!empty($statuses_to_exclude)) {
                    $sql .= ' AND CONCAT(d.status, "_", d.substatus) NOT IN ("' . implode('","', $statuses_to_exclude) . '")' . "\n";
                }
            }

            return self::$registry['db']->GetAll($sql);
        }

        /**
         * Get working hours
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getWorkingHours($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            if (empty($employees_ids)) {
                return [];
            }
            $add_vars = array(DOC_WORKING_HOURS_EMPLOYEE, DOC_WORKING_HOURS_WORKING_TIME, DOC_WORKING_HOURS_OVERTIME);
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_WORKING_HOURS_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d_cstm_empl.value as employee, d.date, d_cstm_wh.value as working_hours, d_cstm_ot.value as overtime, 0 as working_minutes, 0 as overtime_minutes' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_empl' . "\n" .
                   ' ON (d_cstm_empl.model_id=d.id AND d_cstm_empl.var_id="' . (!empty($add_vars[DOC_WORKING_HOURS_EMPLOYEE]) ? $add_vars[DOC_WORKING_HOURS_EMPLOYEE] : '') . '" AND d_cstm_empl.value IN ("' . implode('","', $employees_ids) . '"))' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_wh' . "\n" .
                   ' ON (d_cstm_wh.model_id=d.id AND d_cstm_wh.var_id="' . (!empty($add_vars[DOC_WORKING_HOURS_WORKING_TIME]) ? $add_vars[DOC_WORKING_HOURS_WORKING_TIME] : '') . '" AND d_cstm_wh.num=d_cstm_empl.num)' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ot' . "\n" .
                   ' ON (d_cstm_ot.model_id=d.id AND d_cstm_ot.var_id="' . (!empty($add_vars[DOC_WORKING_HOURS_OVERTIME]) ? $add_vars[DOC_WORKING_HOURS_OVERTIME] : '') . '" AND d_cstm_ot.num=d_cstm_empl.num)' . "\n" .
                   'WHERE d.type="' . DOC_WORKING_HOURS_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.date>="' . $first_day_of_month->format('Y-m-d') . '" AND d.date<="' . $last_day_of_month->format('Y-m-d') . '"' . "\n";
            $working_hours = self::$registry['db']->GetAll($sql);

            foreach ($working_hours as $k => $wh) {
                if (preg_match('#\:#', $wh['working_hours'])) {
                    // Split the string into hours and minutes
                    list($hours, $minutes) = explode(':', $wh['working_hours']);

                    // Explicitly cast to integers
                    $hours = (int)$hours;
                    $minutes = (int)$minutes;
                    if ($minutes<30) {
                        $minutes = 0;
                    } elseif ($minutes<50) {
                        $minutes = 30;
                    } else {
                        $minutes = 0;
                        $hours++;
                    }
                    $working_hours[$k]['working_minutes'] = $hours*60 + $minutes;
                }
                if (preg_match('#\:#', $wh['overtime'])) {
                    // Split the string into hours and minutes
                    list($hours, $minutes) = explode(':', $wh['overtime']);

                    // Explicitly cast to integers
                    $hours = (int)$hours;
                    $minutes = (int)$minutes;

                    if ($minutes >= 55) {
                        $hours++;
                    }
                    $minutes = 0;

                    $working_hours[$k]['overtime_minutes'] = $hours*60 + $minutes;
                }
            }

            return $working_hours;
        }

        /**
         * Get salary documents
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getSalaryDocuments($filters)
        {
            // get the sickness days
            $add_vars = [DOC_SALARY_CONTRACT, DOC_SALARY_YEAR, DOC_SALARY_MONTH, DOC_SALARY_BANK_PAYMENT, DOC_SALARY_SICK_PAYMENT,
                         DOC_SALARY_PAID_DAYS_OFF_WORKING_CONTRACT, DOC_SALARY_OTHER_CHARGES, DOC_SALARY_VOUCHERS];
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_SALARY_DOCUMENT_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT CONCAT(d.customer, "_", d_cstm_sal_contract.value) as idx, d.id as document_id, d_cstm_sal_yr.value as salary_year, ' . "\n" .
                   '       d_cstm_sal_mnth.value as salary_month, d_cstm_sal_bank.value as bank_payment, ' . "\n" .
                   '       d_cstm_sal_sick.value as sick_payment, d_cstm_pdopc.value as paid_days_off_per_contract, ' . "\n" .
                   '       d_cstm_sal_oc.value as other_charges, d_cstm_sal_v.value as vouchers ' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sal_yr' . "\n" .
                   ' ON (d_cstm_sal_yr.model_id=d.id AND d_cstm_sal_yr.var_id="' . ($add_vars[DOC_SALARY_YEAR] ?? '') . '"' . (!empty($filters['year']) ? ' AND d_cstm_sal_yr.value="' . $filters['year'] . '"' : '') . ')' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sal_mnth' . "\n" .
                   ' ON (d_cstm_sal_mnth.model_id=d.id AND d_cstm_sal_mnth.var_id="' . ($add_vars[DOC_SALARY_MONTH] ?? '') . '"' . (!empty($filters['month']) ? ' AND d_cstm_sal_mnth.value="' . $filters['month'] . '"' : '') . ')' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sal_contract' . "\n" .
                   ' ON (d_cstm_sal_contract.model_id=d.id AND d_cstm_sal_contract.var_id="' . ($add_vars[DOC_SALARY_CONTRACT] ?? '') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sal_bank' . "\n" .
                   ' ON (d_cstm_sal_bank.model_id=d.id AND d_cstm_sal_bank.var_id="' . ($add_vars[DOC_SALARY_BANK_PAYMENT] ?? '') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sal_sick' . "\n" .
                   ' ON (d_cstm_sal_sick.model_id=d.id AND d_cstm_sal_sick.var_id="' . ($add_vars[DOC_SALARY_SICK_PAYMENT] ?? '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pdopc' . "\n" .
                   ' ON (d_cstm_pdopc.model_id=d.id AND d_cstm_pdopc.var_id="' . ($add_vars[DOC_SALARY_PAID_DAYS_OFF_WORKING_CONTRACT] ?? '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sal_oc' . "\n" .
                   ' ON (d_cstm_sal_oc.model_id=d.id AND d_cstm_sal_oc.var_id="' . ($add_vars[DOC_SALARY_OTHER_CHARGES] ?? '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sal_v' . "\n" .
                   ' ON (d_cstm_sal_v.model_id=d.id AND d_cstm_sal_v.var_id="' . ($add_vars[DOC_SALARY_VOUCHERS] ?? '') . '")' . "\n" .
                   'WHERE d.type="' . DOC_SALARY_DOCUMENT_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.status!="closed"' . "\n";
            if (!empty($filters['employees'])) {
                $sql .= ' AND d.customer IN (' . implode(',', $filters['employees']) . ')';
            }
            if (!empty(DOC_SALARY_STATUSES_TO_EXCLUDE)) {
                $statuses_to_exclude = array_filter(preg_split('#\s*,\s*#', DOC_SALARY_STATUSES_TO_EXCLUDE));
                if (!empty($statuses_to_exclude)) {
                    $sql .= ' AND CONCAT(d.status, "_", d.substatus) NOT IN ("' . implode('","', $statuses_to_exclude) . '")' . "\n";
                }
            }

            return self::$registry['db']->GetAssoc($sql);
        }

        /**
         * Get transport sheets documents
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getTransportSheets($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            if (empty($employees_ids)) {
                return [];
            }

            // get the sickness days
            $add_vars = [DOC_TRANSPORT_SHEET_DRIVER];
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_TRANSPORT_SHEET_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d_cstm_drv.value as employee, d.date as transport_sheet_date, ' . "\n" .
                   '       t_wo_shortage.tag_id wo_failures, t_w_shortage.tag_id w_failures' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_drv' . "\n" .
                   ' ON (d_cstm_drv.model_id=d.id AND d_cstm_drv.var_id="' . ($add_vars[DOC_TRANSPORT_SHEET_DRIVER] ?? '') . '" AND d_cstm_drv.value IN ("' . implode('","', $employees_ids) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t_wo_shortage' . "\n" .
                   ' ON (t_wo_shortage.model_id=d.id AND t_wo_shortage.model="Document" AND t_wo_shortage.tag_id="' . DOC_TRANSPORT_SHEET_TAG_DELIVERIES_WO_FAILURES . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS t_w_shortage' . "\n" .
                   ' ON (t_w_shortage.model_id=d.id AND t_w_shortage.model="Document" AND t_w_shortage.tag_id="' . DOC_TRANSPORT_SHEET_TAG_DELIVERIES_W_FAILURES . '")' . "\n" .
                   'WHERE d.type="' . DOC_TRANSPORT_SHEET_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.date>="' . $first_day_of_month->format('Y-m-d') . '" AND d.date<="' . $last_day_of_month->format('Y-m-d') . '"' . "\n";
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * Get salary extra sums
         *
         * @return mixed
         */
        private static function getSalaryExtraSums()
        {
            // get the salary extra vars
            $add_vars = [DOC_SALARY_EXTRA_SUMS_WORKTRIP_DAY, DOC_SALARY_EXTRA_SUMS_WORKTRIP_OVERNIGHT, DOC_SALARY_EXTRA_SUMS_DELIVERY_WO_FAILURES, DOC_SALARY_EXTRA_SUMS_DELIVERY_W_FAILURES];
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Nomenclature" AND `model_type`="' . DOC_SALARY_EXTRA_SUMS_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            // select the latest salary extra documents
            $sql = 'SELECT n.id '. "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'WHERE n.type="' . DOC_SALARY_EXTRA_SUMS_TYPE . '" AND n.active=1 AND n.deleted_by=0' . "\n" .
                   'ORDER BY n.id DESC' . "\n" .
                   'LIMIT 1' . "\n";
            $salary_nom_id = self::$registry['db']->GetOne($sql);

            $sql = 'SELECT n_cstm_wd.value as worktrip_day, n_cstm_wo.value as worktrip_overnight, ' . "\n" .
                   '       n_cstm_dwof.value as deliveries_wo_failures, n_cstm_dwf.value as deliveries_w_failures' . "\n" .
                   'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_wd' . "\n" .
                   ' ON (n_cstm_wd.model_id=n.id AND n_cstm_wd.var_id="' . ($add_vars[DOC_SALARY_EXTRA_SUMS_WORKTRIP_DAY] ?? '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_wo' . "\n" .
                   ' ON (n_cstm_wo.model_id=n.id AND n_cstm_wo.var_id="' . ($add_vars[DOC_SALARY_EXTRA_SUMS_WORKTRIP_OVERNIGHT] ?? '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_dwof' . "\n" .
                   ' ON (n_cstm_dwof.model_id=n.id AND n_cstm_dwof.var_id="' . ($add_vars[DOC_SALARY_EXTRA_SUMS_DELIVERY_WO_FAILURES] ?? '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_dwf' . "\n" .
                   ' ON (n_cstm_dwf.model_id=n.id AND n_cstm_dwf.var_id="' . ($add_vars[DOC_SALARY_EXTRA_SUMS_DELIVERY_W_FAILURES] ?? '') . '")' . "\n" .
                   'WHERE n.id="' . $salary_nom_id . '"' . "\n";
            return self::$registry['db']->GetRow($sql);
        }

        /**
         * Get bonus documents
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getBonuses($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            // get the bonuses
            $add_vars = [DOC_BONUS_AMOUNT];
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_BONUS_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d.customer, d_cstm_bon.value as bonus, d.date, di18n.name as description' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                   ' ON (di18n.parent_id=d.id AND di18n.lang="' . self::$lang . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_bon' . "\n" .
                   ' ON (d_cstm_bon.model_id=d.id AND d_cstm_bon.var_id="' . ($add_vars[DOC_BONUS_AMOUNT] ?? '') . '")' . "\n" .
                   'WHERE d.type="' . DOC_BONUS_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.date>="' . $first_day_of_month->format('Y-m-d') . '" AND d.date<="' . $last_day_of_month->format('Y-m-d') . '" AND d.customer IN ("' . implode('","', $employees_ids) . '")' . "\n";
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * Get compensation documents
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getCompensations($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            // get the compensations
            $add_vars = [DOC_COMPENSATIONS_SUM];
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_COMPENSATIONS_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d.customer, d_cstm_comp.value as compensation, d.date' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_comp' . "\n" .
                   ' ON (d_cstm_comp.model_id=d.id AND d_cstm_comp.var_id="' . ($add_vars[DOC_COMPENSATIONS_SUM] ?? '') . '")' . "\n" .
                   'WHERE d.type="' . DOC_COMPENSATIONS_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.date>="' . $first_day_of_month->format('Y-m-d') . '" AND d.date<="' . $last_day_of_month->format('Y-m-d') . '" AND d.customer IN ("' . implode('","', $employees_ids) . '")' . "\n";
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * Get penalties documents
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @param $last_day_of_month
         * @return mixed
         */
        private static function getPenalties($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            // get the penalties
            $add_vars = [DOC_PENALTIES_SUM, DOC_PENALTIES_REASON];
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_PENALTIES_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d.customer, d.date, di18n.name as penalty_description, d_cstm_pen.value as sum, d_cstm_reas.value as reason' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' as di18n' . "\n" .
                   ' ON (di18n.parent_id=d.id AND di18n.lang="' . self::$lang . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pen' . "\n" .
                   ' ON (d_cstm_pen.model_id=d.id AND d_cstm_pen.var_id="' . ($add_vars[DOC_PENALTIES_SUM] ?? '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_reas' . "\n" .
                   ' ON (d_cstm_reas.model_id=d.id AND d_cstm_reas.var_id="' . ($add_vars[DOC_PENALTIES_REASON] ?? '') . '")' . "\n" .
                   'WHERE d.type="' . DOC_PENALTIES_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.date>="' . $first_day_of_month->format('Y-m-d') . '" AND d.date<="' . $last_day_of_month->format('Y-m-d') . '" AND d.customer IN ("' . implode('","', $employees_ids) . '")' . "\n";
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * Get loans documents
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @return mixed
         */
        private static function getLoans($employees_ids, $first_day_of_month)
        {
            // get the loans
            $add_vars = [DOC_LOANS_MONTH, DOC_LOANS_YEAR, DOC_LOANS_SUM_LEFT];
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type`="' . DOC_LOANS_TYPE . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
            $add_vars = self::$registry['db']->GetAssoc($sql);

            $sql = 'SELECT d.id, d.customer, d_cstm_paid_sum.value as received_sum, (
                        SELECT SUM(IFNULL(d_cstm_left_sum.value, 0))
                        FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_left_sum
                        WHERE d_cstm_left_sum.model_id = d.id AND d_cstm_left_sum.var_id="' . ($add_vars[DOC_LOANS_SUM_LEFT] ?? '') . '" AND d_cstm_left_sum.num > d_cstm_yr.num
                    ) AS loans_left' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_yr' . "\n" .
                   ' ON (d_cstm_yr.model_id=d.id AND d_cstm_yr.var_id="' . ($add_vars[DOC_LOANS_YEAR] ?? '') . '" AND d_cstm_yr.value="' . $first_day_of_month->format('Y') . '")' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_mo' . "\n" .
                   ' ON (d_cstm_mo.model_id=d.id AND d_cstm_mo.var_id="' . ($add_vars[DOC_LOANS_MONTH] ?? '') . '" AND d_cstm_mo.num=d_cstm_yr.num AND d_cstm_mo.value="' . $first_day_of_month->format('n') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paid_sum' . "\n" .
                   ' ON (d_cstm_paid_sum.model_id=d.id AND d_cstm_paid_sum.var_id="' . ($add_vars[DOC_LOANS_SUM_LEFT] ?? '') . '" AND d_cstm_paid_sum.num=d_cstm_yr.num)' . "\n" .
                   'WHERE d.type="' . DOC_LOANS_TYPE . '" AND d.active=1 AND d.deleted_by=0 AND d.customer IN ("' . implode('","', $employees_ids) . '")' . "\n";
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * Get extra expenses
         *
         * @param $employees_ids
         * @param $first_day_of_month
         * @return mixed
         */
        private static function getExtraExpenses($employees_ids, $first_day_of_month, $last_day_of_month)
        {
            $expenses_ids = [NOM_ADVANCE, NOM_PENALTY_CAT];
            // get the extra expenses
            $sql = 'SELECT gt2.employee_id as customer, gt2.article_id as `type`, gt2.price_with_discount as `sum`, fer.issue_date' . "\n" .
                   'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                   'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                   ' ON (gt2.model="Finance_Expenses_Reason" AND gt2.model_id=fer.id AND gt2.employee_id IN ("' . implode('","', $employees_ids) . '") AND gt2.employee_id!="" AND gt2.employee_id!="0" AND gt2.article_id IN ("' . implode('","', $expenses_ids) . '"))' . "\n" .
                   'WHERE fer.type="' . REASON_EXPENSE_TYPE . '" AND fer.active=1 AND fer.annulled_by=0 AND fer.status="finished" AND fer.issue_date>="' . $first_day_of_month->format('Y-m-d') . '" AND fer.issue_date<="' . $last_day_of_month->format('Y-m-d') . '"' . "\n";
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * @return array
         */
        public static function prepareMainReportData($registry, $filters): array
        {
            //set model lang filter
            self::$registry = &$registry;
            self::$filters = $filters;
            if (!empty($filters['model_lang'])) {
                self::$lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                self::$lang = self::$registry['lang'];
            }

            self::$additional_data = array(
                'period'             => General::strftime('%B %Y', strtotime(self::$filters['month_year'] . '-01')),
                'report_date'        => date('d.m.Y'),
                'days_list'          => [],
                'legend'             => [
                    [
                        'code' => ABSENCE_TYPE_DAYS_OFF_PAID,
                        'name' => $registry['translater']->translate('reports_legend_days_off_paid'),
                    ],
                    [
                        'code' => ABSENCE_TYPE_DAYS_OFF_UNPAID,
                        'name' => $registry['translater']->translate('reports_legend_days_off_unpaid'),
                    ],
                    [
                        'code' => ABSENCE_TYPE_SICKNESS,
                        'name' => $registry['translater']->translate('reports_legend_sickness'),
                    ],
                    [
                        'code' => ABSENCE_TYPE_WORKTRIP,
                        'name' => $registry['translater']->translate('reports_legend_worktrip'),
                    ],
                ],
                'days_in_month'         => 0,
                'working_days_in_month' => 0,
                'totals'                => [
                    'total'                            => 0,
                    'total_days_off_paid'              => 0,
                    'total_days_off_unpaid'            => 0,
                    'total_days_sick'                  => 0,
                    'total_worked_minutes'             => 0,
                    'total_worked_hours_formatted'     => 0,
                    'total_worked_days'                => 0,
                    'total_paid_days_off_per_contract' => 0,
                    'total_days_off_paid_payment'      => 0,
                    'total_bonus_worked'               => 0,
                    'total_total_positive'             => 0
                ]
            );

            // prepare the days list
            $first_day_of_month = new DateTime(self::$filters['month_year'] . '-01');
            $last_day_of_month = clone $first_day_of_month;
            $last_day_of_month->add(new DateInterval('P1M'));
            $last_day_of_month->sub(new DateInterval('P1D'));

            $temp_date = clone $first_day_of_month;
            while ($temp_date<=$last_day_of_month) {
                self::$additional_data['days_list'][$temp_date->format('Y-m-d')] = array(
                    'label'               => $temp_date->format('j'),
                    'date'                => $temp_date->format('Y-m-d'),
                    'working'             => intval(Calendars_Calendar::isWorkingDay(self::$registry, $temp_date->format('Y-m-d'))),
                    'work_time'           => 0,
                    'overtime'            => 0,
                    'presence'            => '',
                    'color'               => '',
                    'reason'              => ''
                );
                self::$additional_data['days_in_month']++;
                if (self::$additional_data['days_list'][$temp_date->format('Y-m-d')]['working']) {
                    self::$additional_data['working_days_in_month']++;
                }
                $temp_date->add(new DateInterval('P1D'));
            }
            self::$additional_data['total_colspan'] = count(self::$additional_data['days_list']) + 40;
            self::$additional_data['title_colspan'] = self::$additional_data['total_colspan'] - 3;
            unset($temp_date);

             self::$additional_data['working_days_in_month'] = Calendars_Calendar::getWorkingDays(self::$registry, $first_day_of_month->format('Y-m-d'), $last_day_of_month->format('Y-m-d'));

            $employees_list = self::getEmployeesMainData($first_day_of_month, $last_day_of_month);

            $employeeContracts = [];
            foreach ($employees_list as $emp_key => $emp_data) {
                $emp_data = explode('_', $emp_key);
                if (!isset($employeeContracts[$emp_data[0]])) {
                    $employeeContracts[$emp_data[0]] = [];
                }
                $employeeContracts[$emp_data[0]][] = $emp_data[1];
                sort($employeeContracts[$emp_data[0]], SORT_NUMERIC);
            }

            $salary_filters = [
                'employees' => array_keys($employeeContracts),
                'year'      => $first_day_of_month->format('Y'),
                'month'     => $first_day_of_month->format('n'),
            ];
            $salary_documents = self::getSalaryDocuments($salary_filters);

            foreach ($employees_list as $emp_key => $employee_data) {
                $employees_list[$emp_key]['calc_start'] = ($employee_data['contract_start'] >= $first_day_of_month->format('Y-m-d') ? $employee_data['contract_start'] : $first_day_of_month->format('Y-m-d'));
                $employees_list[$emp_key]['calc_end'] = (($employee_data['contract_end'] && $employee_data['contract_end'] !='0000-00-00' && $employee_data['contract_end'] <= $last_day_of_month->format('Y-m-d')) ? $employee_data['contract_end'] : $last_day_of_month->format('Y-m-d'));

                $employees_list[$emp_key]['salary_document'] = ($salary_documents[$emp_key]['document_id'] ?? 0);
                $employees_list[$emp_key]['working_contract'] = (CONTRACT_WORKING_CONTRACT_TYPE == $employee_data['contract_type']);
                $employees_list[$emp_key]['working_contract_ended'] = ($employee_data['contract_end'] && $employee_data['contract_end'] !='0000-00-00' && $employee_data['contract_end'] <= $last_day_of_month->format('Y-m-d') && $first_day_of_month->format('Y-m-d') <= $employee_data['contract_end']);
                $employees_list[$emp_key]['days'] = self::$additional_data['days_list'];
                $employees_list[$emp_key]['days_off_paid'] = 0;
                $employees_list[$emp_key]['days_off_paid_payment'] = 0;
                $employees_list[$emp_key]['days_off_unpaid'] = 0;
                $employees_list[$emp_key]['days_off_unpaid_when_paid_available'] = 0;
                $employees_list[$emp_key]['daily_payment'] = (intval($employee_data['salary_day']) ?: intval($employee_data['salary_month']) /  self::$additional_data['working_days_in_month']);
                $employees_list[$emp_key]['sick'] = 0;
                $employees_list[$emp_key]['sick_payment'] = ($salary_documents[$emp_key]['sick_payment'] ?? 0);
                $employees_list[$emp_key]['paid_days_off_per_contract'] = floatval($salary_documents[$emp_key]['paid_days_off_per_contract'] ?? 0);
                $employees_list[$emp_key]['paid_days_off_total'] = 0;
                $employees_list[$emp_key]['worked_minutes'] = 0;
                $employees_list[$emp_key]['month_salary'] = intval($employee_data['salary_month']);
                $employees_list[$emp_key]['worked_hours'] = 0;
                $employees_list[$emp_key]['worked_hours_formatted'] = 0;
                $employees_list[$emp_key]['worked_days'] = 0;
                $employees_list[$emp_key]['worked_days_official'] = 0;
                $employees_list[$emp_key]['worked_days_saturday'] = 0;
                $employees_list[$emp_key]['worked_days_payment'] = 0;
                $employees_list[$emp_key]['worked_sundays'] = 0;
                $employees_list[$emp_key]['worked_non_sundays'] = 0;
                $employees_list[$emp_key]['overtime_minutes'] = 0;
                $employees_list[$emp_key]['overtime'] = 0;
                $employees_list[$emp_key]['overtime_payment'] = 0;
                $employees_list[$emp_key]['work_trip_days'] = 0;
                $employees_list[$emp_key]['work_trip_day'] = 0;
                $employees_list[$emp_key]['work_trip_overnight'] = 0;
                $employees_list[$emp_key]['work_trip_sum'] = 0;
                $employees_list[$emp_key]['deliveries_wo_failures'] = 0;
                $employees_list[$emp_key]['deliveries_wo_failures_sum'] = 0;
                $employees_list[$emp_key]['bonus_per_day'] = $employee_data['bonus_per_day'] ?: 0;
                $employees_list[$emp_key]['bonus'] = 0;
                $employees_list[$emp_key]['bonus_full'] = floatval($employee_data['bonus_full_month']) ?: 0;
                $employees_list[$emp_key]['bonus_full_saturday'] = floatval($employee_data['bonus_full_saturday']) ?: 0;
                $employees_list[$emp_key]['bonus_description'] = [];
                $employees_list[$emp_key]['bonus_worked'] = 0;
                $employees_list[$emp_key]['compensation'] = 0;
                $employees_list[$emp_key]['other_charges'] = (!empty($salary_documents[$emp_key]['other_charges']) ? floatval($salary_documents[$emp_key]['other_charges']) : 0);
                $employees_list[$emp_key]['total_positive'] = 0;
                $employees_list[$emp_key]['deliveries_w_failures'] = 0;
                $employees_list[$emp_key]['deliveries_w_failures_sum'] = 0;
                $employees_list[$emp_key]['penalties'] = 0;
                $employees_list[$emp_key]['penalties_checking'] = 0;
                $employees_list[$emp_key]['penalties_no'] = 0;
                $employees_list[$emp_key]['penalties_description'] = [];
                $employees_list[$emp_key]['loans'] = 0;
                $employees_list[$emp_key]['loans_left'] = 0;
                $employees_list[$emp_key]['penalties_cat'] = 0;
                $employees_list[$emp_key]['advance'] = 0;
                $employees_list[$emp_key]['vouchers'] = (!empty($salary_documents[$emp_key]['vouchers']) ? floatval($salary_documents[$emp_key]['vouchers']) : 0);
                $employees_list[$emp_key]['total_negative'] = 0;
                $employees_list[$emp_key]['bank_payment'] = (!empty($salary_documents[$emp_key]['bank_payment']) ? floatval($salary_documents[$emp_key]['bank_payment']) : 0);
                $employees_list[$emp_key]['total'] = 0;
                unset($employees_list[$emp_key]['salary_month']);
                unset($employees_list[$emp_key]['salary_day']);
            }

            // WORKING TRIPS
            $worktrips = self::getWorktrips(array_keys($employeeContracts), $first_day_of_month, $last_day_of_month);
            foreach ($worktrips as $wt) {
                $days_wt_start = new DateTime($wt['start_date']);
                $days_wt_end = new DateTime($wt['end_date']);

                while ($days_wt_start <= $days_wt_end) {
                    if ($days_wt_start>=$first_day_of_month && $days_wt_start<=$last_day_of_month) {
                        // define contract
                        $contractKey = self::getContractKey($employees_list, $employeeContracts, $wt['employee'], $days_wt_start->format('Y-m-d'));
                        if (!$contractKey) {
                            $days_wt_start->add(new DateInterval('P1D'));
                            continue;
                        }

                        $worktrip_type = 'work_trip_day';
                        if ($wt['type'] == 'overnight') {
                            $worktrip_type = 'work_trip_overnight';
                        }
                        $employees_list[$contractKey][$worktrip_type]++;
                        $employees_list[$contractKey]['work_trip_days']++;
                        $employees_list[$contractKey]['days'][$days_wt_start->format('Y-m-d')]['presence'] = ABSENCE_TYPE_WORKTRIP;
                        $employees_list[$contractKey]['worked_days']++;
                        $employees_list[$contractKey]['worked_minutes'] += 480;
                        $employees_list[$contractKey]['days'][$days_wt_start->format('Y-m-d')]['work_time'] = 480;
                        if ($employees_list[$contractKey]['days'][$days_wt_start->format('Y-m-d')]['working']) {
                            $employees_list[$contractKey]['worked_days_official']++;
                        }
                        if ($days_wt_start->format('w') == 6) {
                            $employees_list[$contractKey]['worked_days_saturday']++;
                        }
                    }
                    $days_wt_start->add(new DateInterval('P1D'));
                }
            }


            // DAYS OFF
            $days_off_documents = self::getDaysOff(array_keys($employeeContracts), $first_day_of_month,
                $last_day_of_month);
            $paid_options = array_filter(preg_split('#\s*,\s*#', DOC_DAYS_OFF_TYPE_PAID));
            $unpaid_options = array_filter(preg_split('#\s*,\s*#', DOC_DAYS_OFF_TYPE_UNPAID));
            foreach ($days_off_documents as $days_off_doc) {
                $days_off_start = new DateTime($days_off_doc['start_date']);
                $days_off_end = new DateTime($days_off_doc['end_date']);

                while ($days_off_start <= $days_off_end) {
                    $contractKey = self::getContractKey($employees_list, $employeeContracts, $days_off_doc['employee'], $days_off_start->format('Y-m-d'));

                    if (!$contractKey || empty($employees_list[$contractKey]['days'][$days_off_start->format('Y-m-d')]) ||
                        !$employees_list[$contractKey]['days'][$days_off_start->format('Y-m-d')]['working']) {
                        $days_off_start->add(new DateInterval('P1D'));
                        continue;
                    }

                    if (in_array($days_off_doc['kind'], $paid_options)) {
                        $presence_label = ABSENCE_TYPE_DAYS_OFF_PAID;
                        $days_off_index = 'days_off_paid';
                    } elseif (in_array($days_off_doc['kind'], $unpaid_options)) {
                        $presence_label = ABSENCE_TYPE_DAYS_OFF_UNPAID;
                        $days_off_index = 'days_off_unpaid';
                        if (intval($days_off_doc['available_paid_days_off']) > 0) {
                            $employees_list[$contractKey]['days_off_unpaid_when_paid_available']++;
                        }
                    }
                    $employees_list[$contractKey]['days'][$days_off_start->format('Y-m-d')]['presence'] = $presence_label;
                    $employees_list[$contractKey]['days'][$days_off_start->format('Y-m-d')]['reason'] = $days_off_index;
                    $employees_list[$contractKey][$days_off_index]++;

                    $days_off_start->add(new DateInterval('P1D'));
                }
            }

            // SICKNESS
            $sickness_documents = self::getSicknessDocuments(array_keys($employeeContracts), $first_day_of_month,
                $last_day_of_month);
            foreach ($sickness_documents as $sickness_doc) {
                $days_sick_start = new DateTime($sickness_doc['start_date']);
                $days_sick_end = new DateTime($sickness_doc['end_date']);
                $presence_label = ABSENCE_TYPE_SICKNESS;

                while ($days_sick_start <= $days_sick_end) {
                    $contractKey = self::getContractKey($employees_list, $employeeContracts, $sickness_doc['employee'], $days_sick_start->format('Y-m-d'));
                    if (!$contractKey || !isset($employees_list[$contractKey]['days'][$days_sick_start->format('Y-m-d')])) {
                        $days_sick_start->add(new DateInterval('P1D'));
                        continue;
                    }
                    $employees_list[$contractKey]['days'][$days_sick_start->format('Y-m-d')]['presence'] = $presence_label;
                    $employees_list[$contractKey]['sick']++;
                    if ($employees_list[$contractKey]['days'][$days_sick_start->format('Y-m-d')]['reason']) {
                        $employees_list[$contractKey][$employees_list[$contractKey]['days'][$days_sick_start->format('Y-m-d')]['reason']]--;
                    }
                    $employees_list[$contractKey]['days'][$days_sick_start->format('Y-m-d')]['reason'] = 'sick';

                    $days_sick_start->add(new DateInterval('P1D'));
                }
            }

            // WORKING HOURS
            $working_hours_documents = self::getWorkingHours(array_keys($employeeContracts), $first_day_of_month,
                $last_day_of_month);
            foreach ($working_hours_documents as $whd) {
                $current_date = new DateTime($whd['date']);
                $contractKey = self::getContractKey($employees_list, $employeeContracts, $whd['employee'], $current_date->format('Y-m-d'));
                if (!$contractKey) {
                    continue;
                }
                if (!empty($employees_list[$contractKey]['days'][$current_date->format('Y-m-d')]['presence']) && $employees_list[$contractKey]['days'][$current_date->format('Y-m-d')]['presence'] != ABSENCE_TYPE_SICKNESS) {
                    continue;
                }
                if ($whd['working_minutes'] > 0 || $whd['overtime_minutes']>0) {
                    $employees_list[$contractKey]['worked_days']++;
                    if ($employees_list[$contractKey]['days'][$current_date->format('Y-m-d')]['working']) {
                        $employees_list[$contractKey]['worked_days_official']++;
                    }
                    if ($current_date->format('w') == 6) {
                        $employees_list[$contractKey]['worked_days_saturday']++;
                    }
                }
                $employees_list[$contractKey]['worked_minutes'] += $whd['working_minutes'];
                $employees_list[$contractKey]['overtime_minutes'] += $whd['overtime_minutes'];

                $employees_list[$contractKey]['days'][$current_date->format('Y-m-d')]['work_time'] += $whd['working_minutes'];
                $employees_list[$contractKey]['days'][$current_date->format('Y-m-d')]['overtime'] += $whd['overtime_minutes'];

                $key_update = 'worked_non_sundays';
                if ($current_date->format('N') == 7) {
                    $key_update = 'worked_sundays';
                }
                $employees_list[$contractKey][$key_update]++;
            }

            // PROCESS USERS WITH STANDARD WORKING TIME
            foreach ($employees_list as $emp_id => $employee) {
                if (empty($employee['standard_working_time'])) {
                    continue;
                }
                foreach ($employee['days'] as $day_idx => $day_data) {
                    $contractKey = self::getContractKey($employees_list, $employeeContracts, $employee['id'], $day_idx);
                    if (!$contractKey) {
                        continue;
                    }

                    if ($day_data['working'] && empty($employees_list[$contractKey]['days'][$day_idx]['presence'])) {
                        $employees_list[$contractKey]['worked_days']++;
                        $employees_list[$contractKey]['worked_minutes'] += 480;
                        $employees_list[$contractKey]['days'][$day_idx]['work_time'] = 480;
                        if ($day_data['working']) {
                            $employees_list[$contractKey]['worked_days_official']++;
                        }
                    }
                }
            }

            // PROCESS THE TRANSPORT SHEETS
            $salary_extra_sums = self::getSalaryExtraSums();
            $transport_sheets = self::getTransportSheets(array_keys($employeeContracts), $first_day_of_month,
                $last_day_of_month);

            $employee_transport_sheets = array();
            foreach ($transport_sheets as $ts) {
                $contractKey = self::getContractKey($employees_list, $employeeContracts, $ts['employee'], $ts['transport_sheet_date']);
                if (!$contractKey) {
                    continue;
                }

                if (!isset($employee_transport_sheets[$contractKey])) {
                    $employee_transport_sheets[$contractKey] = [
                        'wo_misses' => [],
                        'w_misses' => []
                    ];
                }
                if (!empty($ts['wo_failures']) && !in_array($ts['transport_sheet_date'],
                        $employee_transport_sheets[$contractKey]['wo_misses'])) {
                    $employee_transport_sheets[$contractKey]['wo_misses'][] = $ts['transport_sheet_date'];
                }
                if (!empty($ts['w_failures']) && !in_array($ts['transport_sheet_date'],
                        $employee_transport_sheets[$contractKey]['w_misses'])) {
                    $employee_transport_sheets[$contractKey]['w_misses'][] = $ts['transport_sheet_date'];
                }
            }

            foreach ($employee_transport_sheets as $emp_id => $transport_sheets) {
                $employees_list[$emp_id]['deliveries_wo_failures'] = count($transport_sheets['wo_misses']);
                $employees_list[$emp_id]['deliveries_wo_failures_sum'] = $employees_list[$emp_id]['deliveries_wo_failures'] * floatval($salary_extra_sums['deliveries_wo_failures']);
                $employees_list[$emp_id]['deliveries_w_failures'] = count($transport_sheets['w_misses']);
                $employees_list[$emp_id]['deliveries_w_failures_sum'] = $employees_list[$emp_id]['deliveries_w_failures'] * floatval($salary_extra_sums['deliveries_w_failures']);
            }

            // GET BONUSES
            $bonuses = self::getBonuses(array_keys($employeeContracts), $first_day_of_month, $last_day_of_month);
            foreach ($bonuses as $bon) {
                $contractKey = self::getContractKey($employees_list, $employeeContracts, $bon['customer'], $bon['date']);
                if (!$contractKey) {
                    continue;
                }
                $employees_list[$contractKey]['bonus_worked'] += floatval($bon['bonus']);
                $employees_list[$contractKey]['bonus_description'][] = sprintf(
                    '%s/%.2f лв., %s',
                    strftime('%d.%m.%Y', strtotime($bon['date'])),
                    floatval($bon['bonus']),
                    $bon['description']
                );
            }

            // GET COMPENSATIONS
            $compensations = self::getCompensations(array_keys($employeeContracts), $first_day_of_month, $last_day_of_month);
            foreach ($compensations as $comp) {
                $contractKey = self::getContractKey($employees_list, $employeeContracts, $comp['customer'], $comp['date']);
                if (!$contractKey) {
                    continue;
                }
                $employees_list[$contractKey]['compensation'] += floatval($comp['compensation']);
            }


            // GET PENALTIES
            $penalties = self::getPenalties(array_keys($employeeContracts), $first_day_of_month, $last_day_of_month);
            foreach ($penalties as $pen) {
                $contractKey = self::getContractKey($employees_list, $employeeContracts, $pen['customer'], $pen['date']);
                if (!$contractKey) {
                    continue;
                }
                if ($pen['reason'] == DOC_PENALTIES_REASON_OPTION_OTHER) {
                    $employees_list[$contractKey]['penalties'] += floatval($pen['sum']);
                } elseif ($pen['reason'] == DOC_PENALTIES_REASON_OPTION_NO_CHECKING) {
                    $employees_list[$contractKey]['penalties_checking'] += floatval($pen['sum']);
                }
                $employees_list[$contractKey]['penalties_description'][] = sprintf(
                    '%s/%.2f лв., %s',
                    strftime('%d.%m.%Y', strtotime($pen['date'])),
                    $pen['sum'],
                    $pen['penalty_description'],
                );
            }


            // GET LOANS
            $loans = self::getLoans(array_keys($employeeContracts), $first_day_of_month);
            foreach ($loans as $loan) {
                $contractKey = self::getContractKey($employees_list, $employeeContracts, $loan['customer']);
                if (!$contractKey) {
                    continue;
                }
                $employees_list[$contractKey]['loans'] += floatval($loan['received_sum']);
                $employees_list[$contractKey]['loans_left'] += floatval($loan['loans_left']);
            }


            // GET EXTRA EXPENSES
            $extra_expenses = self::getExtraExpenses(array_keys($employeeContracts), $first_day_of_month, $last_day_of_month);
            foreach ($extra_expenses as $expense) {
                $key_expense = '';
                if ($expense['type'] == NOM_ADVANCE) {
                    $key_expense = 'advance';
                } elseif ($expense['type'] == NOM_PENALTY_CAT) {
                    $key_expense = 'penalties_cat';
                }
                if (!$key_expense) {
                    continue;
                }
                $contractKey = self::getContractKey($employees_list, $employeeContracts, $expense['customer'], $expense['issue_date']);
                if (!$contractKey) {
                    continue;
                }
                $employees_list[$contractKey][$key_expense] += floatval($expense['sum']);
            }

            // FINAL DATA PROCESSING
            foreach ($employees_list as $emp_id => $emp) {
                $employees_list[$emp_id]['worked_hours_formatted'] = sprintf('%02d:%02d', floor($emp['worked_minutes'] / 60),
                    floor($emp['worked_minutes'] % 60));
                $employees_list[$emp_id]['worked_hours'] = $emp['worked_minutes'] / 60;
                $employees_list[$emp_id]['overtime_formatted'] = sprintf('%02d:%02d', floor($emp['overtime_minutes'] / 60),
                    floor($emp['overtime_minutes'] % 60));
                $employees_list[$emp_id]['overtime'] = $emp['overtime_minutes'] / 60;
                $employees_list[$emp_id]['overtime_payment'] = ($emp['daily_payment'] / 8) * (($emp['overtime_minutes'] / 60) * (floatval($employees_list[$emp_id]['overtime_coefficient']) ?? 1));
                $employees_list[$emp_id]['penalties_no'] = $emp['daily_payment'] * $emp['days_off_unpaid_when_paid_available'];

                $employees_list[$emp_id]['days_off_paid_payment'] = $employees_list[$emp_id]['days_off_paid'] * $emp['daily_payment'];
                if ($employees_list[$emp_id]['paid_leave'] == EMPLOYEE_VAR_PAID_LEAVE_OPTION_FULL) {
                    $employees_list[$emp_id]['days_off_paid_payment'] -= $employees_list[$emp_id]['paid_days_off_per_contract'];
                } else {
                    $employees_list[$emp_id]['days_off_paid_payment'] = 0;
                }
                $employees_list[$emp_id]['bonus'] = round(floatval($employees_list[$emp_id]['bonus_per_month']));
                $employees_list[$emp_id]['bonus_transport'] = $employees_list[$emp_id]['worked_days'] * floatval($employees_list[$emp_id]['bonus_transport']);
                if ($employees_list[$emp_id]['bonus_per_day']) {
                    $bonusesPerDayBasis = 0;
                    $hourBonus = $employees_list[$emp_id]['bonus_per_day'] / 8;
                    foreach ($emp['days'] as $day_data) {
                        $bonusesPerDayBasis += (($day_data['work_time'] / 60) * $hourBonus);
                    }
                    $employees_list[$emp_id]['bonus'] = round($bonusesPerDayBasis);
                }

                $bonus_full_month = $employees_list[$emp_id]['worked_days_official'];
                if ($employees_list[$emp_id]['days_off_paid'] <= 1) {
                    $bonus_full_month += $employees_list[$emp_id]['days_off_paid'];
                }
                if ($employees_list[$emp_id]['bonus_full'] && self::$additional_data['working_days_in_month'] == $bonus_full_month) {
                } else {
                    $employees_list[$emp_id]['bonus_full'] = 0;
                }

                if ($employees_list[$emp_id]['bonus_full_saturday'] &&
                    self::$additional_data['working_days_in_month'] == $bonus_full_month &&
                    $employees_list[$emp_id]['worked_days_saturday'] >= 4) {
                } else {
                    $employees_list[$emp_id]['bonus_full_saturday'] = 0;
                }
                $employees_list[$emp_id]['work_trip_sum'] = ($employees_list[$emp_id]['work_trip_day'] * floatval($salary_extra_sums['worktrip_day'])) +
                                                            ($employees_list[$emp_id]['work_trip_overnight'] * floatval($salary_extra_sums['worktrip_overnight']));
                $employees_list[$emp_id]['penalties_description'] = implode("\n", $emp['penalties_description']);
                $employees_list[$emp_id]['bonus_description'] = implode("\n", $emp['bonus_description']);

                foreach ($emp['days'] as $day_idx => $day_data) {
                    $employees_list[$emp_id]['worked_days_payment'] += (!empty($day_data['work_time']) ? $emp['daily_payment'] * ($day_data['work_time'] / 480) : 0);
                    switch ($day_data['presence']) {
                        case ABSENCE_TYPE_SICKNESS:
                            $employees_list[$emp_id]['days'][$day_idx]['color'] = 'salary-col-sick';
                            break;
                        case ABSENCE_TYPE_DAYS_OFF_PAID:
                            $employees_list[$emp_id]['days'][$day_idx]['color'] = 'salary-col-blue';
                            break;
                        case ABSENCE_TYPE_DAYS_OFF_UNPAID:
                            $employees_list[$emp_id]['days'][$day_idx]['color'] = 'salary-col-red';
                            break;
                        case ABSENCE_TYPE_WORKTRIP:
                            $employees_list[$emp_id]['days'][$day_idx]['color'] = 'salary-col-green';
                            break;
                    }

                    if (($day_data['presence'] && $day_data['presence'] != ABSENCE_TYPE_SICKNESS) || (!$day_data['work_time'] && !$day_data['overtime'])) {
                        continue;
                    }
                    $tmp_presence = sprintf('%d:%02d', floor($day_data['work_time'] / 60), floor($day_data['work_time'] % 60));
                    if ($day_data['overtime']) {
                        $tmp_presence .= sprintf(' (%d:%02d)', floor($day_data['overtime'] / 60), floor($day_data['overtime'] % 60));
                    }
                    if ($day_data['presence'] == ABSENCE_TYPE_SICKNESS) {
                        $tmp_presence = sprintf('%s<span class="working_time">/%s</span>', $day_data['presence'], trim($tmp_presence));
                    }
                    $employees_list[$emp_id]['days'][$day_idx]['presence'] = trim($tmp_presence);
                }

                $employees_list[$emp_key]['paid_days_off_total'] = floatval($employees_list[$emp_id]['paid_days_off_per_contract']) + floatval($employees_list[$emp_key]['days_off_paid_payment']);

                $employees_list[$emp_id]['total_positive'] =
                    floatval($employees_list[$emp_id]['sick_payment']) +
                    floatval($employees_list[$emp_id]['days_off_paid_payment']) +
                    floatval($employees_list[$emp_id]['work_trip_sum']) +
                    floatval($employees_list[$emp_id]['deliveries_wo_failures_sum']) +
                    floatval($employees_list[$emp_id]['bonus']) +
                    floatval($employees_list[$emp_id]['bonus_full']) +
                    floatval($employees_list[$emp_id]['bonus_full_saturday']) +
                    floatval($employees_list[$emp_id]['bonus_worked']) +
                    floatval($employees_list[$emp_id]['compensation']) +
                    floatval($employees_list[$emp_id]['bonus_transport']) +
                    floatval($employees_list[$emp_id]['worked_days_payment']) +
                    floatval($employees_list[$emp_id]['paid_days_off_per_contract']) +
                    floatval($employees_list[$emp_id]['overtime_payment']);

                $employees_list[$emp_id]['total_negative'] =
                    floatval($employees_list[$emp_id]['deliveries_w_failures_sum']) +
                    floatval($employees_list[$emp_id]['penalties']) +
                    floatval($employees_list[$emp_id]['penalties_checking']) +
                    floatval($employees_list[$emp_id]['penalties_no']) +
                    floatval($employees_list[$emp_id]['loans']) +
                    floatval($employees_list[$emp_id]['penalties_cat']) +
                    floatval($employees_list[$emp_id]['vouchers']) +
                    floatval($employees_list[$emp_id]['bank_payment']) +
                    floatval($employees_list[$emp_id]['advance']);

                $employees_list[$emp_id]['total'] =
                    round(floatval($employees_list[$emp_id]['total_positive']) -
                          floatval($employees_list[$emp_id]['total_negative']));

                self::$additional_data['totals']['total'] += $employees_list[$emp_id]['total'];
                self::$additional_data['totals']['total_days_off_paid'] += $employees_list[$emp_id]['days_off_paid'];
                self::$additional_data['totals']['total_days_off_unpaid'] += $employees_list[$emp_id]['days_off_unpaid'];
                self::$additional_data['totals']['total_days_sick'] += $employees_list[$emp_id]['sick'];
                self::$additional_data['totals']['total_worked_minutes'] += $employees_list[$emp_id]['worked_minutes'];
                self::$additional_data['totals']['total_worked_days'] += $employees_list[$emp_id]['worked_days'];
                self::$additional_data['totals']['total_paid_days_off_per_contract'] += $employees_list[$emp_id]['paid_days_off_per_contract'];
                self::$additional_data['totals']['total_days_off_paid_payment'] += $employees_list[$emp_id]['days_off_paid_payment'];
                self::$additional_data['totals']['total_bonus_worked'] += $employees_list[$emp_id]['bonus_worked'];
                self::$additional_data['totals']['total_total_positive'] += $employees_list[$emp_id]['total_positive'];
            }

            self::$additional_data['totals']['total_worked_hours_formatted'] = sprintf(
                '%02d:%02d',
                floor(self::$additional_data['totals']['total_worked_minutes'] / 60),
                floor(self::$additional_data['totals']['total_worked_minutes'] % 60)
            );

            return $employees_list;
        }

        /**
         * @return void
         */
        public static function defineVisibleColumns($registry): void
        {
            $default_columns_list = [
                'name', 'department', 'position', 'days_of_month', 'days_off_paid', 'days_off_unpaid', 'sick_leave',
                'worked_days', 'month_salary', 'worked_hours', 'daily_payment', 'overtime',
                'overtime_payment', 'worked_days_payment', 'sick_payment', 'paid_days_off_per_contract',
                'days_off_paid_payment', 'work_trip_days', 'work_trip_sum', 'deliveries_wo_failures',
                'deliveries_wo_failures_sum', 'bonus', 'bonus_full', 'bonus_full_saturday', 'bonus_worked',
                'bonus_transport', 'compensation', 'other_charges', 'total_positive', 'deliveries_w_failures',
                'deliveries_w_failures_sum', 'penalties', 'penalties_checking', 'penalties_no', 'loans',
                'penalties_cat', 'advance', 'vouchers', 'total_negative', 'bank_payment', 'total'
            ];
            $restricted_roles = array_filter(preg_split('#\s*,\s*#', RESTRICTED_ROLES));
            $visible_columns = $default_columns_list;
            if (in_array($registry['currentUser']->get('role'), $restricted_roles)) {
                $visible_columns = array_filter(preg_split('#\s*,\s*#', RESTRICTED_ROLES_ALLOWED_COLUMNS));
            }
            self::$additional_data['visible_columns'] = $visible_columns;
        }

        private static function sortEmployees($a, $b) {
            if ($a['working_contract_ended'] == $b['working_contract_ended']) {
                if (boolval($a['working_contract']) == boolval($b['working_contract'])) {
                    return $a['name'] > $b['name'] ? 1 : -1;
                } elseif (!$a['working_contract']) {
                    return 1;
                } else {
                    return -1;
                }
            } elseif (!$a['working_contract_ended']) {
                return -1;
            } else {
                return 1;
            }
        }

        /**
         * @param $employeesList
         * @param $employeeContracts
         * @param $employeeId
         * @param $checkDate
         * @return string
         */
        private static function getContractKey(
            $employeesList,
            $employeeContracts,
            $employeeId,
            $checkDate = ''
        ): string {
            $use_key = '';
            if (empty($checkDate)) {
                $use_key = $employeeId . '_' . reset($employeeContracts[$employeeId]);
                return $use_key;
            }
            foreach ($employeeContracts[$employeeId] as $contr_id) {
                $tmp_k = sprintf('%d_%d', $employeeId, $contr_id);
                if ($employeesList[$tmp_k]['contract_start'] <= $checkDate &&
                    (!$employeesList[$tmp_k]['contract_end'] || $employeesList[$tmp_k]['contract_end'] == '0000-00-00' || $employeesList[$tmp_k]['contract_end'] >= $checkDate)) {
                    $use_key = $tmp_k;
                    break;
                }
            }
            return $use_key;
        }
    }
?>
