{if !empty($reports_results)}
  <table class="reports_table freeze_table_headers salary-table">
    <thead>
      <tr class="reports_title_row">
        {if in_array('name', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table freeze_column" rowspan="2"><div style="">{#reports_three_names#|escape}</div></th>
        {/if}
        {if in_array('department', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table" rowspan="2"><div style="">{#reports_department#|escape}</div></th>
        {/if}
        {if in_array('position', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table" rowspan="2"><div style="">{#reports_position#|escape}</div></th>
        {/if}
        {if in_array('days_of_month', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table" colspan="{$reports_additional_options.days_in_month|escape|default:0}"><div style="">{#reports_month_days#|escape}</div></th>
        {/if}
        {if in_array('days_off_paid', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_days_off_paid#|escape}</div></div></th>
        {/if}
        {if in_array('days_off_unpaid', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_days_off_unpaid#|escape}</div></div></th>
        {/if}
        {if in_array('sick_leave', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_sick_leave#|escape}</div></div></th>
        {/if}
        {if in_array('worked_hours', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_worked_hours#|escape}</div></div></th>
        {/if}
        {if in_array('worked_days', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_worked_days#|escape}</div></div></th>
        {/if}
        {if in_array('month_salary', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_month_salary#|escape}</div></div></th>
        {/if}
        {if in_array('daily_payment', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_daily_payment#|escape}</div></div></th>
        {/if}
        {if in_array('overtime', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_overtime#|escape}</div></div></th>
        {/if}
        {if in_array('overtime_payment', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_overtime_payment#|escape}</div></div></th>
        {/if}
        {if in_array('worked_days_payment', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_overtime_days#|escape}</div></div></th>
        {/if}
        {if in_array('sick_payment', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_sickness_payment#|escape}</div></div></th>
        {/if}
        {if in_array('paid_days_off_per_contract', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_paid_days_off_per_contract#|escape}</div></div></th>
        {/if}
        {if in_array('days_off_paid_payment', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_paid_days_off#|escape}</div></div></th>
        {/if}
        {if in_array('work_trip_days', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_working_vacation_days#|escape}</div></div></th>
        {/if}
        {if in_array('work_trip_sum', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_working_vacation_sum#|escape}</div></div></th>
        {/if}
        {if in_array('deliveries_wo_failures', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_deliveries_wo_problems_count#|escape}</div></div></th>
        {/if}
        {if in_array('deliveries_wo_failures_sum', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_deliveries_wo_problems_sum#|escape}</div></div></th>
        {/if}
        {if in_array('bonus', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_bonus#|escape}</div></div></th>
        {/if}
        {if in_array('bonus_full', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_bonus_full_month#|escape}</div></div></th>
        {/if}
        {if in_array('bonus_full_saturday', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_bonus_full_month_with_saturdays#|escape}</div></div></th>
        {/if}
        {if in_array('bonus_worked', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_bonus_working#|escape}</div></div></th>
        {/if}
        {if in_array('bonus_transport', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_bonus_transport#|escape}</div></div></th>
        {/if}
        {if in_array('compensation', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_compensation#|escape}</div></div></th>
        {/if}
        {if in_array('other_charges', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_other_charges#|escape}</div></div></th>
        {/if}
        {if in_array('total_positive', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_total_positive#|escape}</div></div></th>
        {/if}
        {if in_array('deliveries_w_failures', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_deliveries_w_problems_count#|escape}</div></div></th>
        {/if}
        {if in_array('deliveries_w_failures_sum', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_deliveries_w_problems_sum#|escape}</div></div></th>
        {/if}
        {if in_array('penalties', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_penalties#|escape}</div></div></th>
        {/if}
        {if in_array('penalties_checking', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_penalties_checking#|escape}</div></div></th>
        {/if}
        {if in_array('penalties_no', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_penalties_no#|escape}</div></div></th>
        {/if}
        {if in_array('loans', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_loans#|escape}</div></div></th>
        {/if}
        {if in_array('penalties_cat', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_penalties_cat#|escape}</div></div></th>
        {/if}
        {if in_array('advance', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_advance#|escape}</div></div></th>
        {/if}
        {if in_array('vouchers', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_vouchers#|escape}</div></div></th>
        {/if}
        {if in_array('bank_payment', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_bank_payment#|escape}</div></div></th>
        {/if}
        {if in_array('total_negative', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_total_negative#|escape}</div></div></th>
        {/if}
        {if in_array('total', $reports_additional_options.visible_columns)}
          <th class="salary_titles_table salary_titles_table_sideways_text" rowspan="2"><div><div class="title_text_conttainer">{#reports_total#|escape}</div></div></th>
        {/if}
      </tr>
      {if in_array('days_of_month', $reports_additional_options.visible_columns)}
        <tr class="reports_title_row">
          {foreach from=$reports_additional_options.days_list item=dl}
            <th class="salary_titles_table salary_titles_table_days{if !$dl.working} presence_form_holiday{/if}"><div style="">{$dl.label|escape}</div></th>
          {/foreach}
        </tr>
      {/if}
    </thead>
    {counter start=0 name='item_counter' print=false}
    {foreach from=$reports_results item=employee}
      <tr class="{cycle name='table1' values='t_odd1 t_odd2,t_even1 t_even2'}{if !$employee.working_contract} salary-row-no-contract{/if}{if $employee.working_contract_ended} t_disabled salary-inactive-row{/if}">
        {if in_array('name', $reports_additional_options.visible_columns)}
          <td class="freeze_column vmiddle" style="white-space: nowrap;">
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$employee.id}" target="_blank">{$employee.name|escape|default:"&nbsp;"}</a>
            <input type="hidden" disabled="disabled" id="salary_document_{$employee.id}_{$employee.contract_type}" value="{if $employee.salary_document}{$employee.salary_document}{/if}">
          </td>
        {/if}
        {if in_array('department', $reports_additional_options.visible_columns)}
          <td class="vmiddle" style="white-space: nowrap;">{$employee.department|escape|default:"&nbsp;"}</td>
        {/if}
        {if in_array('position', $reports_additional_options.visible_columns)}
          <td class="vmiddle" style="white-space: nowrap;">{$employee.position|escape|default:"&nbsp;"}</td>
        {/if}
        {if in_array('days_of_month', $reports_additional_options.visible_columns)}
          {foreach from=$employee.days item=day}
            <td class="hcenter vmiddle{if !$day.working} presence_form_holiday{/if}{if $day.color} {$day.color}{/if}">{$day.presence}</td>
          {/foreach}
        {/if}
        {if in_array('days_off_paid', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle presence_form_holiday">{if $employee.days_off_paid}{$employee.days_off_paid|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('days_off_unpaid', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle presence_form_holiday">{if $employee.days_off_unpaid}{$employee.days_off_unpaid|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('sick_leave', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle presence_form_holiday">{if $employee.sick}{$employee.sick|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('worked_hours', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle presence_form_holiday">{if $employee.worked_hours}{$employee.worked_hours_formatted|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('worked_days', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle presence_form_holiday">{if $employee.worked_days}{$employee.worked_days|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('month_salary', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.month_salary}{$employee.month_salary|escape|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('daily_payment', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.daily_payment}{$employee.daily_payment|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('overtime', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.overtime_minutes}{$employee.overtime_formatted|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('overtime_payment', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.overtime_payment}{$employee.overtime_payment|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('worked_days_payment', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-marked"><strong>{if $employee.worked_days_payment}{$employee.worked_days_payment|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</strong></td>
        {/if}
        {if in_array('sick_payment', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-red">{if $employee.sick_payment}{$employee.sick_payment|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('paid_days_off_per_contract', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-blue">{if $employee.paid_days_off_per_contract}{$employee.paid_days_off_per_contract|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('days_off_paid_payment', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-blue">{if $employee.days_off_paid_payment}{$employee.days_off_paid_payment|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('work_trip_days', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-green">{if $employee.work_trip_days}{$employee.work_trip_days|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('work_trip_sum', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-green">{if $employee.work_trip_sum}{$employee.work_trip_sum|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('deliveries_wo_failures', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.deliveries_wo_failures}{$employee.deliveries_wo_failures|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('deliveries_wo_failures_sum', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.deliveries_wo_failures_sum}{$employee.deliveries_wo_failures_sum|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('bonus', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-red">{if $employee.bonus}{$employee.bonus|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('bonus_full', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.bonus_full}{$employee.bonus_full|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('bonus_full_saturday', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.bonus_full_saturday}{$employee.bonus_full_saturday|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('bonus_worked', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.bonus_worked}{$employee.bonus_worked|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('bonus_transport', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.bonus_transport}{$employee.bonus_transport|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('compensation', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.compensation}{$employee.compensation|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('other_charges', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.other_charges}{$employee.other_charges|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('total_positive', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-marked"><strong>{if $employee.total_positive}{$employee.total_positive|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</strong></td>
        {/if}
        {if in_array('deliveries_w_failures', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.deliveries_w_failures}{$employee.deliveries_w_failures|escape|default:0}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('deliveries_w_failures_sum', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.deliveries_w_failures_sum}{$employee.deliveries_w_failures_sum|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('penalties', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.penalties}{$employee.penalties|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('penalties_checking', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.penalties_checking}{$employee.penalties_checking|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('penalties_no', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle salary-col-red">{if $employee.penalties_no}{$employee.penalties_no|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('loans', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.loans}{$employee.loans|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('penalties_cat', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.penalties_cat}{$employee.penalties_cat|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('advance', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.advance}{$employee.advance|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('vouchers', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.vouchers}{$employee.vouchers|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('bank_payment', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.bank_payment}{$employee.bank_payment|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('total_negative', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle">{if $employee.total_negative}{$employee.total_negative|string_format:"%.2f"|default:"0.00"}{else}&nbsp;{/if}</td>
        {/if}
        {if in_array('total', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle" style="background-color: #FFDAB9;"><strong>{if $employee.total}{$employee.total|string_format:"%d"|default:"0"}{else}&nbsp;{/if}</strong></td>
        {/if}
      </tr>
    {foreachelse}
      <tr class="{cycle name='table1' values='t_odd1 t_odd2,t_even1 t_even2'}">
        <td class="error" colspan="{$reports_additional_options.total_colspan}">{#no_items_found#|escape}</td>
      </tr>
    {/foreach}
    {if in_array('total', $reports_additional_options.visible_columns)}
      <tr class="row_blue">
        {if in_array('name', $reports_additional_options.visible_columns)}
          <td class="freeze_column">&nbsp;</td>
        {/if}
        {if in_array('department', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('position', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('days_of_month', $reports_additional_options.visible_columns)}
          <td colspan="{$reports_additional_options.days_in_month|escape|default:0}">&nbsp;</td>
        {/if}
        {if in_array('days_off_paid', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle"><strong>{$reports_additional_options.totals.total_days_off_paid|string_format:"%d"|default:"0"}</strong></td>
        {/if}
        {if in_array('days_off_unpaid', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle"><strong>{$reports_additional_options.totals.total_days_off_unpaid|string_format:"%d"|default:"0"}</strong></td>
        {/if}
        {if in_array('sick_leave', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle"><strong>{$reports_additional_options.totals.total_days_sick|string_format:"%d"|default:"0"}</strong></td>
        {/if}
        {if in_array('worked_hours', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle"><strong>{$reports_additional_options.totals.total_worked_hours_formatted|default:"00:00"}</strong></td>
        {/if}
        {if in_array('worked_days', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle"><strong>{$reports_additional_options.totals.total_worked_days|string_format:"%d"|default:"0"}</strong></td>
        {/if}
        {if in_array('month_salary', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('daily_payment', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('overtime', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('overtime_payment', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('worked_days_payment', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('sick_payment', $reports_additional_options.visible_columns)}
          <td class="salary-col-red">&nbsp;</td>
        {/if}
        {if in_array('paid_days_off_per_contract', $reports_additional_options.visible_columns)}
          <td class="hright salary-col-blue"><strong>{$reports_additional_options.totals.total_paid_days_off_per_contract|string_format:"%.2f"|default:"0.00"}</strong></td>
        {/if}
        {if in_array('days_off_paid_payment', $reports_additional_options.visible_columns)}
          <td class="hright salary-col-blue"><strong>{$reports_additional_options.totals.total_days_off_paid_payment|string_format:"%.2f"|default:"0.00"}</strong></td>
        {/if}
        {if in_array('work_trip_days', $reports_additional_options.visible_columns)}
          <td class="salary-col-green">&nbsp;</td>
        {/if}
        {if in_array('work_trip_sum', $reports_additional_options.visible_columns)}
          <td class="salary-col-green">&nbsp;</td>
        {/if}
        {if in_array('deliveries_wo_failures', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('deliveries_wo_failures_sum', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('bonus', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('bonus_full', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('bonus_full_saturday', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('bonus_worked', $reports_additional_options.visible_columns)}
          <td class="hright"><strong>{$reports_additional_options.totals.total_bonus_worked|string_format:"%.2f"|default:"0.00"}</strong></td>
        {/if}
        {if in_array('bonus_transport', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('compensation', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('other_charges', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('total_positive', $reports_additional_options.visible_columns)}
          <td class="hright"><strong>{$reports_additional_options.totals.total_total_positive|string_format:"%.2f"|default:"0.00"}</strong></td>
        {/if}
        {if in_array('deliveries_w_failures', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('deliveries_w_failures_sum', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('penalties', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('penalties_checking', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('penalties_no', $reports_additional_options.visible_columns)}
          <td class="salary-col-red">&nbsp;</td>
        {/if}
        {if in_array('loans', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('penalties_cat', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('advance', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('vouchers', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('bank_payment', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('total_negative', $reports_additional_options.visible_columns)}
          <td>&nbsp;</td>
        {/if}
        {if in_array('total', $reports_additional_options.visible_columns)}
          <td class="hright vmiddle"><strong>{$reports_additional_options.totals.total|string_format:"%d"|default:"0"}</strong></td>
        {/if}
      </tr>
    {/if}
  </table>
  <h3 style="text-decoration: underline; margin-bottom: 5px;">{#reports_legend#|escape}</h3>
  {foreach from=$reports_additional_options.legend item=leg name=lg}
    <strong>{$leg.code|escape}</strong> - <i>{$leg.name|escape}</i>{if !$smarty.foreach.lg.last}<br />{/if}
  {/foreach}
  <br />
  <br />
  <button type="button" id="insert_in_doc_button" onclick="completeInSalaryDocument(this); return false;" class="button">{#report_complete_in_salary#|escape}</button>
  {if $export_permission}
  <button type="button" name="export_selected_records" class="button reports_export_button" onclick="window.location.href='{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;{$action_param}=export&amp;report_type={$report_type}&amp;pattern=';">{#export_report#|escape}</button>
{/if}
{else}
  {#no_items_found#|escape}
{/if}
