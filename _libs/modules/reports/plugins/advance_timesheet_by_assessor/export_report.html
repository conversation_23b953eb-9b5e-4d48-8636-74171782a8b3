<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="vertical-align: middle; text-align: center; width: 140px;"><strong>{#reports_report_office#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 140px;"><strong>{#reports_report_num#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 70px;"><strong>{#reports_report_date#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{#reports_main_object#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{#reports_rated_object#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{#reports_address#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{#reports_client#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 140px;"><strong>{#reports_type_business#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{#reports_agent#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{#reports_user_bank#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 70px;"><strong>{#reports_order_date#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 70px;"><strong>{#reports_present_date#|escape}</strong></td>
        {foreach from=$reports_additional_options.available_roles item=avb_role}
          {capture assign='role_column_label'}reports_{$avb_role}{/capture}
          <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{$smarty.config.$role_column_label|escape}</strong></td>
        {/foreach}
        <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_price_rating#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 90px;">
            <strong>{#reports_invoice_num#|escape}</strong>
        </td>
        <td style="vertical-align: middle; text-align: center; width: 70px;">
            <strong>{#reports_invoice_date#|escape}</strong>
        </td>
        <td style="vertical-align: middle; text-align: center; width: 100px;"><strong>{#reports_price_without_vat#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 70px;"><strong>{#reports_payment_date#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 60px;"><strong>{#reports_system_made_by#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 60px;"><strong>{#reports_total#|escape}</strong></td>
        <td style="vertical-align: middle; text-align: center; width: 200px;">
            <strong>{#reports_added_by#|escape}</strong>
        </td>
        {if $reports_additional_options.display_comments}
        <td style="vertical-align: middle; text-align: center; width: 200px;"><strong>{#reports_comments#|escape}</strong></td>
        {/if}
      </tr>
      {foreach from=$reports_results item=result name=results}
        {foreach from=$result.rated item=rated_by name=rb}
          {foreach from=$rated_by.invoices item=rel_doc name=rd}
            {foreach from=$rel_doc.payments item=payment name=pay}
              <tr>
                {if $smarty.foreach.pay.first && $smarty.foreach.rd.first}
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.office|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'\@';">
                    {$result.full_num|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                    {$result.date|date_format:#date_short#|escape|default:"-"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.main_objects|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.rated_objects_names|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.rated_objects_addresses|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.customer_name|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.type_business|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.agent_name|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.bank_name|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                    {$result.order_date|date_format:#date_short#|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                    {$result.present_date|date_format:#date_short#|escape|default:"&nbsp;"}
                  </td>
                  {foreach from=$reports_additional_options.available_roles item=avb_role}
                    <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'\@';">
                      {if $avb_role eq 'rated'}
                        {$rated_by.name|escape|default:"&nbsp;"}
                      {else}
                        {foreach from=$result.$avb_role item=role_user name=ru}
                          {$role_user|escape|default:"&nbsp;"}{if !$smarty.foreach.ru.last}<br />{/if}
                        {foreachelse}
                          &nbsp;
                        {/foreach}
                      {/if}
                    </td>
                  {/foreach}
                  <td style="vertical-align: middle;" align="right" rowspan="{$result.rowspan}">
                    {$result.rating|default:"&nbsp;"}
                  </td>
                {/if}
                {if $smarty.foreach.pay.first}
                  <td style="vertical-align: middle; mso-number-format:'\@';" rowspan="{$rel_doc.rowspan}">
                   {$rel_doc.num|escape|default:"&nbsp;"}
                  </td>
                  <td style="vertical-align: middle; mso-number-format:'\@';" rowspan="{$rel_doc.rowspan}">
                      {$rel_doc.date|date_format:#date_short#|escape|default:"-"}
                  </td>
                  <td align="right" style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$rel_doc.rowspan}">
                    {$rel_doc.price|default:"0.00"}
                  </td>
                {/if}
                <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                  {$payment.payment_date|date_format:#date_short#|escape|default:"&nbsp;"}
                </td>
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {$result.system_made_by|escape|default:"&nbsp;"}
                </td>
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {$result.total|escape|default:"&nbsp;"}
                </td>
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {$result.added_by_name|escape|default:"&nbsp;"}
                </td>
                {if $reports_additional_options.display_comments}
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {foreach from=$result.comments item='comment'}
                    {$comment.added|date_format:#date_mid#|escape}, {$comment.added_by_name}<br />
                    {if $comment.subject}{$comment.subject|mb_wordwrap:50}<br />{/if}
                    {$comment.content|regex_replace:"#<br>(\r\n|\n)?$#":""|mb_wordwrap:50}<br />
                  {foreachelse}
                    {#reports_no_comments#}
                  {/foreach}
                </td>
                {/if}
              </tr>
            {foreachelse}
              <tr>
                {if $smarty.foreach.rd.first}
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.office|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'\@';">
                    {$result.full_num|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                    {$result.date|date_format:#date_short#|escape|default:"-"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.main_objects|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.rated_objects_names|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.rated_objects_addresses|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.customer_name|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.type_business|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.agent_name|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                    {$result.bank_name|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                    {$result.order_date|date_format:#date_short#|escape|default:"&nbsp;"}
                  </td>
                  <td rowspan="{$result.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                    {$result.present_date|date_format:#date_short#|escape|default:"&nbsp;"}
                  </td>
                  {foreach from=$reports_additional_options.available_roles item=avb_role}
                    <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'\@';">
                      {if $avb_role eq 'rated'}
                        {$rated_by.name|escape|default:"&nbsp;"}
                      {else}
                        {foreach from=$result.$avb_role item=role_user name=ru}
                          {$role_user|escape|default:"&nbsp;"}{if !$smarty.foreach.ru.last}<br />{/if}
                        {foreachelse}
                          &nbsp;
                        {/foreach}
                      {/if}
                    </td>
                  {/foreach}
                  <td style="vertical-align: middle;" align="right" rowspan="{$result.rowspan}">
                    {$result.rating|default:"&nbsp;"}
                  </td>
                {/if}
                <td style="vertical-align: middle; mso-number-format:'\@';" rowspan="{$rel_doc.rowspan}">
                  {$rel_doc.num|escape|default:"&nbsp;"}
                </td>
                  <td style="vertical-align: middle; mso-number-format:'\@';" rowspan="{$rel_doc.rowspan}">
                      {$rel_doc.date|date_format:#date_short#|escape|default:"-"}
                  </td>
                <td align="right" style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$rel_doc.rowspan}">
                  {$rel_doc.price|default:"0.00"}
                </td>
                <td style="vertical-align: middle; text-align: center;">
                  <span style="color: #888888;">{#reports_no_specified_payments#}</span>
                </td>
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {$result.system_made_by|escape|default:"&nbsp;"}
                </td>
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {$result.total|escape|default:"&nbsp;"}
                </td>
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {$result.added_by_name|escape|default:"&nbsp;"}
                </td>
                {if $reports_additional_options.display_comments}
                <td nowrap="nowrap" style="vertical-align: middle;">
                  {foreach from=$result.comments item='comment'}
                    {$comment.added|date_format:#date_mid#|escape}, {$comment.added_by_name}<br />
                    {if $comment.subject}{$comment.subject|mb_wordwrap:50}<br />{/if}
                    {$comment.content|regex_replace:"#<br>(\r\n|\n)?$#":""|mb_wordwrap:50}<br />
                  {foreachelse}
                    {#reports_no_comments#}
                  {/foreach}
                </td>
                {/if}
              </tr>
            {/foreach}
          {foreachelse}
            <tr>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.office|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'\@';">
                {$result.full_num|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                {$result.date|date_format:#date_short#|escape|default:"-"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.main_objects|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.rated_objects_names|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.rated_objects_addresses|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.customer_name|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.type_business|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.agent_name|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle;">
                {$result.bank_name|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                {$result.order_date|date_format:#date_short#|escape|default:"&nbsp;"}
              </td>
              <td rowspan="{$result.rowspan}" style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';">
                {$result.present_date|date_format:#date_short#|escape|default:"&nbsp;"}
              </td>
              {foreach from=$reports_additional_options.available_roles item=avb_role}
                <td rowspan="{$result.rowspan}" nowrap="nowrap" style="vertical-align: middle; mso-number-format:'\@';">
                  {if $avb_role eq 'rated'}
                    {$rated_by.name|escape|default:"&nbsp;"}
                  {else}
                    {foreach from=$result.$avb_role item=role_user name=ru}
                      {$role_user|escape|default:"&nbsp;"}{if !$smarty.foreach.ru.last}<br />{/if}
                    {foreachelse}
                      &nbsp;
                    {/foreach}
                  {/if}
                </td>
              {/foreach}
              <td style="vertical-align: middle;" align="right" rowspan="{$result.rowspan}">
                {$result.rating|default:"&nbsp;"}
              </td>
              <td colspan="4" style="vertical-align: middle; text-align: center;">
                <span style="color: #888888;">{#reports_no_invoices#}</span>
              </td>
              <td nowrap="nowrap" style="vertical-align: middle;">
                {$result.system_made_by|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" style="vertical-align: middle;">
                {$result.total|escape|default:"&nbsp;"}
              </td>
              <td nowrap="nowrap" style="vertical-align: middle;">
                {$result.added_by_name|escape|default:"&nbsp;"}
              </td>
              {if $reports_additional_options.display_comments}
              <td nowrap="nowrap" style="vertical-align: middle;">
                {foreach from=$result.comments item='comment'}
                  {$comment.added|date_format:#date_mid#|escape}, {$comment.added_by_name}<br />
                  {if $comment.subject}{$comment.subject|mb_wordwrap:50}<br />{/if}
                  {$comment.content|regex_replace:"#<br>(\r\n|\n)?$#":""|mb_wordwrap:50}<br />
                {foreachelse}
                  {#reports_no_comments#}
                {/foreach}
              </td>
              {/if}
            </tr>
          {/foreach}
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="{math equation='x + y' x=20 y=$reports_additional_options.available_roles|@count}"><span style="color:red;">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>

    {if !$reports_additional_options.hide_second_table}
      <br />
      <table border="1" cellpadding="0" cellspacing="0">
        <tr>
          <td style="vertical-align: middle;"><strong>{#reports_employee#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_count_rates#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_income#|escape}</strong></td>
        </tr>
        {foreach from=$reports_additional_options.totals item=user_total}
          <tr>
            <td width="294">
              {$user_total.name|escape|default:"&nbsp;"}
            </td>
            <td width="62" style="mso-number-format: '0\.00';">
              {$user_total.count_rates|string_format:"%.2f"|default:"0.00"}
            </td>
            <td width="62" style="mso-number-format: '0\.00';">
              {$user_total.sum|string_format:"%.2f"|default:"0.00"}
            </td>
          </tr>
        {foreachelse}
          <tr>
            <td colspan="3">
              {#no_items_found#|escape}
            </td>
          </tr>
        {/foreach}
      </table>
    {/if}
  </body>
</html>
