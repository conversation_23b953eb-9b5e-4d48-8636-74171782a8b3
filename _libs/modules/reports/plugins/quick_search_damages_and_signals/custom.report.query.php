<?php
    Class Quick_Search_Damages_And_Signals Extends Reports {
        public static $itemsPerPage = 100;

        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $completed_filters = false;
            $available_filters = array_keys($registry->get('report_filters'));
            foreach ($available_filters as $avb_filt) {
                if (!empty($filters[$avb_filt])) {
                    $completed_filters = true;
                    break;
                }
            }

            if (!$completed_filters) {
                $records = array('additional_options' => array('dont_show_export_button' => true));
                if (!empty($filters['paginate'])) {
                    $registry['messages']->setError($registry['translater']->translate('error_reports_complete_at_least_one_filter'));
                    $results = array($records, 0);
                } else {
                    //there is no limit set,
                    //get the count from the found records
                    $results = $records;
                }
                return $results;
            }

            // escape the filters
            $filters = General::slashesEscape($filters);

            $included_documents = array_filter(preg_split('/\s*,\s*/', INCLUDED_DOCUMENTS));

            $doc_vars = array(DOCUMENT_VAR_TYPE_DAMAGE, DOCUMENT_VAR_EXTRA_INFO, DOCUMENT_VAR_POLICY, DOCUMENT_VAR_OWNER,
                              DOCUMENT_VAR_REG_NUM,DOCUMENT_VAR_CAR, DOCUMENT_VAR_DATETIME_SIGNAL, DOCUMENT_VAR_SEND_SIGNAL,
                              DOCUMENT_VAR_EVENT_DATE_PLACE, DOCUMENT_VAR_INCIDENT_DESCRIPTION, DOCUMENT_VAR_PTP_PARTICIPANTS,
                              DOCUMENT_VAR_COLLATERAL, DOCUMENT_VAR_AFFECTED, DOCUMENT_VAR_SIGNAL_NUMBER, DOCUMENT_VAR_TYPE_RISK,
                              DOCUMENT_VAR_RESPONSIBLE, DOCUMENT_VAR_REGION, DOCUMENT_VAR_CITY, DOCUMENT_VAR_HEIR, DOCUMENT_VAR_HEIR_UCN,
                              DOCUMENT_VAR_TYPE_RISK, DOCUMENT_VAR_HIGHWAY, DOCUMENT_VAR_DISTRICT,
                              DOCUMENT_VAR_CLAIMED_AMOUNT, DOCUMENT_VAR_COUNTERCLAIM_EVALUATION, DOCUMENT_VAR_NONMATERIAL_DAMAGES, DOCUMENT_VAR_MATERIAL_DAMAGES,
                              DOCUMENT_VAR_CASE_LAW, DOCUMENT_VAR_CLAIMED_RESERVED_AMOUNT);

            // sql to take the ids of the needed additional vars from the documents and expenses reasons
            $sql_for_add_vars = 'SELECT `name`, GROUP_CONCAT(`id`) as id FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `model_type` IN (' . implode(',', $included_documents) . ') AND `name` IN ("' . implode('","', $doc_vars) . '") GROUP BY `name`';
            $var_ids = $registry['db']->GetAssoc($sql_for_add_vars);

            $sql_pre_sort = array();
            $sql_pre_sort['select'] = 'SELECT SQL_CALC_FOUND_ROWS d.id';

            $where = array();
            $where[] = 'd.deleted_by=0';
            $where[] = 'd.active=1';
            if (!empty($filters['doc_type'])) {
                $where[] = 'd.type=' . $filters['doc_type'];
            } else {
                if (!empty($included_documents)) {
                    $where[] = 'd.type IN (' . implode(',', $included_documents) . ')';
                }
            }

            if (!empty($filters['status'])) {
                $status_elements = explode('_', $filters['status']);
                if (!empty($status_elements[0])) {
                    $where[] = 'd.status="' . $status_elements[0] . '"';
                }
                if (!empty($status_elements[1])) {
                    $where[] = 'd.substatus="' . $status_elements[1] . '"';
                }
            }
            if (!empty($filters['period_from'])) {
                $where[] = 'd.date>="' . $filters['period_from'] . '"';
            }
            if (!empty($filters['period_to'])) {
                $where[] = 'd.date<="' . $filters['period_to'] . '"';
            }
            if (!empty($filters['income_num'])) {
                $where[] = 'd.full_num LIKE "%' . $filters['income_num'] . '%"';
            }
            if (!empty($filters['damage_num'])) {
                $where[] = 'd.custom_num LIKE "%' . $filters['damage_num'] . '%"';
            }

            // prepare the presort query
            $sql_pre_sort['from'] = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                    'INNER JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                    '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '" AND ' . implode(' AND ', $where) . ')' . "\n" .
                                    'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ptp_part' . "\n" .
                                    '  ON (d.id=d_cstm_ptp_part.model_id AND d_cstm_ptp_part.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_PTP_PARTICIPANTS]) ? $var_ids[DOCUMENT_VAR_PTP_PARTICIPANTS] : 0) . ') AND (d_cstm_ptp_part.lang="" OR d_cstm_ptp_part.lang="' . $registry['lang'] . '"))' . "\n";

            if (!empty($filters['type_damage'])) {
                $where[] = 'd_cstm_type_dmg.value IN ("' . implode('","', $filters['type_damage']) . '")';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_dmg' . "\n" .
                                         '  ON (d.id=d_cstm_type_dmg.model_id AND d_cstm_type_dmg.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_TYPE_DAMAGE]) ? $var_ids[DOCUMENT_VAR_TYPE_DAMAGE] : 0) . ') AND (d_cstm_type_dmg.lang="" OR d_cstm_type_dmg.lang="' . $registry['lang'] . '") AND d_cstm_type_dmg.value IN ("' . implode('","', $filters['type_damage']) . '"))' . "\n";
            }
            if (!empty($filters['policy_num'])) {
                $where[] = 'd_cstm_policy_num.value LIKE "%' . $filters['policy_num'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_policy_num' . "\n" .
                                         '  ON (d.id=d_cstm_policy_num.model_id AND d_cstm_policy_num.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_POLICY]) ? $var_ids[DOCUMENT_VAR_POLICY] : 0) . ') AND (d_cstm_policy_num.lang="" OR d_cstm_policy_num.lang="' . $registry['lang'] . '") AND d_cstm_policy_num.value LIKE "%' . $filters['policy_num'] . '%")' . "\n";
            }
            if (!empty($filters['owner'])) {
                $where[] = 'd_cstm_owner.value LIKE "%' . $filters['owner'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_owner' . "\n" .
                                         '  ON (d.id=d_cstm_owner.model_id AND d_cstm_owner.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_OWNER]) ? $var_ids[DOCUMENT_VAR_OWNER] : 0) . ') AND (d_cstm_owner.lang="" OR d_cstm_owner.lang="' . $registry['lang'] . '") AND d_cstm_owner.value LIKE "%' . $filters['owner'] . '%")' . "\n";
            }
            if (!empty($filters['reg_num'])) {
                $where[] = 'd_cstm_reg_num.value LIKE "%' . $filters['reg_num'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_reg_num' . "\n" .
                                         '  ON (d.id=d_cstm_reg_num.model_id AND d_cstm_reg_num.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_REG_NUM]) ? $var_ids[DOCUMENT_VAR_REG_NUM] : 0) . ') AND (d_cstm_reg_num.lang="" OR d_cstm_reg_num.lang="' . $registry['lang'] . '") AND d_cstm_ptp_part.num=d_cstm_reg_num.num AND d_cstm_reg_num.value LIKE "%' . $filters['reg_num'] . '%")' . "\n";
            }
            if (!empty($filters['car'])) {
                $where[] = 'd_cstm_car.value LIKE "%' . $filters['car'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_car' . "\n" .
                                         '  ON (d.id=d_cstm_car.model_id AND d_cstm_car.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_CAR]) ? $var_ids[DOCUMENT_VAR_CAR] : 0) . ') AND (d_cstm_car.lang="" OR d_cstm_car.lang="' . $registry['lang'] . '") AND d_cstm_ptp_part.num=d_cstm_car.num AND d_cstm_car.value LIKE "%' . $filters['car'] . '%")' . "\n";
            }
            if (!empty($filters['signal_time_from'])) {
                $where[] = 'd_cstm_date_signal.value>="' . $filters['signal_time_from'] . ':00"';
                $clause_signal = 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date_signal' . "\n" .
                                 '  ON d.id=d_cstm_date_signal.model_id AND d_cstm_date_signal.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_DATETIME_SIGNAL]) ? $var_ids[DOCUMENT_VAR_DATETIME_SIGNAL] : 0) . ') AND (d_cstm_date_signal.lang="" OR d_cstm_date_signal.lang="' . $registry['lang'] . '") AND d_cstm_date_signal.value>="' . $filters['signal_time_from'] . ':00"' . "\n";
            }
            if (!empty($filters['signal_time_to'])) {
                $where[] = 'd_cstm_date_signal.value<="' . $filters['signal_time_to'] . ':00"';
                if (!isset($clause_signal)) {
                    $clause_signal = 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date_signal' . "\n" .
                                     '  ON d.id=d_cstm_date_signal.model_id AND d_cstm_date_signal.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_DATETIME_SIGNAL]) ? $var_ids[DOCUMENT_VAR_DATETIME_SIGNAL] : 0) . ') AND (d_cstm_date_signal.lang="" OR d_cstm_date_signal.lang="' . $registry['lang'] . '")' . "\n";
                }
                $clause_signal .= ' AND d_cstm_date_signal.value<="' . $filters['signal_time_to'] . ':00"';
            }
            if (isset($clause_signal)) {
                $sql_pre_sort['from'] .= $clause_signal;
            }
            if (!empty($filters['signal_sent_by'])) {
                $where[] = 'd_cstm_caused_dmg.value LIKE "%' . $filters['signal_sent_by'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_caused_dmg' . "\n" .
                                         '  ON (d.id=d_cstm_caused_dmg.model_id AND d_cstm_caused_dmg.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_SEND_SIGNAL]) ? $var_ids[DOCUMENT_VAR_SEND_SIGNAL] : 0) . ') AND (d_cstm_caused_dmg.lang="" OR d_cstm_caused_dmg.lang="' . $registry['lang'] . '") AND d_cstm_caused_dmg.value LIKE "%' . $filters['signal_sent_by'] . '%")' . "\n";

            }
            if (!empty($filters['signal_num'])) {
                $where[] = '((d_cstm_signal_num.value LIKE "%' . $filters['signal_num'] . '%" AND d.type="' . DOCUMENT_TYPE_DAMAGE . '") OR (d.full_num LIKE "%' . $filters['signal_num'] . '%" AND d.type="' . DOCUMENT_TYPE_DAMAGE_SIGNAL . '"))';
                $sql_pre_sort['from'] .= 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_signal_num' . "\n" .
                                         '  ON (d.id=d_cstm_signal_num.model_id AND d_cstm_signal_num.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_SIGNAL_NUMBER]) ? $var_ids[DOCUMENT_VAR_SIGNAL_NUMBER] : 0) . ') AND (d_cstm_signal_num.lang="" OR d_cstm_signal_num.lang="' . $registry['lang'] . '"))' . "\n";
            }

            if (!empty($filters['client'])) {
                $where[] = '(d.customer="' . $filters['client'] . '" OR d_cstm_affected.value="' . $filters['client'] . '")';
                $sql_pre_sort['from'] .= 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_affected' . "\n" .
                                         '  ON (d.id=d_cstm_affected.model_id AND d_cstm_affected.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_AFFECTED]) ? $var_ids[DOCUMENT_VAR_AFFECTED] : 0) . ') AND (d_cstm_affected.lang="" OR d_cstm_affected.lang="' . $registry['lang'] . '") AND d_cstm_ptp_part.num=d_cstm_affected.num)' . "\n";

            }
            if (!empty($filters['type_risk'])) {
                $where[] = 'd_cstm_type_risk.value IN ("' . implode('","', $filters['type_risk']) . '")';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_risk' . "\n" .
                                         '  ON (d.id=d_cstm_type_risk.model_id AND d_cstm_type_risk.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_TYPE_RISK]) ? $var_ids[DOCUMENT_VAR_TYPE_RISK] : 0) . ') AND (d_cstm_type_risk.lang="" OR d_cstm_type_risk.lang="' . $registry['lang'] . '") AND d_cstm_type_risk.value IN ("' . implode('","', $filters['type_risk']) . '"))' . "\n";
            }
            if (!empty($filters['participant'])) {
                $where[] = 'd_cstm_ptp_part.value LIKE "%' . $filters['participant'] . '%"';


            }
            if (!empty($filters['responsible'])) {
                $where[] = 'd_cstm_responsible.value LIKE "%' . $filters['responsible'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_responsible' . "\n" .
                                         '  ON (d.id=d_cstm_responsible.model_id AND d_cstm_responsible.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_RESPONSIBLE]) ? $var_ids[DOCUMENT_VAR_RESPONSIBLE] : 0) . ') AND (d_cstm_responsible.lang="" OR d_cstm_responsible.lang="' . $registry['lang'] . '") AND d_cstm_responsible.value LIKE "%' . $filters['responsible'] . '%")' . "\n";
            }
            if (!empty($filters['ptp_region'])) {
                $where[] = 'd_cstm_region.value LIKE "%' . $filters['ptp_region'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_region' . "\n" .
                                         '  ON (d.id=d_cstm_region.model_id AND d_cstm_region.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_REGION]) ? $var_ids[DOCUMENT_VAR_REGION] : 0) . ') AND (d_cstm_region.lang="" OR d_cstm_region.lang="' . $registry['lang'] . '") AND d_cstm_region.value LIKE "%' . $filters['ptp_region'] . '%")' . "\n";

            }
            if (!empty($filters['ptp_city'])) {
                $where[] = 'd_cstm_city.value LIKE "%' . $filters['ptp_city'] . '%"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_city' . "\n" .
                                         '  ON (d.id=d_cstm_city.model_id AND d_cstm_city.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_CITY]) ? $var_ids[DOCUMENT_VAR_CITY] : 0) . ') AND (d_cstm_city.lang="" OR d_cstm_city.lang="' . $registry['lang'] . '") AND d_cstm_city.value LIKE "%' . $filters['ptp_city'] . '%")' . "\n";
            }
            if (!empty($filters['heir'])) {
                $heir_elements = preg_split('#\s*,\s*#', $filters['heir']);
                $heir_elements = array_filter($heir_elements);
                $current_wheres = array();
                foreach ($heir_elements as $h_filt) {
                    $current_wheres[] = 'd_cstm_heir.value LIKE "%' . $h_filt . '%"';
                    $current_wheres[] = 'd_cstm_heir_ucn.value LIKE "%' . $h_filt . '%"';
                }
                $where[] = '(' . implode(' OR ', $current_wheres) . ')';

                $sql_pre_sort['from'] .= 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_heir' . "\n" .
                                         '  ON (d.id=d_cstm_heir.model_id AND d_cstm_heir.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_HEIR]) ? $var_ids[DOCUMENT_VAR_HEIR] : 0) . ') AND (d_cstm_heir.lang="" OR d_cstm_heir.lang="' . $registry['lang'] . '"))' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_heir_ucn' . "\n" .
                                         '  ON (d.id=d_cstm_heir_ucn.model_id AND d_cstm_heir_ucn.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_HEIR_UCN]) ? $var_ids[DOCUMENT_VAR_HEIR_UCN] : 0) . ') AND d_cstm_heir.num=d_cstm_heir_ucn.num AND (d_cstm_heir_ucn.lang="" OR d_cstm_heir_ucn.lang="' . $registry['lang'] . '"))' . "\n";
            }
            if (!empty($filters['highway'])) {
                $where[] = 'd_cstm_highway.value="' . $filters['highway'] . '"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_highway' . "\n" .
                                         '  ON (d.id=d_cstm_highway.model_id AND d_cstm_highway.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_HIGHWAY]) ? $var_ids[DOCUMENT_VAR_HIGHWAY] : 0) . ') AND (d_cstm_highway.lang="" OR d_cstm_highway.lang="' . $registry['lang'] . '") AND d_cstm_highway.value="' . $filters['highway'] . '")' . "\n";
            }
            if (!empty($filters['district'])) {
                $where[] = 'd_cstm_district.value="' . $filters['district'] . '"';
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_district' . "\n" .
                                         '  ON (d.id=d_cstm_district.model_id AND d_cstm_district.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_DISTRICT]) ? $var_ids[DOCUMENT_VAR_DISTRICT] : 0) . ') AND (d_cstm_district.lang="" OR d_cstm_district.lang="' . $registry['lang'] . '") AND d_cstm_district.value="' . $filters['district'] . '")' . "\n";
            }
            if (!empty($filters['show_damage_signals'])) {
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_dmg_sig' . "\n" .
                                         '  ON (d.id=d_cstm_dmg_sig.model_id AND d_cstm_dmg_sig.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_SIGNAL_NUMBER]) ? $var_ids[DOCUMENT_VAR_SIGNAL_NUMBER] : 0) . ') AND (d_cstm_dmg_sig.lang="" OR d_cstm_dmg_sig.lang="' . $registry['lang'] . '") AND (d_cstm_dmg_sig.value!="" AND d_cstm_dmg_sig.value IS NOT NULL AND d_cstm_dmg_sig.value!=0))' . "\n";
            }

            if (!empty($filters['show_damage_agreements'])) {
                $sql_pre_sort['from'] .= 'INNER JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS d_rl_agree' . "\n" .
                                         '  ON (d_rl_agree.origin="transformed" AND d_rl_agree.parent_model_name="Document" AND d_rl_agree.link_to_model_name="Document" AND d_rl_agree.link_to=d.id)' . "\n" .
                                         'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d_rel_agree' . "\n" .
                                         '  ON (d_rel_agree.id=d_rl_agree.parent_id AND d_rel_agree.active=1 AND d_rel_agree.deleted_by=0 AND d_rel_agree.type="' . DOCUMENT_TYPE_AGREEMENT . '")';
            }

            // Filter by comment
            if (defined('FILTER_BY_COMMENT') && constant('FILTER_BY_COMMENT') === '1' && !empty($filters['comment'])) {
                $filters_comment = preg_replace('/(\r\n|\r|\n)/', '%', $filters['comment']);
                $sql_pre_sort['from'] .= 'INNER JOIN (' . "\n" .
                                         '  SELECT DISTINCT(model_id) AS model_id' . "\n" .
                                         '    FROM ' . DB_TABLE_COMMENTS . "\n" .
                                         '    WHERE model = "Document"' . "\n" .
                                         '      AND content LIKE "%' . $filters_comment . '%"' . "\n" .
                                         '  ) AS comments_tmp' . "\n" .
                                         '  ON (comments_tmp.model_id = d.id)' . "\n";
            }

            if (!empty($where)) {
                $sql_pre_sort['where'] = 'WHERE ' . implode(' AND ', $where);
            }
            $sql_pre_sort['group'] = 'GROUP BY d.id';
            $sql_pre_sort['order'] = 'ORDER BY d.id DESC';

            if ($registry['action'] != 'export') {
                $records_per_page = (!empty($filters['display']) ? $filters['display'] : 100);
                $page = (!empty($filters['page']) ? $filters['page'] : 1);

                // prepare the pagination
                $sql_pre_sort['limit'] = 'LIMIT ' . (($page-1)*$records_per_page) . ',' . $records_per_page;
            }
            $ids = $registry['db']->GetCol(implode("\n", $sql_pre_sort));
            $total = $registry['db']->getOne('SELECT FOUND_ROWS()');

            $records = array();
            if (!empty($ids)) {
                $sql = array();
                $sql['select']  = 'SELECT d.id, d.full_num, di18n.name, d.custom_num, d_cstm_policy_num.value as policy_num, d.date, ni18n_reg.name as region, d.customer, ' . "\n" .
                                  '       CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ni18n_city.name as city, d_cstm_signal_num.value as signal_num,' . "\n" .
                                  '       d_cstm_caused_dmg.value as send_signal, d_cstm_extra_inf.value as extra_info, d_cstm_ptp_part.value as ptp_participants, ' . "\n" .
                                  '       d_cstm_ptp_part.num as ptp_num, d_cstm_reg_num.value as reg_num, d_cstm_car.value as car, d_cstm_date_signal.value as signal_datetime, ' . "\n" .
                                  '       d_cstm_type_dmg_name.label as type_damage, d_cstm_type_risk_name.label as type_risk, d_cstm_caused_dmg.value as send_signal, ' . "\n" .
                                  '       d_cstm_owner.value as owner, di18n.description as incident_description, d_cstm_ptp_part.value as ptp_participants, ' . "\n" .
                                  '       d_cstm_collateral.value as collateral, d.status, d_cstm_heir.value as heir, d_cstm_heir_ucn.value as heir_ucn, ' . "\n" .
                                  '       CONCAT(resp_name.firstname, " ", resp_name.lastname) as responsible, ds.name as substatus, ' . "\n" .
                                  '       d_cstm_claimed_amount.value as claimed_amount, d_cstm_res_claimed_amount.value as claimed_reserved_amount, ' . "\n" .
                                  '       d_cstm_counterclaim_evaluation.value as counterclaim_evaluation, d_cstm_nonmaterial_damages.value as nonmaterial_damages, ' . "\n" .
                                  '       d_cstm_material_damages.value as material_damages, d_cstm_case_law.value as case_law' . "\n";

                //from clause
                $sql['from']    = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                  'INNER JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                  '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '" AND d.id IN (' . implode(',', $ids) . '))' . "\n" .
                                  'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                  '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_policy_num' . "\n" .
                                  '  ON (d.id=d_cstm_policy_num.model_id AND d_cstm_policy_num.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_POLICY]) ? $var_ids[DOCUMENT_VAR_POLICY] : 0) . ') AND (d_cstm_policy_num.lang="" OR d_cstm_policy_num.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_signal_num' . "\n" .
                                  '  ON (d.id=d_cstm_signal_num.model_id AND d_cstm_signal_num.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_SIGNAL_NUMBER]) ? $var_ids[DOCUMENT_VAR_SIGNAL_NUMBER] : 0) . ') AND (d_cstm_signal_num.lang="" OR d_cstm_signal_num.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_dmg' . "\n" .
                                  '  ON (d.id=d_cstm_type_dmg.model_id AND d_cstm_type_dmg.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_TYPE_DAMAGE]) ? $var_ids[DOCUMENT_VAR_TYPE_DAMAGE] : 0) . ') AND (d_cstm_type_dmg.lang="" OR d_cstm_type_dmg.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_type_dmg_name' . "\n" .
                                  '  ON (d_cstm_type_dmg_name.parent_name="' . DOCUMENT_VAR_TYPE_DAMAGE . '" AND d_cstm_type_dmg.value=d_cstm_type_dmg_name.option_value AND d_cstm_type_dmg_name.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_region' . "\n" .
                                  '  ON (d.id=d_cstm_region.model_id AND d_cstm_region.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_REGION]) ? $var_ids[DOCUMENT_VAR_REGION] : 0) . ') AND (d_cstm_region.lang="" OR d_cstm_region.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_reg' . "\n" .
                                  '  ON (d_cstm_region.value=ni18n_reg.parent_id AND ni18n_reg.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_city' . "\n" .
                                  '  ON (d.id=d_cstm_city.model_id AND d_cstm_city.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_CITY]) ? $var_ids[DOCUMENT_VAR_CITY] : 0) . ') AND (d_cstm_city.lang="" OR d_cstm_city.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_city' . "\n" .
                                  '  ON (d_cstm_city.value=ni18n_city.parent_id AND ni18n_city.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_extra_inf' . "\n" .
                                  '  ON (d.id=d_cstm_extra_inf.model_id AND d_cstm_extra_inf.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_EXTRA_INFO]) ? $var_ids[DOCUMENT_VAR_EXTRA_INFO] : 0) . ') AND (d_cstm_extra_inf.lang="" OR d_cstm_extra_inf.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ptp_part' . "\n" .
                                  '  ON (d.id=d_cstm_ptp_part.model_id AND d_cstm_ptp_part.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_PTP_PARTICIPANTS]) ? $var_ids[DOCUMENT_VAR_PTP_PARTICIPANTS] : 0) . ') AND (d_cstm_ptp_part.lang="" OR d_cstm_ptp_part.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_car' . "\n" .
                                  '  ON (d.id=d_cstm_car.model_id AND d_cstm_car.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_CAR]) ? $var_ids[DOCUMENT_VAR_CAR] : 0) . ') AND (d_cstm_car.lang="" OR d_cstm_car.lang="' . $registry['lang'] . '") AND d_cstm_ptp_part.num=d_cstm_car.num)' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_reg_num' . "\n" .
                                  '  ON (d.id=d_cstm_reg_num.model_id AND d_cstm_reg_num.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_REG_NUM]) ? $var_ids[DOCUMENT_VAR_REG_NUM] : 0) . ') AND (d_cstm_reg_num.lang="" OR d_cstm_reg_num.lang="' . $registry['lang'] . '") AND d_cstm_ptp_part.num=d_cstm_reg_num.num)' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date_signal' . "\n" .
                                  '  ON (d.id=d_cstm_date_signal.model_id AND d_cstm_date_signal.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_DATETIME_SIGNAL]) ? $var_ids[DOCUMENT_VAR_DATETIME_SIGNAL] : 0) . ') AND (d_cstm_date_signal.lang="" OR d_cstm_date_signal.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_risk' . "\n" .
                                  '  ON (d.id=d_cstm_type_risk.model_id AND d_cstm_type_risk.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_TYPE_RISK]) ? $var_ids[DOCUMENT_VAR_TYPE_RISK] : 0) . ') AND (d_cstm_type_risk.lang="" OR d_cstm_type_risk.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_type_risk_name' . "\n" .
                                  '  ON (d_cstm_type_risk_name.parent_name="' . DOCUMENT_VAR_TYPE_RISK . '" AND d_cstm_type_risk.value=d_cstm_type_risk_name.option_value AND d_cstm_type_risk_name.lang="' . $model_lang . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_owner' . "\n" .
                                  '  ON (d.id=d_cstm_owner.model_id AND d_cstm_owner.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_OWNER]) ? $var_ids[DOCUMENT_VAR_OWNER] : 0) . ') AND (d_cstm_owner.lang="" OR d_cstm_owner.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_caused_dmg' . "\n" .
                                  '  ON (d.id=d_cstm_caused_dmg.model_id AND d_cstm_caused_dmg.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_SEND_SIGNAL]) ? $var_ids[DOCUMENT_VAR_SEND_SIGNAL] : 0) . ') AND (d_cstm_caused_dmg.lang="" OR d_cstm_caused_dmg.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_collateral' . "\n" .
                                  '  ON (d.id=d_cstm_collateral.model_id AND d_cstm_collateral.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_COLLATERAL]) ? $var_ids[DOCUMENT_VAR_COLLATERAL] : 0) . ') AND (d_cstm_collateral.lang="" OR d_cstm_collateral.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_heir' . "\n" .
                                  '  ON (d.id=d_cstm_heir.model_id AND d_cstm_heir.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_HEIR]) ? $var_ids[DOCUMENT_VAR_HEIR] : 0) . ') AND (d_cstm_heir.lang="" OR d_cstm_heir.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_heir_ucn' . "\n" .
                                  '  ON (d.id=d_cstm_heir_ucn.model_id AND d_cstm_heir_ucn.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_HEIR_UCN]) ? $var_ids[DOCUMENT_VAR_HEIR_UCN] : 0) . ') AND d_cstm_heir.num=d_cstm_heir_ucn.num AND (d_cstm_heir_ucn.lang="" OR d_cstm_heir_ucn.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_responsible' . "\n" .
                                  '  ON (d.id=d_cstm_responsible.model_id AND d_cstm_responsible.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_RESPONSIBLE]) ? $var_ids[DOCUMENT_VAR_RESPONSIBLE] : 0) . ') AND (d_cstm_responsible.lang="" OR d_cstm_responsible.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_claimed_amount' . "\n" .
                                  '  ON (d.id=d_cstm_claimed_amount.model_id AND d_cstm_claimed_amount.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_CLAIMED_AMOUNT]) ? $var_ids[DOCUMENT_VAR_CLAIMED_AMOUNT] : 0) . ') AND (d_cstm_claimed_amount.lang="" OR d_cstm_claimed_amount.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_res_claimed_amount' . "\n" .
                                  '  ON (d.id=d_cstm_res_claimed_amount.model_id AND d_cstm_res_claimed_amount.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_CLAIMED_RESERVED_AMOUNT]) ? $var_ids[DOCUMENT_VAR_CLAIMED_RESERVED_AMOUNT] : 0) . ') AND (d_cstm_res_claimed_amount.lang="" OR d_cstm_res_claimed_amount.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_counterclaim_evaluation' . "\n" .
                                  '  ON (d.id=d_cstm_counterclaim_evaluation.model_id AND d_cstm_counterclaim_evaluation.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_COUNTERCLAIM_EVALUATION]) ? $var_ids[DOCUMENT_VAR_COUNTERCLAIM_EVALUATION] : 0) . ') AND (d_cstm_counterclaim_evaluation.lang="" OR d_cstm_counterclaim_evaluation.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_nonmaterial_damages' . "\n" .
                                  '  ON (d.id=d_cstm_nonmaterial_damages.model_id AND d_cstm_nonmaterial_damages.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_NONMATERIAL_DAMAGES]) ? $var_ids[DOCUMENT_VAR_NONMATERIAL_DAMAGES] : 0) . ') AND (d_cstm_nonmaterial_damages.lang="" OR d_cstm_nonmaterial_damages.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_material_damages' . "\n" .
                                  '  ON (d.id=d_cstm_material_damages.model_id AND d_cstm_material_damages.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_MATERIAL_DAMAGES]) ? $var_ids[DOCUMENT_VAR_MATERIAL_DAMAGES] : 0) . ') AND (d_cstm_material_damages.lang="" OR d_cstm_material_damages.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_case_law' . "\n" .
                                  '  ON (d.id=d_cstm_case_law.model_id AND d_cstm_case_law.var_id IN (' . (!empty($var_ids[DOCUMENT_VAR_CASE_LAW]) ? $var_ids[DOCUMENT_VAR_CASE_LAW] : 0) . ') AND (d_cstm_case_law.lang="" OR d_cstm_case_law.lang="' . $registry['lang'] . '"))' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS resp_name' . "\n" .
                                  '  ON (resp_name.parent_id=d_cstm_responsible.value AND resp_name.lang="' . $registry['lang'] . '")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                                  '  ON (d.substatus=ds.id AND ds.lang="' . $registry['lang'] . '")' . "\n";

                $documents = $registry['db']->GetAll(implode("\n", $sql));

                foreach ($documents as $doc) {
                    if (!isset($records[$doc['id']])) {
                        $records[$doc['id']] = $doc;
                        unset($records[$doc['id']]['ptp_participants']);
                        unset($records[$doc['id']]['reg_num']);
                        unset($records[$doc['id']]['car']);
                        unset($records[$doc['id']]['heir']);
                        unset($records[$doc['id']]['ptp_num']);
                        unset($records[$doc['id']]['heir']);
                        unset($records[$doc['id']]['heir_ucn']);
                        unset($records[$doc['id']]['region']);
                        unset($records[$doc['id']]['city']);
                        unset($records[$doc['id']]['extra_info']);
                        $records[$doc['id']]['ptp_participants'] = array();
                        $records[$doc['id']]['type_damage'] = array();
                        $records[$doc['id']]['heirs'] = array();
                        $records[$doc['id']]['location'] = sprintf(
                            $registry['translater']->translate('reports_location_template'),
                            $doc['region'],
                            $doc['city'],
                            $doc['extra_info']
                        );

                        $status_elements = array($registry['translater']->translate('documents_status_' . $doc['status']));
                        $status_elements[] = $doc['substatus'];
                        unset($records[$doc['id']]['substatus']);
                        $status_elements = array_filter($status_elements);
                        $records[$doc['id']]['status'] = implode(', ', $status_elements);
                    }

                    if (!in_array($doc['type_damage'], $records[$doc['id']]['type_damage'])) {
                        $records[$doc['id']]['type_damage'][] = $doc['type_damage'];
                    }
                    if (!isset($records[$doc['id']]['ptp_participants'][$doc['ptp_num']])) {
                        $records[$doc['id']]['ptp_participants'][$doc['ptp_num']] = $doc['ptp_participants'];
                        $records[$doc['id']]['reg_num'][$doc['ptp_num']] = $doc['reg_num'];
                        $records[$doc['id']]['car'][$doc['ptp_num']] = $doc['car'];
                    }

                    // get the heir
                    $heir_name = trim($doc['heir']);
                    $heir_ucn = trim($doc['heir_ucn']);
                    $heir_str = '';
                    if ($heir_name) {
                        $heir_str = $heir_name;
                        if ($heir_ucn) {
                            $heir_str .= ' ';
                        }
                    }
                    if ($heir_ucn) {
                        $heir_str .= sprintf('(%s)', $heir_ucn);
                    }
                    if ($heir_str && !in_array($heir_str, $records[$doc['id']]['heirs'])) {
                        $records[$doc['id']]['heirs'][] = $heir_str;
                    }
                }
            }

            $records['additional_options'] = [
                'total_colspan'                => 26,
                'show_claimed_reserved_amount' => false
            ];
            if (DOCUMENT_VAR_CLAIMED_RESERVED_AMOUNT) {
                $records['additional_options']['total_colspan'] = 27;
                $records['additional_options']['show_claimed_reserved_amount'] = true;
            }

            if (!empty($filters['paginate'])) {
                $results = array($records, $total);
            } else {
                //there is no limit set,
                //get the count from the found records
                $results = $records;
            }

            return $results;
        }
    }
?>
