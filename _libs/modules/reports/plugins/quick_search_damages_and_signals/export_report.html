<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
    <table border="1" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#num#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_income_num#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_damage_num#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_policy_num#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_signal_num#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_client#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_ptp_date#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_reg_num#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_car#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_signal_date_hour#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_type_damage#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_type_risk#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_ptp_location#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_responsible#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_reported_by#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_owner#|escape}</strong></td>
        <td colspan="4" style="text-align: center; vertical-align: middle;"><strong>{#reports_heavy_damage#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_claimed_amount#|escape}</strong></td>
        {if $reports_additional_options.show_claimed_reserved_amount}
          <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_claimed_resserved_amount#|escape}</strong></td>
        {/if}
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_counterclaim_evaluation#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_nonmaterial_damages#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_material_damages#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_case_law#|escape}</strong></td>
        <td rowspan="2" style="text-align: center; vertical-align: middle;"><strong>{#reports_status#|escape}</strong></td>
      </tr>
      <tr>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_incident_description#|escape}</strong></td>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_ptp_participants#|escape}</strong></td>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_heir#|escape}</strong></td>
        <td style="text-align: center; vertical-align: middle;"><strong>{#reports_collateral#|escape}</strong></td>
      </tr>
      {counter start=0 name='item_counter' print=false}
      {foreach from=$reports_results item=result}
        <tr>
          <td align="right" nowrap="nowrap" width="25">
            {counter name='item_counter' print=true}
          </td>
          <td style="mso-number-format:\@">
            {$result.full_num|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.custom_num|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.policy_num|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.signal_num|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.customer_name|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:'dd\.mm\.yyyy';">
            {$result.date|date_format:#date_short#|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {foreach from=$result.reg_num item=reg_num name=rn}
              {$reg_num|escape|default:"&nbsp;"}{if !$smart.foreach.rn.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td style="mso-number-format:\@">
            {foreach from=$result.car item=car name=cr}
              {$car|escape|default:"&nbsp;"}{if !$smart.foreach.cr.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td style="mso-number-format:'dd\.mm\.yyyy, hh\:mm';">
            {$result.signal_datetime|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {foreach from=$result.type_damage item=type_damage name=td}
              {$type_damage|escape|default:"&nbsp;"}{if !$smart.foreach.td.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td style="mso-number-format:\@">
            {$result.type_risk|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.location|escape|nl2br|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.responsible|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.send_signal|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.owner|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.incident_description|escape|nl2br|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {foreach from=$result.ptp_participants item=participant name=ptp}
              {$participant|escape|default:"&nbsp;"}{if !$smart.foreach.ptp.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td style="mso-number-format:\@">
            {foreach from=$result.heirs item=heir name=hr}
              {$heir|escape|default:"&nbsp;"}{if !$smart.foreach.hr.last}<br />{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          </td>
          <td style="mso-number-format:\@">
            {$result.collateral|escape|nl2br|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:'0\.00'">
            {$result.claimed_amount|escape|default:"&nbsp;"}
          </td>
          {if $reports_additional_options.show_claimed_reserved_amount}
            <td style="mso-number-format:'0\.00'">
              {$result.claimed_reserved_amount|escape|default:"&nbsp;"}
            </td>
          {/if}
          <td style="mso-number-format:'0\.00'">
            {$result.counterclaim_evaluation|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:'0\.00'">
            {$result.nonmaterial_damages|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:'0\.00'">
            {$result.material_damages|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:'0\.00'">
            {$result.case_law|escape|default:"&nbsp;"}
          </td>
          <td style="mso-number-format:\@">
            {$result.status|escape|default:"&nbsp;"}
          </td>
        </tr>
      {foreachelse}
        <tr color="FF0000">
          <td colspan="{$reports_additional_options.total_colspan}">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>
