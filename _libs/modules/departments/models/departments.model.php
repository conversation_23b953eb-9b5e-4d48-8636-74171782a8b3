<?php

require_once 'departments.validator.php';

/**
 * Departments model class
 */
Class Department extends Model {
    public $modelName = 'Department';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
    }

    /**
     * Checks the validity of the model
     *
     * @param $action string - action to check model for
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        if (!$this->get('name')) {
            $this->raiseError('error_no_name_specified', 'name');
        }

        //the ancestor of the department cannot be this department itself
        if ($this->get('ancestor') == $this->get('id')) {
            $this->raiseError('error_invalid_ancestor_self', 'ancestor');
            return $this->valid;
        }

        //check for recursion
        //the ancestor of a department cannot be some of its descendants
        if ($this->get('id')) {
            $department_descendants = Departments::getTreeDescendants($this->registry,  array('where' => array('d1.id = ' . $this->get('id'))));
            if (array_key_exists($this->get('ancestor'), $department_descendants)) {
                $this->raiseError('error_invalid_ancestor_recursion', 'ancestor');
            }
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                //normalize left/right position of tree nodes
                Departments::rebuildTree($this->registry);
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_DEPARTMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new department base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_DEPARTMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        //start transaction
        $db->StartTrans();
        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }


    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {

        $set = array();
        $set['ancestor']    = sprintf("ancestor=%d", $this->get('ancestor'));
        $set['modified']    = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('manager')) {
            $set['manager'] = sprintf("manager=%d", $this->get('manager'));
        }
        if ($this->isDefined('position')) {
            $set['position'] = sprintf("`position`=%d", $this->get('position'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->isDefined('group')){
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        $update['name'] = sprintf("name='%s'", $this->get('name'));
        $update['description']  = sprintf("description='%s'", $this->get('description'));

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        //query to insert/update the i18n table for the selected model language
        $query2 = 'INSERT INTO ' . DB_TABLE_DEPARTMENTS_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * Get all users that are members of the department and its subdepartments
     *
     * @return array $users - all users found
     */
    public function getUsers() {

        //get descendants
        $descendants = Departments::getTreeDescendants($this->registry, array('where' => array('d1.id = ' . $this->get('id'))));
        $descendant_ids = array_keys($descendants);

        //select clause
        $sql['select'] = 'SELECT DISTINCT(u.id), ui18n.firstname, ui18n.lastname ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_USERS_DEPARTMENTS . ' AS ug ' . "\n" .
                       'INNER JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                       '  ON (u.id=ug.parent_id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                       '  ON (u.id=ui18n.parent_id AND ui18n.lang="' . $this->registry['lang'] . '")'. "\n";

        $sql['where'] = 'WHERE ' . General::buildClause('ug.department_id', $descendant_ids) . "\n" .
                        ' AND u.active=1 AND u.deleted=0';

        $sql['order'] = 'ORDER BY ui18n.firstname ASC, ui18n.lastname ASC' . "\n";

        $query = implode("\n", $sql);
        $users = $this->registry['db']->GetAll($query);

        $this->set('users', $users, true);

        return $users;
    }
}

?>
