<h1>{$title}</h1>

<table border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td class="pagemenu">
{capture assign='link'}{$smarty.server.PHP_SELF}?{$module_param}=documents&amp;controller=pricelists&amp;pricelists=list&amp;page={/capture}
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
  hide_stats=1
}
    </td>
  </tr>
  <tr>
    <td id="form_container">
      {include file=`$theme->templatesDir`actions_box.html}
      <form name="nomenclatures_pricelists" action="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;controller=pricelists" method="post" enctype="multipart/form-data">
      <table border="0" cellpadding="0" cellspacing="0" class="t_table t_list">
        <tr>
          <td class="t_caption t_border t_checkall">
{include file="`$theme->templatesDir`_select_items.html"
  pages=$pagination.pages
  total=$pagination.total
  session_param=$session_param|default:$pagination.session_param
}
          </td>
          <td class="t_caption t_border" nowrap="nowrap" width="15"><div class="t_caption_title">{#num#|escape}</div></td>
          <td class="t_caption t_border {$sort.name.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.name.link}">{#nomenclatures_pricelists_name#|escape}</div></td>
          <td class="t_caption t_border {$sort.delivery_code.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.delivery_code.link}">{#nomenclatures_pricelists_description#|escape}</div></td>
          <td class="t_caption t_border {$sort.count_articles.class}" nowrap="nowrap"><div class="t_caption_title" onclick="{$sort.count_articles.link}">{#nomenclatures_articles#|escape}</div></td>
          <td class="t_caption" width="80">&nbsp;</td>
        </tr>
      {counter start=$pagination.start name='item_counter' print=false}
      {foreach name='i' from=$nomenclatures_pricelists item='nomenclatures_pricelists'}
      {strip}
      {capture assign='info'}
        <strong>{#added_by#|escape}:</strong> {$nomenclatures_pricelists->get('added_by_name')|escape}<br />
        <strong>{#nomenclatures_pricelists#|escape}:</strong> {$nomenclatures_pricelists->get('name')|escape}<br />
        <strong>{#added#|escape}:</strong> {$nomenclatures_pricelists->get('added')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclatures_pricelists->get('added_by_name')|escape}<br />
        <strong>{#modified#|escape}:</strong> {$nomenclatures_pricelists->get('modified')|date_format:#date_mid#|escape} {#by#|escape} {$nomenclatures_pricelists->get('modified_by_name')|escape}<br />
        {if $nomenclatures_pricelists->isDeleted()}<strong>{#deleted#|escape}:</strong> {$nomenclatures_pricelists->get('deleted')|date_format:#date_mid#|escape}{if $nomenclatures_pricelists->get('deleted_by_name')} {#by#|escape} {$nomenclatures_pricelists->get('deleted_by_name')|escape}{/if}<br />{/if}

        <strong>{#translations#|escape}:</strong>
          <span class="translations">
          {foreach from=$nomenclatures_pricelists->get('translations') item='trans'}
            <img src="{$theme->imagesUrl}flags/{$trans}.png" alt="{$trans}" title="{$trans}" border="0" align="absmiddle"{if $trans eq $nomenclatures_pricelists->get('model_lang')} class="selected"{/if} />
          {/foreach}
          </span>
      {/capture}
      {/strip}
      {if !$nomenclatures_pricelists->checkPermissions('list')}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="t_border dimmed"><input type="checkbox" name="items[]" value="{$nomenclatures_pricelists->get('id')}" title="{#check_to_include#|escape}" disabled="disabled" /></td>
          <td class="t_border hright dimmed" nowrap="nowrap">{counter name='item_counter' print=true}</td>
          <td colspan="3" class="t_border dimmed">{#error_right_notallowed#|escape}</td>
          <td class="hcenter">
            {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclatures_pricelists disabled='edit,delete,view'}
          </td>
        </tr>
        {else}
        <tr class="{cycle values='t_odd,t_even'}{if !$nomenclatures_pricelists->get('active')} t_inactive{/if}{if $nomenclatures_pricelists->get('deleted_by')} t_deleted{/if}">
          <td class="t_border">
            <input onclick="sendIds(params = {ldelim}
                                            the_element: this,
                                            module: '{$module}',
                                            controller: '{$controller}',
                                            action: '{$action}',
                                            session_param: '{$session_param|default:$pagination.session_param}',
                                            total: {$pagination.total}
                                           {rdelim});" 
                   type="checkbox"
                   name='items[]'
                   value="{$nomenclatures_pricelists->get('id')}"
                   title="{#check_to_include#|escape}"
                   {if @in_array($nomenclatures_pricelists->get('id'), $selected_items.ids) || 
                       (@$selected_items.select_all eq 1 && @!in_array($nomenclatures_pricelists->get('id'), $selected_items.ignore_ids))}
                     checked="checked"
                   {/if} />
          </td>
          <td class="t_border hright">{counter name='item_counter' print=true}</td>
          <td class="t_border {$sort.name.isSorted}"><a href="{$smarty.server.SCRIPT_NAME}?{$module_param}={$module}&amp;controller=pricelists&amp;{$action_param}=view&amp;view={$nomenclatures_pricelists->get('id')}">{$nomenclatures_pricelists->get('name')|escape|default:'&nbsp;'}</a></td>
          <td class="t_border {$sort.currency.isSorted}">{$nomenclatures_pricelists->get('description')|mb_truncate:60:true:'...'|escape|default:'&nbsp;'}</td>
          <td class="t_border hright {$sort.count_articles.isSorted}">
            {$nomenclatures_pricelists->get('count_articles')|default:'0'}
            <a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;controller=pricelists&amp;pricelists=articles&amp;articles={$nomenclatures_pricelists->get('id')}"><img src="{$theme->imagesUrl}articles.png" width="16" height="16" border="0" alt="{#view#|escape}" /></a>
          </td>
          <td class="hcenter" nowrap="nowrap">
            {include file=`$theme->templatesDir`single_actions_list.html object=$nomenclatures_pricelists}
          </td>
        </tr>
        {/if}
      {foreachelse}
        <tr class="{cycle values='t_odd,t_even'}">
          <td class="error" colspan="6">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
        <tr>
          <td class="t_footer" colspan="6"></td>
        </tr>
      </table>
      <br />
      <br />
      {include file=`$theme->templatesDir`multiple_actions_list.html exclude='multiedit' session_param=$session_param|default:$pagination.session_param}
      </form>

    </td>
  </tr>
  <tr>
    <td class="pagemenu">
{include file="`$theme->templatesDir`pagination.html"
  found=$pagination.found
  total=$pagination.total
  rpp=$pagination.rpp
  page=$pagination.page
  pages=$pagination.pages
  link=$link
}
    </td>
  </tr>
</table>
