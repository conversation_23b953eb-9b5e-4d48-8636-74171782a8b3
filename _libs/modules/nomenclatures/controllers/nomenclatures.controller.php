<?php

require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.audit.php';

class Nomenclatures_Controller extends Controller {
    use \Nzoom\Mvc\ControllerTrait\GridBasedListTrait;

    public $defaultAction = 'index';

    /**
     * The fields which will switch the additional variables in the search panel
     */
    public static $searchAdditionalVarsSwitch = 'n.type';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit',
        'clone', 'export',
        'create', 'tag', 'remind', 'manage_outlooks', 'distribute', 'history',
        'emails', 'minitasks', 'communications', 'comments', 'printlist'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'history', 'communications'
    );

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'view',
        'edit', 'translate', 'history', 'distribute'
    );

    /**
     * Action definitions for the upper right menu
     */
    public $actionDefinitionsUpRight = array(
        'manage_outlooks', 'printlist'
    );

    public $actionListPageMenu = [
        /** Actions that serve navigational purpouse and have no direct relation on the current page */
        'general' => ['list', 'add'],

        /** Actions that are conextualy dependent on the page (document opened) */
        'context' => ['view', 'edit', 'tag'/*, 'setstatus'*/, 'extract', 'communications', 'relatives', 'history' ],

        /** Actions that should be quick to access but unabtrusive to the user */
        'quick' => ['print'],

        /** Actions that are not very frequent to use, can be hidden behind a menu */
        'infriquent' => ['manage_outlooks', 'printlist'],
    ];

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_select',
    );

    /**
     * Actions where side panels for model can be displayed
     */
    public static $actionsSidePanel = array('view', 'edit');

    /**
     * generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'clone':
            $this->_clone();
            break;
        case 'create':
            $this->_create();
            break;
        case 'history':
            $this->_history();
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
        case 'ajaxdelfile':
            $this->_manageFile();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'addquick':
            $this->_addQuick();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'franky':
            $this->_franky();
            break;
        case 'savegroupvar':
            $this->_saveGroupVar();
            break;
        case 'ajax_import_table':
            $this->_importTable();
            break;
        case 'ajax_import_table_configurator':
            $this->_importTableConfigurator();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'purge':
            $this->_purge($this->registry['request'][$this->action]);
            break;
        case 'ajax_select':
            $this->_select();
            break;
        case 'filter':
            $this->_filter();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'calculate':
            $this->_calculate();
            break;
        case 'getadvancedsearchoptions':
            $this->_getAdvancedSearchOptions();
            break;
        case 'getListColumnsDefinitions':
            $this->_getListColumnsDefinitions();
            break;
        case 'getListMultiActionsPanel':
            $this->_getListMultiActionsPanel('multiedit', 'tags');
            break;
        case 'getListActions':
            $this->_getListActions();
            break;
        case 'getListTitle':
            $this->_getListTitle();
            break;
        case 'listData':
            $this->_listData();
            break;
        case 'saveFilter':
            $this->_saveFilter();
            break;
        case 'loadFilter':
            $this->_loadFilter();
            break;
        case 'deleteFilter':
            $this->_deleteFilter();
            break;
        case 'list':
            $this->_list();
            break;
        case 'search':
            $this->_search();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'distribute':
            $this->_distribute();
            break;
        case 'tag':
            $this->_tag();
            break;
        case 'multitag':
            $this->_multiTag();
            break;
        case 'multiremovetag':
            $this->_multiRemoveTag();
            break;
        case 'ajax_tag':
            $this->_getTags();
            break;
        case 'remind':
            $this->_remind();
            break;
        case 'ajax_sidepanel':
            $this->_sidePanel();
            break;
        case 'attach_additional_field_file':
            $this->_attachAdditionalFieldFile();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'bb':
            $this->_bb();
            break;
        case 'index':
        default:
            if ($this->registry['request']->get('type')) {
                require_once $this->modelsDir . 'nomenclatures.types.factory.php';
                $type = Nomenclatures_Types::search($this->registry,
                    array('where' => array('nt.id=' . $this->registry['request']->get('type')),
                          'sanitize' => true));
                if ($type) {
                    $this->setAction('list');
                    $this->_list();
                } else {
                    $this->redirect($this->module, 'index');
                }
                break;
            } elseif ($this->registry['request']->get('type_section')) {
                require_once $this->modelsDir . 'nomenclatures.sections.factory.php';
                $type_section = Nomenclatures_Sections::search($this->registry,
                    array('where' => array('ns.id=' . $this->registry['request']->get('type_section')),
                          'sanitize' => true));
                if ($type_section) {
                    $this->setAction('list');
                    $this->_list();
                } else {
                    $this->redirect($this->module, 'index');
                }
                break;
            }
            $this->setAction('index');
            $this->_index();
        }
    }

    /**
     * Frontend home page
     */
    public function _index() {
        //everything is in the viewer
        return true;
    }


    /**
     * listing of all models
     */
    private function _listData() {
        /** @var Request $request */
        $request = $this->registry['request'];

        $accept = $request->getHeader('Accept');
        if (!preg_match("/.*json/i", $accept)
            || false !== stripos($accept, "html")) {
            return;
        }

        // The rights are cheked based on the action name. Documents::prepareRightsFilters() is called in the model
        $this->registry->set('action', 'list', true);

        $factoryName = $this->modelFactoryName;

        $filters = $this->prepFiltersFromRequest();
        $this->registry->set('getTags', true, true);

        $shouldCheckPermissions = [
            'view' => true,
            'edit' => true,
        ];

        $outlook = $this->getCurrentOutlook($filters);

        if (isset($outlook) && $outlook) {
            $modelFields = $outlook->get('current_custom_fields');
            $modelFieldsNames = array_column($modelFields, 'name');
            $filters['get_fields'] = $modelFieldsNames;

            if (in_array('tags', $modelFieldsNames)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }
        }

        list($models, $pagination) = $factoryName::pagedSearch($this->registry, $filters);


        if (isset($outlook) && $outlook) {
            $additionalVars = $outlook->getModelAdditionalFields();
            $basicVars = $outlook->getModelFields();
            if ($additionalVars) {
                $outlook->clearNotPermittedVars();
            }
            $modelFieldsWithPermissions = [
                'comments',
                'emails',
            ];
            foreach ($models as $model) {
                $this->prepListRecordFileuploadAttributes($model, $additionalVars);

                if (in_array('categories', $modelFieldsNames) || in_array('categories_names', $modelFieldsNames)) {
                    $model->getCategoryNames();
                }

                if (in_array('tags', $modelFieldsNames)) {
                    $shouldCheckPermissions['tags_view'] = true;
                    $shouldCheckPermissions['tags_edit'] = true;
                }

                foreach ($modelFieldsWithPermissions as $modelFieldWithPermissions) {
                    if (in_array($modelFieldWithPermissions, $modelFieldsNames)) {
                        $shouldCheckPermissions[$modelFieldWithPermissions] = true;
                    }
                }

                $model->sanitize();
                $model->properties['cached_assoc_vars'] = null;

                unset($model->properties['cached_assoc_vars']);
            }
        }

        if (in_array(true, $shouldCheckPermissions, true)) {
            foreach ($models as $model) {
                $rightsProper = [];
                foreach ($shouldCheckPermissions as $action => $test) {
                    if (!$test) {
                        continue;
                    }
                    $rightsProper[$action] = $model->checkPermissions($action);
                }
                $model->rights = array_merge($model->rights, $rightsProper);
                $model->set('type_rights', array_merge(($model->get('type_rights')?:[]), $rightsProper), true);
            }
        }

        $data = [
            'pagination' => $pagination,
            'records' => $models,
        ];

        $this->actionCompleted = true;
        $this->registry->set('ajax_result', json_encode($data), true);
    }

    /**
     * listing of all models
     */
    private function _list() {
        if ($this->registry['theme']->isModern()) {
            // Redirect to a regular search if section_type link.
            $this->redirectTypeSection2ListForModernTheme();

            $this->viewer = $this->getViewer();
            $this->viewer->setTemplate('list.html');
            $this->viewer->data['db_col_alias'] = Nomenclatures::getAlias($this->module, $this->controller);
            /** @var Request $request */
            $request = $this->registry['request'];

            if (!$request->getGet('key')) {
                $request->setGet('key', '');
            }

            $url = $request->getBaseUrl(
                "{$request->getServer()['SCRIPT_NAME']}?" .
                Router::MODULE_PARAM . "={$this->module}&controller={$this->controller}&"
            );
            if ($request->getGet('type')) {
                $url .= "type={$request->getGet('type')}&";
            }
            $url .= "lang={$this->registry->get('lang')}&model_lang={$this->registry->get('lang')}&{$this->controller}=";

            $this->viewer->data['listData'] = $url . 'listData';
            $this->viewer->data['columnsDefinitions'] = $url . 'getListColumnsDefinitions&use_ajax=1';

            $rights = $this->registry['currentUser']->getRights();
            $this->viewer->data['manageOutlooksFeature'] = $rights[$this->module]['manage_outlooks'] != "none" ? '1' : '0';
            $this->viewer->data['showSearch'] = $rights[$this->module]['search'] != "none" ? '1' : '0';
        }
        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        $this->redirectSearch2ListForModernTheme();
        //all the actions are within the viewer
        return true;
    }

    /**
     * Selects certain items by specified parameter.
     * This method prints unordered list and exists
     */
    public function _select($autocomplete = array()) {

        $request = &$this->registry['request'];

        // get/set fields to search by
        $search_fields = array();
        if (!$search_fields = $request->get('search')) {
            $search_fields = array('<code>', '<name>');
        }
        if (!is_array($search_fields)) {
            $search_fields = array($search_fields);
        }
        $i18n_columns = array_keys($this->registry['db']->MetaColumns(DB_TABLE_NOMENCLATURES_I18N, false));
        $main_alias = Nomenclatures::getAlias('nomenclatures', 'nomenclatures');
        foreach ($search_fields as $idx => $field) {
            $field = preg_replace('#<|>#', '', $field);
            switch ($field) {
                case 'id':
                    if ($request->get('filters') && array_key_exists('<customer_trademark>', $request->get('filters'))) {
                        $search_fields[$idx] = 'ct.id';
                    } else {
                        $search_fields[$idx] = $main_alias . '.id';
                    }
                    break;
                case 'customer_name':
                    $search_fields[$idx] = 'CONCAT(ci18n.name, \' \', ci18n.lastname)';
                    break;
                default:
                    if (preg_match('#^a__*#', $field)) {
                        //search by additional variable
                        $search_fields[$idx] = $field;
                    } else {
                        //search by main field
                        $alias = $main_alias;
                        if (in_array(strtoupper($field), $i18n_columns)) {
                            //search by main field in i18n table
                            $alias .= 'i18n';
                        }
                        $search_fields[$idx] = $alias . '.' . $field;
                    }
                    break;
            }
        }

        //prepare sort if is requested
        $sort = array();
        if (!$r_sort = $request->get('sort')) {
            $r_sort = array('<name>', '<code>');
        }
        foreach ($r_sort as $key => $field) {
            preg_match('#<([^>]*)>(\s+(ASC|DESC))?#i', $field, $sort_matches);
            if (!empty($sort_matches[1])) {
                //$order: ASC/DESC
                $order = (!empty($sort_matches[3]) ? $sort_matches[3] : 'ASC');
                if ($sort_matches[1] == 'customer_name') {
                    //sort by customer name
                    $sort[] = 'CONCAT(ci18n.name, \' \', ci18n.lastname) ' . $order;
                } elseif (preg_match('#^a__*#', $sort_matches[1])) {
                    //sort by additional variable
                    $sort[] =  $sort_matches[1] . ' ' . $order;
                } else {
                    //sort by main field
                    $alias = $main_alias;
                    if (in_array(strtoupper($sort_matches[1]), $i18n_columns)) {
                        //sort by main field in i18n table
                        $alias .= 'i18n';
                    }
                    $sort[] = $alias . '.' . $sort_matches[1] . ' ' . $order;
                }
            }
        }

        $additional_where = array();
        if ($req_filters = $request->get('filters')) {
            foreach ($req_filters as $filter => $value) {
                $alias = $main_alias . '.';
                $field = preg_replace('#<|>#', '', $filter);
                // escape value for the SQL search query
                $value = General::slashesEscape($value);

                switch ($filter) {
                case '<tag>':
                    $alias = 'tags.';
                    $field = 'tag_id';
                    break;
                case '<category>':
                    $alias = 'nc.';
                    $field = 'cat_id';
                    break;
                case '<type_keyword>':
                    require_once $this->modelsDir . 'nomenclatures.types.factory.php';
                    $filters = array('where' => array("keyword = '$value'"),
                                     'sanitize' => true);
                    $nom_type = Nomenclatures_Types::searchOne($this->registry, $filters);

                    $field = 'type';
                    $value = $nom_type->get('id');
                    break;
                case '<customer_trademark>':
                    $additional_where['customer_trademark'] = 1;
                    continue 2;
                    break;
                case '<customer_type>':
                    $alias = 'c.';
                    $field = 'type';
                    break;
                case '<customer>':
                    $alias = 'ct.';
                    $field = 'parent_id';
                    break;
                case '<get_available_quantities>':
                    $additional_where[] = 'get_available_quantities = \'' . $value . '\'';
                    continue 2;
                    /*if (isset($req_filters['<warehouse>'])) {
                        continue 2;
                    } else {
                        $alias = 'nq.';
                        $field = 'warehouse_id';
                    }*/
                    break;
                case '<require_warehouse>':
                    $additional_where[] = 'require_warehouse = \'' . $value . '\'';
                    continue 2;
                    break;
                case '<get_all_quantities>':
                    $additional_where[] = 'get_all_quantities = \'' . $value . '\'';
                    continue 2;
                    break;
                case '<quantity>':
                    $alias = 'nq.';
                    $field = 'quantity';
                    break;
                case '<warehouse>':
                    $alias = 'nq.';
                    $field = 'warehouse_id';
                    if (preg_match('#(\d+)_(\d+)_(cash|bank|cheque)_(\d+)#', $value, $matches)) {
                        // we have to get all warehouses for the company and office provided
                        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.dropdown.php';
                        $params = array(
                            $this->registry,
                            'company' => $matches[1],
                            'office' => $matches[2],
                        );
                        $wh = Nomenclatures_Dropdown::getWarehouses($params);
                        if (empty($wh)) {
                            //just in case
                            $wh = array(0);
                        } else {
                            foreach ($wh as $k => $v) {
                                $wh[$k] = $v['option_value'];
                            }
                        }
                        // prepare value as it is expected several rows down -
                        // "elseif" statement after the end of the "switch"
                        $cmp = 'IN';
                        if (preg_match('#!=|(not\s+in)#i', $value)) {
                            $cmp = 'NOT IN';
                        }
                        $value = $cmp . ' (' . implode(', ', $wh) . ')';
                    } elseif (preg_match('#\d+_\d+_\d+#', $value)) {
                        // if value is taken from warehouse_data field, extract the warehouse id
                        $value = preg_replace('#\d+_\d+_(\d+)#', '$1', $value);
                    }
                    break;
                default:
                    if (preg_match('#^a__*#', $field)) {
                        //search by additional variable
                        $alias = '';
                    } elseif (in_array(strtoupper($field), $i18n_columns)) {
                        //search by main field in i18n table
                        $alias = $main_alias . 'i18n.';
                    }
                    break;
                }

                if (preg_match('#^\s*(!?=|<=?|>=?)(.*)#', $value, $matches)) {
                    // search expression for a single value
                    $additional_where[] =
                        sprintf('%s%s %s \'%s\' AND',
                                $alias, $field, trim($matches[1]), trim($matches[2]));
                    continue;
                } elseif (preg_match('#^\s*((not\s+)?in\s*)\((.*)\)\s*#i', $value, $matches)) {
                    // search expression for multiple values
                    $negative_search = preg_match('#not#i', $matches[1]);
                    $compare_operator = $negative_search ? '!=' : '=';
                    $amatches = preg_split('#\s*,\s*#', trim($matches[3]));
                    $count_or_clauses = count($amatches);
                    foreach ($amatches as $idx => $amatch) {
                        $logical_operator = $negative_search || $idx == ($count_or_clauses - 1) ? 'AND' : 'OR';
                        $additional_where[] =
                            sprintf('%s%s %s \'%s\' %s',
                                    $alias, $field, $compare_operator, $amatch, $logical_operator);
                    }
                    continue;
                }

                $vals = preg_split('#\s*,\s*#', $value);
                if (count($vals) > 1) {
                    $count_or_clauses = count($vals);
                    foreach ($vals as $idx => $val) {
                        $clause = $alias . $field . ' = \'' . $val . '\'';
                        if ($idx < $count_or_clauses - 1) {
                            $clause .= ' OR';
                        } else {
                            $clause .= ' AND';
                        }
                        $additional_where[] = $clause;
                    }
                } else {
                    $additional_where[] = $alias . $field . ' = \'' . $vals[0] . '\' AND';
                }
            }
        }

        //prepare suggestions format
        if (!$suggestions_format = $request->get('suggestions')) {
            $suggestions_format = '[<code>] <name>';
        }

        $s_field = $request->get('field');
        //prepare fill option definitions
        if (!$fill_options = $request->get('fill_options')) {
            //we must be in the basic vars
            //so get the autocomplete field
            $fill_options = array(
                '$' . $s_field . ' => [<code>] <name>',
                '$' . preg_replace('#_autocomplete$#', '', $s_field). '_oldvalue' . ' => [<code>] <name>');
            if (preg_match('#_autocomplete$#', $s_field)) {
                $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field) . ' => <id>';
            }
        } else {
            $fill_oldvalue = '[<code>] <name>';
            foreach ($fill_options as $fill_option) {
                if (preg_match('#^\$' . $s_field . '\s*=\>#', $fill_option)) {
                    @list($notimportant, $fill_oldvalue) = preg_split('#\s*=>\s*#', $fill_option);
                }
            }
            $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field). '_oldvalue' . ' => ' . $fill_oldvalue;
        }
        //get subtype
        //we need this to decide if we can put negative price
        //usually for system nomenclatures
        $fill_options[] = '$for_system_use' . ' => <subtype>';

        if ($request->get('currency')) {
            $additional_where[] = 'currency = \'' . $request->get('currency') . '\'';
            $autocomplete['currency'] = $request->get('currency');
        }
        $autocomplete = array('search' => $search_fields,
                              'sort' => $sort,
                              'suggestions_format' => $suggestions_format,
                              'fill_options' => $fill_options,
                              'additional_where' => $additional_where,
                              'type' => 'nomenclatures'
                             );
        if ($request->get('autocomplete_filter')) {
            $filters = parent::_select($autocomplete);
            return $filters;
        }

        parent::_select($autocomplete);

        exit;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        require_once $this->modelsDir . 'nomenclatures.types.factory.php';

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if (!empty($type_id)) {
            $filters = array('where' => array('nt.id = ' . $type_id,
                                              'nt.active = 1'),
                             'sanitize' => true);
            $type = Nomenclatures_Types::searchOne($this->registry, $filters);
        }

        $type_permission = $type ? $this->checkActionPermissions($this->module . $type->get('id'), 'add') : false;

        if (!$type || !$type_permission) {
            //invalid type, redirect to list
            $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('nomenclature');
            $this->registry['messages']->setError($this->i18n('error_nomenclatures_add_failed', array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        }

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclature = Nomenclatures::buildModel($this->registry);
            $nomenclature->set('type_name', $type->get('name'), true);

            //sets the property of the type to the current model
            $old_nomenclature = clone $nomenclature;
            $this->registry->set('get_old_vars', true, true);
            $old_nomenclature->getVars();
            $trans = $this->checkTransition($old_nomenclature);
            $this->old_model = $old_nomenclature->sanitize();

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $nomenclature->unsetVars();
            $nomenclature->getVars();

            if ($trans && $nomenclature->save()) {
                $filters = array('where' => array('n.id = ' . $nomenclature->get('id')),
                                 'model_lang' => $nomenclature->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_nomenclature->getVars();

                Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'add', 'new_model' => $new_nomenclature, 'old_model' => $this->old_model));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_add_success', array($new_nomenclature->getModelTypeName())), '', -1);

                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_add_failed', array($nomenclature->getModelTypeName())), '', -1);
            }
        }

        if (!$this->actionCompleted) {
            //create an empty nomenclature model
            $nomenclature = Nomenclatures::buildModel($this->registry);
            if (!$nomenclature->get('model_id')) {
                $nomenclature->set('model_id', time(), true);
            }

            if (!$type) {
                //show error no such type
                $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature_type'));
                $this->registry['messages']->insertInSession($this->registry);
                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            } else {
                //set default values from the nomenclature type
                if (!$request->isPost()) {
                    $nomenclature->set('group', $type->getDefaultGroup(), true);
                    if (!$nomenclature->get('subtype')) {
                        $nomenclature->set('subtype', $type->get('subtype'), true);
                    }
                    $nomenclature->set('has_batch', $type->get('default_has_batch'), true);
                    $nomenclature->set('has_expire', $type->get('default_has_expire'), true);
                    $nomenclature->set('has_serial', $type->get('default_has_serial'), true);
                    $nomenclature->set('has_batch_code', $type->get('default_has_batch_code'), true);
                }
            }

            //prepare additional variables
            $nomenclature->getVarsForTemplate(false);
            $nomenclature->prepareBBVarsForTemplate('request');

            $added_files = array(0 => array());
            $this->registry['added_files'] = $added_files;
        }

        if (!empty($nomenclature)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('nomenclature', $nomenclature->sanitize());
        }

        return true;
    }

    /**
     * add a single model
     */
    private function _addQuick() {
        $request = &$this->registry['request'];

        $autocomplete_params = array();
        if ($request->get('autocomplete_filter')) {
            if ($request->get('addquick_type')) {
                $autocomplete_params['addquick_type'] = $request->get('addquick_type');
            }
        } else if ($this->registry['session']->get($request['uniqid'], 'autocomplete_params')) {
            $autocomplete_params = $this->registry['session']->get($request['uniqid'], 'autocomplete_params');
        } else if ($request->get('autocomplete_params')) {
            $autocomplete_params = json_decode($request->get('autocomplete_params'), true);
        }

        require_once $this->modelsDir . 'nomenclatures.types.factory.php';

        $filters = array('where' => array('nt.active=1'),
                         'sort' => array('nti18n.name'),
                         'sanitize' => true);
        if (isset($autocomplete_params['addquick_type'])) {
            $filters['where'][] = 'nt.id IN (' . implode(',', $autocomplete_params['addquick_type']) . ')';
        }
        $nomenclaturesTypes = Nomenclatures_Types::search($this->registry, $filters);

        foreach ($nomenclaturesTypes as $key => $nom_type) {
            if (! $this->checkActionPermissions($this->module . $nom_type->get('id'), 'add')) {
                unset($nomenclaturesTypes[$key]);
            }
        }

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if ($type_id) {
            foreach ($nomenclaturesTypes as $key => $nom_type) {
                if ($key == 0) {
                    $type = $nom_type;
                }
                if ($nom_type->get('id') == $type_id) {
                    $type = $nom_type;
                }
            }
        } else {
            $type = reset($nomenclaturesTypes);
        }
        $this->registry->set('current_type', $type, true);
        $this->registry->set('nomenclatures_types', $nomenclaturesTypes, true);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclature = Nomenclatures::buildModel($this->registry);
            $nomenclature->set('type_name', ($type ? $type->get('name') : ''), true);

            //sets the property of the type to the current model
            $old_nomenclature = clone $nomenclature;
            $this->registry->set('get_old_vars', true, true);
            $old_nomenclature->getVars();
            $this->old_model = $old_nomenclature->sanitize();

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $nomenclature->unsetVars();
            $nomenclature->getVars();

            if ($nomenclature->save()) {
                $filters = array('where' => array('n.id = ' . $nomenclature->get('id')),
                                 'model_lang' => $nomenclature->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                $new_nomenclature->getVars();

                Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'add', 'new_model' => $new_nomenclature, 'old_model' => $this->old_model));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_add_success', array($new_nomenclature->getModelTypeName())), '', -1);

                $this->registry['session']->remove($request['uniqid'], 'autocomplete_params');

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_add_failed', array($nomenclature->getModelTypeName())), '', -1);
            }
        }

        if (!$this->actionCompleted) {
            //create an empty nomenclature model
            if (!$request->isPost()) {
                $nomenclature = Nomenclatures::buildModel($this->registry);
                if (!$nomenclature->get('model_id')) {
                    $nomenclature->set('model_id', time(), true);
                }
            }

            if ($type) {
                //set default values from the nomenclature type
                if (!$request->isPost()) {
                    $nomenclature->set('group', $type->getDefaultGroup(), true);
                    $nomenclature->set('subtype', $type->get('subtype'), true);
                    $nomenclature->set('has_batch', $type->get('default_has_batch'), true);
                    $nomenclature->set('has_expire', $type->get('default_has_expire'), true);
                    $nomenclature->set('has_serial', $type->get('default_has_serial'), true);
                    $nomenclature->set('has_batch_code', $type->get('default_has_batch_code'), true);
                }

                $nomenclature->set('type', $type->get('id'), true);
                $nomenclature->set('type_name', $type->get('name'), true);

                //prepare additional variables
                $nomenclature->getVarsForTemplate(false);
                $nomenclature->prepareBBVarsForTemplate('request');
            }

            $added_files = array(0 => array());
            $this->registry['added_files'] = $added_files;
        }

        if (!empty($nomenclature)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('nomenclature', $nomenclature->sanitize());
        }

        return true;
    }

    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('edit');

        $added_files = array(0 => array());

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclature = Nomenclatures::buildModel($this->registry);

            $filters = array('where' => array('n.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_nomenclature->getVars();

            $this->checkAccessOwnership($old_nomenclature, true, 'edit');

            $trans = $this->checkTransition($old_nomenclature);
            $this->old_model = $old_nomenclature->sanitize();
            $nomenclature->set('type_name', $old_nomenclature->get('type_name'), true);

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $nomenclature->unsetVars();
            $nomenclature->getVars();

            if ($trans && $nomenclature->save()) {
                $this->old_model = clone $old_nomenclature;
                $this->old_model->sanitize();

                $filters = array('where' => array('n.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_nomenclature->getVars();

                $audit_parent = Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'edit', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));

                //show message 'message_nomenclatures_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_edit_success', array($new_nomenclature->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_edit_failed', array($old_nomenclature->getModelTypeName())), '', -1);
            }
        }

        if (!$this->actionCompleted) {
            // the model from the DB
            $filters = array('where' => array('n.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);

            if ($nomenclature) {
                //check access and ownership of the model
                $this->checkAccessOwnership($nomenclature, true, 'edit');

                //prepare additional variables
                $nomenclature->getVarsForTemplate(false);
                $mode = ($request->isPost()) ? 'request' : 'database';
                $nomenclature->prepareBBVarsForTemplate($mode);
                $nomenclature->checkBatchDataEdit();

                //get attachments
                $this->registry['added_files'] = $added_files;
                $nomenclature->getAttachments();
            }
        }

        if (!empty($nomenclature)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclature')) {
                $this->registry->set('nomenclature', $nomenclature->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model from portal users
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('n.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
        if (!empty($nomenclature)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclature);

            //get additional variables
            $nomenclature->getVarsForTemplate(false);
            $nomenclature->prepareBBVarsForTemplate();

            $nomenclature->getAttachments();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclature')) {
                $this->registry->set('nomenclature', $nomenclature->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        //$id = $request->get($this->action);
        $id = $request->get('translate');

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclature = Nomenclatures::buildModel($this->registry);

            $filters = array('where' => array('n.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_nomenclature->getVars();
            $this->old_model = $old_nomenclature->sanitize();
            $nomenclature->set('type_name', $old_nomenclature->get('type_name'), true);

            //get post vars
            $this->registry->set('get_old_vars', false, true);
            $nomenclature->unsetVars();
            $nomenclature->getVars();

            if ($nomenclature->save()) {
                $filters = array('where' => array('n.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                $new_nomenclature->getVars();

                $audit_parent = Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'translate', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_translate_success', array($new_nomenclature->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_translate_failed', array($old_nomenclature->getModelTypeName())), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('n.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);

            if ($nomenclature) {
                //check access and ownership of the model
                $this->checkAccessOwnership($nomenclature);
            }
        }

        if (!empty($nomenclature)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('nomenclature', $nomenclature->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Clone nomenclature
     */
    private function _clone() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('n.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $nomenclature = Nomenclatures::searchOne($this->registry, $filters);

        if (empty($nomenclature)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        } else {
            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclature);

            //clone
            $nomenclature->getVars();

            //the source model vars are stored in orig_vars,
            //to be used later when copying the additional vars
            $nomenclature->set('orig_vars', $nomenclature->get('vars'), true);

            //clear the model additional vars, because they are copied later
            $nomenclature->unsetVars();

            //current model lang
            $model_lang = $nomenclature->get('model_lang');
            //get translations of source model
            $langs = $nomenclature->getTranslations();
            // then remove them from model
            $nomenclature->unsetProperty('translations', true);

            //get the categories
            $nomenclature->getCategories();

            //clear the id and set the source (original) id
            $nomenclature->set('id', null, true);
            $nomenclature->set('origin_id', $id, true);
            $nomenclature->set('origin_code', $nomenclature->get('code'), true);
            $nomenclature->set('origin_name', $nomenclature->get('name'), true);

            //clear dates and users stats
            $nomenclature->set('added', null, true);
            $nomenclature->set('added_by', null, true);
            $nomenclature->set('modified', null, true);
            $nomenclature->set('modified_by', null, true);
            $nomenclature->set('code', null, true);
            $nomenclature->set('num', null, true);

            //variable used to store the relatives type
            $nomenclature->set('clone_transform', 'cloned', true);

            //do the clone
            if ($nomenclature->cloneModel()) {
                foreach ($langs as $t_lang) {
                    //copy other translations
                    if ($model_lang != $t_lang) {
                        $filters = array('where' => array('n.id = ' . $id,
                                                          'n.active = 1'),
                                         'model_lang' => $t_lang);
                        $t_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                        if (!empty($t_nomenclature)) {
                            $t_nomenclature->getVars();
                            $t_nomenclature->set('orig_vars', $t_nomenclature->get('vars'), true);
                            $t_nomenclature->unsetVars();
                            $t_nomenclature->set('id', $nomenclature->get('id'), true);
                            $t_nomenclature->slashesEscape();
                            $t_nomenclature->updateI18N();
                            $t_nomenclature->slashesStrip();
                            $t_nomenclature->copyVars($t_lang);
                        }
                    }
                }
                if ($nomenclature->slashesEscaped) {
                    $nomenclature->slashesStrip();
                }

                $filters = array('where' => array('n.id = ' . $nomenclature->get('id')),
                                 'model_lang' => $nomenclature->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                // data for audit
                $new_nomenclature->set('origin_code', $nomenclature->get('origin_code'), true);
                $new_nomenclature->set('origin_name', $nomenclature->get('origin_name'), true);
                $new_nomenclature->getVars();

                $old_nomenclature = new Nomenclature($this->registry, array('type' => $nomenclature->get('type')));
                $old_nomenclature->getVars();
                $old_nomenclature->sanitize();

                Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'clone', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));

                //clone is successful
                //redirect view cloned model
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_clone_success', array($nomenclature->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
            } else {
                //unsuccessful clone
                //redirect view parent model
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_clone_failed', array($nomenclature->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $id);
            }
        }
    }

    /**
     * Redirect to specified module at ADD section and auto complete nomenclature's data
     */
    private function _create() {
        $request = &$this->registry['request'];

        $module = $request->get('operation');
        switch ($module) {
            case 'minitasks':
                $add_link = sprintf('communications=%d&communication_type=minitasks&operation=add#communications_container',
                                    $request->get('create_from_nomenclature_id'));
                $this->redirect($this->module, 'communications', $add_link);
                break;
        }
        return true;
    }

    /**
     * History of the nomenclature
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'n.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $nomenclature = Nomenclatures::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($nomenclature && $this->checkAccessOwnership($nomenclature, false)) {
                if (!$this->registry->isRegistered('nomenclature')) {
                    $this->registry->set('nomenclature', $nomenclature->sanitize());
                }

                require_once $this->viewersDir . 'nomenclatures.history.viewer.php';
                $viewer = new Nomenclatures_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($nomenclature)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclature);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclature')) {
                $this->registry->set('nomenclature', $nomenclature->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Communications concerning the nomenclature (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('n.id = ' . $id ));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
        if (!empty($nomenclature)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclature);

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclature')) {
                $this->registry->set('nomenclature', $nomenclature->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Fetches generated nomenclature file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'generate' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('n.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $nomenclature = Nomenclatures::searchOne($this->registry, $filters);

        if (!empty($nomenclature)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclature);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            if (is_numeric($request->get('file'))) {
                $file_id = $request->get('file');
            } else {
                $file_id = General::decrypt($request->get('file'), '_' . $this->action . '_', 'xtea');
            }

            $filters = array('where' => array('f.id = ' . $file_id),
                             'sanitize' => true);
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
                    }
                    break;
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_nomenclatures_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to edit/view mode
                    if (preg_match('/edit/', $_SERVER['HTTP_REFERER'])) {
                        $this->redirect($this->module, 'edit', 'edit=' . $nomenclature->get('id'));
                    } else {
                        $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
                    }
                    break;
                case 'ajaxdelfile':
                    if (Files::delete($this->registry, array($file->get('id')))) {
                        $result = '1';
                    } else {
                        $result = '0';
                    }
                    exit ($result);
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_nomenclatures_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to edit/view mode
                    if (preg_match('/edit/', $_SERVER['HTTP_REFERER'])) {
                        $this->redirect($this->module, 'edit', 'edit=' . $nomenclature->get('id'));
                    } else {
                        $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
                    }
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //redirect to edit/view mode
                if (preg_match('/edit/', $_SERVER['HTTP_REFERER'])) {
                    $this->redirect($this->module, 'edit', 'edit=' . $nomenclature->get('id'));
                } else {
                    $this->redirect($this->module, 'view', 'view=' . $nomenclature->get('id'));
                }
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Shows attached files of the nomenclature
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        if (!$request->isPost()) {
            //when coming from a link in list/search/outlook,
            //redirect to "view" of model
            $this->redirect($this->module, 'view', "view={$id}#attachments");
        }
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        if ($this->action == 'filter') {
            $this->actionDefinitions = array($this->action);
            $this->afterActionDefinitions = array();
        }

        //get model for this class
        $this->getModel();

        $actions = parent::getActions($action_defs);

        //prepare add options types
        require_once($this->modelsDir . 'nomenclatures.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang(),
                         'sort' => array('nti18n.name ASC'),
                         'sanitize' => true,
                         'where' => array('nt.active = 1'));

        $customize = '';
        $found = 0;

        $custom_filters = array();
        if ($this->registry['request']->get('type')) {
            $customize = 'nt.id="' . $this->registry['request']->get('type') . '"';
            $found++;
        } else if ($this->registry['request']->get('type_section')) {
            $customize = 'nt.type_section="' . $this->registry['request']->get('type_section') . '"';
            $found++;
        } else if ($this->registry['session']->isRequested($this->action . '_nomenclature')) {
            $custom_filters = $this->registry['session']->get($this->action . '_nomenclature');
        }

        if (!empty($custom_filters)) {
            // shows if there is a type defined and if so doesn't add the type section filter
            $type_defined = false;
            if (isset($custom_filters['search_fields'])) {
                foreach ($custom_filters['search_fields'] as $key => $where) {
                    if (preg_match('#n\.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                        $customize = 'nt.id="' . $custom_filters['values'][$key] . '"';
                        if ($type_defined) {
                            $found++;
                        } else {
                            $type_defined = true;
                            $found = 1;
                        }
                    } else if (preg_match('#nt\.type_section#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                        if (!$type_defined) {
                            $customize = 'nt.type_section="' . $custom_filters['values'][$key] . '"';
                            $found++;
                        }
                    }
                }
            }
        }

        // if there is a model the only available options for add
        // will be the options for the current type or the current section
        if ($found == 1 && $customize) {
            $filters['where'][] = $customize;
        }

        $nomenclatureTypes = Nomenclatures_Types::search($this->registry, $filters);
        $theme = $this->registry['theme'];
        $ThemeCanProvideIcons = is_callable([$theme, 'getIconForAction']);

        // list action
        if (isset($actions['list'])) {
            // extend the link for list to clear type sections and types
            if ($this->model && $this->model->get('id')) {
                $actions['list']['ajax_no'] = 1;
                $actions['list']['drop_menu'] = true;

                $actions['list']['options']['previous_list']['img'] = 'list';
                if ($ThemeCanProvideIcons) {
                    $actions['list']['options']['previous_list']['icon'] = $theme->getIconForAction('list');;
                }
                $actions['list']['options']['previous_list']['label'] = $this->i18n('previous_list');
                $actions['list']['options']['previous_list']['url'] = $actions['list']['url'];
                if (isset($actions['search'])) {
                    $actions['list']['options']['previous_search']['img'] = 'search';
                    if ($ThemeCanProvideIcons) {
                        $actions['list']['options']['previous_search']['icon'] = $theme->getIconForAction('search');
                    }
                    $actions['list']['options']['previous_search']['label'] = $this->i18n('previous_search');
                    $actions['list']['options']['previous_search']['url'] = $actions['search']['url'];
                }

                $actions['list']['options']['all_nomenclatures']['img'] = 'nomenclatures';
                if ($ThemeCanProvideIcons) {
                    $actions['list']['options']['all_nomenclatures']['icon'] = $theme->getIconForRecord('nomenclatures');;
                }
                $actions['list']['options']['all_nomenclatures']['label'] = $this->i18n('nomenclatures_all');
                $actions['list']['options']['all_nomenclatures']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';

                $actions['list']['url'] = $actions['list']['url'] . sprintf('&amp;type=%d&amp;type_section=', $this->model->get('type'));
            } else {
                $actions['list']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';
            }
        }

        //prepare add options types
        $_options_add = array();

        foreach ($nomenclatureTypes as $type) {
            $type_permission = $this->checkActionPermissions($this->module . $type->get('id'), 'add');
            if ($type_permission) {
                // Define OPTIONS parameter
                $_options_add[] = array(
                    'label' => $type->get('name'),
                    'option_value' => $type->get('id'));
            }
        }


        if (isset($actions['add']) && !empty($_options_add)) {
            if (count($_options_add) == 1) {
                //directly set the link of the add action with the type selected
                $actions['add']['url'] .= '&amp;type=' . $_options_add[0]['option_value'];
            } else {
                //prepare add options
                $add_options = array(
                    array(
                        'custom_id' => 'type_',
                        'name' => 'type',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('nomenclatures_type'),
                        'help' => $this->i18n('nomenclatures_add_legend'),
                        'options' => $_options_add,
                        'value' => $this->registry['request']->get('type') ?: ''
                    ),
                );
                $actions['add']['options'] = $add_options;
                $actions['add']['ajax_no'] = true;
            }
        } else {
            unset($actions['add']);
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if ($this->model && isset($actions['clone'])) {
            $filters = array('where' => array('nt.id = ' . $this->model->get('type'),
                                              'nt.active = 1'),
                             'sanitize' => true);
            $type = Nomenclatures_Types::searchOne($this->registry, $filters);
            if (empty($type)) {
                unset($actions['clone']);
            } else {
                $actions['clone']['confirm'] = 'confirm_clone';
            }
        }

        if (isset($actions['tag']) && $this->model && $this->model->get('id') && $this->model->getAvailableTags()) {

            $this->model->getTags();

            if ($this->model->checkPermissions('tags_view') && ($this->model->get('tags') && array_intersect($this->model->get('tags'), array_keys($this->model->get('available_tags'))) || $this->model->checkPermissions('tags_edit'))) {
                $actions['tag']['options'] = array('label' => $this->i18n('confirm_tags'));
                $actions['tag']['ajax_no'] = 1;
                $actions['tag']['template'] = '_action_tag.html';
                $actions['tag']['model_id'] = $this->model->get('id');
            } else {
                unset($actions['tag']);
            }
        } else {
            unset($actions['tag']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {
            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^nt\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^nt\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                    if ($ThemeCanProvideIcons) {
                        $actions['printlist']['icon'] = $theme->getIconForAction('print');
                    }
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                    if ($ThemeCanProvideIcons) {
                        $actions['printlist']['icon'] = $theme->getIconForAction('print');
                    }
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                    if ($ThemeCanProvideIcons) {
                        $actions['printlist']['icon'] = $theme->getIconForAction('print');
                    }
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        // communications action
        if (isset($actions['communications'])) {
            $actions['communications']['ajax_no'] = 1;
            $actions['communications']['drop_menu'] = true;
            $actions['communications']['hide_label'] = true;

            if (isset($actions['emails']) || isset($actions['comments']) || isset($actions['minitasks'])) {
                if (isset($actions['comments'])) {
                    $actions['communications']['options']['comments']['img'] = 'comments';
                    $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                    $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['comments']['icon'] = $theme->getIconForAction('comments');
                    }
                    unset($actions['comments']);
                }
                if (isset($actions['emails'])) {
                    $actions['communications']['options']['emails']['img'] = 'email';
                    $actions['communications']['options']['emails']['label'] = $this->i18n('nomenclatures_emails');
                    $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['emails']['icon'] = $theme->getIconForAction('emails');
                    }
                    unset($actions['emails']);
                }
                if (isset($actions['minitasks'])) {
                    $actions['communications']['options']['minitasks']['img'] = 'minitasks';
                    $actions['communications']['options']['minitasks']['label'] = $actions['minitasks']['label'];
                    $actions['communications']['options']['minitasks']['url'] = $actions['communications']['url'] . '&amp;communication_type=minitasks';
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['minitasks']['icon'] = $theme->getIconForAction('minitasks');
                    }
                    // do not unset yet, action is used in "Create" as well
                    //unset($actions['minitasks']);
                }
            } else {
                unset($actions['communications']);
            }
        } else {
            if (isset($actions['emails'])) {
                unset($actions['emails']);
            }
            if (isset($actions['comments'])) {
                unset($actions['comments']);
            }
            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        if ($this->model && $this->model->get('id') && isset($actions['create'])) {
            $actions['create']['label'] = $this->i18n('create');
            $actions['create']['ajax_no'] = 1;
            $actions['create']['template'] = PH_MODULES_DIR . 'nomenclatures/templates/_action_create.html';

            if (isset($actions['minitasks'])) {
                if ($this->registry['currentUser']->checkRights('minitasks', 'add')) {
                    $actions['minitasks']['label'] = $this->i18n('nomenclatures_minitask');
                    $actions['create']['options']['minitasks'] = $actions['minitasks'];
                }
                unset ($actions['minitasks']);
            }

            if (!$actions['create']['options']) {
                unset ($actions['create']);
            } else {
                // get nomenclature's ID
                $actions['create']['nomenclature'] = $this->model->get('id');
            }
        } else {
            unset ($actions['create']);

            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        $_page_menu = [
            'general' => [],
            'context' => [],
            'quick' => [],
            'infriquent' => [],
        ];

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }

            foreach ($this->actionListPageMenu as $k2=>$v2) {
                if (in_array($key, $v2)) {
                    $_page_menu[$k2][$key] = $action;
                }
            }
        }

        if (!empty($_page_menu['infriquent'])) {
            $_page_menu['context']['infriquent'] = [
                'name' => 'infriquent_menu',
                'icon' => 'more_vert',
                'url' => '#',
                'drop_menu' => true,
                'options' => $_page_menu['infriquent'],
            ];
        }

        // check the current action and sets the alternative actions for view, edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } elseif ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit nor view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } elseif (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && ! empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['export'])) {
                unset ($actions['export']);
            }
            if (isset($actions['search'])) {
                unset ($actions['search']);
            }
        }
        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_page_actions', $_page_menu, true);

        $modernizedActions = [
            'index',
            'list',
            'add',
            'adds',
            'view',
            'edit',
            'attachments',
            'communications',
            'history',
        ];
        if (!$theme->isModern() || !in_array($this->action, $modernizedActions)) {
            // Not used in Evolution theme, made available for compatibility reasons in some actions
            $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);
        }

        return $actions;
    }

    /**
     * Activates or deactivates the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Nomenclatures::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);

            foreach ($ids as $id) {
                $nomenclature = new Nomenclature($this->registry);
                $nomenclature->set('id', $id, true);
                Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => $this->action));
            }
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);

        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete nomenclatures
        $result = Nomenclatures::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));

            foreach ($ids as $id) {
                $nomenclature = new Nomenclature($this->registry);
                $nomenclature->set('id', $id, true);
                Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'delete'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Nomenclatures::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));

            foreach ($ids as $id) {
                $nomenclature = new Nomenclature($this->registry);
                $nomenclature->set('id', $id, true);
                Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'restore'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge($ids = '') {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Nomenclatures::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * filter for references
     */
    private function _filter() {
        $this->viewer = $this->getViewer();

        $autocomplete_filter = $this->registry['request']->get('autocomplete_filter');
        if ($autocomplete_filter) {
            if ($autocomplete_filter != 'session') {
                $filters = $this->_select();
                $this->viewer->data['autocomplete_filters'] = $filters;
            }
        }
        $this->viewer->setFrameset('frameset_pop.html');

        return true;
    }

    /**
     * Manage Frankenstein configurator (obsolete).
     * Also manage (load/save/delete) saved configurations for a configurator.
     */
    private function _franky() {
        $request = &$this->registry['request'];
        $id = $request->get('id');

        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if ($id && General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('n.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
        }
        if (empty($nomenclature)) {
            $nomenclature = Nomenclatures::buildModel($this->registry);
            $nomenclature->set('id', $request->get('id', 'get'), true);
            if ($request->get('type')) {
                $nomenclature->set('type', $request->get('type'), true);
            } elseif ($request->get('edit_id')) {
                $type = Nomenclatures::getConfiguratorModelType($this->registry, $request->get('edit_id'));
                $nomenclature->set('type', $type, true);
            }
        }

        $nomenclature->getVarsForTemplate(false);
        $this->registry->set('nomenclature', $nomenclature->sanitize(), true);

        require_once PH_MODULES_DIR . 'configurators/models/configurators.factory.php';
        require_once PH_MODULES_DIR . 'configurators/viewers/franky.viewer.php';
        $this->viewer = new Franky_Viewer($this->registry);
        $this->viewer->model = $nomenclature;

        $configurator = Configurators::buildModel($this->registry, '');
        if ($request->get('edit_id') > 0) {
            // load
            $this->registry['configData'] =
                $configurator->getConfigForTemplate(array('id' => $request->get('edit_id')));
        } else {
            // delete or save
            if ($request->get('del_id') > 0) {
                $configurator->config_delete($request->get('del_id'));
            } else {
                if ($request->get('id', 'get')) {
                    $configurator->saveFranky();
                } else {
                    $configurator->save();
                }
            }
            if (($request->get('del_id') || $request->get('config_id')) && !$request->get('id', 'get')) {
                $this->viewer->data['configPatterns'] =
                    $configurator->getConfigPatterns(
                        array(
                            'model' => $nomenclature->modelName,
                            'model_type' => $nomenclature->get('type'),
                            'model_id' => 0,
                            'config_num' => $request->get('config_num')
                        ));
            }

            if ($request->get('id', 'get')) {
                $nomenclature->getVarsForTemplate(false);
                $this->registry->set('nomenclature', $nomenclature->sanitize(), true);
            }
        }

        return true;
    }

    /**
     * Manage (load/save/delete) saved configurations for a grouping table
     */
    private function _saveGroupVar() {
        $request = &$this->registry['request'];
        $id = $request->get('id');

        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if ($id && General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('n.id = \'' . $id . '\''),
                             'model_lang' => $request->get('model_lang'));
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
        }
        if (empty($nomenclature)) {
            if ($request->get('type')) {
                $nomenclature = new Nomenclature($this->registry);
                $nomenclature->set('id', $request->get('id', 'get'), true);
                $nomenclature->set('type', $request->get('type'), true);
            } else {
                exit;
            }
        }

        $nomenclature->getVarsForTemplate(false);
        $this->registry->set('nomenclature', $nomenclature->sanitize(), true);

        require_once PH_MODULES_DIR . 'configurators/models/configuratorsgroup.factory.php';
        require_once PH_MODULES_DIR . 'configurators/viewers/savegroupvar.viewer.php';
        $this->viewer = new SaveGroupVar_Viewer($this->registry);
        $this->viewer->model = $nomenclature;

        $configurator_group = ConfiguratorsGroups::buildModel($this->registry, '');
        if ($request->get('edit_id') > 0) {
            // load
            $this->registry['configGroupData'] =
                $configurator_group->getConfigGroupForTemplate(array('id' => $request->get('edit_id')));
        } else {
            // delete or save
            if ($request->get('del_id') > 0) {
                $configurator_group->configGroupDelete($request->get('del_id'));
            } else {
                $configurator_group->save();
            }
            $this->viewer->data['configGroupPatterns'] =
                $configurator_group->getConfigGroupPatterns(
                    array(
                        'model' => $nomenclature->modelName,
                        'model_type' => $nomenclature->get('type'),
                        'config_group_num' => $request->get('group_num')
                    ));
        }

        return true;
    }

    /**
     * Sets default percentage distribution of nomenclature by items
     */
    private function _distribute() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $nomenclature = Nomenclatures::buildModel($this->registry);

            $filters = array('where' => array('n.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            $this->old_model = $old_nomenclature->sanitize();

            require_once $this->modelsDir . 'nomenclatures.types.factory.php';
            $filters = array('where' => array('nt.id = ' . $nomenclature->get('type'),
                                              'nt.active = 1'),
                             'sanitize' => true);
            $type = Nomenclatures_Types::searchOne($this->registry, $filters);

            $this->valid = true;

            if ($nomenclature->updateItemsDefaultDistributionValues()) {
                $filters = array('where' => array('n.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);

                Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'distribute', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));

                //show message 'message_nomenclatures_distribute_success'
                $this->registry['messages']->setMessage($this->i18n('message_nomenclatures_distribute_success', array($new_nomenclature->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_nomenclatures_distribute_failed', array($old_nomenclature->getModelTypeName())), '', -1);
            }
        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('n.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);

            if (!empty($nomenclature)) {
                //check access and ownership of the model
                $this->checkAccessOwnership($nomenclature);
            }
        }

        if (!empty($nomenclature)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclature')) {
                $this->registry->set('nomenclature', $nomenclature->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Remind
     */
    private function _remind() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('n.id = ' . $id ));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
        if (!empty($nomenclature)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($nomenclature);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('nomenclature')) {
                $this->registry->set('nomenclature', $nomenclature->sanitize());
            }

            require_once PH_MODULES_DIR . 'reminds/viewers/reminds.viewer.php';
            $this->viewer = new Reminds_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_nomenclature'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions();

        if ($this->model) {
            //get permissions of the currently logged user
            $this->getUserPermissions();

            $type = $this->registry['request']->get('type') ?: $this->model->get('type');

            if (isset($actions['add']) && $this->checkActionPermissions($this->module . $type, 'add')) {

                //prepare add options
                $add_options = array(
                    array(
                        'custom_id' => 'type____',
                        'name' => 'aa1_type',
                        'type' => 'hidden',
                        'label' => $this->i18n('nomenclatures_type'),
                        'value' => $type
                    ),
                );
                $actions['add']['options'] = $add_options;
            } else {
                unset($actions['add']);
            }
        }

        return $actions;
    }

    /**
     * Manage bb (add/clone/edit/save/delete row)
     */
    private function _bb() {
        $request = &$this->registry['request'];
        $id = $request->get('id');              // id of model
        $meta_id = $request->get('meta_id');    // id of variable of type 'config', 'group' or 'gt2' that the bb row contains
        $bb_id = $request->get('bb_id');        // id of data row in `bb` table
        $bb_action = $request->get('bb_action');// current action performed with the bb row
        $bb_num = $request->get('bb_num');      // 'bb' field in `_fields_meta` of variables from bb (usually 1)
        // make a guess whether id is a model id or a temporary id for a model
        // that is not added yet
        if (General::guessIfRealModelId($this->registry, $id)) {
            $filters = array('where' => array('n.id = \'' . $id . '\''),
                'model_lang' => $request->get('model_lang'));
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
        }
        // build model from request
        if (empty($nomenclature)) {
            $nomenclature = Nomenclatures::buildModel($this->registry);
            if (!$id) {
                $id = $request->get('id', 'get');
            }
            $nomenclature->set('id', $id, true);
            // only if variable is available in request and has value (other than 0)
            if ($bb_id) {
                $nomenclature->set('type', Nomenclatures::getBBModelType($this->registry, $bb_id), true);
            }
        }
        $nomenclature->getVarsForTemplate(false);
        $vars = $nomenclature->get('vars');

        // get inner compound variable (prepared for display)
        $inner_var = array();
        if ($meta_id) {
            $meta_info = $nomenclature->getMetaInfo($meta_id);
            foreach ($vars as $var) {
                if (isset($var['id']) && $var['id'] == $meta_id) {
                    $inner_var = $var;
                    break;
                }
            }
        }

        // the requested compound variable was not found, there is some error
        if (empty($inner_var)) {
            $bb_action = 'error';
        }

        /**
         * Check if bb element has file upload controls (in plain vars or in inner variable)
         *
         * @param array $vars - array of all additional variables of model
         * @param array $var - compound additional variable in a bb row
         * @return int - number of found file upload controls
         */
        $bbWithFileUpload = function(&$vars, &$var) {
            $bb_with_file_upload = 0;

            foreach ($vars as $var_bb) {
                if ($var_bb['type'] == 'bb') {
                    foreach ($var_bb['types'] as $var_bb_type) {
                        if ($var_bb_type == 'file_upload') {
                            $bb_with_file_upload++;
                        }
                    }
                }
            }

            if (isset($var['types'])) {
                foreach ($var['types'] as $var_type) {
                    if ($var_type == 'file_upload') {
                        $bb_with_file_upload++;
                    }
                }
            } else if ($var['type'] == 'gt2' && !empty($var['vars'])) {
                foreach ($var['vars'] as $var_info) {
                    if ($var_info['type'] == 'file_upload') {
                        $bb_with_file_upload++;
                    }
                }
            }

            return $bb_with_file_upload;
        };

        // perform selected action
        switch ($bb_action) {
            case 'edit':
                if ($bb_id) {
                    $bb_var = $nomenclature->getBB(array('model_id' => $nomenclature->get('id'), 'bb_id' => $bb_id));

                    if ($bb_var) {
                        $bb_var = reset($bb_var);
                        $inner_var['meta_id'] = $bb_var['meta_id'];
                        $inner_var['model_id'] = $bb_var['model_id'];
                        $inner_var['id'] = $bb_var['id'];
                        $nomenclature->prepareBbVarValues($inner_var, $bb_var['params']);
                    } else {
                        $bb_action = 'error';
                    }
                } else {
                    $bb_action = 'error';
                }
                break;
            case 'add':
                $data = array();
                if ($meta_info['type'] == 'gt2') {
                    $data['values'] = $data['plain_values'] = $data['rows'] = array();
                }
                $params = array();
                $params['data'] = $data;
                $params['bb_id'] = $bb_id;
                $params['bb_num'] = $bb_num;
                $params['meta_id'] = $inner_var['id'];

                $bb_id = $nomenclature->saveBB($params);
                if (!$bb_id) {
                    $bb_action = 'error';
                }
                break;
            case 'clone':
                $bb_id = $nomenclature->cloneBBRow(array('bb_id' => $bb_id, 'var' => &$inner_var));
                if (!$bb_id) {
                    $bb_action = 'error';
                }
                break;
            case 'save':
                $nomenclature->getVars();
                $all_vars = $nomenclature->get('vars');
                if ($meta_info['type'] == 'gt2') {
                    $gt2_vals = array();
                    $gt2_rows = array();
                    $deleted = $request->get('deleted');
                    $delete_file_ids = array();
                    foreach ($inner_var['vars'] as $var_name => $v_var) {
                        if ($v_var['type'] == 'file_upload') {
                            if (!empty($_FILES) && isset($_FILES[$var_name])) {
                                $files_values = $_FILES[$var_name];
                            } else {
                                $files_values['tmp_name'] = array();
                            }

                            if ($request->get('dbid_' . $var_name)) {
                                $post_values = $request->get('dbid_' . $var_name);
                            } else {
                                $post_values = array();
                            }

                            $values_array = array();

                            // files which have to be deleted
                            if ($request->get('deleteid_' . $var_name)) {
                                foreach ($request->get('deleteid_' . $var_name) as $key_v => $file_id_encrypted) {
                                    if ($file_id_encrypted) {
                                        $file_id = General::decrypt($file_id_encrypted, '_delete_file_', 'xtea');
                                        $delete_file_ids[] = $file_id;
                                        $values_array[$key_v] = '';
                                    } else {
                                        $values_array[$key_v] = '';
                                    }
                                }
                            }

                            foreach ($files_values['tmp_name'] as $key_fv => $file_value) {
                                $values_array[$key_fv] = '';
                            }

                            foreach ($post_values as $pv_key => $post_value) {
                                $values_array[$pv_key] = $post_value;
                            }
                            // keys are negative so sort in reverse order
                            krsort($values_array);

                            foreach ($values_array as $r_idx => $r_val) {
                                if (isset($deleted[$r_idx]) && $deleted[$r_idx]) {
                                    unset($gt2_vals['values'][$r_idx]);
                                } else {
                                    $gt2_vals['values'][$r_idx][$var_name] = $r_val;
                                    if (!in_array($r_idx, $gt2_rows)) {
                                        $gt2_rows[] = $r_idx;
                                        $gt2_vals['values'][$r_idx]['id'] = $r_idx;
                                    }
                                }
                            }
                        } else {
                            $vals = $request->get($var_name);
                            if ($vals) {
                                foreach ($vals as $key => $val) {
                                    if (isset($deleted[$key]) && $deleted[$key]) {
                                        unset($gt2_vals['values'][$key]);
                                    } else {
                                        $gt2_vals['values'][$key][$var_name] = $val;
                                        if (!in_array($key, $gt2_rows)) {
                                            $gt2_rows[] = $key;
                                            $gt2_vals['values'][$key]['id'] = $key;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // delete old files
                    require_once PH_MODULES_DIR . 'files/models/files.factory.php';
                    if (!empty($delete_file_ids)) {
                        $del_ids = $delete_file_ids;
                        if (!is_array($del_ids)) {
                            $del_ids = array($del_ids);
                        }
                        Files::delete($this->registry, $del_ids);
                    }

                    $gt2_vals['rows'] = $gt2_rows;
                    foreach ($inner_var['plain_vars'] as $var_name => $v_var) {
                        $gt2_vals['plain_values'][$var_name] = $request->get($var_name);
                    }

                    $gt2_validate = $gt2_vals;
                    $gt2_validate['vars'] = $inner_var['vars'];
                    $gt2_validate['calculated_price'] = $inner_var['calculated_price'];
                    $gt2_validate = $nomenclature->validateGT2(array(), $gt2_validate);
                    // get GT2 after validation
                    unset($gt2_validate['vars']);
                    unset($gt2_validate['calculated_price']);
                    $gt2_vals = $gt2_validate;

                    // reindex 'rows' array and keys of 'values' array of GT2
                    // indexes will go -1, -2, -3 etc. without gaps
                    $reindexed_values = array();
                    // get keys from 'values' array because empty rows have been unset
                    $gt2_rows = array_keys($gt2_vals['values']);
                    foreach ($gt2_rows as $idx => $key) {
                        $gt2_rows[$idx] = -($idx+1);
                        $reindexed_values[$gt2_rows[$idx]] = $gt2_vals['values'][$key];
                    }
                    $gt2_vals['rows'] = $gt2_rows;
                    $gt2_vals['values'] = $reindexed_values;
                }
                $nomenclature->set('vars', $all_vars, true);

                //validate values
                if (!$nomenclature->validateVars(true) || !empty($gt2_validate['has_error'])) {
                    $bb_action = 'error';
                } else {
                    $data = array();
                    if ($meta_info['type'] == 'gt2') {
                        $data = $gt2_vals;
                    } else {
                        foreach ($inner_var['names'] as $key_name => $name) {
                            $default_value = $meta_info['type'] == 'group' || $inner_var['types'][$key_name] == 'checkbox_group' ? array() : '';
                            if ($inner_var['types'][$key_name] == 'file_upload') {
                                $data[$name] = $request->get('dbid_' . $name) ?: $default_value;
                            } else {
                                $data[$name] = $request->get($name) ?: $default_value;
                            }
                        }
                    }
                    $params = array();
                    $params['data'] = $data;
                    $params['bb_id'] = $bb_id;
                    $params['bb_num'] = $bb_num;
                    $params['meta_id'] = $inner_var['id'];
                    $params['var'] = &$inner_var;
                    if (!$nomenclature->saveBB($params)) {
                        $bb_action = 'error';
                    }
                }
                break;
            case 'del':
                $params = array();
                $params['bb_id'] = $bb_id;
                $params['bb_num'] = $inner_var['bb'];
                $params['model_id'] = $id;
                if (!$nomenclature->delBB($params)) {
                    $bb_action = 'error';
                }
                break;
            default:
                break;
        }

        // prepare response to be returned and displayed
        switch ($bb_action) {
            case 'error':
                // an error has occurred, return error messages
                if (!$this->registry['messages']->getErrors()) {
                    $this->registry['messages']->setError($this->i18n('error_bb_action'));
                }
                print $this->getMessagesForAJAX('error');
                break;
            case 'edit':
                // return selected bb row in edit mode
                $bbViewer = new Viewer($this->registry);
                $bbViewer->setFrameset('_bb_edit.html');
                $bbViewer->data['var'] = $inner_var;
                $bbViewer->data['model_id'] = $id;
                // model of the calc variable is other than model name
                $bbViewer->data['calc_meta_id'] = $nomenclature->getMetaId($inner_var['name'] . '_calc');
                // check if the bb has file upload controls in it
                $bbViewer->data['bb_with_file_upload'] = $bbWithFileUpload($vars, $inner_var);

                // prepare saved configurations
                if ($inner_var['type'] == 'grouping') {
                    require_once PH_MODULES_DIR . 'configurators/models/configuratorsgroup.factory.php';
                    $configurator_group = ConfiguratorsGroups::buildModel($this->registry, '');
                    $bbViewer->data['configGroupPatterns'] =
                        $configurator_group->getConfigGroupPatterns(
                            array(
                                'model' => $nomenclature->modelName,
                                'model_type' => $nomenclature->get('type'),
                                'config_group_num' => $inner_var['grouping']
                            ));
                    unset($configurator_group);
                } elseif ($inner_var['type'] == 'config') {
                    require_once PH_MODULES_DIR . 'configurators/models/configurators.factory.php';
                    $configurator = Configurators::buildModel($this->registry, '');
                    $bbViewer->data['configPatterns'] =
                        $configurator->getConfigPatterns(
                            array(
                                'model' => $nomenclature->modelName,
                                'model_type' => $nomenclature->get('type'),
                                'model_id' => 0,
                                'config_num' => $inner_var['config']
                            ));
                    unset($configurator);
                }

                print $bbViewer->fetch();
                break;
            default:
                // return the whole bb table
                // remove inner bb vars from model vars
                $bb_elements = $bb_var = array();
                foreach ($vars as $key => $var) {
                    if (!empty($var['bb']) && in_array($var['type'], array('grouping', 'config', 'gt2'))) {
                        $bb_elements[$var['id']] = $var;
                        unset($vars[$key]);
                    } elseif ($var['type'] == 'bb') {
                        $bb_var = $var;
                    }
                }
                $nomenclature->set('vars', $vars, true);

                // inner bb variables and data
                $bb_vars = $nomenclature->getBB(array('model_id' => $nomenclature->get('id')));
                foreach ($bb_vars as $index => $var) {
                    $bb_vars[$index] = $bb_elements[$var['meta_id']];
                    $bb_vars[$index]['id'] = $var['id'];
                    $bb_vars[$index]['meta_id'] = $var['meta_id'];
                    $bb_vars[$index]['model_id'] = $var['model_id'];
                    $nomenclature->prepareBbVarValues($bb_vars[$index], $var['params']);
                }
                $nomenclature->set('bb_vars', $bb_vars, true);

                // bb variables and data for caption rows
                $bb_fields_params = array('mode' => 'request');
                if (in_array($bb_action, array('add', 'clone'))) {
                    // specify that data for the row that was just added should be taken from db
                    $bb_fields_params['added_bb_id'] = $bb_id;
                }
                $nomenclature->set('add_bb_vars', $nomenclature->getBBFields($bb_fields_params), true);

                $bbViewer = new Viewer($this->registry);
                $bbViewer->setFrameset('_bb_list.html');
                $bbViewer->model = $nomenclature;
                if ($bb_action != 'del') {
                    // highlight current row
                    $bbViewer->data['bb_id'] = $bb_id;
                }
                $bbViewer->data['model_id'] = $id;
                $bbViewer->data['var'] = $bb_var;

                // if current bb row should be displayed in edit mode
                if (in_array($bb_action, array('add'))) {
                    $bbViewer->data['edit_row'] = $bb_id;
                    // model of the calc variable is other than model name
                    $bbViewer->data['calc_meta_id'] = $nomenclature->getMetaId($meta_info['name'] . '_calc');
                    // check if the bb has file upload controls in it
                    $bbViewer->data['bb_with_file_upload'] = $bbWithFileUpload($vars, $inner_var);

                    // prepare saved configurations
                    if ($meta_info['type'] == 'group') {
                        require_once PH_MODULES_DIR . 'configurators/models/configuratorsgroup.factory.php';
                        $configurator_group = ConfiguratorsGroups::buildModel($this->registry, '');
                        $bbViewer->data['configGroupPatterns'] =
                            $configurator_group->getConfigGroupPatterns(
                                array(
                                    'model' => $nomenclature->modelName,
                                    'model_type' => $nomenclature->get('type'),
                                    'config_group_num' => $meta_info['grouping']
                                ));
                        unset($configurator_group);
                    } elseif ($meta_info['type'] == 'config') {
                        require_once PH_MODULES_DIR . 'configurators/models/configurators.factory.php';
                        $configurator = Configurators::buildModel($this->registry, '');
                        $bbViewer->data['configPatterns'] =
                            $configurator->getConfigPatterns(
                                array(
                                    'model' => $nomenclature->modelName,
                                    'model_type' => $nomenclature->get('type'),
                                    'model_id' => 0,
                                    'config_num' => $meta_info['configurator']
                                ));
                        unset($configurator);
                    }
                }

                print $bbViewer->fetch();
                break;
        }

        exit;
    }

    /**
     * Attach a file as additional field using ajax
     */
    private function _attachAdditionalFieldFile() {
        $request = &$this->registry['request'];
        $field_source = $request->get('field_source');

        $result = array();
        $errors = array();
        if ($_FILES && !empty($_FILES)) {
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $id = $request->get('id');
            // make a guess whether id is a model id or a temporary id for a model
            // that is not added yet
            if (General::guessIfRealModelId($this->registry, $id)) {
                $filters = array('where' => array('n.id = \'' . $id . '\''),
                    'model_lang' => $request->get('model_lang'));
                $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            }
            // build model from request
            if (empty($nomenclature)) {
                $nomenclature = Nomenclatures::buildModel($this->registry);
                if (!$id) {
                    $id = $request->get('id', 'get');
                }
                $nomenclature->set('id', $id, true);
                $nomenclature->set('added', General::strftime($this->i18n('date_iso')), true);
            }

            $nomenclature->getVarsForTemplate(false);
            $this->registry->set('nomenclature', $nomenclature, true);

            $doc_vars = $nomenclature->get('vars');

            //get only data for config num table
            $data = array();

            foreach ($doc_vars as $key => $var) {
                if ($var['type'] == 'config' && ($var['id'] == $request->get('meta_id') || ($var['config'] == $request->get('config_num')))) {
                    foreach ($var['names'] as $idx => $var_name) {
                        if (isset($_FILES[$var_name]) && (!empty($_FILES[$var_name]['tmp_name']))) {
                            $current_file = $_FILES[$var_name];
                            $params = array(
                                'name'        => $current_file['name'],
                                'description' => '',
                                'revision'    => '',
                                'permission'  => 'all'
                            );

                            $restrictions = array();
                            if (!empty($var[$var_name]['source'])) {
                                $restrictions = General::parseSettings($var[$var_name]['source']);
                                if (!preg_match('#image#', $current_file['type'])) {
                                    $restrictions = array_diff_key($restrictions, array_flip(array('max_width', 'max_height', 'thumb_width', 'thumb_height', 'view_mode')));
                                }
                            }

                            $operation_result = Files::attachFile($this->registry, $current_file, $params, $nomenclature, $restrictions);

                            if ($operation_result) {
                                // find the last uploaded id and sets it as a value
                                $result[$var_name] = $nomenclature->getLatestAttachment();
                            } else {
                                $errors[$var_name] = $nomenclature->raiseError('warning_attachment_not_uploaded', $var_name, null, array($current_file['name'], $var['labels'][$idx]));
                            }
                        }
                    }
                    break;
                } elseif ($var['type'] == 'grouping' && $var['id'] == $request->get('meta_id') && !empty($var['types'])) {
                    foreach ($var['types'] as $type_num => $var_type) {
                        $var_name = $var['names'][$type_num];
                        if ($var_type == 'file_upload') {
                            foreach ($var['values'] as $row_num => $row_values) {
                                if (isset($row_values[$type_num]) && is_array($row_values[$type_num])) {
                                    $current_file = $row_values[$type_num];

                                    $params = array(
                                        'name'        => $current_file['name'],
                                        'description' => '',
                                        'revision'    => '',
                                        'permission'  => 'all'
                                    );

                                    $restrictions = array();
                                    if (!empty($var[$var_name]['source'])) {
                                        $restrictions = General::parseSettings($var[$var_name]['source']);
                                        if (!preg_match('#image#', $current_file['type'])) {
                                            $restrictions = array_diff_key($restrictions, array_flip(array('max_width', 'max_height', 'thumb_width', 'thumb_height', 'view_mode')));
                                        }
                                    }

                                    $operation_result = Files::attachFile($this->registry, $current_file, $params, $nomenclature, $restrictions);

                                    if ($operation_result) {
                                        // find the last uploaded id and sets it as a value
                                        $result[$var_name . '_' . $row_num] = $nomenclature->getLatestAttachment();
                                    } else {
                                        $errors[$var_name . '_' . $row_num] = $nomenclature->raiseError('warning_attachment_not_uploaded', $var_name . '_' . $row_num, null, array($current_file['name'], $var['labels'][$type_num]));
                                    }
                                }
                            }
                        }
                    }
                    break;
                } elseif ($var['type'] == 'bb') {
                    foreach ($var['types'] as $key_row => $var_type) {
                        if ($var_type == 'file_upload') {
                            $var_name = $var['names'][$key_row];
                            foreach ($var['values'] as $key_bb_val => $bb_val) {
                                if (isset($bb_val[$key_row]) && is_array($bb_val[$key_row])) {
                                    $current_file = $bb_val[$key_row];
                                    $params = array(
                                        'name'        => $current_file['name'],
                                        'description' => '',
                                        'revision'    => '',
                                        'permission'  => 'all'
                                    );

                                    $restrictions = array();

                                    $operation_result = Files::attachFile($this->registry, $current_file, $params, $nomenclature, $restrictions);

                                    if ($operation_result) {
                                        // find the last uploaded id and sets it as a value
                                        $result[$var_name . '_' . ($key_bb_val+1)] = $nomenclature->getLatestAttachment();
                                    } else {
                                        $errors[$var_name . '_' . ($key_bb_val+1)] = $nomenclature->raiseError('warning_attachment_not_uploaded', $var_name . '_' . ($key_bb_val+1), null, array($current_file['name'], $var['labels'][$key_row]));
                                    }
                                }
                            }
                        }
                    }
                } elseif ($var['type'] == 'gt2' && ($var['id'] == $request->get('meta_id'))) {
                    foreach ($var['vars'] as $var_name => $var_info) {
                        if ($var_info['type'] == 'file_upload') {

                            foreach ($var['values'] as $row_num => $row_values) {
                                if (isset($row_values[$var_name]) && is_array($row_values[$var_name])) {

                                    // row is deleted
                                    if (!empty($row_values['deleted'])) {
                                        continue;
                                    }

                                    $current_file = $row_values[$var_name]['file_data'];
                                    $params = $row_values[$var_name]['params'];

                                    $restrictions = array();
                                    if (!empty($var['vars'][$var_name]['source'])) {
                                        $restrictions = General::parseSettings($var['vars'][$var_name]['source']);
                                        // checks the type of the file
                                        if (!preg_match('#image#', $current_file['type'])) {
                                            $restrictions = array_diff_key($restrictions, array_flip(array('max_width', 'max_height', 'thumb_width', 'thumb_height', 'view_mode')));
                                        }
                                    }

                                    $operation_result = Files::attachFile($this->registry, $current_file, $params, $nomenclature, $restrictions);

                                    // IMPORTANT: GT2 rows in BB now have negative row indexes
                                    // because they are not real GT2 rows (saved in gt2_details).
                                    // Row could have old (positive: 0, 1, 2 etc.) or new (negative: -1, -2, -3)
                                    // indexation so try to define upload field suffix correctly
                                    $row_num = $row_num >= 0 ? $row_num + 1 : abs($row_num);
                                    if ($operation_result) {
                                        // find the last uploaded id and sets it as a value
                                        $result[$var_name . '_' . $row_num] = $nomenclature->getLatestAttachment();
                                    } else {
                                        $errors[$var_name . '_' . $row_num] = $nomenclature->raiseError('warning_attachment_not_uploaded', $var_name . '_' . $row_num, null, array($current_file['name'], $var['vars'][$var_name]['label']));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && empty($_POST)) {
            // $_FILES and $_POST are empty because file size exceeds max allowed size for application/server
            $errors[] = $this->registry['messages']->setError(
                $this->i18n(
                'error_file_greater_filesize',
                array(General::convertFileSize(General::convertBytes(ini_get('upload_max_filesize'))))
            ));
        }

        if (!empty(FilesLib::$_errors)) {
            $errors = array_merge($errors, FilesLib::$_errors);
            foreach(FilesLib::$_errors as $error) {
                $this->registry['messages']->setError($error);
            }
        }

        $data['module'] = $request->get($this->registry['module_param']);
        $data['files'] = $result;
        $data['errors'] = !empty($errors) ? $this->getMessagesForAJAX('error') : '';
        $data['form_id'] = $request->get('form_id');
        $data['div_id'] = $request->get('div_id');
        $data['model_id'] = $request->get('id') ?: $request->get('id', 'get');
        $data['field_source'] = $request->get('field_source');

        if ($field_source == 'franky') {
            $data['config_id'] = $request->get('config_id');
            $data['config_num'] = $request->get('config_num');
        } else if ($field_source == 'bb') {
            $data['bb_id'] = $request->get('bb_id');
            $data['bb_num'] = $request->get('bb_num');
            $data['meta_id'] = $request->get('meta_id');
        }

        $html_content = '<script type="text/javascript">' . "\n" .
            '    data=' . json_encode($data) . ';' . "\n" .
            '    window.parent.completeData(data);' . "\n" .
            '</script>';

        print($html_content);
        exit;
    }
}

?>
