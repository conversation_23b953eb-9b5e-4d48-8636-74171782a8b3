<?php

class Nomenclatures_Categories_Add_Viewer extends Viewer {
    public $template = 'categories_add.html';

    public function prepare() {
        $this->model = $this->registry['category'];
        $this->data['category'] = $this->model;
        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;controller=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'],
                            $this->registry['action_param'], $this->action);
        $this->data['submitLink'] = $this->submitLink;

        $this->prepareTitleBar();

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        //prepare category tree
        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.categories.factory.php');
        $this->data['categories_tree'] = Nomenclatures_Categories::getTree($this->registry, array('sanitize' => true));
    }

    public function prepareTitleBar() {
        $this->data['title'] = $this->i18n('nomenclatures_categories_add_new');
    }
}

?>
