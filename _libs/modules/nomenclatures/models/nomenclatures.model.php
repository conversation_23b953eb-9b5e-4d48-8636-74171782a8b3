<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

require_once 'nomenclatures.validator.php';
require_once 'nomenclatures.dropdown.php';

/**
 * Nomenclatures model class
 */
Class Nomenclature extends Model {
    use BelongsToTrait;

    public $modelName = 'Nomenclature';

    public $counter;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->get('id')) {
            if ($registry->isRegistered('get_available_quantities')) {
                //get quantities for this nomenclature in the warehouses
                $this->getAvailableQuantities($registry->get('get_available_quantities'));
            } elseif ($registry->isRegistered('get_all_quantities')) {
                //get quantities for this nomenclature in the warehouses
                $this->getAvailableQuantities($registry->get('get_all_quantities'), true);
            }

            if ($registry->get('nomenclatures_currency')) {
                //change prices in function of the currencies
                $this->set('current_currency', $registry->get('nomenclatures_currency'), true);
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

                //round amounts according to 'gt2_rows' precision setting
                $prec = $this->registry['config']->getSectionParams('precision');

                //get sell price and currency
                $sell_price = $this->get('sell_price');
                $currency = $this->get('sell_price_currency');
                $sell_rate = 0;

                if ($sell_price) {
                    //get rate for the sell price
                    $sell_rate = Finance_Currencies::getRate($registry, $currency, $registry->get('nomenclatures_currency'));
                }

                //get delivery price and currency
                $delivery_price = $this->get('last_delivery_price');
                $currency = $this->get('last_delivery_price_currency');
                $delivery_rate = 0;
                if ($delivery_price) {
                    //get rate for the delivery price
                    $delivery_rate = Finance_Currencies::getRate($registry, $currency, $registry->get('nomenclatures_currency'));
                }

                //get average weighted delivery price and currency
                $average_price = $this->get('average_weighted_delivery_price');
                $currency = $this->get('average_weighted_delivery_price_currency');
                $average_rate = 0;
                if ($average_price) {
                    //get rate for the delivery price
                    $average_rate = Finance_Currencies::getRate($registry, $currency, $registry->get('nomenclatures_currency'));
                }

                $pfs = sprintf('%%.%dF', $prec['nom_sell_price']);
                $pfd = sprintf('%%.%dF', $prec['nom_last_delivery_price']);
                $pfa = sprintf('%%.%dF', $prec['nom_average_weighted_delivery_price']);
                $this->set('sell_price', sprintf($pfs, round($sell_price * $sell_rate, $prec['nom_sell_price'])), true);
                $this->set('last_delivery_price', sprintf($pfd, round($delivery_price * $delivery_rate, $prec['nom_last_delivery_price'])), true);
                $this->set('average_weighted_delivery_price', sprintf($pfa, round($average_price * $average_rate, $prec['nom_average_weighted_delivery_price'])), true);
                $this->set('sell_price_currency', $registry->get('nomenclatures_currency'), true);
                $this->set('last_delivery_price_currency', $registry->get('nomenclatures_currency'), true);
                $this->set('average_weighted_delivery_price_currency', $registry->get('nomenclatures_currency'), true);
            }
        }

        $this->setPermissions();

    }

    /**
     * Custom prepare method for this model
     */
    public function prepare() {
        parent::prepare();

        //get categories names for nomenclatures
        if ($this->get('id') && $this->registry->get('getCategoryNames')) {
            $this->getCategoryNames();
        }
    }

    /**
     * Gets a property value from the nomenclature model
     * Some of the properties are specific
     *
     * @param string $property - the property name
     * @return mixed - the property value
     */
    public function get($property) {
        switch ($property) {
            case 'leftmost_category':
            case 'category':
                if (!isset($this->properties[$property])) {
                    $this->set($property, $this->getCategory(), true);
                }
                break;
        }

        return parent::get($property);
    }

    /**
     * Gets ONLY ONE category (ID) of all nomenclature categories according to specified algorithm
     *
     * @param string $algorithm - name of the algorithm (leftmost)
     * @param int $category - the category ID
     */
    public function getCategory($algorithm = 'leftmost') {
        if (!$this->get('id')) {
            return 0;
        }
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        switch ($algorithm) {
            case 'leftmost':
            default:
                $query = 'SELECT nc.id' . "\n" .
                         'FROM ' . DB_TABLE_NOM_CATS . ' as ncats' . "\n" .
                         'JOIN ' . DB_TABLE_CATEGORIES . ' nc' . "\n" .
                         '  ON ncats.cat_id = nc.id' . "\n" .
                         'WHERE ncats.parent_id = ' . $this->get('id') . "\n" .
                         'ORDER BY nc.`left` DESC';
                $category = $this->registry['db']->GetOne($query);
        }
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $category;
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'nomenclatures', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Nomenclature->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Nomenclature ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        // nomenclatures have no "attachments" right so check visibilty of "attachments" layout
        if ($action == 'attachments') {
            require_once(PH_MODULES_DIR . 'layouts/models/layouts.factory.php');
            $attachments_layout = Layouts::searchOne($this->registry,
                                    array('where' => array('l.model="' . $this->modelName . '"',
                                                           'l.model_type="' . $this->get('type') . '"',
                                                           'l.`system`=1',
                                                           'l.keyname="attachments"'),
                                          'sanitize' => true));
            return $attachments_layout && in_array($attachments_layout->get('id'), $this->getPermittedLayouts('view'));
        }

        return PermissionsChecker::getCurrentUserInstance()->isAllowed(
            $this,
            $action,
            $modulePermissionsKey,
            $force
        );
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - performed action with the model
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        // Get some commonly used vars
        $id   = $this->get('id');
        $code = $this->get('code');

        parent::validate($action);
        if (!$id && !$this->get('type')) {
            $this->raiseError('error_no_type', 'type');
        }

        if (!$code) {
            if (!$id) {
                $code = $this->setCode();
            } elseif ($this->isDefined('code')) {
                $this->raiseError('error_no_code', 'code', null, array($this->getLayoutName('code')));
            }
        }
        if ($code) {
            // If there are any other nomenclatures with the same code
            $query = 'SELECT `id` FROM `' . DB_TABLE_NOMENCLATURES .'` WHERE `code`= \'' . General::slashesEscape($code) . '\'';
            if ($id) {
                $query .= " AND `id` != {$id}";
            }
            $query .= ' LIMIT 1';
            $code_exists = $this->registry['db']->GetOne($query);
            if ($code_exists) {
                // Set an error
                $code_layout_name = $this->getLayoutName('code');
                $this->raiseError('error_code_not_unique', 'code', null, array($code_layout_name, $code_layout_name));
            }
        }

        if ($this->isDefined('name') && !$this->get('name')) {
            $this->raiseError('error_no_name', 'name', null, array($this->getLayoutName('name')));
        }

        // validate additional vars
        $this->validateVars();

        return $this->valid;
    }

    /**
     * Sets code
     *
     * @return string|bool - code, false - existing code
     */
    public function setCode() {
        if ($this->get('code')) {
            return false;
        }

        $tablename = DB_TABLE_NOMENCLATURES;
        /*
        $section = 'nomenclatures';
        $leading_zeros = $this->registry['config']->getParam($section, 'auto_code_leading_zeros');
        $suffix = $this->registry['config']->getParam($section, 'auto_code_suffix');
        */

        //get the settings from the nomenclature type
        if ($this->get('type')) {
            $query = 'SELECT * FROM ' . DB_TABLE_NOMENCLATURES_TYPES .
                     ' WHERE `id`="' . $this->get('type') . '" LIMIT 1';
            list($row) = $this->registry['db']->GetAll($query);
            $leading_zeros = $row['auto_code_leading_zeros'];
            $suffix = $row['auto_code_suffix'];
        } else {
            return false;
        }

        //set default values for leading zeros and suffix
        if (empty($leading_zeros)) {
            $leading_zeros = 4;
        }
        if (empty($suffix)) {
            $suffix = 'NOM';
        }

        $where = array();
        if ($suffix) {
            //get the last code with such suffix
            $where[] = 'code REGEXP "[0-9]{' . $leading_zeros . ',}' . $suffix . '"';
        } else {
            //get the last code with such digits length
            $where[] = 'LENGTH(code) = ' . $leading_zeros;
            $where[] = 'CONVERT(code, SIGNED INTEGER) IS NOT NULL';
            $where[] = 'CONVERT(code, SIGNED INTEGER) != 0';
        }

        $query = 'SELECT code FROM ' . $tablename .
            ' WHERE ' . implode(' AND ', $where) .
            ' ORDER BY ' . ($suffix ? 'CONVERT(REPLACE(code, "' . $suffix . '", ""), SIGNED INTEGER)' : 'code') . ' DESC' .
            ' LIMIT 1';

        $row = $this->registry['db']->getOne($query);

        if (!$row) {
            //no number found try to get the last code
            $query = 'SELECT code FROM ' . $tablename .
                ' ORDER BY code desc LIMIT 1';
            $row = $this->registry['db']->getOne($query);
        }

        if ($row) {
            $next_increment = (int)preg_replace('#'.preg_quote($suffix, '$#').'#', '', $row) + 1;
        } else {
            $next_increment = 1;
        }

        $code = sprintf('%0' . $leading_zeros . 'd', $next_increment) . $suffix;
        $this->set('code', $code, true);

        return $code;
    }

    /**
     * Gets counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            if ($sanitize_after = $this->isSanitized()) {
                $this->unsanitize();
            }

            require_once 'nomenclatures.counters.factory.php';
            $filters = array('where' => array('nt.id = \'' . $this->get('type') . '\'',
                                              'nc.deleted IS NOT NULL'),
                             'sanitize' => true);
            $this->counter = Nomenclatures_Counters::searchOne($this->registry, $filters);

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Sets number to model
     *
     * @return int - result of the operation
     */
    public function setNumber() {
        $this->set('added', '', true);

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_NOMENCLATURES . "\n" .
                  'SET num = ?' . "\n" .
                  'WHERE id = ' . $this->get('id');
        $result = $this->registry['db']->Execute($query1, array($this->getNum()));

        return $result;
    }

    /**
     * Check whether model should get a number
     *
     * @return bool - result of the operation
     */
    public function checkGetNum() {
        if (!$this->isActivated() || !$this->get('type') || $this->get('num') ||
        !$this->registry['db']->GetOne('SELECT counter FROM ' . DB_TABLE_NOMENCLATURES_TYPES . ' WHERE id = \'' . $this->get('type') . '\'')) {
            return false;
        }

        if (!$this->get('id')) {
            return true;
        } else {
            return $this->registry['db']->GetOne('SELECT active FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE id = \'' . $this->get('id') . '\'') === '0';
        }
    }

    /**
     * Gets number for model
     *
     * @param bool $force - force getting a number even when model has one
     * @return string - number of model or empty string when type does not use counter
     */
    public function getNum($force = false) {
        if ($this->get('num') && !$force) {
            return $this->get('num');
        }

        if (!$this->isActivated()) {
            return '';
        }

        //get the counter assigned to the type of financial document
        $this->getCounter();

        if (!$this->counter) {
            return '';
        }

        //create extender to expand the formula components
        $extender = new Extender;

        //get next number from db and lock the counter for update to guarantee unique next number
        $extender->add('num', sprintf('%0' . $this->counter->get('leading_zeroes') . 'd', $this->counter->getNextNumber()));

        // increment the counter
        $this->counter->increment();

        if ($this->counter->get('prefix_used')) {
            $extender->add('prefix', $this->counter->get('prefix'));
        }

        if ($this->counter->get('code_suffix')) {
            $extender->add('code_suffix',
                $this->registry['db']->GetOne('SELECT auto_code_suffix FROM ' . DB_TABLE_NOMENCLATURES_TYPES . ' WHERE id = \'' . $this->get('type') . '\''));
        }

        if ($this->counter->get('user_code')) {
            $extender->add('user_code', $this->registry['currentUser']->get('code'));
        }

        if ($this->counter->get('added_used')) {
            $extender->add('added', General::strftime($this->counter->get('date_format'),
                ($this->get('added') ? strtotime($this->get('added')) : '')));
        }

        $num = $extender->expand($this->counter->get('formula'));

        $delimiter = $this->counter->get('delimiter');
        if ($delimiter) {
            //remove repeating delimiters
            $num = preg_replace('#' . preg_quote($delimiter . $delimiter) . '#', $delimiter, $num);
            $num = preg_replace('#' . preg_quote($delimiter) . '$#', '', $num);
            $num = preg_replace('#^' . preg_quote($delimiter) . '#', '', $num);
        }

        if ($this->slashesEscaped) {
            $num = General::slashesEscape($num);
        }

        $this->set('num', $num, true);

        return $this->get('num');
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {

                //assign categories
                $this->assignCategories();

                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {

        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['type']     = sprintf("type=%d", $this->get('type'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_NOMENCLATURES . "\n" .
                  'SET ' . implode(",\n", $set) . "\n";
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new nomenclature base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        $result = true;
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
            $result = false;
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        if ($result) {
            $result = $this->updateI18N() && $this->saveNomPriceUpdate();
            if (!$result) {
                $db->FailTrans();
            }
        }

        if ($result && $this->isDefined('vars')) {
            //save additional variables
            $result = $this->replaceVars();
            if (!$result) {
                $db->FailTrans();
            }
        }

        $request = $this->registry['request'];
        $added_files = array();
        $additional_descriptions = $request->get('a_file_descriptions');
        $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

        if ($result && !empty($additional_files)) {
            //save attachments
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';
            $this->set('added', date('Y-m-d H:i:s'), true);
            foreach ($additional_files['name'] as $idx => $name) {
                if ($additional_files['tmp_name'][$idx]) {
                    $file = array(
                        'name'     => $additional_files['name'][$idx],
                        'type'     => $additional_files['type'][$idx],
                        'tmp_name' => $additional_files['tmp_name'][$idx],
                        'error'    => $additional_files['error'][$idx],
                        'size'     => $additional_files['size'][$idx]);
                } else {
                    $file = array();
                }
                $params = array(
                    'name'        => $additional_files['name'][$idx] ?? '',
                    'description' => $additional_descriptions[$idx] ?? '',
                    'revision'    => '',
                    'permission'  => 'all');

                if (!empty($file) || $params['name']) {
                    // Clone the current model to sanitize it and send it to the attachFile method
                    $nomenclature = clone $this;
                    $nomenclature->sanitize();
                    if (!Files::attachFile($this->registry, $file, $params, $nomenclature)) {
                        $error_type = '';
                        if (empty($file)) {
                            $error_type = $error_type . $this->i18n('error_attachments_file');
                        }
                        if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                        if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                        $erred_added_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));
                    } else {
                        $success_added_files[] = $idx;
                    }
                }
                $this->unsanitize();
                $added_files[$idx] = $params;
            }
        }

        // do not fail transaction on file upload error
        /*if ($added_files && !empty($erred_added_files)) {
            $db->FailTrans();
            $result = false;
        }*/

        // bb and import specific
        if ($result && $this->get('model_id') && $this->get('model_id') != $this->get('id')) {
            // replace temporary model_id with real id of model
            $this->updateModelIdAfterAdd();
            $this->updateModelFiles();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // If the transaction has failed
        if ($dbTransError) {
            // Remove the id
            $this->set('id', '', true);
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        $old_awdp = $db->GetRow("
            SELECT average_weighted_delivery_price, average_weighted_delivery_price_currency
            FROM " . DB_TABLE_NOMENCLATURES . "
            WHERE id = '{$this->get('id')}'
        ") ?: array();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_NOMENCLATURES . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        $result = true;
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
            $result = false;
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        if ($result) {
            $result = $this->updateI18N() && $this->saveNomPriceUpdate($old_awdp);
            if (!$result) {
                $db->FailTrans();
            }
        }

        if ($result && $this->isDefined('vars')) {
            //save additional variables
            $result = $this->replaceVars();
            if ($result) {
                $this->updateModelFiles();
            } else {
                $db->FailTrans();
            }
        }

        $request = $this->registry['request'];
        $added_files = array();
        $additional_descriptions = $request->get('a_file_descriptions');
        $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

        if ($result && !empty($additional_files)) {
            //save attachments
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';
            $this->set('added', date('Y-m-d H:i:s'), true);
            foreach ($additional_files['name'] as $idx => $name) {
                if ($additional_files['tmp_name'][$idx]) {
                    $file = array(
                        'name'     => $additional_files['name'][$idx],
                        'type'     => $additional_files['type'][$idx],
                        'tmp_name' => $additional_files['tmp_name'][$idx],
                        'error'    => $additional_files['error'][$idx],
                        'size'     => $additional_files['size'][$idx]);
                } else {
                    $file = array();
                }
                $params = array(
                    'name'        => $additional_files['name'][$idx] ?? '',
                    'description' => $additional_descriptions[$idx] ?? '',
                    'revision'    => '',
                    'permission'  => 'all');

                if (!empty($file) || $params['name']) {
                    // Clone the current model to sanitize it and send it to the attachFile method
                    $nomenclature = clone $this;
                    $nomenclature->sanitize();
                    if (!Files::attachFile($this->registry, $file, $params, $nomenclature)) {
                        $error_type = '';
                        if (empty($file)) {
                            $error_type = $error_type . $this->i18n('error_attachments_file');
                        }
                        if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                        if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                        $erred_added_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));
                    } else {
                        $success_added_files[] = $idx;
                    }
                }
                $this->unsanitize();
                $added_files[$idx] = $params;
            }
        }

        // do not fail transaction on file upload error
        /*if ($added_files && !empty($erred_added_files)) {
            $db->FailTrans();
            $result = false;
        }*/

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->isDefined('code')) {
            $set['code'] = sprintf("code='%s'", $this->get('code'));
        }
        if ($this->checkGetNum()) {
            $set['num'] = sprintf("num='%s'", $this->getNum());
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('subtype')) {
            $set['subtype'] = sprintf("`subtype`='%s'", $this->get('subtype'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        $prec = $this->registry['config']->getSectionParams('precision');
        if ($this->isDefined('last_delivery_price')) {
            $set['last_delivery_price'] = sprintf("`last_delivery_price` = '%s'", round($this->get('last_delivery_price'), $prec['nom_last_delivery_price']));
            $set['last_delivery_price_currency'] = sprintf("`last_delivery_price_currency` = '%s'", ($this->get('last_delivery_price_currency') ?: $this->getMainCurrency()));
        }
        if ($this->isDefined('average_weighted_delivery_price')) {
            $set['average_weighted_delivery_price'] = sprintf("`average_weighted_delivery_price` = '%s'", round($this->get('average_weighted_delivery_price'), $prec['nom_average_weighted_delivery_price']));
            $set['average_weighted_delivery_price_currency'] = sprintf("`average_weighted_delivery_price_currency` = '%s'", ($this->get('average_weighted_delivery_price_currency') ?: $this->getMainCurrency()));
        }
        if ($this->isDefined('sell_price')) {
            $set['sell_price'] = sprintf("`sell_price` = '%s'", round($this->get('sell_price'), $prec['nom_sell_price']));
            $set['sell_price_currency'] = sprintf("`sell_price_currency` = '%s'", ($this->get('sell_price_currency') ?: $this->getMainCurrency()));
        }

        if (in_array($this->registry['action'], array('add', 'addquick', 'edit', 'clone'))) {
            if ($this->get('subtype') == 'commodity' && $this->get('has_batch')) {
                $set['has_batch'] = "`has_batch` = 1";
                if ($this->get('has_serial')) {
                    $set['serial'] = "`has_serial` = 1";
                } else {
                    $set['has_serial'] = "`has_serial` = 0";
                }
                if ($this->get('has_expire')) {
                    $set['has_expire'] = "`has_expire` = 1";
                } else {
                    $set['has_expire'] = "`has_expire` = 0";
                }
                if ($this->get('has_batch_code')) {
                    $set['has_batch_code'] = "`has_batch_code` = 1";
                } else {
                    $set['has_batch_code'] = "`has_batch_code` = 0";
                }
            } else {
                $set['has_batch'] = "`has_batch` = 0";
                $set['has_serial'] = "`has_serial` = 0";
                $set['has_expire'] = "`has_expire` = 0";
                $set['has_batch_code'] = "`has_batch_code` = 0";
            }
        }

        $set['modified']        = sprintf("modified=now()");
        $set['modified_by']     = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];

        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_NOMENCLATURES_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing nomenclatures i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }

        return true;
    }

    /**
     * Writes log data when user modifies average weighted delivery price and currency from interface
     *
     * @param array $old_awdp - old data when nomenclature is edited, or null when added
     * @return boolean - result of operation
     */
    public function saveNomPriceUpdate($old_awdp = null) {
        // sanitize checked data to what it should be saved in db, in case it
        // comes from non-standard source and has unexpected type (array, for example)
        $new_awdp = array(
            'average_weighted_delivery_price' =>
                is_numeric($this->get('average_weighted_delivery_price')) || is_string($this->get('average_weighted_delivery_price')) ?
                floatval($this->get('average_weighted_delivery_price')) :
                0,
            'average_weighted_delivery_price_currency' => strval($this->get('average_weighted_delivery_price_currency')),
        );

        if (!$this->get('id') || is_array($old_awdp) && !$old_awdp || ($new_awdp['average_weighted_delivery_price'] != 0 && !$new_awdp['average_weighted_delivery_price_currency'])) {
            // display error for missing currency value
            if (!$new_awdp['average_weighted_delivery_price_currency']) {
                $this->raiseError(
                    'error_notEmpty',
                    'average_weighted_delivery_price_currency',
                    0,
                    array('var_label' => $this->i18n('nomenclatures_average_weighted_delivery_price_currency'))
                );
            }
            // inconsistent data
            return false;
        } elseif (!$this->isDefined('average_weighted_delivery_price') && !$this->isDefined('average_weighted_delivery_price_currency')) {
            // not set
            return true;
        }
        if ($old_awdp === null) {
            // in add mode
            $old_awdp = array('average_weighted_delivery_price' => 0, 'average_weighted_delivery_price_currency' => '');
        }
        $prec = $this->registry['config']->getParam('precision', 'nom_average_weighted_delivery_price');

        if (
            $old_awdp['average_weighted_delivery_price_currency'] != $new_awdp['average_weighted_delivery_price_currency'] ||
            bccomp($old_awdp['average_weighted_delivery_price'], $new_awdp['average_weighted_delivery_price'], $prec) != 0
        ) {
            // log price update - other fields are not relevant to this type of change
            $price_update_params = array(
                'parent_id' => $this->get('id'),
                'old_price' => $old_awdp['average_weighted_delivery_price'],
                'currency' => $new_awdp['average_weighted_delivery_price_currency'],
                'new_price' => $new_awdp['average_weighted_delivery_price'],
                'update_type' => 'user',
                'modified_by' => $this->registry['currentUser']->get('id'),
            );
            $price_update_params = array_map(function($v, $k) { return "$k = '$v'"; }, $price_update_params, array_keys($price_update_params));
            $price_update_params[] = 'modified = NOW()';
            $query = "
                INSERT INTO " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " SET " .
                implode(", ", $price_update_params);
            $this->registry['db']->Execute($query);
            return !$this->registry['db']->HasFailedTrans();
        }
        return true;
    }

    /**
     * Gets ids of categories of model
     *
     * @return array - category ids
     */
    public function getCategories() {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $request = $this->registry['request'];
        if ($request->get('update_categories')) {
            //get categories from the the request as they are posted
            $cats = array();
            if ($request->get('categories')) {
                $cats = $request->get('categories');
            }
        } else {
            //get categories from the db
            $db = &$this->registry['db'];

            if ($this->get('id')) {
                //get the assigned categories
                $where = array("c.model='". $this->modelName ."'", sprintf("c.parent_id=%d", $this->get('id')));
            } elseif ($this->get('type')) {
                //get the default categories from the type
                $where = array("c.model='Nomenclatures_Type'", sprintf("c.parent_id=%d", $this->get('type')));
            }

            if (isset($where)) {
                $query = 'SELECT c.cat_id' . "\n" .
                         'FROM ' . DB_TABLE_NOM_CATS . ' AS c' . "\n" .
                         'WHERE ' . implode(' AND ', $where) . " \n" .
                         'ORDER BY c.cat_id' . "\n";

                $cats = $db->GetCol($query);
            } else {
                $cats = array();
            }
            $this->set('categories', $cats, true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $cats;
    }

    /**
     * Gets category names
     *
     * @return array - category names
     */
    public function getCategoryNames() {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = &$this->registry['db'];

        if (!$this->isDefined('categories')) {
            $this->getCategories();
        }

        if (!$this->get('categories')) {
            return false;
        }

        $where = array(General::buildClause("ci18n.parent_id", $this->get('categories')));

        //get categories from the db
        $query = 'SELECT ci18n.name' . "\n" .
                 'FROM ' . DB_TABLE_CATEGORIES . ' AS c' . "\n" .
                 'INNER JOIN ' . DB_TABLE_CATEGORIES_I18N . ' AS ci18n' . "\n" .
                 '  ON (c.id=ci18n.parent_id AND lang="' . $this->get('model_lang') . '")' . "\n" .
                 'WHERE ' . implode(' AND ', $where) . " \n" .
                 'ORDER BY c.left' . " \n";

        $cats = $db->GetCol($query);

        $this->set('categories_names', $cats, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $cats;
    }

    /**
     * Assigns categories to nomneclatures
     *
     * @return bool - result of the operation
     */
    public function assignCategories() {
        $db = &$this->registry['db'];
        $request = $this->registry['request'];

        //check if the model has assigned
        if (!$request->get('update_categories')) {
            return false;
        }

        if ($this->get('id')) {
            //clear all previously assigned categories for the user
            $query = 'DELETE FROM ' . DB_TABLE_NOM_CATS . "\n" .
                     'WHERE parent_id=' . $this->get('id') . ' AND model="' . $this->modelName . '"';
            $db->Execute($query);

            if ($db->ErrorMsg()) {
                $this->registry['logger']->dbError('clearing nomenclatures categories', $db, $query);
            }
        }

        if ($this->isDefined('categories')) {
            $inserts = array();
            foreach ($this->get('categories') as $cat_id) {
                //create insert statement for all categories
                $inserts[] = sprintf("(%d, %d, '%s')", $this->get('id'), $cat_id, $this->modelName);
            }

            $query2 = 'INSERT INTO ' . DB_TABLE_NOM_CATS . "\n" .
                      'VALUES ' . implode(",\n", $inserts);
            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['logger']->dbError('updating nomenclatures categories', $db, $query2);
                return false;
            }
        }

        return true;
    }

    /**
     * Clone nomenclature
     *
     * @return bool
     */
    public function cloneModel() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL

        $this->slashesEscape();

        $set = $this->prepareMainData();
        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['type']     = sprintf("type=%d", $this->get('type'));
        $set['code']     = sprintf("code='%s'", $this->setCode());

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_NOMENCLATURES . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }
        if (!$this->saveNomPriceUpdate()) {
            $db->FailTrans();
        }

        //assign categories if any
        if ($this->get('categories')) {
            $this->registry['request']->set('update_categories', 1, true);
            $this->assignCategories();
        }

        $this->slashesStrip();

        //copy variables
        $this->copyVars();
        $this->cloneBBVars();

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Copy variables when cloning a nomenclature
     * Copies vars from orig_vars to vars
     *
     * @param string $lang - when set, only multilang vars should be copied in current model lang
     * @return bool - result of the operation
     */
    public function copyVars($lang = '') {
        //gets vars assigned from the source (original) nomenclature
        $orig_vars = $this->get('orig_vars');
        // no additional variables
        if (!$orig_vars) {
            return true;
        }
        $assoc_vars = array();
        //convert variables array to associative
        foreach ($orig_vars as $var) {
            if (empty($lang) || $var['multilang']) {
                $assoc_vars[$var['name']] = $var;
            }
        }

        //gets vars of the destination nomenclature
        $this->unsetVars();
        $this->getVars();

        //compares variable names and removes the unnecessary variables
        $new_vars = $this->get('vars');

        foreach ($new_vars as $k => $var) {
            if (isset($assoc_vars[$var['name']]) && !empty($assoc_vars[$var['name']]['model_id'])) {
                $new_vars[$k]['value'] = $assoc_vars[$var['name']]['value'];
            } else {
                unset($new_vars[$k]);
            }
        }

        //store new vars in the DB
        if (!empty($new_vars)) {
            //assign newly defined vars
            $this->set('vars', $new_vars, true);
            $this->registry->set('edit_all', true, true);
            if ($lang) {
                // save vars as if action is translate (only multilang ones and only in current model lang)
                $this->set('translate_multilang', true, true);
            }
            return $this->replaceVars();
        } else {
            return true;
        }
    }


    /**
     * Get nomenclatures files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        //require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        //$ids_where .= Files::getAdditionalWhere($this->registry);

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get nomenclature attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Nomenclature\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted =  0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }


    /**
     * Translates some i18n fields
     *
     * @return bool - result of the operation
     */
    public function getDefaultTranslations() {
        $db = &$this->registry['db'];
        $model_lang = $this->registry['lang'];

        //select clause
        $sql['select'] = 'SELECT d.*, di18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  dti18n.name as type_name, ' . "\n" .
                         '  gi18n.name as group_name ' . "\n";
        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_NOMENCLATURES . ' AS d' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS di18n' . "\n" .
                       '  ON (d.id=di18n.parent_id AND di18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to nomenclature group
                       'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                       '  ON (d.group=gi18n.parent_id AND gi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to nomenclature types i18n
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS dti18n' . "\n" .
                       '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n";
        $sql['where'] = 'WHERE d.id=' . $this->get('id') . "\n";

        $query = implode("\n", $sql);
        $records = $db->GetRow($query);

        $this->set('default_translations', $records, true);

        return $records;
    }

    /**
     * Gets available quantities of nomenclature in some or all warehouses
     * and sets them as properties of model.
     *
     * @param string $warehouses - comma-separated list of ids of warehouses or a customly formatted value
     * @param boolean $all - if true, include reserved quantities, otherwise exclude them
     */
    public function getAvailableQuantities($warehouses = '', $all = false) {

        // get precision for GT2 quantity
        $gt2_quantity_precision = $this->registry['config']->getParam('precision', 'gt2_quantity');
        $gt2_quantity_prec_format = '%.' . $gt2_quantity_precision . 'F';
        $quantities = array();
        if ($warehouses) {
            if (preg_match('#(\d+)_(\d+)_(bank|cash|cheque)_\d+#', $warehouses, $matches)) {
                //get warehouses by company and office extracted from company_data value
                $warehouses = 'SELECT id FROM ' . DB_TABLE_FINANCE_WAREHOUSES .
                              ' WHERE company = ' . $matches[1] . ' AND office = ' . $matches[2];
                $warehouses = $this->registry['db']->GetCol($warehouses);
            } elseif (preg_match('#office_(\d+)#', $warehouses, $matches)) {
                //get warehouses by office only
                $warehouses = 'SELECT id FROM ' . DB_TABLE_FINANCE_WAREHOUSES . ' WHERE office = ' . $matches[1];
                $warehouses = $this->registry['db']->GetCol($warehouses);
            } else {
                $warehouses = preg_split('#\s*,\s*#', $warehouses);
            }
        } elseif (!$this->registry->get('require_warehouse')) {
            //get ids of all warehouses where this article is present
            $query = 'SELECT DISTINCT(parent_id)' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . " \n" .
                     'WHERE nomenclature_id = ' . $this->get('id') . "\n";
            $warehouses = $this->registry['db']->GetCol($query);
        }
        if ($warehouses) {
            foreach ($warehouses as $wh) {
                $query = 'SELECT ROUND(SUM(quantity), ' . $gt2_quantity_precision . ') AS quantity' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . " \n" .
                         'WHERE nomenclature_id = ' . $this->get('id') . "\n" .
                         '    AND parent_id = "' . $wh. '"';
                $quantities[$wh] = $this->registry['db']->GetOne($query);
            }
        } else {
            //invalid $warehouses(function parameter) has been provided
            // or no warehouses were found
            $this->set('total_available_quantity', '0', true);
            return true;
        }

        foreach ($quantities as $wid => $quantity) {
            if (empty($all)) {
                $query = 'SELECT fwd.id, ROUND(gt2.quantity, ' . $gt2_quantity_precision . ') AS quantity' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                         '  ON fwd.id=gt2.model_id AND model="Finance_Warehouses_Document"' . "\n" .
                         'WHERE fwd.type=' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION . "\n" .
                         '  AND fwd.status="locked"' . "\n" .
                         '  AND fwd.warehouse="' . $wid . '"' . "\n" .
                         '  AND gt2.article_id=' . $this->get('id');
                $res = $this->registry['db']->GetAssoc($query);

                foreach ($res as $cr_id => $cr_quantity) {
                    $quantity -= $cr_quantity;
                    $query = 'SELECT SUM(ROUND(gt2.quantity, ' . $gt2_quantity_precision . '))' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' as fwd' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                             '  ON fwd.id=gt2.model_id AND model="Finance_Warehouses_Document"' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                             '  ON frr.parent_id=fwd.id AND frr.parent_model_name="Finance_Warehouses_Document"' . "\n" .
                             '    AND frr.link_to_model_name="Finance_Warehouses_Document"' . "\n" .
                             'WHERE fwd.type=' . PH_FINANCE_TYPE_COMMODITIES_RELEASE . "\n" .
                             '  AND frr.link_to=' . $cr_id . "\n" .
                             '  AND gt2.article_id=' . $this->get('id');
                    $rel = $this->registry['db']->GetOne($query);
                    if ($rel) {
                        $quantity += $rel;
                    }
                }
            }

            $quantity = sprintf($gt2_quantity_prec_format, $quantity);
            //we will set the total quantity too
            //this will be used for available_quantity gt2 field autocompletion
            $this->set('total_available_quantity', $quantity + $this->get('total_available_quantity'), true);
            $this->set('warehouse' . $wid . '_available', $quantity, true);
        }
    }

    /**
     * Gets related to the model records
     *
     * @return array - array with related records
     */
    public function getRelatedRecords() {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $related = array();
        $registry = &$this->registry;
        $db = &$registry['db'];

        //gets which modules of optional related records should be displayed
        list($related_records_modules) = $this->getRelatedRecordsModules();

        // do not display modules that user has no access to
        $rights = $registry['currentUser']->get('rights');

        foreach ($related_records_modules as $rrm) {
            $model_factory = implode('_', array_map('ucfirst', explode('_', $rrm)));

            //get related records
            $result = $this->getCstmRelatives(General::plural2singular($model_factory));
            if (!empty($result)) {
                // search once again to get only ids for models of active types
                // that user has 'list' permissions for
                $module = preg_split('#_#', $rrm, 2);
                $controller = count($module) == 2 ? $module[1] : $module[0];
                $module = $module[0];
                //include factory file
                $file = PH_MODULES_DIR . $module . '/models/' . $module .
                    ($controller != $module ? '.' . $controller : '') . '.factory.php';
                require_once $file;

                $alias = $model_factory::getAlias($module, $controller);
                $filters = array('sanitize' => true,
                                 'where' => array($alias . '.id IN (' . implode(', ', $result) . ')'),
                                 'check_module_permissions' => $module);
                if ($module == 'contracts') {
                    $filters['where'][] = $alias . '.subtype!="original"';
                } elseif (preg_match('#^finance_(incomes|expenses)_reasons$#i', $rrm)) {
                    // exclude inactive records
                    $filters['where'][] = $alias . '.active = 1';
                }
                $result = $model_factory::getIds($this->registry, $filters);
            }

            $related[$rrm] = array(
                'name' => $rrm,
                'label' => $this->i18n('menu_' . $rrm),
                'link' => '#related_subpanel_nomenclature' . $this->get('id'),
                'ids' => is_array($result) ? $result : array()
            );
        }

        if (isset($rights[$this->module]['communications']) && $rights[$this->module]['communications'] != 'none' && $this->checkPermissions('communications')) {
            if (isset($rights[$this->module]['comments']) && $rights[$this->module]['comments'] != 'none' && $this->checkPermissions('comments')) {
                //get related comments
                $query = 'SELECT id' . "\n" .
                         'FROM `' . DB_TABLE_COMMENTS . "`\n" .
                         'WHERE deleted_by = 0 AND model = \'Nomenclature\' AND model_id = \'' . $this->get('id') . '\'' . ($registry['currentUser']->get('is_portal') ? ' AND is_portal = \'1\'' : '');

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=nomenclatures&amp;';
                $link .= 'nomenclatures=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=comments';
                $related['comments'] = array('name' => 'comments',
                                             'label' => $this->i18n('nomenclatures_comments'),
                                             'link' => $link,
                                             'ids' => is_array($result) ? $result : array());
            }

            if (isset($rights[$this->module]['emails']) && $rights[$this->module]['emails'] != 'none' && $this->checkPermissions('emails')) {
                //get related e-mails
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model = \'Nomenclature\' AND model_id = ' . $this->get('id') . ' AND `system`=\'0\'' . "\n" .
                         'GROUP BY code';

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=nomenclatures&amp;';
                $link .= 'nomenclatures=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=emails';
                $related['emails'] = array('name' => 'email',
                                           'label' => $this->i18n('nomenclatures_emails'),
                                           'link' => $link,
                                           'ids' => is_array($result) ? $result : array());
            }

            if ($this->checkPermissions('minitasks')) {
                //get related mini tasks
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_MINITASKS . ' AS m' . "\n" .
                         'WHERE model = \'Nomenclature\' AND model_id=' . $this->get('id');

                $current_user_id = $registry['currentUser']->get('id');
                $rights = $registry['currentUser']->get('rights');
                $current_right = isset($rights['minitasks']['list']) ? $rights['minitasks']['list'] : '';
                unset($rights);
                if ($current_right == 'mine') {
                    $query .= " AND m.assigned_to=$current_user_id ";
                } elseif ($current_right == 'group') {
                    $query .= " AND (m.added_by=$current_user_id OR m.assigned_to=$current_user_id) ";
                } elseif ($current_right != 'all') {
                    $query .= ' AND 0';
                }

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=nomenclatures&amp;';
                $link .= 'nomenclatures=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=minitasks';
                $related['minitasks'] = array('name' => 'minitasks',
                                              'label' => $this->i18n('menu_minitasks'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }
        }

        if ($this->checkPermissions('attachments')) {
            //get related files
            $query = 'SELECT f.id' . "\n" .
                     'FROM `' . DB_TABLE_FILES . '` AS f ' . "\n" .
                     'WHERE f.deleted_by = 0 AND f.model = \'Nomenclature\' AND f.model_id = ' . $this->get('id');

            $result = $db->GetCol($query);
            $link = '#attachments';
            $related['files'] = array('name' => 'attachments',
                                      'label' => $this->i18n('attachments'),
                                      'link' => $link,
                                      'ids' => is_array($result) ? $result : array());
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $related;
    }

    /**
     * Get ids of records of specified kind that current model is related to in
     * their basic variables - these are specific fields for some system nomenclature types.
     *
     * @param string $module - module(+controller) of related records
     * @return array - ids of related records
     */
    public function getBasicRelatives($module) {
        $related = array();

        // define basic vars to search in and for
        $field = '';
        $id_field = 'id';
        $table = constant('DB_TABLE_' . strtoupper($module));
        $db = &$this->registry['db'];

        if ($this->get('type_keyword') == PH_NOMENCLATURES_TYPE_KEYWORD_TRADEMARK) {
            if ($module == 'customers') {
                $table = DB_TABLE_CUSTOMERS_TRADEMARKS;
                $field = 'trademark_id';
                $id_field = 'parent_id';
            } elseif (in_array('trademark', $db->MetaColumnNames($table))) {
                $field = 'trademark';
            }
        } elseif ($this->get('type_keyword') == 'cd_reasons' && $module == 'finance_incomes_reasons') {
            $field = 'reason';
        } elseif ($this->get('type_keyword') == 'no_vat_reason' &&
            preg_match('#^finance_$#', $module) &&
            in_array('total_no_vat_reason', $db->MetaColumnNames($table))
        ) {
            $field = 'total_no_vat_reason';
        }

        if ($field) {
            $related = $this->registry['db']->GetCol(
                "SELECT $id_field FROM $table WHERE $field = '{$this->get('id')}'"
            );
        }

        return $related;
    }

    /**
     * Gets default percentage distribution of nomenclature by items
     *
     * @param string $type - 'income'/'expense' to return only one of arrays, empty string to return both arrays
     * @return array $items_distribution - result of operation
     */
    public function getItemsDefaultDistributionValues($type = '') {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $precision = $this->registry['config']->getParam('precision', 'finance_analysis_percentage');

        $query = 'SELECT nd.item, ROUND(nd.percentage, ' . $precision . ') AS percentage, fai.type' . "\n" .
                 '  FROM ' . DB_TABLE_NOMENCLATURES_DISTRIBUTION . ' AS nd' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS . ' AS fai' . "\n" .
                 '  ON nd.item = fai.id' . "\n" .
                 '  WHERE nd.parent_id = ' . $this->get('id') . "\n" .
                 (in_array($type, array('income', 'expense')) ? ' AND fai.type = "' . $type . '"' . "\n" : '') .
                 '  ORDER BY nd.id ASC';
        $records = $this->registry['db']->GetAll($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        $income = array();
        $expense = array();

        foreach ($records as $row) {
            if ($row['type'] == 'income') {
                if (!$income) {
                    $income = array('items' => array(), 'percentage' => array());
                }
                $income['items'][] = $row['item'];
                $income['percentage'][] = $row['percentage'];
            } elseif ($row['type'] == 'expense') {
                if (!$expense) {
                    $expense = array('items' => array(), 'percentage' => array());
                }
                $expense['items'][] = $row['item'];
                $expense['percentage'][] = $row['percentage'];
            }
        }

        if ($type == 'income') {
            $items_distribution = $income;
        } elseif ($type == 'expense') {
            $items_distribution = $expense;
        } else {
            $items_distribution = array($income, $expense);
        }

        return $items_distribution;
    }

    /**
     * Deletes all items or all but listed in $exclude_items
     * from default percentage distribution of nomenclature.
     *
     * @param array $exclude_items - array of ids
     * @return bool - result of operation
     */
    public function deleteItemsDefaultDistributionValues($exclude_items = array()) {
        $query = 'DELETE FROM ' . DB_TABLE_NOMENCLATURES_DISTRIBUTION . "\n" .
                 '  WHERE parent_id=' . $this->get('id');
        if ($exclude_items) {
            $query .= ' AND item NOT IN (' . implode(', ', $exclude_items) . ')';
        }
        $result = $this->registry['db']->Execute($query);

        return $result;
    }

    /**
     * Updates default percentage distribution of nomenclature by items
     *
     * @return bool - result of operation
     */
    public function updateItemsDefaultDistributionValues() {

        //round every percentage to a certain digit after decimal point
        $precision = $this->registry['config']->getParam('precision', 'finance_analysis_percentage');

        $percentage_income = $this->get('percentage_income') ? $this->get('percentage_income') : array();
        foreach ($percentage_income as $idx => $value) {
            $percentage_income[$idx] = round($value, $precision);
        }
        $total_percentage_income = round(array_sum($percentage_income), $precision);

        $percentage_expense = $this->get('percentage_expense') ? $this->get('percentage_expense') : array();
        foreach ($percentage_expense as $idx => $value) {
            $percentage_expense[$idx] = round($value, $precision);
        }
        $total_percentage_expense = round(array_sum($percentage_expense), $precision);

        $percentage = array_merge($percentage_income, $percentage_expense);
        $items = array_merge(($this->get('items_income') ? $this->get('items_income') : array()),
                             ($this->get('items_expense') ? $this->get('items_expense') : array()));

        if ($total_percentage_income !== (float)100 || $total_percentage_expense !== (float)100 ||
        count($items) != count($percentage) || count(array_unique($items)) != count($items) || in_array('', $items)) {
            $this->raiseError('error_total_items_percentage', '');
            return false;
        }

        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //delete items which are not in POST
        $this->deleteItemsDefaultDistributionValues($items);

        $insert = array();
        $update = array();
        $insert['parent_id'] = sprintf("parent_id=%d", $this->get('id'));

        foreach ($items as $key => $value) {
            $insert['item'] = sprintf('item=%d', $value);
            $insert['percentage'] = sprintf('percentage=%.' . $precision . 'F', $percentage[$key]);
            $update['percentage'] = $insert['percentage'];

            $query = 'INSERT INTO ' . DB_TABLE_NOMENCLATURES_DISTRIBUTION . "\n" .
                     'SET ' . implode(', ', $insert) . "\n" .
                     'ON DUPLICATE KEY UPDATE ' . "\n" .
                     implode(', ', $update);
            $db->Execute($query);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Checks if current model is present in any warehouse documents.
     * If true, sets flag to model that batch parameters cannot be modified.
     */
    public function checkBatchDataEdit() {

        $query = 'SELECT id FROM ' . DB_TABLE_GT2_DETAILS . "\n" .
                 'WHERE model = "Finance_Warehouses_Document" AND article_id = "' . $this->get('id') .'"';
        $processed = $this->registry['db']->GetOne($query);
        if ($processed) {
            $this->set('disable_batch_edit', true, true);
        }
    }

    /**
     * Copy all bb data of original model to current model (used when cloning a model)
     *
     * @return bool - result of the operation
     */
    public function cloneBBVars() {
        // get bb variables for caption rows from parent model
        $id = $this->get('id');
        $this->set('id', $this->get('origin_id'), true);
        $add_bb_vars = $this->getBBFields();
        $this->set('id', $id, true);

        // no bb vars for model or no bb data saved - nothing to copy
        if (!$add_bb_vars) {
            return true;
        }

        $ids = array();
        //get ids of bb fields as 'bb_name', 'bb_quantity' ...
        foreach ($add_bb_vars as $key => $var) {
            $ids[] = $var['id'];
        }
        //get saved bb configs for the original model
        $query = "SELECT * "
            . " FROM " . DB_TABLE_BB
            . " WHERE model='Nomenclature' AND model_id=" . $this->get('origin_id');
        $records = $this->registry['db']->GetAll($query);

        foreach ($records as $rec) {
            //insert new bb row for new model
            $query = "INSERT INTO " . DB_TABLE_BB
                . " (model, model_type, model_id, bb_num, meta_id, params, added, added_by) VALUES ("
                . "'" . $rec['model'] . "', '" . $rec['model_type'] . "', "
                . $this->get('id')
                . "," . $rec['bb_num'] . ", " . $rec['meta_id'] . ", '" . General::slashesEscape($rec['params']) . "', now(),"
                . $this->registry['currentUser']->get('id') . ')';
            $this->registry['db']->Execute($query);

            $id = $this->registry['db']->Insert_Id();
            //insert bb fields values for inserted bb row
            $query = "INSERT INTO " . DB_TABLE_NOMENCLATURES_CSTM . "\n"
                . " (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) SELECT " . "\n"
                . $this->get('id') . ", var_id, " . $id . " , value, now(), " . "\n"
                . $this->registry['currentUser']->get('id') . ", now(), " . "\n"
                . $this->registry['currentUser']->get('id') . ", lang " . "\n"
                . " FROM " . DB_TABLE_NOMENCLATURES_CSTM . "\n"
                . " WHERE num=" . $rec['id'] . "\n"
                . " AND model_id=" . $this->get('origin_id') . "\n"
                . " AND var_id in (" . implode(',', $ids) . ")";
            $this->registry['db']->Execute($query);

            // clone cstm relatives for both main and inner bb vars
            $query = 'INSERT INTO ' . DB_TABLE_CSTM_RELATIVES . ' (model, model_id, cstm_model, cstm_model_id, var_id, num, lang)' . "\n" .
                'SELECT cr.model, ' . $this->get('id') . ', cr.cstm_model, cr.cstm_model_id, cr.var_id, ' . $id . ', lang' . "\n" .
                'FROM ' . DB_TABLE_CSTM_RELATIVES . ' AS cr' . "\n" .
                'JOIN ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                '  ON cr.var_id=fm.id' . "\n" .
                'WHERE cr.model=\'' . $this->modelName . '\'' . "\n" .
                '  AND cr.model_id=\'' . $this->get('origin_id') . '\'' . "\n" .
                '  AND fm.bb=\'' . $rec['bb_num'] . '\'' . "\n" .
                '  AND cr.num=\'' . $rec['id'] . '\'';
            $this->registry['db']->Execute($query);
        }

        return !$this->registry['db']->HasFailedTrans();
    }

    /**
     * Get the main currency of the installation
     *
     * @return string
     */
    private function getMainCurrency() {
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        return Finance_Currencies::getMain($this->registry);
    }

    /**
     * Gets value for export
     *
     * @param array $field - array as prepared by the Outlook (getOutlookSettings)
     * @param bool $force - IMPORTANT: set to true to get data without formatting, set to 0 to get autocompleter as link, if having such settings
     * @param bool $ignore_permissions - if true, do not check layout permission (when getting data for autocompleter fill options, for example)
     * @return mixed - the value of the variable
     */
    public function getExportVarValue($field, $force = false, $ignore_permissions = false) {
        switch($field['name']) {
            case 'categories':
                $categories = $this->getCategoryNames();
                return !empty($categories) && is_array($categories) ? implode(", ", $categories) : '';
                break;
            default:
                return parent::getExportVarValue($field, $force = false, $ignore_permissions = false);
        }
    }
}
