<?php

/**
 * Custom plugin to prepare data for print/genaration for VEOLIA
 */
class Custom_Factory extends Model_Factory
{
    public $folderName = 'veolia';

    public static Registry $registry;
    public static Model $model;
    public static Pattern $pattern;
    public static Extender $extender;

    public static \PhpOffice\PhpWord\TemplateProcessor $templateProcessor;
    public static array $settings;
    public static string $filePath;

    /**
     * Gets DOCS template
     *
     * @return File $file - file found
     * @throws \Exception
     */
    private static function _getDOCXTemplate():File {
        $files = self::$pattern->getAttachments(array('f.filename LIKE \'%.docx\''));
        if (empty($files)) {
            throw new \Exception('No template found!');
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            throw new \Exception('File does not exist!');
        }

        return $file;
    }

    /**
     * Parse some custom placeholders for document type 13
     *
     * @param object $registry - registry object
     * @param object $model - finance_incomes_reason model (the invoice)
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function prepareData(&$registry, &$model, &$pattern, &$params = array())
    {
        self::$registry = &$registry;
        self::$model = &$model;
        self::$pattern = &$pattern;
        self::$settings = $params['settings'];
        self::$extender = clone $model->extender;
        $lang = self::$registry['lang'];

        try {
            $templateFile = self::_getDOCXTemplate();
        } catch (\Exception $e) {
            //ToDo: report error
            return false;
        }


        self::$extender->setOpenTag('${');
        self::$extender->setCloseTag('}');
        self::$templateProcessor = new \PhpOffice\PhpWord\TemplateProcessor($templateFile->get('path'));

        self::replaceEmployeesList();

        self::replacePremisesList();

        self::replaceSinglePlaceholders();

        self::$filePath = tempnam(ini_get('upload_tmp_dir'), 'docx');
        self::$templateProcessor->saveAs(self::$filePath);

        if (self::$pattern->get('format') == 'docx2pdf') {
            //save the docx template
            try {
                //convert DOCX to PDF
                $doc2pdf_settings = self::$registry['config']->getSectionParams('docx2pdf');

                if (!empty($doc2pdf_settings)) {
                    $convert_result = Converter::convert(array(
                                                             'type' => 'docx2pdf',
                                                             'source' => self::$filePath,
                                                             'docx2pdf' => $doc2pdf_settings,
                                                         ));
                    if ($convert_result) {
                        $to_delete = self::$filePath;
                        self::$filePath = Converter::$destination;
                        //remove the old file
                        if (file_exists($to_delete)) {
                            unlink($to_delete);
                        }
                    } else {
                        throw new Exception('Error converting docx to pdf (conversion failed)');
                    }
                } else {
                    throw new Exception('Error converting docx to pdf (no conversion settings)');
                }
            } catch (Exception $e) {
                self::$registry['messages']->setError($e->getMessage());
                //the PHPWord failed to save the file
                return false;
            }
        }

        //header("Content-Disposition: attachment; filename={$templateFile->get('filename')}");
        //self::$templateProcessor->saveAs('php://output');

        set_error_handler(function($code, $msg) {
            //ZipArchive->close() workaround
        });

        return true;
    }

    /**
     * Replaces list of employees
     *
     * @return void
     * @throws \PhpOffice\PhpWord\Exception\Exception
     */
    private function replaceEmployeesList() : void
    {
        $assocVars = self::$model->getAssocVars();
        $listEmployees = array_column(
            $assocVars['list_employee_grp']['assoc_values'] ?? array(), 'list_employee_name'
        );
        $listEmployees[] = $assocVars['list_customer_representative']['value'] ?? '';
        $listEmployees = array_values(array_filter($listEmployees));

        if (empty($listEmployees)) {
            //self::$templateProcessor->deleteBlock("EMPLOYEESBLOCK");
            //ToDo: should use deleteBlock when it starts to work again
            //dummy sollution by https://stackoverflow.com/questions/59626675/why-is-deleteblocks-not-working-in-phpword
            self::$templateProcessor->cloneBlock("EMPLOYEESBLOCK", 0, true, true);
            return;
        }

        self::$templateProcessor->cloneRow("num", count($listEmployees));
        foreach ($listEmployees as $rowNum => $employee) {
            $rNum = $rowNum + 1;
            self::$templateProcessor->setValue("num#{$rNum}", $rNum);
            self::$templateProcessor->setValue("employee_name#{$rNum}", $employee);
        }
    }

    /**
     * Replaces list of premises
     *
     * @return void
     * @throws \PhpOffice\PhpWord\Exception\Exception
     */
    private function replacePremisesList() : void
    {
        $assocVars = self::$model->getAssocVars();
        $premises = array_filter($assocVars['rooms_grp']['assoc_values'] ?? array(), function($row) {
            return !empty($row['room']) || !empty($row['room_list']);
        });

        if (empty($premises)) {
            //self::$templateProcessor->deleteBlock("PREMISESBLOCK");
            //ToDo: should use deleteBlock when it starts to work again
            //dummy sollution by https://stackoverflow.com/questions/59626675/why-is-deleteblocks-not-working-in-phpword
            self::$templateProcessor->cloneBlock("PREMISESBLOCK", 0, true, true);
            return;
        }
        $labels = array_combine(
            $assocVars['rooms_grp']['names'],
            $assocVars['rooms_grp']['labels'],
        );
        $roomConditionOptions = array_combine(
            array_column($assocVars['rooms_grp']['room_condition']['options'], 'option_value'),
            array_column($assocVars['rooms_grp']['room_condition']['options'], 'label')
        );
        $secondValveOptions = array_combine(
            array_column($assocVars['rooms_grp']['second_valve_available']['options'], 'option_value'),
            array_column($assocVars['rooms_grp']['second_valve_available']['options'], 'label')
        );

        $section = new \PhpOffice\PhpWord\Element\TextRun(['align' => 'left']);

        foreach($premises as $rowIdx => $premise) {
            //room
            if (!empty($premise['room'])) {
                $section->addText("{$labels['room']}: ", ['bold' => true]);
                $section->addText($premise['room']);
            } else {
                $section->addText("{$labels['room_list']}: ", ['bold' => true]);
                $section->addText($premise['room_list']);
            }


            //condition
            $section->addTextBreak();
            $section->addText("{$labels['room_condition']}: ", ['bold' => true]);
            $section->addText($roomConditionOptions[$premise['room_condition']]??'');

            switch($premise['room_condition']) {
                case 1:
                    $section->addTextBreak();
                    $section->addText("{$labels['second_valve_available']}: ", ['bold' => true]);
                    $section->addText($secondValveOptions[$premise['second_valve_available']]??'');

                    $section->addTextBreak();
                    $section->addText("{$labels['heating_element_type_name']}: ", ['bold' => true]);
                    $section->addText($premise['heating_element_type_id'] == '2155' ? $premise['heating_element_type_desc'] : $premise['heating_element_type_name']);

                    $section->addTextBreak();
                    $section->addText("{$labels['heating_body_type_name']}: ", ['bold' => true]);
                    $section->addText($premise['heating_body_type_id'] == '2179' ? $premise['heating_body_type_desc'] : $premise['heating_body_type_name']);

                    foreach(['depth', 'overall_height', 'width', 'gliders_number', 'gliders_count', 'gliders_structure',
                                'structure', 'length', 'double_body', 'pipe_diameter',
                                'pipes_in_parallel'] as $item) {
                        if ($premise[$item] === '') {
                            continue;
                        }
                        $section->addTextBreak();
                        if ($item === 'double_body') {
                            $options = $assocVars['rooms_grp']['double_body']['options'];
                            foreach($options as $option) {
                                if ($option['option_value'] === $premise[$item]) {
                                    $section->addText("{$labels[$item]}: ", ['bold' => true]);
                                    $section->addText($option['label']);
                                    break;
                                }
                            }
                        } else {
                            $section->addText("{$labels[$item]}: ", ['bold' => true]);
                            $section->addText($premise[$item]);
                        }
                    }
                    //intentionally omit break!
                    //break;
                case 2:

                    foreach(['available_apportionment_device', 'brands', 'device', 'prod_year',
                                'metrological_suitability_date', 'indication_new'] as $item) {
                        if ($premise[$item] === '') {
                            continue;
                        }
                        $value = $premise[$item] ?? '';
                        if (!empty($assocVars['rooms_grp'][$item]['options'])) {
                            $options = array_combine(
                                array_column($assocVars['rooms_grp'][$item]['options'], 'option_value'),
                                array_column($assocVars['rooms_grp'][$item]['options'], 'label')
                            );
                            $value = $options[$value] ?? '';
                        } elseif ($item == 'prod_year') {
                            $date = new DateTime($value);
                            $value = $date->format('Y');
                        } elseif ($item == 'metrological_suitability_date') {
                            $date = new DateTime($value);
                            $value = $date->format('m.Y');
                        }
                        $section->addTextBreak();
                        $section->addText("{$labels[$item]}: ", ['bold' => true]);
                        $section->addText($value);
                    }
                    break;
                case 3:
                    break;
            }

            $section->addTextBreak();
            $section->addText("{$labels['device_notes']}: ", ['bold' => true]);
            $section->addText($premise['device_notes'] ?? '');

            if ($rowIdx !== array_key_last($premises)) {
                $section->addTextBreak(2);

                $section->addLine(['weight' => 1.5, 'width' => 500, 'height' => 0]);
            }
        }

        self::$templateProcessor->setComplexBlock('premises', $section);
    }

    /**
     * Replace single placeholders
     *
     * @return void
     */
    public static function replaceSinglePlaceholders(): void
    {

        $templateVariables = self::$templateProcessor->getVariables();

        /*
         * Todo: Get only placeholders needed
        $placeholdersUsed = array();
        foreach($templateVariables as $tVar) {
            $placeholdersUsed[] = self::$extender->getPlaceholder($tVar);
        }
        self::$model->getPatternsVars($placeholdersUsed);
        */

        foreach ($templateVariables as $tVar) {
            $replacement = self::$extender->replace($tVar);
            $replacement = preg_replace('#<br */>\r*\n*#', '<w:br/>', $replacement);
            self::$templateProcessor->setValue($tVar, $replacement);
        }
    }

}

?>
