<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

/**
 * Finance_Incomes_Reason model class
 */
Class Finance_Incomes_Reason extends Finance_Document {
    use BelongsToTrait;

    public $modelName = 'Finance_Incomes_Reason';

    public $counter;

    public $editableDataFields = [
        'description',
        'fin_field_1',
        'fin_field_2',
        'name',
        'received_by',
    ];
    public $checkPermissionsByStatus = true;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('num', 'issue_date', 'customer_name', 'name', 'company_name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->get('id') && $registry->get('getAssignments')) {
            $this->getAssignments('responsible');
            $this->getAssignments('decision');
            $this->getAssignments('observer');
            $this->getAssignments();
        }

        if ($this->get('annulled_by') && !$this->get('annulled_by_name')) {
            $query = 'SELECT CONCAT(ui18n.firstname, " ", ui18n.lastname) AS annulled_by_name ' . "\n" .
                     'FROM ' . DB_TABLE_USERS_I18N . ' AS ui18n ' . "\n" .
                     'WHERE ui18n.parent_id=' . $this->get('annulled_by') . ' AND ui18n.lang="' . $registry['lang'] . '"';
            $annulled_by_name = $registry['db']->GetOne($query);
            $this->set('annulled_by_name', $annulled_by_name, true);
        }

        if ($this->get('type') && $this->get('type') > 0 && !$this->get('type_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                     'WHERE parent_id=' . $this->get('type') . ' AND lang="' . $registry['lang'] . '"';
            $type_name = $registry['db']->GetOne($query);
            $this->set('type_name', $type_name, true);
        }

        // in add mode: keep company in a hidden field and get it when company_data field has no value
        if (!$this->get('company') && $this->get('original_company')) {
            $this->set('company', $this->get('original_company'), true);
            $this->unsetProperty('original_company', true);
        }

        if ($this->get('company') && !$this->get('company_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . "\n" .
                     'WHERE parent_id=' . $this->get('company');
            $company_name = $registry['db']->GetOne($query);
            $this->set('company_name', $company_name, true);
        }

        if ($this->get('office') && !$this->get('office_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                     'WHERE parent_id=' . $this->get('office');
            $company_name = $registry['db']->GetOne($query);
            $this->set('office_name', $company_name, true);
        }

        if ($this->get('payment_type')) {
            if ($this->get('cheque')) {
                $this->set('payment_type_text',
                            $registry['translater']->translate('finance_payment_type_cheque'));
            } else {
                $this->set('payment_type_text',
                            $registry['translater']->translate('finance_payment_type_' . $this->get('payment_type')));
            }
        }

        // if payment is added then the data for it is taken from fields with prefix 'payment_'
        if ($this->isDefined('payment_container_rate')) {
            $this->set('container_rate', $this->get('payment_container_rate'), true);
            $this->unsetProperty('payment_container_rate', true);
        }
        if ($this->isDefined('payment_container_amount')) {
            $this->set('container_amount', $this->get('payment_container_amount'), true);
            $this->unsetProperty('payment_container_amount', true);
        }
        if ($this->isDefined('payment_container_currency')) {
            $this->set('container_currency', $this->get('payment_container_currency'), true);
            $this->unsetProperty('payment_container_currency', true);
        }

        if ($this->get('container_id') && !$this->get('container_name')) {
            $query = 'SELECT ti18n.name' . "\n" .
                     'FROM ' . ($this->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS : DB_TABLE_FINANCE_CASHBOXES) . ' AS t' .  "\n" .
                     'LEFT JOIN ' . ($this->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N : DB_TABLE_FINANCE_CASHBOXES_I18N) . ' AS ti18n' . "\n" .
                     '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $this->get('model_lang') . '"' . "\n" .
                     'WHERE t.id=' . $this->get('container_id');
            $container_name = $registry['db']->GetOne($query);
            $this->set('container_name', $container_name, true);
        }

        if ($this->get('fiscal_event_date') && !preg_match('#\d{4}-\d{2}-\d{2}#', $this->get('fiscal_event_date'))) {
            $tmp = preg_split('#\r\n|\r|\n#', $this->get('fiscal_event_date'));
            foreach ($tmp as $k => $v) {
                $v = preg_split('#\s*:=\s*#', $v);
                $this->set('fiscal_event_date_' . $v[0], $v[1], true);
            }
            $this->unsetProperty('fiscal_event_date', true);
        }
        if ($this->get('date_of_payment') && !preg_match('#\d{4}-\d{2}-\d{2}#', $this->get('date_of_payment'))) {
            $tmp = preg_split('#\r\n|\r|\n#', $this->get('date_of_payment'));
            foreach ($tmp as $k => $v) {
                $v = preg_split('#\s*:=\s*#', $v);
                $this->set('date_of_payment_' . $v[0], $v[1], true);
            }
            $this->unsetProperty('date_of_payment', true);
        }

        //assign fiscal currency
        $fiscal_currency = $this->registry['config']->getParam('finance', 'fiscal_currency');
        $this->set('fiscal_currency', $fiscal_currency, true);
        if ($fiscal_currency && $this->get('status') == 'finished') {
            $prec = $this->registry['config']->getSectionParams('precision');
            $prec_formats = array();
            foreach ($prec as $item => $format) {
                $prec_formats[$item] = '%.' . $format . 'F';
            }

            $fiscal_total_with_vat = sprintf($prec_formats['gt2_total_with_vat'], round($this->get('fiscal_total') + $this->get('fiscal_total_vat'), $prec['gt2_total_with_vat']));
            $this->set('fiscal_total_with_vat', $fiscal_total_with_vat, true);
            unset($prec);
            unset($prec_formats);
        }

        if ($this->get('reason') && !is_object($this->get('reason'))) {
            $query = 'SELECT name FROM ' . DB_TABLE_NOMENCLATURES_I18N . "\n" .
                     'WHERE parent_id = ' . $this->get('reason') . ' AND lang = "' . $this->get('model_lang') . '"';
            $this->set('reason_name', $registry['db']->GetOne($query), true);
        }

        // If this is invoice or proforma invoice, credit notice or debit notice
        if ($this->get('id') && in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE))) {
            // Check for print form and set the result as property
            $this->set('for_printform', $this->checkForPrintForm(), true);
        }
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - current action
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if (!$this->get('customer') && $action != 'translate') {
                $this->raiseError('error_finance_incomes_reasons_no_customer_specified', 'customer', null,
                                  array($this->getLayoutName('customer', false)));
            }
        if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
                    $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
                }
        if ((!$this->get('company') || !$this->get('office') || !$this->get('container_id')) && $action != 'translate') {
                $this->raiseError('error_finance_incomes_reasons_no_container_id_specified', 'company_data', null,
                                  array($this->getLayoutName('company_data')));
            }
        if ($this->get('date_of_payment_count') === '' && !$this->get('date_of_payment') && $action != 'translate') {
                $this->raiseError('error_finance_incomes_reasons_no_date_of_payment_specified', 'date_of_payment', null,
                                  array($this->getLayoutName('date_of_payment')));
            }
        if ($this->get('type') == PH_FINANCE_TYPE_INVOICE && $this->get('fiscal_event_date_count') === '' && !$this->get('fiscal_event_date') && $action != 'translate') {
                $this->raiseError('error_finance_incomes_reasons_no_fiscal_event_date_specified', 'fiscal_event_date', null,
                                  array($this->getLayoutName('fiscal_event_date')));
            }
        if ($this->get('finance_after_action') && preg_match('#payment#', $this->get('finance_after_action'))) {
            if (!$this->get('employee')) {
                $this->raiseError('error_finance_incomes_reasons_no_employee_specified', 'employee', null,
                                  array($this->getLayoutName('employee')));
            }
            if ($this->registry['action'] != 'ajax_prepare_payment_form' && round($this->get('container_rate'), 6) <= 0) {
                $this->raiseError('error_finance_payments_no_container_rate', 'container_rate', null,
                                  array($this->get('currency'), $this->get('container_currency')));
            }
        }
        if ($this->get('type') && $this->get('issue_date')) {
            if ($this->get('issue_date') < date('Y-m-d') && !$this->registry['currentUser']->checkRights('finance_incomes_reasons' . $this->get('type'), 'issue_date')) {
                $this->raiseError('error_finance_invalid_issue_date_before', 'issue_date');
            } elseif ($this->get('issue_date') > date('Y-m-d') && !$this->registry['currentUser']->checkRights('finance_incomes_reasons' . $this->get('type'), 'future_issue_date')) {
                $this->raiseError('error_finance_invalid_issue_date_after', 'issue_date');
            }
        }
        if (!$this->isActivated() && $this->get('type') &&
        ($this->get('type') <= PH_FINANCE_TYPE_MAX &&
        ($this->get('status') == 'finished' || $this->get('finance_after_action') || !$this->get('id') && $this->get('type') == PH_FINANCE_TYPE_INVOICE) ||
        $this->get('type') > PH_FINANCE_TYPE_MAX &&
        $this->get('finance_after_action') && preg_match('#payment#', $this->get('finance_after_action')))) {
            $this->raiseError('error_finance_deactivate_' . ($this->get('type') <= PH_FINANCE_TYPE_MAX ? (!$this->get('id') && $this->get('type') == PH_FINANCE_TYPE_INVOICE ? 'add' : 'finished') : 'payment'),
                              'activate', null, array($this->getModelTypeName()));
        }

        // TODO: validation has to be made here
        // don't forget fiscal_event_date and date_of_payment

        //check counter
        if ($action != 'translate' && $this->get('company') && $this->get('type') && !$this->getCounter()) {
            $this->raiseError('error_finance_incomes_reasons_no_counter', 'type', 0,
                              array($this->get('type_name'), $this->get('company_name'), $this->get('office_name')));
        }

        $this->validateCustomerFinData();

        return $this->valid;
    }

    /**
     * Validates invoice quantities
     *
     * @return bool - true if valid, otherwise false
     */
    public function validateInvoice() {
        // if property is not set, quantities cannot be validated so return immediately
        // (such validation should not be done for advanced invoices)
        if (!$this->isDefined('link_to')) {
            return true;
        }

        $request = &$this->registry['request'];
        $reason = '';
        if ($this->get('link_to_model_name') == 'Finance_Incomes_Reason') {
            $filters = array('where' => array('fir.id = ' . $this->get('link_to')),
                             'sanitize' => false);

            //get incomes reason
            $reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }
        //ToDo - check Contract link_to_model_name

        if ($reason) {
            $old = $reason->registry->get('get_old_vars');
            $reason->registry->set('get_old_vars', true, true);
            //get the grouping table
            $reason->getGT2Vars();
            $reason->registry->set('get_old_vars', $old, true);
            $reason->sanitize();

            // in edit mode, set a property in reason to hold id of current invoice/proforma invoice
            if ($this->get('id')) {
                $reason->set('invoice_id', $this->get('id'), true);
            }

            $invoice_invalid = false;

            //check if we can add invoices
            if (!$reason->checkAddingInvoice(true)) {
                $invoice_invalid = 'quantity';
            }

            $reason_values = $reason->get('grouping_table_2');
            $reason_values = isset($reason_values['values']) ? $reason_values['values'] : array();

            //check if the invoice values are acceptable
            if ($request->isPost()) {
                $invoice_values = $request->getAll('post');
                foreach ($reason_values as $row => $values) {
                    if (empty($invoice_values['deleted'][$row])) {
                        if (isset($invoice_values['quantity'][$row]) && $invoice_values['quantity'][$row] > $values['quantity']) {
                            $invoice_invalid = 'quantity';
                            break;
                        } elseif (isset($invoice_values['price'][$row]) && $invoice_values['price'][$row] > $values['price']) {
                            $invoice_invalid = 'price';
                            break;
                        }
                    }
                }
            } else {
                $invoice_values = $this->get('grouping_table_2');
                $invoice_values = $invoice_values['values'];

                // check if the proforma invoice is attempting to issue an invoice
                // that has rows that reason has no uninvoiced quantities for
                // !!! such rows are not present in the GT2 table of the reason after $reason->checkAddingInvoice(true) !!!
                $invalid_rows = array_diff(array_keys($invoice_values), array_keys($reason_values));
                if (!empty($invalid_rows) && !$this->get('advance')) {
                    foreach ($invalid_rows as $invalid_row) {
                        if (isset($invoice_values[$invalid_row]['subtotal']) && $invoice_values[$invalid_row]['subtotal'] > 0) {
                            $invoice_invalid = 'price';
                            break;
                        }
                    }
                } else {
                    foreach ($reason_values as $row => $values) {
                        if (isset($invoice_values[$row]) && $invoice_values[$row]['quantity'] > $values['quantity']) {
                            $invoice_invalid = 'quantity';
                            break;
                        } elseif (isset($invoice_values[$row]) && $invoice_values[$row]['price'] > $values['price']) {
                            $invoice_invalid = 'price';
                            break;
                        }
                    }
                }
            }

            if ($invoice_invalid == 'quantity') {
                $this->raiseError('error_finance_incomes_reasons_invoice_no_quantity', '', 0,
                                  array('reason_type_name' => $reason->getModelTypeName()));
            } elseif ($invoice_invalid == 'price') {
                $this->raiseError('error_finance_incomes_reasons_invoice_over_price', '', 0,
                                  array('reason_type_name' => $reason->getModelTypeName()));
            }

            if ($this->valid && $this->get('type') == PH_FINANCE_TYPE_INVOICE) {
                // validate that paid amount to proformas and reason does not exceed amount
                // of non-invoiced quantities after adding/editing current invoice
                $reason_amount = $reason->get('total_with_vat');

                if ($reason->get('total_vat_rate') != $this->get('total_vat_rate')) {
                    if ($this->checkUpdateReasonVAT(true, $reason)) {
                        // in this case the invoice is going to add VAT to the reason
                        // so we calculate what its amount is going to be
                        $precision = $this->registry['config']->getParam('precision', 'gt2_total_with_vat');
                        $reason_amount += round($reason_amount*$this->get('total_vat_rate')/100, $precision);
                    } else {
                        // IMPORTANT: when proforma and reason VAT rates are different,
                        // but other invoices have been issued after the proforma was added, do not allow adding invoice
                        $this->raiseError('error_finance_incomes_reasons_invoice_from_proforma_vat_wrong');
                    }
                }

                if ($this->valid) {
                    // total amount of issued invoices + current invoice
                    $invoices_amount = $reason->getInvoicedAmount(true) + $this->get('total_with_vat');
                    // when issuing invoice from proforma, exclude its paid amount as it will go to the invoice
                    $exclude_id = $this->isDefined('proforma_id') && $this->get('proforma_id') > 0 ?
                                  'fir.id != ' . $this->get('proforma_id') : '';
                    // paid amount to non-invoiced proformas
                    $relatives_paid_amount = $reason->getProformasPaidAmount(true, $exclude_id);
                    if ($this->get('proforma_id')) {
                        // when issuing invoice from proforma, add paid amount directly to reason
                        $relatives_paid_amount += $reason->getPaidAmount();
                    }

                    if ($relatives_paid_amount > 0 && $relatives_paid_amount > $reason_amount - $invoices_amount) {
                        // if there is а proforma id, parameter is integer > 0, otherwise it is boolean - false
                        $ignore_proforma_id = $this->get('proforma_id') > 0 ? (int)$this->get('proforma_id') : false;
                        $this->raiseError('error_finance_incomes_reasons_paid_relatives',
                                          '', '', array($reason->preparePaidRelativesError($ignore_proforma_id)));
                    }
                }
            }
        }

        if ($this->checkAddingAdvance()) {
            $customer_advances = $this->getAllCustomerAdvances();
            $checkedAdvances = array();
            foreach ($customer_advances as $customer_advance) {
                if ($customer_advance['checked'] && $customer_advance['shared'] - $customer_advance['invoiced_reason'] > 0) {
                    $checkedAdvances[] = $customer_advance;
                }
            }
            if ($checkedAdvances) {
                //check count advance rows of the invoice
                require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                $filters = array('where' => array('n.subtype = "advance"'));
                $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                $advance_rows = 0;
                if ($nomenclature) {
                    if ($request->isPost()) {
                        $invoice_values = $request->getAll('post');
                        if ($invoice_values['deleted']) {
                            $deleted_values = $invoice_values['deleted'];
                        } else {
                            $deleted_values = array();
                        }
                        $invoice_values = $invoice_values['article_id'];
                        foreach ($invoice_values as $row => $values) {
                            if ($values == $nomenclature->get('id') && !$deleted_values[$row]) {
                                $advance_rows++;
                            }
                        }
                    } else {
                        $invoice_values = $this->get('grouping_table_2');
                        $invoice_values = $invoice_values['values'];
                        foreach ($invoice_values as $key => $val) {
                            if ($val['article_id'] == $nomenclature->get('id')) {
                                $advance_rows++;
                            }
                        }
                    }
                }
                if ($advance_rows != count($checkedAdvances)) {
                    $this->raiseError('error_finance_incomes_reasons_invoice_advance_rows', '');
                }
            }
        }

        if ($this->isDefined('invoice_total_with_vat') && $this->get('invoice_total_with_vat') != $this->get('total_with_vat')) {
            $this->raiseError('error_finance_incomes_reasons_invoice_printform_doesnot_match_invoice', 'remaining_sum');
        }

        if (!$this->valid) {
            return false;
        }

        return true;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            //if document is invoice or proforma invoice, check its quantities
            if (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE))) {
                $valid = $this->validateInvoice();
                if (!$valid) {
                    return false;
                }
            }

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();
                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if ($this->get('finance_after_action') != '') {
            $this->set('status', 'finished', true);
        }
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        if ($this->get('import_added')) {
            $set['added']     = sprintf("added='%s'", $this->get('import_added'));
        } else {
            $set['added']     = sprintf("added=now()");
        }
        if ($this->get('custom_modified_by')) {
            $set['added_by']      = sprintf("added_by=%d", $this->get('custom_modified_by'));
        } else {
            $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        }

        if ($this->get('status') == 'finished') {
            // complete issue_by field if the incomes reason is finished
            if (!$this->get('issue_by')) {
                $this->set('issue_by', $this->registry['currentUser']->get('id'), true);
                $this->set('issue_by_name', $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname'), true);
            }
            $set['issue_by'] = sprintf("issue_by=%d", $this->get('issue_by'));
            // complete invoice_code field if the incomes reason is finished
            if (in_array(
                $this->get('type'),
                array(
                    PH_FINANCE_TYPE_INVOICE,
                    PH_FINANCE_TYPE_PRO_INVOICE,
                    PH_FINANCE_TYPE_CREDIT_NOTICE,
                    PH_FINANCE_TYPE_DEBIT_NOTICE
                ))
            ) {
                if (!$this->get('invoice_code')) {
                    $this->set('invoice_code', $this->registry['currentUser']->get('invoice_code'), true);
                }
                $set['invoice_code'] = sprintf("invoice_code='%s'", $this->get('invoice_code'));
            }
        }

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if ($this->get('fix_issue_date')) {
              //the date of issue could change in getNum
              //reassign it
              $set['issue_date'] = sprintf("`issue_date`='%s'", $this->get('issue_date'));
              $set['date_of_payment'] = sprintf("`date_of_payment`='%s'", $this->get('date_of_payment'));
            }
            if ($this->get('type') == PH_FINANCE_TYPE_INVOICE &&
                $this->get('fiscal_event_date') < date_sub(date_create($this->get('issue_date')), new DateInterval('P5D'))->format('Y-m-d')) {
                $this->set('fiscal_event_date', $this->get('issue_date'), true);
                $set['fiscal_event_date'] = sprintf("`fiscal_event_date`='%s'", $this->get('fiscal_event_date'));
            }
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
            } else {
                if (is_object($this->counter)) {
                    $this->counter->increment();
                } else {
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                }
            }
        } elseif ($this->get('import_num')) {
            //set num from import
            if ($this->counter) {
                $this->counter = null;
            }
            $this->getCounter();
            if ($this->counter) {
                $this->counter->increment();
            } else {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
            }
            $this->set('num',  $this->get('import_num'), true);
            $set['num'] = sprintf("num='%s'", $this->get('import_num'));
        }

        if (!empty($this->counter)) {
            $set['counter'] = sprintf("counter='%s'", $this->counter->get('id'));
        }

        if ($this->get('annulled_by')) {
            $set['annulled_by'] = "`annulled_by` = " . $this->get('annulled_by');
            $set['annulled'] = "`annulled` = NOW()";
        }

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                  'SET ' . implode(",\n", $set) . "\n";

        $r = $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new finance's invoice base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if ($this->get('transform_params')) {
            //update the "transform from" model
            $this->saveTransformationDetails();
        }

        //edit 2nd type grouping table data
        $this->saveGT2Vars(false, true);
        if ($this->get('status') && $this->get('status') != 'opened' /*&& ($this->get('type') > PH_FINANCE_TYPE_MAX || $this->get('type') == PH_FINANCE_TYPE_CORRECT_REASON)*/) {
            $this->setAvailableQuantities();
        }

        if ($this->isDefined('new_handovered_status')) {
            $this->setHandoveredStatus();
        }

        if ($this->get('transform_params') && $this->get('rows_links')) {
            //update GT2 rows links
            $this->updateRowsLinks();
        }

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            //update invoice relatives
            $this->updateReasonsRelatives();
        }

        if ($this->get('finance_after_action') == 'payment') {
            //add payment document
            if (!$this->addPayment()) {
                $db->FailTrans();
            }
        } elseif (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                                     PH_FINANCE_TYPE_PRO_INVOICE)) &&
        $this->get('status') == 'finished' &&
        !preg_match('#^(proforma)?advances#', $this->registry['action'])) {
            //update invoice/proforma payments if there are payments for parent reason
            $this->updatePaymentsFromParent();
        } elseif ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE && $this->get('save_auto_balance')) {
            // automatically save balance between credit note and parent invoice when flag is set
            $this->set('relatives_payments', array($this->get('link_to') => abs(round($this->get('total_with_vat'), 2))), true);
            $this->set('save_auto_balance', $this->updateFinanceRelatives('incomes_invoice'), true);
            $this->unsetProperty('relatives_payments', true);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //get current model status
        $query = 'SELECT status FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' WHERE id = ' . $this->get('id');
        $old_status = $db->GetOne($query);

        if ($this->get('finance_after_action') != '') {
            $this->set('status', 'finished', true);
        }

        if ($this->get('status') == 'finished') {
            // complete issue_by field if the incomes reason is finished
            if (!$this->get('issue_by')) {
                $this->set('issue_by', $this->registry['currentUser']->get('id'), true);
                $this->set('issue_by_name', $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname'), true);
            }
            $set['issue_by'] = sprintf("issue_by=%d", $this->get('issue_by'));
            // complete invoice_code field if the incomes reason is finished
            if (in_array(
                $this->get('type'),
                array(
                    PH_FINANCE_TYPE_INVOICE,
                    PH_FINANCE_TYPE_PRO_INVOICE,
                    PH_FINANCE_TYPE_CREDIT_NOTICE,
                    PH_FINANCE_TYPE_DEBIT_NOTICE
                ))
            ) {
                if (!$this->get('invoice_code')) {
                    $this->set('invoice_code', $this->registry['currentUser']->get('invoice_code'), true);
                }
                $set['invoice_code'] = sprintf("invoice_code='%s'", $this->get('invoice_code'));
            }
        }

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
                $db->FailTrans();
                $db->CompleteTrans();
                return false;
            } else {
                if (is_object($this->counter)) {
                    $this->counter->increment();
                } else {
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                }
            }
        }

        if (!empty($this->counter)) {
            $set['counter'] = sprintf("counter='%s'", $this->counter->get('id'));
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //save 2nd type grouping table data
        $this->saveGT2Vars(false, true);
        if ($this->get('status') && $this->get('status') != 'opened' && $old_status == 'opened' || $this->get('correction')/*&& ($this->get('type') > PH_FINANCE_TYPE_MAX || $this->get('type') == PH_FINANCE_TYPE_CORRECT_REASON)*/) {
            $this->setAvailableQuantities();
        }

        if ($this->isDefined('new_handovered_status')) {
            $this->setHandoveredStatus();
        }

        if ($this->get('finance_after_action') == 'payment') {
            //add payment document
            if (!$this->addPayment()) {
                $db->FailTrans();
            }
        } elseif (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                                     PH_FINANCE_TYPE_PRO_INVOICE)) &&
        $this->get('status') == 'finished') {
            //update invoice/proforma payments if there are payments for parent reason
            $this->updatePaymentsFromParent();
        }

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            //update invoice relatives
            $this->updateReasonsRelatives();
        }

        // if this is an annulled or deactivated invoice, check if VAT of reason should be removed
        // (include_VAT=1 for type+company of reason and this was the last non-annulled and active invoice)
        if ($this->get('type') == PH_FINANCE_TYPE_INVOICE &&
        (!$this->isActivated() || $this->get('annulled') && $this->get('annulled_by'))) {
            $this->getRelatives(array('get_parent_reasons' => array('fir.type > ' . PH_FINANCE_TYPE_MAX)));
            $reason = $this->get('parent_reasons');
            $this->unsetProperty('parent_reasons', true);
            if (!empty($reason)) {
                $reason = array_shift($reason);
                // check if VAT should be removed from reason
                if ($this->checkUpdateReasonVAT(false, $reason)) {
                    $vat_params = array(
                        'total_vat_rate' => 0,
                        'total_no_vat_reason' => 0,
                        'total_no_vat_reason_text' => ''
                    );
                    $reason->updateReasonVAT($vat_params);
                }
            }
            unset($reason);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Translate existing model
     *
     * @return bool - result of the operation
     */
    public function translate() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //IMPORTANT: force validation so customer name and address are fetched for the destination language!!!
        unset($this->customer);
        $this->prepareMainData();
        if ($this->valid) {
            //UPDATE THE I18N TABLE OF THE MODEL
            $this->updateI18N();
        } else {
            $db->FailTrans();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //save 2nd type grouping table data
        if (!$this->translateGT2Vars()) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('description')) {
            $update['description']  = sprintf("description='%s'", $this->get('description'));
        }
        if ($this->isDefined('total_no_vat_reason_text')) {
            $update['total_no_vat_reason_text'] = sprintf("`total_no_vat_reason_text`='%s'", $this->get('total_no_vat_reason_text'));
        }

        //set customer data (escape values because they are set to model after it is escaped)
        $update['customer_name'] = sprintf("customer_name='%s'", General::slashesEscape($this->get('customer_name')));
        $update['customer_address'] = sprintf("customer_address='%s'", General::slashesEscape($this->get('customer_address')));
        $update['received_by'] = sprintf("received_by='%s'", General::slashesEscape($this->get('received_by')));

        $update['fin_field_1'] = sprintf("fin_field_1='%s'", $this->get('fin_field_1'));
        $update['fin_field_2'] = sprintf("fin_field_2='%s'", $this->get('fin_field_2'));

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('translating finance i18n details', $db, $query2);
            }
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        if (!$this->get('table_values_are_set')) {
            //calculate totals
            $this->getGT2Vars();

            // if model is an invoice and it is deactivated (i.e. property "active" exists in model and its value is 0)
            if ($this->get('id') && $this->get('type') == PH_FINANCE_TYPE_INVOICE && !$this->isActivated()) {
                $table = $this->get('grouping_table_2');
                foreach ($table['values'] as $id => $values) {
                    if (empty($values)) continue;
                    $table['values'][$id]['deleted'] = 1;
                    $table['values'][$id]['quantity'] = 0;
                }
                $this->set('grouping_table_2', $table, true);
            }

            $this->calculateGT2();
            $this->set('table_values_are_set', true, true);
        }

        $set = array();

        if ($this->isDefined('type')) {
            $set['type'] = sprintf("`type`='%d'", $this->get('type'));

            // automatically set name on add, if empty
            $this->setDefaultName();
        }

        if ($this->isDefined('company')) {
            $set['company'] = sprintf("`company`='%d'", $this->get('company'));
        }
        if ($this->isDefined('office')) {
            $set['office'] = sprintf("`office`='%d'", $this->get('office'));
        }
        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("`customer`='%d'", $this->get('customer'));

            if (empty($this->customer) || !is_object($this->customer)) {
                $this->getCustomer(true);
            }
            //set customer data to model to be saved into reason
            if ($this->get('import_customer_name')) {
                $this->set('customer_name', $this->get('import_customer_name'), true);
            }
            if (is_object($this->customer)) {
                if ($this->customer->get('is_company')) {
                    if (!$this->get('import_customer_name')) {
                        $this->set('customer_name', $this->customer->get('company_name'), true);
                    }
                    $this->set('customer_address', $this->customer->get('registration_address'), true);
                    $this->set('eik', $this->customer->get('eik'), true);
                    $this->set('vat_num', $this->customer->get('in_dds'), true);
                    $this->set('received_by', $this->customer->get('mol'), true);
                } else {
                    if (!$this->get('import_customer_name')) {
                        $this->set('customer_name', trim($this->customer->get('name') . ' ' . $this->customer->get('lastname')), true);
                    }
                    $this->set('customer_address', $this->customer->get('address_by_personal_id'), true);
                    $this->set('eik', $this->customer->get('ucn'), true);
                    $this->set('vat_num', $this->customer->get('in_dds'), true);
                    $this->set('received_by', $this->get('customer_name'), true);
                }
            }

            $set['eik'] = sprintf("`eik`='%s'", $this->get('eik'));
            $set['vat_num'] = sprintf("`vat_num`='%s'", $this->get('vat_num'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark'] = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("project=%d", $this->get('project'));
        }
        if ($this->isDefined('phase')) {
            $set['phase'] = sprintf("phase=%d", $this->get('phase'));
        }
        if ($this->isDefined('currency')) {
            $set['currency'] = sprintf("`currency`='%s'", $this->get('currency'));
        }
        if ($this->isDefined('container_id')) {
            $set['container_id'] = sprintf("`container_id`=%d", $this->get('container_id'));
        }
        if ($this->isDefined('employee')) {
            $set['employee'] = sprintf("`employee`=%d", $this->get('employee'));
        }
        if ($this->get('finance_after_action') != '') {
            $this->set('status', 'finished', true);
        }
        if ($this->isDefined('status')) {
            $set['status'] = sprintf("`status`='%s'", $this->get('status'));
            $set['status_modified'] = sprintf("status_modified=now()");
            if ($this->get('custom_modified_by')) {
                $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->get('custom_modified_by'));
            } else {
                $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
            }
        }
        if ($this->get('import_issue_date')) {
            $set['issue_date'] = sprintf("`issue_date`='%s'", $this->get('import_issue_date'));
            $this->set('issue_date', $this->get('import_issue_date'), true);
        } elseif (!$this->get('issue_date')) {
            $set['issue_date'] = "`issue_date`=NOW()";
            $this->set('issue_date', General::strftime('%Y-%m-%d'), true);
        } else {
            $set['issue_date'] = sprintf("`issue_date`='%s'", $this->get('issue_date'));
        }

        if ($this->get('status') == 'finished') {
            if ($this->isDefined('date_of_payment_count')) {
                if ($this->get('date_of_payment_count') !== '') {
                    if ($this->get('date_of_payment_point') == 'issue') {
                        $date_start = date_create($this->get('issue_date'))->format('Y-m-d');
                        if ($this->get('date_of_payment_period_type') == 'working') {
                            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                            $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date_start,
                                    $this->get('date_of_payment_count'), 'after');
                        } else {
                            $date_of_payment = General::strftime('%Y-%m-%d',
                                    strtotime('+' . $this->get('date_of_payment_count') . ' day', strtotime($date_start)));
                        }
                    } else {
                        $date_of_payment = array(
                            'count := ' . $this->get('date_of_payment_count'),
                            'period_type := ' . $this->get('date_of_payment_period_type'),
                            'period := day',
                            'direction := after',
                            'point := receive');
                        $date_of_payment = implode('\n', $date_of_payment);
                    }

                    $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $date_of_payment);
                    $this->set('date_of_payment', $date_of_payment, true);
                } elseif ($this->get('import_date_of_payment')) {
                    $this->set('date_of_payment', $this->get('import_date_of_payment'), true);
                    $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $this->get('date_of_payment'));
                } else {
                    $this->set('date_of_payment', General::strftime('%Y-%m-%d'), true);
                    $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $this->get('date_of_payment'));
                }
            } elseif ($this->get('date_of_payment')) {
                $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $this->get('date_of_payment'));
            }

            // set zero-amount finished documents as paid but only if they are not annulled, inactive or correction documents
            if ($this->get('total') == 0 && !$this->get('annulled_by') && $this->isActivated() &&
            $this->get('type') != PH_FINANCE_TYPE_CORRECT_REASON) {
                $set['payment_status'] = sprintf('payment_status="paid"');
                $set['payment_status_modified'] = sprintf("payment_status_modified=now()");
                $this->set('payment_status', 'paid', true);
            }

        } else {
            if ($this->isDefined('date_of_payment_count')) {
                $date_of_payment = array(
                    'count := ' . ($this->get('date_of_payment_count') ? $this->get('date_of_payment_count') : 0),
                    'period_type := ' . $this->get('date_of_payment_period_type'),
                    'period := day',
                    'direction := after',
                    'point := ' . $this->get('date_of_payment_point'));
                $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', implode('\n', $date_of_payment));
            }
        }

        if (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE))) {
            if ($this->get('import_fiscal_event_date')) {
                $set['fiscal_event_date'] = sprintf("`fiscal_event_date`='%s'", $this->get('import_fiscal_event_date'));
                $this->set('fiscal_event_date', $this->get('import_fiscal_event_date'), true);
            } elseif ($this->get('fiscal_event_date')) {
                $set['fiscal_event_date'] = sprintf('`fiscal_event_date`= "%s"', $this->get('fiscal_event_date'));
            } else {
                $set['fiscal_event_date'] = sprintf('`fiscal_event_date`= "%s"', $this->get('issue_date'));
                $this->set('fiscal_event_date', $this->get('issue_date'), true);
            }
        } else {
            $set['fiscal_event_date'] = sprintf('`fiscal_event_date`= ""');
        }

        $prec = $this->registry['config']->getSectionParams('precision');
        if ($this->isDefined('total')) {
            $set['total'] = sprintf("`total`='%.6f'", round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_discount_surplus_field')) {
            $set['total_discount_surplus_field'] = sprintf("`total_discount_surplus_field`='%s'", $this->get('total_discount_surplus_field'));
        }
        if ($this->isDefined('total_discount_value')) {
            $set['total_discount_value'] = sprintf("`total_discount_value`='%.6f'", round($this->get('total_discount_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_discount_percentage')) {
            $set['total_discount_percentage'] = sprintf("`total_discount_percentage`='%.6f'", round($this->get('total_discount_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_value')) {
            $set['total_surplus_value'] = sprintf("`total_surplus_value`='%.6f'", round($this->get('total_surplus_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_percentage')) {
            $set['total_surplus_percentage'] = sprintf("`total_surplus_percentage`='%.6f'", round($this->get('total_surplus_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_without_discount')) {
            $set['total_without_discount'] = sprintf("`total_without_discount`='%.6f'", round($this->get('total_without_discount'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_vat_rate')) {
            $set['total_vat_rate'] = sprintf("`total_vat_rate`='%.2f'", round($this->get('total_vat_rate'), 2));
        }
        if ($this->get('import_total_vat')) {
            $total_vat = $this->get('import_total_vat');
        } elseif ($this->isDefined('total_vat')) {
            $total_vat = round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total_vat']);
        }
        if (isset($total_vat)) {
            $set['total_vat'] = sprintf("`total_vat`='%.6f'", $total_vat);
        }
        if ($this->get('import_total_with_vat')) {
            $set['total_with_vat'] = sprintf("`total_with_vat`='%.6f'", $this->get('import_total_with_vat'));
        } elseif ($this->isDefined('total_with_vat')) {
            $set['total_with_vat'] = sprintf("`total_with_vat`='%.6f'",
                        round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total_vat']) + round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_no_vat_reason')) {
            $set['total_no_vat_reason'] = sprintf("`total_no_vat_reason`='%d'", $this->get('total_no_vat_reason'));
        }

        if ($this->get('status') == 'finished') {
            //set fiscal_total and fiscal_total_vat
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $currency = $this->registry['config']->getParam('finance', 'fiscal_currency');
            if (empty($currency)) {
                $currency = Finance_Currencies::getMain($this->registry);
            }
            $rate = Finance_Currencies::getRate($this->registry, $this->get('currency'), $currency);
            if ($this->isDefined('total')) {
                $fiscal_total = $rate * $this->get('total');
                $set['fiscal_total'] = sprintf('`fiscal_total`= "%f"', $fiscal_total);
            }
            if (isset($total_vat)) {
                $fiscal_total_vat = $rate * $total_vat;
                $set['fiscal_total_vat'] = sprintf('`fiscal_total_vat`= "%f"', $fiscal_total_vat);
            }
        }

        if ($this->isDefined('payment_type')) {
            $set['payment_type'] = sprintf("`payment_type`='%s'", $this->get('payment_type'));
            if ($this->get('cheque')) {
                $set['cheque'] = '`cheque` = 1';
            } else {
                $set['cheque'] = '`cheque` = 0';
            }
        }
        if ($this->isDefined('advance')) {
            $set['advance'] = sprintf("`advance`='%d'", $this->get('advance'));
        }
        if ($this->get('type') > PH_FINANCE_TYPE_MAX && !$this->get('id') && !$this->isDefined('distributed')) {
            $this->set('distributed', PH_FINANCE_DISTRIBUTION_NO, true);
        }
        if ($this->isDefined('distributed')) {
            $set['distributed'] = sprintf("`distributed`=%d", $this->get('distributed'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }
        if ($this->get('annulled') && $this->get('annulled_by')) {
            $set['annulled'] = sprintf("annulled='%s'", $this->get('annulled'));
            $set['annulled_by'] = sprintf("annulled_by=%d", $this->get('annulled_by'));
        }
        $set['modified']       = sprintf("modified=now()");
        if ($this->get('custom_modified_by')) {
            $set['modified_by']    = sprintf("modified_by=%d", $this->get('custom_modified_by'));
        } else {
            $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        }
        if ($this->isDefined('cd_reason')) {
            $set['reason'] = sprintf('`reason` = "%s"', $this->get('cd_reason'));
        }

        return $set;
    }

    /**
     * Prepares default name of model before adding, if empty, and sets it to model
     */
    private function setDefaultName() {
        if (!$this->get('id') && $this->isDefined('name') && !$this->get('name')) {
            if ($this->get('advance') &&
                in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                                   PH_FINANCE_TYPE_PRO_INVOICE))) {
                // advance invoice or proforma
                $name = $this->get('type') == PH_FINANCE_TYPE_INVOICE ?
                        $this->i18n('finance_incomes_reasons_advance') :
                        $this->i18n('finance_incomes_reasons_proforma_advance');
            } else {
                $parent_name = '';
                if ($this->get('type') < PH_FINANCE_TYPE_MAX && $this->get('link_to')) {
                    // derivative document of system type
                    if ($this->isDefined('parent_name')) {
                        // parent name is set to model so just get it
                        $parent_name = $this->get('parent_name');
                    } else {
                        if ($this->get('link_to_model_name') == 'Contract') {
                            $parent_i18n_table = DB_TABLE_CONTRACTS_I18N;
                        } else {
                            $parent_i18n_table = DB_TABLE_FINANCE_INCOMES_REASONS_I18N;
                        }
                        $query_parent_name = 'SELECT name FROM ' . $parent_i18n_table . "\n" .
                                             'WHERE parent_id=' . $this->get('link_to') . ' AND lang="' . $this->registry['lang'] . '"';
                        $parent_name = $this->registry['db']->GetOne($query_parent_name);
                    }
                }

                // construct name (type name is used by default)
                if (!$this->get('type_name')) {
                    $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                             'WHERE parent_id=' . $this->get('type') . ' AND lang="' . $this->registry['lang'] . '"';
                    $this->set('type_name', $this->registry['db']->GetOne($query), true);
                }
                $name = $this->get('type_name') .
                        ($parent_name ? sprintf(' %s %s', $this->i18n('for'), $parent_name) : '');
            }

            $this->set('name', $name, true);
        }
    }

    /**
     * Method to define and set group of a new model (without id) based on
     * default group type setting<br />
     * IMPORTANT: Always pass $parent_group parameter when creating secondary documents.
     *
     * @param mixed $parent_group - group of direct parent or false if there is no parent
     * @param mixed $type_default_group - default group from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setGroup($parent_group = false, $type_default_group = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_group === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $group = '';
        if ($type_default_group !== false) {
            $group = $type_default_group;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_group' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $group = $this->registry['db']->GetOne($query);
        }
        // if setting is parent group
        if ($group == '[parent_group]') {
            // if value is passed for parent group
            if ($parent_group !== false) {
                $group = $parent_group;
            } else {
                // if there is no parent document,
                // group will be defined according to current user
                $group = '[default_user_group]';
            }
        }
        // get the default group of the user
        if ($group == '[default_user_group]') {
            if ($this->registry->get('crontab_issue_invoices') && $this->get('custom_modified_by')) {
                // issuing invoices from crontab
                $query = 'SELECT default_group' . "\n" .
                         'FROM ' . DB_TABLE_USERS . "\n" .
                         'WHERE id=\'' . $this->get('custom_modified_by') . '\'';
                $group = $this->registry['db']->GetOne($query);
            } elseif ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $group = $this->registry['originalUser']->get('default_group');
            } else {
                // get the default group id from the current user
                $group = $this->registry['currentUser']->get('default_group');
            }
            // set "All" if user has no default group
            if (!$group) {
                $group = PH_ROOT_GROUP;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('group', $group, true);
    }

    /**
     * Method to define and set department of a new model (without id) based on
     * default department type setting<br />
     * IMPORTANT: Always pass $parent_department parameter when creating secondary documents.
     *
     * @param mixed $parent_department - department of direct parent or false if there is no parent
     * @param mixed $type_default_department - default department from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setDepartment($parent_department = false, $type_default_department = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_department === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $department = '';
        if ($type_default_department !== false) {
            $department = $type_default_department;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_department' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $department = $this->registry['db']->GetOne($query);
        }
        // if setting is parent department
        if ($department == '[parent_department]') {
            // if value is passed for parent department
            if ($parent_department !== false) {
                $department = $parent_department;
            } else {
                // if there is no parent document,
                // department will be defined according to current user
                $department = '[default_user_department]';
            }
        }
        // get the default department of the user
        if ($department == '[default_user_department]') {
            if ($this->registry->get('crontab_issue_invoices') && $this->get('custom_modified_by')) {
                // issuing invoices from crontab
                $query = 'SELECT default_department' . "\n" .
                         'FROM ' . DB_TABLE_USERS . "\n" .
                         'WHERE id=\'' . $this->get('custom_modified_by') . '\'';
                $department = $this->registry['db']->GetOne($query);
            } elseif ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $department = $this->registry['originalUser']->get('default_department');
            } else {
                // get the default department id from the current user
                $department = $this->registry['currentUser']->get('default_department');
            }
            // set "All" if user has no default department
            if (!$department) {
                $department = PH_DEPARTMENT_FIRST;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('department', $department, true);
    }

    /**
     * get counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->unsanitize();
            }

            if (in_array($this->get('type'), array(PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE))) {
                $query = 'SELECT counter FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                         'WHERE id = ' . $this->get('link_to');
                $this->set('custom_counter', $this->registry['db']->GetOne($query), true);
            }

            require_once 'finance.counters.factory.php';
            if (!$this->get('custom_counter')) {
                $filters = array('where' => array(
                                            'fc.model = "' . $this->modelName . '"',
                                            'fc.model_type = "' . $this->get('type') . '"',
                                            '(fco.office_id = "' . $this->get('office') . '" OR fco.office_id = 0)',
                                            'fc.company = "' . $this->get('company') . '"',
                                            'fc.active = 1',
                                            'fc.deleted_by = 0'),
                                 'sort' => array('fco.office_id DESC', 'fc.default DESC')
                                );
            } else {
                $filters = array('where' => array('fc.id = ' . $this->get('custom_counter')));
            }
            $this->counter = Finance_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Get number
     *
     * @param bool $force
     * @return string
     */
    public function getNum($force = false) {
        if (!$this->get('num') || $force) {

            //get the counter assigned to the type of financial document
            $this->getCounter();

            if (!$this->counter) {
                return false;
            }

            if ($this->get('type') <= PH_FINANCE_TYPE_MAX && $this->get('type') != PH_FINANCE_TYPE_CORRECT_REASON) {
                //try to find the issue date of the document with previous number
                //it is not necessarily the last issue document with this counter
                $query = 'SELECT MAX(issue_date) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir ' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_COUNTERS . ' fc ' . "\n" .
                         ' ON  fc.id = fir.counter AND fc.id = ' . intval($this->counter->get('id')) . "\n" .
                         '     AND fc.formula LIKE "%[num]%" ' . "\n" .
                         '     AND fir.num LIKE CONCAT("%", LPAD(fc.next_number-1, fc.leading_zeroes, "0"), "%")' . "\n" .
                         '     AND issue_date > "' . $this->get('issue_date') . '"';
                $issued = $this->registry['db']->GetOne($query);
                if (!empty($issued)) {
                    if ($this->get('auto_issue') && $this->registry->get('crontab_issue_invoices')) {
                        //we will have this parameters only for invoices for contracts automatically issued by crontab
                        //change issue date with the first possible - the found last date we have invoice for
                        $this->set('issue_date', $issued, true);
                        $this->set('fix_issue_date', true, true);

                        //recalculate date of payment too
                        if ($this->isDefined('date_of_payment_count')) {
                            if ($this->get('date_of_payment_point') == 'issue') {
                                $date_start = date_create($this->get('issue_date'))->format('Y-m-d');
                                if ($this->get('date_of_payment_period_type') == 'working') {
                                    require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                                    $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date_start,
                                            intval($this->get('date_of_payment_count')), 'after');
                                } else {
                                    $date_of_payment = General::strftime('%Y-%m-%d',
                                            strtotime('+' . intval($this->get('date_of_payment_count')) . ' day', strtotime($date_start)));
                                }
                                $this->set('date_of_payment', $date_of_payment, true);
                            }
                        }
                    } else {
                        $this->counter = -1;
                        $this->raiseError('error_finance_incomes_reason_wrong_issue_date', 'issue_date', '', array('issue_date' => General::strftime($this->i18n('date_short'), $issued)));
                        return false;
                    }
                }
            }

            if ($this->counter) {
                //define some of the counter's fomula components
                $formula = $this->counter->get('formula');
                $prefix = $this->counter->get('prefix');
                $delimiter = $this->counter->get('delimiter');
                $zeroes = $this->counter->get('leading_zeroes');
                $date_format = $this->counter->get('date_format');

                //create extender to expand the formula components
                $extender = new Extender;

                //lock the counter for update to guarantee unique next number
                $query = 'SELECT next_number FROM ' . DB_TABLE_FINANCE_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                //set financial document number
                $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                $extender->add('num', $num);

                if ($this->counter->get('prefix')) {
                    //add this component to the extender
                    $extender->add('prefix', $prefix);
                }

                if ($this->counter->get('company_code') && $this->get('company')) {
                    //get company code
                    $query = 'SELECT code FROM ' . DB_TABLE_FINANCE_COMPANIES . ' WHERE id=' . $this->get('company');
                    $company_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('company_code', $company_code);
                }

                if ($this->counter->get('office_code') && $this->get('office')) {
                    //get office code
                    $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
                    $office_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('office_code', $office_code);
                }

                if ($this->counter->get('user_code')) {
                    //get user code
                    //add this component to the extender
                    $extender->add('user_code', $this->registry['currentUser']->get('code'));
                }

                if ($this->counter->get('project_code') && $this->get('project')) {
                    //get project code
                    require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                    $filters = array('where' => array('p.id = ' . $this->get('project'),
                                                      'p.deleted IS NOT NULL'),
                                     'sanitize' => true);
                    $project = Projects::searchOne($this->registry, $filters);

                    //add this component to the extender
                    $extender->add('project_code', $project->get('code'));
                }

                if ($this->counter->get('document_date')) {
                    //replace the date
                    if ($this->get('issue_date')) {
                        $date = General::strftime($date_format, strtotime($this->get('issue_date')));
                    } elseif ($this->get('added')) {
                        $date = General::strftime($date_format, strtotime($this->get('added')));
                    } else {
                        $date = General::strftime($date_format);
                    }

                    //add this component to the extender
                    $extender->add('document_date', $date);
                }

                $num = $extender->expand($formula);
                if ($delimiter) {
                    //remove repeating delimiters
                    $num = preg_replace('#' . preg_quote($delimiter . $delimiter) .'#', $delimiter, $num);
                    $num = preg_replace('#' . preg_quote($delimiter) .'$#', '', $num);
                    $num = preg_replace('#^' . preg_quote($delimiter) .'#', '', $num);
                }

                $this->set('num', $num, true);
            }
        }

        return $this->get('num');
    }

    /**
     * Checks whether the advance invoices should be put as negative rows in the current invoice
     * When issuing a "normal" invoice all of the advance rows should be put within the normal invoice with negative sign
     * This function checks if normal invoices have already been issued and returns true in this case, otherwise - false
     *
     * @return bool - result of the operation
     */
    public function checkAddingAdvance() {

        if (!$this->get('id')) {
            //we are here when we are adding invoice
            return false;
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $db = $this->registry['db'];

        $link_to_id = $this->get('id');
        $link_to_model_name = 'Finance_Incomes_Reason';

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            //this is an invoice or another system finance type
            //get the id of the reason document
            $query = 'SELECT frr.*,  fir.type' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.link_to = fir.id' . "\n" .
                     'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"';
            //get parent id
            $data = $this->registry['db']->GetRow($query);
            if (!empty($data)) {
                if ($data['link_to_model_name'] == 'Finance_Incomes_Reason' && $data['type'] > PH_FINANCE_TYPE_MAX) {
                    //the invoice is related with income reason
                    $link_to_model_name = 'Finance_Incomes_Reason';
                    $link_to_id = $data['link_to'];
                } elseif ($data['link_to_model_name'] == 'Contract') {
                    //invoice from contract
                    $link_to_model_name = 'Contract';
                    $link_to_id = $data['link_to'];
                }
            }
        }

        //get normal invoices
        $query = 'SELECT fir.id FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                    ' JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr ' . "\n" .
                    ' ON frr.parent_id = fir.id' . "\n" .
                    ' AND frr.link_to = ' . $link_to_id . "\n" .
                    ' AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                    ' AND frr.link_to_model_name = "' . $link_to_model_name . '"' . "\n" .
                    ' WHERE fir.customer = ' . $this->get('customer') . "\n" .
                    ' AND fir.type = ' . PH_FINANCE_TYPE_INVOICE . "\n" .
                    ' AND fir.annulled_by = 0 AND fir.advance=0 AND fir.active = 1' . "\n" .
                    'ORDER BY fir.id';
        $invoices_ids = $db->GetCol($query);

        if ($unsanitize) {
            $this->sanitize();
        }

        if (!empty($invoices_ids) && $invoices_ids[0] == $this->get('id') && $this->get('type') <= PH_FINANCE_TYPE_MAX) {
             return true;
        }

        /*
        if (count($invoices_ids)) {
            return false;
        } else {
            return true;
        }
        */

        return true;
    }

    /**
     * Get total amount (without VAT) of advance invoices (and related credit notes)
     * for incomes reason
     *
     * @return double - result of the operation
     */
    public function getAdvancedTotal() {

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        if ($this->isDefined('new_advances')) {
            // advance ids are set to model, i.e. maybe they are not related yet
            $advance_ids = array_keys($this->get('new_advances'));
        } else {
            // get related advance invoice ids from db
            $query = 'SELECT fir.id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                     '    AND frr.parent_id=fir.id' . "\n" .
                     'WHERE frr.link_to_model_name="Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.link_to=' . $this->get('id') . "\n" .
                     '  AND fir.status="finished"' . "\n" .
                     '  AND fir.type=' . PH_FINANCE_TYPE_INVOICE . "\n" .
                     '  AND fir.annulled_by=0' . "\n" .
                     '  AND fir.advance=' . $this->get('type');
            $advance_ids = $this->registry['db']->GetCol($query);
        }

        $advanced_total = 0;
        if ($advance_ids) {
            $query = 'SELECT IF(frr.shared!=0, frr.shared, fir.total) as amount' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                     '  ON frr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                     '    AND frr.link_to_model_name="Finance_Incomes_Reason"' . "\n" .
                     '    AND frr.parent_id=fir.id' . "\n" .
                     '    AND frr.link_to=' . $this->get('id') . "\n" .
                     'WHERE fir.id IN (' . implode(', ', $advance_ids) . ')';
            $advanced_total = (double) array_sum($this->registry['db']->GetCol($query));

            // advance invoices could have credit notes so add their amount (it is negative)
            $credit_query = 'SELECT SUM(fir.total)' . "\n" .
                            'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                            'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                            '  ON frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id' . "\n" .
                            'WHERE frr.link_to_model_name="Finance_Incomes_Reason"' . "\n" .
                            '  AND frr.link_to IN (' . implode(', ', $advance_ids) . ')' . "\n" .
                            '  AND fir.type=' . PH_FINANCE_TYPE_CREDIT_NOTICE . "\n" .
                            '  AND fir.status="finished"' . "\n" .
                            '  AND fir.annulled_by=0' . "\n" .
                            '  AND fir.advance=' . $this->get('type');
            $advanced_total += (double)$this->registry['db']->GetOne($credit_query);
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        return $advanced_total;
    }

    /**
     * Get customer advances
     *
     * @return array - result of the operation
     */
    public function getAllCustomerAdvances() {

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $reasonId = $this->get('id');
        $advanceType = $this->get('type');
        $link_to_model_name = 'Finance_Incomes_Reason';
        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            //this is an invoice or another system finance type
            //get the id of the reason document
            $query = 'SELECT frr.*,  fir.type' . "\n" .
                'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason" ' . "\n" .
                'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n";
            //get parent id
            $data = $this->registry['db']->GetRow($query);
            if (!empty($data)) {
                if ($data['link_to_model_name'] == 'Finance_Incomes_Reason' && $data['type'] > PH_FINANCE_TYPE_MAX) {
                    //the invoice is related with income reason
                    $link_to_model_name = 'Finance_Incomes_Reason';
                    $reasonId = $data['link_to'];
                    $advanceType = $data['type'];
                } elseif ($data['link_to_model_name'] == 'Contract') {
                    //invoice from contract
                    $link_to_model_name = 'Contract';
                    $reasonId = $data['link_to'];
                    $advanceType = 1;
                }
            }
        }

        $modelLang = $this->get('lang');
        $companyId = $this->get('company');
        $customerId = $this->get('customer');
        $currency = $this->get('currency');
        $typeInvoice = PH_FINANCE_TYPE_INVOICE;
        $typeProformaInvoice = PH_FINANCE_TYPE_PRO_INVOICE;
        $tblFinIncomes = DB_TABLE_FINANCE_INCOMES_REASONS;
        $tblFinIncomesI18N = DB_TABLE_FINANCE_INCOMES_REASONS_I18N;
        $tblFinRelatives = DB_TABLE_FINANCE_REASONS_RELATIVES;
        $tblGT2 = DB_TABLE_GT2_DETAILS;

        $query = "SELECT fir.id, fir2.id as reason_id, fir3.id as proforma_id, fir.num, fir.issue_date, firi18n.name, fir.total_vat_rate,
                         ROUND(fir.total, {$gt2_total_precision}) AS total, fir.currency,
                         IF(fir2.id={$reasonId}, fir.id, NULL) AS checked,
                         IF(fir3.id IS NOT NULL, fir3.id, NULL) AS from_proforma,
                         IF(fir3.id IS NOT NULL, fir3.num, NULL) AS from_proforma_num,
                         IF(fir3.id IS NOT NULL, fir3.issue_date, NULL) AS from_proforma_issue_date,
                         IF(fir3.id IS NOT NULL, fir3i18n.name, NULL) AS from_proforma_name,
                         IF(frr.`shared`!='',
                             frr.`shared`,
                             -- use the total of a advance invoice as a full amount shared,
                             -- for the records before adding `shared` column
                             -- if no related reason -> nothing is shared yet
                             IF (fir2.id IS NOT NULL, fir.total, 0)
                         ) as `shared`
                  FROM {$tblFinIncomes} as fir

                  -- JOIN TO REASONS
                  LEFT JOIN {$tblFinRelatives} AS frr
                    ON (fir.id=frr.parent_id AND frr.parent_model_name='Finance_Incomes_Reason'
                         AND frr.link_to_model_name='Finance_Incomes_Reason') AND frr.link_to IN (SELECT id FROM fin_incomes_reasons WHERE TYPE={$advanceType})
                  LEFT JOIN {$tblFinIncomes} AS fir2
                    ON (fir2.id=frr.link_to)

                  -- JOIN TO PROFORMA (the advance invoice is issued from proforma)
                  LEFT JOIN {$tblFinRelatives} AS frr2
                    ON (fir.id=frr2.parent_id AND frr2.parent_model_name='Finance_Incomes_Reason'
                         AND frr2.link_to_model_name='Finance_Incomes_Reason'
                         -- ignore the relations to reasons (only proforma needed here)
                         AND (frr.link_to IS NULL OR frr.link_to != frr2.link_to)
                         AND frr2.link_to IN (SELECT id FROM fin_incomes_reasons WHERE TYPE={$typeProformaInvoice}))
                  LEFT JOIN {$tblFinIncomes} AS fir3
                    ON (fir3.id=frr2.link_to AND fir3.type={$typeProformaInvoice})
                  LEFT JOIN {$tblFinIncomesI18N} AS fir3i18n
                    ON (fir3.id=fir3i18n.parent_id AND fir3i18n.lang='{$modelLang}')

                  -- the proforma should be related to the reason!
                  -- LEFT JOIN fin_reasons_relatives AS frr3
                  --   ON (fir3.id=frr3.parent_id AND frr3.parent_model_name='Finance_Incomes_Reason'
                  --        AND frr3.link_to_model_name='Finance_Incomes_Reason'
                  --        AND frr3.link_to={$reasonId}
                  --        AND frr.link_to IS NULL)

                  LEFT JOIN {$tblFinIncomesI18N} AS firi18n
                    ON (fir.id=firi18n.parent_id AND firi18n.lang='{$modelLang}')
                  WHERE fir.customer={$customerId}
                    AND fir.advance={$advanceType}
                    AND fir.type={$typeInvoice}
                    AND fir.status='finished'
                    AND fir.annulled_by=0
                    AND fir.currency='{$currency}'
                    AND fir.company={$companyId}
                    AND (
                        fir2.type={$advanceType} OR frr.link_to IS NULL
                        -- invoice is not from proforma and is not checked yet
                        OR frr.link_to IS NULL AND frr2.link_to IS NULL -- AND frr3.link_to IS NULL
                        -- invoice is from proforma which is related to the reason
                        -- OR frr.link_to IS NULL AND frr3.link_to IS NOT NULL
                    )
                  HAVING
                      -- REMOVE unrelated and those related to another reason
                      (`reason_id` IS NULL OR `reason_id`='{$reasonId}')
                      -- REMOVE already completely shared
                      OR (`shared` < `total` OR `checked`)
                  ORDER BY checked DESC, fir.id DESC";
        $records = $this->registry['db']->GetAll($query);

        //get invoiced sum per advance invoice
        $invoicedAdvancesAmountsReason = $invoicedAdvancesAmountsOther = array();
        if (!empty($records)) {
            $advanceIdsCSV = implode(',', array_column($records, 'id'));

            //invoices (invoices, proforma) directly related to reason
            $query = "SELECT gt2.article_barcode, SUM(gt2.subtotal_with_discount*-1)
                  FROM {$tblGT2} as gt2
                  JOIN {$tblFinIncomes} as fir
                    ON gt2.model='Finance_Incomes_Reason'
                       AND gt2.model_id=fir.id
                       AND (fir.type={$typeInvoice} OR fir.type={$typeProformaInvoice})
                       AND fir.status='finished'
                       AND fir.annulled_by=0
                  JOIN {$tblFinRelatives} as frr
                    ON frr.parent_id=fir.id AND frr.parent_model_name='Finance_Incomes_Reason'
                      AND frr.link_to={$reasonId} AND frr.link_to_model_name='Finance_Incomes_Reason'
                  -- in case it is proforma relate to the invoice (there should be no invoice)
                  LEFT JOIN {$tblFinRelatives} as frr2
                    ON fir.type={$typeProformaInvoice}
                      AND frr2.parent_model_name='Finance_Incomes_Reason'
                      AND frr2.link_to=fir.id AND frr2.link_to_model_name='Finance_Incomes_Reason'
                  -- fir2 is for invoice issued from proforma (there should be no invoice, not to dublicate it)
                  LEFT JOIN {$tblFinIncomes} as fir2
                    ON frr2.parent_id=fir2.id
                       AND fir2.type={$typeInvoice}
                       AND fir2.status='finished'
                       AND fir2.annulled_by=0
                  WHERE gt2.article_barcode IN ($advanceIdsCSV) AND fir2.id IS NULL
                  GROUP BY gt2.article_barcode";
            $invoicedAdvancesAmountsReason = $this->registry['db']->GetAssoc($query);

            //invoices issued from proforma that are directly related to reason
            $query = "SELECT gt2.article_barcode, SUM(gt2.subtotal_with_discount*-1)
                  FROM {$tblGT2} as gt2
                  JOIN {$tblFinIncomes} as fir
                    ON fir.type={$typeProformaInvoice}
                       AND fir.status='finished'
                       AND fir.annulled_by=0
                  -- reason to proforma relation
                  JOIN {$tblFinRelatives} as frr
                    ON frr.parent_id=fir.id AND frr.parent_model_name='Finance_Incomes_Reason'
                      AND frr.link_to={$reasonId} AND frr.link_to_model_name='Finance_Incomes_Reason'
                  -- proforma to invoice relation
                  JOIN {$tblFinRelatives} as frr2
                    ON frr2.parent_model_name='Finance_Incomes_Reason'
                      AND frr2.link_to=fir.id AND frr2.link_to_model_name='Finance_Incomes_Reason'
                  JOIN {$tblFinIncomes} as fir2
                    ON frr2.parent_id=fir2.id
                       AND gt2.model='Finance_Incomes_Reason'
                       AND gt2.model_id=fir2.id
                       AND fir2.type={$typeInvoice}
                       AND fir2.status='finished'
                       AND fir2.annulled_by=0
                  WHERE gt2.article_barcode IN ({$advanceIdsCSV})
                  GROUP BY gt2.article_barcode";
            $invoicedAdvancesViaProformaAmountsReason = $this->registry['db']->GetAssoc($query);
            if (!empty($invoicedAdvancesViaProformaAmountsReason)) {
                $invoicedAdvancesAmountsReason += $invoicedAdvancesViaProformaAmountsReason;
                $query = "SELECT fir2.id
                  FROM {$tblGT2} as gt2
                  JOIN {$tblFinIncomes} as fir
                    ON fir.type={$typeProformaInvoice}
                       AND fir.status='finished'
                       AND fir.annulled_by=0
                  JOIN {$tblFinRelatives} as frr
                    ON frr.parent_id=fir.id AND frr.parent_model_name='Finance_Incomes_Reason'
                      AND frr.link_to={$reasonId} AND frr.link_to_model_name='Finance_Incomes_Reason'
                  JOIN {$tblFinRelatives} as frr2
                    ON frr2.parent_model_name='Finance_Incomes_Reason'
                      AND frr2.link_to=fir.id AND frr2.link_to_model_name='Finance_Incomes_Reason'
                  JOIN {$tblFinIncomes} as fir2
                    ON frr2.parent_id=fir2.id
                       AND gt2.model='Finance_Incomes_Reason'
                       AND gt2.model_id=fir2.id
                       AND fir2.type={$typeInvoice}
                       AND fir2.status='finished'
                       AND fir2.annulled_by=0
                  WHERE gt2.article_barcode IN ({$advanceIdsCSV})
                  GROUP BY gt2.article_barcode";
                $indirectInvoiceViaProformaIdsCSV = implode(', ', $this->registry['db']->GetCol($query));
            }

            $query = "SELECT gt2.article_barcode, SUM(gt2.subtotal_with_discount*-1)
                  FROM {$tblGT2} as gt2
                  JOIN {$tblFinIncomes} as fir
                    ON gt2.model='Finance_Incomes_Reason'
                       AND gt2.model_id=fir.id
                       AND fir.type={$typeInvoice}
                       AND fir.status='finished'
                       AND fir.annulled_by=0
                  JOIN {$tblFinRelatives} as frr
                    ON frr.parent_id=fir.id AND frr.parent_model_name='Finance_Incomes_Reason'
                      AND frr.link_to!={$reasonId} AND frr.link_to_model_name='Finance_Incomes_Reason'
                      -- exclude invoices to proforma relations:
                      AND frr.link_to IN (SELECT id FROM fin_incomes_reasons WHERE type>100)
                  WHERE gt2.article_barcode IN ({$advanceIdsCSV})" .
                (!empty($indirectInvoiceViaProformaIdsCSV) ? " AND fir.id NOT IN ({$indirectInvoiceViaProformaIdsCSV}) ":"")
                  . "GROUP BY gt2.article_barcode";
            $invoicedAdvancesAmountsOther = $this->registry['db']->GetAssoc($query);
        }

        $advances = array();
        foreach ($records as $rec) {
            $invoicedReason = (float) ($invoicedAdvancesAmountsReason[$rec['id']]??0);
            $invoicedOther = (float) ($invoicedAdvancesAmountsOther[$rec['id']]??0);
            if (!array_key_exists($rec['id'], $advances)) {
                $rec['remainder'] = $rec['total'];
                $rec['invoiced_reason'] = $invoicedReason;
                $rec['invoiced_other'] = $invoicedOther;
                $rec['invoiced'] = $invoicedReason + $invoicedOther;
                $advances[$rec['id']] = $rec;
            }
            if (empty($rec['checked'])) {
                $advances[$rec['id']]['remainder'] -= $rec['shared'];
            }
        }

        $this->set('customer_advances', $advances, true);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $advances;
    }

    /**
     * Get advances max shareable amount
     *
     * @return array - result of the operation
     */
    public function getAdvancesMaxSharableAmount() {
        $customerAdvances = $this->isDefined('customer_advances') ?
            $this->get('customer_advances') :
            $this->getAllCustomerAdvances();

        $customerAdvancesThisReasonOnly = array_filter($customerAdvances, function($a) {
            return $a['remainder'] > 0 && $a['checked'];
        });

        //define max sharable amount
        $reasonPaidAmountWithVat = $this->getPaidAmount();
        $paidPaidProformaTotalAmountNoVat = $this->getPaidUninvoicedProformasTotalAmount();
        $invoicedAmountWithVAT = $this->getInvoicedAmount(true);
        $reasonPaidAmountNoVat = $reasonPaidAmountWithVat/(1 + $this->get('total_vat_rate')/100);
        $invoicedAmountNoVat = $invoicedAmountWithVAT/(1 + $this->get('total_vat_rate')/100);
        $sharedAmountNoVat = array_sum(array_column($customerAdvancesThisReasonOnly, 'shared'));
        $maxSharableAmountNoVat = $this->get('total') - $reasonPaidAmountNoVat - $paidPaidProformaTotalAmountNoVat - $invoicedAmountNoVat + $sharedAmountNoVat;

        return $maxSharableAmountNoVat;
    }

    /**
     * Get customer proforma advances
     *
     * @param bool $all - if false - get only invoiced advance proforma invoices,
     *                    if true - get both invoiced and uninvoiced advance proforma invoices
     * @return array - result of the operation
     */
    public function getCustomerProformaAdvances($all = false) {

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        $link_to_id = $this->get('id');
        $advance_type = $this->get('type');
        $link_to_model_name = 'Finance_Incomes_Reason';
        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            //this is an invoice or another system finance type
            //get the id of the reason document or contract
            $query = 'SELECT frr.*,  fir.type' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n";
            //get parent id
            $data = $this->registry['db']->GetRow($query);
            if (!empty($data)) {
                if ($data['link_to_model_name'] == 'Finance_Incomes_Reason' && $data['type'] > PH_FINANCE_TYPE_MAX) {
                    //the invoice is related to income reason
                    $link_to_model_name = 'Finance_Incomes_Reason';
                    $link_to_id = $data['link_to'];
                    $advance_type = $data['type'];
                } elseif ($data['link_to_model_name'] == 'Contract') {
                    //invoice from contract
                    $link_to_model_name = 'Contract';
                    $link_to_id = $data['link_to'];
                    $advance_type = 1;
                }
            }
        }
        $query = 'SELECT fir.id, fir.num, fir.issue_date, firi18n.name, ' . "\n" .
                 '  ROUND(fir.total, ' . $gt2_total_precision . ') AS total, fir.currency, ' . "\n" .
                 '  (SELECT MAX(fir2.id) ' . "\n" .
                 '    FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir2, ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
                 '    WHERE frr2.link_to=fir.id' . "\n" .
                 '      AND frr2.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                 '      AND frr2.link_to_model_name="Finance_Incomes_Reason"' . "\n" .
                 '      AND frr2.parent_id=fir2.id AND fir2.annulled_by=0 AND fir2.type=' . PH_FINANCE_TYPE_INVOICE . ') AS checked' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
                 '  ON (fir.id=firi18n.parent_id AND firi18n.lang="' . $this->get('lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 '  ON (frr.parent_id=fir.id' . "\n" .
                 '    AND frr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                 '    AND frr.link_to_model_name="' . $link_to_model_name . '")' . "\n" .
                 'WHERE fir.customer=' . $this->get('customer') . "\n" .
                 '  AND frr.link_to=' . $link_to_id . "\n" .
                 '  AND fir.status="finished"' . "\n" .
                 '  AND frr.parent_id > 0' . "\n" .
                 '  AND fir.type=' . PH_FINANCE_TYPE_PRO_INVOICE . "\n" .
                 '  AND fir.annulled_by=0' . "\n" .
                 '  AND fir.advance=' . $advance_type . "\n" .
                 ($all ? '' : 'HAVING checked IS NOT NULL ' . "\n") .
                 'ORDER BY checked DESC';
        $records = $this->registry['db']->GetAll($query);
        $this->set('customer_advances', $records, true);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Calculates the maximum sum of a reason to be shared among advances
     * Important: remove the paid proforma sum
     *
     * @param $includeVat
     * @return float
     */
    public function getMaxAdvanceTotalToShare($includeVat = false):float {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        //sum including VAT
        $maxAdvanceSumToShare = $this->get('total_with_vat') - $this->getProformasPaidAmount();
        if (!$includeVat) {
            $precision = $this->registry['config']->getParam('precision', 'gt2_total_with_vat');
            //remove VAT
            $maxAdvanceSumToShare = round($maxAdvanceSumToShare/(1 +$this->get('vat_rate')/100), $precision);
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        return (float) $maxAdvanceSumToShare;
    }

    /**
     * Updates relations between advances or proforma advances and incomes reason -
     * adds relation to newly added ones and if $delete = true,
     * deletes old advances that are not present in 'new_advances' property of model.
     *
     * @param bool $delete - if true, deletes old advances
     * @param int $model_type - invoice or proforma
     * @return bool - result of the operation
     */
    public function updateAdvances($delete = true, $model_type = PH_FINANCE_TYPE_INVOICE) {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $db = $this->registry['db'];
        $db->StartTrans();

        //get related advances (invoices or proformas)
        $query = 'SELECT fir.id, IF(frr.`shared`!="", frr.`shared`, fir.total) as shared ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 '  ON frr.parent_id=fir.id' . "\n" .
                 '  AND frr.link_to=' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                 '  AND frr.link_to_model_name="Finance_Incomes_Reason"' . "\n" .
                 'WHERE fir.customer=' . $this->get('customer') . "\n" .
                 '  AND fir.company="' . $this->get('company') . '"' . "\n" .
                 '  AND fir.type=' . $model_type . "\n" .
                 '  AND fir.status="finished"' . "\n" .
                 '  AND fir.annulled_by=0' . "\n" .
                 '  AND fir.advance=' . $this->get('type');
        $ids = $db->GetAssoc($query);
        $new_advances = $this->get('new_advances');
        $new_ids = array_keys($new_advances);

        if ($model_type == PH_FINANCE_TYPE_INVOICE) {
            $all_advances = $this->getAllCustomerAdvances();
            foreach ($new_ids as $nid) {
                $advance = $all_advances[$nid];
                $remainder = $advance['remainder'] ?? 0;
                $shared = $new_advances[$nid]['value'] ?? 0;
                if ($shared > $remainder) {
                    $this->raiseError('error_finance_incomes_reasons_advance_over', '', '', array(
                        $shared . ' ' . $advance['currency'],
                        sprintf(
                            '%s %s',
                            General::strftime($this->i18n('date_short'), $advance['issue_date']),
                            $advance['num']
                        ),
                        $remainder . ' ' . $advance['currency']
                    ));
                }
            }
            if (!$this->valid) {
                return false;
            }
        }

        if ($delete) {
            //set old advances to model for audit
            $this->set('old_advances', array_keys($ids), true);
            //deleted advances
            $deleted_ids = array_diff(array_keys($ids), $new_ids);
            if ($deleted_ids) {
                //delete advances relations to reason
                $query = 'DELETE FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                         'WHERE parent_id IN (' . implode(',', $deleted_ids) . ')' . "\n" .
                         '  AND link_to=' . $this->get('id') . "\n" .
                         '  AND parent_model_name="Finance_Incomes_Reason"' . "\n" .
                         '  AND link_to_model_name="Finance_Incomes_Reason"';
                $db->Execute($query);
            }
        }

        if ($model_type == PH_FINANCE_TYPE_INVOICE) {
            // create a dummy invoice model with some properties to check if VAT should be added or removed
            $params = array('type' => $model_type,
                            'company' => $this->get('company'),
                            'total_vat_rate' => $this->get('total_vat_rate'));
            $dummy_invoice = new Finance_Incomes_Reason($this->registry, $params);

            if (!empty($new_ids)) {
                //get VAT rates for all new advances to check if they are the same
                $query = 'SELECT id, total_vat_rate FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                         'WHERE id IN (' . implode(',', $new_ids) . ')';
                $new_ids_vat = $db->GetAssoc($query);
                //get ONLY RELATED customer advances ($all = false)
                $vat = reset($new_ids_vat);

                // we will use the VAT rate of the first invoice for the check
                $dummy_invoice->set('total_vat_rate', $vat, true);

                // if value is true, then current action will add VAT to reason because
                // VAT settings for type+company of reason are include_VAT=1 and it is not invoiced yet;
                // in all other cases, value will be false
                $check_update_VAT = $dummy_invoice->checkUpdateReasonVAT(true, $this);

                foreach ($new_ids_vat as $vr) {
                    if ($vr != $vat || $vr != $this->get('total_vat_rate') && !$check_update_VAT) {
                        //different VAT rate so fail the trans and show error
                        $this->raiseError('error_finance_incomes_reasons_different_advances_vat');
                        $db->FailTrans();
                        break;
                    }
                }

                if (!$db->HasFailedTrans() && $check_update_VAT) {
                    //VAT rate for the incomes reason has not been set yet
                    $this->updateReasonVAT(array('total_vat_rate' => $vat));
                }
            // here we check if VAT should be removed
            } elseif ($dummy_invoice->checkUpdateReasonVAT(false, $this)) {
                //all the advances have been removed
                //remove the VAT of the reason too
                $this->updateReasonVAT(array('total_vat_rate' => 0));
            }

            unset($dummy_invoice);
        }

        //added advances
        $added_ids = array_diff($new_ids, array_keys($ids));
        if ($added_ids) {
            //add related advances
            $set = array();
            foreach ($added_ids as $add_id) {
                $set['parent_id'] = $add_id;
                $set['parent_model_name'] = '"Finance_Incomes_Reason"';
                $set['link_to'] = $this->get('id');
                $set['link_to_model_name'] = '"Finance_Incomes_Reason"';
                $set['rows_links'] = '""';
                $set['changes'] = '""';
                $set['shared'] = $new_advances[$add_id]['value']??0;
                $insert[] = '(' . implode(', ', $set) . ')';
            }
            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     'VALUES ' . implode(',', $insert) . "\n";
            $db->Execute($query);
        }

        foreach($new_advances as $new_id => $new_advance) {
            if (!in_array($new_id, $added_ids) && array_key_exists($new_id, $ids) && $new_advance['value'] != $ids[$new_id]) {
                $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                    'SET `shared`="'.$new_advance['value'].'"' . "\n" .
                    'WHERE parent_id=' . $new_id . "\n" .
                    ' AND parent_model_name="Finance_Incomes_Reason"' . "\n" .
                    ' AND link_to=' . $this->get('id') . "\n" .
                    ' AND link_to_model_name="Finance_Incomes_Reason"';
                $db->Execute($query);
            }
        }

        // get up to date value of "total_with_vat" from database
        $query_reason_total = 'SELECT total_with_vat FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                              'WHERE id=' . $this->get('id');
        $reason_total = (double)$db->getOne($query_reason_total);

        // validate only when editing relations to advance invoices:
        // paid amount to reason and advance proformas should not exceed non-invoiced amount
        if (!$db->HasFailedTrans() && $model_type == PH_FINANCE_TYPE_INVOICE && $delete &&
        ($this->getPaidAmount() + $this->getProformasPaidAmount() + $this->getInvoicedAmount(true)) > $reason_total) {
            $paidRelativesLinks = $this->preparePaidRelativesError(true);
            if ($paidRelativesLinks) {
                $this->raiseError(
                    'error_finance_incomes_reasons_paid_relatives',
                    '',
                    '',
                    array($paidRelativesLinks)
                );
            } else {
                $this->raiseError('error_finance_incomes_reasons_paid_relatives2');
            }
            $db->FailTrans();
            $db->CompleteTrans();
            if ($unsanitize) {
                $this->sanitize();
            }
            return false;
        }

        if (!$db->HasFailedTrans()) {
            // get up-to-date payment status and set it to model
            $query = 'SELECT payment_status FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' WHERE id=' . $this->get('id');
            $this->set('payment_status', $db->GetOne($query), true);
        }

        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();
        $result = !$dbTransError;

        if ($unsanitize) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Get paid amount for this financial document and set it as a property to model.
     *
     * @param array $params - when specified parameters
     *                        ('paid_to_model_name' and 'paid_to' for credit notes,
     *                        'parent_model_name' and 'parent_id' for other revenue documents)
     *                        get paid amount only for specified record
     * @return double - paid amount
     */
    public function getPaidAmount($params = array()) {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
            $query = 'SELECT SUM(paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                     'WHERE fr.parent_id=' . $this->get('id') . "\n" .
                     '  AND parent_model_name="' . $this->modelName . '"';
            if (isset($params['paid_to_model_name']) && isset($params['paid_to'])) {
                $query .= ' AND paid_to_model_name="' . $params['paid_to_model_name'] .
                          '" AND paid_to=' . $params['paid_to'];
            }
        } else {
            $query = 'SELECT SUM(paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                     'WHERE fr.paid_to=' . $this->get('id') . "\n" .
                     '  AND paid_to_model_name="' . $this->modelName . '"';
            if (isset($params['parent_model_name']) && isset($params['parent_id'])) {
                $query .= ' AND parent_model_name="' . $params['parent_model_name'] .
                          '" AND parent_id=' . $params['parent_id'];
            }
        }
        $record = (double)$this->registry['db']->GetOne($query);
        if (isset($params['parent_model_name']) && isset($params['parent_id'])) {
            // paid amount for $params['paid_to'] expenses reason, payment etc.
           $this->set('paid_amount_' . $params['parent_model_name'] . '_' . $params['parent_id'], $record, true);
        } elseif (isset($params['paid_to_model_name']) && isset($params['paid_to'])) {
            // paid amount for $params['parent_id'] incomes invoice, expenses credit notice, payment etc.
            $this->set('paid_amount_' . $params['paid_to_model_name'] . '_' . $params['paid_to'], $record, true);
        } else {
            // paid amount for all financial documents and payments
            $this->set('paid_amount', $record, true);
        }
        if ($unsanitize) {
            $this->sanitize();
        }

        return $record;
    }

    /**
     * Get total amount paid to non-invoiced proformas for reason
     *
     * @param bool $force - defines if the function will return the value, if property exists in the model (on FALSE), or will retake it again (on TRUE)
     * @param string $additional_where - additional condition (used to exclude parent proforma when issuing invoice form proforma)
     * @return double - paid amount
     */
    public function getProformasPaidAmount($force = false, $additional_where = '') {
        if ($this->get('proformas_paid_amount') && !$force) {
            return $this->get('proformas_paid_amount');
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        $proformas_paid_amount = 0;
        if ($this->get('type') > PH_FINANCE_TYPE_MAX) {
            $sql_paid_amount = 'SELECT SUM(paid_amount) AS amount' . "\n" .
                               'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                               'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                               '  ON (frr.link_to_model_name ="Finance_Incomes_Reason" AND frr.link_to=\'' . $this->get('id') . '\'' . "\n" .
                               '    AND frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id)' . "\n" .
                               'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                               '  ON (fr.paid_to=fir.id AND fr.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
                               'WHERE fir.type = ' . PH_FINANCE_TYPE_PRO_INVOICE . "\n" .
                               '  AND fir.annulled_by=0 ' . "\n" .
                               '  AND fir.status="finished"' . "\n" .
                               '  AND fir.payment_status!="invoiced"' . "\n" .
                               ($additional_where ? '  AND ' . $additional_where : '');
            $proformas_paid_amount = (double)$this->registry['db']->GetOne($sql_paid_amount);
        }
        if ($unsanitize) {
            $this->sanitize();
        }

        $this->set('proformas_paid_amount', $proformas_paid_amount, true);

        return $proformas_paid_amount;
    }

    /**
     * Get total amount of proformas that are paid
     *
     * @param bool $force - defines if the function will return the value, if property exists in the model (on FALSE), or will retake it again (on TRUE)
     * @param string $additional_where - additional condition (used to exclude parent proforma when issuing invoice form proforma)
     * @return double - paid amount
     */
    public function getPaidUninvoicedProformasTotalAmount($force = false, $additional_where = '') {
        if ($this->get('paid_proformas_total_amount') && !$force) {
            return $this->get('paid_proformas_total_amount');
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        $paid_proformas_total_amount = 0;
        if ($this->get('type') > PH_FINANCE_TYPE_MAX) {
            $sql_paid_amount = 'SELECT SUM(total) AS amount' . "\n" .
                'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                '  ON (frr.link_to_model_name ="Finance_Incomes_Reason" AND frr.link_to=\'' . $this->get('id') . '\'' . "\n" .
                '    AND frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id)' . "\n" .
                'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                '  ON (fr.paid_to=fir.id AND fr.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
                'WHERE fir.type = ' . PH_FINANCE_TYPE_PRO_INVOICE . "\n" .
                '  AND fir.annulled_by=0 ' . "\n" .
                '  AND fir.status="finished"' . "\n" .
                '  AND fir.payment_status!="invoiced"' . "\n" .
                ($additional_where ? '  AND ' . $additional_where : '');
            $paid_proformas_total_amount = (double)$this->registry['db']->GetOne($sql_paid_amount);
        }
        if ($unsanitize) {
            $this->sanitize();
        }

        $this->set('paid_proformas_total_amount', $paid_proformas_total_amount, true);

        return $paid_proformas_total_amount;
    }

    /**
     * Function to get the full paid amount, including direct payments and all of its related finance documents
     *
     * @param bool $force - defines if the function will return the value, if property exists in the model (on FALSE), or will retake it again (on TRUE)
     * @return float - full paid amount
     */
    public function getFullPaidAmount($force = false) {
        if ($this->get('full_paid_amount') && !$force) {
            return $this->get('full_paid_amount');
        }

        $direct_paid_amount = $this->getSignedPaidAmount();

        //check the type of the current incomes reason
        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            // if it is custom user defined type (type id > 100) this amount is returned
            $this->set('full_paid_amount', $direct_paid_amount, true);
            return $this->get('full_paid_amount');
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        // paid amount to invoices + proformas
        $sql_invoiced_amount = 'SELECT IF(fir.advance=0, SUM(paid_amount), (frr.SHARED/fir.total)*SUM(paid_amount)) as amount' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
            '  ON (frr.link_to_model_name ="Finance_Incomes_Reason" AND frr.link_to=\'' . $this->get('id') . '\'' . "\n" .
            '    AND frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id)' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
            '  ON (fr.paid_to=fir.id AND fr.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
            'WHERE fir.type IN (\'' . PH_FINANCE_TYPE_INVOICE . '\', \'' . PH_FINANCE_TYPE_PRO_INVOICE . '\')' . "\n" .
            '  AND fir.annulled_by=0 AND fir.status="finished"' . "\n" .
            'GROUP BY frr.parent_id';
        $invoiced_amount = array_sum($this->registry['db']->GetCol($sql_invoiced_amount));

        // paid amount to debit notices
        $sql_debit_amount = 'SELECT SUM(paid_amount) as amount' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
            '  ON (frr.link_to=\'' . $this->get('id') . '\' AND frr.link_to_model_name="Finance_Incomes_Reason" AND frr.parent_model_name="Finance_Incomes_Reason")' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
            '  ON (frr2.parent_id=fir.id AND frr2.parent_model_name="Finance_Incomes_Reason" AND frr2.link_to_model_name="Finance_Incomes_Reason" AND frr2.link_to=frr.parent_id)' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
            '  ON (fr.paid_to=frr2.parent_id AND fr.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
            'WHERE fir.type=\'' . PH_FINANCE_TYPE_DEBIT_NOTICE . '\' AND fir.annulled_by=0 AND fir.status="finished"';
        $debit_amount = $this->registry['db']->GetOne($sql_debit_amount);

        // paid amount to credit notices
        $sql_credit_amount = 'SELECT SUM(paid_amount) as amount' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
            '  ON (frr.link_to=' . $this->get('id') . ' AND frr.link_to_model_name="Finance_Incomes_Reason" AND frr.parent_model_name="Finance_Incomes_Reason")' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
            '  ON (frr2.parent_id=fir.id AND frr2.parent_model_name="Finance_Incomes_Reason" AND frr2.link_to_model_name="Finance_Incomes_Reason"  AND frr.parent_id=frr2.link_to)' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
            '  ON (fr.parent_id=frr2.parent_id AND fr.parent_model_name ="Finance_Incomes_Reason")' . "\n" .
            'WHERE fir.type=\'' . PH_FINANCE_TYPE_CREDIT_NOTICE . '\' AND fir.annulled_by=0 AND fir.status="finished"';
        $credit_amount = $this->registry['db']->GetOne($sql_credit_amount);

        $full_paid_amount = $direct_paid_amount + $invoiced_amount + $debit_amount - $credit_amount;

/* CODE TO FIX THE INCORRECT CALCULATION OF THE PAID SUM
 * THE PROBLEM IS THAT AFTER A REVISION OF THE CODE WE DID NOT SUCCEED TO REPEAT THE EXAMPLE WHICH CAUSED THE PROBLEM
        // paid amount to invoices + proformas
        $sql_invoiced_amount = 'SELECT fir.id as idx, paid_amount as amount' . "\n" .
                               'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                               'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                               '  ON (frr.link_to_model_name ="Finance_Incomes_Reason" AND frr.link_to=\'' . $this->get('id') . '\'' . "\n" .
                               '    AND frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id)' . "\n" .
                               'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                               '  ON (fr.paid_to=fir.id AND fr.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
                               'WHERE fir.type IN (\'' . PH_FINANCE_TYPE_INVOICE . '\', \'' . PH_FINANCE_TYPE_PRO_INVOICE . '\')' . "\n" .
                               '  AND fir.annulled_by=0 AND fir.status="finished"';
        $invoiced_amount = $this->registry['db']->GetAssoc($sql_invoiced_amount);

        // paid amount to debit notices
        $sql_debit_amount = 'SELECT fir.id as idx, paid_amount' . "\n" .
                            'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                            '  ON (frr.link_to=\'' . $this->get('id') . '\' AND frr.link_to_model_name="Finance_Incomes_Reason" AND frr.parent_model_name="Finance_Incomes_Reason")' . "\n" .
                            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
                            '  ON (frr2.parent_id=fir.id AND frr2.parent_model_name="Finance_Incomes_Reason" AND frr2.link_to_model_name="Finance_Incomes_Reason" AND frr2.link_to=frr.parent_id)' . "\n" .
                            'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                            '  ON (fr.paid_to=frr2.parent_id AND fr.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
                            'WHERE fir.type=\'' . PH_FINANCE_TYPE_DEBIT_NOTICE . '\' AND fir.annulled_by=0 AND fir.status="finished"';
        $debit_amount = $this->registry['db']->GetAssoc($sql_debit_amount);

        // paid amount to credit notices
        $sql_credit_amount = 'SELECT SUM(paid_amount) as amount' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                             'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                             '  ON (frr.link_to=' . $this->get('id') . ' AND frr.link_to_model_name="Finance_Incomes_Reason" AND frr.parent_model_name="Finance_Incomes_Reason")' . "\n" .
                             'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
                             '  ON (frr2.parent_id=fir.id AND frr2.parent_model_name="Finance_Incomes_Reason" AND frr2.link_to_model_name="Finance_Incomes_Reason"  AND frr.parent_id=frr2.link_to)' . "\n" .
                             'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                             '  ON (fr.parent_id=frr2.parent_id AND fr.parent_model_name ="Finance_Incomes_Reason" ' . "\n" .
                             '      AND (fr.paid_to_model_name!="Finance_Incomes_Reason" OR fr.paid_to NOT IN ("' . implode('","', array_merge(array_keys($invoiced_amount), array_keys($debit_amount))) . '")))' . "\n" .
                             'WHERE fir.type=\'' . PH_FINANCE_TYPE_CREDIT_NOTICE . '\' AND fir.annulled_by=0 AND fir.status="finished"';
        $credit_amount = $this->registry['db']->GetOne($sql_credit_amount);

        $full_paid_amount = $direct_paid_amount + array_sum($invoiced_amount) + array_sum($debit_amount) - $credit_amount;
*/
        $this->set('full_paid_amount', $full_paid_amount, true);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $this->get('full_paid_amount');
    }

    /**
     * Get paid amount for this financial document and set it as a property to model.
     * Amount is positive for all types but for credit notes.
     *
     * @param bool $force - if true, force getting value from database even when property is set to model
     * @return float - result of the operation
     */
    public function getSignedPaidAmount($force = false) {
        if (!$this->isDefined('paid_amount') || $force) {
            $this->getPaidAmount();
        }
        $this->set('signed_paid_amount', ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE ? -1 : 1) * $this->get('paid_amount'), true);

        return $this->get('signed_paid_amount');
    }

    /**
     * Get payments
     *
     * @param array $params -
     * @return float - result of the operation
     */
    public function getInvoicePaid($params = array()) {

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
            $query = 'SELECT -1*SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     ', ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     ' WHERE fir.id=' . $this->get('id') . "\n" .
                     ' AND fr.parent_id=fir.id' . "\n" .
                     ' AND fr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                     //   ' AND fir.type =' . PH_FINANCE_TYPE_INVOICE . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"';
        } else {
            $query = 'SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     ', ' . DB_TABLE_FINANCE_BALANCE . ' as fr ' . "\n" .
                     ' WHERE fir.id=' . $this->get('id') . "\n" .
                     ' AND fr.paid_to=fir.id' . "\n" .
                     ' AND fr.paid_to_model_name="Finance_Incomes_Reason"' . "\n" .
                     //   ' AND fir.type =' . PH_FINANCE_TYPE_INVOICE . "\n" .
                     ' AND fir.annulled_by=0 AND fir.status="finished"';
        }
        // filter by 'added' field
        if (!empty($params['added'])) {
            $query .= ' AND ' . $params['added'];
        }

        $record = (double)$this->registry['db']->GetOne($query);

        $this->set('paid_invoices_amount', $record, true);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $record;
    }

    /**
     * Get total amount of all invoices and debit/credit notes related to this income reason
     *
     * @param bool $force - if true, force getting value from database even when property is set to model
     * @return float - result of the operation
     */
    public function getInvoicedAmount($force = false) {

        if ($this->isDefined('invoices_amount') && !$force) {
            return $this->get('invoices_amount');
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        //get the total of ALL (FINISHED!!!) invoices related to this income reason
        //IMPORTANT: advance invoices might have only part shared of their total
        //IMPORTANT: get the value including VAT
        $query = 'SELECT IF(fir.advance!=0 AND frr.shared!=0, ' . "\n" .
                 '               frr.shared*(1+fir.total_vat_rate/100),' . "\n" .
                 '               fir.total_with_vat) as invoiced FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr ' . "\n" .
                 ' WHERE frr.link_to=' . $this->get('id') . "\n" .
                 ' AND frr.link_to_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr.parent_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr.parent_id=fir.id AND fir.type =' . PH_FINANCE_TYPE_INVOICE . "\n" .
                 ' AND fir.annulled_by=0 AND fir.status="finished"';

        $total_invoices_quantities = $this->registry['db']->GetCol($query);
        $total_invoices = is_array($total_invoices_quantities) ? array_sum($total_invoices_quantities) : 0;

        //get the total of ALL debit/credit notes related to the invoices of this income reason
        $notices_list = array(PH_FINANCE_TYPE_DEBIT_NOTICE, PH_FINANCE_TYPE_CREDIT_NOTICE);
        $query = 'SELECT SUM(total_with_vat) FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr ' . "\n" .
                 ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr2 ' . "\n" .
                 ' WHERE frr.link_to=' . $this->get('id') . "\n" .
                 ' AND frr.link_to_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr.parent_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr2.parent_id=fir.id AND frr2.parent_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND fir.type in (' . implode(',', $notices_list) . ')' . "\n" .
                 ' AND frr2.link_to=frr.parent_id AND frr2.link_to_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND fir.annulled_by=0 AND fir.status="finished"';
        $total_debit_credit = (double)$this->registry['db']->GetOne($query);

        $invoices_amount = $total_invoices + $total_debit_credit;

        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');
        $invoices_amount = round($invoices_amount, $gt2_total_precision);

        //assign the total of all invoices and debit/credit notes to the income reason
        $this->set('invoices_amount', $invoices_amount, true);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $invoices_amount;
    }

    /**
     * Prepares links for paid reason and/or non-invoiced proformas for error messages.
     * Method is called either on reason or on proforma.
     *
     * @param mixed $include_self - whether to include current model or not;
     *                              when integer passed, it holds the id of the proforma to exclude
     * @return string - links for models
     */
    public function preparePaidRelativesError($include_self = false) {

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        // use filtering to get only what looking for
        $other_paid_filters = array('fir.annulled_by = 0',
                                    'fir.status = "finished"',
                                    'fir.payment_status IN ("paid", "partial")');

        // get reason model
        if ($this->get('type') > PH_FINANCE_TYPE_MAX) {
            $reason = clone $this;
        } elseif ($this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            $this->getRelatives(array('get_parent_reasons' => $other_paid_filters));
            $reason = $this->get('parent_reasons');
            if (!empty($reason)) {
                $reason = array_shift($reason);
            }
            $this->unsetProperty('parent_reasons', true);
        }

        $result = '';
        if (!empty($reason)) {
            if (!$include_self && $this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
                // exclude current proforma
                $other_paid_filters[] = 'fir.id != ' . $this->get('id');
            } elseif (is_int($include_self) && $include_self > 0) {
                // parameter also is the id of proforma to exclude
                $other_paid_filters[] = 'fir.id != ' . $include_self;
            }

            // get info for proformas
            $paid_relatives = array();
            $reason->getRelatives(array('get_pro_invoices' => $other_paid_filters));
            if ($reason->isDefined('pro_invoices')) {
                $paid_relatives = $reason->get('pro_invoices');
                $reason->unsetProperty('pro_invoices', true);
            }

            // include reason when it has directly paid amount
            if (($this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE || $include_self) && $reason->getPaidAmount() > 0) {
                if (!$reason->sanitized) {
                    $reason->sanitize();
                }
                array_unshift($paid_relatives, $reason);
            }

            $links = array();
            foreach ($paid_relatives as $p) {
                $links[] =
                    sprintf('<a href="%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=payments&amp;payments=%s&amp;selected_tab=payment" target="_blank">%s %s</a>',
                            $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'],
                            $p->get('id'), $p->getModelTypeName(), $p->get('num'));
            }
            $result = implode(', ', $links);
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Add payment
     *
     * @param array $params - input parameters (none for now)
     * @return bool - result of the operation
     */
    public function addPayment($params = array()) {

        $db = $this->registry['db'];

        $db->StartTrans();

        //get paid amount
        $paid_amount = $this->getPaidAmount();
        //amount left to pay
        $amount = round($this->get('total_with_vat'), 2) - $paid_amount;
        $amount = round($amount, 2);

        if ($amount > 0 && ($this->get('type') > PH_FINANCE_TYPE_MAX || $this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE)) {
            if ($this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
                // get parent reason
                $this->getRelatives(array('get_parent_reasons' => true));
                $reason = $this->get('parent_reasons');
                if (!empty($reason)) {
                    $reason = array_shift($reason);
                }
                $this->unsetProperty('parent_reasons', true);
            } else {
                // the reason is the current model
                $reason = $this;
            }

            if ($reason) {
                // the maximum possible amount that could be paid to the incomes reason
                $reason_available_amount = $reason->getRemainingAmount();
                if ($reason_available_amount <= 0) {
                    $paidRelativesLinks = $reason->preparePaidRelativesError($this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE);
                    if ($paidRelativesLinks) {
                        // no more payments could be added to reason or non-invoiced proformas
                        $this->raiseError(
                            'error_finance_incomes_reasons_paid_relatives',
                            '',
                            '',
                            array($paidRelativesLinks)
                        );
                    } else {
                        $this->raiseError('error_finance_incomes_reasons_paid_relatives2');
                    }
                    $db->CompleteTrans();
                    return false;
                } elseif ($this->get('type') > PH_FINANCE_TYPE_MAX || $reason_available_amount < $amount) {
                    // the amount from the payment that will be distributed to current document
                    $amount = round($reason_available_amount, 2);
                }
            }
        }

        // properties to be copied from financial document
        $set_from_old_model = array(
            'customer', 'customer_name',
            'trademark', 'trademark_name',
            'project', 'project_name', 'phase', 'phase_name',
            'currency', 'group', 'group_name',
            'employee', 'employee_name'
        );

        //create new payment
        require_once 'finance.payments.factory.php';
        $payment = new Finance_Payment($this->registry);
        foreach ($set_from_old_model as $param) {
            $reason_value = $this->get($param);
            if ($this->slashesEscaped) {
                $reason_value = General::slashesStrip($reason_value);
            }
            $payment->set($param, $reason_value, true);
        }

        // get the data for payment container
        preg_match('#^(\d+)_(\d+)_(cash|bank|cheque)_(\d+)$#', $this->get('payment_container'), $matches);
        $payment->set('company', @$matches[1], true);
        $payment->set('office', @$matches[2], true);
        $payment_type = @$matches[3];
        $payment->set('payment_type', (($payment_type == 'cheque') ? 'bank' : $payment_type), true);
        $payment->set('container_id', @$matches[4], true);
        $payment->set('container_rate', $this->get('container_rate'), true);
        $payment->set('container_amount', $this->get('container_amount'), true);
        $payment->set('container_currency', $this->get('container_currency'), true);

        // prepare container, company and office name
        if ($payment->get('company')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . "\n" .
                     'WHERE parent_id=' . $payment->get('company') . ' AND lang="' . $payment->get('model_lang') . '"';
            $company_name = $db->GetOne($query);
            $payment->set('company_name', $company_name, true);
        }
        if ($payment->get('container_id')) {
            $query = 'SELECT ti18n.name' . "\n" .
                     'FROM ' . ($payment->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS : DB_TABLE_FINANCE_CASHBOXES) . ' AS t' .  "\n" .
                     'LEFT JOIN ' . ($payment->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N : DB_TABLE_FINANCE_CASHBOXES_I18N) . ' AS ti18n' . "\n" .
                     '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $payment->get('model_lang') . '"' . "\n" .
                     'WHERE t.id=' . $payment->get('container_id');
            $container_name = $db->GetOne($query);
            $payment->set('container_name', $container_name, true);
        }
        if ($payment->get('office')) {
            $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                     'WHERE parent_id=' . $payment->get('office') . ' AND lang="' . $payment->get('model_lang') . '"';
            $office_name = $db->GetOne($query);
            $payment->set('office_name', $office_name, true);
        }

        if ($this->isDefined('payment_sum') && $this->get('payment_sum') > 0) {
            $payment->set('amount', $this->get('payment_sum'), true);
            if ($amount > $this->get('payment_sum')) {
                $amount = $this->get('payment_sum');
            }
        } elseif ($this->registry->get('action') != 'addpayment') {
            $payment->set('amount', $amount, true);
        }

        // define the payment date
        if ($this->get('payment_date')) {
            $payment->set('issue_date', $this->get('payment_date'), true);
        } elseif ($this->registry->get('action') != 'addpayment') {
            // in case of direct issue payment (without intermediate screen) the current date is taken
            $payment->set('issue_date', date('Y-m-d'), true);
        }

        if ($this->get('type') != PH_FINANCE_TYPE_CREDIT_NOTICE) {
            if ($payment->get('payment_type') == 'cash') {
                $payment->set('type', 'PKO', true);
            } elseif ($payment->get('payment_type') == 'bank') {
                $payment->set('type', 'BP', true);
            }
        } else {
            if ($payment->get('payment_type') == 'cash') {
                $payment->set('type', 'RKO', true);
            } elseif ($payment->get('payment_type') == 'bank') {
                $payment->set('type', 'PN', true);
            }
        }
        $payment->set('type_name', $this->i18n('finance_payments_type_' . $payment->get('type')), true);

        // define the payment reason
        if ($this->isDefined('payment_reason') && $this->get('payment_reason')) {
            $reason = $this->get('payment_reason');
        } else {
            $reason = sprintf('%s %s %s',
                              $this->i18n('finance_payment'),
                              $this->i18n('for'),
                              ($this->get('name') ? $this->get('name') : $this->getModelTypeName()));
        }
        if ($this->slashesEscaped) {
            $reason = General::slashesStrip($reason);
        }
        $payment->set('reason', $reason, true);

        $payment->set('status', 'finished', true);

        //save payment
        if ($payment->save()) {
            // save payment balance
            $payment->updateBalance(array('model_id' => $this->get('id'),
                                          'model_name' => 'Finance_Incomes_Reason',
                                          'amount' => $amount));
            $payment->slashesStrip();

            //add history and audit
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.history.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.audit.php';
            $old_payment = new Finance_Payment($this->registry);
            Finance_Payments_History::saveData($this->registry, array('model' => $payment, 'action_type' => 'add', 'new_model' => $payment, 'old_model' => $old_payment));

            //the transaction status could be checked only before CompleteTrans()
            $dbTransError = $db->HasFailedTrans();
            $result = !$dbTransError;
            if ($result) {
                $result = $payment->get('id');
            }
            $this->set('new_payment_id', $payment->get('id'), true);
        } else {
            $result = false;
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        return $result;
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Finance_Incomes_Reason\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted =  0'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        if (!empty($sanitize_after)) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get all patterns variables - basic/system, additional
     *
     * @return array $vars - variables
     */
    public function getPatternsVars() {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Finance_Incomes_Reason", "Customer", "CurrentUser") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $customer_translations = $customer->getTranslations();
        $t_customer = array();
        foreach ($customer_translations as $t_lang) {
            if ($t_lang != $customer->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $customer->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
            }
        }

        //get the pattern
        $pattern_id = $this->registry['request']->get('pattern');
        $filters = array('where' => array('p.id = ' . $pattern_id),
            'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        $t_user = array();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //prepare contact person
        if ($this->get('contact_person')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                       'c.subtype = \'contact\''),
                                      'sanitize'  => true,
                                      'model_lang' => $this->get('model_lang'));
            $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
            if ($contactperson) {
                $this->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
            }
        }

        //prepare customer's branch
        if ($this->get('branch')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                     'c.subtype = \'branch\''),
                                    'sanitize'  => true,
                                    'model_lang' => $this->get('model_lang'));
            $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
            if ($branch) {
                $this->set('branch_name', $branch->get('name'), true);
            }
        }

        //prepare cashbox/bank account
        if ($this->get('container_id')) {
            if ($this->get('payment_type') == 'bank') {
                require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                           'sanitize'  => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_bic', $container->get('bic'), true);
                    $this->set('container_iban', $container->get('iban'), true);
                    $this->set('container_bank', $container->get('bank'), true);
                }
            } else {
                require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                           'sanitize'  => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_location', $container->get('location'), true);
                }
            }
        }

        $translations = $this->getTranslations();
        if (empty($translations)) {
            $translations = array($this->get('model_lang'));
        }
        $t_reason = array();

        //save the previous registry lang
        $registry_lang_old = $this->registry['lang'];

        foreach ($translations as $t_lang) {
            $this->registry->set('lang', $t_lang, true);
            $this->registry['translater']->reloadFiles($t_lang);

            if ($this->get('id') > 0) {
                //when previewing the invoice templates the id of the invoice is -1
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('fir.id = ' . $this->get('id')));
                $t_reason[$t_lang] = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            } else {
                //when previewing the invoice templates the id of the invoice is -1
                //that's why the search could not find an invoice
                $t_reason[$t_lang] = clone $this;
            }
            if ($this->get('contact_person')) {
                $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                           'c.subtype = \'contact\''),
                                          'sanitize'  => true,
                                          'model_lang' => $t_lang);
                $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
                if ($contactperson) {
                    $t_reason[$t_lang]->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                }
            }
            if ($this->get('branch')) {
                $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                         'c.subtype = \'branch\''),
                                        'sanitize'  => true,
                                        'model_lang' => $t_lang);
                $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
                if ($branch) {
                    $t_reason[$t_lang]->set('branch_name', $branch->get('name'), true);
                }
            }
            if ($this->get('container_id')) {
                if ($this->get('payment_type') == 'bank') {
                    require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                    $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                               'sanitize'  => true,
                                               'model_lang' => $t_lang);
                    $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_bic', $container->get('bic'), true);
                        $t_reason[$t_lang]->set('container_iban', $container->get('iban'), true);
                        $t_reason[$t_lang]->set('container_bank', $container->get('bank'), true);
                    }
                } else {
                    require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                    $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                               'sanitize'  => true,
                                               'model_lang' => $t_lang);
                    $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_location', $container->get('location'), true);
                    }
                }
            }
            if ($this->get('issue_by') && !$t_reason[$t_lang]->isDefined('issue_by_name')) {
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';
                $filters_users = array('where'      => array('u.id=' . $this->get('issue_by')),
                                       'sanitize'   => true,
                                       'model_lang' => $t_lang);
                $issue_by_user = Users::searchOne($this->registry, $filters_users);
                $t_reason[$t_lang]->set('issue_by_name', ($issue_by_user ? trim($issue_by_user->get('firstname') . ' ' . $issue_by_user->get('lastname')) : ''), true);
            }
        }
        $this->registry->set('lang', $registry_lang_old, true);
        $this->registry['translater']->reloadFiles($registry_lang_old);

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            $pl_source = $placeholder->get('source');
            $pl_varname = $placeholder->get('varname');
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Finance_Incomes_Reason') {
                    //reason variables
                    if (!$placeholder->get('multilang')) {
                        if (preg_match('#^company_#', $pl_source)) {
                            $vars[$pl_varname] = $this->getCompanyData(str_replace('company_', '', $pl_source));
                        } else {
                            $vars[$pl_varname] = $this->get($pl_source);
                        }
                    } else {
                        // get customer`s name or mol depends on is_company
                        if ($pl_source == 'invoiced_to') {

                            foreach ($customer_translations as $t_lang) {
                                $filters = array(
                                    'model_lang' => $t_lang,
                                    'where' => array('c.id = ' . $customer->get('id'))
                                );
                                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
                                // check if customer is a company
                                if ($t_customer[$t_lang]->get('is_company')) {
                                    // get company`s mol
                                    $vars[$t_lang . '_' . $pl_varname] = $t_customer[$t_lang]->get('mol');
                                } else {
                                    // get person`s name and lastname
                                    $vars[$t_lang . '_' . $pl_varname] = $t_customer[$t_lang]->get('full_name');
                                    //$vars[$t_lang . '_' . $pl_varname] = trim($t_customer[$t_lang]->get('name') . ' ' . $t_customer[$t_lang]->get('lastname'));
                                }
                            }
                            continue;
                        }
                        foreach ($translations as $t_lang) {
                            if (preg_match('#^company_#', $pl_source)) {
                                $vars[ $t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_reason[$t_lang]->getCompanyData(str_replace('company_', '', $pl_source));
                            } else {
                                $vars[ $t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname] = $t_reason[$t_lang]->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer') {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $customer->get($pl_source);
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[ $t_customer[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_customer[$t_lang]->get($pl_source);
                            } else {
                                $vars[ $customer->get('model_lang') . '_' . $pl_varname] =
                                $customer->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                    //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $user->get($pl_source);
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_user[$t_lang]->get($pl_source);
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $pl_varname] =
                                $user->get($pl_source);
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
                //system variables
                if (strpos($pl_source, '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $pl_source);
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$pl_varname] = $res;
                } else {
                    $vars[$pl_varname] = $pl_source;
                }
            }
        }

        //prepare additional variables

        //get print settings for the 2nd type grouping table
        $print_properties = $this->getGT2PrintSettings($pattern_id);

        $lang = $this->get('model_lang');

        foreach ($translations as $t_lang) {
            $this->set('model_lang', $t_lang, true);
            // set no VAT reason text in current language
            $this->set('total_no_vat_reason_text', $t_reason[$t_lang]->get('total_no_vat_reason_text'), true);

            if (!$this->get('table_values_are_set')) {
                $this->getGT2Vars();
            }
            $table = $this->get('grouping_table_2');

            //prepare files in GT2
            if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                foreach ($table['values'] as $ridx => $row) {
                    foreach ($row as $rkey => $rval) {
                        if (!empty($rval) && is_object($rval)) {
                            $file = $rval;
                            if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                            } else {
                                $file = '';
                            }
                            $table['values'][$ridx][$rkey] = $file;
                        }
                    }
                }
            }

            $table_ordered = $table;
            $table_ordered['vars'] = array();
            $styles_for_template = array();

            foreach ($print_properties as $key => $property) {
                // style properties
                if (!empty($property['style'])) {
                    $styles_for_template[$key] = $property['style'];
                }
                // label for table caption
                if ($key == 'var_' . $table['id']) {
                    if (isset($property['labels'][$t_lang])) {
                        $table_ordered['label'] = $property['labels'][$t_lang];
                    }
                    continue;
                }
                foreach ($table['vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        $table_ordered['vars'][$idx] = $var;
                        // label for field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        // aggregates
                        if (isset($property['agregate'])) {
                            if ($property['agregate'] != 'none') {
                                $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                            } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                unset($table_ordered['vars'][$idx]['agregate']);
                            }
                        }
                        continue 2;
                    }
                }
                foreach ($table['plain_vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        // label for total field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        continue 2;
                    }
                }
            }

            unset($table);

            // calculate aggregates in GT2 table
            $table_ordered = $this->calculateGT2Agregates($table_ordered);

            $this->set('grouping_table_2', $table_ordered, true);

            $groupingViewer = new Viewer($this->registry);
            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');
            $groupingViewer->data['table'] = $table_ordered;
            $groupingViewer->data['styles'] = $styles_for_template;
            $groupingViewer->data['pattern_id'] = $pattern_id;
            $vars[$t_lang . '_grouping_table_2'] = $groupingViewer->fetch();

            //prepare placeholders for invoice number and invoice date if the plugin is used for debit/credit notes
            if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE || $this->get('type') == PH_FINANCE_TYPE_DEBIT_NOTICE) {
                $this->getRelatives(array('get_parent_reasons' => true));
                $relatives = $this->get('parent_reasons');

                foreach ($relatives as $relative) {
                    if ($relative->get('type') == PH_FINANCE_TYPE_INVOICE || $relative->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
                        $vars['finance_incomes_reason_invoice_num'] = $relative->get('num');
                        $vars['finance_incomes_reason_invoice_issue_date'] = $relative->get('issue_date');
                        break;
                    }
                }
                unset($relatives);
            }

            //set fake printform variables for invoices, proforma invoices and credit/debit notes
            if (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE))) {
                $gt2 = $this->getGT2Vars('print');

                if (count($gt2['values']) != 1 || !isset($gt2['values']['0']) || !empty($gt2['values']['0'])) {
                    $table_ordered_print = $gt2;
                    $table_ordered_print['vars'] = array();
                    $styles_for_template = array();

                    foreach ($print_properties as $key => $property) {
                        // style properties
                        if (!empty($property['style'])) {
                            $styles_for_template[$key] = $property['style'];
                        }
                        // label for table caption
                        if ($key == 'var_' . $gt2['id']) {
                            if (isset($property['labels'][$t_lang])) {
                                $table_ordered_print['label'] = $property['labels'][$t_lang];
                            }
                            continue;
                        }
                        foreach ($gt2['vars'] as $idx => $var) {
                            if ($key == 'var_' . $var['id']) {
                                $table_ordered_print['vars'][$idx] = $var;
                                // label for field
                                if (isset($property['labels'][$t_lang])) {
                                    $table_ordered_print['vars'][$idx]['label'] = $property['labels'][$t_lang];
                                }
                                // aggregates
                                if (isset($property['agregate'])) {
                                    if ($property['agregate'] != 'none') {
                                        $table_ordered_print['vars'][$idx]['agregate'] = $property['agregate'];
                                    } elseif (isset($table_ordered_print['vars'][$idx]['agregate'])) {
                                        unset($table_ordered_print['vars'][$idx]['agregate']);
                                    }
                                }
                                continue 2;
                            }
                        }
                        foreach ($gt2['plain_vars'] as $idx => $var) {
                            if ($key == 'var_' . $var['id']) {
                                // label for total field
                                if (isset($property['labels'][$t_lang])) {
                                    $table_ordered_print['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                                }
                                continue 2;
                            }
                        }
                    }

                    // calculate aggregates in GT2 table
                    $table_ordered_print = $this->calculateGT2Agregates($table_ordered_print);

                    $groupingViewer = new Viewer($this->registry);
                    $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');

                    $groupingViewer->data['styles'] = $styles_for_template;
                    $groupingViewer->data['table'] = $table_ordered_print;
                    $vars[$t_lang . '_grouping_table_2_printform'] = $groupingViewer->fetch();
                }

                //set back the original GT2 (not the printform fake one)
                $this->set('grouping_table_2', $table_ordered, true);
            }

            unset($table_ordered);
            unset($table_ordered_print);
        }

        $this->set('model_lang', $lang, true);
        // set no VAT reason text in current language
        $this->set('total_no_vat_reason_text', (isset($t_reason[$lang]) ? $t_reason[$lang]->get('total_no_vat_reason_text') : ''), true);

        return $vars;
    }

    /**
     * Get reasons attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'' . $this->modelName . '\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted =  0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get reasons files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Change status/substatus
     *
     * @return bool - result of the operation
     */
    public function setStatus() {
        $flag_error = false;
        $flag_error_substatus = false;

        $db = $this->registry['db'];
        //start transaction
        $db->StartTrans();

        $permission_unlock = $this->checkPermissions('setstatus_unlock');

        if (!$this->get('status')) {
            $flag_error = true;
        } else {
            if ($this->get('id')) {
                $id_reason = $this->get('id');
            }
            $current_status = Finance_Incomes_Reasons::getModelStatus($this->registry, $id_reason);
            $status_info = explode ('_', $this->get('status'));
            $status_name = $status_info[0];
            if ($current_status == 'locked' && $status_name == 'opened') {
                if (! $permission_unlock) {
                    $flag_error = true;
                }
            } else if ($current_status == 'finished' && ($status_name == 'opened' || $status_name == 'locked')) {
                $flag_error = true;
            }

            //takes the status properties based
            //if the status is defined in a dropdown it's a single
            // value containing the main status and the id of the substatus if such is defined
            @ $status_properties = explode('_', $this->get('status'));
            $new_status = $status_properties[0];

            if ($this->get('substatus')) {
                $substatus_properties = explode('_', $this->get('substatus'));
                if ($substatus_properties[0] != $new_status) {
                    $flag_error_substatus = true;
                } else {
                    $new_substatus = $substatus_properties[1];
                }
            } elseif (isset($status_properties[1])) {
                $new_substatus = $status_properties[1];
            }
        }

        if ($flag_error || $flag_error_substatus) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            if (isset($current_substatus)) {
                $this->set('substatus', $current_substatus, true);
            }
            if ($flag_error) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }
            if ($flag_error_substatus) {
                $this->raiseError('error_invalid_substatus_change', 'substatus', -3);
            }
            $db->CompleteTrans();
            return false;
        }

        $set = array();
        $set['status'] = sprintf("`status`='%s'", $new_status);
        if (isset($new_substatus)) {
            $set['substatus'] = sprintf("`substatus`=%d", $new_substatus);
        } else {
            $set['substatus'] = "`substatus`=0";
        }

        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

        if ($new_status == 'finished') {
            if ($current_status != 'finished') {
                if (!$this->get('issue_by')) {
                    $this->set('issue_by', $this->registry['currentUser']->get('id'), true);
                    $this->set('issue_by_name', $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname'), true);
                }
                $set['issue_by'] = sprintf("issue_by=%d", $this->get('issue_by'));
                // complete invoice_code field if the incomes reason is finished
                if (in_array(
                    $this->get('type'),
                    array(
                        PH_FINANCE_TYPE_INVOICE,
                        PH_FINANCE_TYPE_PRO_INVOICE,
                        PH_FINANCE_TYPE_CREDIT_NOTICE,
                        PH_FINANCE_TYPE_DEBIT_NOTICE
                    ))
                ) {
                    if (!$this->get('invoice_code')) {
                        $this->set('invoice_code', $this->registry['currentUser']->get('invoice_code'), true);
                    }
                    $set['invoice_code'] = sprintf("invoice_code='%s'", $this->get('invoice_code'));
                }
            }
            if (!$this->get('issue_date')) {
                $set['issue_date'] = "`issue_date` = NOW()";
                $this->set('issue_date', General::strftime('%Y-%m-%d'), true);
            }

            if ($this->get('type') != PH_FINANCE_TYPE_PRO_INVOICE) {
                if ($this->isDefined('fiscal_event_date_count')) {
                    if ($this->get('fiscal_event_date_count')) {
                        if ($this->get('periods_' . $this->get('fiscal_event_date_point'))) {
                            $date_start = $this->get('periods_' . $this->get('fiscal_event_date_point'));
                        } else {
                            $date_start = General::strftime('%Y-%m-%d');
                        }
                        if ($this->get('fiscal_event_date_period_type') == 'working') {
                            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                                $fiscal_event_date = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date_start,
                                    $this->get('fiscal_event_date_count'), $this->get('fiscal_event_date_direction'));
                        } elseif ($this->get('fiscal_event_date_direction') == 'before') {
                            $fiscal_event_date = General::strftime('%Y-%m-%d',
                                strtotime('-' . $this->get('fiscal_event_date_count') . ' day', strtotime($date_start)));
                        } else {
                            $fiscal_event_date = General::strftime('%Y-%m-%d',
                                strtotime('+' . $this->get('fiscal_event_date_count') . ' day', strtotime($date_start)));
                        }
                        $set['fiscal_event_date'] = sprintf('`fiscal_event_date`= "%s"', $fiscal_event_date);
                    } else {
                        $set['fiscal_event_date'] = sprintf('`fiscal_event_date`= NOW()');
                    }
                }
            }
            if (!isset($set['fiscal_event_date']) && !empty($set['issue_date'])) {
                $set['fiscal_event_date'] = preg_replace('#issue_date#', 'fiscal_event_date', $set['issue_date']);
            }
            if ($this->isDefined('date_of_payment_count')) {
                if ($this->get('date_of_payment_count') !== '') {
                    if ($this->get('date_of_payment_point') == 'issue') {
                        $date_start = date_create($this->get('issue_date'))->format('Y-m-d');
                        if ($this->get('date_of_payment_period_type') == 'working') {
                            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                            $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date_start,
                                $this->get('date_of_payment_count'), 'after');
                        } else {
                            $date_of_payment = General::strftime('%Y-%m-%d',
                                strtotime('+' . $this->get('date_of_payment_count') . ' day', strtotime($date_start)));
                        }
                    } else {
                        $date_of_payment = array('count := ' . $this->get('date_of_payment_count'),
                                                 'period_type := ' . $this->get('date_of_payment_period_type'),
                                                 'period := day',
                                                 'direction := after',
                                                 'point := receive');
                        $date_of_payment = implode('\n', $date_of_payment);
                    }
                    $this->set('date_of_payment', $date_of_payment, true);
                } else {
                    $this->set('date_of_payment', General::strftime('%Y-%m-%d'), true);
                }
                $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $this->get('date_of_payment'));
            }

            // set zero-amount finished documents as paid but only if they are not annulled, inactive or correction documents
            if ($this->get('total') == 0 && !$this->get('annulled_by') && $this->isActivated() &&
            $this->get('type') != PH_FINANCE_TYPE_CORRECT_REASON) {
                $set['payment_status'] = sprintf('payment_status="paid"');
                $set['payment_status_modified'] = sprintf("payment_status_modified=now()");
                $this->set('payment_status', 'paid', true);
            }
        }

        if (!$this->get('num') && $new_status == 'finished') {
            //set num
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
                $db->FailTrans();
                $db->CompleteTrans();
                return false;
            } else {
                if (is_object($this->counter)) {
                    $this->counter->increment();
                } else {
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                }
            }
        }

        if (!empty($this->counter) && is_object($this->counter)) {
            $set['counter'] = sprintf("counter='%s'", $this->counter->get('id'));
        }

        if ($current_status != 'finished' && $new_status == 'finished') {
            //set fiscal_total and fiscal_total_vat
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $currency = $this->registry['config']->getParam('finance', 'fiscal_currency');
            if (empty($currency)) {
                $currency = Finance_Currencies::getMain($this->registry);
            }
            $rate = Finance_Currencies::getRate($this->registry, $this->get('currency'), $currency);
            $fiscal_total = $rate * $this->get('total');
            $fiscal_total_vat = $rate * $this->get('total_vat');
            $set['fiscal_total'] = sprintf('`fiscal_total`= "%f"', $fiscal_total);
            $set['fiscal_total_vat'] = sprintf('`fiscal_total_vat`= "%f"', $fiscal_total_vat);
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($current_status != 'finished' && $new_status == 'finished' &&
        in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE))) {
            // update invoice/proforma payments if there are payments for parent reason
            // AFTER status of invoice/proforma has been changed to 'finished'!!!
            $this->updatePaymentsFromParent();
        }

        if ($new_status != 'opened' && $current_status == 'opened' /*&& ($this->get('type') > PH_FINANCE_TYPE_MAX || $this->get('type') == PH_FINANCE_TYPE_CORRECT_REASON)*/) {
            $this->setAvailableQuantities();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        return $result;
    }

    /**
     * Update payments for new invoice/proforma from parent reason OR
     * update payments for new invoice from parent proforma
     *
     * @return bool - result of the operation
     */
    public function updatePaymentsFromParent() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        $paid_amount = $this->getPaidAmount();

        $query = 'SELECT fir.total_with_vat' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 'WHERE fir.annulled_by=0 AND fir.id = ' . $this->get('id');
        $total_with_vat = $db->GetOne($query);

        if ($paid_amount < $total_with_vat) {
            // remaining amount to pay for current model (invoice/proforma)
            $amount_to_pay = $total_with_vat - $paid_amount;
            $amount_to_pay = round($amount_to_pay, 2);

            if ($this->get('type') == PH_FINANCE_TYPE_INVOICE && $this->get('proforma_id') > 0) {
                // when issuing invoice from proforma, relation in fin_reasons_relatives
                // does not exist yet, so proforma id is passed directly
                $parent_reason_id = $this->get('proforma_id');
            } else {
                // relative is an incomes reason (type > 100)
                $this->getRelatives(array('get_parent_reasons' => true));
                $reason = $this->get('parent_reasons');
                if (!empty($reason)) {
                    $reason = array_shift($reason);
                    $parent_reason_id = $reason->get('id');
                }
                $this->unsetProperty('parent_reasons', true);
            }

            if (!empty($parent_reason_id)) {
                // get payment rows for parent reason/proforma
                $query = 'SELECT fr.* ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                         'WHERE fr.paid_to=' . $parent_reason_id . "\n" .
                         '  AND fr.paid_to_model_name="Finance_Incomes_Reason"';
                $payment_rows = $db->GetAll($query);

                // if parent is an incomes reason (but not proforma), check if enough amount is
                // distributed to payments and not to expenses reasons
                if (!empty($reason)) {
                    $reason_total = $reason->get('total_with_vat');
                    // if this invoice adds VAT to reason
                    if ($this->checkUpdateReasonVAT(true, $reason)) {
                        // the invoice is going to add VAT to the reason
                        // so we calculate what its amount is going to be
                        $precision = $this->registry['config']->getParam('precision', 'gt2_total_with_vat');
                        $reason_total += round($reason_total*$this->get('total_vat_rate')/100, $precision);
                    }
                    $parent_total = $reason_total - $reason->getInvoicedAmount() + $total_with_vat;
                    unset($reason);
                    foreach ($payment_rows as $idx => $payment_row) {
                        if ($payment_row['parent_model_name'] == 'Finance_Expenses_Reason') {
                            $parent_total -= $payment_row['paid_amount'];
                            // distributed expenses should not go to invoices/proformas, even when validation is ok
                            unset($payment_rows[$idx]);
                        }
                    }
                    if ($parent_total < $amount_to_pay) {
                        //check for difference from precision
                        if (abs(bcsub($parent_total, $amount_to_pay, 2)) > 0.01) {
                            $link = sprintf('<a href="%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=payments&amp;payments=%s&amp;selected_tab=expenses_reason" target="_blank">',
                                            $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'], $parent_reason_id);
                            $this->raiseError('error_finance_incomes_reasons_expenses_reason_payment', '', '', array($link, '</a>'));
                            $this->raiseError('error_finance_incomes_reasons_expenses_reason_redistribute');
                            $db->FailTrans();
                            $db->CompleteTrans();
                            return false;
                        }
                    }
                }

                foreach ($payment_rows as $payment_row) {
                    // distributed amount in current fin_balance row
                    $paid_amount = $payment_row['paid_amount'];
                    if ($paid_amount) {
                        if ($paid_amount > $amount_to_pay) {
                            // paid amount to be redistributed to current model (invoice/proforma)
                            $invoice_amount = $amount_to_pay;
                            // the rest of the paid amount remains distributed to the parent reason
                            $reason_amount = $paid_amount - $amount_to_pay;
                            $reason_amount = round($reason_amount, 2);

                            // insert payment relation for invoice/proforma
                            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                                     'SET parent_id=' . $payment_row['parent_id'] . "\n" .
                                     ',parent_model_name="' . $payment_row['parent_model_name'] . '"' . "\n" .
                                     ',paid_to=' . $this->get('id') . "\n" .
                                     ',paid_to_model_name="Finance_Incomes_Reason"' . "\n" .
                                     ',paid_amount=' . $invoice_amount . "\n" .
                                     ',paid_currency="' . $payment_row['paid_currency'] . '"' . "\n" .
                                     ',added=now()' . "\n" .
                                     ',added_by=' . $payment_row['added_by'] . "\n" .
                                     ',modified=now()' . "\n" .
                                     ',modified_by=' . $payment_row['modified_by'];
                            $db->Execute($query);

                            // update payment relation for parent reason
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                                     'SET paid_amount=' . $reason_amount . "\n" .
                                     ',modified=now()' . "\n" .
                                     'WHERE id=' . $payment_row['id'];
                            $db->Execute($query);
                        } else {
                            // paid amount is redistributed to current model (invoice/proforma)
                            $invoice_amount = $paid_amount;
                            $reason_amount = 0;
                            // change payment from parent reason to invoice/proforma
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                                     'SET paid_to=' . $this->get('id') . "\n" .
                                     ',modified=now()' . "\n" .
                                     'WHERE id=' . $payment_row['id'];
                            $db->Execute($query);
                        }
                        // subtract distributed amount from remaining amount to pay for current model
                        $amount_to_pay -= $invoice_amount;
                        $amount_to_pay = round($amount_to_pay, 2);
                        if ($amount_to_pay <= 0) {
                            break;
                        }
                    }
                }
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Gets related records for revenue document
     *
     * @param array $params
     */
    public function getRelatives($params = array()) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        if (($this->get('type') > PH_FINANCE_TYPE_MAX || $this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) && (isset($params['get_invoices']) && $params['get_invoices'] || isset($params['get_all']) && $params['get_all'])) {
            //get invoices relative to this income reason
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND fir.type = ' . PH_FINANCE_TYPE_INVOICE . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"';
            if (!empty($params['get_invoices']) && is_array($params['get_invoices'])) {
                foreach ($params['get_invoices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($invoices = $db->GetCol($query)) {

                $filters = array('where' => array('fir.id IN (' . implode(', ', $invoices) . ')'),
                                 'sanitize' => true);
                $invoices = Finance_Incomes_Reasons::search($this->registry, $filters);

                $this->set('invoices', $invoices, true);
            }
        }

        if ($this->get('type') > PH_FINANCE_TYPE_MAX && (isset($params['get_pro_invoices']) && $params['get_pro_invoices'] || isset($params['get_all']) && $params['get_all'])) {
            //get proforma invoices relative to this model
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND fir.type = ' . PH_FINANCE_TYPE_PRO_INVOICE . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"';
            if (!empty($params['get_pro_invoices']) && is_array($params['get_pro_invoices'])) {
                foreach ($params['get_pro_invoices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($pro_invoices = $db->GetCol($query)) {

                $filters = array('where' => array('fir.id IN (' . implode(', ', $pro_invoices) . ')'),
                                 'sanitize' => true);
                $pro_invoices = Finance_Incomes_Reasons::search($this->registry, $filters);

                $this->set('pro_invoices', $pro_invoices, true);
            }
        }
        //To Do - save credit/debit notices in relatives table
        if ($this->get('type') == PH_FINANCE_TYPE_INVOICE && (isset($params['get_credit_notices']) && $params['get_credit_notices'] || isset($params['get_all']) && $params['get_all'])) {
            //get credit_notices relative to this model
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND fir.type = ' . PH_FINANCE_TYPE_CREDIT_NOTICE . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"';
            if (!empty($params['get_credit_notices']) && is_array($params['get_credit_notices'])) {
                foreach ($params['get_credit_notices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($credit_notices = $db->GetCol($query)) {

                $filters = array('where' => array('fir.id IN (' . implode(', ', $credit_notices) . ')'),
                                 'sanitize' => true);
                $credit_notices = Finance_Incomes_Reasons::search($this->registry, $filters);

                $this->set('credit_notices', $credit_notices, true);
            }
        }

        if ($this->get('type') == PH_FINANCE_TYPE_INVOICE && (isset($params['get_debit_notices']) && $params['get_debit_notices'] || isset($params['get_all']) && $params['get_all'])) {
            //get debit_notices relative to this model
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND fir.type = ' . PH_FINANCE_TYPE_DEBIT_NOTICE . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"';
            if (!empty($params['get_debit_notices']) && is_array($params['get_debit_notices'])) {
                foreach ($params['get_debit_notices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($debit_notices = $db->GetCol($query)) {

                $filters = array('where' => array('fir.id IN (' . implode(', ', $debit_notices) . ')'),
                                 'sanitize' => true);
                $debit_notices = Finance_Incomes_Reasons::search($this->registry, $filters);

                $this->set('debit_notices', $debit_notices, true);
            }
        }

        if ($this->get('type') > PH_FINANCE_TYPE_MAX && (isset($params['get_correct_reasons']) && $params['get_correct_reasons'] || isset($params['get_all']) && $params['get_all'])) {
            //get rectifying documents relative to this model
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND fir.type = ' . PH_FINANCE_TYPE_CORRECT_REASON . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"';
            if (!empty($params['get_correct_reasons']) && is_array($params['get_correct_reasons'])) {
                foreach ($params['get_correct_reasons'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($correct_reasons = $db->GetCol($query)) {

                $filters = array('where' => array('fir.id IN (' . implode(', ', $correct_reasons) . ')'),
                                 'sanitize' => true);
                $correct_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);

                $this->set('correct_reasons', $correct_reasons, true);
            }
        }

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX && (isset($params['get_parent_reasons']) && $params['get_parent_reasons'] || isset($params['get_all']) && $params['get_all'])) {
            //get parent revenue documents relative to this model
            $query = 'SELECT frr.link_to' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.link_to = fir.id' . "\n" .
                     'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' ."\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"';
            if (!empty($params['get_parent_reasons']) && is_array($params['get_parent_reasons'])) {
                foreach ($params['get_parent_reasons'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($parent_reasons = $db->GetCol($query)) {

                $filters = array('where' => array('fir.id IN (' . implode(', ', $parent_reasons) . ')'),
                                 'sanitize' => true);
                $parent_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);

                $this->set('parent_reasons', $parent_reasons, true);
            } else {
                //set to empty array for advance invoice
                $this->set('parent_reasons', array(), true);
            }
        }

        if ($this->get('type') > PH_FINANCE_TYPE_MAX && (isset($params['get_handovers']) && $params['get_handovers'] || isset($params['get_all']) && $params['get_all'])) {
            //get handover relatives to this incomes reason
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                    '    ON frr.parent_id = fwd.id AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER .
                    (!empty($params['handovers_direction']) ? sprintf(' AND direction = "%s"', $params['handovers_direction']) : '') ."\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name ="Finance_Incomes_Reason"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Warehouses_Document"';
            if (!empty($params['get_handovers']) && is_array($params['get_handovers'])) {
                foreach ($params['get_handovers'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }
            if ($handovers = $db->GetCol($query)) {
                $filters = array('where' => array('fwd.id IN (' . implode(', ', $handovers) . ')'),
                                 'sort' => array('fwd.id ASC'),
                                 'sanitize' => true
                );
                require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
                $handovers = Finance_Warehouses_Documents::search($this->registry, $filters);
            }
            $this->set('handovers', $handovers, true);
        }

        if (!empty($params['get_commodity_reservations']) || !empty($params['get_all'])) {
            // get commodity reservations relative to this incomes reason
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                     '  ON frr.parent_id = fwd.id AND fwd.type = ' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_model_name = "Finance_Warehouses_Document"';
            if (!empty($params['get_commodity_reservations']) && is_array($params['get_commodity_reservations'])) {
                foreach ($params['get_commodity_reservations'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($commodity_reservations = $db->GetCol($query)) {

                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php');
                $filters = array('where' => array('fwd.id IN (' . implode(', ', $commodity_reservations) . ')'),
                                 'sanitize' => true);
                $commodity_reservations = Finance_Warehouses_Documents::search($this->registry, $filters);

                $this->set('commodity_reservations', $commodity_reservations, true);
            }
        }

        if (!empty($params['get_reason'])) {
            //get incomes reason relative to this document(invoice, correct reason, proforma invoice)
            $query = 'SELECT frr.link_to, frr.link_to_model_name' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     '  AND fir.annulled_by=0 AND fir.type > ' . PH_FINANCE_TYPE_MAX . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                     '  ON frr.link_to = co.id AND frr.link_to_model_name = "Contract" ' . "\n" .
                     'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND (frr.link_to_model_name = "Finance_Incomes_Reason" AND fir.id IS NOT NULL OR' . "\n" .
                     '       frr.link_to_model_name = "Contract" AND co.id IS NOT NULL)' . "\n" .
                     'LIMIT 1';
            $reason = $db->GetRow($query);

            if (!empty($reason)) {
                if ($reason['link_to_model_name'] == 'Finance_Incomes_Reason') {
                    $filters = array('where' => array('fir.id = ' . $reason['link_to']),
                                     'sanitize' => true);
                    $reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                } elseif ($reason['link_to_model_name'] == 'Contract') {
                    $filters = array('where' => array('co.id = ' . $reason['link_to']),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                    $reason = Contracts::searchOne($this->registry, $filters);
                }

                /*$filters = array('where' => array('fir.id = ' . $reason, 'fir.annulled_by = 0'),
                                 'sanitize' => true);
                $reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);*/

                $this->set('reason', $reason, true);
            }
        }

        if (isset($params['get_parent_doc']) && $params['get_parent_doc']  || isset($params['get_all']) && $params['get_all']) {
            //get incomes reason relative to this document(invoice, correct reason, proforma invoice)
            $query = 'SELECT frr.link_to' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.link_to_model_name ="Document"';
            if ($document = $db->GetOne($query)) {

                $filters = array('where' => array('d.id = ' . $document),
                                 'sanitize' => true);
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                $document = Documents::searchOne($this->registry, $filters);

                $this->set('document', $document, true);
            }
        }

        if (isset($params['get_interest_accounts']) && $params['get_interest_accounts'] || isset($params['get_all']) && $params['get_all']) {
            //get interest accounts relative to the invoice
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND fir.type = ' . PH_FINANCE_TYPE_INTEREST_ACCOUNT . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_model_name ="Finance_Incomes_Reason"';
            if ($interest_account = $db->GetOne($query)) {

                $filters = array('where' => array('fir.id = ' . $interest_account),
                                 'sanitize' => true);
                require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
                $interest_account = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                $this->set('interest_account', $interest_account, true);
            }
        }

        if (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE)) &&
        (isset($params['get_contract']) && $params['get_contract'] || isset($params['get_all']) && $params['get_all'])) {
            //get contract that invoice/proforma invoice was issued for
            $query = 'SELECT frr.link_to' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.link_to_model_name ="Contract"';
            if ($contract = $db->GetOne($query)) {
                $filters = array('where' => array('co.id = ' . $contract),
                                 'sanitize' => true);
                require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                $contract = Contracts::searchOne($this->registry, $filters);

                $this->set('contract', $contract, true);
            }
        }

        if ($this->get('annulled_by') && (isset($params['get_annulled']) || isset($params['get_all']))) {
            //get annulment
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Annulment"' . "\n" .
                     '  AND frr.link_to_model_name ="Finance_Incomes_Reason"';
            if ($annulment_id = $db->GetOne($query)) {
                $filters = array('where' => array('fa.id = ' . $annulment_id),
                                 'sanitize' => true);
                require_once PH_MODULES_DIR . 'finance/models/finance.annulments.factory.php';
                $annulment = Finance_Annulments::searchOne($this->registry, $filters);

                $this->set('annulment', $annulment, true);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }
    }

    /**
     * Some of the invoices are issued from a contract. Sometimes the contract data is needed.
     * The credit/debit notes issued for an invoice could also be related to a contract (via invoice issued from contract)
     * ATTENTION: this function works only for invoices, debit notice and credit notice!!!
     *
     * @param array $data - contract data to be fetched (e.g. id, trademark, etc.)
     * @return array $result - associative array with fetched data
     */
    public function getContractData($data) {
        if (empty($data)) {
            //nothing to fetch
            return array();
        }

        if (!in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                                PH_FINANCE_TYPE_PRO_INVOICE,
                                                PH_FINANCE_TYPE_CREDIT_NOTICE,
                                                PH_FINANCE_TYPE_DEBIT_NOTICE))) {
            //this function works only for invoices, debit notice and credit notice!!!
            return array();
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        $result = array();

        //get incomes reason relative to this document(invoice, correct reason, proforma invoice)
        $query = 'SELECT frr.link_to, frr.link_to_model_name' . "\n" .
             'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
             'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
             '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason" ' . "\n" .
             '  AND fir.annulled_by=0 AND fir.type > ' . PH_FINANCE_TYPE_MAX . "\n" .
             'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
             '  ON frr.link_to = co.id AND frr.link_to_model_name = "Contract" ' . "\n" .
             'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
             '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
             'LIMIT 1';
        $reason = $db->GetRow($query);

        //initialize contract id
        $contract_id = 0;

        if (!empty($reason)) {
            if ($reason['link_to_model_name'] == 'Finance_Incomes_Reason') {
                //check for debit/credit notices
                $query = 'SELECT frr.link_to, frr.link_to_model_name' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     '  AND fir.annulled_by=0 AND fir.type > ' . PH_FINANCE_TYPE_MAX . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                     '  ON frr.link_to = co.id AND frr.link_to_model_name = "Contract" ' . "\n" .
                     'WHERE frr.parent_id = ' . $reason['link_to'] . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     'LIMIT 1';
                $reason = $db->GetRow($query);

                if (!empty($reason) && $reason['link_to_model_name'] == 'Contract') {
                    $contract_id = $reason['link_to'];
                }
            } elseif ($reason['link_to_model_name'] == 'Contract') {
                $contract_id = $reason['link_to'];
            }

            if (!empty($contract_id)) {
                $query = 'SHOW COLUMNS FROM ' . DB_TABLE_CONTRACTS;
                $main_table_columns = $db->GetCol($query);

                $query = 'SHOW COLUMNS FROM ' . DB_TABLE_CONTRACTS_I18N;
                $i18n_main_table_columns = $db->GetCol($query);

                //finally we got a contract id, now process the requested data
                $fields = array();
                foreach ($data as $param) {
                    if (in_array($param, $main_table_columns)) {
                        $fields[] = 'co.' . $param;
                    } elseif (in_array($param, $i18n_main_table_columns)) {
                        $fields[] = 'coi18n.' . $param;
                    } elseif ($param == 'trademark_name') {
                        $fields[] = 'ni18n.name AS ' . $param;
                    }
                }
                $query = 'SELECT ' . implode(', ', $fields) . "\n" .
                         'FROM ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n' . "\n" .
                         '  ON (co.id=coi18n.parent_id AND coi18n.lang="'.$this->get('model_lang').'")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                         ' ON (co.trademark=ni18n.parent_id AND ni18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                         'WHERE co.id=' . $contract_id . "\n";
                $result = $db->GetRow($query);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Checks whether an invoice or proforma invoice could be added
     *
     * @param bool $set_table_back - sets the calculated quantities back in the model
     * @return bool - true if invoice can be added, false otherwise
     */
    public function checkAddingInvoice($set_table_back = false) {
        if (!$this->isActivated() || $this->get('annulled_by')) {
            return false;
        }
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        if (!$set_table_back && $this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            //if there is an invoice for proforma return false
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND frr.parent_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     '  AND fir.type = ' . PH_FINANCE_TYPE_INVOICE . ' AND fir.annulled_by=0 AND fir.active=1' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . ' AND frr.link_to_model_name = "Finance_Incomes_Reason"';
            $invoice_id = $db->GetOne($query);
            if ($invoice_id) {
                if ($sanitize_after) {
                    $this->sanitize();
                }

                return false;
            }
        }

        // check if we have not finished invoice for this reason
        if ($set_table_back) {
            $query = 'SELECT f.id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' fr' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f' . "\n" .
                     '  ON f.id = fr.parent_id AND f.type = ' . PH_FINANCE_TYPE_INVOICE . ' AND f.status != "finished" AND f.annulled_by = 0 AND f.active = 1' . "\n" .
                     'WHERE fr.parent_model_name = "Finance_Incomes_Reason" AND fr.link_to_model_name = "Finance_Incomes_Reason" AND fr.link_to = ' . $this->get('id');
            $has_unfinished = $db->GetOne($query);
            if ($has_unfinished && $has_unfinished != $this->get('invoice_id')) {
                $link = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=view&amp;view=%d',
                    $_SERVER['PHP_SELF'], $this->registry['module_param'],
                    $this->registry['controller_param'],
                    $has_unfinished);
                $this->raiseError('error_finance_incomes_reasons_has_unfinished_invoice', '', 0, array($link));
                $this->registry['messages']->insertInSession($this->registry);
                return false;
            }
        }

        //get invoiced quantities
        $query = 'SELECT fdd.id AS row_id, fdd.quantity, fdd.price, fdd.last_delivery_price,' . "\n" .
                 '  fdd.discount_value, fdd.surplus_value, fdd.discount_percentage, fdd.surplus_percentage' . "\n" .
                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 '  ON fdd.model_id = frr.parent_id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON frr.parent_id = fir.id' . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fdd.model = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fir.advance=0 AND fir.annulled_by=0 AND fir.type = ' . PH_FINANCE_TYPE_INVOICE;
        // in edit mode - do not include currently edited invoice
        if ($this->get('invoice_id')) {
            $query .= ' AND frr.parent_id != ' . $this->get('invoice_id');
        }
        $invoiced = $db->GetAssoc($query);

        //get links between invoices and incomes reason
        $query = 'SELECT frr.parent_id as idx, frr.rows_links, frr.changes' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON frr.parent_id = fir.id AND frr.parent_model_name = "Finance_Incomes_Reason" AND fir.type = ' . PH_FINANCE_TYPE_INVOICE . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fir.advance=0 AND fir.annulled_by=0' . "\n";
        // in edit mode - do not include currently edited invoice
        if ($this->get('invoice_id')) {
            $query .= ' AND frr.parent_id != ' . $this->get('invoice_id') . "\n";
        }
        $query .= 'ORDER BY frr.parent_id ASC';
        $invoice_links = $db->GetAssoc($query);

        if (empty($invoice_links) && !$set_table_back) {
            if ($sanitize_after) {
                $this->sanitize();
            }
            //there are no invoices from this income reason
            return true;
        }

        $map_keys = array();

        // in edit mode, we will have to map the keys
        // (gt2 details row ids of the primary financial document)
        // to the row ids of the currently edited secondary financial document
        // as we need them for validation of invoiced quantities
        if ($this->get('invoice_id')) {
            //get links for currently edited invoice and incomes reason
            $query = 'SELECT frr.rows_links' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND frr.parent_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                     '  ON fir.type = fdt.id ' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.parent_id = ' . $this->get('invoice_id') . "\n" .
                     '  AND fir.advance=0 AND fir.annulled_by=0 AND fdt.id IN (' . PH_FINANCE_TYPE_INVOICE . ', ' . PH_FINANCE_TYPE_PRO_INVOICE . ')' . "\n" .
                     'LIMIT 1';
            $current_invoice_rows_links = $db->GetOne($query);

            //parse rows links
            $current_invoice_rows = preg_split('#\n|\r|\r\n#', $current_invoice_rows_links);
            foreach ($current_invoice_rows as $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                if (isset($row[1])) {
                    // $row[1] - reason row, $row[0] - invoice row
                    $map_keys[$row[1]] = $row[0];
                }
            }
        }

        $gt2_just_fetched = false;
        if (!$this->isDefined('grouping_table_2')) {
            $gt2_just_fetched = true;
            $this->getGT2Vars();
        }

        $gt2 = $this->get('grouping_table_2');

        foreach ($invoice_links as $invoice => $invoice_rows) {

            //get debit/credit notices' quantities
            $query = 'SELECT fdd.id AS row_id, fdd.quantity, fdd.price, fdd.last_delivery_price,' . "\n" .
                     '  fdd.discount_value, fdd.surplus_value, fdd.discount_percentage, fdd.surplus_percentage' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                     '  ON fdd.model_id = frr.parent_id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id' . "\n" .
                     'WHERE frr.link_to = ' . $invoice . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND fir.annulled_by=0' . "\n" .
                     '    AND (fir.type = ' . PH_FINANCE_TYPE_DEBIT_NOTICE .' OR fir.type = ' . PH_FINANCE_TYPE_CREDIT_NOTICE . ')';
            $dc = $db->GetAssoc($query);

            //get links between invoice and debit/credit notices
            $query = 'SELECT frr.parent_id as idx, frr.rows_links, frr.changes' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND frr.parent_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     'WHERE frr.link_to = ' . $invoice . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND fir.annulled_by=0' . "\n" .
                     '    AND (fir.type = ' . PH_FINANCE_TYPE_DEBIT_NOTICE .' OR fir.type = ' . PH_FINANCE_TYPE_CREDIT_NOTICE . ')' . "\n" .
                     'ORDER BY idx';

            $links = $db->GetAssoc($query);

            //recalculate quantities from the debit/credit notices to the invoiced quantities
            foreach ($links as $vals) {
                $vals['changes'] = preg_split('#\n|\r|\r\n#', $vals['changes']);
                $changes = array();
                foreach ($vals['changes'] as $row) {
                    if (preg_match('#\s*=>\s*#', $row)) {
                        $row = preg_split('#\s*=>\s*#', $row);
                        $changes[$row[0]] = $row[1];
                    }
                }
                $vals['rows_links'] = preg_split('#\n|\r|\r\n#', $vals['rows_links']);
                foreach ($vals['rows_links'] as $row) {
                    $row = preg_split('#\s*=>\s*#', $row);
                    if (isset($row[1]) && isset($invoiced[$row[1]])) {
                        if ($changes[$row[1]] == 'deleted') {
                            //row is deleted
                            unset($invoiced[$row[1]]);
                        } else {
                            //row is changed - recalc the field required ($changes[$row[1]])
                            $invoiced[$row[1]][$changes[$row[1]]] += $dc[$row[0]][$changes[$row[1]]];
                        }
                        unset($changes[$row[1]]);
                    } else {
                        //row is added
                        $row[1] = array_keys($changes);
                        $row[1] = array_shift($row[1]);
                        $invoiced[$row[1]] = $dc[$row[0]];
                        $invoice_rows['rows_links'] .= "\n" . $row[1] . ' => ' . $row[1];
                        unset($changes[$row[1]]);
                    }
                    unset($dc[$row[0]]);
                }
            }

            //calculate invoiced quantities
            $invoice_rows['rows_links'] = preg_split('#\n|\r|\r\n#', $invoice_rows['rows_links']);
            $changes = array();
            $rows = array();
            foreach ($invoice_rows['rows_links'] as $k => $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                if (isset($row[0]) && isset($row[1])) {
                    $rows[$row[0]] = $row[1];
                }
            }
            $invoice_rows['changes'] = preg_split('#\n|\r|\r\n#', $invoice_rows['changes']);
            foreach ($invoice_rows['changes'] as $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                if (isset($row[0]) && isset($row[1])) {
                    $changes[$row[0]] = $row[1];
                }
            }

            foreach ($rows as $to => $from) {
                if (!isset($changes[$from])) {
                    // nothing is changed - e.g. the row is fully invoiced
                    $changes[$from] = 'quantity';
                }
                if (isset($invoiced[$to])) {
                    if (!empty($gt2['values'][$from])) {
                        $gt2['values'][$from][$changes[$from]] -= $invoiced[$to][$changes[$from]];
                        if ($gt2['values'][$from][$changes[$from]] < 0 && $this->registry['request']->isPost()
                          || $gt2['values'][$from][$changes[$from]] <= 0 && (!$this->registry['request']->isPost() || $this->registry['action'] == 'addpayment')) {
                            unset($gt2['values'][$from]);
                        }
                    }
                }
            }
        }

        // in edit mode, gt2 values must be mapped to the rows ids of
        // the currently edited secondary financial document
        if ($map_keys) {
            $gt2_values_mapped = array();
            foreach ($gt2['values'] as $row => $row_values) {
                if (array_key_exists($row, $map_keys)) {
                    $gt2_values_mapped[$map_keys[$row]] = $row_values;
                    $gt2_values_mapped[$map_keys[$row]]['reason_row'] = $row;
                }
            }
            $gt2['values'] = $gt2_values_mapped;
        }

        if ($set_table_back) {
            if ($gt2['values']) {
                $this->set('grouping_table_2', $gt2, true);
            } else {
                $this->set('grouping_table_2', array(), true);
            }
        } elseif ($gt2_just_fetched) {
            $this->unsetProperty('grouping_table_2');
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        // make sure GT2 is not empty
        if ($gt2['values'] && reset($gt2['values'])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Checks whether a handover could be added
     *
     * @param string $direction - 'incoming' or 'outgoing'
     * @param bool $set_table_back - sets the calculated quantities back in the model
     * @return bool - true if handover can be added, false otherwise
     */
    public function checkAddingHandover($direction, $set_table_back = false) {
        if (!$this->isActivated()) {
            return false;
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];
        $precQuantity = $this->registry['config']->getParam('precision', 'gt2_quantity');

        //get quantities in the previous handovers
        $query = 'SELECT fdd.id AS row_id, fwd.direction, fdd.quantity' . "\n" .
                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 '  ON fdd.model_id = frr.parent_id' . "\n" .
                 '  AND frr.parent_model_name = fdd.model' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                 '  ON frr.parent_id = fwd.id' . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND fdd.model = "Finance_Warehouses_Document"' . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fwd.annulled_by = 0' . "\n" .
                 '  AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER;
        $handovered = $db->GetAssoc($query);

        //get links between handovers and incomes reason
        $query = 'SELECT frr.parent_id as idx, frr.rows_links' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                 '  ON frr.parent_id = fwd.id AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fwd.annulled_by = 0' . "\n" .
                 '  AND frr.parent_model_name = "Finance_Warehouses_Document"';
        $handover_links = $db->GetAssoc($query);

        if (empty($handover_links) && !$set_table_back) {
            if ($sanitize_after) {
                $this->sanitize();
            }
            if ($direction == 'outgoing') {
                //there are no handovers for this income reason
                return true;
            } else {
                return false;
            }
        }

        $gt2_just_fetched = false;
        if (!$this->isDefined('grouping_table_2')) {
            $gt2_just_fetched = true;
            $this->getGT2Vars();
        }

        foreach ($handover_links as $handover => $handover_rows) {
            //calculate handovered quantities
            $handover_rows = preg_split('#\n|\r|\r\n#', $handover_rows);
            foreach ($handover_rows as $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                $sign = 1;
                if ($handovered[$row[0]]['direction'] == 'incoming') {
                    $sign = -1;
                }
                if (isset($row[1]) && isset($handovered[$row[1]])) {
                    $handovered[$row[1]]['quantity'] += $sign * $handovered[$row[0]]['quantity'];
                    unset($handovered[$row[0]]);
                } else {
                    $handovered[$row[1]]['quantity'] = $sign * $handovered[$row[0]]['quantity'];
                    unset($handovered[$row[0]]);
                }
            }
        }

        $gt2 = $this->get('grouping_table_2');
        $nom_ids = array();
        foreach ($gt2['values'] as $values) {
            if (!empty($values['article_id'])) {
                $nom_ids[] = $values['article_id'];
            }
        }
        // clear blank values from array
        $nom_ids = array_diff($nom_ids, array(''));

        //get only commodities (the nomenclatures that are saved in warehouses)
        $noms_in_warehouse = array();
        if ($nom_ids) {
            $query = 'SELECT id FROM ' . DB_TABLE_NOMENCLATURES . "\n" .
                     'WHERE `subtype`="commodity" AND id IN (' . implode(', ', $nom_ids) . ') AND deleted_by = 0';
            $noms_in_warehouse = $this->registry['db']->GetCol($query);
        }

        foreach ($gt2['values'] as $row_id => $row) {
            if (empty($row) || !in_array($row['article_id'], $noms_in_warehouse)) {
                //remove non-commodity noms
                unset($gt2['values'][$row_id]);
                continue;
            } else {
                $gt2['values'][$row_id]['quantity'] = abs($row['quantity']);
            }
        }
        if ($direction == 'outgoing') {
            //calculate the difference between the reason and handovers
            foreach ($handovered as $row => $value) {
                if (!empty($gt2['values'][$row])) {
                    $gt2['values'][$row]['quantity'] = bcsub(
                        $gt2['values'][$row]['quantity'],
                        $value['quantity'],
                        $precQuantity
                    );
                    if ($gt2['values'][$row]['quantity'] <= 0) {
                        unset($gt2['values'][$row]);
                    }
                }
            }
        } elseif ($direction == 'incoming') {
            //set the handovered quantity to the reason
            //as we can return in the warehouse only handovered quantity
            foreach ($gt2['values'] as $row => $values) {
                if (isset($handovered[$row]) && $handovered[$row]['quantity'] > 0) {
                    $gt2['values'][$row]['quantity'] = $handovered[$row]['quantity'];
                } else {
                    unset($gt2['values'][$row]);
                }
            }
        } else {
            if ($sanitize_after) {
                $this->sanitize();
            }
            return false;
        }

        if ($set_table_back) {
            if ($gt2['values']) {
                $this->set('grouping_table_2', $gt2, true);
            } else {
                $this->set('grouping_table_2', array(), true);
            }
        } elseif ($gt2_just_fetched) {
            $this->unsetProperty('grouping_table_2');
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        // make sure GT2 is not empty
        if ($gt2['values'] && reset($gt2['values'])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Checks whether a commodity reservation could be made
     *
     * @param bool $set_table_back - sets the calculated quantities back in the model
     * @return bool - true if commoditiy reservation can be added, otherwise false
     */
    public function checkCommoditiesReservation($set_table_back = false) {
        if (!$this->isActivated()) {
            return false;
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];
        $precQuantity = $this->registry['config']->getParam('precision', 'gt2_quantity');

        //get quantities in the previous handovers and commodity reservations
        $query = 'SELECT fdd.id AS row_id, fwd.direction, fwd.type, fdd.quantity' . "\n" .
                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 '  ON fdd.model_id = frr.parent_id' . "\n" .
                 '  AND frr.parent_model_name = fdd.model' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                 '  ON frr.parent_id = fwd.id' . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND fdd.model = "Finance_Warehouses_Document"' . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fwd.annulled_by = 0' . "\n" .
                 '  AND fwd.type IN (' . PH_FINANCE_TYPE_HANDOVER . ', ' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION . ')';
        $reserved = $db->GetAssoc($query);

        //get links between handovers/commodity reservations and incomes reason
        $query = 'SELECT frr.parent_id as idx, frr.rows_links' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                 '  ON frr.parent_id = fwd.id AND frr.parent_model_name = "Finance_Warehouses_Document" ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                 '  ON fwd.type = fdt.id ' . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fwd.annulled_by = 0' . "\n" .
                 '  AND fdt.id IN (' . PH_FINANCE_TYPE_HANDOVER . ', ' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION . ')';
        $reserved_links = $db->GetAssoc($query);

        $ids = array_keys($reserved_links);
        $released = array();
        $released_links = array();
        if (!empty($ids)) {
            //get quantities in commodity releases for commodity reservations
            $query = 'SELECT fdd.id AS row_id, fwd.direction, fwd.type, fdd.quantity' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                     '  ON fdd.model_id = frr.parent_id' . "\n" .
                     '  AND frr.parent_model_name = fdd.model' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                     '  ON frr.parent_id = fwd.id' . "\n" .
                     'WHERE frr.link_to IN (' . implode(', ', $ids) . ')' . "\n" .
                     '  AND fdd.model = "Finance_Warehouses_Document"' . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Warehouses_Document"' . "\n" .
                     '  AND fwd.type IN (' . PH_FINANCE_TYPE_COMMODITIES_RELEASE . ')';
            $released = $db->GetAssoc($query);

            //get links between commodity releases and commodity reservations
            $query = 'SELECT frr.parent_id as idx, frr.rows_links' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                     '  ON frr.parent_id = fwd.id AND frr.parent_model_name = "Finance_Warehouses_Document" ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                     '  ON fwd.type = fdt.id ' . "\n" .
                     'WHERE frr.link_to IN (' . implode(', ', $ids) . ')' . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Warehouses_Document"' . "\n" .
                     '  AND fdt.id IN (' . PH_FINANCE_TYPE_COMMODITIES_RELEASE . ')';
            $released_links = $db->GetAssoc($query);

            //$reserved += $released;
            //$reserved_links += $released_links;
        }
        if (empty($reserved_links) && !$set_table_back) {
            if ($sanitize_after) {
                $this->sanitize();
            }
            return true;
        }

        $gt2_just_fetched = false;
        if (!$this->isDefined('grouping_table_2')) {
            $gt2_just_fetched = true;
            $this->getGT2Vars();
        }

        foreach ($released_links as $release => $released_rows) {
            //calculate released quantities
            $released_rows = preg_split('#\n|\r|\r\n#', $released_rows);
            foreach ($released_rows as $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                $sign= -1;
                if (isset($row[1]) && isset($reserved[$row[1]])) {
                    $reserved[$row[1]]['quantity'] += $sign * $released[$row[0]]['quantity'];
                    unset($released[$row[0]]);
                } else {
                    $reserved[$row[1]]['quantity'] = $sign * $released[$row[0]]['quantity'];
                    unset($released[$row[0]]);
                }
            }
        }

        foreach ($reserved_links as $reservation => $reserved_rows) {
            //calculate reserved quantities
            $reserved_rows = preg_split('#\n|\r|\r\n#', $reserved_rows);
            foreach ($reserved_rows as $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                $sign = 1;
                if ($reserved[$row[0]]['direction'] == 'incoming' || $reserved[$row[0]]['type'] == PH_FINANCE_TYPE_COMMODITIES_RELEASE) {
                    $sign= -1;
                }
                if (isset($row[1]) && isset($reserved[$row[1]])) {
                    $reserved[$row[1]]['quantity'] += $sign * $reserved[$row[0]]['quantity'];
                    unset($reserved[$row[0]]);
                } else {
                    $reserved[$row[1]]['quantity'] = $sign * $reserved[$row[0]]['quantity'];
                    unset($reserved[$row[0]]);
                }
            }
        }

        //calculate the difference between the reason and commodity reservations
        $gt2 = $this->get('grouping_table_2');
        foreach ($reserved as $row => $value) {
            if (!empty($gt2['values'][$row])) {
                $gt2['values'][$row]['quantity'] = bcsub(
                    $gt2['values'][$row]['quantity'],
                    $value['quantity'],
                    $precQuantity
                );
                if ($gt2['values'][$row]['quantity'] <= 0) {
                    unset($gt2['values'][$row]);
                }
            }
        }
        if ($set_table_back) {
            if ($gt2['values']) {
                $this->set('grouping_table_2', $gt2, true);
            } else {
                $this->set('grouping_table_2', array(), true);
            }
        } elseif ($gt2_just_fetched) {
            $this->unsetProperty('grouping_table_2');
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        // make sure GT2 is not empty
        if ($gt2['values'] && reset($gt2['values'])) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Checks whether a credit/debit document could be added to invoice
     *
     * @param bool $set_table_back - sets the calculated quantities back in the model
     * @param string $parent_model - model of parent of invoice - 'Finance_Incomes_Reason' or 'Contract'
     * @return bool - true if note can be added, otherwise false
     */
    public function checkAddingCredit($set_table_back = false, $parent_model = 'Finance_Incomes_Reason') {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        //get credit/debit quantities and prices
        $query = 'SELECT fdd.id AS row_id, fdd.quantity, fdd.price, fdd.last_delivery_price,' . "\n" .
                 '    fdd.discount_value, fdd.surplus_value, fdd.discount_percentage, fdd.surplus_percentage,' . "\n" .
                 '    fdd.discount_surplus_field, fdd.date_from, fdd.date_to, fdd.model_id' . "\n" .
                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 '  ON fdd.model_id = frr.parent_id AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '    AND frr.link_to_model_name = "Finance_Incomes_Reason" AND frr.link_to = ' . $this->get('id') . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON frr.parent_id = fir.id AND fir.annulled_by=0' . "\n" .
                 '    AND (fir.type = ' . PH_FINANCE_TYPE_CREDIT_NOTICE . ' OR  fir.type = ' . PH_FINANCE_TYPE_DEBIT_NOTICE . ')' . "\n" .
                 'WHERE fdd.model = "Finance_Incomes_Reason"';
        $credited = $db->GetAssoc($query);

        //get links between invoice and credit/debit notices
        $query = 'SELECT frr.parent_id as idx, frr.rows_links, frr.changes' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON frr.parent_id = fir.id AND frr.parent_model_name = "Finance_Incomes_Reason" ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' AS fdt' . "\n" .
                 '  ON fir.type = fdt.id ' . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND fir.annulled_by=0 AND (fdt.id = ' . PH_FINANCE_TYPE_CREDIT_NOTICE . "\n" .
                 '  OR fdt.id = ' . PH_FINANCE_TYPE_DEBIT_NOTICE . ')' . "\n" .
                 'ORDER BY fir.id ASC';
        $credit_links = $db->GetAssoc($query);

        $cl = array();
        foreach ($credit_links as $credit => $credit_rows) {
            //prepare row changes
            $changes = array();
            $credit_rows['changes'] = preg_split('#\n|\r|\r\n#', $credit_rows['changes']);
            foreach ($credit_rows['changes'] as $row) {
                if (preg_match('#\s*=>\s*#', $row)) {
                    $row = preg_split('#\s*=>\s*#', $row);
                    if ($parent_model == 'Contract') {
                        if (!isset($changes[$row[0]])) {
                            $changes[$row[0]] = array();
                        }
                        //IMPORTANT !!! count how many times this row exists in the links
                        //as for each link we have to put a change
                        preg_match_all('#=>(\s*)(' . $row[0] . ')(\r?\n|$)#', $credit_rows['rows_links'], $matches);
                        $current_count = count($matches[2]) - count($changes[$row[0]]);
                        for($i = 0; $i < $current_count; $i++) {
                            $changes[$row[0]][] = $row[1];
                        }
                    } else {
                        $changes[$row[0]] = $row[1];
                    }
                }
            }

            //prepare links and changes between rows
            $credit_rows['rows_links'] = preg_split('#\n|\r|\r\n#', $credit_rows['rows_links']);
            foreach ($credit_rows['rows_links'] as $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                if (!empty($row[1])) {
                    if ($parent_model == 'Contract') {
                        $cl[$row[1]][] = array('to' => $row[0], 'change' => array_shift($changes[$row[1]]));
                    } else {
                        $cl[$row[1]][] = array('to' => $row[0], 'change' => $changes[$row[1]]);
                        unset($changes[$row[1]]);
                    }
                }
            }

            foreach ($changes as $row => $key) {
                //set newly added rows in the links/changes array
                if (is_array($key)) {
                    //could be possible in contracts
                    if (empty($key)) {
                        continue;
                    }
                    $key = array_shift($key);
                }
                $cl[$row][] = array('to' => $row, 'change' => $key);
            }
        }

        $gt2 = $this->get('grouping_table_2');
        $prec = $this->registry['config']->getSectionParams('precision');
        if ($parent_model == 'Contract') {
            //check if the invoice is issued from a recurrent template
            if (!$this->isDefined('recurrent')) {
                $query = 'SELECT fit. recurrent' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' fit' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' fiti' . "\n" .
                         '  ON fit.id = fiti.parent_id AND fiti.invoice_id = ' . $this->get('id');
                $recurrent = $this->registry['db']->GetCol($query);
                if (!count($recurrent)) {
                    //invoice issued from proforma (maybe)
                    $query = 'SELECT fit. recurrent' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' fit' . "\n" .
                             'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' fiti' . "\n" .
                             '  ON fit.id = fiti.parent_id' . "\n" .
                             'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' . "\n" .
                             '  ON frr.link_to_model_name = "Finance_Incomes_Reason" AND frr.link_to = fiti.invoice_id' . "\n" .
                             '  AND frr.parent_model_name = "Finance_Incomes_Reason" AND frr.parent_id = ' . $this->get('id') . "\n" .
                             'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                             '  ON fir.id = frr.parent_id AND fir.type = ' . PH_FINANCE_TYPE_INVOICE;
                    $recurrent = $this->registry['db']->GetCol($query);
                }
                $this->set('recurrent', reset($recurrent), true);
            }
            //calculate current rows values
            foreach ($cl as $c => $links) {
                foreach ($links as $l => $link) {
                    $new_values = array();
                    $linked = false;

                    foreach ($gt2['values'] as $v => $values) {
                        $part = array();
                        if ($values['id'] != $c || $linked) {
                            $new_values[] = $values;
                            continue;
                        }
                        //calculate the new values for this row
                        //in function of the dependencies and rows changes
                        if (!in_array($link['change'], array('added', 'deleted'))) {
                            if ($this->get('recurrent')) {
                                //check if the credit/debit row is for the full period of the invoice row
                                //or just for the part of the period
                                if ($credited[$link['to']]['date_to'] < $values['date_from'] || $credited[$link['to']]['date_from'] > $values['date_to']) {
                                    //these periods are not relative
                                    $new_values[] = $values;
                                    continue;
                                } elseif ($credited[$link['to']]['date_from'] < $values['date_from'] || $credited[$link['to']]['date_to'] > $values['date_to']) {
                                    //period of the credit/debit row must not mismatch the period of the invoice row
                                    die('The periods of the credit/debit and invoice rows are incorrect! Contact the support!');

                                }

                                if ($credited[$link['to']]['date_from'] > $values['date_from']) {
                                    //we have to cut the invoice row from left
                                    $part = $gt2['values'][$v];
                                    $part['date_to'] = date_sub(date_create($credited[$link['to']]['date_from']), new DateInterval('P1D'))->format('Y-m-d');
                                    $diff = date_diff(date_create($values['date_to']), date_create($values['date_from']));
                                    $new_diff = date_diff(date_create($part['date_to']), date_create($part['date_from']));
                                    $index = ($new_diff->days + 1) / ($diff->days + 1);
                                    //calculate the changed value for the rest of the period
                                    $part[$link['change']] *= $index;
                                    $new_values[] = $part;
                                    $values['date_from'] = $credited[$link['to']]['date_from'];
                                    $values[$link['change']] *= (1 - $index);
                                    unset($part);
                                }
                                if ($credited[$link['to']]['date_to'] < $values['date_to']) {
                                    //we have to cut the invoice row from right
                                    $part = $gt2['values'][$v];
                                    $part['date_from'] = date_add(date_create($credited[$link['to']]['date_to']), new DateInterval('P1D'))->format('Y-m-d');
                                    $diff = date_diff(date_create($values['date_to']), date_create($values['date_from']));
                                    $new_diff = date_diff(date_create($part['date_to']), date_create($part['date_from']));
                                    $index = ($new_diff->days + 1) / ($diff->days + 1);
                                    //calculate the changed value for the rest of the period
                                    $part[$link['change']] *= $index;
                                    $values['date_to'] = $credited[$link['to']]['date_to'];
                                    $values[$link['change']] *= (1 - $index);
                                }
                            }
                            //at this point we are sure the periods for the invoice row
                            //and the credit/debit row are the same
                            $values[$link['change']] += $credited[$link['to']][$link['change']];
                            if ($link['change'] != 'quantity') {
                                if ($credited[$link['to']]['discount_surplus_field'] == 'discount_value') {
                                    // we have to recalculate percentage
                                    if ($values['price'] == 0) {
                                        $values['discount_percentage'] = 0;
                                        // we can not calculate discount percentage because of division by 0
                                        // set it to 0
                                    } else {
                                        $values['discount_percentage'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round(($values['discount_value'] / $values['price']) * 100, $prec['gt2_rows']));
                                    }
                                } elseif ($credited[$link['to']]['discount_surplus_field'] == 'discount_percentage') {
                                    // we have to recalculate value
                                    $values['discount_value'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round($values['price'] * $values['discount_percentage'] / 100, $prec['gt2_rows']));
                                } elseif ($credited[$link['to']]['discount_surplus_field'] == 'surplus_value') {
                                    // we have to recalculate percentage
                                    if ($values['price'] == 0) {
                                        $values['surplus_percentage'] = 0;
                                        // we can not calculate discount percentage because of division by 0
                                        // set it to 0
                                    } else {
                                        $values['surplus_percentage'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round(($values['surplus_value'] / $values['price']) * 100, $prec['gt2_rows']));
                                    }
                                } elseif ($credited[$link['to']]['discount_surplus_field'] == 'surplus_percentage') {
                                    // we have to recalculate value
                                    $values['surplus_value'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round($values['price'] * $values['surplus_percentage'] / 100, $prec['gt2_rows']));
                                }
                            }
                            $new_values[] = $values;
                            if (!empty($part)) {
                                //the right part from the invoice period(if there is such part)
                                $new_values[] = $part;
                            }
                            $linked = true;
                        } elseif ($link['change'] == 'deleted') {
                            //set the price to zero
                            $values[$link['change']] = 0;
                            $new_values[] = $values;
                            $linked = true;
                        }
                    }
                    $gt2['values'] = $new_values;
                }
                unset($cl[$c]);
            }
            unset($gt2['rows']);
        } else {
            //calculate current rows values
            foreach ($gt2['values'] as $key => $values) {
                if (isset($cl[$key])) {
                    foreach ($cl[$key] as $vals) {
                        if (!in_array($vals['change'], array('added', 'deleted'))) {
                            //calculate the new values for this row
                            //in function of the dependencies and rows changes
                            $gt2['values'][$key][$vals['change']] += $credited[$vals['to']][$vals['change']];
                            if ($gt2['values'][$key][$vals['change']] <= 0 && !preg_match('#discount|surplus#', $vals['change'])) {
                                unset($gt2['values'][$key]);
                            } elseif ($vals['change'] != 'quantity') {
                                if ($credited[$vals['to']]['discount_surplus_field'] == 'discount_percentage') {
                                    $gt2['values'][$key]['discount_value'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round($gt2['values'][$key]['price'] * $gt2['values'][$key]['discount_percentage'] / 100, $prec['gt2_rows']));
                                } elseif ($credited[$vals['to']]['discount_surplus_field'] == 'discount_value') {
                                    if ($gt2['values'][$key]['price'] == 0) {
                                        $gt2['values'][$key]['discount_percentage'] = 0;
                                        // we can not calculate discount percentage because of division by 0
                                        // set it to 0
                                    } else {
                                        $gt2['values'][$key]['discount_percentage'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round(($gt2['values'][$key]['discount_value'] / $gt2['values'][$key]['price']) * 100, $prec['gt2_rows']));
                                    }
                                } elseif ($credited[$vals['to']]['discount_surplus_field'] == 'surplus_percentage') {
                                    $gt2['values'][$key]['surplus_value'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round($gt2['values'][$key]['price'] * $gt2['values'][$key]['surplus_percentage'] / 100, $prec['gt2_rows']));
                                } elseif ($credited[$vals['to']]['discount_surplus_field'] == 'surplus_value') {
                                    if ($gt2['values'][$key]['price'] == 0) {
                                        $gt2['values'][$key]['surplus_percentage'] = 0;
                                        // we can not calculate discount percentage because of division by 0
                                        // set it to 0
                                    } else {
                                        $gt2['values'][$key]['surplus_percentage'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round(($gt2['values'][$key]['surplus_value'] / $gt2['values'][$key]['price']) * 100, $prec['gt2_rows']));
                                    }
                                }
                            }
                        } elseif ($vals['change'] == 'deleted') {
                            //remove row from the array as it's deleted
                            unset($gt2['values'][$key]);
                            unset($gt2['rows'][array_search($key, $gt2['rows'])]);
                        }
                    }
                    unset($cl[$key]);
                }
            }
        }

        //process added rows
        foreach ($cl as $key => $values) {
            foreach ($values as $vals) {
                if ($vals['change'] == 'added') {
                    //we need to get row values from the DB
                    //these rows are added with debit notes ONLY
                    $row = $this->getGT2Row($vals['to']);
                    if (!empty($row)) {
                        $gt2['values'][$vals['to']] = $row;
                    } else {
                        //row has been added but afterwards it has been deleted
                    }
                    //add these rows to the array with table rows
                    //as we need these rows to be with their original indexes
                    //in other case we cannot calculate the row difference
                    //after the submit of the form
                    $gt2['rows'][] = $vals['to'];
                }
            }
        }

        if ($set_table_back) {
            if ($gt2['values']) {
                $this->set('grouping_table_2', $gt2, true);
                $this->calculateGT2();
            } else {
                $this->set('grouping_table_2', array(), true);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        if ($gt2['values']) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Update reasons relatives
     *
     * @return bool
     */
    public function updateReasonsRelatives($new_relation = false) {
        // If it's set to skip relatives
        $params = unserialize(General::slashesStrip($this->get('transform_params')));
        if (!empty($params['skip_relatives'])) {
            // Skip the relatives
            return true;
        }

        $db = $GLOBALS['registry']['db'];

        $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                 'WHERE parent_id=' . $this->get('id') . ' AND parent_model_name="Finance_Incomes_Reason"' . "\n";
        $record = $db->GetRow($query);

        $new_rows_links = $this->get('rows_links');

        //array to show how we change each row(price/quantity/added/deleted)
        $rows_changes = $this->get('rows_changes');
        if (empty($rows_changes)) {
            $rows_changes = array();
        }

        if (!empty($record) && !$new_relation) {
            //invoice edit - remove deleted rows form the links
            if (!empty($new_rows_links['deleted'])) {
                foreach ($new_rows_links['deleted'] as $deleted) {
                    if (preg_match('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', $record['rows_links'])) {
                        $record['rows_links'] = preg_replace('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', "", $record['rows_links']);
                    }
                }
                if (!empty($new_rows_links['added'])) {
                    //document adding
                    foreach ($new_rows_links['added'] as $new_row => $old_row) {
                        $record['rows_links'] .= $new_row . ' => ' . $old_row . "\n";
                    }
                }
                $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                         ' SET rows_links = "' . $record['rows_links'] . '", changes = "' . implode("\n", $rows_changes) . '"' . "\n" .
                         'WHERE parent_id = ' . $record['parent_id'] .
                         '  AND parent_model_name="' . $record['parent_model_name'] . '" ' . "\n" .
                         '  AND link_to= ' . $record['link_to'] . "\n" .
                         '  AND link_to_model_name="' . $record['link_to_model_name'] . '" ';
                $db->Execute($query);
            } elseif ($rows_changes) {
                $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                    ' SET changes = "' . implode("\n", $rows_changes) . '"' . "\n" .
                    'WHERE parent_id = ' . $record['parent_id'] .
                    '  AND parent_model_name="' . $record['parent_model_name'] . '" ' . "\n" .
                    '  AND link_to= ' . $record['link_to'] . "\n" .
                    '  AND link_to_model_name="' . $record['link_to_model_name'] . '" ';
                $db->Execute($query);
            }
        } elseif (!empty($new_rows_links['added']) || ($this->get('annulled_by') && $this->get('link_to'))) {
            //document adding
            if (!empty($new_rows_links['added'])) {
                foreach ($new_rows_links['added'] as $new_row => $old_row) {
                    $new_rows_links['added'][$new_row] = $new_row . ' => ' . $old_row;
                }
            } else {
                $new_rows_links['added'] = array();
            }

            if ($this->get('link_to_model_name')) {
                $link_to_model_name = $this->get('link_to_model_name');
            } else {
                $link_to_model_name = "Finance_Incomes_Reason";
            }

            $shared = $this->isDefined('shared') ? $this->get('shared') : 'NULL';

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     ' SET parent_id = ' . $this->get('id') . ",\n" .
                     '     parent_model_name = "Finance_Incomes_Reason",' . "\n" .
                     '     link_to = ' . $this->get('link_to') . ",\n" .
                     '     link_to_model_name = "' . $link_to_model_name . '"' . ",\n" .
                     '     rows_links = \'' . implode("\n", $new_rows_links['added']) . '\',' . "\n" .
                     '       changes = "' . implode("\n", $rows_changes) . '",' . "\n" .
                     '       shared = ' . $shared;
            $db->Execute($query);
        }
        return !$db->HasFailedTrans();
    }

    /**
     * Correct the incomes reason and create correction document
     *
     * @param object $old_model - The old incomes reason
     * @return bool - result of the operation
     */
    public function saveCorrect(&$old_model) {

        $db = $this->registry['db'];

        if (!$this->isActivated()) {
            $this->raiseError('error_finance_deactivate_add', 'activate', null, array($this->i18n('addcorrect')));
        }
        if (in_array($old_model->get('payment_status'), array('paid', 'partial')) &&
        bccomp($this->get('total_with_vat'), $old_model->getFullPaidAmount(), $this->registry['config']->getParam('precision', 'gt2_total_with_vat')) == -1) {
            $this->raiseError('error_finance_incomes_reason_overpaid', '', null,
                array($old_model->get('full_paid_amount'), $this->get('total_with_vat'),
                      sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=payments&amp;payments=%d',
                              $_SERVER['PHP_SELF'], $this->registry['module_param'],
                              $this->registry['controller_param'], $old_model->get('id')),
                      sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=relatives&amp;relatives=%d',
                              $_SERVER['PHP_SELF'], $this->registry['module_param'],
                              $this->registry['controller_param'], $old_model->get('id'))
                ));
        }
        if (!$this->valid) {
            return false;
        }

        //start transaction
        $db->StartTrans();

        $name = $this->get('name');
        $description = $this->get('description');

        //set old properties
        $this->set('name', $old_model->get('name'), true);
        $this->set('description', $old_model->get('description'), true);
        $this->set('import_customer_name', $old_model->get('customer_name'), true);
        $this->set('trademark_name', $old_model->get('trademark_name'), true);
        $this->set('issue_date', $old_model->get('issue_date'), true);
        $this->set('payment_status', $old_model->get('payment_status'), true);
        $this->set('correction', true, true);

        //get current gt2 rows
        $query = 'SELECT id FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE model = "Finance_Incomes_Reason" AND model_id = ' . $this->get('id');
        $this->set('old_gt2_rows', $this->registry['db']->GetCol($query), true);

        // remove distribution data of incomes reason
        if ($old_model->get('distributed') == PH_FINANCE_DISTRIBUTION_YES) {
            $this->deleteDistribution();
            $this->set('distributed', PH_FINANCE_DISTRIBUTION_NO, true);
        }

        //save changes in the parent document
        $this->slashesEscape();
        $this->edit();
        $this->slashesStrip();

        //get model type
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $fin_type = Finance_Documents_Types::searchOne($this->registry, array('where' => array('fdt.id = ' . $this->get('type'))));
        $fin_type->sanitize();

        //get old and new values of the model
        $old_gt2 = $old_model->get('grouping_table_2');
        $old_values = $old_gt2['values'];
        $old_plain_values = $old_gt2['plain_values'];
        $new_values = $this->get('grouping_table_2');
        $new_plain_values = $new_values['plain_values'];
        $new_values = $new_values['values'];

        //create correction document
        $this->set('parent_name', $this->get('name'), true);
        $this->set('name', $name, true);
        $this->set('description', $description, true);
        $correct_reason = clone $this;
        $correct_reason->unsetProperty('id', true);
        $correct_reason->unsetProperty('num', true);
        $correct_reason->unsetProperty('payment_status', true);
        $correct_reason->unsetProperty('new_handovered_status', true);
        $correct_reason->unsetProperty('distributed', true);
        $correct_reason->set('parent_type', $this->get('type'), true);
        $correct_reason->set('type', PH_FINANCE_TYPE_CORRECT_REASON, true);
        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                 'WHERE parent_id = ' . $correct_reason->get('type') . ' AND lang="' . $this->registry['lang'] . '"';
        $type_name = $db->GetOne($query);
        $correct_reason->set('type_name', $type_name, true);
        $correct_reason->set('status', 'finished', true);
        $correct_reason->set('link_to', $old_model->get('id'), true);
        if (!$this->registry['request']->isRequested('issue_date')) {
            $correct_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
        } else {
            $correct_reason->set('issue_date', $this->registry['request']->get('issue_date'), true);
        }
        unset($correct_reason->counter);

        $correct_reason->getGT2Vars();
        $correct_gt2 = $correct_reason->get('grouping_table_2');
        //clean the values
        $correct_gt2['values'] = array();
        $correct_gt2['rows'] = array();
        $rows_changes = array();

        $correctables = array('quantity', $fin_type->get('calculated_price'), 'discount_value', 'surplus_value');
        $prec = $this->registry['config']->getSectionParams('precision');

        //calculate the difference
        foreach ($old_values as $row => $values) {
            if (isset($new_values[$row])) {
                $changed = array();
                //get changed field(s)
                foreach ($correctables as $k => $v) {
                    if ($new_values[$row][$v] - $values[$v] != 0) {
                        //if price has been changed the discount/surplus values also change
                        //so do not treat this change as another one
                        if (preg_match('#discount|surplus#', $v)) {
                            if (in_array($fin_type->get('calculated_price'), $changed)) {
                                continue;
                            }
                        }
                        $changed[] = $v;
                    }
                }

                if (count($changed) > 1) {
                    //we can not change more than one field per row
                    $this->registry['messages']->setError($this->i18n('error_finance_correct_both_changed'));
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                } elseif (count($changed) == 1) {
                    $changed = reset($changed);
                    //process changed field
                    $correct_gt2['values'][$row] = $values;
                    $correct_gt2['values'][$row][$changed] = $new_values[$row][$changed] - $values[$changed];
                    if (preg_match('#discount|surplus#', $changed)) {
                        $correct_gt2['values'][$row][$fin_type->get('calculated_price')] = 0;
                        if ($new_values[$row]['discount_surplus_field'] == 'none') {
                            // discount/surplus has been removed
                            $correct_gt2['values'][$row]['discount_surplus_field'] = $changed;
                        } else {
                            $changed = $correct_gt2['values'][$row]['discount_surplus_field'] = $new_values[$row]['discount_surplus_field'];
                        }

                        // we have to get discount/surplus percentage as well
                        $of = preg_replace('#value#', 'percentage', $changed);
                        $correct_gt2['values'][$row][$of] = $new_values[$row][$of] - $values[$of];
                    } elseif ($changed == $fin_type->get('calculated_price')) {
                        //recalculate the discount/surplus values in function of the percentage
                        if ($correct_gt2['values'][$row]['discount_surplus_field'] == 'discount_percentage') {
                            $correct_gt2['values'][$row]['discount_value'] = round($correct_gt2['values'][$row][$changed] * $correct_gt2['values'][$row]['discount_percentage'] / 100, $prec['gt2_rows']);
                        } elseif ($correct_gt2['values'][$row]['discount_surplus_field'] == 'discount_value') {
                            $correct_gt2['values'][$row]['discount_value'] = $correct_gt2['values'][$row]['discount_percentage'] = 0;
                            $correct_gt2['values'][$row]['discount_surplus_field'] = 'none';
                        }
                        if ($correct_gt2['values'][$row]['discount_surplus_field'] == 'surplus_percentage') {
                            $correct_gt2['values'][$row]['surplus_value'] = round($correct_gt2['values'][$row][$changed] * $correct_gt2['values'][$row]['surplus_percentage'] / 100, $prec['gt2_rows']);
                        } elseif ($correct_gt2['values'][$row]['discount_surplus_field'] == 'surplus_value') {
                            $correct_gt2['values'][$row]['surplus_value'] = $correct_gt2['values'][$row]['surplus_percentage'] = 0;
                            $correct_gt2['values'][$row]['discount_surplus_field'] = 'none';
                        }
                    }
                    $rows_changes[$row] = $row . ' => ' . $changed;
                }
                unset($new_values[$row]);
            } else {
                //row has been deleted
                $correct_gt2['values'][$row] = $values;
                $correct_gt2['values'][$row]['quantity'] = - $values['quantity'];
                $rows_changes[$row] = $row . ' => deleted';
            }
        }

        foreach ($new_values as $row => $values) {
            //rows that are added
            if (!empty($values['article_id'])) {
                //and not empty
                $correct_gt2['values'][$row] = $values;
                $rows_changes[$row] = $row . ' => added';
            }
        }

        if (!empty($correct_gt2['values'])) {

            //get information about what we can issue with handover
            $old_model->checkAddingHandover('outgoing', true);
            $handover_gt2 = $old_model->get('grouping_table_2');
            $handover_gt2 = isset($handover_gt2['values']) ? $handover_gt2['values'] : array();

            $erred_articles = $articles = array();
            foreach ($correct_gt2['values'] as $row => $values) {
                $articles[] = $values['article_id'];
            }
            if (!empty($articles)) {
                $query = 'SELECT id, subtype FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE id IN (' . implode(', ', $articles) . ')';
                $articles = $db->GetAssoc($query);
            }
            foreach ($correct_gt2['values'] as $row => $values) {
                //for each row which we decrease quantity for (negative quantity)
                //we have to check if we have quantities not received
                //from the customer with handovers
                //!!! ONLY FOR COMMODITY ARTICLES
                if ($values['article_id'] > 0 && $articles[$values['article_id']] == 'commodity' && $values['quantity'] < 0 &&
                (empty($handover_gt2[$row]['quantity']) ||
                $handover_gt2[$row]['quantity'] < abs($values['quantity']))) {
                    //calculate the minimal quantity we can choose
                    $minimal = isset($old_values[$row]['quantity']) ? $old_values[$row]['quantity'] : 0;
                    if (!empty($handover_gt2[$row]['quantity'])) {
                        $minimal -= $handover_gt2[$row]['quantity'];
                    }
                    $erred_articles[] = $values['article_name'] . ' (min. ' . $minimal . ')';
                }
            }

            if (!empty($erred_articles)) {
                //set error message for the erred articles
                $erred_articles = '<br>&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;-&nbsp;', $erred_articles);
                $this->registry['messages']->setError($this->i18n('error_finance_correct_more_in_handover') . $erred_articles);
                $db->FailTrans();
            }

            $correct_reason->set('rows_changes', $rows_changes, true);
            $correct_gt2['calculated_price'] = $old_gt2['calculated_price'];
            $correct_reason->set('grouping_table_2', $correct_gt2, true);
            $correct_reason->calculateGT2();
            $correct_reason->set('table_values_are_set', true, true);

            //save the correction document
            if (!$correct_reason->save()) {
                $db->FailTrans();
            } else {
                $old_reason = new Finance_Incomes_Reason($this->registry,
                    array(
                        'type' => PH_FINANCE_TYPE_CORRECT_REASON,
                        'parent_type' => $correct_reason->get('parent_type'),
                        'company' => $correct_reason->get('company')
                    ));
                $get_old_vars = $this->registry->get('get_old_vars');
                $this->registry->set('get_old_vars', true, true);
                $old_reason->getGT2Vars();
                $this->registry->set('get_old_vars', $get_old_vars, true);
                $old_reason->sanitize();
                $correct_reason->slashesStrip();
                require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
                Finance_Incomes_Reasons_History::saveData($this->registry,
                                                          array(
                                                              'action_type' => 'add',
                                                              'new_model' => $correct_reason,
                                                              'old_model' => $old_reason
                                                          ));
            }
        // if there are differences in plain values, edit the reason
        } elseif (!array_diff_assoc($new_plain_values, $old_plain_values)) {
            $this->registry['messages']->setError($this->i18n('error_finance_correct_empty'));
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;
        //restore gt2 as it could have been changed in checkAddingHandover function
        $old_model->set('grouping_table_2', $old_gt2, true);
        if ($result) {
            $old_model->set('correction_id', $correct_reason->get('id'), true);
        }

        return $result;
    }

    /**
     * Creates credit and/or debit notices for invoices
     *
     * @param object $old_model - the invoice
     * @return bool - result of the operation
     */
    public function saveCreditDebit($old_model) {

        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if (!$this->get('cd_reason')) {
            $this->raiseError('error_finance_incomes_reasons_no_cd_reason_specified', 'cd_reason', null,
                              array($this->getLayoutName('cd_reason')));
            $db->FailTrans();
        }
        if (!$this->isActivated()) {
            $this->raiseError('error_finance_deactivate_add', 'activate', null, array($this->i18n('addcreditdebit')));
            $db->FailTrans();
        }
        if ($db->HasFailedTrans()) {
            $db->CompleteTrans();
            return false;
        }

        //$this->edit();

        // keep real total_with_vat of invoice before any modifications
        $real_total_with_vat = $old_model->get('total_with_vat');

        //get values of the models
        $old_model->checkAddingCredit(true);
        $old_values = $old_model->get('grouping_table_2');
        $invoice_rows = $old_values['rows'];
        $calculated_price = $old_values['calculated_price'];
        $old_values = $old_values['values'];
        $this->registry->set('get_old_vars', false, true);
        $this->getGT2Vars();
        $new_values = $this->get('grouping_table_2');
        $new_values = $new_values['values'];

        //check settings in function of the reason type
        $old_model->getRelatives(array('get_reason' => true));
        $reason = $old_model->get('reason');
        $old_model->unsetProperty('reason');
        if ($reason->modelName != 'Contract') {
            require_once(PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php');
            $reason_type = Finance_Documents_Types::searchOne($this->registry,
                array('where' => array('fdt.id = ' . $reason->get('type')),
                      'sanitize' => true));
        } else {
            require_once(PH_MODULES_DIR . 'contracts/models/contracts.types.factory.php');
            $reason_type = Contracts_Types::searchOne($this->registry,
                array('where' => array('cot.id = ' . $reason->get('type')),
                      'sanitize' => true));
        }

        if (empty($calculated_price)) {
            $calculated_price = $reason_type->get('calculated_price');
        }

        // get parent name from request or from old model
        $parent_name = $this->get('name') ? $this->get('name') : $old_model->get('name');
        $this->set('trademark_name', $old_model->get('trademark_name'), true);

        //create an empty credit notice
        $credit = clone $this;
        $credit->unsetProperty('id', true);
        $credit->unsetProperty('num', true);
        $credit->set('type', PH_FINANCE_TYPE_CREDIT_NOTICE, true);
        $credit->set('status', 'finished', true);
        $credit->set('link_to', $old_model->get('id'), true);
        // set name to an empty string (it will be set automatically before save using type name and parent name)
        $credit->set('parent_name', $parent_name, true);
        $credit->set('name', '', true);
        $credit->getGT2Vars();
        $credit_gt2 = $credit->get('grouping_table_2');
        $credit_gt2['values'] = array();

        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' WHERE parent_id = ' . PH_FINANCE_TYPE_CREDIT_NOTICE;
        $credit->set('type_name', $db->GetOne($query), true);

        //create an empty debit notice
        $debit = clone $this;
        $debit->unsetProperty('id', true);
        $debit->unsetProperty('num', true);
        $debit->set('type', PH_FINANCE_TYPE_DEBIT_NOTICE, true);
        $debit->set('status', 'finished', true);
        $debit->set('link_to', $old_model->get('id'), true);
        // set name to an empty string (it will be set automatically before save using type name and parent name)
        $debit->set('parent_name', $parent_name, true);
        $debit->set('name', '', true);
        $debit->getGT2Vars();
        $debit_gt2 = $debit->get('grouping_table_2');
        $debit_gt2['values'] = array();

        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' WHERE parent_id = ' . PH_FINANCE_TYPE_DEBIT_NOTICE;
        $debit->set('type_name', $db->GetOne($query), true);

        //calculate the differences
        $changed_rows_d = array();
        $changed_rows_c = array();
        $prec = $this->registry['config']->getSectionParams('precision');

        $correctables = array('quantity', $calculated_price, 'discount_value', 'surplus_value');

        foreach ($old_values as $row => $values) {
            if (!empty($new_values[$row]) && empty($new_values[$row]['article_id'])) {
                unset($new_values[$row]);
                continue;
            }
            if (isset($new_values[$row]) && !$new_values[$row]['deleted']) {
                $changed = $tmp = array();
                foreach ($correctables as $k => $v) {
                    if ($v == 'quantity') {
                        $multiplier = pow(10, $prec['gt2_quantity']);
                    } else {
                        $multiplier = pow(10, $prec['gt2_rows']);
                    }
                    //if price has been changed the discount/surplus values also change
                    //so do not treat this change as another one
                    if (intval($new_values[$row][$v] * $multiplier) - intval($values[$v] * $multiplier) != 0) {
                        if (preg_match('#discount|surplus#', $v) && in_array($calculated_price, $changed)) {
                            continue;
                        }
                        $changed[] = $v;
                    }
                }
                if (count($changed) > 1) {
                    $this->registry['messages']->setError($this->i18n('error_finance_correct_both_changed'));
                    $db->FailTrans();
                } elseif (count($changed) == 1) {
                    $changed = reset($changed);
                    //process changed field
                    $tmp = $values;
                    $tmp[$changed] = $new_values[$row][$changed] - $values[$changed];
                    if (preg_match('#discount|surplus#', $changed)) {
                        $tmp[$calculated_price] = 0;
                        if ($new_values[$row]['discount_surplus_field'] == 'none') {
                            // discount/surplus has been removed
                            $tmp['discount_surplus_field'] = $changed;
                        } else {
                            $changed = $tmp['discount_surplus_field'] = $new_values[$row]['discount_surplus_field'];
                        }

                        // we have to get discount/surplus percentage as well
                        $of = preg_replace('#value#', 'percentage', $changed);
                        $tmp[$of] = $new_values[$row][$of] - $values[$of];
                    } elseif ($changed == $calculated_price) {
                        //recalculate the discount/surplus values in function of the percentage
                        if ($tmp['discount_surplus_field'] == 'discount_percentage') {
                            $tmp['discount_value'] = round($tmp[$changed] * $tmp['discount_percentage'] / 100, $prec['gt2_rows']);
                        } elseif ($tmp['discount_surplus_field'] == 'discount_value') {
                            $tmp['discount_value'] = $tmp['discount_percentage'] = 0;
                            $tmp['discount_surplus_field'] = 'none';
                        }
                        if ($tmp['discount_surplus_field'] == 'surplus_percentage') {
                            $tmp['surplus_value'] = round($tmp[$changed] * $tmp['surplus_percentage'] / 100, $prec['gt2_rows']);
                        } elseif ($tmp['discount_surplus_field'] == 'surplus_value') {
                            $tmp['surplus_value'] = $tmp['surplus_percentage'] = 0;
                            $tmp['discount_surplus_field'] = 'none';
                        }
                    } elseif (!empty($values['discount_value']) || !empty($values['surplus_value'])) {
                        //quantity is changed but the discount/surplus have been changed in previous credit/debit
                        if (!empty($new_values[$row]['discount_surplus_field']) && $new_values[$row]['discount_surplus_field'] != 'none') {
                            //someone touched the discount/surplus percentage/value fields without changing its content
                            $tmp['discount_surplus_field'] = $new_values[$row]['discount_surplus_field'];
                        } elseif (!empty($values['discount_percentage'])) {
                            //wild guess that the percentage has been selected in the previous credit/debit
                            $tmp['discount_surplus_field'] = 'discount_percentage';
                        } elseif (!empty($values['surplus_percentage'])) {
                            //wild guess that the percentage has been selected in the previous credit/debit
                            $tmp['discount_surplus_field'] = 'surplus_percentage';
                        }
                    }
                }

                if (!empty($tmp)) {
                    if (preg_match('#discount#', $changed)) {
                        if ($tmp[$changed] > 0) {
                            //row was changed - credit
                            $credit_gt2['values'][$row] = $tmp;
                            $changed_rows_c[$row] = $row . ' => ' . $changed;
                        } elseif ($tmp[$changed] < 0) {
                            //row was changed - debit
                            $debit_gt2['values'][$row] = $tmp;
                            $changed_rows_d[$row] = $row . ' => ' . $changed;
                        }
                    } elseif ($tmp[$changed] < 0) {
                        //row was changed - credit
                        $credit_gt2['values'][$row] = $tmp;
                        $changed_rows_c[$row] = $row . ' => ' . $changed;
                    } elseif ($tmp[$changed] > 0) {
                        //row was changed - debit
                        $debit_gt2['values'][$row] = $tmp;
                        $changed_rows_d[$row] = $row . ' => ' . $changed;
                    }
                }
                unset($new_values[$row]);
            } else {
                //row was deleted - credit
                if (isset($debit_gt2['system_articles'][$values['article_id']]) && $debit_gt2['system_articles'][$values['article_id']] == 'discount') {
                    $debit_gt2['values'][$row] = $values;
                    $debit_gt2['values'][$row][$calculated_price] *= -1;
                    $changed_rows_d[$row] = $row . ' => deleted';
                } elseif (!empty($credit_gt2['system_articles'][$values['article_id']])) {
                    $credit_gt2['values'][$row] = $values;
                    $credit_gt2['values'][$row][$calculated_price] *= -1;
                    $changed_rows_c[$row] = $row . ' => deleted';
                } else {
                    $credit_gt2['values'][$row] = $values;
                    $credit_gt2['values'][$row]['quantity'] = - $values['quantity'];
                    $changed_rows_c[$row] = $row . ' => deleted';
                }
                if (!empty($new_values[$row]['deleted'])) {
                    unset($new_values[$row]);
                }
            }
        }

        if (!empty($new_values)) {
            foreach ($new_values as $row => $values) {
                //rows that are added - debit
                if (!empty($values['article_id'])) {
                    if ($values['price_with_discount'] < 0 && $credit_gt2['system_articles'][$values['article_id']] == 'discount') {
                        $credit_gt2['values'][$row] = $values;
                        $credit->set('invoice_rows', $invoice_rows, true);
                    } else {
                        $debit_gt2['values'][$row] = $values;
                        $debit->set('invoice_rows', $invoice_rows, true);
                    }
                }
            }
        }

        if ($old_model->get('advance') && !empty($debit_gt2['values'])) {
            // debit note cannot be added to advance invoice
            // MOREOVER: check for non-invoiced quantities against reason
            // could not possibly be performed for "Advance" article
            $debit_error_msg = $this->i18n('error_finance_incomes_reasons_advance_debit_denied');

            // if user has permissions to add invoice
            $rights = $this->registry['currentUser']->get('rights');
            $add_invoice_right = isset($rights['finance_incomes_reasons' . PH_FINANCE_TYPE_INVOICE]['add']) ?
                                 $rights['finance_incomes_reasons' . PH_FINANCE_TYPE_INVOICE]['add'] :
                                 'all';
            if ($add_invoice_right) {
                if ($reason && $reason->modelName == 'Finance_Incomes_Reason' && $reason->checkAddingAdvance()) {
                    // if advance invoice is related to reason and another advance invoice could be issued from reason
                    $add_invoice_url = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=advances&amp;advances=%d',
                                               $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                               $this->registry['controller_param'],
                                               $reason->get('id'));

                    $debit_error_msg .= '<br />' .
                            $this->i18n('error_finance_incomes_reasons_advance_debit_denied_add_invoice',
                                        array('add_invoice_url' => $add_invoice_url));
                }
            }

            $this->registry['messages']->setError($debit_error_msg);
            return false;

        } elseif ($reason->modelName == 'Contract' || !$reason_type->get('credit')) {
            // if reason type does not allow incomes reason to be corrected,
            // adding debit note is not allowed when non-invoiced quantities in reason are insufficient
            $this->registry->set('get_old_vars', true, true);
            $reason->getGT2Vars();
            //calculate quantities
            if ($reason->modelName != 'Contract') {
                $reason->checkAddingInvoice(true);
            }
            $reason_values = $reason->get('grouping_table_2');
            //if table is empty, everything is invoiced
            if (empty($reason_values['values']) && !empty($debit_gt2['values'])) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_debit_denied',
                                                      array('reason_type_name' => $reason->getModelTypeName(),
                                                            'invoice_type_name' => $old_model->getModelTypeName())));
                return false;
            }

            $reason_values = isset($reason_values['values']) ? $reason_values['values'] : array();
            $relations = array();
            //get rows relations
            $relations = Finance_Incomes_Reasons::getRelationsRows($old_model->get('id'), $relations, $reason->modelName);
            $relations = $relations['links'];
            //check debit rows
            if (!empty($debit_gt2['values']) && $reason->modelName != 'Contract') {
                foreach ($debit_gt2['values'] as $row => $values) {
                    //check row by row if we have enough quantity
                    if (isset($relations[$row]) && $values['quantity'] > $reason_values[$relations[$row]]['quantity']) {
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_debit_denied',
                                                              array('reason_type_name' => $reason->getModelTypeName(),
                                                                    'invoice_type_name' => $old_model->getModelTypeName())));
                        return false;
                    }
                }
            }
        }

        require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');

        $added = array();
        $error_adding_dc = false;
        if (!empty($credit_gt2['values'])) {
            $credit->set('rows_changes', $changed_rows_c, true);
            $credit->set('grouping_table_2', $credit_gt2, true);
            //ToDo - check for calculation
            $credit->calculateGT2();
            $credit->set('table_values_are_set', true, true);
            $credit->slashesEscape();
            // check if credit note can be fully allocated to parent invoice
            // make sure amount of credit note is less than 0
            if (bccomp($credit->get('total_with_vat'), 0, 2) == -1 &&
            bccomp($real_total_with_vat - $old_model->getPaidAmount(), abs($credit->get('total_with_vat')), 2) > -1) {
                $credit->set('save_auto_balance', true, true);
            }

            //save the credit notice
            if ($credit->add()) {
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                            array('action_type' => 'addcredit',
                                                                                  'model' => $this,
                                                                                  'new_model' => $credit,
                                                                                  'old_model' => $credit
                                                                            ));
                $added['credit'] = $credit->get('id');
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addcredit_success',
                                                        array('invoice_type_name' => $old_model->getModelTypeName())));

                $old_reason = new Finance_Incomes_Reason($this->registry);
                $credit_gt2 = $credit->get('grouping_table_2');
                $credit_gt2['values'] = $credit_gt2['plain_values'] = $credit_gt2['rows'] = array();
                $old_reason->set('grouping_table_2', $credit_gt2, true);
                $old_reason->sanitize();
                Finance_Incomes_Reasons_History::saveData($this->registry,
                                                          array('action_type' => 'add',
                                                                'new_model' => $credit,
                                                                'old_model' => $old_reason
                                                          ));

                if ($credit->isDefined('save_auto_balance')) {
                    if ($credit->get('save_auto_balance')) {
                        Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                  array('action_type' => 'payments',
                                                                        'new_model' => $credit,
                                                                        'old_model' => $credit
                                                                  ));

                        //show success message
                        $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_payment_success',
                                                                array($credit->getModelTypeName())));
                    } else {
                        //some error occurred
                        $this->registry['messages']->setWarning($this->i18n('error_finance_incomes_reasons_edit_payment_failed',
                                                                array($credit->getModelTypeName())));
                    }
                }
            } else {
                $error_adding_dc = true;
            }
        }

        if (!empty($debit_gt2['values'])) {
            $debit->set('rows_changes', $changed_rows_d, true);
            $debit->set('grouping_table_2', $debit_gt2, true);
            $debit->calculateGT2();
            $debit->set('table_values_are_set', true, true);
            $debit->slashesEscape();
            //save the debit notice
            if ($debit->add()) {
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                            array('action_type' => 'adddebit',
                                                                                  'model' => $this,
                                                                                  'new_model' => $debit,
                                                                                  'old_model' => $debit
                                                                            ));
                $added['debit'] = $debit->get('id');
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_adddebit_success',
                                                        array('invoice_type_name' => $old_model->getModelTypeName())));

                $old_reason = new Finance_Incomes_Reason($this->registry);
                $debit_gt2 = $debit->get('grouping_table_2');
                $debit_gt2['values'] = $debit_gt2['plain_values'] = $debit_gt2['rows'] = array();
                $old_reason->set('grouping_table_2', $debit_gt2, true);
                $old_reason->sanitize();
                Finance_Incomes_Reasons_History::saveData($this->registry,
                                                          array('action_type' => 'add',
                                                                'new_model' => $debit,
                                                                'old_model' => $old_reason
                                                          ));
            } else {
                $error_adding_dc = true;
            }
        }

        //no data for credit/debit notes
        if (empty($added) && !$error_adding_dc) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_addcreditdebit_no_data',
                                                  array('invoice_type_name' => $old_model->getModelTypeName())));
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;
        $this->set('reason_model_name', $reason->modelName, true);
        if ($result && !empty($added)) {
            if ($reason->modelName == 'Contract' || !$reason_type->get('credit')) {
                return true;
            }
            return $added;
        } else {
            return false;
        }
    }

    /**
     * Prepare rows changes between incomes reason and invoice
     *
     * @param Finance_Incomes_Reason $old_model - incomes reason with GT2 prepared
     */
    public function prepareInvoiceChanges($old_model) {
        if (empty($old_model)) {
            return true;
        }

        if ($old_model->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            // invoice from proforma is issued
            // tricky, tricky
            $query = 'SELECT changes FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     'WHERE parent_model_name = "Finance_Incomes_Reason" AND link_to_model_name = parent_model_name AND parent_id = ' . $old_model->get('id');
            $changed_rows = $this->registry['db']->GetOne($query);
            $changed_rows = preg_split('#\r\n|\n|\r#', $changed_rows);
            $this->set('rows_changes', $changed_rows, true);
            return true;
        }

        $chFields = array('quantity', 'price', 'discount_value', 'surplus_value');

        $old_values = $old_model->get('grouping_table_2');
        $old_values = $old_values['values'];

        $gt2 = $this->get('grouping_table_2');
        if (empty($gt2)) {
            $gt2 = $this->getGT2Vars();
        }
        $new_values = $gt2['values'];

        $prec = $this->registry['config']->getSectionParams('precision');
        $changed_rows = array();
        $error = false;
        foreach ($old_values as $row => $values) {
            if (isset($new_values[$row]) && empty($new_values[$row]['deleted'])) {
                $changed = $tmp = array();
                foreach ($chFields as $v) {
                    if ($v == 'quantity') {
                        $multiplier = pow(10, $prec['gt2_quantity']);
                    } else {
                        $multiplier = pow(10, $prec['gt2_rows']);
                    }
                    //if price has been changed the discount/surplus values also change
                    //so do not treat this change as another one
                    if (intval($new_values[$row][$v] * $multiplier) - intval($values[$v] * $multiplier) != 0) {
                        if (preg_match('#discount|surplus#', $v) && in_array('price', $changed)) {
                            continue;
                        }
                        $changed[] = $v;
                    }
                }
                if (count($changed) > 1) {
                    $this->registry['messages']->setError($this->i18n('error_finance_correct_both_changed'));
                    $error = true;
                } elseif (count($changed) == 1) {
                    $changed = reset($changed);
                    //process changed field
                    $tmp = $values;
                    $tmp[$changed] = $new_values[$row][$changed] - $values[$changed];
                    if (preg_match('#discount|surplus#', $changed)) {
                        $tmp['price'] = 0;
                    } elseif ($changed == 'price') {
                        //recalculate the discount/surplus values in function of the percentage
                        $tmp['discount_value'] = round($tmp[$changed] * $tmp['discount_percentage'] / 100, $prec['gt2_rows']);
                        $tmp['surplus_value'] = round($tmp[$changed] * $tmp['surplus_percentage'] / 100, $prec['gt2_rows']);
                    }
                }

                if (!empty($tmp)) {
                    //row was changed
                    $changed_rows[$row] = (!empty($values['reason_row']) ? $values['reason_row'] : $row) . ' => ' . $changed;
                }
                unset($new_values[$row]);
            }
        }
        $this->set('rows_changes', $changed_rows, true);
        return !$error;
    }

    /**
     * Corrects parent incomes reason of invoice when
     * credit and/or debit notes are issued for invoice
     *
     * @param array $added - ids of added credit or debit notes if any
     * @return bool - result of the operation
     */
    public function correctFromInvoice($added = array()) {

        $db = &$this->registry['db'];
        $request = &$this->registry['request'];

        //start transaction
        $db->StartTrans();

        $credit = array();
        $debit = array();
        $debit_relations = array();
        $credit_relations = array();
        $relations = array();
        if (!empty($added['credit'])) {

            $filters = array('where' => array('fir.id = ' . $added['credit'], 'fir.annulled_by = 0'),
                             'sanitize' => true);
            $credit = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $credit->getGT2Vars();
            $credit_relations = Finance_Incomes_Reasons::getRelationsRows($credit->get('id'), $credit_relations);
        }
        if (!empty($added['debit'])) {

            $filters = array('where' => array('fir.id = ' . $added['debit'], 'fir.annulled_by = 0'),
                             'sanitize' => true);
            $debit = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $debit->getGT2Vars();
            $debit_relations = Finance_Incomes_Reasons::getRelationsRows($debit->get('id'), $debit_relations);
        }

        //merge debit and credit relations
        $relations = array();
        if (!empty($credit_relations['links'])) {
            $relations += $credit_relations['links'];
        }
        if (!empty($debit_relations['links'])) {
            $relations += $debit_relations['links'];
        }

        $changes = array();
        if (!empty($credit_relations['changes'])) {
            $changes = $credit_relations['changes'];
        }

        if (!empty($debit_relations['changes'])) {
            foreach ($debit_relations['changes'] as $k => $v) {
                //merge rows changes
                if (isset($changes[$k])) {
                    $changes[$k] += $v;
                } else {
                    $changes[$k] = $v;
                }
                ksort($changes[$k]);
            }
        }

        $query = 'SELECT frr.link_to' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON fir.id = frr.link_to' . "\n" .
                 'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' .
                 '  AND fir.annulled_by=0 AND fir.`type` > ' . PH_FINANCE_TYPE_MAX;
        $ids = $db->GetCol($query);

        $filters = array('where' => array('fir.id IN (' . implode(', ', $ids) . ')'),
                         'sanitize' => true);

        // get all incomes reasons related to the invoice
        // IMPORTANT!!! now we have only one incomes reason because we don't have complex transformations
        $incomes_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);

        // TODO : when the other transformations are made this part has to be modified
        //        to work with several incomes reasons if one invoice is issued for several reasons
        $incomes_reason = $incomes_reasons[0];

        //check for add credit/debit
        /*require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $financeType = Finance_Documents_Types::searchOne($this->registry, array(
                                    'where' => array('fdt.id=' . $incomes_reason->get('type'))));
        if (!$financeType->get('credit')) {
            return false;
        }*/

        $this->registry->set('get_old_vars', true, true);
        $incomes_reason->getGT2Vars();
        $old_incomes_reason = clone $incomes_reason;
        $reason_gt2 = $incomes_reason->get('grouping_table_2');
        $reason_rows = !empty($reason_gt2['rows']) ? $reason_gt2['rows'] : array();

        //check what we have not invoiced in the reason
        if (!$request->get('issue_correct')) {
            $incomes_reason->checkAddingInvoice(true);
            $not_invoiced = $incomes_reason->get('grouping_table_2');
            $not_invoiced = !empty($not_invoiced['values']) ? $not_invoiced['values'] : array();
        }

        // Create correction document
        $correct_reason = clone $this;
        $correct_reason->unsetProperty('id', true);
        $correct_reason->unsetProperty('num', true);
        $correct_reason->unsetProperty('date_of_payment', true);
        $correct_reason->unsetProperty('payment_status', true);
        $correct_reason->unsetProperty('new_handovered_status', true);
        $correct_reason->unsetProperty('distributed', true);
        $correct_reason->set('parent_type', $incomes_reason->get('type'), true);
        $correct_reason->set('type', PH_FINANCE_TYPE_CORRECT_REASON, true);
        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' WHERE parent_id = ' . PH_FINANCE_TYPE_CORRECT_REASON;
        $correct_reason->set('type_name', $db->GetOne($query), true);
        $correct_reason->set('status', 'finished', true);
        $correct_reason->set('link_to', $incomes_reason->get('id'), true);
        $correct_reason->getGT2Vars();
        unset($correct_reason->counter);

        // set name to an empty string (it will be set automatically before save using type name and parent name)
        $correct_reason->set('parent_name', $incomes_reason->get('name'), true);
        $correct_reason->set('name', '', true);
        // set group and department from type settings
        $correct_reason->setGroup($correct_reason->get('group'));
        $correct_reason->setDepartment($correct_reason->get('department'));
        $correct_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);

        if ($request->get('issue_correct') && $credit && $credit->get('id')) {
            $correct_reason->set('cd_reason', $credit->get('id'), true);
        }
        $correct_gt2 = $correct_reason->get('grouping_table_2');

        // add all credit rows to the correction document
        $correct_gt2['values'] = array();
        if (!empty($added['credit'])) {
            $gt2 = $credit->get('grouping_table_2');
            $correct_gt2['values'] += $gt2['values'];
        }

        // add all debit rows to the correction document
        if (!empty($added['debit'])) {
            $gt2 = $debit->get('grouping_table_2');
            $correct_gt2['values'] += $gt2['values'];
        }

        // edit the incomes reason rows
        $correct_changes = array();
        foreach ($correct_gt2['values'] as $row => $values) {

            $depend = preg_split('#\s*=>\s*#', $relations[$row]);
            $reason_row = $depend[count($depend)-1];
            //reason_row == 0 means that the row is added
            //so leave it with the same id
            if ($reason_row != 0) {
                //row is changed
                unset($correct_gt2['values'][$row]);
                $correct_gt2['values'][$reason_row] = $values;
            }
            if (empty($changes[$depend[0]])) {
                $change = 'added';
            } else {
                $change = array_shift($changes[$depend[0]]);
            }
            if (isset($reason_gt2['values'][$reason_row])) {
                // get what is changed in this row
                // IMPORTANT!!! If complicate transformation is made
                // the next few lines of code have to be reviewed
                // for correct operations
                if (!$request->get('issue_correct') && $change == 'quantity') {
                    // IMPORTANT!!! the user chose not to issue correction document
                    // We can do this only if we have to decrease quantity for a row
                    // or we have not invoiced quantity in the incomes reason
                    // and want do increase the quantity in the invoice(with debit note)
                    // not more than this not invoiced quantity
                    // in any other case (!!! IMPORTANT !!!) we FORCE the issue of the correction document
                    // for the quantities not enough(for debit note) or any price changes
                    if ($values['quantity'] < 0) {
                        //credit note
                        unset($correct_gt2['values'][$reason_row]);
                        continue;
                    }
                    if ($values['quantity'] > 0) {
                        //debit note - we need to check what we have not invoiced
                        if (!empty($not_invoiced[$reason_row]['quantity'])) {
                            if ($not_invoiced[$reason_row]['quantity'] < $values['quantity']) {
                                // we have to issue correction row for the difference
                                // between not invoiced value and new value
                                $correct_gt2['values'][$reason_row]['quantity'] = $values['quantity'] - $not_invoiced[$reason_row]['quantity'];
                                // increase the quantity in the incomes reason too
                                $reason_gt2['values'][$reason_row]['quantity'] += $correct_gt2['values'][$reason_row]['quantity'];
                            } else {
                                //we have enough quantity not invoiced so exclude this row from the debit notice
                                unset($correct_gt2['values'][$reason_row]);
                                continue;
                            }
                        } else {
                            // all the quantity for this row is invoiced
                            // so add the difference to the reason
                            $reason_gt2['values'][$reason_row][$change] += $values[$change];
                            if (preg_match('#discount|surplus#', $change)) {
                                $reason_gt2['values'][$reason_row]['discount_surplus_field'] = $values['discount_surplus_field'];
                            }
                        }
                    }
                } elseif ($change == 'deleted') {
                    // get the gt2 row from the invoice and match the quantities
                    $invoice_grouping_tbl = $this->get('grouping_table_2');
                    if (!empty($invoice_grouping_tbl['values'][$depend[0]]) && ($invoice_grouping_tbl['values'][$depend[0]]['quantity'] != $reason_gt2['values'][$reason_row]['quantity'])) {
                        // if the quantities does not match then the row in the reason has to be updated
                        $reason_gt2['values'][$reason_row]['quantity'] = $reason_gt2['values'][$reason_row]['quantity'] - $invoice_grouping_tbl['values'][$depend[0]]['quantity'];
                    } else {
                        // if the quantities match then the row in the reason has to be deleted
                        unset($reason_gt2['values'][$reason_row]);
                    }
                } elseif ($change == 'added') {
                    //new row has been added to the invoice
                    $reason_gt2['values'][] = $values;
                } else {
                    $reason_gt2['values'][$reason_row][$change] += $values[$change];
                    if (preg_match('#discount|surplus#', $change)) {
                        $reason_gt2['values'][$reason_row]['discount_surplus_field'] = $values['discount_surplus_field'];
                    } elseif ($reason_gt2['values'][$reason_row][$change] == 0) {
                        unset($reason_gt2['values'][$reason_row]);
                        $correct_changes[$reason_row] = $reason_row . ' => deleted';
                    }
                }
                //set what is changed - quantity, price or last_delivery_price
                $correct_changes[$reason_row] = $reason_row . ' => ' . $change;
            } elseif (!preg_match('#discount|surplus#', $change)) {
                //new row has been added to the invoice
                $reason_gt2['values'][] = $values;
            }
        }

        if (!empty($correct_gt2['values'])) {

            $incomes_reason->unsanitize();

            // remove distribution data of incomes reason
            if ($incomes_reason->get('distributed') == PH_FINANCE_DISTRIBUTION_YES) {
                $incomes_reason->deleteDistribution();
                $incomes_reason->set('distributed', PH_FINANCE_DISTRIBUTION_NO, true);
            }

            $incomes_reason->set('grouping_table_2', $reason_gt2, true);
            $incomes_reason->calculateGT2();
            $incomes_reason->set('table_values_are_set', true, true);
            //set old properties
            $incomes_reason->set('import_customer_name', $incomes_reason->get('customer_name'), true);
            $incomes_reason->slashesEscape();
            //edit incomes reason
            if ($incomes_reason->edit()) {
                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'addcorrect',
                                                                                'new_model' => $incomes_reason,
                                                                                'old_model' => $old_incomes_reason
                                                                          ));
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_quantity_change_success',
                                                        array('reason_type_name' => $incomes_reason->getModelTypeName())));
                if ($old_incomes_reason->get('distributed') == PH_FINANCE_DISTRIBUTION_YES) {
                    $url = sprintf('<a target="_blank" href="%s?%s=%s&amp;%s=%s&amp;%s=distribute&amp;distribute=%d">%s</a>',
                                   $_SERVER['PHP_SELF'],
                                   $this->registry['module_param'], 'finance',
                                   $this->registry['controller_param'], 'incomes_reasons',
                                   'incomes_reasons', $incomes_reason->get('id'),
                                   $incomes_reason->getModelTypeName());
                    $this->registry['messages']->setWarning($this->i18n('warning_finance_incomes_reasons_distribution_deleted',
                                                            array('reason_type_name' => $url)));
                }
            }

            //prepare the correction document
            $reason_gt2 = $incomes_reason->get('grouping_table_2');
            $old_reason_gt2 = $old_incomes_reason->get('grouping_table_2');
            if (empty($reason_gt2['rows'])) {
                $reason_gt2['rows'] = array();
            }
            if (empty($old_reason_gt2['rows'])) {
                $old_reason_gt2['rows'] = array();
            }
            $new_rows = array_diff($reason_gt2['rows'], $old_reason_gt2['rows']);

            $update_debit_changes = array();
            foreach ($correct_gt2['values'] as $row => $values) {
                if (!in_array($row, $old_reason_gt2['rows'])) {
                    unset($correct_gt2['values'][$row]);
                    $new_row = array_shift($new_rows);
                    //set new rows in changes
                    $correct_gt2['values'][$new_row] = $values;
                    $correct_changes[$new_row] = $update_debit_changes[$new_row] = $new_row . ' => added';
                }
            }

            //get information about what we can issue with handover
            $old_incomes_reason->checkAddingHandover('outgoing', true);
            $handover_gt2 = $old_incomes_reason->get('grouping_table_2');
            $handover_gt2 = !empty($handover_gt2['values']) ? $handover_gt2['values'] : array();

            $erred_articles = $articles = array();
            foreach ($correct_gt2['values'] as $row => $values) {
                $articles[] = $values['article_id'];
            }
            if (!empty($articles)) {
                $query = 'SELECT id, subtype FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE id IN (' . implode(', ', $articles) . ')';
                $articles = $db->GetAssoc($query);
            }
            foreach ($correct_gt2['values'] as $row => $values) {
                //for each row which we decrease quantity for (negative quantity)
                //we have to check if we have quantities not received
                //from the customer with handovers
                //!!! ONLY FOR COMMODITY ARTICLES
                if (isset($articles[$values['article_id']]) && $articles[$values['article_id']] == 'commodity' && $values['quantity'] < 0 &&
                isset($old_reason_gt2['values'][$row]['quantity']) &&
                (empty($handover_gt2[$row]['quantity']) ||
                $handover_gt2[$row]['quantity'] < abs($values['quantity']))) {
                    //calculate the minimal quantity we can choose
                    $minimal = $old_reason_gt2['values'][$row]['quantity'];
                    if (!empty($handover_gt2[$row]['quantity'])) {
                        $minimal -= $handover_gt2[$row]['quantity'];
                    }
                    $erred_articles[] = $values['article_name'] . ': ' . $minimal;
                }
            }

            if (!empty($erred_articles)) {
                //set error message for the erred articles
                $erred_articles = '<br>&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;-&nbsp;', $erred_articles);
                $this->registry['messages']->setError($this->i18n('error_finance_correct_more_in_handover') . $erred_articles);
                $db->FailTrans();
            }

            $correct_reason->set('grouping_table_2', $correct_gt2, true);
            $correct_reason->calculateGT2();
            $correct_reason->set('table_values_are_set', true, true);
            //set old customer name
            $correct_reason->set('import_customer_name', $incomes_reason->get('customer_name'), true);
            $correct_reason->set('trademark_name', $incomes_reason->get('trademark_name'), true);
            $correct_reason->set('rows_changes', $correct_changes, true);
            $correct_reason->slashesEscape();
            // add correct reason
            if ($correct_reason->add()) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addcorrect_success'));
            }

            //update rows that have been added in the changes for the debit note
            //in reasons relatives table
            if (!empty($update_debit_changes) && !empty($debit)) {
                $update_debit_changes = '\n' . implode('\n', $update_debit_changes);
                $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                         'SET changes = CONCAT(changes, "' . $update_debit_changes . '")' . "\n" .
                         'WHERE parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                         '    AND link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                         '    AND link_to = ' . $this->get('id') . "\n" .
                         '    AND parent_id = ' . $debit->get('id');
                $db->Execute($query);

            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;

    }

    /**
     * Get incomes reason relative to this document (invoice, correct reason, proforma invoice).
     * Intended to be used on secondary financial documents for finding id of
     * primary financial documents they were created from.
     *
     * @return int - id of incomes reason relative
     */
    public function getLinkTo() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $link_to = '';

        $query = 'SELECT frr.link_to, frr.link_to_model_name' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason" ' . "\n" .
                 '  AND fir.annulled_by=0 AND fir.type > ' . PH_FINANCE_TYPE_MAX . "\n" .
                 //'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                 //'  ON frr.link_to = co.id AND frr.link_to_model_name = "Contract" ' . "\n" .
                 'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                 'LIMIT 1';
        $reason = $this->registry['db']->GetRow($query);

        if (!empty($reason) && $reason['link_to_model_name'] == 'Finance_Incomes_Reason') {
            $link_to = $reason['link_to'];
            $this->set('link_to', $link_to, true);
            $this->set('link_to_model_name', $reason['link_to_model_name'], true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $link_to;
    }

    /**
     * Checks permitted layouts
     *
     * (non-PHPdoc)
     * @see _libs/inc/mvc/Model::getPermittedLayouts()
     * @param string $mode - action name
     * @param string $model - not used in overwrite method
     * @return array - 'view' layouts or 'edit' layouts or array of both
     */
    public function getPermittedLayouts($mode = '', $model = '') {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        //if validLogin or automation user for crontab
        if ($this->registry['validLogin'] || ($this->registry['currentUser'] && $this->registry['currentUser']->get('id') == PH_AUTOMATION_USER)) {
            if (!$this->isDefined('layouts_view')) {
                $groups = $this->registry['currentUser']->getGroups();
                if (count($groups)) {
                    // check if current user is assigned as observer of records of this type
                    $module = 'finance';
                    $set_observer =
                        in_array('observer', $this->registry['config']->getParamAsArray($module, 'assignment_types_' . $this->get('type'))) &&
                        $this->registry['currentUser']->getPersonalSettings($module, 'set_observer');

                    // make sure model has all types of assignments
                    if ($this->get('id')) {
                        if (!$this->isDefined('assignments_responsible')) {
                            $this->getAssignments('responsible');
                        }
                        if (!$this->isDefined('assignments_decision')) {
                            $this->getAssignments('decision');
                        }
                        if (!$this->isDefined('assignments_observer')) {
                            $this->getAssignments('observer');
                        }
                        if (!$this->isDefined('assignments_owner')) {
                            $this->getAssignments();
                        }
                    }

                    //get rights for layouts view
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="view")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval($this->get('type')) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="view"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_view_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_view = array();
                    foreach ($layouts_view_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_view[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_view[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_view[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_view', $layouts_view);

                    //get rights for layouts edit
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="edit")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval($this->get('type')) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="edit"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_edit_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_edit = array();
                    foreach ($layouts_edit_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_edit[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_edit[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_edit[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_edit', $layouts_edit);
                } else {
                    $this->set('layouts_view', array());
                    $this->set('layouts_edit', array());
                }
            }
        } else {
            $this->set('layouts_view', array());
            $this->set('layouts_edit', array());
        }

        if ($sanitized) {
            $this->sanitize();
        }

        if ($mode) {
            return $this->get('layouts_' . $mode);
        } else {
            return array($this->get('layouts_view'), $this->get('layouts_edit'));
        }
    }

    /**
     * Saves data related to the transformation made
     * from document to incomes reason
     *
     * @return bool - result of the operation
     */
    public function saveTransformationDetails() {

        $params = unserialize(General::slashesStrip($this->get('transform_params')));

        $destination_params = array(
            'origin_method' => !empty($params['origin_method']) ? $params['origin_method'] : '',
            'origin_method_id' => !empty($params['origin_method_id']) ? $params['origin_method_id'] : '',
            'destination_model' => $this->modelName,
            'destination_id' => $this->get('id'),
            'destination_full_num' => $this->get('num'),
            'destination_name' => $this->get('name') ?: $this->get('type_name'),
        );

        // if the transformation has been triggered via report it is possible creation of relation
        // multiple model gt2 rows to the current
        // So all the relations have to be saved.
        if (!empty($params['transform_from_report']) && !empty($params['origin_id']) && is_array($params['origin_id'])) {

            // this array is used to prevent the duplicated unique keys during the add of the relations
            $unique_keys = array();

            foreach ($params['origin_id'] as $key => $origin_id) {
                $unique_key = sprintf('%s-%d-%s-%d', $this->modelName, $this->get('id'), $params['origin_model'][$key], $origin_id);
                if (!in_array($unique_key, $unique_keys)) {
                    $unique_keys[] = $unique_key;
                    $set = array();

                    $set['parent_id'] = sprintf('`parent_id` = "%d"', $this->get('id'));
                    $set['parent_model_name'] = sprintf('`parent_model_name` = "%s"', $this->modelName);
                    $set['link_to'] = sprintf('`link_to` = "%d"', $origin_id);
                    $set['link_to_model_name'] = sprintf('`link_to_model_name` = "%s"', $params['origin_model'][$key]);

                    $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . " SET \n" . implode(",\n", $set);

                    $this->registry['db']->Execute($query);

                    // save history for each source model
                    $factory_name = General::singular2plural($params['origin_model'][$key]);
                    $module = strtolower($factory_name);
                    if (strpos($module, '_') !== false) {
                        list($module, $controller) = explode('_', $module, 2);
                    } else {
                        $controller = $module;
                    }

                    require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.factory.php';

                    $alias = $factory_name::getAlias($module, $controller);

                    $filters = array('where' => array($alias . '.id = ' . $origin_id));

                    $model = $factory_name::searchOne($this->registry, $filters);
                    if (!$model) {
                        continue;
                    }

                    $model->set('destination_params', $destination_params, true);

                    $this->loadI18NFiles(PH_MODULES_DIR . $module . '/i18n/' . $this->registry['lang'] . '/' . $module . '.ini');
                    require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.history.php';
                    $history_name = $factory_name . '_History';

                    $history_name::saveData(
                        $this->registry,
                        array(
                            'action_type' => 'create',
                            'model' => $model,
                            'old_model' => $model,
                            'new_model' => $model
                        ));
                }
            }
            if ($this->registry['db']->ErrorMsg()) {
                $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
                return false;
            }
            $this->set('link_to', $params['origin_id'], true);
            $this->set('origin_gt2_relations', $params['origin_gt2_relations'], true);
            return true;
        }

        // transformation from a single parent
        $this->set('link_to', $params['origin_id'], true);
        $this->set('link_to_model_name', $params['origin_model'], true);

        // If it's not set to skip the relatives
        if (empty($params['skip_relatives'])) {
            // Make a relation
            $set['parent_id'] = sprintf('`parent_id` = "%d"', $this->get('id'));
            $set['parent_model_name'] = sprintf('`parent_model_name` = "%s"', $this->modelName);
            $set['link_to'] = sprintf('`link_to` = "%d"', $params['origin_id']);
            $set['link_to_model_name'] = sprintf('`link_to_model_name` = "%s"', $params['origin_model']);

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . " SET \n" . implode(",\n", $set);

            $this->registry['db']->Execute($query);
        }

        // save history for source model
        $factory_name = General::singular2plural($params['origin_model']);
        $module = strtolower($factory_name);
        if (strpos($module, '_') !== false) {
            list($module, $controller) = explode('_', $module, 2);
        } else {
            $controller = $module;
        }

        require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.factory.php';

        $alias = $factory_name::getAlias($module, $controller);

        $filters = array('where' => array($alias . '.id = ' . $params['origin_id']));

        $model = $factory_name::searchOne($this->registry, $filters);
        if (!$model) {
            return false;
        }

        $old_model = clone $model;
        $old_model->sanitize();

        $model->set('destination_params', $destination_params, true);

        $this->loadI18NFiles(PH_MODULES_DIR . $module . '/i18n/' . $this->registry['lang'] . '/' . $module . '.ini');
        require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.history.php';
        $history_name = $factory_name . '_History';

        $history_name::saveData(
            $this->registry,
            array(
                'action_type' => 'create',
                'model' => $model,
                'old_model' => $old_model,
                'new_model' => $model
            ));

        // change status of source model
        if (!empty($params['origin_status'])) {

            $model->set('status', $params['origin_status'], true);
            $model->set('substatus', (isset($params['origin_substatus']) ? $params['origin_substatus'] : ''), true);

            if (!$model->setStatus()) {
                return false;
            } else {
                $model = $factory_name::searchOne($this->registry, $filters);
                $model->sanitize();

                $history_name::saveData(
                    $this->registry,
                    array(
                        'action_type' => 'status',
                        'model' => $model,
                        'old_model' => $old_model,
                        'new_model' => $model
                    ));
            }
        }

        if ($this->registry['db']->ErrorMsg()) {
            $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
            return false;
        }

        return true;
    }

    /**
     * update GT2 row links for transformations
     *
     * @return bool - result of the operation
     */
    public function updateRowsLinks() {
        // If it's set to skip relatives
        $params = unserialize(General::slashesStrip($this->get('transform_params')));
        if (!empty($params['skip_relatives'])) {
            // Skip updating rows links
            // (there will be no record to update anyway,
            // because it will be skipped earlier)
            return true;
        }

        $new_rows_links = $this->get('rows_links');
        if (!empty($new_rows_links['added'])) {
            if ($this->get('origin_gt2_relations')) {
                // if the parameter is set then the model has been made via report
                //and the relations have to be established
                $update_structured = array();
                $origin_gt2_relations = $this->get('origin_gt2_relations');

                // forms the array which contains the model and model id for a key and all the gt2 rows for it
                foreach ($new_rows_links['added'] as $current_model_row => $parent_model_row) {
                    if ($parent_model_row && isset($origin_gt2_relations[$parent_model_row])) {
                        $gt2_parent_row_info = $origin_gt2_relations[$parent_model_row];
                        $key = $gt2_parent_row_info['model'] . '|' . $gt2_parent_row_info['model_id'];
                        if (!isset($update_structured[$key])) {
                            $update_structured[$key] = array();
                        }

                        $update_structured[$key][] = $current_model_row . ' => ' . $parent_model_row;
                    }
                }

                // write the rows_links in the table
                foreach ($update_structured as $key_data => $rows_relations) {
                    list($link_to_model_name, $link_to) = explode('|', $key_data);
                    if ($link_to_model_name && $link_to) {
                        $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                                 ' SET rows_links = \'' . implode("\n", $rows_relations) . '\'' . "\n" .
                                 ' WHERE parent_id = ' . $this->get('id') . "\n" .
                                 ' AND parent_model_name = "' . $this->modelName . '"' . "\n" .
                                 ' AND link_to = ' . $link_to . "\n" .
                                 ' AND link_to_model_name = "' . $link_to_model_name . '"' . "\n" ;
                        $this->registry['db']->Execute($query);
                    }
                }
            } else {
                // default case - 1:1
                foreach ($new_rows_links['added'] as $new_row => $old_row) {
                    $new_rows_links['added'][$new_row] = $new_row . ' => ' . $old_row;
                }
                if ($this->get('link_to_model_name')) {
                    $link_to_model_name = $this->get('link_to_model_name');
                } else {
                    $link_to_model_name = "Finance_Incomes_Reason";
                }
                $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                         ' SET rows_links = \'' . implode("\n", $new_rows_links['added']) . '\'' . "\n" .
                         ' WHERE parent_id = ' . $this->get('id') . "\n" .
                         ' AND parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                         ' AND link_to = ' . $this->get('link_to') . "\n" .
                         ' AND link_to_model_name = "' . $link_to_model_name . '"' . "\n" ;
                $this->registry['db']->Execute($query);
            }

            if ($this->registry['db']->ErrorMsg()) {
                $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
                return false;
            }
        }

        return true;
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'finance_incomes_reasons', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Finance_Incomes_Reason->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Income ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        // do not allow any other actions for deactivated models
        if (!$this->isActivated() && $this->origin == 'database' && !in_array($action, array('add', 'view', 'history', 'relatives', 'activate', 'deactivate', 'system_settings_active', 'system_settings_group', 'system_settings_portal'))) {
            return false;
        }

        //special conditions for the clone action
        if ($action == 'clone' && $this->get('type') < PH_FINANCE_TYPE_MAX) {
            //only user defined types of income reasons could be cloned
            return false;
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'locked') {
                //locked status
                switch ($action) {
                //forbidden actions
                case 'edit':
                case 'addinvoice':
                case 'addproformainvoice':
                case 'advances':
                case 'proformaadvances':
                case 'addcorrect':
                case 'addcreditdebit':
                case 'addhandover':
                case 'payments':
                case 'distribute':
                    return false;
                    break;
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } elseif ($this->get('status') == 'finished') {
                //finished status
                switch ($action) {
                //forbidden actions
                case 'edit':
                case 'observer':
                    return false;
                    break;
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } else {
                //opened status
                switch ($action) {
                //forbidden actions
                case 'addinvoice':
                case 'addproformainvoice':
                case 'advances':
                case 'proformaadvances':
                case 'addcorrect':
                case 'addcreditdebit':
                case 'addhandover':
                case 'payments':
                case 'distribute':
                    return false;
                    break;
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            }
        } else {
            return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                $this,
                $action,
                $modulePermissionsKey,
                $force
            );
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Check the companies info to see if the current user have permissions for at least one bank account/cashbox
     *
     * @return bool - true if allowed, otherwise false
     */
    public function checkAddPaymentPermissions() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        // check the allowed bank accounts and cashboxes for the slelected company
        require_once(PH_MODULES_DIR . 'finance/models/finance.dropdown.php');
        $params = array($this->registry,
                        'lang'              => $this->get('model_lang'),
                        'company_id'        => ($this->get('company') ? $this->get('company') : array(0)),
                        'payment_direction' => ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE ? 'expenses' : 'incomes'),
                        'active'            => 1);
        $companies_data = Finance_Dropdown::getCompaniesData($params);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return count($companies_data);
    }

    /**
     * Check for additional conditions before allowing (de)activation of model
     *
     * @param string $status - activate or deactivate
     * @return boolean - true - action is allowed, false - not allowed
     */
    public function checkChangeActivePermissions($status) {

        $action_allowed = true;

        if ($status == 'deactivate') {
            if ($this->get('type') < PH_FINANCE_TYPE_MAX && $this->get('status') == 'finished' || $this->get('annulled_by')) {
                $action_allowed = false;
            } elseif ($this->get('type') > PH_FINANCE_TYPE_MAX && $this->get('status') == 'finished') {
                // use filtering to get only what looking for
                $filters_fir = array('fir.annulled_by = 0',
                                     'fir.active = 1');
                $filters_fwd = array('fwd.annulled_by = 0',
                                     'fwd.active = 1');
                $this->getRelatives(array('get_invoices' => $filters_fir,
                                          'get_pro_invoices' => $filters_fir,
                                          'get_handovers' => $filters_fwd,
                                          'get_commodity_reservations' => array_merge($filters_fwd, array('fwd.status != \'finished\''))));

                // if there are any related financial or warehouse documents
                // or there are distributed payments/expenses in balance
                // or document is included in a repayment plan
                if ($this->get('invoices') || $this->get('pro_invoices') ||
                $this->get('handovers') ||$this->get('commodity_reservations') ||
                $this->getPaidAmount() || $this->checkRepaymentPlans(array('any_status' => true))) {
                    $this->unsetProperty('invoices', true);
                    $this->unsetProperty('pro_invoices', true);
                    $this->unsetProperty('handovers', true);
                    $this->unsetProperty('commodity_reservations', true);
                    $action_allowed = false;
                }
            }
        } elseif ($status == 'activate') {
            // deactivated correction document cannot be directly activated
            // deactivated invoice is useless (it has not GT2 rows) so activation is not allowed
            if (in_array($this->get('type'), array(PH_FINANCE_TYPE_CORRECT_REASON, PH_FINANCE_TYPE_INVOICE)) && !$this->isActivated()) {
                $action_allowed = false;
            }
        }

        // get correction documents for reason because they should be (de)activated as well
        if ($action_allowed && $this->get('type') > PH_FINANCE_TYPE_MAX && $this->get('status') == 'finished') {
            $filters_fir = array('fir.active IS NOT NULL');
            $this->getRelatives(array('get_correct_reasons' => $filters_fir));
        }

        return $action_allowed;
    }

    /**
     * Checks permission for nopay action
     *
     * @return bool - true - accessible, false - inaccessible
     */
    public function checkNopayPermission() {

        $right = false;
        if (!$this->get('annulled_by')) {
            if ($this->get('status') == 'finished' && $this->checkPermissions('nopay') && in_array($this->get('payment_status'), array('unpaid', 'nopay'))) {
                if ($this->get('type') == PH_FINANCE_TYPE_INVOICE) {
                    //check whether the debit/credit notices have the permission for nopay
                    $right = true;
                    $this->getRelatives(array('get_credit_notices' => true, 'get_debit_notices' => true));
                    if ($this->get('credit_notices')) {
                        foreach ($this->get('credit_notices') as $credit_notice) {
                            if ($credit_notice->get('payment_status') == 'partial' || $credit_notice->get('payment_status') == 'paid') {
                                //the invoice should not be marked with nopay payment status if any of its credit notices is partially or fully paid
                                $right = false;
                                break;
                            }
                        }
                    }

                    if ($right && $this->get('debit_notices')) {
                        foreach ($this->get('debit_notices') as $debit_notice) {
                            if ($debit_notice->get('payment_status') == 'partial' || $debit_notice->get('payment_status') == 'paid') {
                                //the invoice should not be marked with nopay payment status if any of its debit notices is partially or fully paid
                                $right = false;
                                break;
                            }
                        }
                    }
                } elseif (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE))) {
                    //true for credit/debit notices
                    $right = true;
                } elseif ($this->get('type') > PH_FINANCE_TYPE_MAX && $this->getInvoicedAmount() == 0) {
                    //there are no invoices for this reason
                    $sanitize_after = false;
                    if ($this->sanitized) {
                        $sanitize_after = true;
                        $this->unsanitize();
                    }
                    //check type settings for nopay
                    require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                    $financeType = Finance_Documents_Types::searchOne($this->registry, array(
                                                'where' => array('fdt.id=' . $this->get('type'))));
                    if ($financeType->get('nopay')) {
                        $right = true;
                    }

                    if ($sanitize_after) {
                        $this->sanitize();
                    }
                }
            }
        }

        return $right;
    }

    /**
     * Change payment status to nopay or unpaid
     *
     * @return mixed - old payment status on success or false
     */
    public function changeNopayStatus() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        $result = false;
        $new_payment_status = '';
        $current_payment_status = $this->get('payment_status');
        if ($current_payment_status == 'nopay') {
            $new_payment_status = 'unpaid';
        } elseif ($current_payment_status == 'unpaid') {
            $new_payment_status = 'nopay';
        }
        if ($new_payment_status) {
            $set = array();
            $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
            $set['modified']       = sprintf("modified=now()");
            $set['payment_status'] = sprintf("`payment_status`='%s'", $new_payment_status);
            $set['payment_status_modified'] = sprintf("payment_status_modified=NOW()");

            //query to update payment status
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                     'SET ' . implode(', ', $set) . "\n" .
                     'WHERE id=' . $this->get('id');
            $this->registry['db']->Execute($query);

            if ($this->registry['db']->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $result = false;
            } else {
                $result = $current_payment_status;
            }
        }
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Updates payment status of proforma when invoice has been issued from it
     * or when invoice has been annulled.
     *
     * @return boolean - result of the operation
     */
    public function updateProformaPaymentStatus() {
        if (!$this->get('id') || $this->get('type') != PH_FINANCE_TYPE_INVOICE) {
            return true;
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        $query = 'SELECT fir2.id AS proforma_id, fir.annulled_by AS invoice_annulled' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 '  ON frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir2' . "\n" .
                 '  ON frr.link_to_model_name="Finance_Incomes_Reason" AND frr.link_to=fir2.id' . "\n" .
                 'WHERE fir.type=' . PH_FINANCE_TYPE_INVOICE . "\n" .
                 '  AND fir.id=' . $this->get('id') . "\n" .
                 '  AND fir2.type=' . PH_FINANCE_TYPE_PRO_INVOICE;
        $result = $db->GetRow($query);

        if (!empty($result)) {
            $proforma_id = $result['proforma_id'];
            $proforma_payment_status = $result['invoice_annulled'] ? 'unpaid' : 'invoiced';

            $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                     'SET payment_status="' . $proforma_payment_status . '" ' . "\n" .
                     'WHERE id=' . $proforma_id;
            $db->Execute($query);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Checks whether a finance document could be printed/generated with defined pattern or not
     * IMPORTANT: This check should not be used anymore (Bug 4366, comment 3)
     *
     * @param int $pattern - the pattern id
     * @return $result bool - true - allowed, false - forbidden
     */
    public function checkPrintPattern($pattern) {

        // IMPORTANT: This check should not be used anymore (Bug 4366, comment 3)
        return true;

        $result = true;

        if ($this->get('type') == PH_FINANCE_TYPE_INVOICE || $this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            //this special check is only for invoices

            //get the content of the pattern
            $sanitize_after = false;
            if ($this->sanitized) {
                $sanitize_after = true;
                $this->unsanitize();
            }

            $query = 'SELECT content FROM ' . DB_TABLE_PATTERNS_I18N . "\n" .
                     'WHERE parent_id=' . $pattern . ' AND lang="' . $this->get('model_lang') . '"';
            $content = $this->registry['db']->GetOne($query);

            //check if the pattern contains printform or not
            if (preg_match('#\[.._grouping_table_2_printform\]#', $content)) {
                //the pattern contains printform

                //now check the history if the invoice has been printed with a pattern with NO PRINTFORM
                $query = 'SELECT count(h_id) FROM ' . DB_TABLE_FINANCE_HISTORY . "\n" .
                         'WHERE model="Finance_Incomes_Reason"' . "\n" .
                         ' AND (action_type="print" OR action_type="generate")' . "\n" .
                         ' AND data LIKE \'%s:9:"printform";i:0;%\'' . "\n" .
                         ' AND model_id=' . $this->get('id');
                $used = $this->registry['db']->GetOne($query);
                if ($used > 0) {
                    $result = false;
                }
            } elseif (preg_match('#\[.._grouping_table_2\]#', $content)) {
                //the pattern DOES NOT contain printform!

                //now check the history if the invoice has been printed with a pattern with PRINTFORM
                $query = 'SELECT count(h_id) FROM ' . DB_TABLE_FINANCE_HISTORY . "\n" .
                         'WHERE model="Finance_Incomes_Reason"' . "\n" .
                         ' AND (action_type="print" OR action_type="generate")' . "\n" .
                         ' AND data LIKE \'%s:9:"printform";i:1;%\'' . "\n" .
                         ' AND model_id=' . $this->get('id');
                $used = $this->registry['db']->GetOne($query);
                if ($used > 0) {
                    $result = false;
                }
            }

            if ($sanitize_after) {
                $this->sanitize();
            }

        }

        return $result;
    }

    /**
     * Prepares display properties in list view according to some properties of model
     *
     * @return array - display properties
     */
    public function getBackgroundColor() {
        $background_properties = array();
        if (in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                               PH_FINANCE_TYPE_PRO_INVOICE,
                                               PH_FINANCE_TYPE_DEBIT_NOTICE,
                                               PH_FINANCE_TYPE_CREDIT_NOTICE)) &&
        !$this->get('annulled_by') &&
        $this->get('status') == 'finished' &&
        !in_array($this->get('payment_status'), array('nopay', 'invoiced'))) {
            if ($this->get('payment_status') == 'paid') {
                if ($this->get('advance')) {
                    $background_properties['background_color'] = 'FFC770';
                    $background_properties['definition'] = 'paid_advance';
                } else {
                    $background_properties['background_color'] = 'A5DBA8';
                    $background_properties['definition'] = 'paid_default';
                }
                $background_properties['text_color'] = '000000';
            } else {
                $current_day = strtotime(date('Y-m-d'));
                $date_payment = '';
                // if property is not defined, payment date has not been calculated yet
                if ($this->isDefined('date_of_payment')) {
                    $date_payment = strtotime($this->get('date_of_payment'));
                }

                if ($date_payment && $date_payment < $current_day) {
                    if ($this->get('advance')) {
                        $background_properties['background_color'] = 'FF6F18';
                        $background_properties['definition'] = 'not_paid_advance';
                    } else {
                        $background_properties['background_color'] = 'EFB9B9';
                        $background_properties['definition'] = 'not_paid_default';
                    }
                    $background_properties['text_color'] = '000000';
                } elseif (($date_payment >= $current_day) && ($current_day > $date_payment-(3600*24*3))) {
                    $background_properties['background_color'] = 'FFCA2D';
                    $background_properties['definition'] = 'payment_day_soon';
                    $background_properties['text_color'] = '000000';
                }
            }
        }

        return $background_properties;
    }

    /**
     * Check if revenue document is part of a repayment plan
     *
     * @param array $params - filtering parameters for search
     * @return bool - result of the operation
     */
    public function checkRepaymentPlans($params = array()) {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        $db = $this->registry['db'];

        $query = 'SELECT frpi.parent_id ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . ' AS frpi' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . ' AS frp' . "\n" .
                 '  ON frpi.parent_id=frp.id' . "\n" .
                 'WHERE frpi.incomes_id=' . $this->get('id') . ' AND frp.deleted=0' . "\n" .
                 (empty($params['any_status']) ? '  AND frp.status = "locked"' : '');
        $plan_id = $db->GetOne($query);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $plan_id;
    }

    /*
     * Function for taking the related records from first level
     * It used from custom outlooks and front page dashlets.
     *
     * @param string $relation - relation type may be 'child' or 'parent'
     * @return array $relatives - all related records from first level
     */
    public function getFirstLevelRelatedIncomesReasons($relation) {
        $this->unsanitize();

        // defines the searched fields from the tables
        if ($relation == 'parent') {
            $related_id     = 'link_to';
            $related_model  = 'link_to_model_name';

            $current_id_field    = 'parent_id';
            $current_model_field = 'parent_model_name';
        } else {
            $related_id     = 'parent_id';
            $related_model  = 'parent_model_name';

            $current_id_field    = 'link_to';
            $current_model_field = 'link_to_model_name';
        }

        // query to take the related:
        // - incomes reasons
        // - documents
        // - contracts
        // - warehouse documents
        $query = 'SELECT CONCAT(fr.' . $related_model . ', \'^\', fr.' . $related_id . ') AS idx,' . "\n" .
                 'fr.' . $related_model . ' AS related_model, fr.' . $related_id . ' AS related_id,' . "\n" .
                 // documents and contracts can only be parents of incomes reason
                 ($relation == 'parent' ?
                 '  d.full_num AS d_num, dt.direction AS d_direction, di18n.name AS d_name, dti18n.name AS d_type_name, ' . "\n" .
                 '  co.num AS co_num, coi18n.name AS co_name, coti18n.name AS co_type_name, ' . "\n" :
                 '') .
                 // warehouse documents can only be children of incomes reason
                 ($relation == 'child' ?
                 '  fwd.num AS fwd_num, fwdi18n.name AS fwd_name, fwd_type.name AS fwd_type_name, fwd.annulled_by AS fwd_annulled_by, ' . "\n" :
                 '') .
                 '  fir.num AS fir_num, firi18n.name AS fir_name, fir_type.name AS fir_type_name, fir.annulled_by AS fir_annulled_by ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS fr' . "\n" .
                 ($relation == 'parent' ?
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                 '  ON (fr.' . $related_id . '=d.id AND fr.' . $related_model . '="Document")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                 '  ON (di18n.parent_id=d.id AND di18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES . ' AS dt' . "\n" .
                 '  ON (d.type=dt.id AND dt.active=1 AND dt.deleted=0)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                 '  ON (dti18n.parent_id=d.type AND dti18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                 '  ON (fr.' . $related_id . '=co.id AND fr.' . $related_model . '="Contract")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n' . "\n" .
                 '  ON (coi18n.parent_id=co.id AND coi18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS coti18n' . "\n" .
                 '  ON (coti18n.parent_id=co.type AND coti18n.lang="' . $this->get('model_lang') . '")' . "\n" :
                 '') .
                 ($relation == 'child' ?
                 'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                 '  ON (fr.' . $related_id . '=fwd.id AND fr.' . $related_model . '="Finance_Warehouses_Document")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS_I18N . ' AS fwdi18n' . "\n" .
                 '  ON (fwdi18n.parent_id=fwd.id AND fwdi18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fwd_type' . "\n" .
                 '  ON (fwd_type.parent_id=fwd.type AND fwd_type.lang="' . $this->get('model_lang') . '")' . "\n" :
                 '') .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON (fr.' . $related_id . '=fir.id AND fr.' . $related_model . '="Finance_Incomes_Reason")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
                 '  ON (firi18n.parent_id=fir.id AND firi18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fir_type' . "\n" .
                 '  ON (fir.type=fir_type.parent_id AND fir_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'WHERE fr.' . $current_id_field . '="' . $this->get('id') . '" AND fr.' . $current_model_field . '="Finance_Incomes_Reason"' . "\n" .
                 '  AND fr.' . $related_model . ' IN ("Finance_Incomes_Reason", ' . ($relation == 'parent' ? '"Document", "Contract"' : '"Finance_Warehouses_Document"') . ')';

        $records = $this->registry['db']->GetAssoc($query);

        if ($relation == 'child' && $this->get('type') != PH_FINANCE_TYPE_CREDIT_NOTICE || $relation == 'parent' && $this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
            // defines the searched fields from the tables
            if ($relation == 'parent') {
                $related_id     = 'paid_to';
                $related_model  = 'paid_to_model_name';

                $current_id_field    = 'parent_id';
                $current_model_field = 'parent_model_name';
            } else {
                $related_id     = 'parent_id';
                $related_model  = 'parent_model_name';

                $current_id_field    = 'paid_to';
                $current_model_field = 'paid_to_model_name';
            }

            // if the relation is for child and model is not a credit note, balance relations are taken as well:
            // - payments
            // - credit notes (which are incomes reasons)
            // - expenses reasons
            // OR if the relation is for parent and model is a credit note, balance relations are taken as well:
            // - outgoing payments
            // - invoices or debit notes
            // - incoming credit notes
            $query_2 = 'SELECT CONCAT(fr.' . $related_model . ', \'^\', fr.' . $related_id . ') AS idx,' . "\n" .
                       '  fr.' . $related_model . ' AS related_model, fr.' . $related_id . ' AS related_id, ' . "\n" .
                       '  fp.num AS fp_num, fp_type.name AS fp_type_name, fp.annulled_by AS fp_annulled_by, ' . "\n" .
                       '  fir.num AS fir_num, firi18n.name AS fir_name, fir_type.name AS fir_type_name, fir.annulled_by AS fir_annulled_by, ' . "\n" .
                       '  IF(fer.invoice_num!="", CONCAT(fer.num, " (", fer.invoice_num, ")"), fer.num) AS fer_num, feri18n.name AS fer_name, fer_type.name AS fer_type_name, fer.annulled_by AS fer_annulled_by ' . "\n" .
                       'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                       '  ON (fr.' . $related_id . '=fp.id AND fr.' . $related_model . '="Finance_Payment")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_PAYMENTS_TYPES_I18N . ' AS fp_type' . "\n" .
                       '  ON (fp.type=fp_type.parent_id AND fp_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                       '  ON (fr.' . $related_id . '=fir.id AND fr.' . $related_model . '="Finance_Incomes_Reason")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
                       '  ON (firi18n.parent_id=fir.id AND firi18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fir_type' . "\n" .
                       '  ON (fir.type=fir_type.parent_id AND fir_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                       '  ON (fr.' . $related_id . '=fer.id AND fr.' . $related_model . '="Finance_Expenses_Reason")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS_I18N . ' AS feri18n' . "\n" .
                       '  ON (feri18n.parent_id=fer.id AND feri18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fer_type' . "\n" .
                       '  ON (fer.type=fer_type.parent_id AND fer_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                       'WHERE fr.' . $current_id_field . '="' . $this->get('id') . '" AND fr.' . $current_model_field . '="Finance_Incomes_Reason" AND fr.paid_amount!=0';

            $records_payments = $this->registry['db']->GetAssoc($query_2);

            // merges the records from fin_reasons_relatives table and fin_balance table
            // (if there are relations to the same model in both tables, they should be present just once)
            $records = array_merge($records, $records_payments);
        }

        // forms the array with related records
        $relatives = array();
        foreach ($records as $key => $record) {
            if ($record['related_model'] == 'Finance_Incomes_Reason') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'finance',
                    'controller'    => 'incomes_reasons',
                    'id'            => $record['related_id'],
                    'num'           => ($record['fir_num'] ? $record['fir_num'] : $this->i18n('finance_payments_unfinished_payment')),
                    'note'          => $record['fir_name'] . ' (' . $record['fir_type_name'] . ')',
                    'annulled_by'   => $record['fir_annulled_by']
                );
            } else if ($record['related_model'] == 'Finance_Expenses_Reason') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'finance',
                    'controller'    => 'expenses_reasons',
                    'id'            => $record['related_id'],
                    'num'           => $record['fer_num'],
                    'note'          => $record['fer_name'] . ' (' . $record['fer_type_name'] . ')',
                    'annulled_by'   => $record['fer_annulled_by']
                );
            } else if ($record['related_model'] == 'Document') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'documents',
                    'controller'    => 'documents',
                    'id'            => $record['related_id'],
                    'num'           => $record['d_num'],
                    'direction'     => $record['d_direction'],
                    'note'          => $record['d_name'] . ' (' . $record['d_type_name'] . ')',
                    'annulled_by'   => 0
                );
            } else if ($record['related_model'] == 'Contract') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'contracts',
                    'controller'    => 'contracts',
                    'id'            => $record['related_id'],
                    'num'           => $record['co_num'],
                    'note'          => $record['co_name'] . ' (' . $record['co_type_name'] . ')',
                    'annulled_by'   => 0
                );
            } else if ($record['related_model'] == 'Finance_Warehouses_Document') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'finance',
                    'controller'    => 'warehouses_document',
                    'id'            => $record['related_id'],
                    'num'           => ($record['fwd_num'] ? $record['fwd_num'] : $this->i18n('finance_warehouses_unfinished_document')),
                    'note'          => $record['fwd_name'] . ' (' . $record['fwd_type_name'] . ')',
                    'annulled_by'   => $record['fwd_annulled_by']
                );
            } else if ($record['related_model'] == 'Finance_Payment') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'finance',
                    'controller'    => 'payments',
                    'id'            => $record['related_id'],
                    'num'           => $record['fp_num'],
                    'note'          => $record['fp_num'] . ' (' . $record['fp_type_name'] . ')',
                    'annulled_by'   => $record['fp_annulled_by']
                );
            }
        }
        $this->sanitize();

        return $relatives;
    }

    /**
     * Get the amount that can be allocated to current model in balance.
     * There are specifics for reasons and proformas.
     *
     * @return float - remaining amount
     */
    public function getRemainingAmount() {
        //get paid amount
        $this->getPaidAmount();
        //remaining amount is total of the incomes reason without the payments that have already been distributed to this incomes reason
        $remaining_amount = abs($this->get('total_with_vat')) - $this->get('paid_amount');
        if ($this->get('type') > PH_FINANCE_TYPE_MAX) {
            // get total amount of all FINISHED invoices and debit/credit notes for reason +
            // total paid amount to non-invoiced proformas
            $remaining_amount -= ($this->getInvoicedAmount(true) + $this->getProformasPaidAmount(true));
        } elseif ($this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            // get parent reason
            $this->getRelatives(array('get_parent_reasons' => true));
            $reason = $this->get('parent_reasons');
            if (!empty($reason)) {
                $reason = array_shift($reason);
            }
            $this->unsetProperty('parent_reasons', true);
            // if proforma has a parent reason
            if ($reason) {
                // the maximum possible amount that could be paid to the incomes reason
                $reason_available_amount = $reason->get('total_with_vat') - ($reason->getInvoicedAmount() + $reason->getPaidAmount() + $reason->getProformasPaidAmount());
                if ($reason_available_amount < $remaining_amount) {
                    // the amount from the proforma that can be paid
                    $remaining_amount = $reason_available_amount;
                }
                unset($reason);
            }
        }
        $remaining_amount = round($remaining_amount, 2);
        $this->set('remaining_amount', $remaining_amount, true);

        return $remaining_amount;
    }

    /**
     * Get distributed and available payments for the customer of this reason
     *
     * @return bool - result of the operation
     */
    public function getCustomerPayments() {
        //get remaining amount
        $remaining_amount = $this->getRemainingAmount();

        $db = $this->registry['db'];

        //get ids of payments relative to this income reason
        $query = 'SELECT DISTINCT(fp.id) FROM ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp, ' . "\n" .
                 DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                 'WHERE fp.status="finished" AND fp.annulled_by=0 AND fr.paid_amount > 0' . "\n" .
                 ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE ?
                 ' AND fr.parent_model_name="' . $this->modelName . '" AND fr.parent_id=' . $this->get('id') . "\n" .
                 ' AND fr.paid_to_model_name="Finance_Payment" AND fr.paid_to=fp.id' :
                 ' AND fr.paid_to_model_name="' . $this->modelName . '" AND fr.paid_to=' . $this->get('id') . "\n" .
                 ' AND fr.parent_model_name="Finance_Payment" AND fr.parent_id=fp.id');
        $ids = $db->GetCol($query);

        if ($ids) {
            $paid_ids = ' OR fp.id in (' . implode(',', $ids) . ')';
        } else {
            $paid_ids = '';
        }

        //get payments for the customer in the incomes reason
        $filters = array();
        $filters['where'] = array('fp.company = ' . $this->get('company'),
                                  'fp.status="finished"',
                                  'fp.currency="' . $this->get('currency') . '"',
                                  'fp.customer=' . $this->get('customer'));
        if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
            //this filter gets only those payments that are not yet destributed to other financial documents
            $filters['where'][] = '((SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE. ' as fr WHERE fp.id=fr.paid_to AND paid_to_model_name="Finance_Payment" GROUP BY fr.paid_to) IS NULL OR
                                  fp.amount>(SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE. ' as fr WHERE fp.id=fr.paid_to AND paid_to_model_name="Finance_Payment" GROUP BY fr.paid_to)' . $paid_ids . ')';
            $filters['where'][] = 'fp.type IN ("RKO", "PN")';
        } else {
            $filters['where'][] = '((SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE. ' as fr WHERE fp.id=fr.parent_id AND parent_model_name="Finance_Payment" GROUP BY fr.parent_id) IS NULL OR
                                   fp.amount>(SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE. ' as fr WHERE fp.id=fr.parent_id AND parent_model_name="Finance_Payment" GROUP BY fr.parent_id)' . $paid_ids . ')';
            $filters['where'][] = 'fp.type IN ("PKO", "BP")';
        }
        $filters['model_lang'] = $this->get('lang');
        $filters['sort'] = array('fp.issue_date ASC', 'fp.added ASC', 'fp.id ASC');
        require_once 'finance.payments.factory.php';
        $payments = Finance_Payments::search($this->registry, $filters);

        $payments_auto_balance = $this->registry['config']->getParam('finance', 'payments_auto_balance');

        $records = array();
        foreach ($payments as $key => $payment) {

            //get the distributed amount of this payment for all financial documents (paid_amount)
            $payment->getPaidAmount();
            //get the distributed amount of this payment for this income reason
            if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                $payment->getPaidAmount(array('parent_model_name' => $this->modelName, 'parent_id' => $this->get('id')));
            } else {
                $payment->getPaidAmount(array('paid_to_model_name' => $this->modelName, 'paid_to' => $this->get('id')));
            }
            if ($remaining_amount < 0.01) {
                $remaining_amount = 0;
            }
            //remaining amount for this payment
            $remaining_payment_amount = $payment->get('amount') - $payment->get('paid_amount');
            $remaining_payment_amount = round($remaining_payment_amount, 2);

            if ($remaining_amount > 0 && $remaining_payment_amount >= 0.01 && $payments_auto_balance != 'no') {
                //set suggest amount for every unpaid reason
                if ($remaining_amount > $remaining_payment_amount) {
                    $suggest_amount = $remaining_payment_amount;
                    $remaining_amount -= $remaining_payment_amount;
                    $remaining_amount = round($remaining_amount, 2);
                } else {
                    $suggest_amount = $remaining_amount;
                    $remaining_amount = 0;
                }
                if ($suggest_amount < 0.01) {
                    $suggest_amount = 0;
                }
                $payments[$key]->set('suggest_amount', $suggest_amount, true);
            } elseif (($remaining_payment_amount <= 0 || $this->get('remaining_amount') <= 0)
                && !in_array($payment->get('id'), $ids)) {
                //unset no depending distributed payment
                unset($payments[$key]);
            }
            if (isset($payments[$key])) {
                $records[$payment->get('id')] = $payment->sanitize();
                unset($payments[$key]);
            }
        }
        $this->set('payments', $records, true);

        return true;
    }

    /**
     * Get and propose distribution of financial documents to the incomes reason
     *
     * @param string $selected - selected tab
     * @param bool $check_only - if true, only check if there are any allocated records
     * @return bool - result of the operation
     */
    public function getPaymentsDistribution($selected, $check_only = false) {
        //get remaining amount
        $remaining_amount = $this->getRemainingAmount();

        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');
        $db = $this->registry['db'];

        if ($selected == 'expenses_credit') {
            $type = '= ' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE;
            $alias = 'fer';
            $model = 'Finance_Expenses_Reason';
            $table = DB_TABLE_FINANCE_EXPENSES_REASONS;
        } elseif ($selected == 'incomes_invoice') {
            $type = '= ' . PH_FINANCE_TYPE_INVOICE;
            $alias = 'fir';
            $model = 'Finance_Incomes_Reason';
            $table = DB_TABLE_FINANCE_INCOMES_REASONS;
        } elseif ($selected == 'incomes_debit') {
            $type = '= ' . PH_FINANCE_TYPE_DEBIT_NOTICE;
            $alias = 'fir';
            $model = 'Finance_Incomes_Reason';
            $table = DB_TABLE_FINANCE_INCOMES_REASONS;
        } elseif ($selected == 'incomes_credit') {
            $type = '= ' . PH_FINANCE_TYPE_CREDIT_NOTICE;
            $alias = 'fir';
            $model = 'Finance_Incomes_Reason';
            $table = DB_TABLE_FINANCE_INCOMES_REASONS;
        } elseif ($selected == 'expenses_invoice') {
            $type = '= ' . PH_FINANCE_TYPE_EXPENSES_INVOICE;
            $alias = 'fer';
            $model = 'Finance_Expenses_Reason';
            $table = DB_TABLE_FINANCE_EXPENSES_REASONS;
        } elseif ($selected == 'expenses_debit') {
            $type = '= ' . PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE;
            $alias = 'fer';
            $model = 'Finance_Expenses_Reason';
            $table = DB_TABLE_FINANCE_EXPENSES_REASONS;
        } elseif ($selected == 'expenses_reason') {
            $type = '> ' . PH_FINANCE_TYPE_MAX;
            $alias = 'fer';
            $model = 'Finance_Expenses_Reason';
            $table = DB_TABLE_FINANCE_EXPENSES_REASONS;
        }

        //get ids of models paid to this incomes reason
        if ($this->get('type') != PH_FINANCE_TYPE_CREDIT_NOTICE) {
            $query = 'SELECT fr.parent_id, fr.paid_amount FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr' . "\n" .
                    'JOIN ' . $table . ' as t' . "\n" .
                    '    ON t.id = fr.parent_id AND t.type ' . $type . "\n" .
                    '    AND ABS(t.total_with_vat) > 0 AND t.annulled_by = 0' . "\n" .
                    'WHERE fr.paid_to = ' . $this->get('id') . "\n" .
                    '    AND fr.paid_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                    '    AND fr.parent_model_name = "' . $model . '" AND fr.paid_amount > 0';
        } else {
            $query = 'SELECT fr.paid_to, fr.paid_amount FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr' . "\n" .
                    'JOIN ' . $table . ' as t' . "\n" .
                    '    ON t.id = fr.paid_to AND t.type ' . $type . "\n" .
                    '    AND ABS(t.total_with_vat) > 0 AND t.annulled_by = 0' . "\n" .
                    'WHERE fr.parent_id = ' . $this->get('id') . "\n" .
                    '    AND fr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                    '    AND fr.paid_to_model_name = "' . $model . '" AND fr.paid_amount > 0';
        }
        $ids = $db->GetAssoc($query);
        if ($ids) {
            $paid_ids = ' OR ' . $alias . '.id IN (' . implode(',', array_keys($ids)) . ')';
        } else {
            $paid_ids = '';
        }

        //get requested models for the customer in the incomes reason
        $filters = array();
        $filters['where'] = array(
            $alias . '.company = ' . $this->get('company'),
            '(' . $alias . '.type ' . $type . $paid_ids . ')',
            $alias . '.status = "finished"',
            $alias . '.active = 1',
            $alias . '.annulled_by = 0',
            $alias . '.payment_status <> "nopay"',
            'ABS(' . $alias . '.total_with_vat) > 0 ',
            $alias . '.currency = "' . $this->get('currency') . '"',
            $alias . '.customer = ' . $this->get('customer')
        );
        $filters['model_lang'] = $this->get('lang');
        $filters['sort'] = array($alias . '.issue_date ASC', $alias . '.added ASC', $alias . '.id ASC');

        require_once PH_MODULES_DIR . 'finance/models/' . General::singular2plural(strtolower(preg_replace('#_#', '.', $model, 1))) . '.factory.php';
        $model_factory = General::singular2plural($model);
        $models = $model_factory::search($this->registry, $filters);

        $payments_auto_balance = $this->registry['config']->getParam('finance', 'payments_auto_balance');

        // unallocated amount of credit notes should be suggested to parent invoice with preference
        $reserved_amounts = array();
        if ($payments_auto_balance != 'no' && bccomp($remaining_amount, 0, 2) == 1) {
            $relatives = array();
            if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE && $selected == 'incomes_invoice') {
                // if model is a credit note and selected tab is invoices
                $this->getRelatives(array('get_parent_reasons' =>
                    array('fir.type = ' . PH_FINANCE_TYPE_INVOICE, 'fir.payment_status IN ("unpaid", "partial")', 'fir.active = 1', 'fir.annulled_by = 0')));
                $relatives = $this->get('parent_reasons') ?: array();
                $this->unsetProperty('parent_reasons', true);
            } elseif ($this->get('type') == PH_FINANCE_TYPE_INVOICE && $selected == 'incomes_credit') {
                // if model is an invoice and selected tab is credit notes
                $this->getRelatives(array('get_credit_notices' =>
                    array('fir.payment_status IN ("unpaid", "partial")', 'fir.active = 1', 'fir.annulled_by = 0')));
                $relatives = $this->get('credit_notices') ?: array();
                // sort records by issue_date ASC, added ASC, id ASC
                usort($relatives, function($a, $b) {
                    if ($a->get('issue_date') != $b->get('issue_date')) {
                        return $a->get('issue_date') < $b->get('issue_date') ? -1: 1;
                    } elseif ($a->get('added') != $b->get('added')) {
                        return $a->get('added') < $b->get('added') ? -1 : 1;
                    } else {
                        return $a->get('id') < $b->get('id') ? -1 : 1;
                    }
                });
            }

            while (list(, $rel) = each($relatives)) {
                $reserved_amount = bcsub(abs($rel->get('total_with_vat')), $rel->getPaidAmount(), 2);
                if (bccomp($reserved_amount, $remaining_amount, 2) == 1) {
                    $reserved_amount = $remaining_amount;
                }
                $reserved_amounts[$rel->get('id')] = $reserved_amount;
                $remaining_amount = bcsub($remaining_amount, $reserved_amount, 2);
                if ($remaining_amount <= 0) {
                    break;
                }
            }
            unset($relatives);
        }

        $records = array();
        foreach ($models as $key => $model) {
            // get the distributed amount of financial document for all financial documents (paid_amount)
            $model->getPaidAmount();
            if ($model->modelName == 'Finance_Expenses_Reason' && $model->get('type') > PH_FINANCE_TYPE_MAX && $model->getPaidRelative()) {
                // add paid amount to non-invoiced proforma
                $model->set('paid_amount', $model->get('paid_amount') + (double)$model->get('relative_paid_amount'), true);
            }
            // get the distributed amount of financial document for this incomes reason
            if (!empty($ids[$model->get('id')])) {
                $model->set('paid_amount_Finance_Incomes_Reason_' . $this->get('id'), $ids[$model->get('id')], true);
            } else {
                $model->set('paid_amount_Finance_Incomes_Reason_' . $this->get('id'), 0.00, true);
            }

            $model->set('total_with_vat', sprintf('%.' . $gt2_total_precision . 'f', abs($model->get('total_with_vat'))), true);

            if ($remaining_amount < 0.01) {
                $remaining_amount = 0;
            }
            //remaining amount for this notice
            $remaining_payment_amount = round($model->get('total_with_vat'), 2) - $model->get('paid_amount');
            $remaining_payment_amount = round($remaining_payment_amount, 2);

            if (array_key_exists($model->get('id'), $reserved_amounts)) {
                $models[$key]->set('suggest_amount', $reserved_amounts[$model->get('id')], true);
            } elseif ($remaining_amount > 0 && $remaining_payment_amount >= 0.01 && $payments_auto_balance != 'no') {
                //set suggest amount for every unpaid notice
                if ($remaining_amount > $remaining_payment_amount) {
                    $suggest_amount = $remaining_payment_amount;
                    $remaining_amount -= $remaining_payment_amount;
                    $remaining_amount = round($remaining_amount, 2);
                } else {
                    $suggest_amount = $remaining_amount;
                    $remaining_amount = 0;
                }
                if ($suggest_amount < 0.01) {
                    $suggest_amount = 0;
                }
                $models[$key]->set('suggest_amount', $suggest_amount, true);
            } elseif (($remaining_payment_amount <= 0 || $this->get('remaining_amount') <= 0)
                    && !isset($ids[$models[$key]->get('id')])) {
                //unset no depending distributed notice
                unset($models[$key]);
            }
            if (isset($models[$key])) {
                $records[$model->get('id')] = $model;
                unset($models[$key]);
            }
        }
        $this->set(General::singular2plural($selected), $records, true);

        return true;
    }

    /**
     * Remove paid amount from incomes reason
     *
     * @param int $parent_id - payment id
     * @param string $selected_tab - type of payment (payment, credit notice, invoice or debit note, reason)
     * @return bool - result of the operation
     */
    public function emptyPaidAmount($parent_id, $selected_tab = 'payment') {

        if (!$parent_id) {
            return true;
        }

        $db = &$this->registry['db'];

        // if a started transaction has already failed (because of
        // before_action automations), do not perform actions
        if ($db->HasFailedTrans()) {
            return false;
        }

        $db->StartTrans();

        if ($selected_tab == 'payment') {
            $parent_model_name = "Finance_Payment";
        } elseif (preg_match('#^incomes_#', $selected_tab)) {
            $parent_model_name = "Finance_Incomes_Reason";
        } elseif (preg_match('#^expenses_#', $selected_tab)) {
            $parent_model_name = "Finance_Expenses_Reason";
        }

        if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
            $query = 'SELECT paid_amount FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                     'WHERE paid_to=' . $parent_id . ' AND  parent_id=' . $this->get('id') . "\n" .
                     '  AND paid_to_model_name="' . $parent_model_name . '" AND parent_model_name="Finance_Incomes_Reason"';
            $paid_amount = $db->GetOne($query);

            // removes balance record for current model and related model
            $query = 'DELETE FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                     'WHERE paid_to=' . $parent_id . ' AND  parent_id=' . $this->get('id') . "\n" .
                     '  AND paid_to_model_name="' . $parent_model_name . '" AND parent_model_name="Finance_Incomes_Reason"';

            $db->Execute($query);
        } else {
            $query = 'SELECT paid_amount FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                     'WHERE parent_id=' . $parent_id . ' AND  paid_to=' . $this->get('id') . "\n" .
                     '  AND paid_to_model_name="Finance_Incomes_Reason" AND parent_model_name="' . $parent_model_name . '"';
            $paid_amount = $db->GetOne($query);

            // removes balance record for current model and related model
            $query = 'DELETE FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                     'WHERE parent_id=' . $parent_id . ' AND  paid_to=' . $this->get('id') . "\n" .
                     '  AND paid_to_model_name="Finance_Incomes_Reason" AND parent_model_name="' . $parent_model_name . '"';

            $db->Execute($query);
        }

        //remove from repayment plan
        require_once 'finance.repayment_plans.factory.php';
        $filters = array('where' => array('frp.customer = ' . $this->get('customer'),
                                          'frp.company = ' . $this->get('company'),
                                          'frp.status = "locked"'),
                         'model_lang' => $this->get('lang'));
        $repayment_plan = Finance_Repayment_Plans::searchOne($this->registry, $filters);
        if ($repayment_plan) {
            $query = 'SELECT frpi.incomes_id FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . ' as frpi ' . "\n" .
                     'WHERE frpi.parent_id=' . $repayment_plan->get('id') .
                     ' AND frpi.incomes_id=' . $this->get('id');
            $repayment_incomes_id = $db->GetOne($query);
            if ($repayment_incomes_id) {
                $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . ' as frpa ' . "\n" .
                         'WHERE frpa.parent_id=' . $repayment_plan->get('id') .
                         ' AND frpa.paid_amount > 0' . "\n" .
                         'ORDER BY deadline DESC';
                $repayment_data = $db->GetAll($query);
                foreach ($repayment_data as $row) {
                    if ($row['paid_amount'] > 0) {
                        $empty_amount = $row['paid_amount'] >= $paid_amount ? $paid_amount : $row['paid_amount'];
                        $paid_amount -= $empty_amount;
                        $paid_amount = round($paid_amount, 2);
                        $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                                 ' SET paid_amount=paid_amount-' . $empty_amount . "\n" .
                                 ' , last_payment=now()' . "\n" .
                                 ' WHERE parent_id=' . $row['parent_id'] .
                                 ' AND deadline="' . $row['deadline'] . '"';
                        $db->Execute($query);
                        $repayment_plan->set('amount', $empty_amount, true);
                        require_once 'finance.repayment_plans.history.php';
                        Finance_Repayment_Plans_History::saveData($this->registry,
                        array('model' => $repayment_plan, 'action_type' => 'empty',
                         'new_model' => $repayment_plan, 'old_model' => $repayment_plan));
                        if ($paid_amount <= 0) {
                            break;
                        }
                    }
                }
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update incomes reason payments
     *
     * @param string $selected_tab - type of payment (payment, credit note, reason, invoice)
     * @return bool - result of the operation
     */
    public function updateFinanceRelatives($selected_tab = 'payment') {
        //UPDATE RELATIVES OF THE MODEL
        $db = &$this->registry['db'];

        // if a started transaction has already failed (because of
        // before_action automations), do not perform actions
        if ($db->HasFailedTrans()) {
            return false;
        }

        $db->StartTrans();

        if (!$this->get('relatives_payments')) {
            $db->FailTrans();
            $db->CompleteTrans();
            return false;
        }

        if ($selected_tab == 'payment') {
            $this->getCustomerPayments();

            $all_amounts = 0;
            if ($this->get('relatives_payments')) {
                $all_amounts += array_sum($this->get('relatives_payments'));
                $all_amounts = round($all_amounts, 2);
                if (bccomp($all_amounts, $this->get('remaining_amount'), 2) == 1) {
                    //the distributed amount ($all_amounts) is larger than the remaining amount for this expense reason
                    // set error message
                    $this->raiseError('error_finance_incomes_reasons_allocated_amount_total');
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                }
            }

            $amounts = $this->get('relatives_payments');
            $payments = $this->get('payments');
            $distributed_amount = 0;
            foreach ($amounts as $key => $amount) {
                $amount = round($amount, 2);
                if ($amount > 0) {
                    $tmp_compare = round($payments[$key]->get('amount') - $payments[$key]->get('paid_amount'), 2);
                    if (!isset($payments[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        //the distributed amount for this payment ($amount) is larger than the remaining undistributed amount for this payment
                        // set error message
                        $this->raiseError('error_finance_incomes_reasons_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }
                    $insert = array();
                    $insert['modified'] = sprintf("modified=now()");
                    $insert['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
                    $update = $insert;
                    $update['paid_amount'] = sprintf("paid_amount=paid_amount + %f", $amount);
                    $insert['paid_amount'] = sprintf("paid_amount='%f'", $amount);
                    if ($this->get('type') != PH_FINANCE_TYPE_CREDIT_NOTICE) {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $this->get('id'));
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", $this->modelName);
                        $insert['parent_id'] = sprintf("parent_id='%d'", $key);
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", 'Finance_Payment');
                    } else {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $key);
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", 'Finance_Payment');
                        $insert['parent_id'] = sprintf("parent_id='%d'", $this->get('id'));
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", $this->modelName);
                    }
                    $insert['paid_currency'] = sprintf("paid_currency='%s'", $this->get('currency'));
                    $insert['added'] = sprintf("added=now()");
                    $insert['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

                    //query to insert/update the finance balance
                    $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);

                    $res = $db->Execute($query2);
                    if ($res) {
                        $distributed_amount += $amount;
                    }
                }

            }
        } else {
            //save amounts from other model
            $this->getPaymentsDistribution($selected_tab);

            $all_amounts = 0;
            if ($this->get('relatives_payments')) {
                $all_amounts += array_sum($this->get('relatives_payments'));
                $all_amounts = round($all_amounts, 2);
                if (bccomp($all_amounts, $this->get('remaining_amount'), 2) == 1) {
                    //the distributed amount ($all_amounts) is larger than the remaining amount for this income reason
                    // set error message
                    $this->raiseError('error_finance_incomes_reasons_allocated_amount_total');
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                }
            }

            $amounts = $this->get('relatives_payments');
            $models = $this->get(General::singular2plural($selected_tab));
            $distributed_amount = 0;
            foreach ($amounts as $key => $amount) {
                $amount = round($amount, 2);
                if ($amount > 0) {
                    $tmp_compare = round($models[$key]->get('total_with_vat') - $models[$key]->get('paid_amount'), 2);
                    if (!isset($models[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        //the distributed amount for this payment ($amount) is larger than the remaining undistributed amount for this payment
                        // set error message
                        $this->raiseError('error_finance_incomes_reasons_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    if (preg_match('#^expenses_#', $selected_tab)) {
                        $model_name = 'Finance_Expenses_Reason';
                    } elseif (preg_match('#^incomes_#', $selected_tab)) {
                        $model_name = 'Finance_Incomes_Reason';
                    }

                    $insert = array();
                    $insert['modified'] = sprintf("modified=now()");
                    $insert['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
                    $update = $insert;
                    $update['paid_amount'] = sprintf("paid_amount=paid_amount + %f", $amount);
                    $insert['paid_amount'] = sprintf("paid_amount='%f'", $amount);
                    if ($this->get('type') != PH_FINANCE_TYPE_CREDIT_NOTICE) {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $this->get('id'));
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", $this->modelName);
                        $insert['parent_id'] = sprintf("parent_id='%d'", $key);
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", $model_name);
                    } else {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $key);
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", $model_name);
                        $insert['parent_id'] = sprintf("parent_id='%d'", $this->get('id'));
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", $this->modelName);
                    }

                    $insert['paid_currency'] = sprintf("paid_currency='%s'", $this->get('currency'));
                    $insert['added'] = sprintf("added=now()");
                    $insert['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

                    //query to insert/update the finance balance
                    $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);

                    $res = $db->Execute($query2);
                    if ($res) {
                        $distributed_amount += $amount;
                        $distributed_amount = round($distributed_amount, 2);
                    }
                }

            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update receive date (and date of payment if it depends on it)
     *
     * @return bool - result of the operation
     */
    public function updateReceiveDate() {

        if ($this->get('date_of_receive') < $this->get('issue_date')) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_receive_date'));
            return false;
        }
        $db = $this->registry['db'];
        $db->StartTrans();

        $date_of_payment = '';
        if ($this->get('date_of_payment_count') !== '') {
            if ($this->get('date_of_payment_period_type') == 'working') {
                require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($this->registry,
                    $this->get('date_of_receive'),
                    $this->get('date_of_payment_count'), 'after');
            } else {
                $date_of_payment = General::strftime('%Y-%m-%d',
                    strtotime('+' . $this->get('date_of_payment_count') . ' day', strtotime($this->get('date_of_receive'))));
            }
        }
        //update invoice receive date
        $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                 ' SET date_of_receive="' . $this->get('date_of_receive') . '"' . "\n" .
                 (!empty($date_of_payment) ? ',date_of_payment = "' . $date_of_payment . '"' : '') .
                 ' WHERE id=' . $this->get('id');
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Validate advanced invoice added to contract
     *
     * @param object $template - Finance_Invoices_Template model that invoice will be created from
     * @return bool - true if valid, otherwise false
     */
    public function validateAdvanced($template) {

        if (!$this->get('payment_type')) {
            $this->raiseError('error_finance_invoices_advanced_no_payment_type', 'company_data');
        }
        if (!$this->get('periods_start')) {
            $this->raiseError('error_finance_invoices_advanced_no_periods_start', 'periods_start');
        }
        if (!$this->get('periods_end')) {
            $this->raiseError('error_finance_invoices_advanced_no_periods_end', 'periods_end');
        }
        if ($this->get('periods_start') > $this->get('periods_end')) {
            $this->raiseError('error_finance_invoices_advanced_recurrence_wrong_period', 'periods_start');
        }
        if ((!$this->get('date_of_payment_period_type') || !$this->get('date_of_payment_point')) && !$this->get('date_of_payment')) {
            $this->raiseError('error_finance_invoices_advanced_no_pay_after', 'date_of_payment');
        }
        if ($this->get('periods_start') < $template->get('periods_start')) {
            $this->raiseError('error_finance_invoices_advanced_recurrence_template_start', 'periods_start');
        }
        if ($this->get('periods_end') > $template->get('periods_end')) {
            $this->raiseError('error_finance_invoices_advanced_recurrence_template_end', 'periods_end');
        }
        $query = 'SELECT MIN(fiti.`from`) AS `from_min`, MAX(fiti.`to`) AS `to_max`' . "\n" .
                 '  FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti ' . "\n" .
                 '  INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                 '  ON fir.id=fiti.invoice_id AND fir.annulled_by=0 ' . "\n" .
                 '  WHERE fiti.`parent_id`=' . $template->get('id') . "\n" .
                 '  AND fiti.`invoice_id`>0';
        $dates = $this->registry['db']->GetRow($query);
        if ($dates['from_min'] && $dates['from_min'] != '0000-00-00' && $dates['to_max'] && $dates['to_max'] != '0000-00-00' && $this->get('periods_start') <= $dates['to_max'] && $this->get('periods_end') >= $dates['from_min']) {
            $this->raiseError('error_finance_invoices_advanced_recurrence_wrong_period', 'periods_end');
            $dates['to_max'] = strtotime('+1 day', strtotime($dates['to_max']));
            $dates['to_max'] = General::strftime($this->i18n('date_short'), $dates['to_max']);
            $this->raiseError('error_finance_invoices_advanced_invoiced_period', 'periods_start', '', array($dates['to_max']));
        }

        if (!$this->valid) {
            return false;
        }

        if ($this->get('advance')) {
            //validate advance against the template data
            $this->registry->set('get_old_vars', true, true);
            $template->getGT2Vars();
            $this->registry->set('get_old_vars', false, true);
            $template->set('periods_start', $this->get('periods_start'), true);
            $template->set('periods_end', $this->get('periods_end'), true);
            $next_from = $template->get('periods_start');
            $date_to = $template->get('periods_end');
            $template->set('status', 'opened', true);
            //calculate template recurrence until we reach the date_to
            $total = 0;
            while ($next_from <= $date_to) {
                list($gt2, $to) = $template->calculateRecurrence(array('date_from' => $next_from));
                //apply indexes for the rows if any indexes are present
                $gt2 = Finance_Incomes_Reasons::getIndexedValues($this->registry, $gt2);
                $gt2 = Finance_Incomes_Reasons::calculateGT2Rows($this->registry, $gt2, $template->get('total_vat_rate'));
                $template->set('status', 'locked', true);
                $next_from = date_add(date_create($to), new DateInterval('P1D'))->format('Y-m-d');
                foreach ($gt2 as $k => $v) {
                    $total +=$v['subtotal_with_discount'];
                }
            }
            //calculate advance - tricky
            $advance = array_sum($this->registry['request']->get('price'));
            if ($total < $advance) {
                $this->raiseError('error_finance_incomes_reasons_advance_overdue');
            }
        }
        $this->validateCustomerFinData();

        return $this->valid;
    }

    /**
     * Send model to customer automatically with file generated
     *
     * @param int $email_pattern - id of email pattern
     * @param int $generate_pattern - id of print pattern (optional)
     * @param string $lang - the language we will use to send the model (optional)
     * @return array - result for the operation (sent and erred mails)
     */
    public function sendToCustomer($email_pattern, $generate_pattern = '', $lang = '') {
        //get email for the customers financial contact from the contract
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        if (!$lang) {
            $lang = $this->registry['config']->getParam('crontab', 'invoices_send_lang');
        }
        if (!$lang) {
            $lang = $this->registry['lang'];
        }

        $rLang = $this->registry->get('lang');
        $reqLang = $this->registry['request']->get('lang');
        $this->registry['translater']->reloadFiles($lang);
        $this->registry->set('lang', $lang, true);
        $this->registry['request']->set('model_lang', $lang, '', true);

        //get contract in the language we have to send the model in
        if ($this->get('contract') && !$this->get('custom_email_address')) {
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $filters = array('where' => array('co.id = ' . $this->get('contract')),
                             'model_lang' => $lang);
            $contract = Contracts::searchOne($this->registry, $filters);
            $fin = $contract->getCstmFinancialData();
        } elseif ($this->get('custom_email_address')) {
            $fin = $this->get('custom_email_address');
            if (!is_array($fin)) {
                $fin = array('email' => $fin, 'name' => '');
            }
        } else {
            $fin = array();
        }

        if (empty($fin['email'])) {
            if (!empty($sanitize_after)) {
                $this->sanitize();
            }
            return array();
        }
        if ($lang == $this->get('model_lang')) {
            $trans_invoice = clone $this;
        } else {
            $filters = array('where' => array('fir.id = ' . $this->get('id')),
                             'model_lang' => $lang);
            $trans_invoice = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $trans_invoice->getGT2Vars();
        }

        if ($trans_invoice->get('type') != $this->get('type')) {
            //do some magic here to be able to generate credit/debit notices
            //with template for the invoice they belong to
            $trans_invoice->set('type', $this->get('type'), true);
        }

        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $filters = array('where' => array('e.id = ' . $email_pattern),
                         'sanitize' => true, 'model_lang' => $lang);
        $mail = Emails::searchOne($this->registry, $filters);

        $trans_invoice->set('email_subject', $mail->get('subject'), true);
        $trans_invoice->set('body', $mail->get('body'), true);
        $trans_invoice->set('email_template', $mail->get('id'), true);

        $trans_invoice->set('customer_email', array($fin['name'] . ' <'.$fin['email'].'>'), true);

        //get CC recipients of financial contact
        $cstm_fin_cc = array();
        if (!empty($contract)) {
            $cstm_fin_cc = $contract->getContactCcData('cstm', 'fin');
        }

        $customer_email_cc = array();
        foreach ($cstm_fin_cc as $contact_cc) {
            $customer_email_cc[] = $contact_cc['name'] . ' <'.$contact_cc['email'].'>';
        }
        $trans_invoice->set('customer_email_cc', $customer_email_cc, true);

        //get the pattern to generate file from
        if ($generate_pattern) {
            $trans_invoice->set('attached_files', array('pattern_' . $generate_pattern), true);
        }
        if (!empty($contract)) {
            $trans_invoice->set('email_extra', 'contract:=' . $contract->get('id'), true);
        }
        $mails = $trans_invoice->sendAsMail();

        $this->registry['translater']->reloadFiles($rLang);
        $this->registry->set('lang', $rLang, true);
        $this->registry['request']->set('model_lang', $reqLang, '', true);

        if (!empty($sanitize_after)) {
            $this->sanitize();
        }

        return $mails;
    }

    /**
     * Translate model to languages defined in the settings
     *
     * @return boolean - result of the operation
     */
    public function autoTranslate() {
        $this->registry['db']->StartTrans();
        //get languages for the invoices to be translated to
        $translate_langs = $this->registry['config']->getParamAsArray('crontab', 'translate_invoices_issued');

        //translate invoice
        if ($translate_langs) {
            $model_lang = $this->get('model_lang');
            $rLang = $this->registry->get('lang');
            $customer = isset($this->customer) ? $this->customer : null;

            // prepare data for gt2 i18n field for row info
            $gt2_info = array(
                'create_info_data' => 'free_text5',
                'gt2_info_data_model_name' => 'Contract'
            );
            $contract_id = 0;
            if ($this->get('contract_id')) {
                $contract_id = $this->get('contract_id');
            } elseif ($this->get('link_to_model_name') == 'Contract' && $this->get('link_to')) {
                $contract_id = $this->get('link_to');
            }
            if ($contract_id) {
                $query = 'SELECT type FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = \'' . $contract_id . '\'';
                $gt2_info['gt2_info_data_model_type'] = $this->registry['db']->GetOne($query);
            }

            foreach ($translate_langs as $lang) {
                if ($lang == $model_lang) {
                    continue;
                }
                //reload all i18n files with the new language
                if (!$this->registry['translater']->reloadFiles($lang)) {
                    continue;
                }

                // prepare info format in every language just once per model
                $query = 'SELECT *' . "\n" .
                         '  FROM `' . DB_TABLE_CONTRACTS_TYPES_I18N . '`' . "\n" .
                         '  WHERE `parent_id` = \'' . ($gt2_info['gt2_info_data_model_type'] ?: '') . '\'' . "\n" .
                         '    AND `lang`      = \'' . $lang . '\'';
                $type_i18n = $this->registry['db']->GetRow($query);
                if (isset($type_i18n['invoice_issue_auto_message'])) {
                    $gt2_info['invoice_issue_auto_message'] = $type_i18n['invoice_issue_auto_message'];
                }

                //set the new model lang
                $this->set('model_lang', $lang, true);
                //translate gt2 i18n field for row info
                $this->set('grouping_table_2', $this->get('grouping_table_2') + $gt2_info, true);
                $this->slashesEscape();
                $this->translate();
                $this->slashesStrip();
            }

            $this->customer = $customer;
            if ($this->get('model_lang') != $model_lang) {
                $this->registry['translater']->reloadFiles($rLang);
                $this->set('model_lang', $model_lang, true);
                $this->registry->set('lang', $rLang, true);
            }
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Annuls invoice/proforma invoice/credit/debit notice
     *
     * @return bool - result of the operation
     */
    public function annul() {

        $db = $this->registry['db'];
        $db->StartTrans();

        $old_reason = clone $this;
        $old_reason->getGT2Vars();
        $old_reason->sanitize();

        $this->getGT2Vars();
        $table = $this->get('grouping_table_2');

        $query = 'SELECT *' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                 'WHERE ' .
                 ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE ?
                 '(fr.parent_id=' . $this->get('id') . ' AND fr.parent_model_name="Finance_Incomes_Reason")' :
                 '(fr.paid_to= ' . $this->get('id') . ' AND fr.paid_to_model_name="Finance_Incomes_Reason")');
        $payments = $db->GetAll($query);

        //remove payments
        require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
        $pmnts = array();
        foreach ($payments as $payment) {
            if ($payment['parent_id'] == $this->get('id') && $payment['parent_model_name'] == "Finance_Incomes_Reason") {
                if (preg_match('#^finance_#i', $payment['paid_to_model_name'])) {
                    $selected_tab = strtolower(preg_replace('#finance_#i', '', $payment['paid_to_model_name']));
                } else {
                    $selected_tab = 'payment';
                }
                $this->emptyPaidAmount($payment['paid_to'], $selected_tab);
                $search_id = $payment['paid_to'];
            } elseif ($payment['paid_to'] == $this->get('id') && $payment['paid_to_model_name'] == "Finance_Incomes_Reason") {
                if (preg_match('#^finance_#i', $payment['parent_model_name'])) {
                    $selected_tab = strtolower(preg_replace('#finance_#i', '', $payment['parent_model_name']));
                } else {
                    $selected_tab = 'payment';
                }
                $this->emptyPaidAmount($payment['parent_id'], $selected_tab);
                $search_id = $payment['parent_id'];
            }

            $query = 'SELECT ' . ($selected_tab == 'payment' ? 'reason' : 'name') . "\n" .
                     'FROM ' . ($selected_tab == 'payment' ? DB_TABLE_FINANCE_PAYMENTS_I18N : (preg_match('#expenses#', $selected_tab) ? DB_TABLE_FINANCE_EXPENSES_REASONS_I18N : DB_TABLE_FINANCE_INCOMES_REASONS_I18N)) . "\n" .
                     'WHERE parent_id = ' . $search_id . ' AND lang = "' . $this->registry['lang'] . '"';
            $pmnts[] = array(
                    'id' => $search_id,
                    'name' => $this->registry['db']->GetOne($query),
                    'controller' => ($selected_tab == 'payment' ? 'payments' : (preg_match('#expenses#', $selected_tab) ? 'expenses_reasons' : 'incomes_reasons'))
            );
            ///write some history for payment removal
            require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
            Finance_Incomes_Reasons_History::saveData($this->registry,
                array('model' => $this,
                      'action_type' => 'empty',
                      'new_model' => $this,
                      'old_model' => $old_reason));
            if ($this->slashesEscaped) {
                $this->slashesStrip();
            }
        }
        if (!empty($pmnts)) {
            foreach ($pmnts as $k => $v) {
                $pmnts[$k] = sprintf('<a target="_blank" href="%s?%s=finance&amp;controller=%s&amp;%s=view&amp;view=%d">%s</a>',
                                     $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                     $v['controller'], $v['controller'], $v['id'], ($v['name'] ?: $this->i18n('finance_' . General::plural2singular($v['controller']))));
            }
            $this->registry['messages']->setWarning($this->i18n('finance_removed_payments', array(implode(', ', $pmnts))));
        }

        // check whether annulment report should be issued
        if ($this->checkIssueAnnulmentReport()) {
            require_once PH_MODULES_DIR . 'finance/models/finance.annulments.factory.php';
            $annulment = new Finance_Annulment($this->registry);
            $annulment->properties = $this->properties;
            $annulment->set('id', null, true);
            $annulment->set('num', null, true);
            $annulment->set('type', PH_FINANCE_TYPE_ANNULMENT, true);
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                     'WHERE parent_id = ' . PH_FINANCE_TYPE_ANNULMENT . ' AND lang="' . $this->registry['lang'] . '"';
            $type_name = $db->GetOne($query);
            $annulment->set('type_name', $type_name, true);
            $annulment->set('description', $this->get('annul_description'), true);
            $annulment->set('fiscal_event_date', $this->get('annul_fiscal_event_date'), true);
            $annulment->setGroup($annulment->get('group'));
            $annulment->setDepartment($annulment->get('department'));

            // set link to invoice/credit/debit note before getting GT2 vars of annulment
            // in order to get VAT settings for incomes reason type (id > 100)
            $annulment->set('link_to_model_name', $this->modelName, true);
            $annulment->set('link_to', $this->get('id'), true);
            // advance invoice (and credit note for it) might be freely added and not related to a reason
            if ($this->get('advance') > PH_FINANCE_TYPE_MAX) {
                $annulment->set('parent_type', $this->get('advance'), true);
            }

            $annulment->getGT2Vars();
            $annulment_table = $annulment->get('grouping_table_2');
            $annulment_table['values'] = array();
            foreach ($table['values'] as $id => $values) {
                if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                    if ($table['values'][$id]['quantity'] < 0) {
                        $table['values'][$id]['quantity'] *= -1;
                    } elseif ($table['values'][$id][$table['calculated_price']] < 0) {
                        $table['values'][$id][$table['calculated_price']] *= -1;
                    }
                } else {
                    // negative price in advance or discount row
                    if ($table['values'][$id][$table['calculated_price']] < 0) {
                        $table['values'][$id][$table['calculated_price']] *= -1;
                    } else {
                        $table['values'][$id]['quantity'] *= -1;
                    }
                }
                $annulment_table['values'][$id] = $table['values'][$id];
            }
            $annulment->set('grouping_table_2', $annulment_table, true);
            $annulment->calculateGT2();
            $annulment->set('table_values_are_set', true, true);

            if (!$annulment->save()) {
                $db->FailTrans();
                return false;
            } else {
                if ($annulment->slashesEscaped) {
                    $annulment->slashesStrip();
                }
                require_once PH_MODULES_DIR . 'finance/models/finance.annulments.history.php';
                Finance_Annulments_History::saveData(
                    $this->registry,
                    array(
                        'action_type' => 'add',
                        'new_model' => $annulment,
                        'old_model' => $annulment,
                    )
                );
            }
        } else {
            if ($this->get('type') != PH_FINANCE_TYPE_PRO_INVOICE) {
                foreach ($table['values'] as $id => $values) {
                    if (empty($values)) continue;
                    $table['values'][$id]['deleted'] = 1;
                    $table['values'][$id]['quantity'] = 0;
                }
                $this->set('grouping_table_2', $table, true);
                $this->calculateGT2();
                $this->set('table_values_are_set', true, true);
            }
        }

        $this->set('new_handovered_status', 'none', true);
        $this->set('annulled', date('Y-m-d H:i:s'), true);
        $this->set('annulled_by', $this->registry['currentUser']->get('id'), true);
        $this->slashesEscape();
        // update income document
        $this->edit();

        if ($this->get('type') == PH_FINANCE_TYPE_INVOICE) {
            // update payment status of proforma to 'unpaid' when annulling invoice
            $this->updateProformaPaymentStatus();
        }

        $filters = array('where' => array('fir.id = ' . $this->get('id')));
        $new_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $new_reason->getGT2Vars();
        $new_reason->unlockIndexes();
        $new_reason->sanitize();

        //get if this is the last invoice issued from a contract
        $query = 'SELECT invoice_id, parent_id, `to`, advanced FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . "\n" .
                   'WHERE contract_id IN (SELECT contract_id FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' WHERE invoice_id = ' . $this->get('id') . ')' . "\n" .
                   ($this->get('advance') ? 'AND advanced != "none"' : 'AND advanced = "none"') . "\n" .
                   'ORDER BY `to` DESC' . "\n";
        $last_id = $db->GetRow($query);

        if (!empty($last_id)) {
            //invoice issued from a contract
            $filters = array('where' => array('fit.id = ' . $last_id['parent_id'], 'fit.deleted IS NOT NULL'));
            require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
            $tpl = Finance_Invoices_Templates::searchOne($this->registry, $filters);
            if ($last_id['invoice_id'] == $this->get('id') || $this->get('advance')) {
                //last or advanced invoice - just delete the info row
                $query = 'DELETE FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' WHERE invoice_id = ' . $this->get('id');
                $db->Execute($query);
                if (!$this->get('advance')) {
                    if (!$tpl->get('recurrent')) {
                        $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET status = "opened", next_issue_date = issue_date WHERE id = ' . $last_id['parent_id'];
                        $db->Execute($query);
                    } else {
                        //check if we have other invoices issued from the template of the invoice
                        $query = 'SELECT `to` as date_to FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . "\n" .
                                   'WHERE invoice_id > 0 AND parent_id = ' . $last_id['parent_id'] . "\n" .
                                   'ORDER BY `to` DESC';
                        $max_date = $db->GetRow($query);

                        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                        if (!empty($max_date)) {
                            $tpl->set('next_issue_date', '', true);
                            $tpl->updateAfterIssue($max_date);
                        } else {
                            //calc next issue date
                            if ($tpl->get('issue_start_direction') == 'before') {
                                $sign = '-';
                            } else {
                                $sign = '+';
                            }
                            if ($tpl->get('issue_start_point') == 'start') {
                                $date = $tpl->get('periods_start');
                            } else {
                                $tpl->getGT2Vars();
                                list(,$date) = $tpl->calculateRecurrence(array('date_from' => $tpl->get('periods_start')));
                            }
                            if ($tpl->get('issue_date_period_type') == 'working') {
                                $next_issue_date = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date,
                                        $tpl->get('issue_start_count'),
                                        $tpl->get('issue_date_direction'));
                            } else {
                                $next_issue_date = sprintf('%s %s %s', $sign, $tpl->get('issue_start_count'), 'day');
                                $next_issue_date = General::strftime('%y-%m-%d', strtotime($next_issue_date, strtotime($date)));
                            }
                            //mark the template as opened
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES .
                                     ' SET status = "opened", next_issue_date = "' . $next_issue_date . '" WHERE id = ' . $tpl->get('id');
                            $db->Execute($query);
                        }
                    }
                }
            } else {
                //not the last one - we will mark it as canceled
                $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' SET' . "\n" .
                           '  invoice_id = 0, observer_response = "cancel", response_date = NOW(),' . "\n" .
                           '  response_id = ' . $this->registry['currentUser']->get('id') . "\n" .
                           'WHERE invoice_id = ' . $this->get('id');
                $db->Execute($query);
            }
        } else {
            //this invoice is not from a contract
            //nothing to do
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
        $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                  array('action_type' => 'annul',
                                                                        'new_model' => $new_reason,
                                                                        'old_model' => $old_reason));

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Checks if annulment of invoice/proforma invoice/credit/debit notice is allowed
     *
     * @return bool - true if allowed, otherwise false
     */
    public function allowAnnul() {

        if ($this->get('status') != 'finished' ||
            $this->get('annulled_by') != 0 ||
            !in_array($this->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                                PH_FINANCE_TYPE_PRO_INVOICE,
                                                PH_FINANCE_TYPE_CREDIT_NOTICE,
                                                PH_FINANCE_TYPE_DEBIT_NOTICE))) {
            return false;
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $db = $this->registry['db'];

        $result = true;
        if ($this->get('type') == PH_FINANCE_TYPE_INVOICE) {
            // check if non-annulled credit/debit notes were issued from invoice
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND frr.parent_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND fir.annulled_by = 0' . "\n" .
                     '  AND fir.type IN (' . PH_FINANCE_TYPE_CREDIT_NOTICE . ',' . PH_FINANCE_TYPE_DEBIT_NOTICE . ')';
            $credits_debits = $db->GetAll($query);
            if ($credits_debits) {
                $result = false;
            }
        } elseif ($this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            // check if non-annulled invoice was issued from proforma
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.parent_id = fir.id AND frr.parent_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND fir.annulled_by = 0' . "\n" .
                     '  AND fir.type = ' . PH_FINANCE_TYPE_INVOICE;
            $invoice = $db->GetOne($query);
            if ($invoice) {
                $result = false;
            }
        }

        if ($result) {
            //check whether annulment report should issued
            $this->checkIssueAnnulmentReport();
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Defines whether an annulment report should be issued upon invoice annulment
     *
     * @return bool $add_report - true if report should be added
     */
    public function checkIssueAnnulmentReport() {
        if ($this->isDefined('add_report')) {
            return $this->get('add_report');
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        // get settings from the configuration
        // annul_with_report: defines whether to issue annulment report
        // IMPORTANT: if set to 1 ignores the issue_date/annulled dates relevance check
        $add_report = $this->registry['config']->getParam('finance', 'annul_with_report');
        // annul_after: defines the date of next month (after issue_date), after that date report should be issued
        //              The default value is 1, max value is 15
        //              Example:
        //              issue_date: 2015-09-25
        //              annulled (current date): 2015-10-07
        //              annul_after: 10 -> no annulment report (add_report is 0)
        //              annul_after: 5  -> annulment report (add_report is 1)
        $annul_after = (int) $this->registry['config']->getParam('finance', 'annul_after');
        //do not allow values greater than 15
        $annul_after = ($annul_after > 15) ? 15 : $annul_after;

        if ($this->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
            // no report if proforma
            $add_report = 0;
        } elseif (!$add_report && substr($this->get('issue_date'), 0, 7) != date('Y-m')) {
            // check the relevance of dates only if the months of issue_date and annulled (current date) differ
            if ($annul_after) {
                // compose the limit date (according to the setting annul_after)
                // it is the month after the issue date
                // the day of month is specified in $annul_after
                $date = new DateTime($this->get('issue_date'));
                $date->add(new DateInterval('P1M'));
                $date = sprintf($date->format('Y-m-') . '%02d', $annul_after);
                // IMPORTANT: the day (defined in annul_after) is excluded
                if ($date < date('Y-m-d')) {
                    $add_report = 1;
                } else {
                    $add_report = 0;
                }
            } else {
                //the setting is empty or is not an integer
                $add_report = 1;
            }
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        $this->set('add_report', $add_report, true);

        return $add_report;
    }

    /**
     * Updates 'distributed' status of model
     * (Set to distributed after distribution is saved or
     * set to not distributed when invoice/credit note/debit note
     * is issued from contract and no automatic distribution has been saved.
     * By default value is "there will be no distribution".)
     *
     * @return bool Result of operation
     */
    public function updateDistributed() {
        $db = $this->registry['db'];
        $db->StartTrans();

        $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                 'SET distributed="' . $this->get('distributed') . '"' . "\n" .
                 'WHERE id=' . $this->get('id');
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Overwrites method in main model so that type name is
     * replaced with a type-specific string if not present.
     *
     * (non-PHPdoc)
     * @see _libs/inc/mvc/Model::getModelTypeName()
     * @return string - Name of type of model
     */
    function getModelTypeName() {
        $type_name = $this->get('type_name');
        if (!$type_name) {
            $type = $this->get('type');
            switch ($type) {
                case PH_FINANCE_TYPE_INVOICE:
                    $type_name = $this->i18n('finance_incomes_reason_invoice');
                    break;
                case PH_FINANCE_TYPE_PRO_INVOICE:
                    $type_name = $this->i18n('finance_incomes_reasons_proforma_invoice');
                    break;
                case PH_FINANCE_TYPE_CREDIT_NOTICE:
                    $type_name = $this->i18n('finance_incomes_reasons_credit_notice');
                    break;
                case PH_FINANCE_TYPE_DEBIT_NOTICE:
                    $type_name = $this->i18n('finance_incomes_reasons_debit_notice');
                    break;
                case PH_FINANCE_TYPE_CORRECT_REASON:
                    $type_name = $this->i18n('finance_incomes_reasons_correct_reason');
                    break;
                default:
                    $type_name = $this->i18n('finance_incomes_reason');
                    break;
            }
        }

        return $type_name;
    }

    /**
     * Checks if the model has a print form or not
     *
     * @return bool - the result from the operation
     */
    public function checkForPrintForm() {
        // Set the registry
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // Count the GT2 rows saved for print form for this model
        $query  = 'SELECT COUNT(*) AS `pf_count` FROM `' . DB_TABLE_GT2_DETAILS . '` WHERE `model` = \'Finance_Incomes_Reason_Print\' AND `model_id` = \'' . $this->get('id') . '\'';
        $result = $this->registry['db']->GetOne($query);

        // Unset the registry
        if ($sanitize_after) {
            $this->sanitize();
        }

        // Check if there are any GT2 rows saved for print form
        if ($result > 0) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Gets container currency, conversion rate beween currencies of model and container,
     * calculates converted amount and sets them all to model.
     * Conversion rate is used when issuing bank payment from financial document and
     * bank account is in different currency than document.
     * Petty cash payments do not use conversion rate.
     *
     * @param mixed $companies_data - array of all options for Cashbox/Bank account field available to current user OR
     *                                false when no validation should be done if container is available to current user
     */
    public function prepareContainerRate($companies_data) {
        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // currency of model
        $currency = $this->get('currency');
        if (!$currency && $this->isDefined('grouping_table_2')) {
            $gt2 = $this->get('grouping_table_2');
            if (!empty($gt2['plain_values']['currency'])) {
                $currency = $gt2['plain_values']['currency'];
            }
            unset($gt2);
        }
        if (!$currency) {
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $currency = Finance_Currencies::getMain($this->registry);
        }

        // currency of container
        $container_currency = $currency;
        // conversion rate from currency of financial document to currency of container
        $container_rate = 1;
        // suggested amount for payment converted into currency of container
        $container_amount = round($this->get('total_with_vat') - $this->get('paid_amount') - $this->get('invoices_amount'), 2);

        if ($this->isDefined('company_data') && is_array($companies_data)) {
            // check if 'company_data' of model is among available options
            $option_found = false;
            foreach ($companies_data as $c => $c_data) {
                foreach ($c_data as $idx => $data) {
                    if ($data['option_value'] == $this->get('company_data')) {
                        $option_found = true;
                    }
                }
            }
            if (!$option_found) {
                $company_data = '0_0_0_0';
                // if there is only one available option, set it to model
                if (count($companies_data) == 1 && count(reset($companies_data)) == 1) {
                    $only_option = reset($companies_data);
                    $only_option = reset($only_option);
                    $company_data = $only_option['option_value'];
                }
                $this->set('company_data', $company_data, true);
            }
        }

        // get payment_type and container_id of financial document
        $payment_type = '';
        $container_id = 0;
        if ($this->isDefined('company_data') && preg_match('#^\d+_\d+_(cash|bank)_(\d+)$#', $this->get('company_data'), $matches)) {
            $payment_type = $matches[1];
            $container_id = $matches[2];
        } else {
            $payment_type = $this->get('payment_type');
            $container_id = $this->get('container_id');
        }
        if ($payment_type == 'bank' && $container_id > 0) {

            require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.model.php';
            $bank_account = new Finance_Bank_Account($this->registry, array('id' => $container_id));
            $container_currency = $bank_account->getCurrency();
            unset($bank_account);

            if ($container_currency && $currency != $container_currency) {
                // get conversion rate and calculate converted suggested amount
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $container_rate = round(Finance_Currencies::getRate($this->registry, $currency, $container_currency), 6);
                $container_amount = round($container_amount * $container_rate, 2);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        // set properties to model
        $this->set('currency', $currency, true);
        $this->set('container_currency', $container_currency, true);
        $this->set('container_rate', sprintf('%.6f', $container_rate), true);
        $this->set('container_amount', sprintf('%.2f', $container_amount), true);

        return true;
    }

    /*
     * Calculates fiscal event date depending of the company and type settings.
     * Also calculates the minimum and maximum allowed dates for fiscal event.
     */
    public function calculateFiscalEventDates() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
        $financeType = Finance_Documents_Types::searchOne($this->registry, array('where' => array('fdt.id = ' . $this->get('type'))));
        $company = Finance_Companies::searchOne($this->registry, array('where' => array('fc.id = ' . $this->get('company'))));
        $issue_date = $this->get('issue_date');

        $fiscal_event_date = '';
        //get the fiscal event date by type settings
        if ($financeType->get('default_fiscal_event_date_period_type') == 'working') {
            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $fiscal_event_date_type = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $issue_date,
                    $financeType->get('default_fiscal_event_date_count'), $financeType->get('default_fiscal_event_date_direction'));
        } elseif ($financeType->get('default_fiscal_event_date_direction') == 'before') {
            $fiscal_event_date_type = General::strftime('%Y-%m-%d',
                    strtotime('-' . $financeType->get('default_fiscal_event_date_count') . ' day',strtotime($issue_date)));
        } else {
            $fiscal_event_date_type = General::strftime('%Y-%m-%d',
                    strtotime('+' . $financeType->get('default_fiscal_event_date_count') . ' day', strtotime($issue_date)));
        }
        //get fiscal event date by company settings
        if ($company->get('fiscal_event_date_type') == 'working') {
            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $fiscal_event_date_company = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $issue_date,
                    $company->get('fiscal_event_date_indent'), $financeType->get('default_fiscal_event_date_direction'));
            //get the date in the other direction
            if ($financeType->get('default_fiscal_event_date_direction') == 'before') {
                $fiscal_event_date_company_ad = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $issue_date,
                        $company->get('fiscal_event_date_indent'), 'after');
            } else {
                $fiscal_event_date_company_ad = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $issue_date,
                        $company->get('fiscal_event_date_indent'), 'before');
            }
        } elseif ($financeType->get('default_fiscal_event_date_direction') == 'before') {
            $fiscal_event_date_company = General::strftime('%Y-%m-%d',
                    strtotime('-' . $company->get('fiscal_event_date_indent') . ' day', strtotime($issue_date)));
            //get the date in the other direction
            $fiscal_event_date_company_ad = General::strftime('%Y-%m-%d',
                    strtotime('+' . $company->get('fiscal_event_date_indent') . ' day', strtotime($issue_date)));
        } else {
            $fiscal_event_date_company = General::strftime('%Y-%m-%d',
                    strtotime('+' . $company->get('fiscal_event_date_indent') . ' day', strtotime($issue_date)));
            //get the date in the other direction
            $fiscal_event_date_company_ad = General::strftime('%Y-%m-%d',
                    strtotime('-' . $company->get('fiscal_event_date_indent') . ' day', strtotime($issue_date)));
        }

        //get the fiscal event date depending of the settings for the type and company
        //type settings are applyed first but company settings has priority
        if ($financeType->get('default_fiscal_event_date_direction') == 'before') {
            if ($fiscal_event_date_type < $fiscal_event_date_company) {
                $fiscal_event_date = $fiscal_event_date_company;
            } else {
                $fiscal_event_date = $fiscal_event_date_type;
            }
            $min_fiscal = $fiscal_event_date_company;
            $max_fiscal = $fiscal_event_date_company_ad;
        } else {
            if ($fiscal_event_date_type > $fiscal_event_date_company) {
                $fiscal_event_date = $fiscal_event_date_company;
            } else {
                $fiscal_event_date = $fiscal_event_date_type;
            }
            $min_fiscal = $fiscal_event_date_company_ad;
            $max_fiscal = $fiscal_event_date_company;
        }

        $this->set('fiscal_event_date', $fiscal_event_date, true);
        $this->set('fiscal_dates_warning', $this->i18n('warning_finance_incomes_reasons_invoice_fiscal_dates',
                array(General::strftime($this->i18n('date_short'), $min_fiscal), General::strftime($this->i18n('date_short'), $max_fiscal))), true);
        $this->set('min_fiscal', $min_fiscal, true);
        $this->set('max_fiscal', $max_fiscal, true);

        if ($sanitize_after) {
            $this->sanitize();
        }
    }

    /**
     * Updates financial data from customer into data of current financial document
     *
     * @return boolean - result of the operation
     */
    public function updateCustomerData() {
        $query =
            'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
            'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
            '  ON fir.id=firi18n.parent_id' . "\n" .
            'JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
            '  ON fir.customer=c.id' . "\n" .
            'JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
            '  ON c.id=ci18n.parent_id AND ci18n.lang=firi18n.lang' . "\n" .
            'SET fir.eik = IF(c.is_company=1, c.eik, c.ucn),' . "\n" .
            '    fir.vat_num = c.in_dds,' . "\n" .
            '    fir.modified = NOW(),' . "\n" .
            '    fir.modified_by = \'' . $this->registry['currentUser']->get('id') . '\',' . "\n" .
            '    firi18n.customer_name = IF(c.is_company=1, ci18n.company_name, CONCAT(ci18n.name, " ", ci18n.lastname)),' . "\n" .
            '    firi18n.customer_address = IF(c.is_company=1, ci18n.registration_address, ci18n.address_by_personal_id),' . "\n" .
            '    firi18n.received_by = IF(c.is_company=1, ci18n.mol, CONCAT(ci18n.name, " ", ci18n.lastname))' . "\n" .
            'WHERE fir.id=\'' . $this->get('id') . '\'';
        $this->registry['db']->Execute($query);

        return !$this->registry['db']->ErrorNo();
    }

    /**
     * Updates company data of a model
     *
     * @return bool - result of the operation
     */
    public function updateCompanyData() {

        $this->registry['db']->StartTrans();
        $set = array(
            'company = "' . $this->get('company') . '"',
            'office = "' . $this->get('office') . '"',
            'container_id = "' . $this->get('container_id') . '"',
            'payment_type = "' . $this->get('payment_type') . '"',
            'cheque = "' . $this->get('cheque') . '"',
            'modified = NOW()',
            'modified_by = ' . $this->registry['currentUser']->get('id')
        );
        $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS .
                 ' SET ' . implode(', ', $set) .
                 ' WHERE id = ' . $this->get('id');
        $this->registry['db']->Execute($query);
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Check if the model has direct payments to it
     *
     * @return array|false - array of payment ids or false if none found
     */
    public function getDirectPayments() {
        //get ids of payments relative to this model
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $query = 'SELECT DISTINCT(fp.id) FROM ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fb ' . "\n" .
                 '  ON fb.paid_amount > 0' .
                   ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE ?
                     ' AND fb.parent_model_name="' . $this->modelName . '" AND fb.parent_id=' . $this->get('id') . "\n" .
                     ' AND fb.paid_to_model_name="Finance_Payment" AND fb.paid_to=fp.id' :
                     ' AND fb.paid_to_model_name="' . $this->modelName . '" AND fb.paid_to=' . $this->get('id') . "\n" .
                     ' AND fb.parent_model_name="Finance_Payment" AND fb.parent_id=fp.id');
                 'WHERE fp.status="finished" AND fp.annulled_by=0';
        $ids = $this->registry['db']->GetCol($query);
        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
        if (empty($ids)) {
            return false;
        }
        return $ids;
    }

    /**
     * Updates date of payment of a model
     *
     * @return bool - result of the operation
     */
    public function updateDateOfPayment() {

        $this->registry['db']->StartTrans();
        $set = array(
            'date_of_payment = "' . $this->get('date_of_payment') . '"',
            'modified = NOW()',
            'modified_by = ' . $this->registry['currentUser']->get('id')
        );
        $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS .
        ' SET ' . implode(', ', $set) .
        ' WHERE id = ' . $this->get('id');
        $this->registry['db']->Execute($query);
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Updates data fields
     * ToDo: define data fields that are not only i18n
     *
     * @var array $data - assoc array field => value
     * @return bool - result of the operation
     */
    public function updateDataFields($data) {

        $set = array();

        foreach($data as $field => $value) {
            if (!in_array($field, $this->editableDataFields)) {
                return false;
            }
            $value = General::slashesEscape($value);
            $set[] = "`{$field}` = '{$value}'";
        }

        if (empty($set)) {
            return false;
        }


        $this->registry['db']->StartTrans();
        $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N .
            ' SET ' . implode(', ', $set) .
            ' WHERE parent_id = ' . $this->get('id') . ' AND lang="' . $this->get('model_lang') . '"';
        $this->registry['db']->Execute($query);

        $set = [
            'modified = NOW()',
            'modified_by = ' . $this->registry['currentUser']->get('id')
        ];
        $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS .
            ' SET ' . implode(', ', $set) .
            ' WHERE id = ' . $this->get('id');
        $this->registry['db']->Execute($query);
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Checks if VAT of parent reason of current invoice should be added or removed.
     * This should happen only when settings for reason type+company are include_VAT = 1
     * (VAT is applied only when reason is invoiced) AND
     * first invoice is added (add VAT) or last invoice is annulled/deactivated (remove VAT).
     *
     * @param boolean $check_add - if true check if VAT should be added, if false check if VAT should be removed
     * @param object $reason - model of parent reason
     * @return boolean true - if VAT of parent reason should be updated, otherwise false
     */
    public function checkUpdateReasonVAT($check_add = true, $reason = null) {
        // if any of these conditions is met, do not continue
        if ($this->get('type') != PH_FINANCE_TYPE_INVOICE || empty($reason) ||
        $reason && ($reason->modelName != 'Finance_Incomes_Reason' || $reason->get('type') <= PH_FINANCE_TYPE_MAX)) {
            return false;
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $query = 'SELECT include_VAT' . "\n" .
                 'FROM ' . DB_TABLE_TYPES_VAT_OPTIONS . "\n" .
                 'WHERE model="Finance_Incomes_Reason" AND model_type="' . $reason->get('type') . '" AND company = "' . $reason->get('company') . '"';
        $include_VAT = $db->GetOne($query);

        $result = false;
        if ($include_VAT == 1) {
            // search for active and not annulled invoices
            $invoice_filters = array('fir.annulled_by = 0', 'fir.active = 1');
            // do not include current invoice (because the relation might be already saved)
            if ($check_add && $this->get('id') > 0) {
                $invoice_filters[] = 'fir.id != ' . $this->get('id');
            }
            $reason->getRelatives(array('get_invoices' => $invoice_filters));

            if (!$reason->get('invoices')) {
                if ($check_add) {
                    // checking if VAT should be added
                    if ($reason->get('total_vat_rate') != $this->get('total_vat_rate')) {
                        // yes, VAT should be added
                        $result = true;
                    }
                } else {
                    // checking if VAT should be removed
                    if ($reason->get('total_vat_rate') > 0) {
                        // yes, VAT should be removed
                        $result = true;
                    }
                }
            }

            $reason->unsetProperty('invoices', true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Updates (adds/removes) VAT of reason model and any correcting documents
     * (when VAT settings for type+company are include_VAT=1)
     *
     * @param array $params - new VAT rate and no VAT reason properties; if rate is 0, VAT is removed, if rate > 0, VAT is added
     * @return boolean - result of the operations
     */
    public function updateReasonVAT($params = array()) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $this->registry->set('get_old_vars', true, true);
        $this->getGT2Vars();
        $gt2 = $this->get('grouping_table_2');
        foreach ($params as $key => $value) {
            $this->set($key, $value, true);
            $gt2['plain_values'][$key] = $value;
        }
        $this->set('grouping_table_2', $gt2, true);
        $this->set('table_values_are_set', true, true);
        $this->slashesEscape();
        $result = $this->edit();

        if ($result) {
            // processing of correction documents
            $this->getRelatives(array('get_correct_reasons' => true));
            $correct_reasons = $this->get('correct_reasons');
            $this->unsetProperty('correct_reasons', true);
            if (!empty($correct_reasons)) {
                foreach ($correct_reasons as $reason) {
                    $reason->unsanitize();
                    $reason->getGT2Vars();
                    $gt2 = $reason->get('grouping_table_2');
                    foreach ($params as $key => $value) {
                        $reason->set($key, $value, true);
                        $gt2['plain_values'][$key] = $value;
                    }
                    $reason->set('grouping_table_2', $gt2, true);
                    $reason->set('table_values_are_set', true, true);
                    $reason->slashesEscape();
                    $result = $reason->edit();
                    $reason->sanitize();

                    if (!$result) {
                        break;
                    }
                }
            }
            unset($correct_reasons);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Check if the current incomes reason can be annulled
     *
     * @return boolean - result of the operations
     */
    public function checkAnnul() {
        $allow_annul = true;
        if ($this->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
            // check if there is a correction document for the credit notice
            $sql = 'SELECT `id`, `num` FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' ' . "\n" .
                   'WHERE `reason`="' . $this->get('id') . '" AND `type`="' . PH_FINANCE_TYPE_CORRECT_REASON .  '"' . "\n";
            $correction_data = $this->registry['db']->GetRow($sql);

            if (!empty($correction_data)) {
                $allow_annul = false;
                $link_to_last_doc = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=view&amp;view=%d',
                                            $_SERVER['PHP_SELF'],$this->registry['module_param'],$this->registry['controller_param'], $correction_data['id']);
                $this->raiseError('error_finance_incomes_reasons_credit_notice_annul_not_allowed', '', 0, array($this->get('num'), $link_to_last_doc, $correction_data['num']));
            }
        }

        return $allow_annul;
    }

    public function prepareDuplicates($action) {
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        //duplicate fields for price, quantity, surplus, and discount
        //as we have to know if one of the fields is changed and we must lock the other one
        //This is to prevent several changes in one row as we don't have
        //extreme math logic in the software
        $gt2 = $this->get('grouping_table_2');
        $duplicate = array('quantity', $gt2['calculated_price'], 'discount_value', 'discount_percentage', 'surplus_value', 'surplus_percentage');
        $request = $this->registry['request'];
        if (!$request->isPost() && $action == 'edit') {
            // we will get values from the parent reason
            // in function of the rows links
            $query = 'SELECT rows_links FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     'WHERE parent_model_name = "Finance_Incomes_Reason" AND link_to_model_name = "Finance_Incomes_Reason" AND parent_id = ' . $this->get('id');
            $rls = $this->registry['db']->GetOne($query);
            $rls = array_filter(preg_split('#\r\n|\n|\r#', $rls));
            if (!empty($rls)) {
                $rl = array();
                foreach ($rls as $r) {
                    $r = preg_split('#\s*=>\s*#', $r);
                    if (isset($r[1])) {
                        $rl[$r[0]] = $r[1];
                    }
                }
                if (!empty($rl)) {
                    $query = 'SELECT id, ' . implode(', ', $duplicate) . ' FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE id IN (' . implode(', ', $rl) . ')';
                    $data = $this->registry['db']->GetAssoc($query);
                }
            }
        }
        foreach ($duplicate as $k => $d) {
            $gt2['vars'][$d . '_duplicate'] = $gt2['vars'][$d];
            $gt2['vars'][$d . '_duplicate']['hidden'] = 1;
            $gt2['vars'][$d . '_duplicate']['name'] = $d . '_duplicate';
            $gt2['vars'][$d . '_duplicate']['label'] .= ' duplicate';
            unset($gt2['vars'][$d . '_duplicate']['custom_class']);
            $gt2['vars'][$d]['js_methods']['onchange'] = 'preventCorrectionColisions(this, true)';

            foreach ($gt2['values'] as $key => $values) {
                //get control values for quantity and price
                if ($request->isPost()) {
                    //from the request(after error)
                    $v = $request->get($d . '_duplicate');
                    $gt2['values'][$key][$d . '_duplicate'] = $v[$key];
                } elseif ($action == 'edit') {
                    if (isset($rls[$key]) && isset($data[$rls[$key]][$d])) {
                        $gt2['values'][$key][$d . '_duplicate'] = $data[$rls[$key]][$d];
                    }
                } else {
                    //from the original values
                    $gt2['values'][$key][$d . '_duplicate'] = $values[$d];
                }
            }
        }
        $this->set('grouping_table_2', $gt2, true);
        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
    }

    /**
     * Get the name of the user that has issued the document
     */
    public function getIssueByName() {
        if (!$this->get('issue_by_name')) {
            if ($this->sanitized) {
                $this->unsanitize();
                $sanitize_after = true;
            }

            $filters_users = array('where' => array('u.id=' . $this->get('issue_by')),
                'sanitize' => true,
                'model_lang' => $this->get('model_lang'));
            $issue_by_user = Users::searchOne($this->registry, $filters_users);
            $this->set('issue_by_name', ($issue_by_user ? trim($issue_by_user->get('firstname') . ' ' . $issue_by_user->get('lastname')) : ''), true);

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $this->get('issue_by_name');
    }

     /**
     * Gets value for export
     *
     * @param array $field - array as prepared by the Outlook (getOutlookSettings)
     * @param bool $force - IMPORTANT: set to true to get data without formatting, set to 0 to get autocompleter as link, if having such settings
     * @param bool $ignore_permissions - if true, do not check layout permission (when getting data for autocompleter fill options, for example)
     * @return mixed - the value of the variable
     */
    public function getExportVarValue($field, $force = false, $ignore_permissions = false) {
        switch($field['name']) {
            case 'relatives_parent':
                $relatives = $this->getFirstLevelRelatedIncomesReasons('parent');
                return is_array($relatives) ? implode("\n", array_column($relatives, 'num')) : '';
                break;
            case 'relatives_children':
                $relatives = $this->getFirstLevelRelatedIncomesReasons('child');
                return is_array($relatives) ? implode("\n", array_column($relatives, 'num')) : '';
                break;
            default:
                return parent::getExportVarValue($field, $force = false, $ignore_permissions = false);
        }
    }
        }

?>
