<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

/**
 * Finance_Payment model class
 */
Class Finance_Payment extends Model {
    use BelongsToTrait;

    public $modelName = 'Finance_Payment';

    public $counter;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('num', 'issue_date', 'customer_name', 'company_name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        $this->getTypeName();

        if ($this->get('company') && !$this->get('company_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . ' WHERE parent_id=' . $this->get('company');
            $company_name = $registry['db']->GetOne($query);
            $this->set('company_name', $company_name, true);
        }

        if ($this->get('office') && !$this->get('office_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                     'WHERE parent_id=' . $this->get('office');
            $company_name = $registry['db']->GetOne($query);
            $this->set('office_name', $company_name, true);
        }

        if ($this->get('annulled_by') && !$this->get('annulled_by_name')) {
            $query = 'SELECT CONCAT(ui18n.firstname, " ", ui18n.lastname) as annulled_by_name FROM ' . DB_TABLE_USERS_I18N . ' AS ui18n WHERE ui18n.parent_id=' . $this->get('annulled_by') . ' AND ui18n.lang="' . $registry['lang'] . '"';
            $annulled_by_name = $registry['db']->GetOne($query);
            $this->set('annulled_by_name', $annulled_by_name, true);
        }
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if (!$this->get('id') && !$this->get('type')) {
            $this->raiseError('error_finance_payments_no_type_specified', 'type', null,
                              array($this->getLayoutName('type')));
        }
        if ($this->registry->get('action') != 'translate' &&
        // do not validate annulled payment but validate its new copy
        ($this->registry->get('action') != 'annul' || !$this->get('id'))) {
            if (!$this->get('amount') || round($this->get('amount'), 2) == 0) {
                $this->raiseError('error_finance_payments_no_amount_specified', 'amount', null,
                                  array($this->getLayoutName('amount')));
            }

            if (($this->get('type') == 'PKO' || $this->get('type') == 'RKO') && !$this->get('currency')) {
                $this->raiseError('error_finance_payments_no_currency_specified', 'currency', null,
                                  array($this->getLayoutName('currency')));
            }

            if (($this->get('type') == 'BP' || $this->get('type') == 'PN') &&
            $this->get('currency') != $this->get('container_currency') && round($this->get('container_rate'), 6) <= 0) {
                $this->raiseError('error_finance_payments_no_container_rate', 'container_rate', null,
                                  array($this->get('currency'), $this->get('container_currency')));
            }

            if (!$this->get('customer') && $this->get('type') != 'TR') {
                $this->raiseError('error_finance_payments_no_customer_specified', 'customer', null,
                                  array($this->getLayoutName('customer')));
            }
            if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
                $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
            }

            //set container_type used in counter
            if (($this->get('type') == 'PKO' || $this->get('type') == 'RKO')) {
                $this->set('container_type', 'cashbox', true);
            } elseif (($this->get('type') == 'BP' || $this->get('type') == 'PN')) {
                $this->set('container_type', 'bank_account', true);
            }

            //check counter
            if ($this->get('company') && $this->get('type') && !$this->getCounter()) {
                $this->raiseError('error_finance_payments_no_counter', 'type', 0,
                                  array($this->get('type_name'), $this->get('company_name'), $this->get('office_name')));
            }

            if ($this->get('status') == 'finished' && !$this->get('issue_date')) {
                $this->raiseError('error_finance_payments_no_issue_date', 'issue_date', null,
                                  array($this->getLayoutName('issue_date')));
            }

            // no cashbox or bank account
            if (!$this->get('container_id')) {
                $this->raiseError('error_finance_payments_no_container_id', 'company_data', null,
                                  array($this->getLayoutName('company_data')));
            } elseif ($this->get('status') == 'finished') {
                if ($this->get('type') == 'PN') {
                    //amount could not be more than the available amount in the bank account unless the credit limit is covered
                    require_once 'finance.bank_accounts.factory.php';
                    $container_id = $this->get('container_id');
                    $bank_account = Finance_Bank_Accounts::searchOne($this->registry,
                                                                     array('where' => array('fba.id=' . $container_id)));
                    if ($bank_account) {
                        // amount that will be subtracted from bank account
                        $container_rate = $this->isDefined('container_rate') ? round($this->get('container_rate'), 6) : 1;
                        $container_amount = round(round($this->get('amount'), 2) * $container_rate, 2);
                        if ($bank_account->getAmount() + $bank_account->get('credit_limit') - $container_amount < 0) {
                            $this->raiseError('error_less_credit_limit', 'amount', null,
                                              array($this->getLayoutName('amount', false)));
                        }
                    } else {
                        // container not found
                        $this->raiseError('error_finance_payments_no_container_id', 'company_data', null,
                                          array($this->getLayoutName('company_data')));
                    }
                } elseif ($this->get('type') == 'RKO') {
                    //amount could not be more than the available amount in the cash box
                    require_once 'finance.cashboxes.factory.php';
                    $container_id = $this->get('container_id');
                    $cashbox = Finance_Cashboxes::searchOne($this->registry,
                                                            array('where' => array('fcb.id=' . $container_id)));
                    if ($cashbox) {
                        $cashbox_amounts = $cashbox->getAmount();
                        $cashbox_amount = isset($cashbox_amounts[$this->get('currency')]) ? $cashbox_amounts[$this->get('currency')] : 0;
                        if ($cashbox_amount - round($this->get('amount'), 2) < 0) {
                            $this->raiseError('error_less_cashbox_amount', 'amount', null,
                                              array($this->getLayoutName('amount', false)));
                        }
                    } else {
                        // container not found
                        $this->raiseError('error_finance_payments_no_container_id', 'company_data', null,
                                          array($this->getLayoutName('company_data')));
                    }
                }
            }
        }

        return $this->valid;
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = '', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Finance_Payment->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Payment ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        if ($this->get('annulled_by')) {
            switch ($action) {
                case 'edit':
                case 'translate':
                case 'balance':
                case 'relatives':
                case 'annul':
                case 'attachments':
                case 'tag':
                case 'tags_view':
                case 'tags_edit':
                case 'communications':
                case 'comments':
                case 'email':
                case 'generate':
                case 'print':
                    return false;
                default:
                    return $this->isActionAllowed($action, $modulePermissionsKey, $force);
            }
            return false;
        } elseif ($action == 'edit') {
            if ($this->get('status') == 'finished') {
                return false;
            } else {
                // check container permissions as well
                return $this->isActionAllowed($action, $modulePermissionsKey, $force)
                    && $this->checkContainerPermissions($action);
            }
        } else {
            return $this->isActionAllowed($action, $modulePermissionsKey, $force);
        }
    }

    /**
     * @throws Exception
     */
    private function isActionAllowed($action, $modulePermissionsKey, $force): bool
    {
        // Recreate the logics from parent::checkPermissions
        if (!$modulePermissionsKey) {
            $registry = $GLOBALS['registry'];
            if ($registry['module'] != $this->getModule()) {
                return true;
            }

            $modulePermissionsKey = $this->module;
            if ($registry['module'] != $registry['controller']) {
                $modulePermissionsKey .= '_' . $registry['controller'];
            }
        }

        return PermissionsChecker::getCurrentUserInstance()->isAllowed(
            $this,
            $action,
            $modulePermissionsKey,
            $force
        );
    }

    /**
     * Check permissions for container - cashbox or bank account - for specified action.
     *
     * @param string $action - action parameter
     * @return boolean - result of the operation
     */
    public function checkContainerPermissions($action) {

        $result = true;
        if (!in_array($action, array('view', 'edit', 'balance'))) {
            return $result;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        if ($this->get('container_type') == 'bank_account') {
            $finance_bank_accounts = $this->registry['currentUser']->get('finance_bank_accounts');
            if ($finance_bank_accounts && is_array($finance_bank_accounts)
                && isset($finance_bank_accounts[$this->get('type')][$action])
                && in_array($this->get('container_id'), $finance_bank_accounts[$this->get('type')][$action])) {

            } else {
                $result = false;
            }
        } else {
            $finance_cashboxes = $this->registry['currentUser']->get('finance_cashboxes');
            if ($finance_cashboxes && is_array($finance_cashboxes)
                && isset($finance_cashboxes[$this->get('type')][$action])
                && in_array($this->get('container_id'), $finance_cashboxes[$this->get('type')][$action])) {

            } else {
                $result = false;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        if ($this->get('import_added')) {
            $set['added']     = sprintf("added='%s'", $this->get('import_added'));
        } else {
            $set['added']     = sprintf("added=now()");
        }
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));


        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_finance_payments_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
            } else {
                $this->counter->increment();
            }
        }

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_FINANCE_PAYMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new finance payment base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_finance_payments_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
            } else {
                $this->counter->increment();
            }
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_PAYMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('reason')){
            $update['reason']  = sprintf("reason='%s'", $this->get('reason'));
        }
        if ($this->isDefined('note')){
            $update['note']  = sprintf("note='%s'", $this->get('note'));
        }

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        //query to insert/update the i18n table for the selected model language
        $query2 = 'INSERT' . ($update ? '' : ' IGNORE') . ' INTO ' . DB_TABLE_FINANCE_PAYMENTS_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  ($update ? 'ON DUPLICATE KEY UPDATE ' . implode(', ', $update) : '');

        $db->Execute($query2);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
            $this->registry['logger']->dbError('editing finance payment i18n details', $db, $query2);
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Update relatives of the model
     *
     * @param object $model - relative model
     * @return bool - result of the operation
     */
    public function insertRelatives($model) {
        $db = $this->registry['db'];
        //UPDATE RELATIVES OF THE MODEL
        $insert = array();
        $insert['parent_id'] = sprintf("parent_id='%d'", $this->get('id'));
        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", $this->modelName);
        $insert['link_to'] = sprintf("link_to='%d'", $model->get('id'));
        $insert['link_to_model_name'] = sprintf("link_to_model_name='%s'", $model->modelName);

        //query to insert relatives of payment
        $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_PAYMENTS_RELATIVES . "\n" .
                  'SET ' . implode(', ', $insert);

        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * Get payment/transfer that transaction expenses (TR) payment was added for.
     *
     * @return array - array with id and model name of payment/transfer
     */
    public function getTransactionRelative() {
        $query = 'SELECT link_to, link_to_model_name ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_PAYMENTS_RELATIVES . "\n" .
                 'WHERE parent_model_name="Finance_Payment" AND parent_id=' . $this->get('id') . "\n" .
                 '  AND link_to_model_name IN ("Finance_Payment", "Finance_Transfer")';
        return $this->registry['db']->GetRow($query);
    }

    /**
     * Update payment for incomes
     *
     * @return bool - result of the operation
     */
    public function updateIncomesBalance() {

        $db = &$this->registry['db'];

        // if a started transaction has already failed (because of
        // before_action automations), do not perform actions
        if ($db->HasFailedTrans()) {
            return false;
        }

        $db->StartTrans();

        $this->getCustomerDocuments($this->get('selected_tab'));
        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');
        $all_amounts = 0;
        if ($this->get('relatives_payments')) {
            $all_amounts += array_sum($this->get('relatives_payments'));
            $all_amounts = round($all_amounts, 2);
            if (bccomp($all_amounts, $this->get('remaining_amount'), 2) == 1) {
                // set error message
                $this->raiseError('error_finance_payments_allocated_amount_total');
                $db->FailTrans();
                $db->CompleteTrans();
                return false;
            }
        } else {
            $db->FailTrans();
            $db->CompleteTrans();
            return false;
        }

        //get repayment plan if existing
        require_once 'finance.repayment_plans.factory.php';
        $filters = array('where' => array('frp.customer = ' . $this->get('customer'),
                                          'frp.company = ' . $this->get('company'),
                                          'frp.status = "locked"'),
                         'model_lang' => $this->get('lang'));
        $repayment_plan = Finance_Repayment_Plans::searchOne($this->registry, $filters);
        $repayment_data = array();
        $repayment_incomes_ids = array();
        if ($repayment_plan) {
            $query = 'SELECT fir.id ' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . ' as frpi ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir ' . "\n" .
                     '  ON fir.id=frpi.incomes_id ' . "\n" .
                     'WHERE frpi.parent_id=' . $repayment_plan->get('id') . ' AND fir.payment_status != "paid" AND fir.annulled_by=0';
            $repayment_incomes_ids = $db->GetCol($query);
            $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . ' as frpa ' . "\n" .
                     'WHERE frpa.parent_id=' . $repayment_plan->get('id') . ' AND frpa.amount != frpa.paid_amount' . "\n" .
                     'ORDER BY deadline ASC';
            $repayment_data = $db->GetAll($query);
        }

        if (in_array($this->get('selected_tab'), array('invoice', 'proforma', 'reason'))) {
            // pay amounts to incomes reasons/invoices/proformas
            $amounts = $this->get('relatives_payments');
            $reasons = $this->get('incomes_reasons');
            foreach ($amounts as $key => $amount) {
                if ($amount > 0) {
                    if (isset($reasons[$key])) {
                        if ($this->get('selected_tab') == 'proforma') {
                            // get remaining amount dynamically because we might be paying proformas from the same reason
                            $tmp_compare = $reasons[$key]->getRemainingAmount();
                            if ($tmp_compare < $amount) {
                                $amount = $tmp_compare;
                            }
                            if (bccomp($amount, 0, 2) < 1) {
                                continue;
                            }
                        } else {
                            $tmp_compare = round($reasons[$key]->get('total_with_vat') - $reasons[$key]->get('paid_amount') - ($this->get('selected_tab') == 'reason' ? $reasons[$key]->get('invoices_amount') : 0), 2);
                        }
                    }
                    if (!isset($reasons[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        // set error message
                        $this->raiseError('error_finance_payments_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    // save payment balance
                    $this->updateBalance(array('model_id' => $key,
                                               'model_name' => 'Finance_Incomes_Reason',
                                               'amount' => $amount));

                    if (in_array($reasons[$key]->get('id'), $repayment_incomes_ids)) {
                        foreach ($repayment_data as $parent_id => $row) {
                            if ($row['amount'] > $row['paid_amount']) {
                                $paid_amount = $row['amount'] - $row['paid_amount'];
                                $paid_amount = round($paid_amount, 2);
                                $paid_amount = $paid_amount <= $amount ? $paid_amount : $amount;
                                $amount -= $paid_amount;
                                $amount = round($amount, 2);
                                $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                                         'SET paid_amount=paid_amount+' . $paid_amount . ', last_payment=now()' . "\n" .
                                         'WHERE parent_id=' . $row['parent_id'] . ' AND deadline="' . $row['deadline'] . '"';
                                $db->Execute($query);
                                $repayment_data[$parent_id]['paid_amount'] += $paid_amount;
                                $repayment_plan->set('amount', $paid_amount, true);
                                require_once 'finance.repayment_plans.history.php';
                                Finance_Repayment_Plans_History::saveData($this->registry,
                                                                          array('model' => $repayment_plan,
                                                                                'action_type' => 'payment',
                                                                                'new_model' => $repayment_plan,
                                                                                'old_model' => $repayment_plan));
                                //check amount and paid amount
                                $query = 'SELECT SUM(amount) as amount, SUM(paid_amount) as paid_amount ' . "\n" .
                                         'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                                         'WHERE parent_id=' . $row['parent_id'];
                                $amount_sum = $db->GetRow($query);
                                if ($amount_sum['amount'] == $amount_sum['paid_amount']) {
                                    //update repayment plan status - finished, executed
                                    $set = array();
                                    $set['modified'] = sprintf("`modified`=now()");
                                    $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
                                    $set['status_modified'] = sprintf("`status_modified`=now()");
                                    $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
                                    $set['status'] = "`status`='finished'";
                                    $set['substatus'] = sprintf("`substatus`=%d", PH_FINANCE_EXECUTED_STATUS);
                                    $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS . "\n" .
                                             'SET ' . implode(', ', $set) . "\n" .
                                             'WHERE id=' . $row['parent_id'];
                                    $db->Execute($query);
                                }
                                if ($amount <= 0) {
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } elseif ($this->get('selected_tab') == 'payment') {
            // pay amounts to opposite payments
            $amounts = $this->get('relatives_payments');
            $opposite_payments = $this->get('opposite_payments');
            foreach ($amounts as $key => $amount) {
                if ($amount > 0) {
                    $tmp_compare = round($opposite_payments[$key]->get('amount') - $opposite_payments[$key]->get('paid_amount'), 2);
                    if (!isset($opposite_payments[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        // set error message
                        $this->raiseError('error_finance_payments_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    // save payment balance
                    $this->updateBalance(array('model_id' => $key,
                                               'model_name' => 'Finance_Payment',
                                               'amount' => $amount));
                }
            }
        } elseif ($this->get('selected_tab') == 'credit') {
            // pay amounts to expenses credit notices
            $amounts = $this->get('relatives_payments');
            $credit_notices = $this->get('credit_notices');
            foreach ($amounts as $key => $amount) {
                if ($amount > 0 && isset($credit_notices[$key])) {
                    /**
                     * NOTE: 'total_with_vat' has been set to a positive value in
                     * @see _libs/modules/finance/models/Finance_Payment::getCustomerDocuments()
                     */
                    $tmp_compare = round($credit_notices[$key]->get('total_with_vat') - $credit_notices[$key]->get('paid_amount'), 2);
                    if (!isset($credit_notices[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        // set error message
                        $this->raiseError('error_finance_payments_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    // save payment balance
                    $this->updateBalance(array('model_id' => $key,
                                               'model_name' => 'Finance_Expenses_Reason',
                                               'amount' => $amount));
                }
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update payment for expenses
     *
     * @return bool - result of the operation
     */
    public function updateExpensesBalance() {

        $db = &$this->registry['db'];

        // if a started transaction has already failed (because of
        // before_action automations), do not perform actions
        if ($db->HasFailedTrans()) {
            return false;
        }

        $db->StartTrans();

        $this->getCustomerDocuments($this->get('selected_tab'));
        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');
        $all_amounts = 0;
        if ($this->get('relatives_payments')) {
            $all_amounts += array_sum($this->get('relatives_payments'));
            $all_amounts = round($all_amounts, 2);
            if (bccomp($all_amounts, $this->get('remaining_amount'), 2) == 1) {
                // set error message
                $this->raiseError('error_finance_payments_allocated_amount_total');
                $db->FailTrans();
                $db->CompleteTrans();
                return false;
            }
        } else {
            $db->FailTrans();
            $db->CompleteTrans();
            return false;
        }

        if (in_array($this->get('selected_tab'), array('invoice', 'proforma', 'reason'))) {
            // pay amounts to expenses reasons/invoices/proformas
            $amounts = $this->get('relatives_payments');
            $reasons = $this->get('expenses_reasons');
            foreach ($amounts as $key => $amount) {
                if ($amount > 0) {
                    $tmp_compare = round($reasons[$key]->get('total_with_vat') - $reasons[$key]->get('paid_amount'), 2);
                    if (!isset($reasons[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        // set error message
                        $this->raiseError('error_finance_payments_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    // save payment balance
                    $this->updateBalance(array('model_id' => $key,
                                               'model_name' => 'Finance_Expenses_Reason',
                                               'amount' => $amount));
                }
            }
        } elseif ($this->get('selected_tab') == 'payment') {
            // pay amounts to opposite payments
            $amounts = $this->get('relatives_payments');
            $opposite_payments = $this->get('opposite_payments');
            foreach ($amounts as $key => $amount) {
                if ($amount > 0) {
                    $tmp_compare = round($opposite_payments[$key]->get('amount') - $opposite_payments[$key]->get('paid_amount'), 2);
                    if (!isset($opposite_payments[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        // set error message
                        $this->raiseError('error_finance_payments_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    // save payment balance
                    $this->updateBalance(array('model_id' => $key,
                                               'model_name' => 'Finance_Payment',
                                               'amount' => $amount));
                }
            }
        } elseif ($this->get('selected_tab') == 'credit') {
            // pay amounts to credit notices
            $amounts = $this->get('relatives_payments');
            $credit_notices = $this->get('credit_notices');
            foreach ($amounts as $key => $amount) {
                if ($amount > 0 && isset($credit_notices[$key])) {
                    /**
                     * NOTE: 'total_with_vat' has been set to a positive value in
                     * @see _libs/modules/finance/models/Finance_Payment::getCustomerDocuments()
                     */
                    $tmp_compare = round($credit_notices[$key]->get('total_with_vat') - $credit_notices[$key]->get('paid_amount'), 2);
                    if (!isset($credit_notices[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        // set error message
                        $this->raiseError('error_finance_payments_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    // save payment balance
                    $this->updateBalance(array('model_id' => $key,
                                               'model_name' => 'Finance_Incomes_Reason',
                                               'amount' => $amount));
                }
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Automatically allocates a payment to available records in chronological order
     *
     * @return int - result of the operation: 1 on success, 0 on error, -1 if nothing to allocate to
     */
    public function saveAutoBalance() {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        // subtract paid amount just in case
        $remaining_amount = bcsub($this->get('amount'), $this->getPaidAmount(), 2);

        if (bccomp($remaining_amount, 0, 2) < 1) {
            if ($unsanitize) {
                $this->sanitize();
            }
            return -1;
        }

        $records = array();
        foreach (array('invoice', 'proforma', 'reason', 'credit', 'payment') as $tab_index => $selected_tab) {
            $this->getCustomerDocuments($selected_tab);
            switch ($selected_tab) {
                case 'credit':
                    $prop = 'credit_notices';
                    break;
                case 'payment':
                    $prop = 'opposite_payments';
                    break;
                default:
                    $prop = $this->get('receive_flag') == 1 ? 'incomes_reasons' : 'expenses_reasons';
                    break;
            }
            $records_tab = $this->get($prop) ?: array();
            foreach ($records_tab as &$rec) {
                $rec->set('tab_index', $tab_index, true);
            }
            // save yourself from trouble and unset the variable reference (see the while cycle below)
            unset($rec);
            $this->unsetProperty($prop);
            $records = array_merge($records, $records_tab);
        }

        if (!$records) {
            if ($unsanitize) {
                $this->sanitize();
            }
            return -1;
        }

        // sort records by issue_date ASC, added ASC, tab_index ASC, id ASC
        usort($records, function($a, $b) {
            if ($a->get('issue_date') != $b->get('issue_date')) {
                return $a->get('issue_date') < $b->get('issue_date') ? -1: 1;
            } elseif ($a->get('added') != $b->get('added')) {
                return $a->get('added') < $b->get('added') ? -1 : 1;
            } elseif ($a->get('tab_index') != $b->get('tab_index')) {
                return $a->get('tab_index') < $b->get('tab_index') ? -1 : 1;
            } else {
                return $a->get('id') < $b->get('id') ? -1 : 1;
            }
        });

        $old_payment = clone $this;
        $old_payment->getRelatives();

        $db = &$this->registry['db'];
        $db->StartTrans();

        reset($records);
        // cycle until end of records or no remaining amount - whichever comes first
        while ((list(,$rec) = each($records)) !== false && bccomp($remaining_amount, 0, 2) == 1) {
            $amount = $rec->modelName == 'Finance_Payment' ? $rec->get('amount') : abs($rec->get('total_with_vat'));
            // if record is a reason/proforma, paid amount should be taken dynamically
            // as those two are limited by each other (the other record might have been paid in a previous iteration)
            if ($rec->modelName == 'Finance_Incomes_Reason') {
                if ($rec->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
                    $reason_available_amount = $rec->getRemainingAmount();
                    if ($reason_available_amount < $rec->get('total_with_vat') - $rec->get('paid_amount')) {
                        // the amount from the proforma that can be paid
                        // (set greater paid amount than it really is)
                        $rec->set('paid_amount', $rec->get('total_with_vat') - $reason_available_amount, true);
                    }
                } elseif ($rec->get('type') > PH_FINANCE_TYPE_MAX) {
                    $rec->unsanitize();
                    // get directly paid amount
                    $rec->getPaidAmount();
                    // add paid amount to non-invoiced proformas - force getting it from db
                    $rec->set('paid_amount', $rec->get('paid_amount') + $rec->getProformasPaidAmount(true), true);
                    $rec->sanitize();
                }
            } elseif ($rec->modelName == 'Finance_Expenses_Reason') {
                if ($rec->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE || $rec->get('type') > PH_FINANCE_TYPE_MAX) {
                    $rec->unsanitize();
                    $rec->getPaidAmount();
                    // check for paid relative (proforma/reason)
                    if ($rec->getPaidRelative()) {
                        // add paid amount to relative so that total amount does not get overpaid
                        $rec->set('paid_amount', $rec->get('paid_amount') + (double)$rec->get('relative_paid_amount'), true);
                    }
                    $rec->sanitize();
                }
            }
            $amount = bcsub($amount, $rec->get('paid_amount'), 2);
            if ($rec->isDefined('invoices_amount')) {
                $amount = bcsub($amount, $rec->get('invoices_amount'), 2);
            }

            if (bccomp($amount, 0, 2) < 1) {
                continue;
            }
            if (bccomp($amount, $remaining_amount, 2) == 1) {
                $amount = $remaining_amount;
            }

            if (!$this->updateBalance(array('model_id' => $rec->get('id'),
                                            'model_name' => $rec->modelName,
                                            'amount' => $amount))) {
                break;
            }

            $remaining_amount = bcsub($remaining_amount, $amount, 2);
        }

        if (!$db->HasFailedTrans()) {
            $payment = clone $this;
            $payment->getRelatives();
            Finance_Payments_History::saveData($this->registry,
                                               array('action_type' => 'balance',
                                                     'new_model' => $payment,
                                                     'old_model' => $old_payment));
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Remove paid amount from financial document or payment related to this payment
     *
     * @param int $paid_to - record id
     * @param string $selected_tab - selected tab from balance: 'invoice', 'proforma', 'reason', 'credit', 'payment'
     * @return boolean - result of the operation
     */
    public function emptyPaidAmount($paid_to, $selected_tab = 'invoice') {

        if (!$paid_to) {
            return true;
        }

        $db = &$this->registry['db'];

        // if a started transaction has already failed (because of
        // before_action automations), do not perform actions
        if ($db->HasFailedTrans()) {
            return false;
        }

        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');

        $db->StartTrans();

        if ($this->get('type') == 'PKO' || $this->get('type') == 'BP') {

            $paid_to_model_name = '';
            if (in_array($selected_tab, array('invoice', 'proforma', 'reason'))) {
                $paid_to_model_name = 'Finance_Incomes_Reason';
            } elseif ($selected_tab == 'payment') {
                $paid_to_model_name = 'Finance_Payment';
            } elseif ($selected_tab == 'credit') {
                $paid_to_model_name = 'Finance_Expenses_Reason';
            }

            if ($selected_tab == 'invoice' || $selected_tab == 'reason') {
                // get paid amount for relative before removing it
                $query = 'SELECT paid_amount FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                         'WHERE parent_id=' . $this->get('id') . ' AND parent_model_name="' . $this->modelName . '"' . "\n" .
                         '  AND paid_to=' . $paid_to . ' AND paid_to_model_name="Finance_Incomes_Reason"';
                $paid_amount = $db->GetOne($query);
            }

            // removes balance record for current model and related model
            $this->emptyBalance(array('model_id' => $paid_to,
                                      'model_name' => $paid_to_model_name));

            if ($selected_tab == 'invoice' || $selected_tab == 'reason') {
                //remove from repayment plan
                require_once 'finance.repayment_plans.factory.php';
                $filters = array('where' => array('frp.customer = ' . $this->get('customer'),
                                                  'frp.company = ' . $this->get('company'),
                                                  'frp.status = "locked"'),
                                 'model_lang' => $this->get('lang'));
                $repayment_plan = Finance_Repayment_Plans::searchOne($this->registry, $filters);
                if ($repayment_plan) {
                    $query = 'SELECT frpi.incomes_id ' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES . ' AS frpi ' . "\n" .
                             'WHERE frpi.parent_id=' . $repayment_plan->get('id') . "\n" .
                             '  AND frpi.incomes_id=' . $paid_to;
                    $repayment_incomes_id = $db->GetOne($query);
                    if ($repayment_incomes_id) {
                        $query = 'SELECT * ' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . ' AS frpa ' . "\n" .
                                 'WHERE frpa.parent_id=' . $repayment_plan->get('id') . "\n" .
                                 '  AND frpa.paid_amount > 0' . "\n" .
                                 'ORDER BY deadline DESC';
                        $repayment_data = $db->GetAll($query);
                        foreach ($repayment_data as $row) {
                            if ($row['paid_amount'] > 0) {
                                $empty_amount = $row['paid_amount'] >= $paid_amount ? $paid_amount : $row['paid_amount'];
                                $paid_amount -= $empty_amount;
                                $paid_amount = round($paid_amount, 2);
                                $query = 'UPDATE ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_DATA . "\n" .
                                         'SET paid_amount=paid_amount-' . $empty_amount . ', last_payment=now()' . "\n" .
                                         'WHERE parent_id=' . $row['parent_id'] . ' AND deadline="' . $row['deadline'] . '"';
                                $db->Execute($query);
                                $repayment_plan->set('amount', $empty_amount, true);
                                require_once 'finance.repayment_plans.history.php';
                                Finance_Repayment_Plans_History::saveData($this->registry,
                                                                          array('model' => $repayment_plan,
                                                                                'action_type' => 'empty',
                                                                                'new_model' => $repayment_plan,
                                                                                'old_model' => $repayment_plan));
                                if ($paid_amount <= 0) {
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        } elseif ($this->get('type') == 'RKO' || $this->get('type') == 'PN') {

            $paid_to_model_name = '';
            if (in_array($selected_tab, array('invoice', 'proforma', 'reason'))) {
                $paid_to_model_name = 'Finance_Expenses_Reason';
            } elseif ($selected_tab == 'payment') {
                $paid_to_model_name = 'Finance_Payment';
            } elseif ($selected_tab == 'credit') {
                $paid_to_model_name = 'Finance_Incomes_Reason';
            }

            // removes balance record for current model and related model
            $this->emptyBalance(array('model_id' => $paid_to,
                                      'model_name' => $paid_to_model_name));
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Inserts/Updates balance of current payment and some related record
     *
     * @param array $params - 'model_id' and 'model_name' of related record, 'amount' for distributed amount
     * @return boolean - result of the operation
     */
    public function updateBalance($params) {
        // validate input parameters
        if (empty($params['model_id']) || empty($params['model_name']) || !isset($params['amount'])) {
            return true;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $insert = array();
        $update = array();
        $insert['modified']         = sprintf("modified=now()");
        $insert['modified_by']      = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        $update = $insert;
        $insert['paid_currency']    = sprintf("paid_currency='%s'", $this->get('currency'));
        $insert['added']            = sprintf("added=now()");
        $insert['added_by']         = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $update['paid_amount']      = sprintf("paid_amount=paid_amount + %f", round($params['amount'], 2));
        $insert['paid_amount']      = sprintf("paid_amount='%f'", round($params['amount'], 2));

        if (in_array($this->get('type'), array('PKO', 'BP'))) {
            $insert['parent_id']            = sprintf("parent_id='%d'",             $this->get('id'));
            $insert['parent_model_name']    = sprintf("parent_model_name='%s'",     $this->modelName);
            $insert['paid_to']              = sprintf("paid_to='%d'",               $params['model_id']);
            $insert['paid_to_model_name']   = sprintf("paid_to_model_name='%s'",    $params['model_name']);
        } elseif (in_array($this->get('type'), array('RKO', 'PN'))) {
            $insert['paid_to']              = sprintf("paid_to='%d'",               $this->get('id'));
            $insert['paid_to_model_name']   = sprintf("paid_to_model_name='%s'",    $this->modelName);
            $insert['parent_id']            = sprintf("parent_id='%d'",             $params['model_id']);
            $insert['parent_model_name']    = sprintf("parent_model_name='%s'",     $params['model_name']);
        }

        $query = 'INSERT INTO ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                 'SET ' . implode(', ', $insert) . "\n" .
                 'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);
        $db->Execute($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Removes balance of current payment and some related record
     *
     * @param array $params - 'model_id' and 'model_name' of related record
     * @return boolean - result of the operation
     */
    public function emptyBalance($params) {
        // validate input parameters
        if (empty($params['model_id']) || empty($params['model_name'])) {
            return true;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $where = array();
        if (in_array($this->get('type'), array('PKO', 'BP'))) {
            $where[] = sprintf("parent_id='%d'",            $this->get('id'));
            $where[] = sprintf("parent_model_name='%s'",    $this->modelName);
            $where[] = sprintf("paid_to='%d'",              $params['model_id']);
            $where[] = sprintf("paid_to_model_name='%s'",   $params['model_name']);
        } elseif (in_array($this->get('type'), array('RKO', 'PN'))) {
            $where[] = sprintf("paid_to='%d'",              $this->get('id'));
            $where[] = sprintf("paid_to_model_name='%s'",   $this->modelName);
            $where[] = sprintf("parent_id='%d'",            $params['model_id']);
            $where[] = sprintf("parent_model_name='%s'",    $params['model_name']);
        } else {
            // just in case
            $where[] = '0';
        }

        $query = 'DELETE FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                 'WHERE ' . implode(' AND ', $where);
        $db->Execute($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Get repayment plan invoices and reasons
     *
     * @param string $selected_tab - selected tab from balance: 'invoice', 'proforma', 'reason', 'credit', 'payment'
     * @return bool
     */
    public function getRepaymentPlanDocuments($selected_tab = 'invoice') {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $db = $this->registry['db'];

        $query = 'SELECT incomes_id FROM ' . DB_TABLE_FINANCE_REPAYMENT_PLANS_INCOMES .
                 ',' . DB_TABLE_FINANCE_REPAYMENT_PLANS . '' .
                 ' WHERE parent_id=id AND status="locked" AND deleted=0 AND customer=' . $this->get('customer');
        $records = $db->GetCol($query);
        $this->set('repayment_plan_documents', $records, true);

        if ($unsanitize) {
            $this->sanitize();
        }
    }

    /**
     * Get unpaid invoices and reasons for the customer of this payment
     *
     * @param string $selected_tab - selected tab from balance: 'invoice', 'proforma', 'reason', 'credit', 'payment'
     */
    public function getCustomerDocuments($selected_tab = 'invoice') {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');
        $payments_auto_balance = $this->registry['config']->getParam('finance', 'payments_auto_balance');

        if ($this->get('type') == 'PKO' || $this->get('type') == 'BP') {
            $this->set('selected_tab', $selected_tab, true);
            // get paid amount from this payment
            $this->getPaidAmount();
            // set amounts - amount, paid amount and remaining amount
            $remaining_amount = round($this->get('amount'), 2) - $this->get('paid_amount');
            $remaining_amount = round($remaining_amount, 2);
            $this->set('remaining_amount', $remaining_amount, true);
            $db = $this->registry['db'];

            if ($selected_tab == 'invoice' || $selected_tab == 'proforma') {
                if ($selected_tab == 'invoice') {
                    $types = array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_DEBIT_NOTICE);
                } else {
                    $types = array(PH_FINANCE_TYPE_PRO_INVOICE);
                }
                require_once 'finance.incomes_reasons.factory.php';
                //get ids of invoices+debit notes or proformas with status 'paid' or 'partial' relative to this payment
                $query = 'SELECT DISTINCT(fir.id) ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                         '  ON fir.id=fr.paid_to' . "\n" .
                         'WHERE fir.payment_status IN ("paid", "partial")' . "\n" .
                         '  AND fir.annulled_by=0' . "\n" .
                         '  AND fir.type IN (' . implode(', ', $types) . ')' . "\n" .
                         '  AND fr.paid_to_model_name="Finance_Incomes_Reason"' . "\n" .
                         '  AND fr.parent_model_name="' . $this->modelName . '"' . "\n" .
                         '  AND fr.parent_id=' . $this->get('id') . "\n" .
                         '  AND fr.paid_amount>0';
                $ids = $db->GetCol($query);
                if ($ids) {
                    $paid_ids = ' OR fir.id IN (' . implode(',', $ids) . ')';
                } else {
                    $paid_ids = '';
                }
                //get invoices+debit notes or proformas for customer of this payment
                $filters = array();
                $filters['where'] = array('fir.company = ' . $this->get('company'),
                                          '(fir.payment_status IN ("unpaid", "partial")' . $paid_ids . ')',
                                          'fir.status="finished"',
                                          'fir.annulled_by=0',
                                          'fir.active=1',
                                          'fir.type IN (' . implode(', ', $types) . ')',
                                          'fir.currency="' . $this->get('currency') . '"',
                                          'fir.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fir.issue_date ASC', 'fir.added ASC', 'fir.id ASC');
                $filters['sanitize'] = true;
                $incomes_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);

                $reasons = array();
                foreach ($incomes_reasons as $key => $reason) {
                    // get total amount paid for invoice
                    $reason->getPaidAmount();
                    if ($selected_tab == 'proforma') {
                        $reason_available_amount = $reason->getRemainingAmount();
                        if ($reason_available_amount < $reason->get('total_with_vat') - $reason->get('paid_amount')) {
                            // the amount from the proforma that can be paid
                            // (set greater paid amount than it really is)
                            $reason->set('paid_amount', $reason->get('total_with_vat') - $reason_available_amount, true);
                        }
                    }
                    // get amount paid for invoice from current payment
                    $reason->getPaidAmount(array('parent_model_name' => $this->modelName, 'parent_id' => $this->get('id')));

                    if ($remaining_amount > 0 && $reason->get('total_with_vat') - $reason->get('paid_amount') > 0 && $payments_auto_balance != 'no') {
                        //set suggest amount for every unpaid reason
                        $tmp_compare = round($reason->get('total_with_vat') - $reason->get('paid_amount'), 2);
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        $incomes_reasons[$key]->set('suggest_amount', $suggest_amount, true);
                    } elseif (($reason->get('total_with_vat') - $reason->get('paid_amount') <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($reason->get('id'), $ids)) {
                        // unset unrelated paid invoice or unset any unrelated invoices when payment is fully distributed
                        unset($incomes_reasons[$key]);
                    }
                    if (isset($incomes_reasons[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($reason->get('id'), $ids)) {
                            $reasons[$reason->get('id')] = $reason;
                        }
                        unset($incomes_reasons[$key]);
                    }
                }
                $this->set('incomes_reasons', $reasons, true);
            } elseif ($selected_tab == 'reason') {
                require_once 'finance.incomes_reasons.factory.php';
                //get ids of incomes reasons with status 'paid' or 'partial' relative to this payment
                $query = 'SELECT DISTINCT(fir.id) ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                         '  ON fir.id=fr.paid_to' . "\n" .
                         'WHERE fir.payment_status in ("paid", "partial")' . "\n" .
                         '  AND fir.annulled_by=0' . "\n" .
                         '  AND fir.type > ' . PH_FINANCE_TYPE_MAX . "\n" .
                         '  AND fr.paid_to_model_name="Finance_Incomes_Reason"' . "\n" .
                         '  AND fr.parent_model_name="' . $this->modelName . '"' . "\n" .
                         '  AND fr.parent_id=' . $this->get('id') . "\n" .
                         '  AND fr.paid_amount>0';
                $ids = $db->GetCol($query);
                if ($ids) {
                    $paid_ids = ' OR fir.id IN (' . implode(',', $ids) . ')';
                } else {
                    $paid_ids = '';
                }
                //get income reasons for customer of this payment
                $filters = array();
                $filters['where'] = array('fir.company = ' . $this->get('company'),
                                          '(fir.payment_status in ("unpaid", "partial")' . $paid_ids . ')',
                                          'fir.status="finished"',
                                          'fir.annulled_by = 0',
                                          'fir.active=1',
                                          'fir.type > ' . PH_FINANCE_TYPE_MAX,
                                          'fir.currency="' . $this->get('currency') . '"',
                                          'fir.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fir.issue_date ASC', 'fir.added ASC', 'fir.id ASC');
                $filters['sanitize'] = true;
                $incomes_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);

                $reasons = array();
                foreach ($incomes_reasons as $key => $reason) {
                    // get total amount paid for reason
                    $reason->getPaidAmount();
                    // add paid amount to non-invoiced proformas
                    $reason->set('paid_amount', $reason->get('paid_amount') + $reason->getProformasPaidAmount(), true);
                    // get total invoiced amount for reason
                    $reason->getInvoicedAmount();
                    // get amount paid for reason from current payment
                    $reason->getPaidAmount(array('parent_model_name' => $this->modelName, 'parent_id' => $this->get('id')));
                    if ($remaining_amount < 0.01) {
                        $remaining_amount = 0;
                    }
                    $tmp_compare = round($reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount'), 2);

                    if ($remaining_amount > 0 && $tmp_compare >= 0.01 && $payments_auto_balance != 'no') {
                        //set suggest amount for every unpaid reason
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount');
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount');
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        if ($suggest_amount < 0.01) {
                            $suggest_amount = 0;
                        }
                        $incomes_reasons[$key]->set('suggest_amount', $suggest_amount, true);
                    } elseif (($reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount') <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($reason->get('id'), $ids)) {
                        // unset unrelated paid reason or unset any unrelated reasons when payment is fully distributed
                        unset($incomes_reasons[$key]);
                    }
                    if (isset($incomes_reasons[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($reason->get('id'), $ids)) {
                            $reasons[$reason->get('id')] = $reason;
                        }
                        unset($incomes_reasons[$key]);
                    }
                }
                $this->set('incomes_reasons', $reasons, true);
            } elseif ($selected_tab == 'payment') {
                // get opposite payments for customer of this payment
                $filters['where'] = array('fp.company = ' . $this->get('company'),
                                          'fp.type in ("RKO", "PN")',
                                          'fp.status="finished"',
                                          'fp.currency="' . $this->get('currency') . '"',
                                          'fp.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fp.issue_date ASC', 'fp.added ASC', 'fp.id ASC');
                $filters['sanitize'] = true;
                $payments = Finance_Payments::search($this->registry, $filters);
                $payment_ids = array();
                //get ids of payments relative to this payment
                $ids = array();
                foreach ($payments as $payment) {
                    $payment_ids[] = $payment->get('id');
                    // get amount paid for opposite payment from current payment
                    $payment->getPaidAmount(array('parent_model_name' => $this->modelName, 'parent_id' => $this->get('id')));
                    if ($payment->get('paid_amount_' . $this->modelName . '_' . $this->get('id')) ) {
                        $ids[] = $payment->get('id');
                    }
                }
                $payments_amounts = array();
                if ($payment_ids) {
                    // get total paid amount for each opposite payment
                    $query = 'SELECT paid_to, SUM(paid_amount)' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                             'WHERE fr.paid_to IN (' . implode(',', $payment_ids) . ')' . "\n" .
                             '  AND fr.paid_to_model_name="Finance_Payment"' . "\n" .
                             'GROUP BY paid_to';
                    $payments_amounts = $db->GetAssoc($query);
                }

                $opposite_payments = array();
                foreach ($payments as $key => $payment) {
                    // paid amount for opposite payment
                    $paid = isset($payments_amounts[$payment->get('id')]) ? $payments_amounts[$payment->get('id')] : 0;
                    $payment->set('paid_amount', $paid, true);
                    $tmp_compare = round($payment->get('amount') - $paid, 2);
                    if ($remaining_amount && $tmp_compare > 0 && $payments_auto_balance != 'no') {
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $payment->get('amount') - $paid;
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $payment->get('amount') - $paid;
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        $payment->set('suggest_amount', $suggest_amount, true);
                    } elseif (($payment->get('amount') - $paid <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($payment->get('id'), $ids)) {
                        // unset unrelated paid opposite payment or unset any unrelated opposite payments when payment is fully distributed
                        unset($payments[$key]);
                    }
                    if (isset($payments[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($payment->get('id'), $ids)) {
                            $opposite_payments[$payment->get('id')] = $payment;
                        }
                        unset($payments[$key]);
                    }
                }
                $this->set('opposite_payments', $opposite_payments, true);
            } elseif ($selected_tab == 'credit') {
                require_once 'finance.expenses_reasons.factory.php';
                // get ids of expenses credit notices with status 'paid' or 'partial' relative to this payment
                $query = 'SELECT DISTINCT(fer.id) ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
                         '  ON fer.id=fr.paid_to' . "\n" .
                         'WHERE fer.payment_status in ("paid", "partial")' . "\n" .
                         '  AND fer.annulled_by=0' . "\n" .
                         '  AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE . "\n" .
                         '  AND fr.paid_to_model_name="Finance_Expenses_Reason"' . "\n" .
                         '  AND fr.parent_model_name="' . $this->modelName . '"' . "\n" .
                         '  AND fr.parent_id=' . $this->get('id') . "\n" .
                         '  AND fr.paid_amount>0';
                $ids = $db->GetCol($query);
                if ($ids) {
                    $paid_ids = ' OR fer.id IN (' . implode(',', $ids) . ')';
                } else {
                    $paid_ids = '';
                }
                // get expenses credit notices for customer of this payment
                $filters = array();
                $filters['where'] = array('fer.company = ' . $this->get('company'),
                                          '(fer.payment_status in ("unpaid", "partial")' . $paid_ids . ')',
                                          'fer.status="finished"',
                                          'fer.annulled_by=0',
                                          'fer.active=1',
                                          'fer.type = ' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE,
                                          'fer.currency="' . $this->get('currency') . '"',
                                          'fer.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fer.issue_date ASC', 'fer.added ASC', 'fer.id ASC');
                $filters['sanitize'] = true;
                $credit_notices = Finance_Expenses_Reasons::search($this->registry, $filters);

                $reasons = array();
                foreach ($credit_notices as $key => $reason) {
                    // get total amount paid for credit note
                    $reason->getPaidAmount();
                    // get amount paid for credit note from current payment
                    $reason->getPaidAmount(array('parent_model_name' => $this->modelName, 'parent_id' => $this->get('id')));
                    $reason->set('total_with_vat', sprintf('%.' . $gt2_total_precision . 'f', -1*$reason->get('total_with_vat')), true);
                    $tmp_compare = round($reason->get('total_with_vat') - $reason->get('paid_amount'), 2);

                    if ($remaining_amount > 0 && $tmp_compare > 0 && $payments_auto_balance != 'no') {
                        // set suggest amount for every unpaid expenses credit note
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        $credit_notices[$key]->set('suggest_amount', $suggest_amount, true);
                    } elseif (($reason->get('total_with_vat') - $reason->get('paid_amount') <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($reason->get('id'), $ids)) {
                        // unset unrelated paid expenses credit note or unset any unrelated expenses credit notes when payment is fully distributed
                        unset($credit_notices[$key]);
                    }
                    if (isset($credit_notices[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($reason->get('id'), $ids)) {
                            $reasons[$reason->get('id')] = $reason;
                        }
                        unset($credit_notices[$key]);
                    }
                }
                $this->set('credit_notices', $reasons, true);
            }
        } elseif ($this->get('type') == 'RKO' || $this->get('type') == 'PN') {
            $this->set('selected_tab', $selected_tab, true);
            // get paid amount from this payment
            $this->getPaidAmount();
            // set amounts - amount, paid amount and remaining amount
            $remaining_amount = $this->get('amount') - $this->get('paid_amount');
            $remaining_amount = round($remaining_amount, 2);
            $this->set('remaining_amount', $remaining_amount, true);
            $db = $this->registry['db'];

            if ($selected_tab == 'invoice' || $selected_tab == 'proforma') {
                if ($selected_tab == 'invoice') {
                    $types = array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE);
                } else {
                    $types = array(PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE);
                }
                require_once 'finance.expenses_reasons.factory.php';
                //get ids of expenses invoices+debit notes or proformas with status 'paid' or 'partial' relative to this payment
                $query = 'SELECT DISTINCT(fer.id) ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
                         '  ON fr.parent_id=fer.id' . "\n" .
                         'WHERE fer.payment_status IN ("paid", "partial")' . "\n" .
                         '  AND fer.annulled_by=0' . "\n" .
                         '  AND fer.type IN (' . implode(', ', $types) . ')' . "\n" .
                         '  AND fr.parent_model_name="Finance_Expenses_Reason" ' . "\n" .
                         '  AND fr.paid_to_model_name="' . $this->modelName . '"' . "\n" .
                         '  AND fr.paid_to=' . $this->get('id') . "\n" .
                         '  AND fr.paid_amount>0';
                $ids = $db->GetCol($query);
                if ($ids) {
                    $paid_ids = ' OR fer.id IN (' . implode(',', $ids) . ')';
                } else {
                    $paid_ids = '';
                }
                //get expenses invoices+debit notes or proformas for customer of this payment
                $filters = array();
                $filters['where'] = array('fer.company = ' . $this->get('company'),
                                          '(fer.payment_status IN ("unpaid", "partial")' . $paid_ids . ')',
                                          'fer.status="finished"',
                                          'fer.annulled_by=0',
                                          'fer.active=1',
                                          'fer.type IN (' . implode(', ', $types) . ')',
                                          'fer.currency="' . $this->get('currency') . '"',
                                          'fer.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fer.issue_date ASC', 'fer.added ASC', 'fer.id ASC');
                $filters['sanitize'] = true;
                $expenses_reasons = Finance_Expenses_Reasons::search($this->registry, $filters);

                $reasons = array();
                foreach ($expenses_reasons as $key => $reason) {
                    // get total amount paid for expense invoice
                    $reason->getPaidAmount();
                    // check for paid relative (reason)
                    if ($selected_tab == 'proforma' && $reason->getPaidRelative()) {
                        // add paid amount to relative so that total amount does not get overpaid
                        $reason->set('paid_amount', $reason->get('paid_amount') + (double)$reason->get('relative_paid_amount'), true);
                    }
                    // get amount paid for expense invoice from current payment
                    $reason->getPaidAmount(array('paid_to_model_name' => $this->modelName, 'paid_to' => $this->get('id')));

                    if ($remaining_amount > 0 && $reason->get('total_with_vat') - $reason->get('paid_amount') > 0 && $payments_auto_balance != 'no') {
                        //set suggest amount for every unpaid reason
                        $tmp_compare = round($reason->get('total_with_vat') - $reason->get('paid_amount'), 2);
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        $expenses_reasons[$key]->set('suggest_amount', $suggest_amount, true);
                    } elseif (($reason->get('total_with_vat') - $reason->get('paid_amount') <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($reason->get('id'), $ids)) {
                        // unset unrelated paid expense invoice or unset any unrelated expense invoices when payment is fully distributed
                        unset($expenses_reasons[$key]);
                    }
                    if (isset($expenses_reasons[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($reason->get('id'), $ids)) {
                            $reasons[$reason->get('id')] = $reason;
                        }
                        unset($expenses_reasons[$key]);
                    }
                }
                $this->set('expenses_reasons', $reasons, true);
            } elseif ($selected_tab == 'reason') {
                require_once 'finance.expenses_reasons.factory.php';
                //get ids of expenses reasons with status 'paid' or 'partial' relative to this payment
                $query = 'SELECT DISTINCT(fer.id) ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
                         '  ON fr.parent_id=fer.id' . "\n" .
                         'WHERE fer.payment_status in ("paid", "partial")' . "\n" .
                         '  AND fer.annulled_by=0' . "\n" .
                         '  AND fer.type > ' . PH_FINANCE_TYPE_MAX . "\n" .
                         '  AND fr.parent_model_name="Finance_Expenses_Reason"' . "\n" .
                         '  AND fr.paid_to_model_name="' . $this->modelName . '"' . "\n" .
                         '  AND fr.paid_to=' . $this->get('id') . "\n" .
                         '  AND fr.paid_amount>0';
                $ids = $db->GetCol($query);
                if ($ids) {
                    $paid_ids = ' OR fer.id IN (' . implode(',', $ids) . ')';
                } else {
                    $paid_ids = '';
                }
                //get expenses reasons for customer of this payment
                $filters = array();
                $filters['where'] = array('fer.company = ' . $this->get('company'),
                                          '(fer.payment_status in ("unpaid", "partial")' . $paid_ids . ')',
                                          'fer.status="finished"',
                                          'fer.annulled_by=0',
                                          'fer.active=1',
                                          'fer.type > ' . PH_FINANCE_TYPE_MAX,
                                          'fer.currency="' . $this->get('currency') . '"',
                                          'fer.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fer.issue_date ASC', 'fer.added ASC', 'fer.id ASC');
                $filters['sanitize'] = true;
                $expenses_reasons = Finance_Expenses_Reasons::search($this->registry, $filters);

                $reasons = array();
                foreach ($expenses_reasons as $key => $reason) {
                    // only non-invoiced expenses reasons should be related to payments
                    // (otherwise the expenses invoices are related instead)
                    if (!$reason->checkAddingInvoice()) {
                        unset($expenses_reasons[$key]);
                        continue;
                    }
                    // get total amount paid for reason
                    $reason->getPaidAmount();
                    // check for paid relative (proforma)
                    if ($reason->getPaidRelative()) {
                        // add paid amount to relative so that total amount does not get overpaid
                        $reason->set('paid_amount', $reason->get('paid_amount') + (double)$reason->get('relative_paid_amount'), true);
                    }
                    // get amount paid for reason from current payment
                    $reason->getPaidAmount(array('paid_to_model_name' => $this->modelName, 'paid_to' => $this->get('id')));
                    if ($remaining_amount < 0.01) {
                        $remaining_amount = 0;
                    }
                    $tmp_compare = round($reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount'), 2);

                    if ($remaining_amount > 0 && $tmp_compare >= 0.01 && $payments_auto_balance != 'no') {
                        //set suggest amount for every unpaid reason
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount');
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount');
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        if ($suggest_amount < 0.01) {
                            $suggest_amount = 0;
                        }
                        $expenses_reasons[$key]->set('suggest_amount', $suggest_amount, true);
                    } elseif (($reason->get('total_with_vat') - $reason->get('invoices_amount') - $reason->get('paid_amount') <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($reason->get('id'), $ids)) {
                        // unset unrelated paid reason or unset any unrelated reasons when payment is fully distributed
                        unset($expenses_reasons[$key]);
                    }
                    if (isset($expenses_reasons[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($reason->get('id'), $ids)) {
                            $reasons[$reason->get('id')] = $reason;
                        }
                        unset($expenses_reasons[$key]);
                    }
                }
                $this->set('expenses_reasons', $reasons, true);
            } elseif ($selected_tab == 'payment') {
                // get opposite payments for customer of this payment
                $filters['where'] = array('fp.company = ' . $this->get('company'),
                                          'fp.type in ("PKO", "BP")',
                                          'fp.status="finished"',
                                          'fp.currency="' . $this->get('currency') . '"',
                                          'fp.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fp.issue_date ASC', 'fp.added ASC', 'fp.id ASC');
                $filters['sanitize'] = true;
                $payments = Finance_Payments::search($this->registry, $filters);
                $payment_ids = array();
                //get ids of payments relative to this payment
                $ids = array();
                foreach ($payments as $payment) {
                    $payment_ids[] = $payment->get('id');
                    // get amount paid for opposite payment from current payment
                    $payment->getPaidAmount(array('paid_to_model_name' => $this->modelName, 'paid_to' => $this->get('id')));
                    if ($payment->get('paid_amount_' . $this->modelName . '_' . $this->get('id')) ) {
                        $ids[] = $payment->get('id');
                    }
                }
                $payments_amounts = array();
                if ($payment_ids) {
                    // get total paid amount for each opposite payment
                    $query = 'SELECT parent_id, SUM(paid_amount)' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                             'WHERE fr.parent_id IN (' . implode(',', $payment_ids) . ')' . "\n" .
                             '  AND fr.parent_model_name="Finance_Payment"' . "\n" .
                             'GROUP BY parent_id';
                    $payments_amounts = $db->GetAssoc($query);
                }

                $opposite_payments = array();
                foreach ($payments as $key => $payment) {
                    // paid amount for opposite payment
                    $paid = isset($payments_amounts[$payment->get('id')]) ? $payments_amounts[$payment->get('id')] : 0;
                    $payment->set('paid_amount', $paid, true);
                    $tmp_compare = round($payment->get('amount') - $paid, 2);
                    if ($remaining_amount && $tmp_compare > 0 && $payments_auto_balance != 'no') {
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $payment->get('amount') - $paid;
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $payment->get('amount') - $paid;
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        $payment->set('suggest_amount', $suggest_amount, true);
                    } elseif (($payment->get('amount') - $paid <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($payment->get('id'), $ids)) {
                        // unset unrelated paid opposite payment or unset any unrelated opposite payments when payment is fully distributed
                        unset($payments[$key]);
                    }
                    if (isset($payments[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($payment->get('id'), $ids)) {
                            $opposite_payments[$payment->get('id')] = $payment;
                        }
                        unset($payments[$key]);
                    }
                }
                $this->set('opposite_payments', $opposite_payments, true);
            } elseif ($selected_tab == 'credit') {
                require_once 'finance.incomes_reasons.factory.php';
                // get ids of credit notices with status 'paid' or 'partial' relative to this payment
                $query = 'SELECT DISTINCT(fir.id) ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                         '  ON fr.parent_id=fir.id' . "\n" .
                         'WHERE fir.payment_status in ("paid", "partial")' . "\n" .
                         '  AND fir.annulled_by=0' . "\n" .
                         '  AND fir.type=' . PH_FINANCE_TYPE_CREDIT_NOTICE . "\n" .
                         '  AND fr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                         '  AND fr.paid_to_model_name="' . $this->modelName . '"' . "\n" .
                         '  AND fr.paid_to=' . $this->get('id') . "\n" .
                         '  AND fr.paid_amount>0';
                $ids = $db->GetCol($query);
                if ($ids) {
                    $paid_ids = ' OR fir.id IN (' . implode(',', $ids) . ')';
                } else {
                    $paid_ids = '';
                }
                // get credit notices for customer of this payment
                $filters = array();
                $filters['where'] = array('fir.company = ' . $this->get('company'),
                                          '(fir.payment_status in ("unpaid", "partial")' . $paid_ids . ')',
                                          'fir.status="finished"',
                                          'fir.annulled_by = 0',
                                          'fir.active=1',
                                          'fir.type=' . PH_FINANCE_TYPE_CREDIT_NOTICE,
                                          'fir.currency="' . $this->get('currency') . '"',
                                          'fir.customer=' . $this->get('customer'));
                $filters['model_lang'] = $this->get('lang');
                $filters['sort'] = array('fir.issue_date ASC', 'fir.added ASC', 'fir.id ASC');
                $filters['sanitize'] = true;
                $credit_notices = Finance_Incomes_Reasons::search($this->registry, $filters);

                $reasons = array();
                foreach ($credit_notices as $key => $reason) {
                    // get total amount paid for credit note
                    $reason->getPaidAmount();
                    // get amount paid for credit note from current payment
                    $reason->getPaidAmount(array('paid_to_model_name' => $this->modelName, 'paid_to' => $this->get('id')));
                    $reason->set('total_with_vat', sprintf('%.' . $gt2_total_precision . 'f', -1*$reason->get('total_with_vat')), true);
                    $tmp_compare = round($reason->get('total_with_vat') - $reason->get('paid_amount'), 2);

                    if ($remaining_amount > 0 && $tmp_compare > 0 && $payments_auto_balance != 'no') {
                        //set suggest amount for every unpaid credit note
                        if ($remaining_amount > $tmp_compare) {
                            $suggest_amount = $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $suggest_amount = round($suggest_amount, 2);
                            $remaining_amount -= $reason->get('total_with_vat') - $reason->get('paid_amount');
                            $remaining_amount = round($remaining_amount, 2);
                        } else {
                            $suggest_amount = $remaining_amount;
                            $remaining_amount = 0;
                        }
                        $credit_notices[$key]->set('suggest_amount', $suggest_amount, true);
                    } elseif (($reason->get('total_with_vat') - $reason->get('paid_amount') <= 0
                                || $this->get('amount') - $this->get('paid_amount') <= 0)
                        && !in_array($reason->get('id'), $ids)) {
                        // unset unrelated paid credit note or unset any unrelated credit notes when payment is fully distributed
                        unset($credit_notices[$key]);
                    }
                    if (isset($credit_notices[$key])) {
                        // check for the export tag
                        if (!$this->get('exported_not_allowed_edit') || in_array($reason->get('id'), $ids)) {
                            $reasons[$reason->get('id')] = $reason;
                        }
                        unset($credit_notices[$key]);
                    }
                }
                $this->set('credit_notices', $reasons, true);
            }
        }

        if ($unsanitize) {
            $this->sanitize();
        }
    }

    /**
     * Get paid amount for this payment and set it as a property to model.
     *
     * @param array $params - when specified parameters
     *                        ('paid_to_model_name' and 'paid_to' for revenue payments,
     *                        'parent_model_name' and 'parent_id' for expense payments)
     *                        get paid amount only from/to specified record
     * @return float - paid amount
     */
    public function getPaidAmount($params = array()) {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $record = 0;

        if ($this->get('type') == 'PKO' || $this->get('type') == 'BP') {
            $query = 'SELECT SUM(paid_amount)' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                     'WHERE fr.parent_id=' . $this->get('id') . "\n" .
                     '  AND fr.parent_model_name="' . $this->modelName . '"';
            if (isset($params['paid_to_model_name']) && isset($params['paid_to'])) {
                $query .= ' AND paid_to_model_name="' . $params['paid_to_model_name'] .
                          '" AND paid_to=' . $params['paid_to'];
            }
            $record = (double)$this->registry['db']->GetOne($query);
            if (isset($params['paid_to_model_name']) && isset($params['paid_to'])) {
                $this->set('paid_amount_' . $params['paid_to_model_name'] . '_' . $params['paid_to'], $record, true);
            } else {
                $this->set('paid_amount', $record, true);
            }
        } elseif ($this->get('type') == 'RKO' || $this->get('type') == 'PN') {
            $query = 'SELECT SUM(paid_amount)' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                     'WHERE fr.paid_to=' . $this->get('id') . "\n" .
                     '  AND fr.paid_to_model_name="' . $this->modelName . '"';
            if (isset($params['parent_model_name']) && isset($params['parent_id'])) {
                $query .= ' AND parent_model_name="' . $params['parent_model_name'] .
                          '" AND parent_id=' . $params['parent_id'];
            }
            $record = (double)$this->registry['db']->GetOne($query);
            if (isset($params['parent_model_name']) && isset($params['parent_id'])) {
                $this->set('paid_amount_' . $params['parent_model_name'] . '_' . $params['parent_id'], $record, true);
            } else {
                $this->set('paid_amount', $record, true);
            }
        }
        if ($unsanitize) {
            $this->sanitize();
        }

        return $record;
    }

    /**
     * Get relatives (all records that payment is distributed to in 'balance' tab).
     * Method is used for preparing data for audit of 'balance' and 'empty' actions.
     * IMPORTANT: method is used in plugin exports!!!
     *
     * @param array $params - input parameters (none for now)
     * @return bool - result of the operation
     */
    public function getRelatives($params = array()) {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        // all incomes or expenses reasons, respectively
        if ($this->get('type') == 'PKO' || $this->get('type') == 'BP') {

            $query = 'SELECT fir.id, fir.num, fir.issue_date, fir.type, fr.paid_amount, fr.paid_currency' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                     '  ON fir.id=fr.paid_to AND fr.paid_to_model_name="Finance_Incomes_Reason"' . "\n" .
                     'WHERE fr.parent_id=' . $this->get('id') . ' AND fr.paid_amount > 0 ' . "\n" .
                     '  AND fr.parent_model_name="' . $this->modelName . '"' . "\n" .
                     '  AND fir.annulled_by=0' . "\n" .
                     '  AND (fir.type IN (' . implode(', ', array(PH_FINANCE_TYPE_INVOICE,
                                                                  PH_FINANCE_TYPE_DEBIT_NOTICE,
                                                                  PH_FINANCE_TYPE_PRO_INVOICE)) . ')' . "\n" .
                     '    OR fir.type > ' . PH_FINANCE_TYPE_MAX . ')';
            $records = $this->registry['db']->GetAssoc($query);

            $paid_invoices = $paid_proformas = $paid_reasons = array();
            foreach ($records as $id => $record) {
                if (in_array($record['type'], array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_DEBIT_NOTICE))) {
                    $paid_invoices[$id] = $record;
                } elseif ($record['type'] == PH_FINANCE_TYPE_PRO_INVOICE) {
                    $paid_proformas[$id] = $record;
                } else {
                    $paid_reasons[$id] = $record;
                }
            }
            // outgoing invoices and debit notes
            $this->set('paid_invoices', $paid_invoices, true);
            // outgoing proformas
            $this->set('paid_proformas', $paid_proformas, true);
            // incomes reasons
            $this->set('paid_reasons', $paid_reasons, true);
        } elseif ($this->get('type') == 'RKO' || $this->get('type') == 'PN') {

            $query = 'SELECT fer.id, fer.num, fer.issue_date, fer.type, fr.paid_amount, fr.paid_currency' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
                     '  ON fer.id=fr.parent_id AND fr.parent_model_name="Finance_Expenses_Reason"' . "\n" .
                     'WHERE fr.paid_to=' . $this->get('id') . "\n" .
                     '  AND fr.paid_to_model_name="' . $this->modelName . '"' . "\n" .
                     '  AND fr.paid_amount > 0 ' . "\n" .
                     '  AND fer.annulled_by=0 ' . "\n" .
                     '  AND (fer.type IN (' . implode(', ', array(PH_FINANCE_TYPE_EXPENSES_INVOICE,
                                                                  PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE,
                                                                  PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE)) . ')' . "\n" .
                     '    OR fer.type > ' . PH_FINANCE_TYPE_MAX . ')';
            $records = $this->registry['db']->GetAssoc($query);

            $paid_invoices = $paid_proformas = $paid_reasons = array();
            foreach ($records as $id => $record) {
                if (in_array($record['type'], array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                    $paid_invoices[$id] = $record;
                } elseif ($record['type'] == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                    $paid_proformas[$id] = $record;
                } else {
                    $paid_reasons[$id] = $record;
                }
            }
            // incoming invoices and debit notes
            $this->set('paid_invoices', $paid_invoices, true);
            // incoming proformas
            $this->set('paid_proformas', $paid_proformas, true);
            // expenses reasons
            $this->set('paid_reasons', $paid_reasons, true);
        }

        // outgoing credit notes or incoming credit notes
        if ($this->get('type') == 'PKO' || $this->get('type') == 'BP') {
            $query = 'SELECT fer.id, fer.num, fer.issue_date, fer.type, fr.paid_amount, fr.paid_currency' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
                     '  ON fer.id=fr.paid_to AND fr.paid_to_model_name="Finance_Expenses_Reason"' . "\n" .
                     'WHERE fr.parent_id=' . $this->get('id') . "\n" .
                     '  AND fr.parent_model_name="' . $this->modelName . '"' . "\n" .
                     '  AND fr.paid_amount > 0 ' . "\n" .
                     '  AND fer.annulled_by=0 AND fer.type=' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE;
        } elseif ($this->get('type') == 'RKO' || $this->get('type') == 'PN') {
            $query = 'SELECT fir.id, fir.num, fir.issue_date, fir.type, fr.paid_amount, fr.paid_currency' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                     '  ON fir.id=fr.parent_id AND fr.parent_model_name="Finance_Incomes_Reason"' . "\n" .
                     'WHERE fr.paid_to=' . $this->get('id') .  "\n" .
                     '  AND fr.paid_to_model_name="' . $this->modelName . '"' . "\n" .
                     '  AND fr.paid_amount > 0 ' . "\n" .
                     '  AND fir.annulled_by=0 AND fir.type=' . PH_FINANCE_TYPE_CREDIT_NOTICE;
        }
        $records = $this->registry['db']->GetAssoc($query);
        $this->set('paid_credit_notices', $records, true);

        // opposite payments
        if ($this->get('type') != 'TR') {
            $query = 'SELECT fp.id, fr.paid_amount, fr.paid_currency' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                     '  ON fp.id=' . (($this->get('type') == 'PKO' || $this->get('type') == 'BP') ?
                                     'fr.paid_to AND fr.paid_to_model_name="Finance_Payment"' :
                                     'fr.parent_id AND fr.parent_model_name="Finance_Payment"') . "\n" .
                     'WHERE ' . (($this->get('type') == 'PKO' || $this->get('type') == 'BP') ?
                                'fr.parent_id=' . $this->get('id') . ' AND fr.parent_model_name="' . $this->modelName . '"' :
                                'fr.paid_to=' . $this->get('id') . ' AND fr.paid_to_model_name="' . $this->modelName . '"') . "\n" .
                     '  AND fr.paid_amount > 0 ' . "\n" .
                     '  AND fp.annulled_by=0';
            $records = $this->registry['db']->GetAssoc($query);
            $this->set('paid_payments', $records, true);
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Annul payment
     *
     * @return mixed - id of anuulled payment, id of copy of payment or false if unsuccessful
     */
    public function annul() {

        $request = &$this->registry['request'];

        $db = $this->registry['db'];
        $db->StartTrans();

        $old_payment = clone $this;
        $old_payment->sanitize();

        $this->set('annulled', date('Y-m-d H:i:s'), true);
        $this->set('annulled_by', $this->registry['currentUser']->get('id'), true);
        if ($request->get('description')) {
            $annulment_description = $this->i18n('finance_payments_annulment_description') . ':' . "\n" .
                                     $request->get('description');
            $this->set('note', ($this->get('note') ? $this->get('note') . "\n\n" : '') . $annulment_description, true);
        }

        // update payment
        if ($this->save()) {
            $filters = array('where' => array('fp.id = ' . $this->get('id'), 'fp.annulled IS NOT NULL'));
            $new_payment = Finance_Payments::searchOne($this->registry, $filters);
            $new_payment->sanitize();

            require_once PH_MODULES_DIR . 'finance/models/finance.payments.history.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.audit.php';
            $audit_parent = Finance_Payments_History::saveData($this->registry,
                                                               array('action_type' => 'annul',
                                                                     'new_model' => $new_payment,
                                                                     'old_model' => $old_payment));

            if ($request->isRequested('create_copy')) {
                $rights = $this->registry['currentUser']->get('rights');

                $containers_permissions = ($this->get('container_type') == 'bank_account') ?
                                          $this->registry['currentUser']->get('finance_bank_accounts') :
                                          $this->registry['currentUser']->get('finance_cashboxes');

                // check permissions by container for adding payment of this type
                if (!empty($rights['finance_payments']['add']) && $rights['finance_payments']['add'] != 'none' &&
                isset($containers_permissions[$this->get('type')]['add']) &&
                in_array($this->get('container_id'), $containers_permissions[$this->get('type')]['add'])) {
                    // clone from old model (before annulment)
                    $cloned_payment = clone $old_payment;
                    $cloned_payment->unsanitize();
                    $cloned_payment->unsetProperty('id', true);
                    $cloned_payment->unsetProperty('num', true);
                    $cloned_payment->unsetProperty('status_modified', true);
                    $cloned_payment->unsetProperty('status_modified_by', true);
                    $cloned_payment->unsetProperty('added', true);
                    $cloned_payment->unsetProperty('added_by', true);
                    $cloned_payment->unsetProperty('modified', true);
                    $cloned_payment->unsetProperty('modified_by', true);
                    // status should always be 'added'
                    $cloned_payment->set('status', 'added', true);

                    if ($cloned_payment->save()) {
                        $old_payment = new Finance_Payment($this->registry);
                        $old_payment->sanitize();

                        $filters = array('where' => array('fp.id = ' . $cloned_payment->get('id')));
                        $new_payment = Finance_Payments::searchOne($this->registry, $filters);
                        $new_payment->sanitize();

                        $audit_parent = Finance_Payments_History::saveData($this->registry,
                                                                           array('action_type' => 'add',
                                                                                 'new_model' => $new_payment,
                                                                                 'old_model' => $old_payment));

                        $this->registry['messages']->setMessage($this->i18n('message_finance_payment_add_success',
                                                                array($cloned_payment->getModelTypeName())), '', 1);
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_finance_payment_add_failed',
                                                              array($cloned_payment->getModelTypeName())), '', 1);
                    }

                } else {
                    $this->registry['messages']->setError($this->i18n('error_finance_payment_add_failed',
                                                          array($this->getModelTypeName())), '', 1);
                    $this->registry['messages']->setError($this->i18n('error_no_permissions_create_copy',
                            array($this->i18n('finance_payments_type_' . $this->get('type')),
                                  $this->i18n('finance_payments_' . $this->get('container_type')),
                                  $this->get('container_name')
                            )), '', 2);
                }
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        // return id of copied model or id of current model
        // in order to know where to redirect to
        if ($result) {
            $result = (isset($cloned_payment) && $cloned_payment->get('id')) ?
                      $cloned_payment->get('id') :
                      $this->get('id');
        }
        return $result;
    }

    /**
     * Check if payment can be annulled
     *
     * @return bool - true if allowed, otherwise false
     */
    public function allowAnnul() {
        if ($this->get('annulled') == '0000-00-00 00:00:00' &&
        ($this->get('status') != 'finished' || !floatval($this->getPaidAmount()))) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();

        if ($this->isDefined('type')) {
            $set['type'] = sprintf("`type`='%s'", $this->get('type'));
        }
        if ($this->isDefined('company')) {
            $set['company'] = sprintf("`company`='%d'", $this->get('company'));
        }
        if ($this->isDefined('office')) {
            $set['office'] = sprintf("`office`='%d'", $this->get('office'));
        }
        if ($this->isDefined('iban')) {
            $set['iban'] = sprintf("`iban`='%s'", $this->get('iban'));
        }
        if ($this->isDefined('customer_num')) {
            $set['customer_num'] = sprintf("`customer_num`='%s'", $this->get('customer_num'));
        }
        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("`customer`='%d'", $this->get('customer'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark'] = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("`project`=%d", $this->get('project'));
        }
        if ($this->isDefined('phase')) {
            $set['phase'] = sprintf("`phase`=%d", $this->get('phase'));
        }
        if ($this->isDefined('employee')) {
            $set['employee'] = sprintf("`employee`='%d'", $this->get('employee'));
        }
        if ($this->isDefined('container_id')) {
            $set['container_id'] = sprintf("`container_id`='%d'", $this->get('container_id'));
        }
        if (($this->get('type') == 'PKO' || $this->get('type') == 'RKO')) {
            $set['container_type'] = "`container_type`='cashbox'";
            $this->set('container_type', 'cashbox', true);
        } elseif (($this->get('type') == 'BP' || $this->get('type') == 'PN')) {
            $set['container_type'] = "`container_type`='bank_account'";
            $this->set('container_type', 'bank_account', true);
        }
        if ($this->isDefined('amount')) {
            $amount = round($this->get('amount'), 2);
            $this->set('amount', $amount, true);
            // container rate and amount
            $container_rate = $this->isDefined('container_rate') ? round($this->get('container_rate'), 6) : 1;
            $container_amount = round($amount * $container_rate, 2);
            $set['amount']           = sprintf("`amount`='%.2f'", $amount);
            $set['container_amount'] = sprintf("`container_amount`='%.2f'", $container_amount);
            $set['container_rate']   = sprintf("`container_rate`='%.6f'", $container_rate);
        }
        if ($this->isDefined('currency')) {
            $set['currency'] = sprintf("`currency`='%s'", $this->get('currency'));
        } elseif ($this->get('container_id') && ($this->get('type') == 'BP' || $this->get('type') == 'PN')) {
            // if no currency set in model
            $set['currency'] = sprintf("`currency`='%s'", $this->getBankAccountCurrency());
        }
        if ($this->isDefined('transaction_amount')) {
            $set['transaction_amount'] = sprintf("`transaction_amount`='%.2f'", round($this->get('transaction_amount'), 2));
        }
        if ($this->isDefined('transaction_currency')) {
            $set['transaction_currency'] = sprintf("`transaction_currency`='%s'", $this->get('transaction_currency'));
        }
        if ($this->isDefined('payment_way')) {
            $set['payment_way'] = sprintf("`payment_way`='%s'", $this->get('payment_way'));
        }
        if ($this->isDefined('issue_date')) {
            if ($this->get('issue_date')) {
                $set['issue_date'] = sprintf("`issue_date`='%s'", $this->get('issue_date'));
            } else {
                $set['issue_date'] = sprintf("`issue_date`='0000-00-00'");
            }
        }
        if ($this->isDefined('status')) {
            $set['status'] = sprintf("`status`='%s'", $this->get('status'));
            $set['status_modified'] = sprintf("status_modified=now()");
            $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        }
        if ($this->get('type') == 'RKO' || $this->get('type') == 'PN' || $this->get('type') == 'TR') {
            $set['receive_flag'] = "`receive_flag`=-1";
            $this->set('receive_flag', -1, true);
        } else {
            $this->set('receive_flag', 1, true);
        }
        if ($this->get('annulled') && $this->get('annulled_by')) {
            $set['annulled'] = sprintf("`annulled`='%s'", $this->get('annulled'));
            $set['annulled_by'] = sprintf("`annulled_by`=%d", $this->get('annulled_by'));
        }

        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        $set['modified']       = sprintf("`modified`=now()");
        $set['modified_by']    = sprintf("`modified_by`=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Get payment type name
     *
     * @return string - payment type name
     */
    public function getTypeName() {
        if (!$this->isDefined('type_name')) {
            $this->set('type_name', $this->i18n('finance_payments_type_' . $this->get('type')), true);
        }

        return $this->get('type_name');
    }

    /**
     * get counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->unsanitize();
            }

            require_once 'finance.counters.factory.php';
            if (!$this->get('custom_counter')) {
                $filters = array('where' => array(
                                            'fc.model = "' . $this->modelName . '"',
                                            'fc.model_type = "' . $this->get('type') . '"',
                                            '(fco.office_id = "' . $this->get('office') . '" OR fco.office_id = 0)',
                                            '((fco.container_id = "' . $this->get('container_id') . '" AND fco.container_type="' . $this->get('container_type') . '") OR fco.container_id = 0)',
                                            'fc.company = "' . $this->get('company') . '"',
                                            'fc.active = 1',
                                            'fc.deleted_by = 0'),
                                 'sort' => array('fco.office_id DESC', 'fc.default DESC', 'fco.container_id DESC')
                                );
            } else {
                $filters = array('where' => array('fc.id = ' . $this->get('custom_counter')));
            }
            $this->counter = Finance_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Get number
     *
     * @param bool $force - force number generation even if set in model
     * @return string - num
     */
    public function getNum($force = false) {
        if (!$this->get('num') || $force) {

            //get the counter assigned to the finance payment type
            $this->getCounter();

            if ($this->counter) {
                //define some the counter's formula components
                $formula = $this->counter->get('formula');
                $prefix = $this->counter->get('prefix');
                $delimiter = $this->counter->get('delimiter');
                $zeroes = $this->counter->get('leading_zeroes');
                $date_format = $this->counter->get('date_format');

                //create extender to expand the formula components
                $extender = new Extender;

                //lock the counter for update to guarantee unique next number
                $query = 'SELECT next_number FROM ' . DB_TABLE_FINANCE_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                //set finance payment number
                $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                $extender->add('num', $num);

                if ($this->counter->get('prefix')) {
                    //add this component to the extender
                    $extender->add('prefix', $prefix);
                }

                if ($this->counter->get('company_code') && $this->get('company')) {
                    //get customer code
                    $query = 'SELECT code FROM ' . DB_TABLE_FINANCE_COMPANIES . ' WHERE id=' . $this->get('company');
                    $company_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('company_code', $company_code);
                }

                if ($this->counter->get('office_code') && $this->get('office')) {
                    //get office code
                    $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
                    $office_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('office_code', $office_code);
                }

                if ($this->counter->get('user_code')) {
                    //get user code
                    //add this component to the extender
                    $extender->add('user_code', $this->registry['currentUser']->get('code'));
                }

                if ($this->counter->get('project_code') && $this->get('project')) {
                    //get project code
                    require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                    $filters = array('where' => array('p.id = ' . $this->get('project'),
                                                      'p.deleted IS NOT NULL'),
                                     'sanitize' => true);
                    $project = Projects::searchOne($this->registry, $filters);

                    //add this component to the extender
                    $extender->add('project_code', $project->get('code'));
                }

                if ($this->counter->get('document_date')) {
                    //replace the date
                    if ($this->get('issue_date')) {
                        $date = General::strftime($date_format, strtotime($this->get('issue_date')));
                    } elseif ($this->get('added')) {
                        $date = General::strftime($date_format, strtotime($this->get('added')));
                    } else {
                        $date = General::strftime($date_format);
                    }

                    //add this component to the extender
                    $extender->add('document_date', $date);
                }

                $num = $extender->expand($formula);
                if ($delimiter) {
                    //remove repeating delimiters
                    $num = preg_replace('#'. preg_quote($delimiter . $delimiter) .'#', $delimiter, $num);
                    $num = preg_replace('#'. preg_quote($delimiter) .'$#', '', $num);
                    $num = preg_replace('#^'. preg_quote($delimiter) .'#', '', $num);
                }

                $this->set('num', $num, true);
            }
        }

        return $this->get('num');
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'' . $this->modelName . '\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted =  0'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        return $files;
    }

    /**
     * Get patterns variables
     *
     * @param int $pattern_id - id of the pattern
     * @return array - variables
     */
    public function getPatternsVars($pattern_id = 0) {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Finance_Payment", "Customer", "CurrentUser") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $customer_translations = $customer->getTranslations();
        $t_customer = array();
        foreach ($customer_translations as $t_lang) {
            if ($t_lang != $customer->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $customer->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
            }
        }

        //get the pattern
        $filters = array('where' => array('p.id = ' . $pattern_id),
            'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        $t_user = array();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //prepare contact person
        if ($this->get('contact_person')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                       'c.subtype = \'contact\''),
                                      'sanitize' => true,
                                      'model_lang' => $this->get('model_lang'));
            $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
            if ($contactperson) {
                $this->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
            }
        }

        //prepare customer's branch
        if ($this->get('branch')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                     'c.subtype = \'branch\''),
                                    'sanitize' => true,
                                    'model_lang' => $this->get('model_lang'));
            $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
            if ($branch) {
                $this->set('branch_name', $branch->get('name'), true);
            }
        }

        //prepare cashbox/bank account
        if ($this->get('container_id')) {
            if ($this->get('container_type') == 'bank_account') {
                require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                           'sanitize' => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_bic', $container->get('bic'), true);
                    $this->set('container_iban', $container->get('iban'), true);
                    $this->set('container_bank', $container->get('bank'), true);
                }
            } else {
                require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                           'sanitize' => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_location', $container->get('location'), true);
                }
            }
        }

        $translations = $this->getTranslations();
        if (empty($translations)) {
            $translations = array($this->get('model_lang'));
        }
        $t_reason = array();

        //save the previous registry lang
        $registry_lang_old = $this->registry['lang'];

        foreach ($translations as $t_lang) {
            $this->registry->set('lang', $t_lang, true);
            $this->registry['translater']->reloadFiles($t_lang);

            $filters = array('model_lang' => $t_lang,
                             'where' => array('fp.id = ' . $this->get('id')));
            $t_reason[$t_lang] = Finance_Payments::searchOne($this->registry, $filters);

            if ($this->get('contact_person')) {
                $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                           'c.subtype = \'contact\''),
                                          'sanitize' => true,
                                          'model_lang' => $t_lang);
                $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
                if ($contactperson) {
                    $t_reason[$t_lang]->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                }
            }
            if ($this->get('branch')) {
                $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                         'c.subtype = \'branch\''),
                                        'sanitize' => true,
                                        'model_lang' => $t_lang);
                $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
                if ($branch) {
                    $t_reason[$t_lang]->set('branch_name', $branch->get('name'), true);
                }
            }
            if ($this->get('container_id')) {
                if ($this->get('container_type') == 'bank_account') {
                    require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                    $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                               'sanitize' => true,
                                               'model_lang' => $t_lang);
                    $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_bic', $container->get('bic'), true);
                        $t_reason[$t_lang]->set('container_iban', $container->get('iban'), true);
                        $t_reason[$t_lang]->set('container_bank', $container->get('bank'), true);
                    }
                } else {
                    require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                    $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                               'sanitize' => true,
                                               'model_lang' => $t_lang);
                    $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_location', $container->get('location'), true);
                    }
                }
            }
        }
        $this->registry->set('lang', $registry_lang_old, true);
        $this->registry['translater']->reloadFiles($registry_lang_old);

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            $pl_source = $placeholder->get('source');
            $pl_varname = $placeholder->get('varname');
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Finance_Payment') {
                    //reason variables
                    if (!$placeholder->get('multilang')) {
                        if (preg_match('#^company_#', $pl_source)) {
                            $vars[$placeholder->get('varname')] = $this->getCompanyData(str_replace('company_', '', $pl_source));
                        } else {
                            $vars[$placeholder->get('varname')] = $this->get($pl_source);
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if (preg_match('#^company_#', $pl_source)) {
                                $vars[$t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_reason[$t_lang]->getCompanyData(str_replace('company_', '', $pl_source));
                            } else {
                                $vars[$t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_reason[$t_lang]->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer') {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $customer->get($pl_source);
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[$t_customer[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_customer[$t_lang]->get($pl_source);
                            } else {
                                $vars[$customer->get('model_lang') . '_' . $pl_varname] =
                                $customer->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $user->get($pl_source);
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[$t_user[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_user[$t_lang]->get($pl_source);
                            } else {
                                $vars[$user->get('model_lang') . '_' . $pl_varname] =
                                $user->get($pl_source);
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($pl_source, '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $pl_source);
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$pl_varname] = $res;
                } else {
                    $vars[$pl_varname] = $pl_source;
                }
            }
        }

        return $vars;
    }

    /**
     * Get model attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        if (!$this->registry) {
            $this->unsanitize();
        }
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Finance_Payment\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted = 0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get payments files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get bank account currency
     *
     * @return string - currency
     */
    public function getBankAccountCurrency() {
        //select clause
        $sql['select'] = 'SELECT fba.currency ' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FINANCE_BANK_ACCOUNTS . ' AS fba ';

        //where clause
        $sql['where'] = 'WHERE fba.id=' . $this->get('container_id') . "\n";

        $query = implode("\n", $sql);

        $record = $this->registry['db']->GetOne($query);

        return $record;
    }

    /*
     * Function for taking the related records from first level.
     * It is used from custom outlooks and front page dashlets.
     *
     * @param string $relation - relation type may be 'child' or 'parent'
     * @return array - all related records from first level
     */
    public function getFirstLevelRelatedPayments($relation) {
        // payments are ALWAYS on either side in fin_balance table so no need the search the other one
        if (in_array($this->get('type'), array('PKO', 'BP')) && $relation == 'child' ||
            in_array($this->get('type'), array('RKO', 'PN')) && $relation == 'parent') {
            return array();
        }

        $this->unsanitize();

        // defines the searched fields from the tables
        if ($relation == 'parent') {
            $related_id     = 'paid_to';
            $related_model  = 'paid_to_model_name';

            $current_id_field    = 'parent_id';
            $current_model_field = 'parent_model_name';
        } else {
            $related_id     = 'parent_id';
            $related_model  = 'parent_model_name';

            $current_id_field    = 'paid_to';
            $current_model_field = 'paid_to_model_name';
        }

        // query to take the related:
        // - incomes reasons
        // - expense reasons
        // - payments
        $query = 'SELECT fr.' . $related_model . ' as related_model, fr.' . $related_id . ' as related_id,' . "\n" .
                 '       fir.num as fir_num, firi18n.name as fir_name, fir_type.name as fir_type_name,' . "\n" .
                 '       IF(fer.invoice_num!="", CONCAT(fer.num, " (", fer.invoice_num, ")"), fer.num) AS fer_num, feri18n.name as fer_name, fer_type.name as fer_type_name,' . "\n" .
                 '       fp.num as fp_num, fp_type.name AS fp_type_name ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON (fr.' . $related_id . '=fir.id AND fr.' . $related_model . '="Finance_Incomes_Reason" AND fir.annulled_by=0)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
                 '  ON (firi18n.parent_id=fir.id AND firi18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fir_type' . "\n" .
                 '  ON (fir.type=fir_type.parent_id AND fir_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                 '  ON (fr.' . $related_id . '=fer.id AND fr.' . $related_model . '="Finance_Expenses_Reason" AND fer.annulled_by=0)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS_I18N . ' AS feri18n' . "\n" .
                 '  ON (feri18n.parent_id=fer.id AND feri18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' AS fer_type' . "\n" .
                 '  ON (fer_type.parent_id=fer.type AND fer_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
                 '  ON (fr.' . $related_id . '=fp.id AND fr.' . $related_model . '="Finance_Payment" AND fp.annulled_by=0)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_PAYMENTS_TYPES_I18N . ' AS fp_type' . "\n" .
                 '  ON (fp.type=fp_type.parent_id AND fp_type.lang="' . $this->get('model_lang') . '")' . "\n" .
                 'WHERE fr.' . $current_model_field . ' = "Finance_Payment"' . "\n" .
                 '  AND fr.' . $current_id_field . ' = ' . $this->get('id') . ' AND fr.paid_amount!=0';

        $records = $this->registry['db']->GetAll($query);

        // forms the array with related records
        $relatives = array();
        foreach ($records as $key => $record) {
            if ($record['related_model'] == 'Finance_Incomes_Reason') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'finance',
                    'controller'    => 'incomes_reasons',
                    'id'            => $record['related_id'],
                    'num'           => $record['fir_num'],
                    'note'          => $record['fir_name'] . ' (' . $record['fir_type_name'] . ')'
                );
            } else if ($record['related_model'] == 'Finance_Expenses_Reason') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'finance',
                    'controller'    => 'expenses_reasons',
                    'id'            => $record['related_id'],
                    'num'           => $record['fer_num'],
                    'note'          => $record['fer_name'] . ' (' . $record['fer_type_name'] . ')'
                );
            } else if ($record['related_model'] == 'Finance_Payment') {
                $relatives[] = array(
                    'related_model' => $record['related_model'],
                    'module'        => 'finance',
                    'controller'    => 'payments',
                    'id'            => $record['related_id'],
                    'num'           => $record['fp_num'],
                    'note'          => $record['fp_num']  . ' (' . $record['fp_type_name'] . ')'
                );
            }
        }
        $this->sanitize();

        return $relatives;
    }

    /**
     * Set flag to payment whether it has a tag with 'exported' keyword set,
     * which specifies that it was exported and its balance is not editable.
     *
     * @return boolean - result of the operation
     */
    public function getExportedNotAllowedEdit() {

        $was_sanitized = $this->isSanitized();
        if ($was_sanitized) {
            $this->unsanitize();
        }

        $query = 'SELECT COUNT(t.id)' . "\n" .
                 'FROM ' . DB_TABLE_TAGS_MODELS . ' AS ft' . "\n" .
                 'JOIN ' . DB_TABLE_TAGS . ' AS t' . "\n" .
                 '  ON ft.model=\'' . $this->modelName . '\'' . "\n" .
                 '    AND ft.model_id=\'' . $this->get('id') . '\'' . "\n" .
                 '    AND ft.tag_id=t.id' . "\n" .
                 'WHERE t.keyword=\'exported\' AND t.active=1 AND t.deleted_by=0';
        $exported_not_allowed_edit = $this->registry['db']->GetOne($query);

        if ($was_sanitized) {
            $this->sanitize();
        }

        return $this->set('exported_not_allowed_edit', ($exported_not_allowed_edit ? true : false), true);
    }

    /**
     * Gets type of model as object
     *
     * @return Model - Model corresponding to type of current Model object
     */
    function getModelType()
    {
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $alias = Finance_Payments_Types::getAlias('Finance', 'Payments_Types');
        $filters = array('where' => array("{$alias}.id = '{$this->get('type')}'"));
        $type = Finance_Payments_Types::searchOne($this->registry, $filters);

        // TODO: This is VERY bad, but I don't know if something is dependant on this
        //     Ideally the method should throw an exception if the type is not found
        //     Any callers should handle it or not, depending on the context!
        if (empty($type)) {
            // no type for the model - return empty object
            $type = new Model($this->registry);
        }

        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
        return $type;
    }
}

?>
