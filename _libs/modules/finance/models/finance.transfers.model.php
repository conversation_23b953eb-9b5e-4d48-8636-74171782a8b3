<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

/**
 * Finance_Transfer model class
 */
Class Finance_Transfer extends Model {
    use BelongsToTrait;

    public $modelName = 'Finance_Transfer';

    public $checkPermissionsByStatus = true;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->get('from_company_data')) {
            // Check for from_company_data
            $company_data_arr = explode('_', $this->get('from_company_data'));
            $this->set('from_company', $company_data_arr[0], true);
            $this->set('from_office', $company_data_arr[1], true);
            if ($company_data_arr[2] == 'cash') {
                $this->set('from_container_type', 'cashbox', true);
            } elseif ($company_data_arr[2] == 'bank') {
                $this->set('from_container_type', 'bank_account', true);
            }
            $this->set('from_container_id', $company_data_arr[3], true);
        } elseif ($this->get('from_company') && $this->get('from_office') && $this->get('from_container_type') && $this->get('from_container_id')) {
            $from_company_data = $this->get('from_company') . '_' . $this->get('from_office') . '_';
            if ($this->get('from_container_type') == 'cashbox') {
                $from_company_data .= 'cash_' . $this->get('from_container_id');
            } elseif ($this->get('from_container_type') == 'bank_account') {
                $from_company_data .= 'bank_' . $this->get('from_container_id');
            }
            $this->set('from_company_data', $from_company_data, true);
        }

        if ($this->get('to_company_data')) {
            // Check for to_company_data
            $company_data_arr = explode('_', $this->get('to_company_data'));
            $this->set('to_company', $company_data_arr[0], true);
            $this->set('to_office', $company_data_arr[1], true);
            if ($company_data_arr[2] == 'cash') {
                $this->set('to_container_type', 'cashbox', true);
            } elseif ($company_data_arr[2] == 'bank') {
                $this->set('to_container_type', 'bank_account', true);
            }
            $this->set('to_container_id', $company_data_arr[3], true);
        } elseif ($this->get('to_company') && $this->get('to_office') && $this->get('to_container_type') && $this->get('to_container_id')) {
            $from_company_data = $this->get('to_company') . '_' . $this->get('to_office') . '_';
            if ($this->get('to_container_type') == 'cashbox') {
                $from_company_data .= 'cash_' . $this->get('to_container_id');
            } elseif ($this->get('to_container_type') == 'bank_account') {
                $from_company_data .= 'bank_' . $this->get('to_container_id');
            }
            $this->set('to_company_data', $from_company_data, true);
        }

        if ($this->get('from_company') && !$this->get('from_company_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N .
                     ' WHERE parent_id=' . $this->get('from_company') . ' AND lang="' . $this->get('model_lang') . '"';
            $company_name = $registry['db']->GetOne($query);
            $this->set('from_company_name', $company_name, true);
        }
        if ($this->get('to_company') && !$this->get('to_company_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N .
                     ' WHERE parent_id=' . $this->get('to_company') . ' AND lang="' . $this->get('model_lang') . '"';
            $company_name = $registry['db']->GetOne($query);
            $this->set('to_company_name', $company_name, true);
        }
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if ($this->registry->get('action') != 'translate') {
            if (!$this->get('amount')) {
                $this->raiseError('error_finance_transfers_no_amount_specified', 'amount', null,
                                  array($this->getLayoutName('amount')));
            }

            if (!$this->get('currency')) {
                $this->raiseError('error_finance_transfers_no_currency_specified', 'currency', null,
                                  array($this->getLayoutName('currency')));
            }

            if (!$this->get('transfer_date')) {
                $this->raiseError('error_finance_transfers_no_transfer_date_specified', 'transfer_date', null,
                                  array($this->getLayoutName('transfer_date')));
            }

            if (!$this->get('from_company') || !$this->get('from_office') || !$this->get('from_container_type') || !$this->get('from_container_id')) {
                $this->raiseError('error_finance_transfers_no_from_company_data_specified', 'from_company_data', null,
                                  $this->getLayoutName('from_company_data', false));
            }

            if (!$this->get('to_company') || !$this->get('to_office') || !$this->get('to_container_type') || !$this->get('to_container_id')) {
                $this->raiseError('error_finance_transfers_no_to_company_data_specified', 'to_company_data', null,
                                  array($this->getLayoutName('to_company_data', false)));
            }

            if ($this->get('from_container_id') && $this->get('from_container_id') == $this->get('to_container_id') && $this->get('from_container_type') == $this->get('to_container_type')) {
                $this->raiseError('error_finance_transfers_container_id', 'to_company_data', null,
                                  array($this->getLayoutName('from_company_data', false),
                                        $this->getLayoutName('to_company_data', false)));
            }

            if ($this->get('from_container_type') == 'bank_account' && $this->get('currency') != $this->get('from_currency') && round($this->get('from_rate'), 6) <= 0) {
                $this->raiseError('error_finance_transfers_no_rate', 'from_rate', null,
                                  array($this->get('from_currency'), $this->get('currency')));
            }

            if ($this->get('to_container_type') == 'bank_account' && $this->get('currency') != $this->get('to_currency') && round($this->get('to_rate'), 6) <= 0) {
                $this->raiseError('error_finance_transfers_no_rate', 'to_rate', null,
                                  array($this->get('currency'), $this->get('to_currency')));
            }

            //check counter
            if ($this->get('from_company') && !$this->getCounter()) {
                $this->raiseError('error_finance_transfers_no_counter', 'from_company_data', '',
                                  array($this->get('from_company_name')));
            }
            //amount must be ...
            if ($this->get('status') == 'finished') {
                $from_container_id = $this->get('from_container_id');
                if ($this->get('from_container_type') == 'cashbox'){
                    require_once 'finance.cashboxes.factory.php';
                    $cashbox = Finance_Cashboxes::searchOne($this->registry,
                                                            array('where' => array('fcb.id=' . $from_container_id)));
                    $cashbox_amounts = $cashbox->getAmount();
                    $cashbox_amount = isset($cashbox_amounts[$this->get('currency')]) ? $cashbox_amounts[$this->get('currency')] : 0;
                    if ($cashbox_amount - floatval($this->get('amount')) < 0) {
                        $this->raiseError('error_finance_transfers_less_cashbox_amount', 'amount', null,
                                          array($this->getLayoutName('amount', false)));
                    }
                } elseif ($this->get('from_container_type') == 'bank_account' && round($this->get('from_rate'), 6) > 0) {
                    require_once 'finance.bank_accounts.factory.php';
                    $bank_account = Finance_Bank_Accounts::searchOne($this->registry,
                                                                     array('where' => array('fba.id=' . $from_container_id)));
                    $from_amount = round(round($this->get('amount'), 2)/round($this->get('from_rate'), 6), 2);
                    if ($bank_account->getAmount() - $from_amount < -1*$bank_account->get('credit_limit')) {
                        $this->raiseError('error_finance_transfers_less_credit_limit', 'amount', null,
                                          array($this->getLayoutName('amount', false)));
                    }
                }
            }
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];
        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError(sprintf($this->i18n('error_finance_transfers_no_counter'),
                                                      $this->i18n('finance_transfers'), $this->get('from_company_name')));
            } else {
                $this->counter->increment();
            }
        }

        // set default name if empty
        if (!$this->get('name')) {
            $this->set('name', $this->i18n('finance_transfer'), true);
        }

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_FINANCE_TRANSFERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new finance transfer base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError(sprintf($this->i18n('error_finance_transfers_no_counter'),
                                                      $this->i18n('finance_transfers'), $this->get('from_company_name')));
            } else {
                $this->counter->increment();
            }
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_TRANSFERS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')){
            $update['name']     = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('reason')){
            $update['reason']   = sprintf("reason='%s'", $this->get('reason'));
        }
        if ($this->isDefined('note')){
            $update['note']     = sprintf("note='%s'", $this->get('note'));
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_TRANSFERS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing finance transfer i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();

        if ($this->isDefined('transfer_date')) {
            $set['transfer_date']   = sprintf("`transfer_date`='%s'", $this->get('transfer_date'));
        }
        if ($this->isDefined('from_company')) {
            $set['from_company']    = sprintf("`from_company`='%d'", $this->get('from_company'));
        }
        if ($this->isDefined('to_company')) {
            $set['to_company']      = sprintf("`to_company`='%d'", $this->get('to_company'));
        }
        if ($this->isDefined('from_office')) {
            $set['from_office']     = sprintf("`from_office`='%d'", $this->get('from_office'));
        }
        if ($this->isDefined('to_office')) {
            $set['to_office']       = sprintf("`to_office`='%d'", $this->get('to_office'));
        }
        if ($this->isDefined('employee')) {
            $set['employee']        = sprintf("`employee`='%d'", $this->get('employee'));
        }
        if ($this->isDefined('from_container_type')) {
            $set['from_container_type'] = sprintf("`from_container_type`='%s'", $this->get('from_container_type'));
        }
        if ($this->isDefined('to_container_type')) {
            $set['to_container_type'] = sprintf("`to_container_type`='%s'", $this->get('to_container_type'));
        }
        if ($this->isDefined('from_container_id')) {
            $set['from_container_id'] = sprintf("`from_container_id`='%d'", $this->get('from_container_id'));
        }
        if ($this->isDefined('to_container_id')) {
            $set['to_container_id'] = sprintf("`to_container_id`='%d'", $this->get('to_container_id'));
        }
        if ($this->isDefined('amount')) {
            $set['amount']          = sprintf("`amount`='%.2f'", round($this->get('amount'), 2));
            // from and to amounts
            if ($this->isDefined('from_rate')) {
                $from_amount = round($this->get('from_rate'), 6) > 0 ?
                               round($this->get('amount'), 2) / round($this->get('from_rate'), 6) :
                               0;
                $set['from_amount'] = sprintf("`from_amount`='%.2f'", round($from_amount, 2));
                $set['from_rate']   = sprintf("`from_rate`='%.6f'", round($this->get('from_rate'), 6));
            }
            if ($this->isDefined('to_rate')) {
                $to_amount = round($this->get('amount'), 2) * round($this->get('to_rate'), 6);
                $set['to_amount']   = sprintf("`to_amount`='%.2f'", round($to_amount, 2));
                $set['to_rate']     = sprintf("`to_rate`='%.6f'", round($this->get('to_rate'), 6));
            }
        }
        if ($this->isDefined('currency')) {
            $set['currency']        = sprintf("`currency`='%s'", $this->get('currency'));
        }
        if ($this->isDefined('transaction_amount')) {
            $set['transaction_amount'] = sprintf("`transaction_amount`='%.2f'", round($this->get('transaction_amount'), 2));
        }
        if ($this->isDefined('transaction_currency')) {
            $set['transaction_currency'] = sprintf("`transaction_currency`='%s'", $this->get('transaction_currency'));
        }
        if ($this->isDefined('payment_way')) {
            $set['payment_way']     = sprintf("`payment_way`='%s'", $this->get('payment_way'));
        }
        if ($this->isDefined('status')) {
            $set['status']          = sprintf("`status`='%s'", $this->get('status'));
            $set['status_modified'] = sprintf("status_modified=now()");
            $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        }

        if ($this->isDefined('group')) {
            $set['group']           = sprintf("`group`=%d", $this->get('group'));
        }

        $set['modified']            = sprintf("modified=now()");
        $set['modified_by']         = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Get counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->unsanitize();
            }

            $from_container_id = $this->get('from_container_id');
            $from_container_type = $this->get('from_container_type');

            require_once 'finance.counters.factory.php';
            $filters = array('where' => array(
                                        'fc.model = "' . $this->modelName . '"',
                                        '(fco.office_id = \'' . $this->get('from_office') . '\' OR fco.office_id = 0)',
                                        '((fco.container_id = \'' . $from_container_id . '\' AND fco.container_type = \'' . $from_container_type . '\') OR fco.container_id = 0)',
                                        'fc.company = \'' . $this->get('from_company') . '\'',
                                        'fc.active = 1',
                                        'fc.deleted_by = 0'),
                             'sort' => array('fco.office_id DESC',
                                             'fco.container_id DESC')
                            );
            $this->counter = Finance_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Get number
     *
     * @param bool $force - force
     * @return string - num
     */
    public function getNum($force = false) {
        if (!$this->get('num') || $force) {

            //get the counter assigned to the transfer
            $this->getCounter();

            if ($this->counter) {
                //define some the counter's fomula components
                $formula = $this->counter->get('formula');
                $prefix = $this->counter->get('prefix');
                $delimiter = $this->counter->get('delimiter');
                $zeroes = $this->counter->get('leading_zeroes');
                $date_format = $this->counter->get('date_format');

                //create extender to expand the formula components
                $extender = new Extender;

                //lock the counter for update to guarantee unique next number
                $query = 'SELECT next_number FROM ' . DB_TABLE_FINANCE_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                //set model number
                $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                $extender->add('num', $num);

                if ($this->counter->get('prefix')) {
                    //add this component to the extender
                    $extender->add('prefix', $prefix);
                }

                if ($this->counter->get('company_code') && $this->get('from_company')) {
                    //get from_company code
                    $query = 'SELECT code FROM ' . DB_TABLE_FINANCE_COMPANIES . ' WHERE id=' . $this->get('from_company');
                    $company_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('company_code', $company_code);
                }

                if ($this->counter->get('office_code') && $this->get('from_office')) {
                    //get from_office code
                    $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('from_office');
                    $office_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('office_code', $office_code);
                }

                if ($this->counter->get('user_code')) {
                    //get user code
                    //add this component to the extender
                    $extender->add('user_code', $this->registry['currentUser']->get('code'));
                }

                if ($this->counter->get('document_date')) {
                    //replace the date
                    if ($this->get('transfer_date')) {
                        $date = General::strftime($date_format, strtotime($this->get('transfer_date')));
                    } elseif ($this->get('added')) {
                        $date = General::strftime($date_format, strtotime($this->get('added')));
                    } else {
                        $date = General::strftime($date_format);
                    }

                    //add this component to the extender
                    $extender->add('document_date', $date);
                }

                $num = $extender->expand($formula);
                if ($delimiter) {
                    //remove repeating delimiters
                    $num = preg_replace('#'. preg_quote($delimiter . $delimiter) .'#', $delimiter, $num);
                    $num = preg_replace('#'. preg_quote($delimiter) .'$#', '', $num);
                    $num = preg_replace('#^'. preg_quote($delimiter) .'#', '', $num);
                }

                $this->set('num', $num, true);
            }
        }

        return $this->get('num');
    }

    /**
     * Get model attachments
     *
     * @return bool - result of the operation
     */
    public function getAttachments() {
        if (!isset($this->registry)) {
            $this->unsanitize();
        }
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Finance_Transfer\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted = 0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get transfers files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        //require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        //$ids_where .= Files::getAdditionalWhere($this->registry);

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'finance_transfers', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Finance_Transfer->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Transfer ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'finished') {
                //finished status
                switch($action) {
                //forbidden actions
                case 'edit':
                    return false;
                    break;
                default:
                    return $this->isActionAllowed($action, $modulePermissionsKey, $force);
                }
            } else {
                return $this->isActionAllowed($action, $modulePermissionsKey, $force);
            }
        } else {
            return $this->isActionAllowed($action, $modulePermissionsKey, $force);
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * @throws Exception
     */
    private function isActionAllowed($action, $modulePermissionsKey, $force): bool
    {
        return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                $this,
                $action,
                $modulePermissionsKey,
                $force
            )
            && (!in_array($action, ['view', 'edit'])
                || $this->checkContainerPermissions($action));
    }

    /**
     * Check permissions for "from" container - cashbox or bank account - for specified action.
     *
     * @param string $action - action parameter
     * @return boolean - result of the operation
     */
    public function checkContainerPermissions($action) {

        $result = true;
        if (!in_array($action, array('view', 'edit'))) {
            return $result;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        if ($this->get('from_container_type') == 'bank_account') {
            $finance_bank_accounts = $this->registry['currentUser']->get('finance_bank_accounts');
            if ($finance_bank_accounts && is_array($finance_bank_accounts)
                && isset($finance_bank_accounts['transfer'][$action])
                && in_array($this->get('from_container_id'), $finance_bank_accounts['transfer'][$action])) {

            } else {
                $result = false;
            }
        } else {
            $finance_cashboxes = $this->registry['currentUser']->get('finance_cashboxes');
            if ($finance_cashboxes && is_array($finance_cashboxes)
                && isset($finance_cashboxes['transfer'][$action])
                && in_array($this->get('from_container_id'), $finance_cashboxes['transfer'][$action])) {

            } else {
                $result = false;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }
}

?>
