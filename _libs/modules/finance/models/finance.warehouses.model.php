<?php

/**
 * Finance_Warehouse model class
 */
class Finance_Warehouse extends Model {

    public $modelName = 'Finance_Warehouse';

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        // Get some commonly used vars
        $id = $this->get('id');
        $code = $this->get('code');

        if (!$this->get('name')) {
            $this->raiseError('error_no_name_specified', 'name');
        }

        if (!$code) {
            if (!$id) {
                $code = $this->setCode();
            } elseif ($this->isDefined('code')) {
                $this->raiseError('error_finance_warehouse_no_code', 'code');
            }
        }
        if ($code) {
            // If there are any other warehouses with the same code
            $query = 'SELECT `id` FROM `' . DB_TABLE_FINANCE_WAREHOUSES . '` WHERE `code`= \'' . General::slashesEscape($code) . '\'';
            if ($id) {
                $query .= " AND `id` != {$id}";
            }
            $query .= ' LIMIT 1';
            $code_exists = $this->registry['db']->GetOne($query);
            if ($code_exists) {
                // Set an error
                $this->raiseError('error_finance_warehouse_code_not_unique', 'code');
            }
        }

        if ($this->isDefined('company') && !$this->get('company')) {
            $this->raiseError('error_finance_warehouse_no_company_specified', 'company');
        }

        if ($this->isDefined('office') && !$this->get('office')) {
            $this->raiseError('error_finance_warehouse_no_office_specified', 'office');
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            // edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            // escape the quotes and double quotes
            // in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        // INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added'] = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        // query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_FINANCE_WAREHOUSES . "\n" .
                  'SET ' . implode(', ', $set) . "\n";

        // start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new finance warehouse base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            // rollback the transaction
            $db->FailTrans();
        }

        // INSERT/UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        // the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        // INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        // query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        // start transaction
        $db->StartTrans();
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("editing finance warehouse base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // INSERT/UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        // the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        // UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('location')) {
            $update['location'] = sprintf("location='%s'", $this->get('location'));
        }
        if ($this->isDefined('description')) {
            $update['description'] = sprintf("description='%s'", $this->get('description'));
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id'] = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang'] = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            // query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_WAREHOUSES_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing finance warehouse i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();

        if ($this->isDefined('company') && !$this->get('id')) {
            $set['company'] = sprintf("company='%d'", $this->get('company'));
        }
        if ($this->isDefined('office')) {
            $set['office'] = sprintf("office='%d'", $this->get('office'));
        }
        if ($this->isDefined('employees')) {
            $set['employees'] = sprintf("employees='%s'",
                                        (is_array($this->get('employees')) ?
                                        implode(', ', array_unique(array_diff($this->get('employees'), array('')))) :
                                        $this->get('employees'))
                                       );
        }
        if ($this->get('code')) {
            $set['code'] = sprintf("code = '%s'", $this->get('code'));
        }

        $set['modified'] = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        }

        return $set;
    }

    /**
     * Get all the employees in this warehouse
     *
     * @param bool $for_dropdown - when used in a dropdown
     * @return array $employees - array with all the employees
     */
    public function getEmployees($for_dropdown = false) {
        $employees = array(0 => array('id' => '', 'code' => '', 'name' => '', 'active_option' => '1', 'option_value' => ''));

        if ($this->get('employees') && is_string($this->get('employees'))) {
            $this->set('employees', preg_split('#\s*,\s*#', $this->get('employees')), true);
        }

        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $employee_ids = is_array($this->get('employees')) ?
                        array_unique(array_diff($this->get('employees'), array(''))) :
                        array();
        if ($employee_ids) {
            // get employees details
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array(
                'where' => array(
                    'c.type=' . PH_CUSTOMER_EMPLOYEE,
                    'c.id IN (' . implode(', ', $employee_ids) . ')'),
                'sanitize' => true);
            $employees_found = Customers::search($this->registry, $filters);

            $empls = array();
            if ($for_dropdown) {
                foreach ($employees_found as $employee) {
                    $empls[] = array(
                        'option_value' => $employee->get('id'),
                        'active_option' => $employee->get('active'),
                        'label' => $employee->get('name') . ' ' . $employee->get('lastname')
                    );
                }

                if ($sanitize_after) {
                    $this->sanitize();
                }

                return $empls;
            }

            foreach ($employees_found as $employee) {
                $empls[$employee->get('id')] = array(
                    'id' => $employee->get('id'),
                    'code' => $employee->get('code'),
                    'name' => $employee->get('name') . ' ' . $employee->get('lastname')
                );
            }

            // this is done in order to sort the employees in order of input
            $employees = array();
            foreach ($this->get('employees') as $employee_id) {
                $employees[$employee_id] = $empls[$employee_id];
            }
        } elseif ($for_dropdown) {
            if ($sanitize_after) {
                $this->sanitize();
            }

            return array();
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $employees;
    }

    /**
     * Gets available quantity for certain nomenclature(s) or all for a warehouse.
     *
     * @param array $params - method params
     *        nom_id - id of the nomenclature or array of ids of nomenclatures, or empty
     *        bool force - force the getting of data from the DB
     *        customer_id - if specified, available quantity is taken according to customer
     *        reservation - if specified, available quantity is taken according to reservation ID
     *        get_zeros - this is used only to finish an inspection. As it is possible that a quantity
     *        shown during the inspection adding has been handovered before the inspection finish.
     *        so we have to get also zero quantities for an article
     *        create_key - names of keys from the batches_data array which should be used to build a custom key from the corresponding values of each batch
     *        date - a date from past (in ISO format) which should be used only if we want the quantities at this date
     *        IMPORTANT!!!: if we want the quantities for example 2013-09-05, then we should pass: 2013-09-05 23:59:59
     * @return mixed $result - the available quantity in this warehouse for a single nomenclature
     *         or array of ids and quantities for multiple nomenclatures
     * @throws Exception
     * @todo check availability against reserved quantities
     *
     */
    public function getAvailableQuantity($params = array()) {
        // Ensure that nom_id is array
        if (!empty($params['nom_id']) && !is_array($params['nom_id'])) {
            $params['nom_id'] = array($params['nom_id']);
        }

        // Remove all empty nomenclature ids
        $params['nom_id'] = array_filter($params['nom_id'], function ($e) {
            return !empty($e);
        });

        // Prepare an array for the available quantities
        $available_quantities = array();

        // If we are not forcing to get the data from the DB
        // and the parameter 'nom_id' contains a single nomenclature
        // and the model already contains the available quantities
        $n_id = '';
        if (count($params['nom_id']) == 1) {
            $n_id = reset($params['nom_id']);
        }
        if (empty($params['force']) && $n_id && $this->get('available_quantities')) {
            $available_quantities = $this->get('available_quantities');
            if (isset($available_quantities[$n_id])) {
                // Return the available quantities from the model
                return $available_quantities;
            }
        }

        // Unsanitize the model (if sanitized)
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // Get the precision settings for the current installation
        $prec = $this->registry['config']->getSectionParams('precision');

        $available = array();
        if (!empty($params['date']) && date_create($params['date'])->format('Y-m-d H:i:s') <= date('Y-m-d H:i:s')) {

            // Get the quantities from the history (some of the quantities will be calculated later, because some of the values are serialized)
            $query = 'SELECT fwa.field_name AS nom, fwa.field_value AS batch_data, fwa.old_value AS old_data,' . "\n" .
                      '      fwa.batch_id, fwa.is_array' . "\n" .
                      'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_AUDIT . ' AS fwa' . "\n" .
                      'JOIN (' . "\n" .
                      '    SELECT MAX(fwh.h_id) AS h_id, fwa.field_name, fwa.batch_id' . "\n" .
                      '    FROM ' . DB_TABLE_FINANCE_WAREHOUSES_HISTORY . ' AS fwh' . "\n" .
                      '    JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_AUDIT . ' AS fwa' . "\n" .
                      '      ON fwa.parent_id = fwh.h_id' . (!empty($params['nom_id']) ? ' AND fwa.field_name IN ("' . implode('", "', $params['nom_id']) . '")' : '') . "\n" .
                      '    JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                      '      ON n.id = fwa.field_name AND (n.has_batch > 0 AND fwa.is_array = 1 OR n.has_batch = 0)' . "\n" .
                      '    WHERE fwh.model = "Finance_Warehouse"' . "\n" .
                      '      AND fwh.model_id = "' . $this->get('id') . '"' . "\n" .
                      '      AND fwh.h_date <= "' . $params['date'] . '"' . "\n" .
                      '      AND fwh.action_type = "change_quantities"' . "\n" .
                      '    GROUP BY fwa.field_name, fwa.batch_id' . "\n" .
                      ') AS fwa_max' . "\n" .
                      '  ON fwa.parent_id = fwa_max.h_id AND fwa.field_name = fwa_max.field_name AND fwa.batch_id = fwa_max.batch_id';
            $quantities = $this->registry['db']->GetAll($query);
            foreach ($quantities as $k => $q) {
                if ($q['is_array']) {
                    $q['batch_data'] = unserialize($q['batch_data']);
                    if (!empty($q['batch_data'])) {
                        if (empty($params['get_zeros']) && $q['batch_data']['quantity'] <= 0) {
                            unset($quantities[$k]);
                            continue;
                        }
                        $q['batch_data']['quantity'] = sprintf('%.' . $prec['gt2_quantity'] . 'F', round($q['batch_data']['quantity'], $prec['gt2_quantity']));
                        $q['batch_data']['delivery_price'] = sprintf('%.' . $prec['gt2_rows'] . 'F', round($q['batch_data']['delivery_price'], $prec['gt2_rows']));
                        $quantities[$k] = $q['batch_data'];
                    } else {
                        if (empty($params['get_zeros'])) {
                            unset($quantities[$k]);
                        } else {
                            $q['old_data'] = unserialize($q['old_data']);
                            $q['old_data']['quantity'] = sprintf('%.' . $prec['gt2_quantity'] . 'F', 0);
                            $q['old_data']['delivery_price'] = sprintf('%.' . $prec['gt2_rows'] . 'F', 0);
                            $quantities[$k] = $q['old_data'];
                        }
                    }
                } else {
                    if ($q['batch_data'] <= 0 && empty($params['get_zeros'])) {
                        unset($quantities[$k]);
                        continue;
                    }
                    $quantities[$k] = array(
                        'nom' => $q['nom'],
                        'quantity' => sprintf('%.' . $prec['gt2_quantity'] . 'F', round($q['batch_data'], $prec['gt2_quantity'])),
                        'delivery_price' => sprintf('%.' . $prec['gt2_rows'] . 'F', 0),
                        'currency' => '',
                        'batch' => 0,
                        'batch_code' => '',
                        'serial' => '',
                        'custom' => '',
                        'expire' => '0000-00-00',
                        'warehouse' => $this->get('id')
                    );
                }
            }
        } else {
            $where = array('parent_id' => 'fwq.parent_id = ' . $this->get('id'));
            if (empty($params['get_zeros'])) {
                $where['quantity'] = 'fwq.quantity > 0';
            }

            if (!empty($params['nom_id'])) {
                // get all rows for the article(batch articles have more than 1 record for single warehouse)
                $where['nom_id'] = General::buildClause('fwq.nomenclature_id', $params['nom_id']);
            }

            $query = 'SELECT fwq.nomenclature_id as nom, ROUND(fwq.quantity, ' . $prec['gt2_quantity'] . ') AS quantity,' . "\n" .
                    '    ROUND(fwq.delivery_price, ' . $prec['gt2_rows'] . ') as delivery_price, fwq.currency,' . "\n" .
                    '    fwq.batch_id as batch, fb.code as batch_code, fwq.serial_number as serial, fwq.cstm_number as custom,' . "\n" .
                    '    fwq.expire_date as expire, fwq.parent_id as warehouse' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . ' AS fwq' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_FINANCE_BATCHES . ' AS fb' .
                    '    ON fb.id = fwq.batch_id' . "\n" .
                    'WHERE ' . implode(' AND ', $where) . "\n" .
                    'ORDER BY nomenclature_id';
            $quantities = $this->registry['db']->GetAll($query);
        }

        if (empty($params['create_key'])) {
            $params['create_key'] = array();
        } elseif (!is_array($params['create_key'])) {
            $params['create_key'] = array($params['create_key']);
        }

        foreach ($quantities as $q) {
            // Prepare the default array structure for each article
            if (empty($available[$q['nom']])) {
                $available[$q['nom']] = array(
                    'quantity' => 0,
                    'batch_data' => array());
            }

            if (empty($params['create_key'])) {
                $available[$q['nom']]['batch_data'][] = $q;
            } else {
                $ukData = array();
                $ukElementsMap = [
                    'warehouse'      => 'parent_id',
                    'nom'            => 'nomenclature_id',
                    'batch'          => 'batch_id',
                    'expire'         => 'expire_date',
                    'serial'         => 'serial_number',
                    'custom'         => 'cstm_number',
                    'delivery_price' => 'delivery_price',
                    'currency'       => 'currency',
                ];
                foreach ($params['create_key'] as $ck) {
                    if (!array_key_exists($ck, $ukElementsMap)) {
                        throw new \Exception("Unknown element for batch unique key: {$ck}");
                    }
                    $ukElement = $ukElementsMap[$ck];
                    if ($ck == 'delivery_price') {
                        $ukData[$ukElement] = sprintf('%.' . $prec['gt2_rows'] . 'F', round($q[$ck], $prec['gt2_rows']));
                    } else {
                        $ukData[$ukElement] = $q[$ck];
                    }
                }
                $uk = Finance_Warehouses_Documents::buildBatchUniqueKey($this->registry['db'], $ukData);
                $available[$q['nom']]['batch_data'][$uk] = $q;
            }

            // Calculate total available quantity for the current article
            $available[$q['nom']]['quantity'] += round($q['quantity'], $prec['gt2_quantity']);
            $available[$q['nom']]['quantity'] = round($available[$q['nom']]['quantity'], $prec['gt2_quantity']);
        }

        $released = array();
        $reserved = array();
        $released_customer = array();
        $reserved_customer = array();
        if (empty($params['reservation']) || $params['reservation'] != 'none') {
            // get commodities reservation quantities (if any) and decrease available quantities
            $query = 'SELECT fwd.id as model_id,' . "\n" .
                      '    ROUND(gt2.quantity, ' . $prec['gt2_quantity'] . ') AS batch_quantity,' . "\n" .
                      '    ROUND(gt1.quantity, ' . $prec['gt2_quantity'] . ') AS quantity,' . "\n" .
                      '    ROUND(gt2.delivery_price, ' . $prec['gt2_rows'] . ') AS delivery_price,' . "\n" .
                      '    gt2.batch_id as batch, gt2.currency,' . "\n" .
                      '    gt2.serial_number as serial, gt2.cstm_number as custom,' . "\n" .
                      '    gt2.expire_date as expire, fwd.warehouse, gt1.article_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                     'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt1' . "\n" .
                     '  ON fwd.id = gt1.model_id AND gt1.model = "Finance_Warehouses_Document" ' . (!empty($params['nom_id']) ? 'AND gt1.article_id IN (' . implode(', ', $params['nom_id']) . ')' : '') . "\n" .
                     'LEFT JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' AS gt2' . "\n" .
                     '  ON gt1.id = gt2.parent_id AND gt2.quantity > 0' . "\n" .
                     'WHERE fwd.type = ' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION . "\n" .
                     '  AND fwd.status = "locked"' . "\n" .
                     '  AND fwd.warehouse = ' . $this->get('id') . "\n" .
                     (empty($params['reservation']) ? '' : '  AND fwd.id <> ' . $params['reservation']) .
                     (empty($params['customer_id']) ? '' : '  AND fwd.customer <> ' . $params['customer_id']) .
                     (empty($params['date']) ? '' : ' AND fwd.added<="' . $params['date'] . '"');
            $reserved = $this->registry['db']->GetAll($query);
            if (!empty($reserved)) {
                $rids = array();
                // get all reservations ids
                foreach ($reserved as $b => $bd) {
                    $rids[] = $bd['model_id'];
                }
                $query = 'SELECT ROUND(gt2.quantity, ' . $prec['gt2_quantity'] . ') AS batch_quantity,' . "\n" .
                          '    ROUND(gt1.quantity, ' . $prec['gt2_quantity'] . ') AS quantity,' . "\n" .
                          '    ROUND(gt2.delivery_price, ' . $prec['gt2_rows'] . ') AS delivery_price,' . "\n" .
                          '    gt2.batch_id as batch, gt2.currency,' . "\n" .
                          '    gt2.serial_number as serial, gt2.cstm_number as custom,' . "\n" .
                          '    gt2.expire_date as expire, fwd.warehouse, gt1.article_id' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                         'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt1' . "\n" .
                         '  ON fwd.id = gt1.model_id AND gt1.model = "Finance_Warehouses_Document" ' . (!empty($params['nom_id']) ? 'AND gt1.article_id IN (' . implode(', ', $params['nom_id']) . ')' : '') . "\n" .
                         'LEFT JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' AS gt2' . "\n" .
                         '  ON gt1.id = gt2.parent_id AND gt2.quantity > 0' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                         '  ON frr.parent_id = fwd.id AND frr.parent_model_name = "Finance_Warehouses_Document"' . "\n" .
                         '    AND frr.link_to_model_name = "Finance_Warehouses_Document"' . "\n" .
                         '    AND frr.link_to IN (' . implode(', ', $rids) . ")\n" .
                         'WHERE fwd.type = ' . PH_FINANCE_TYPE_COMMODITIES_RELEASE . "\n" .
                         (empty($params['date']) ? '' : ' AND fwd.added<="' . $params['date'] . '"');
                $released = $this->registry['db']->GetAll($query);
            } else {
                $released = array();
            }

            if (!empty($params['customer_id'])) {
                // get commodities reservation quantities for the certain customer
                $query = 'SELECT fwd.id as model_id,' . "\n" .
                    '    ROUND(gt2.quantity, ' . $prec['gt2_quantity'] . ') AS batch_quantity,' . "\n" .
                    '    ROUND(gt1.quantity, ' . $prec['gt2_quantity'] . ') AS quantity,' . "\n" .
                    '    ROUND(gt2.delivery_price, ' . $prec['gt2_rows'] . ') AS delivery_price,' . "\n" .
                    '    gt2.batch_id as batch, gt2.currency,' . "\n" .
                    '    gt2.serial_number as serial, gt2.cstm_number as custom,' . "\n" .
                    '    gt2.expire_date as expire, fwd.warehouse, gt1.article_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                    'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt1' . "\n" .
                    '  ON fwd.id = gt1.model_id AND gt1.model = "Finance_Warehouses_Document" ' . (!empty($params['nom_id']) ? 'AND gt1.article_id IN (' . implode(
                            ', ',
                            $params['nom_id']
                        ) . ')' : '') . "\n" .
                    'LEFT JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' AS gt2' . "\n" .
                    '  ON gt1.id = gt2.parent_id AND gt2.quantity > 0' . "\n" .
                    'WHERE fwd.type = ' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION . "\n" .
                    '  AND fwd.status = "locked"' . "\n" .
                    '  AND fwd.warehouse = ' . $this->get('id') . "\n" .
                    '  AND fwd.customer = ' . $params['customer_id'] .
                    (empty($params['reservation']) ? '' : '  AND fwd.id <> ' . $params['reservation']) .
                    (empty($params['date']) ? '' : ' AND fwd.added<="' . $params['date'] . '"');
                $reserved_customer = $this->registry['db']->GetAll($query);
                if (!empty($reserved_customer)) {
                    $rids = array();
                    // get all reservations ids
                    foreach ($reserved_customer as $b => $bd) {
                        $rids[] = $bd['model_id'];
                    }
                    $query = 'SELECT ROUND(gt2.quantity, ' . $prec['gt2_quantity'] . ') AS batch_quantity,' . "\n" .
                        '    ROUND(gt1.quantity, ' . $prec['gt2_quantity'] . ') AS quantity,' . "\n" .
                        '    ROUND(gt2.delivery_price, ' . $prec['gt2_rows'] . ') AS delivery_price,' . "\n" .
                        '    gt2.batch_id as batch, gt2.currency,' . "\n" .
                        '    gt2.serial_number as serial, gt2.cstm_number as custom,' . "\n" .
                        '    gt2.expire_date as expire, fwd.warehouse, gt1.article_id' . "\n" .
                        'FROM ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                        'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt1' . "\n" .
                        '  ON fwd.id = gt1.model_id AND gt1.model = "Finance_Warehouses_Document" ' . (!empty($params['nom_id']) ? 'AND gt1.article_id IN (' . implode(
                                ', ',
                                $params['nom_id']
                            ) . ')' : '') . "\n" .
                        'LEFT JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' AS gt2' . "\n" .
                        '  ON gt1.id = gt2.parent_id AND gt2.quantity > 0' . "\n" .
                        'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                        '  ON frr.parent_id = fwd.id AND frr.parent_model_name = "Finance_Warehouses_Document"' . "\n" .
                        '    AND frr.link_to_model_name = "Finance_Warehouses_Document"' . "\n" .
                        '    AND frr.link_to IN (' . implode(', ', $rids) . ")\n" .
                        'WHERE fwd.type = ' . PH_FINANCE_TYPE_COMMODITIES_RELEASE . "\n" .
                        (empty($params['date']) ? '' : ' AND fwd.added<="' . $params['date'] . '"');
                    $released_customer = $this->registry['db']->GetAll($query);
                } else {
                    $released_customer = array();
                }
            }
        }

        // process batches data
        $unique = array();
        $unique_customer = array();
        if (!empty($reserved)) {
            foreach ($reserved as $b => $bd) {
                // create unique keys with reserved quantities
                $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                    $this->registry['db'],
                    [
                        'nomenclature_id' => $bd['article_id'],
                        'batch_id'        => intval($bd['batch']),
                        'expire_date'     => $bd['expire'] ?: '0000-00-00',
                        'serial_number'   => $bd['serial'],
                        'cstm_number'     => $bd['custom'],
                        'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                        'currency'        => $bd['currency'],
                    ]
                );
                if (empty($unique[$k])) {
                    $unique[$k] = 0;
                }
                $unique[$k] += $bd['batch_quantity'] ? round($bd['batch_quantity'], $prec['gt2_quantity']) : round($bd['quantity'], $prec['gt2_quantity']);

                // add article into results even if it has no availability (row in fin_warehouses_quantities table)
                if (!empty($params['get_zeros']) && empty($available[$bd['article_id']])) {
                    $available[$bd['article_id']] = array(
                        'quantity' => 0,
                        'batch_data' => array(
                            $k => array('quantity' => 0, 'batch_quantity' => 0, 'nom' => $bd['article_id']) + $bd
                        )
                    );
                }
            }
            foreach ($released as $b => $bd) {
                // create unique keys with released quantities
                $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                    $this->registry['db'],
                    [
                        'nomenclature_id' => $bd['article_id'],
                        'batch_id'        => intval($bd['batch']),
                        'expire_date'     => $bd['expire'] ?: '0000-00-00',
                        'serial_number'   => $bd['serial'],
                        'cstm_number'     => $bd['custom'],
                        'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                        'currency'        => $bd['currency'],
                    ]
                );
                if (!empty($unique[$k])) {
                    // decrease reserved quantity
                    $unique[$k] -= $bd['batch_quantity'] ? round($bd['batch_quantity'], $prec['gt2_quantity']) : round($bd['quantity'], $prec['gt2_quantity']);
                }
            }
        }

        // process batches data
        if (!empty($reserved_customer)) {
            foreach ($reserved_customer as $b => $bd) {
                // create unique keys with reserved quantities
                $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                    $this->registry['db'],
                    [
                        'nomenclature_id' => $bd['article_id'],
                        'batch_id'        => intval($bd['batch']),
                        'expire_date'     => $bd['expire'] ?: '0000-00-00',
                        'serial_number'   => $bd['serial'],
                        'cstm_number'     => $bd['custom'],
                        'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                        'currency'        => $bd['currency'],
                    ]
                );
                if (empty($unique_customer[$k])) {
                    $unique_customer[$k] = 0;
                }
                $unique_customer[$k] += $bd['batch_quantity'] ? round($bd['batch_quantity'], $prec['gt2_quantity']) : round($bd['quantity'], $prec['gt2_quantity']);

                // add article into results even if it has no availability (row in fin_warehouses_quantities table)
                if (!empty($params['get_zeros']) && empty($available[$bd['article_id']])) {
                    $available[$bd['article_id']] = array(
                        'quantity' => 0,
                        'batch_data' => array(
                            $k => array('quantity' => 0, 'batch_quantity' => 0, 'nom' => $bd['article_id']) + $bd
                        )
                    );
                }
            }
            foreach ($released_customer as $b => $bd) {
                // create unique keys with released quantities
                $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                    $this->registry['db'],
                    [
                        'nomenclature_id' => $bd['article_id'],
                        'batch_id'        => intval($bd['batch']),
                        'expire_date'     => $bd['expire'] ?: '0000-00-00',
                        'serial_number'   => $bd['serial'],
                        'cstm_number'     => $bd['custom'],
                        'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                        'currency'        => $bd['currency'],
                    ]
                );
                if (!empty($unique_customer[$k])) {
                    // decrease reserved quantity
                    $unique_customer[$k] -= $bd['batch_quantity'] ? round($bd['batch_quantity'], $prec['gt2_quantity']) : round($bd['quantity'], $prec['gt2_quantity']);
                }
            }
        }

        foreach ($available as $key => $vals) {
            foreach ($vals['batch_data'] as $b => $bd) {
                // create unique keys for available quantities
                $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                    $this->registry['db'],
                    [
                        'nomenclature_id' => $key,
                        'batch_id'        => intval($bd['batch']),
                        'expire_date'     => $bd['expire'] ?: '0000-00-00',
                        'serial_number'   => $bd['serial'],
                        'cstm_number'     => $bd['custom'],
                        'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                        'currency'        => $bd['currency'],
                    ]
                );

                //real available quantities
                $available_quantity = $available[$key]['quantity'];
                $available_quantity_batch = $available[$key]['batch_data'][$b]['quantity'];

                //reserved quantities
                $reserved_by_others = isset($unique[$k]) ? round($unique[$k], $prec['gt2_quantity']) : 0;
                $reserved_by_customer = isset($unique_customer[$k]) ? round($unique_customer[$k], $prec['gt2_quantity']) : 0;

                //correction
                $correction = 0;

                if (empty($available_quantity_batch)) {
                    //no batches
                    if ($reserved_by_customer >= $available_quantity) {
                        //the reserved by the customer quantity is greater than the available,
                        //so no correction is needed, use available
                        $correction = 0;
                    } else {
                        //the reserved by the customer quantity is LESS than the available,
                        if ($reserved_by_others) {
                            //there is a quantity reserved by the others
                            if ($available_quantity - $reserved_by_customer < $reserved_by_others) {
                                //what remains is less than reserved by the others
                                $correction = $available_quantity - $reserved_by_customer;
                            } else {
                                //what remains is equal or greater than reserved by the others
                                $correction = $reserved_by_others;
                            }
                        } else {
                            //there is no quantity reserved by the others
                            $correction = 0;
                        }
                    }
                } else {
                    //batches
                    if ($reserved_by_customer >= $available_quantity_batch) {
                        //the reserved by the customer quantity is greater than the available,
                        //so no correction is needed, use available
                        $correction = 0;
                    } else {
                        //the reserved by the customer quantity is LESS than the available,
                        if ($reserved_by_others) {
                            //there is a quantity reserved by the others
                            if ($available_quantity_batch - $reserved_by_customer < $reserved_by_others) {
                                //what remains is less than reserved by the others
                                $correction = $available_quantity_batch - $reserved_by_customer;
                            } else {
                                //what remains is equal or greater than reserved by the others
                                $correction = $reserved_by_others;
                            }
                        } else {
                            //there is no quantity reserved by the others
                            $correction = 0;
                        }
                    }
                }

                $available[$key]['quantity'] -= $correction;
                $available[$key]['quantity'] = round($available[$key]['quantity'], $prec['gt2_quantity']);
                $available[$key]['reserved'] = ($available[$key]['reserved'] ?? 0) + $reserved_by_others;
                $available[$key]['batch_data'][$b]['quantity'] -= $correction;
                $available[$key]['batch_data'][$b]['quantity'] = round($available[$key]['batch_data'][$b]['quantity'], $prec['gt2_quantity']);
                $available[$key]['batch_data'][$b]['reserved'] = round($reserved_by_others, $prec['gt2_quantity']);
                /*
                if (empty($params['get_zeros'])) {
                    if ($available[$key]['quantity'] <= 0) {
                        unset($available[$key]);
                        continue 2;
                    } elseif ($available[$key]['batch_data'][$b]['quantity'] <= 0) {
                        unset($available[$key]['batch_data'][$b]);
                    }
                }
                */

            }
        }

        foreach ($available as $key => $vals) {
            //order the batches data by batch code
            if (!empty($vals['batch_data'])) {
                $available[$key]['batch_data'] = $vals['batch_data'] = $this->sortBatches($vals['batch_data']);
            }
            $available_quantities[$key] = $vals;
        }
        $this->set('available_quantities', $available_quantities, true);
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $available;
    }


    /**
     * Sorts batches by code and expiration date
     *
     * @param array batch_data
     * @return array batch_data
     */
    private function sortBatches($batch_data) {
        uasort(
            $batch_data,
            function ($a, $b) {
                if (!array_key_exists('batch_code', $a) ||
                    !array_key_exists('batch_code', $b) ||
                    $a['batch_code'] == $b['batch_code']) {
                    return $a['expire'] >= $b['expire'];
                }

                return $a['batch_code'] >= $b['batch_code'];
            }
        );

        return $batch_data;
    }
}

?>
