<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

/**
 * Finance_Budget model class
 */
Class Finance_Budget extends Model {
    use BelongsToTrait;

    public $modelName = 'Finance_Budget';

    public $counter;

    public $checkPermissionsByStatus = true;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if (!$this->get('method')) {
            $this->raiseError('error_no_method_specified', 'method');
        }

        if (!$this->get('company')) {
            $this->raiseError('error_no_company_specified', 'company');
        }

        if (!$this->get('year')) {
            $this->raiseError('error_no_year_specified', 'year');
        }

        if (!$this->get('currency')) {
            $this->raiseError('error_no_currency_specified', 'currency');
        }

        if (!$this->get('income')) {
            $this->raiseError('error_finance_budgets_no_income_items', 'income');
        }

        if (!$this->get('expense')) {
            $this->raiseError('error_finance_budgets_no_expense_items', 'expense');
        }

        if ($this->get('status')) {
            $valid = true;
            $data_arrays = array('income' => ($this->get('income') ? $this->get('income') : array()),
                                 'expense' => ($this->get('expense') ? $this->get('expense') : array()));

            if ($this->get('status') == 'progress') {
                $errors = array();
                foreach ($data_arrays as $data_type => $data_array) {
                    if ($data_array) {
                        //validation of data from POST and collecting all errors
                        if (!empty($data_array['is_leaf'])) {
                            $data_errors = array();
                            foreach ($data_array['is_leaf'] as $idx => $is_leaf) {
                                if ($is_leaf) {
                                    if (!$data_array['responsible'][$idx] || !$data_array['controlling'][$idx] ||
                                        $data_array['status'][$idx] != 'approved' && (!Validator::validDateTime($data_array['deadline'][$idx]) || $data_array['deadline'][$idx] <= date('Y-m-d H:i:00'))) {
                                        $valid = false;
                                    }
                                    $data_errors['responsible'][] = !$data_array['responsible'][$idx] ? 1 : 0;
                                    $data_errors['controlling'][] = !$data_array['controlling'][$idx] ? 1 : 0;
                                    $data_errors['deadline'][] = $data_array['status'][$idx] != 'approved' && (!Validator::validDateTime($data_array['deadline'][$idx]) || $data_array['deadline'][$idx] <= date('Y-m-d H:i:00')) ? 1 : 0;
                                } else {
                                    $data_errors['responsible'][] = $data_errors['controlling'][] = $data_errors['deadline'][] = 0;
                                }
                                $errors[$data_type] = $data_errors;
                            }
                        //checking if data from DB matches conditions; breaking on first error
                        } else {
                            foreach ($data_array as $idx => $data_item) {
                                if ($data_item['is_leaf']) {
                                    if (!$data_item['responsible'] || !$data_item['controlling'] ||
                                        $data_item['status'] != 'approved' && (!Validator::validDateTime($data_item['deadline']) || $data_item['deadline'] <= date('Y-m-d H:i:00'))) {
                                        $valid = false;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                $this->set('errors', $errors, true);

                if (!$valid) {
                    $this->raiseError('error_finance_budgets_status_progress', 'status');
                }

            } elseif ($this->get('status') == 'approved') {
                foreach ($data_arrays as $data_type => $data_array) {
                    if ($data_array) {
                        //validation of data from POST
                        if (!empty($data_array['status'])) {
                            foreach ($data_array['status'] as $idx => $status) {
                                if ($status != 'approved') {
                                    $valid = false;
                                    break 2;
                                }
                            }
                        //checking if data from DB matches conditions
                        } else {
                            foreach ($data_array as $idx => $data_item) {
                                if ($data_item['status'] != 'approved') {
                                    $valid = false;
                                    break 2;
                                }
                            }
                        }
                    }
                }

                if (!$valid) {
                    $this->raiseError('error_finance_budgets_status_approved', 'status');
                }
            }
            //allow only one budget in 'progress' or 'approved' status per company and year
            if (in_array($this->get('status'), array('progress', 'approved'))) {
                $filters = array('where' => array('fb.year = ' . $this->get('year'),
                                                  'fb.company = ' . $this->get('company'),
                                                  'fb.status IN ("progress", "approved")',
                                                  //'fb.active=1'
                                            ),
                                 'sanitize' => true);
                if ($this->get('id')) {
                    $filters['where'][] = 'fb.id != ' . $this->get('id');
                }
                $finance_budget_exist = Finance_Budgets::searchOne($this->registry, $filters);

                if ($finance_budget_exist && $finance_budget_exist->get('id')) {
                    $this->raiseError('error_finance_budgets_exist', 'status');
                }
            }
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        if ($this->validate()) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status']   = sprintf("`status`='%s'", $this->get('status'));
        $set['status_modified']      = sprintf("`status_modified`=now()");
        $set['status_modified_by']   = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));

        //start transaction
        $db->StartTrans();

        //query to insert the main table
        $query = 'INSERT INTO ' . DB_TABLE_FINANCE_BUDGETS . "\n" .
                 'SET ' . implode(', ', $set);
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new budget base details', $db, $query);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE DATA TABLE
        $this->saveBudgetStructure('add');

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        $old_status = Finance_Budgets::getModelStatus($this->registry, $this->get('id'));

        $set = $this->prepareMainData();
        if ($this->get('status') != $old_status) {
            $set['status'] = sprintf("status='%s'", $this->get('status'));
            $set['status_modified'] = sprintf("status_modified=now()");
            $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        }

        //query to update the main table
        $query = 'UPDATE ' . DB_TABLE_FINANCE_BUDGETS . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE id = ' . $this->get('id');
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('edit budget base details', $db, $query);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE DATA TABLE
        $this->saveBudgetStructure('edit');

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->isDefined('method')) {
            $set['method']  = sprintf("method='%s'", $this->get('method'));
        }
        if ($this->isDefined('company')) {
            $set['company'] = sprintf("company=%d", $this->get('company'));
        }
        if ($this->isDefined('year')) {
            $set['year'] = sprintf("year=%d", $this->get('year'));
        }
        if ($this->isDefined('currency')) {
            $set['currency'] = sprintf("currency='%s'", $this->get('currency'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        $set['modified'] = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Changes status of model
     *
     * @return bool - result of operation
     */
    public function setStatus() {
        $flag_error = false;

        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if (!$this->get('status')) {
            $flag_error = true;
        } else {
            if ($this->get('id')) {
                $id_budget = $this->get('id');
            }
            $current_status = Finance_Budgets::getModelStatus($this->registry, $id_budget);

            //call validate() function to check if assignments and statuses of items allow change to new budget status
            if (!$this->validate()) {
                $flag_error = true;
            }

            $new_status = $this->get('status');
        }

        if ($flag_error) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            if ($flag_error) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }
            $db->CompleteTrans();
            return false;
        }

        $set = array();
        $set['status'] = sprintf("`status`='%s'", $new_status);
        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_BUDGETS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Clone model
     *
     * @return bool
     */
    public function cloneModel() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL

        $set = $this->prepareMainData();
        $set['added']               = sprintf("added=now()");
        $set['added_by']            = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status']              = sprintf("`status`='%s'", 'preparation');
        $set['status_modified']     = sprintf("status_modified=now()");
        $set['status_modified_by']  = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_FINANCE_BUDGETS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE DATA TABLE
        $this->saveBudgetStructure('add');

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Gets budget structure (for add/edit)
     *
     * @return bool - result of operation
     */
    public function getBudgetStructure() {

        //round all subtotal amounts according to 'gt2_rows' precision setting
        $precision = $this->registry['config']->getParam('precision', 'gt2_rows');

        $lang = $this->registry['lang'];

        //income and expense items models
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';

        $income_analysis_items_models = Finance_Analysis_Items::getTree($this->registry,
                                    array('where' => array('fai.type="income"'),
                                          'model_lang' => $lang));

        $expense_analysis_items_models = Finance_Analysis_Items::getTree($this->registry,
                                    array('where' => array('fai.type="expense"'),
                                          'model_lang' => $lang));

        //queries for income and expense items from budgets data table
        $query =    'SELECT fbd.model_id AS idx, fbd.*, ' . "\n" .
                    'CONCAT(ui18n1.firstname, " ", ui18n1.lastname) AS responsible_name, ' . "\n" .
                    'CONCAT(ui18n2.firstname, " ", ui18n2.lastname) AS controlling_name, ' . "\n" .
                    'ROUND(data_amount, ' . $precision . ') AS data_amount, ' . "\n";
        for ($i = 1; $i <= 12; $i++) {
            $query .= 'ROUND(month_amount_' . $i . ', ' . $precision . ') AS month_amount_' . $i . ($i < 12 ? ', ' : '') . "\n";
        }
        $query .=   'FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' AS fbd ' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1 ' . "\n" .
                    'ON ui18n1.lang = "' . $lang . '" AND ui18n1.parent_id = fbd.responsible' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2 ' . "\n" .
                    'ON ui18n2.lang = "' . $lang . '" AND ui18n2.parent_id = fbd.controlling' . "\n" .
                    'WHERE fbd.budget = ' . $this->get('id') . ' AND fbd.type = "%s" AND fbd.kind = "item"' . "\n" .
                    'ORDER BY fbd.id ASC';

        $query_income = sprintf($query, 'income');
        $query_expense = sprintf($query, 'expense');

        $request = $this->registry['request'];

        $db = $this->registry['db'];

        $income_analysis_items = array();
        $expense_analysis_items = array();

        if ($request->isPost() && $this->isDefined('income') && $this->isDefined('expense')) {
            $income_analysis_items_db = $this->get('id') ? $db->getAll($query_income) : array();
            $expense_analysis_items_db = $this->get('id') ? $db->getAll($query_expense) : array();

            $data_arrays = array('income' => $this->get('income'), 'expense' => $this->get('expense'));

            foreach ($data_arrays as $data_type => $data_array) {
                if ($data_type == 'income') {
                    $items_models = $income_analysis_items_models;
                    $items_db = $income_analysis_items_db;
                } else {
                    $items_models = $expense_analysis_items_models;
                    $items_db = $expense_analysis_items_db;
                }

                foreach ($data_array['model_id'] as $idx => $model_id) {
                    $item_array = array('model_id' => $model_id,
                                        'model_factory' => $data_array['model_factory'][$idx],
                                        'is_leaf' => $data_array['is_leaf'][$idx],
                                        'deadline' => $data_array['deadline'][$idx],
                                        'responsible' => $data_array['responsible'][$idx],
                                        'controlling' => $data_array['controlling'][$idx],
                                        'status' => $data_array['status'][$idx]);

                    if (array_key_exists($model_id, $items_models)) {
                        $item_array['name'] = $items_models[$model_id]->get('name');
                        $item_array['level'] = $items_models[$model_id]->get('level');
                    }

                    //in add mode
                    if (!$this->get('id')) {
                        $item_array['data_amount'] = sprintf('%.' . $precision . 'F', 0);
                        for ($i = 1; $i <= 12; $i++) {
                            $item_array['month_amount_' . $i] = sprintf('%.' . $precision . 'F', 0);
                        }
                    //in edit mode
                    } else {
                        foreach ($items_db as $item_db) {
                            if ($item_db['model_id'] == $model_id) {
                                $item_array['data_amount'] = $item_db['data_amount'];
                                for ($i = 1; $i <= 12; $i++) {
                                    $item_array['month_amount_' . $i] = $item_db['month_amount_' . $i];
                                }
                                break;
                            }
                        }
                    }

                    if ($data_type == 'income') {
                        $income_analysis_items[$model_id] = $item_array;
                    } else {
                        $expense_analysis_items[$model_id] = $item_array;
                    }
                }
            }

        } else {
            //in add mode - construct structure
            if (!$this->get('id')) {
                $data_types = array('income', 'expense');

                foreach ($data_types as $data_type) {
                    if ($data_type == 'income') {
                        $items_models = $income_analysis_items_models;
                    } else {
                        $items_models = $expense_analysis_items_models;
                    }

                    foreach ($items_models as $model_id => $item) {
                        $item_array = $item->getAll();
                        $item_array['model_id'] = $item_array['id'];
                        unset($item_array['id']);
                        $item_array['model_factory'] = $item_array['elements_factory'];
                        unset($item_array['elements_factory']);
                        $item_array['is_leaf'] = ($item_array['right'] - $item_array['left'] == 1) ? 1 : 0;
                        $item_array['status'] = $item_array['is_leaf'] ? 'unforwarded' : 'progress';
                        $item_array['data_amount'] = sprintf('%.' . $precision . 'F', 0);
                        for ($i = 1; $i <= 12; $i++) {
                            $item_array['month_amount_' . $i] = sprintf('%.' . $precision . 'F', 0);
                        }

                        if ($data_type == 'income') {
                            $income_analysis_items[$model_id] = $item_array;
                        } else {
                            $expense_analysis_items[$model_id] = $item_array;
                        }
                    }
                }

            //in edit mode - get structure from DB
            } else {
                $income_analysis_items = $db->getAssoc($query_income);
                foreach ($income_analysis_items as $model_id => $item) {
                    if (!empty($income_analysis_items_models[$item['model_id']])) {
                        $item_model = $income_analysis_items_models[$model_id];
                        $income_analysis_items[$model_id]['level'] = $item_model->get('level');
                        $income_analysis_items[$model_id]['name'] = $item_model->get('name');
                        $income_analysis_items[$model_id]['ancestor'] = $item_model->get('ancestor');
                    }
                }

                $expense_analysis_items = $db->getAssoc($query_expense);
                foreach ($expense_analysis_items as $model_id => $item) {
                    if (!empty($expense_analysis_items_models[$model_id])) {
                        $item_model = $expense_analysis_items_models[$item['model_id']];
                        $expense_analysis_items[$model_id]['level'] = $item_model->get('level');
                        $expense_analysis_items[$model_id]['name'] = $item_model->get('name');
                        $expense_analysis_items[$model_id]['ancestor'] = $item_model->get('ancestor');
                    }
                }
            }
        }

        $this->set('income', $income_analysis_items, true);
        $this->set('expense', $expense_analysis_items, true);

        return true;
    }

    /**
     * Gets budget data available for enter/control for current user
     *
     * @param string $action - enter or control
     * @return bool - result of operation
     */
    public function getBudgetData($action) {
        $assignment_type = '';
        if ($action == 'enter') {
            $assignment_type = 'responsible';
        } elseif ($action == 'control') {
            $assignment_type = 'controlling';
        }

        //round all subtotal amounts according to 'gt2_rows' precision setting
        $precision = $this->registry['config']->getParam('precision', 'gt2_rows');

        $lang = $this->registry['lang'];

        $db = $this->registry['db'];
        $current_user_id = $this->registry['currentUser']->get('id');

        //queries for assignments for income and expense items of budget
        $query = 'SELECT fbd.*, ' . "\n" .
                'CONCAT(ui18n1.firstname, " ", ui18n1.lastname) AS responsible_name, ' . "\n" .
                'CONCAT(ui18n2.firstname, " ", ui18n2.lastname) AS controlling_name, ' . "\n" .
                'ROUND(data_amount, ' . $precision . ') AS data_amount, ' . "\n";
        for ($i = 1; $i <= 12; $i++) {
            $query .= 'ROUND(month_amount_' . $i . ', ' . $precision . ') AS month_amount_' . $i . ($i < 12 ? ', ' : '') . "\n";
        }
        $query .= 'FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' AS fbd ' . "\n" .
                'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1 ' . "\n" .
                'ON ui18n1.lang = "' . $lang . '" AND ui18n1.parent_id = fbd.responsible' . "\n" .
                'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2 ' . "\n" .
                'ON ui18n2.lang = "' . $lang . '" AND ui18n2.parent_id = fbd.controlling' . "\n" .
                'WHERE fbd.budget = ' . $this->get('id') . '  AND fbd.type = "%s" AND fbd.' . $assignment_type . ' = ' . $current_user_id . ' AND fbd.kind = "item"' . "\n" .
                'ORDER BY fbd.id ASC';

        $query_income = sprintf($query, 'income');
        $query_expense = sprintf($query, 'expense');

        $data_arrays = array('income' => $db->getAll($query_income), 'expense' => $db->getAll($query_expense));

        $this->set('income', $data_arrays['income'], true);
        $this->set('expense', $data_arrays['expense'], true);

        $this->prepareBudgetDataItemsNamePaths();

        return true;
    }

    /**
     * Gets data for an item from budget
     *
     * @param int $item_id - id of item
     * @return array - data for item
     */
    public function getBudgetDataItem($item_id) {

        $request = &$this->registry['request'];
        $db = $this->registry['db'];
        $lang = ($request->get('model_lang')) ? $request->get('model_lang') : $this->registry['lang'];

        //round all subtotal amounts according to 'gt2_rows' precision setting
        $precision = $this->registry['config']->getParam('precision', 'gt2_rows');

        //analysis item model
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';
        $filters = array('where' => array('fai.id = "' . $item_id . '"'),
                         'model_lang' => $lang);

        $this->registry->set('getElementsFilters', true, true);
        $analysis_item = Finance_Analysis_Items::searchOne($this->registry, $filters);

        //analysis item budget data
        $query = 'SELECT fbd.*, ' . "\n" .
                'CONCAT(ui18n1.firstname, " ", ui18n1.lastname) AS responsible_name, ' . "\n" .
                'CONCAT(ui18n2.firstname, " ", ui18n2.lastname) AS controlling_name, ' . "\n" .
                'ROUND(data_amount, ' . $precision . ') AS data_amount, ' . "\n";
        for ($i = 1; $i <= 12; $i++) {
            $query .= 'ROUND(month_amount_' . $i . ', ' . $precision . ') AS month_amount_' . $i . ($i < 12 ? ', ' : '') . "\n";
        }
        $query .= 'FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' AS fbd ' . "\n" .
                'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1 ' . "\n" .
                'ON ui18n1.lang = "' . $lang . '" AND ui18n1.parent_id = fbd.responsible' . "\n" .
                'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2 ' . "\n" .
                'ON ui18n2.lang = "' . $lang . '" AND ui18n2.parent_id = fbd.controlling' . "\n" .
                'WHERE fbd.budget = ' . $this->get('id') . '  AND fbd.model_id = ' . $item_id . ' AND fbd.kind = "item"';
        $data_item_array = $db->getRow($query);

        if ($data_item_array) {
            $name_path = array();
            $parents = Finance_Analysis_Items::getTreeParents($this->registry, $item_id, array('sanitize' => true));
            if ($parents) {
                foreach ($parents as $parent_id => $parent) {
                    $name_path[] = $parent->get('name');
                }
            }
            $name_path[] = !empty($analysis_item) ? $analysis_item->get('name') : '';
            $data_item_array['name_path'] = implode(' > ', $name_path);
        }

        return $data_item_array;
    }

    /**
     * Prepares name paths for items of budget and sets them to corresponding items
     *
     * @return bool result of operation
     */
    public function prepareBudgetDataItemsNamePaths() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $lang = $this->registry['lang'];

        //income and expense items models
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';

        $income_analysis_items_models = Finance_Analysis_Items::getTree($this->registry,
                                    array('where' => array('fai.type="income"'),
                                          'model_lang' => $lang));

        $expense_analysis_items_models = Finance_Analysis_Items::getTree($this->registry,
                                    array('where' => array('fai.type="expense"'),
                                          'model_lang' => $lang));

        $data_arrays = array('income' => $this->get('income'), 'expense' => $this->get('expense'));

        foreach ($data_arrays as $data_type => $data_array) {
            if ($data_type == 'income') {
                $items_models = $income_analysis_items_models;
            } else {
                $items_models = $expense_analysis_items_models;
            }

            foreach ($data_array as $idx => $item_data) {
                $model_id = $item_data['model_id'];
                $name_path = array();
                $parent_ids = Finance_Analysis_Items::getTreeParentsIds($this->registry, $model_id);
                if ($parent_ids) {
                    foreach ($parent_ids as $parent_id) {
                        $name_path[] = !empty($items_models[$parent_id]) ? $items_models[$parent_id]->get('name') : '';
                    }
                }
                $name_path[] = !empty($items_models[$model_id]) ? $items_models[$model_id]->get('name') : '';
                $data_arrays[$data_type][$idx]['name_path'] = implode(' > ', $name_path);
            }
        }

        $this->set('income', $data_arrays['income'], true);
        $this->set('expense', $data_arrays['expense'], true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return true;
    }

    /**
     * Gets data for elements of an item from budget
     *
     * @param int $item_id - id of item
     * @param bool $level_required - whether level of item in tree is required for elements
     * @return array - array of elements of item and flag whether item has values by elements saved
     */
    public function getBudgetDataElements($item_id, $level_required = true) {

        $request = &$this->registry['request'];
        $db = $this->registry['db'];
        $lang = ($request->get('model_lang')) ? $request->get('model_lang') : $this->registry['lang'];

        //round all subtotal amounts according to 'gt2_rows' precision setting
        $precision = $this->registry['config']->getParam('precision', 'gt2_rows');

        //analysis item model
        require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';

        $filters = array('where' => array('fai.id = ' . $item_id),
                         'model_lang' => $lang,
                         'sanitize' => true);

        $this->registry->set('getElementsFilters', true, true);
        $analysis_item = Finance_Analysis_Items::searchOne($this->registry, $filters);

        //level of analysis item in tree is required for elements
        if ($level_required) {
            $filters['where'] = array('fai.type = "' . $analysis_item->get('type') . '"');
            $analysis_items = Finance_Analysis_Items::getTree($this->registry, $filters);
            $analysis_item = $analysis_items[$item_id];
        }

        $item_elements_saved = 0;
        if ($this->get('id')) {
            $query = 'SELECT count(id) FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . "\n" .
                     'WHERE budget = ' . $this->get('id') . ' AND model_parent_id = ' . $item_id . ' AND kind = "element"';
            $item_elements_saved = $db->getOne($query);
        }

        $analysis_elements = array();

        if ($analysis_item) {
            $elements_factory = $analysis_item->get('elements_factory');

            //elements have been saved (by Responsible assignee)
            if ($item_elements_saved) {

                if (in_array($elements_factory, array('customers', 'offices', 'nomenclatures', 'projects'))) {
                    $tbl_name = constant('DB_TABLE_' . strtoupper($elements_factory) . '_I18N');

                    $query = 'SELECT fbd.model_id AS idx, fbd.*, ' . "\n" .
                             ($level_required ? ($analysis_item->get('level') + 1) . ' AS level, ' . "\n" : '') .
                             ($elements_factory == 'customers' ? 'CONCAT(ti18n.name, " ", ti18n.lastname)' : 'ti18n.name') . ' AS name, ' . "\n" .
                             'ROUND(data_amount, ' . $precision . ') AS data_amount, ' . "\n";
                    for ($i = 1; $i <= 12; $i++) {
                        $query .= 'ROUND(month_amount_' . $i . ', ' . $precision . ') AS month_amount_' . $i . ($i < 12 ? ', ' : '') . "\n";
                    }
                    $query .= 'FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' AS fbd ' . "\n" .
                             'LEFT JOIN ' . $tbl_name . ' AS ti18n ' . "\n" .
                             'ON ti18n.lang = "' . $lang . '" AND fbd.model_id = ti18n.parent_id' . "\n" .
                             'WHERE fbd.kind = "element" AND fbd.budget = ' . $this->get('id') . ' AND fbd.model_parent_id = ' . $item_id . "\n" .
                             'ORDER BY name ASC';
                    $analysis_elements = $db->getAssoc($query);
                }

            //get elements from factory and filters for elements of item
            } else {
                //if item has no 'elements_factory', it has no elements
                if ($elements_factory) {
                    //get ids of elements of analysis item
                    $elements_ids = $analysis_item->getElementsIdsOfAnalysisItem($analysis_item);

                    $elements = $elements_factory::getElementsForDistribution($this->registry, $elements_ids);

                    foreach ($elements as $element) {
                        $element_array = array('model_id' => $element['element'],
                                               'name' => $element['name'],
                                               'kind' => 'element');
                        if ($level_required) {
                            $element_array['level'] = $analysis_item->get('level') + 1;
                        }
                        $element_array['data_amount'] = sprintf('%.' . $precision . 'F', 0);
                        for ($i = 1; $i <= 12; $i++) {
                            $element_array['month_amount_' . $i] = sprintf('%.' . $precision . 'F', 0);
                        }
                        $analysis_elements[$element['element']] = $element_array;
                    }
                }
            }
        }

        return array($analysis_elements, $item_elements_saved);
    }

    /**
     * Saves budget structure
     *
     * @param string action - 'add' or 'edit' - if budget structure is created or updated
     * @return bool - result of operation
     */
    public function saveBudgetStructure($action) {
        $db = $this->registry['db'];

        $data_arrays = array('income' => $this->get('income'), 'expense' => $this->get('expense'));

        //when cloning model, invert property arrays from DB in the way they come from POST
        if ($this->origin == 'database') {
            //properties to be cloned to items of new budget
            $cloned_props = array('model_id', 'model_factory', 'is_leaf', 'responsible', 'controlling');

            foreach ($data_arrays as $data_type => $data_array) {
                $data_array_inv = array();
                foreach ($data_array as $data_item) {
                    foreach ($data_item as $key => $value) {
                        if (in_array($key, $cloned_props)) {
                            $data_array_inv[$key][] = $value;
                        }
                    }
                    $data_array_inv['deadline'][] = '';
                    $data_array_inv['status'][] = '';
                }
                $data_arrays[$data_type] = $data_array_inv;
            }
        }

        $data_set = array();
        foreach ($data_arrays as $data_type => $data_array) {
            if (!empty($data_array['model_id'])) {
                foreach ($data_array['model_id'] as $idx => $model_id) {

                    $data_params = array();

                    $data_params['budget']   = sprintf('budget=%d', $this->get('id'));
                    $data_params['type']     = sprintf('type="%s"', $data_type);
                    $data_params['kind']     = sprintf('kind="item"');
                    $data_params['model_id'] = sprintf('model_id=%d', $model_id);
                    $data_params['model_factory'] = sprintf('model_factory="%s"', $data_array['model_factory'][$idx]);
                    $data_params['is_leaf']  = sprintf('is_leaf=%d', $data_array['is_leaf'][$idx]);
                    $data_params['added']    = sprintf("added=now()");
                    $data_params['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
                    $data_params['modified']    = sprintf("modified=now()");
                    $data_params['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

                    $data_params['deadline'] = sprintf('deadline="%s"', $data_array['deadline'][$idx]);
                    $data_params['responsible'] = sprintf('responsible=%d', $data_array['responsible'][$idx]);
                    $data_params['controlling'] = sprintf('controlling=%d', $data_array['controlling'][$idx]);

                    //only modify status when adding model - initial statuses of all items (leaves or branches)
                    //or when editing model and leaf item is fully assigned
                    $old_status = $data_array['status'][$idx];
                    $item_is_assigned = $data_array['responsible'][$idx] > 0 && $data_array['controlling'][$idx] > 0 && $data_array['deadline'][$idx];

                    if ($action == 'add' || ($old_status == 'unforwarded' && $item_is_assigned)) {
                        if ($data_array['is_leaf'][$idx]) {
                            $data_params['status'] = sprintf('status="%s"', ($item_is_assigned ? 'assigned' : 'unforwarded'));
                        } else {
                            $data_params['status'] = sprintf('status="%s"', 'progress');
                        }
                        $data_params['status_modified']      = sprintf("status_modified=now()");
                        $data_params['status_modified_by']   = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
                    }

                    $data_set[] = $data_params;
                }
            }
        }

        foreach ($data_set as $data_params) {
            $insert = $data_params;
            $update = array($data_params['deadline'], $data_params['responsible'], $data_params['controlling'],
                            $data_params['modified'], $data_params['modified_by']);
            if (isset($data_params['status'])) {
                $update = array_merge($update,
                    array($data_params['status'], $data_params['status_modified'], $data_params['status_modified_by']));
            }

            $query =    'INSERT INTO ' . DB_TABLE_FINANCE_BUDGETS_DATA . "\n" .
                        'SET ' . implode(', ', $insert) . "\n" .
                        'ON DUPLICATE KEY UPDATE ' . "\n" .
                        implode(', ', $update);
            $db->Execute($query);
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Saves an item (and its elements) of budget when performing enter/control.
     *
     * @param string $action - 'enter' or 'control'
     * @return bool - result of operation
     */
    public function saveBudgetData($action) {

        //round all subtotal amounts according to 'gt2_rows' precision setting
        $precision = $this->registry['config']->getParam('precision', 'gt2_rows');

        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        $budget_data_id = $this->get('budget_data_id');
        //old and new status of item
        $status_old = $this->get('status_old');
        $status = $this->get('status');

        if ($action == 'enter') {
            $data_array = array('model_id' => $this->get('model_id'),
                                'kind' => $this->get('kind'),
                                'data_amount' => $this->get('data_amount'),
                                //single values: common for all elements
                                'type' => $this->get('type'),
                                'model_factory' => $this->get('model_factory'),
                                'model_parent_id' => $this->get('model_parent_id'));
            for ($i = 1; $i <= 12; $i++) {
                $data_array['month_amount_' . $i] = $this->get('month_amount_' . $i);
            }

            foreach ($data_array['model_id'] as $idx => $model_id) {

                if ($data_array['kind'][$idx] == 'item') {
                    $update_item = array();
                    for ($i = 1; $i <= 12; $i++) {
                        $update_item['month_amount_' . $i] = sprintf('month_amount_' . $i . '=%.' . $precision . 'F',
                                                                     round($data_array['month_amount_' . $i][$idx], $precision));
                    }
                    $update_item['data_amount'] = sprintf('data_amount=%.' . $precision . 'F',
                                                          round($data_array['data_amount'][$idx], $precision));
                    $update_item['modified']    = sprintf("modified=now()");
                    $update_item['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

                    if ($status_old != $status) {
                        $update_item['status']              = sprintf('status="%s"', $status);
                        $update_item['status_modified']     = sprintf("status_modified=now()");
                        $update_item['status_modified_by']  = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
                    }

                    $query_item =   'UPDATE ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' SET ' . "\n" .
                                    implode(', ', $update_item) . "\n" .
                                    'WHERE id = ' . $budget_data_id;
                    $db->Execute($query_item);

                    //if request has no amounts by elements, delete old ones from db (if any)
                    if (count($data_array['model_id']) == 1) {
                        $query = 'DELETE FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . "\n" .
                                 'WHERE budget = ' . $this->get('id') . ' AND model_parent_id = ' . $data_array['model_parent_id'] . ' AND type = "' . $data_array['type'] . '" AND kind = "element"';
                        $db->Execute($query);
                    }

                } else {
                    $insert = array();
                    $update = array();

                    for ($i = 1; $i <= 12; $i++) {
                        $update['month_amount_' . $i] = sprintf('month_amount_' . $i . '=%.' . $precision . 'F',
                                                                round($data_array['month_amount_' . $i][$idx], $precision));
                    }
                    $update['data_amount'] = sprintf('data_amount=%.' . $precision . 'F',
                                                     round($data_array['data_amount'][$idx], $precision));
                    $update['modified']    = sprintf("modified=now()");
                    $update['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

                    $insert = $update;
                    $insert['budget']   = sprintf('budget=%d', $this->get('id'));
                    $insert['type']     = sprintf('type="%s"', $data_array['type']);
                    $insert['kind']     = sprintf('kind="%s"', $data_array['kind'][$idx]);
                    $insert['model_id'] = sprintf('model_id=%d', $model_id);
                    $insert['model_factory'] = sprintf('model_factory="%s"', $data_array['model_factory']);
                    $insert['model_parent_id'] = sprintf('model_parent_id=%d', $data_array['model_parent_id']);
                    $insert['added']    = sprintf("added=now()");
                    $insert['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

                    $query = 'INSERT INTO ' . DB_TABLE_FINANCE_BUDGETS_DATA . "\n" .
                             'SET ' . implode(', ', $insert) . "\n" .
                             'ON DUPLICATE KEY UPDATE ' . "\n" .
                             implode(', ', $update);
                    $db->Execute($query);
                }
            }

        } elseif ($action == 'control') {
            $update_item = array();

            $update_item['modified']    = sprintf("modified=now()");
            $update_item['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

            if ($status_old != $status) {
                $update_item['status']              = sprintf('status="%s"', $status);
                $update_item['status_modified']     = sprintf("status_modified=now()");
                $update_item['status_modified_by']  = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
            }

            $query_item =   'UPDATE ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' SET ' . "\n" .
                            implode(', ', $update_item) . "\n" .
                            'WHERE id = ' . $budget_data_id;
            $db->Execute($query_item);

            if ($status == 'approved' && $status_old != $status) {
                $item_idx = array_search('item', $this->get('kind'));
                $item_id = $this->get('model_id');
                $item_id = !empty($item_id[$item_idx]) ? $item_id[$item_idx] : 0;

                $query = 'SELECT ROUND(data_amount, ' . $precision . ') AS data_amount, ' . "\n";
                for ($i = 1; $i <= 12; $i++) {
                    $query .= 'ROUND(month_amount_' . $i . ', ' . $precision . ') AS month_amount_' . $i . ($i < 12 ? ', ' : ' ') . "\n";
                }
                $query .= 'FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' WHERE id = ' . $budget_data_id;
                $item_data_db = $db->getRow($query);

                require_once PH_MODULES_DIR . 'finance/models/finance.analysis_items.factory.php';

                $filters = array('where' => array('fai.id = "' . $item_id . '"'));
                $analysis_item = Finance_Analysis_Items::searchOne($this->registry, $filters);
                $ancestor_id = $analysis_item->get('ancestor');
                $cascade_status_update = true;

                while ($ancestor_id) {
                    $item_id = $ancestor_id;

                    if ($cascade_status_update) {
                        $descendant_ids = Finance_Analysis_Items::getTreeDescendantsIds($this->registry, $item_id);

                        //remove first item which is current item itself
                        array_shift($descendant_ids);

                        $query = 'SELECT DISTINCT(status) FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . "\n" .
                                 'WHERE budget = ' . $this->get('id') . ' AND type = "' . $this->get('type') . '" AND kind = "item" AND model_id IN (' . implode(', ', $descendant_ids) . ')';
                        $descendant_statuses = $db->getCol($query);

                        if (!(count($descendant_statuses) == 1 && $descendant_statuses[0] == 'approved')) {
                            $cascade_status_update = false;
                        }
                    }

                    $query = 'UPDATE ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' SET ' . "\n" .
                             'data_amount = data_amount + ' . $item_data_db['data_amount'] . ', ' . "\n";
                    if ($cascade_status_update) {
                        $query .= '`status` = "approved", ' . "\n";
                    }
                    for ($i = 1; $i <= 12; $i++) {
                        $query .= 'month_amount_' . $i . ' = month_amount_' . $i . ' + ' . $item_data_db['month_amount_' . $i] . ($i < 12 ? ', ' : ' ') . "\n";
                    }
                    $query .= 'WHERE budget = ' . $this->get('id') . ' AND type = "' . $this->get('type') . '" AND kind = "item" AND model_id = ' . $item_id;
                    $db->Execute($query);

                    $filters = array('where' => array('fai.id = "' . $item_id . '"'));
                    $analysis_item = Finance_Analysis_Items::searchOne($this->registry, $filters);
                    $ancestor_id = $analysis_item->get('ancestor');
                }

                $query = 'UPDATE ' . DB_TABLE_FINANCE_BUDGETS . ' SET ' . "\n" .
                         $this->get('type') . '_amount = ' . $this->get('type') . '_amount + ' . $item_data_db['data_amount'] . "\n" .
                         'WHERE id = ' . $this->get('id');
                $db->Execute($query);
            }
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'finance_budgets', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Finance_Budget->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Budget ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'approved') {
                //approved status
                switch($action) {
                //forbidden actions
                case 'edit':
                case 'enter':
                case 'control':
                case 'setstatus':
                    return false;
                    break;
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } elseif ($this->get('status') == 'obsolete') {
                //obsolete status
                switch($action) {
                //forbidden actions
                case 'edit':
                case 'enter':
                case 'control':
                case 'setstatus':
                    return false;
                    break;
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } else {
                //opened status
                switch($action) {
                //forbidden actions
                //allowed actions
                case 'enter':
                case 'control':
                    return ($this->checkAssignments($action));
                    break;
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            }
        } else {
            return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                $this,
                $action,
                $modulePermissionsKey,
                $force
            );
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Checks if user has assignments to enter/control data for items of model
     *
     * @param string $action - enter or control
     * @return bool - whether user has assignments
     */
    public function checkAssignments($action) {
        $assignment_type = '';
        if ($action == 'enter') {
            $assignment_type = 'responsible';
        } elseif ($action == 'control') {
            $assignment_type = 'controlling';
        }

        $user_assigned = false;

        if ($assignment_type && $this->get('status') == 'progress') {
            $sanitize_after = false;
            if ($this->sanitized) {
                $this->unsanitize();
                $sanitize_after = true;
            }

            $db = $this->registry['db'];
            $current_user_id = $this->registry['currentUser']->get('id');

            $query = 'SELECT COUNT(id) FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . "\n" .
                     'WHERE budget = ' . $this->get('id') . ' AND ' . $assignment_type . ' = ' . $current_user_id . ' AND status NOT LIKE "unforwarded"';
            $user_assigned = $db->getOne($query) ? true : false;

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $user_assigned;
    }

    /**
     * Get contact info for distinct assignee users from database
     *
     * @param string assignment_type - optional; responsible or controlling
     * @return array - user assignments
     */
    public function getAssignments($assignment_type = '', $from_post = false) {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $sql =   'SELECT DISTINCT(u.id) AS idx, u.email, u.active, ' . "\n" .
                 '  CONCAT(ui18n.firstname, " ", ui18n.lastname) AS assigned_to_name ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_BUDGETS_DATA . ' AS fbd ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_BUDGETS . ' AS fb ' . "\n" .
                 '  ON fbd.budget=fb.id ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS . ' AS u ' . "\n" .
                 '  ON (fbd.%s=u.id)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n ' . "\n" .
                 '  ON (u.id=ui18n.parent_id AND ui18n.lang="' . $lang . '")' . "\n" .
                 'WHERE fbd.budget=' . $this->get('id') . ' AND u.id > 0';

        if ($assignment_type) {
            $query = sprintf($sql, $assignment_type);
        } else {
            $query = sprintf($sql, 'responsible') . "\n" .
                     'UNION' . "\n" .
                     sprintf($sql, 'controlling');
        }

        $assignments = $db->GetAssoc($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        $this->set('user_assignments', $assignments);

        return $assignments;
    }

    /**
     * Gets names and emails of recipient users
     *
     * @param array $user_ids - array of user ids
     * @return array - associative array with recipient info
     */
    public function getRecipients($user_ids = array()) {
        $recipients = array();

        if ($user_ids) {
            $sanitize_after = false;
            if ($this->sanitized) {
                $this->unsanitize();
                $sanitize_after = true;
            }

            $db = $this->registry['db'];
            $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

            $query = 'SELECT u.id AS idx, u.email, u.active, ' . "\n" .
                     '  CONCAT(ui18n.firstname, " ", ui18n.lastname) AS name' . "\n" .
                     'FROM ' . DB_TABLE_USERS . ' AS u ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n ' . "\n" .
                     'ON u.id=ui18n.parent_id AND ui18n.lang="' . $lang . '"' . "\n" .
                     'WHERE u.id IN (' . implode(', ', $user_ids) . ')';
            $recipients = $db->getAssoc($query);

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $recipients;
    }

    /**
     * Sends notification email to a user
     *
     * @param string $template - name of system email template
     * @param string $email - recipient email
     * @param string $user_name - recipient name
     * @return bool - result of operation
     */
    public function sendNotification($template, $email, $user_name) {

        if (empty($email) || empty($template)) {
            return false;
        }

        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        $mailer = new Mailer($this->registry, $template, $this);
        $mailer->placeholder->add('budget_name', $this->get('company_name') . ' ' . $this->get('year'));
        $mailer->placeholder->add('finance_budget_added_by', $this->get('added_by_name'));
        $mailer->placeholder->add('user_name', $user_name);
        $mailer->placeholder->add('user_firstname', $this->registry['currentUser']->get('firstname'));
        $mailer->placeholder->add('user_lastname', $this->registry['currentUser']->get('lastname'));

        switch ($template) {
        case 'finance_budget_enter_data':
            $mailer->placeholder->add('item_name_path', $this->get('name_path'));
            $mailer->placeholder->add('responsible_name', $this->registry['currentUser']->get('firstname') . ' ' .
                                                          $this->registry['currentUser']->get('lastname'));
            $mailer->placeholder->add('item_status', $this->i18n('finance_budgets_status_' . $this->get('status')));
            $mailer->placeholder->add('comment', nl2br($this->get('comment')));
            break;
        case 'finance_budget_control_data':
            $mailer->placeholder->add('item_name_path', $this->get('name_path'));
            $mailer->placeholder->add('controlling_name', $this->registry['currentUser']->get('firstname') . ' ' .
                                                          $this->registry['currentUser']->get('lastname'));
            $mailer->placeholder->add('item_status', $this->i18n('finance_budgets_status_' . $this->get('status')));
            $mailer->placeholder->add('comment', nl2br($this->get('comment')));
            break;
        case 'finance_budget_assign_data':
        case 'finance_budget_delete_data':
            $mailer->placeholder->add('assignment_type', $this->i18n('finance_budgets_' . $this->get('assignment_type')));
            //get items (un)assigned to current recipient
            $cViewer = new Viewer($this->registry);
            $cViewer->setFrameset('frameset_blank.html');
            $cViewer->template = '_budgets_items_assigned.html';
            $cViewer->data['item_assignments'] = $this->get('item_assignments');
            $items_info = $cViewer->fetch();
            $mailer->placeholder->add('items_info', $items_info);
            break;
        default:
            break;
        }
        $mailer->template['model_name'] = $this->modelName;
        $mailer->template['model_id'] = $this->get('id');

        $model_view_url = sprintf('%s/index.php?%s=finance&%s=budgets&budgets=view&view=%d',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'],
                                    $this->registry['controller_param'], $this->get('id'));
        $mailer->placeholder->add('model_view_url', $model_view_url);

        $add_comment_url = sprintf('%s/index.php?%s=finance&%s=budgets&budgets=communications&communications=%d&communication_type=comments#comments_add_form',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'],
                                    $this->registry['controller_param'], $this->get('id'));
        $mailer->placeholder->add('finance_budget_add_comment_url', $add_comment_url);

        $mailer->placeholder->add('to_email', $email);

        $result = $mailer->send();
        if (!@in_array($email, $result['erred'])) {
            return true;
        } else {
            return false;
        }
    }
}

?>
