<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

/**
 * Finance_Annulment model class
 */
Class Finance_Annulment extends Finance_Document {
    use BelongsToTrait;

    public $modelName = 'Finance_Annulment';

    public $counter;

    public $checkPermissionsByStatus = true;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('num', 'issue_date', 'customer_name', 'name', 'company_name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->get('cheque')) {
            $this->set('payment_type_text',
                        $registry['translater']->translate('finance_payment_type_cheque'));
        } else {
            $this->set('payment_type_text',
                        $registry['translater']->translate('finance_payment_type_' . $this->get('payment_type')));
        }

        if ($this->get('container_id') && !$this->get('container_name')) {
            $query = 'SELECT ti18n.name' . "\n" .
                     'FROM ' . ($this->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS : DB_TABLE_FINANCE_CASHBOXES) . ' AS t' .  "\n" .
                     'LEFT JOIN ' . ($this->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N : DB_TABLE_FINANCE_CASHBOXES_I18N) . ' AS ti18n' . "\n" .
                     '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $this->get('model_lang') . '"' . "\n" .
                     'WHERE t.id=' . $this->get('container_id');
            $container_name = $registry['db']->GetOne($query);
            $this->set('container_name', $container_name, true);
        }

        if ($this->get('company') && !$this->get('company_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . "\n" .
                    'WHERE parent_id=' . $this->get('company');
            $company_name = $registry['db']->GetOne($query);
            $this->set('company_name', $company_name, true);
        }

        if ($this->get('office') && !$this->get('office_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                    'WHERE parent_id=' . $this->get('office');
            $company_name = $registry['db']->GetOne($query);
            $this->set('office_name', $company_name, true);
        }

        //assign fiscal currency
        $fiscal_currency = $this->registry['config']->getParam('finance', 'fiscal_currency');
        $this->set('fiscal_currency', $fiscal_currency, true);
        if ($fiscal_currency && $this->get('status') == 'finished') {
            $prec = $this->registry['config']->getSectionParams('precision');
            $prec_formats = array();
            foreach ($prec as $item => $format) {
                $prec_formats[$item] = '%.' . $format . 'F';
            }

            $fiscal_total_with_vat = sprintf($prec_formats['gt2_total_with_vat'], round($this->get('fiscal_total') + $this->get('fiscal_total_vat'), $prec['gt2_total_with_vat']));
            $this->set('fiscal_total_with_vat', $fiscal_total_with_vat, true);
            unset($prec);
            unset($prec_formats);
        }
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if (!$this->get('customer') && $action != 'translate') {
            $this->raiseError('error_finance_annulments_no_customer_specified', 'customer', null,
                              array($this->getLayoutName('customer', false)));
        }
        if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
            $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
        }
        if (!$this->get('office') && $action != 'translate') {
            $this->raiseError('error_finance_annulments_no_office_specified', 'company_data');
        }
        if (!$this->get('company') && $action != 'translate') {
            $this->raiseError('error_finance_annulments_no_company_specified', 'company_data');
        }
        // TODO: validation has to be made here
        // don't forget fiscal_event_date and date_of_payment

        //check counter
        if ($action != 'translate' && $this->get('company') && $this->get('type') && !$this->getCounter()) {
            $this->raiseError('error_finance_annulments_no_counter', 'type', '',
                              array($this->get('type_name'), $this->get('company_name'), $this->get('office_name')));
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();
                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        $this->set('status', 'finished', true);
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        if ($this->get('import_added')) {
            $set['added']     = sprintf("added='%s'", $this->get('import_added'));
        } else {
            $set['added']     = sprintf("added=now()");
        }
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError(sprintf($this->i18n('error_finance_annulments_no_counter'),
                                                      $this->get('type_name'), $this->get('company_name'), $this->get('office_name')));
            } else {
                $this->counter->increment();
            }
        } elseif ($this->get('import_num')) {
            //set num from import
            if ($this->counter) {
                $this->counter = null;
            }
            $this->getCounter();
            if ($this->counter) {
                $this->counter->increment();
            } else {
                $db->FailTrans();
                $this->registry['messages']->setError(sprintf($this->i18n('error_finance_annulments_no_counter'),
                                                      $this->get('type_name'), $this->get('company_name'), $this->get('office_name')));
            }
            $this->set('num', $this->get('import_num'), true);
            $set['num'] = sprintf("num='%s'", $this->get('import_num'));
        }

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_FINANCE_ANNULMENTS . "\n" .
                  'SET ' . implode(",\n", $set) . "\n";

        $r = $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new finance annulment base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //edit 2nd type grouping table data
        $this->saveGT2Vars();

        //update invoice relatives
        $this->updateReasonsRelatives();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Translated existing model
     *
     * @return bool - result of the operation
     */
    public function translate() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //IMPORTANT: prepare main data so that the customer name and address are fetched in the destination language!!!
        $this->prepareMainData();

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //save 2nd type grouping table data
        if (!$this->translateGT2Vars()) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('description')) {
            $update['description']  = sprintf("description='%s'", $this->get('description'));
        }
        if ($this->isDefined('total_no_vat_reason_text')) {
            $update['total_no_vat_reason_text'] = sprintf("`total_no_vat_reason_text`='%s'", $this->get('total_no_vat_reason_text'));
        }
        //set customer data
        $update['customer_name'] = sprintf("customer_name='%s'", General::slashesEscape($this->get('customer_name')));
        $update['customer_address'] = sprintf("customer_address='%s'", General::slashesEscape($this->get('customer_address')));

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_ANNULMENTS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('translating finance annulment i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();

        if ($this->isDefined('type')) {
            $set['type'] = sprintf("`type`='%d'", $this->get('type'));
        }

        if ($this->isDefined('company')) {
            $set['company'] = sprintf("`company`='%d'", $this->get('company'));
        }
        if ($this->isDefined('office')) {
            $set['office'] = sprintf("`office`='%d'", $this->get('office'));
        }
        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("`customer`='%d'", $this->get('customer'));

            //set customer data
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_customer = array('where' => array('c.id = ' . $this->get('customer'), 'c.deleted >= 0'),
                                      'sanitize'  => true,
                                      'model_lang' => $this->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters_customer);

            if ($this->get('import_customer_name')) {
                $this->set('customer_name', $this->get('import_customer_name'), true);
            } elseif (is_object($customer)) {
                $this->set('customer_name', trim($customer->get('name') . ' ' . $customer->get('lastname')), true);
            }
            if (is_object($customer)) {
                $this->set('customer_address', ($customer->get('is_company') ? $customer->get('registration_address') : $customer->get('address_by_personal_id')), true);
                $this->set('eik', ($customer->get('is_company') ? $customer->get('eik') : $customer->get('ucn')), true);
                $this->set('vat_num', $customer->get('in_dds'), true);
                $this->set('received_by', ($customer->get('is_company') ? $customer->get('mol') : trim($customer->get('name') . ' ' . $customer->get('lastname'))), true);
            }

            $set['eik'] = sprintf("`eik`='%s'", $this->get('eik'));
            $set['vat_num'] = sprintf("`vat_num`='%s'", $this->get('vat_num'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark'] = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("project=%d", $this->get('project'));
        }
        if ($this->isDefined('phase')) {
            $set['phase'] = sprintf("phase=%d", $this->get('phase'));
        }
        if ($this->isDefined('currency')) {
            $set['currency'] = sprintf("`currency`='%s'", $this->get('currency'));
        }
        if ($this->isDefined('container_id')) {
            $set['container_id'] = sprintf("`container_id`=%d", $this->get('container_id'));
        }
        if ($this->isDefined('employee')) {
            $set['employee'] = sprintf("`employee`=%d", $this->get('employee'));
        }

        $set['status'] = sprintf("`status`='%s'", $this->get('status'));
        $set['status_modified'] = sprintf("status_modified=now()");
        if ($this->get('custom_modified_by')) {
            $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->get('custom_modified_by'));
        } else {
            $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        }
        $this->set('issue_date', General::strftime('%Y-%m-%d'), true);
        $set['issue_date'] = "`issue_date`=NOW()";
        if (!$this->get('issue_by')) {
            $this->set('issue_by', $this->registry['currentUser']->get('id'), true);
            $this->set('issue_by_name', $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname'), true);
        }
        $set['issue_by'] = sprintf("issue_by=%d", $this->get('issue_by'));
        if (!$this->get('invoice_code')) {
            $this->set('invoice_code', $this->registry['currentUser']->get('invoice_code'), true);
        }
        $set['invoice_code'] = sprintf("invoice_code='%s'", $this->get('invoice_code'));
        if ($this->isDefined('payment_status')) {
            $set['payment_status'] = sprintf("`payment_status`='%s'", $this->get('payment_status'));
            $set['payment_status_modified'] = sprintf("payment_status_modified=now()");
        }
        $prec = $this->registry['config']->getSectionParams('precision');
        if ($this->isDefined('total')) {
            $set['total'] = sprintf("`total`='%.6f'", round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_discount_surplus_field')) {
            $set['total_discount_surplus_field'] = sprintf("`total_discount_surplus_field`='%s'", $this->get('total_discount_surplus_field'));
        }
        if ($this->isDefined('total_discount_value')) {
            $set['total_discount_value'] = sprintf("`total_discount_value`='%.6f'", round($this->get('total_discount_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_discount_percentage')) {
            $set['total_discount_percentage'] = sprintf("`total_discount_percentage`='%.6f'", round($this->get('total_discount_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_value')) {
            $set['total_surplus_value'] = sprintf("`total_surplus_value`='%.6f'", round($this->get('total_surplus_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_percentage')) {
            $set['total_surplus_percentage'] = sprintf("`total_surplus_percentage`='%.6f'", round($this->get('total_surplus_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_without_discount')) {
            $set['total_without_discount'] = sprintf("`total_without_discount`='%.6f'", round($this->get('total_without_discount'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_vat_rate')) {
            $set['total_vat_rate'] = sprintf("`total_vat_rate`='%.2f'", round($this->get('total_vat_rate'), 2));
        }
        if ($this->get('import_total_vat')) {
            $set['total_vat'] = sprintf("`total_vat`='%.6f'", $this->get('import_total_vat'));
        } elseif ($this->isDefined('total_vat')) {
            $set['total_vat'] = sprintf("`total_vat`='%.6f'",
                        round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total_vat']));
        }
        if ($this->get('import_total_with_vat')) {
            $set['total_with_vat'] = sprintf("`total_with_vat`='%.6f'", $this->get('import_total_with_vat'));
        } elseif ($this->isDefined('total_with_vat')) {
            $set['total_with_vat'] = sprintf("`total_with_vat`='%.6f'",
                        round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total_vat']) + round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_no_vat_reason')) {
            $set['total_no_vat_reason'] = sprintf("`total_no_vat_reason`='%d'", $this->get('total_no_vat_reason'));
        }
        //set fiscal_total and fiscal_total_vat
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        $currency = $this->registry['config']->getParam('finance', 'fiscal_currency');
        if (empty($currency)) {
            $currency = Finance_Currencies::getMain($this->registry);
        }
        $rate = Finance_Currencies::getRate($this->registry, $this->get('currency'), $currency);
        $set['fiscal_total'] = sprintf('`fiscal_total`= "%.6f"',
                                       round($rate * $this->get('total'), $prec['gt2_total']));
        $set['fiscal_total_vat'] = sprintf('`fiscal_total_vat`= "%.6f"',
                                           round($rate * $this->get('total_vat'), $prec['gt2_total']));
        if ($this->isDefined('payment_type')) {
            $set['payment_type'] = sprintf("`payment_type`='%s'", $this->get('payment_type'));
            if ($this->get('cheque')) {
                $set['cheque'] = '`cheque` = 1';
            } else {
                $set['cheque'] = '`cheque` = 0';
            }
        }
        if ($this->isDefined('date_of_payment_count')) {
            if ($this->get('date_of_payment_count') !== '') {
                if ($this->get('date_of_payment_point') == 'issue') {
                    $date_start = General::strftime($this->get('issue_date'));
                    if ($this->get('date_of_payment_period_type') == 'working') {
                        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                        $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date_start,
                            $this->get('date_of_payment_count'), 'after');
                    } else {
                        $date_of_payment = General::strftime('%Y-%m-%d',
                            strtotime('+' . $this->get('date_of_payment_count') . ' day', strtotime($date_start)));
                    }
                 } else {
                     $date_of_payment = array('count := ' . $this->get('date_of_payment_count'),
                                              'period_type := ' . $this->get('date_of_payment_period_type'),
                                              'period := day',
                                              'direction := after',
                                              'point := receive');
                     $date_of_payment = implode('\n', $date_of_payment);
                 }
                $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $date_of_payment);
                $this->set('date_of_payment', $date_of_payment, true);
            } elseif ($this->get('import_date_of_payment')) {
                $this->set('date_of_payment', $this->get('import_date_of_payment'), true);
                $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $this->get('date_of_payment'));
            } else {
                $this->set('date_of_payment', strftime('%Y-%m-%d'), true);
                $set['date_of_payment'] = sprintf('`date_of_payment`= "%s"', $this->get('date_of_payment'));
            }
        } elseif ($this->get('date_of_payment')) {
            $set['date_of_payment'] = sprintf("`date_of_payment`='%s'", $this->get('date_of_payment'));
        }
        if ($this->isDefined('advance')) {
            $set['advance'] = sprintf("`advance`='%d'", $this->get('advance'));
        }
        if ($this->isDefined('reason')) {
            $set['reason'] = sprintf('`reason` = "%s"', $this->get('reason'));
        }
        if ($this->get('fiscal_event_date')) {
            $set['fiscal_event_date'] = sprintf("`fiscal_event_date`='%s'", $this->get('fiscal_event_date'));
        }
        if ($this->get('date_of_receive')) {
            $set['date_of_receive'] = sprintf("`date_of_receive`='%s'", $this->get('date_of_receive'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }
        if ($this->get('annulled') && $this->get('annulled_by')) {
            $set['annulled'] = sprintf("annulled='%s'", $this->get('annulled'));
            $set['annulled_by'] = sprintf("annulled_by=%d", $this->get('annulled_by'));
        }
        $set['modified']       = sprintf("modified=now()");
        if ($this->get('custom_modified_by')) {
            $set['modified_by']    = sprintf("modified_by=%d", $this->get('custom_modified_by'));
        } else {
            $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        }

        return $set;
    }

    /**
     * get counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->unsanitize();
            }

            require_once 'finance.counters.factory.php';
            if (!$this->get('custom_counter')) {
                $filters = array('where' => array(
                                            'fc.model = "' . $this->modelName . '"',
                                            'fc.model_type = "' . $this->get('type') . '"',
                                            '(fco.office_id = "' . $this->get('office') . '" OR fco.office_id = 0)',
                                            'fc.company = "' . $this->get('company') . '"',
                                            'fc.active = 1',
                                            'fc.deleted_by = 0'),
                                 'sort' => array('fco.office_id DESC', 'fc.default DESC')
                                );
            } else {
                $filters = array('where' => array('fc.id = ' . $this->get('custom_counter')));
            }

            $this->counter = Finance_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Get number
     *
     * @param bool $force
     * @return string
     */
    public function getNum($force = false) {
        if (!$this->get('num') || $force) {

            //get the counter assigned to the finance annulment type
            $this->getCounter();

            if ($this->counter) {
                //define some the counter's fomula components
                $formula = $this->counter->get('formula');
                $prefix = $this->counter->get('prefix');
                $delimiter = $this->counter->get('delimiter');
                $zeroes = $this->counter->get('leading_zeroes');
                $date_format = $this->counter->get('date_format');

                //create extender to expand the formula components
                $extender = new Extender;

                //lock the counter for update to guarantee unique next number
                $query = 'SELECT next_number FROM ' . DB_TABLE_FINANCE_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                //set finance annulment number
                $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                $extender->add('num', $num);

                if ($this->counter->get('prefix')) {
                    //add this component to the extender
                    $extender->add('prefix', $prefix);
                }

                if ($this->counter->get('company_code') && $this->get('company')) {
                    //get customer code
                    $query = 'SELECT code FROM ' . DB_TABLE_FINANCE_COMPANIES . ' WHERE id=' . $this->get('company');
                    $company_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('company_code', $company_code);
                }

                if ($this->counter->get('office_code') && $this->get('office')) {
                    //get office code
                    $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
                    $office_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('office_code', $office_code);
                }

                if ($this->counter->get('user_code')) {
                    //get user code
                    //add this component to the extender
                    $extender->add('user_code', $this->registry['currentUser']->get('code'));
                }

                if ($this->counter->get('project_code') && $this->get('project')) {
                    //get project code
                    require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                    $filters = array('where' => array('p.id = ' . $this->get('project'),
                                                      'p.deleted IS NOT NULL'),
                                     'sanitize' => true);
                    $project = Projects::searchOne($this->registry, $filters);

                    //add this component to the extender
                    $extender->add('project_code', $project->get('code'));
                }

                if ($this->counter->get('document_date')) {
                    //replace the date
                    if ($this->get('issue_date')) {
                        $date = General::strftime($date_format, strtotime($this->get('issue_date')));
                    } elseif ($this->get('added')) {
                        $date = General::strftime($date_format, strtotime($this->get('added')));
                    } else {
                        $date = General::strftime($date_format);
                    }

                    //add this component to the extender
                    $extender->add('document_date', $date);
                }

                $num = $extender->expand($formula);
                if ($delimiter) {
                    //remove repeating delimiters
                    $num = preg_replace('#'. preg_quote($delimiter . $delimiter) .'#', $delimiter, $num);
                    $num = preg_replace('#'. preg_quote($delimiter) .'$#', '', $num);
                    $num = preg_replace('#^'. preg_quote($delimiter) .'#', '', $num);
                }

                $this->set('num', $num, true);
            }
        }

        return $this->get('num');
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Finance_Annulment\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted =  0'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        return $files;
    }

    /**
     * Get all patterns variables - basic/system, additional
     *
     * @return array $vars - variables
     */
    public function getPatternsVars() {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Finance_Annulment", "Customer", "CurrentUser") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $customer_translations = $customer->getTranslations();
        $t_customer = array();
        foreach ($customer_translations as $t_lang) {
            if ($t_lang != $customer->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $customer->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
            }
        }

        //get the pattern
        $pattern_id = $this->registry['request']->get('pattern');
        $filters = array('where' => array('p.id = ' . $pattern_id),
            'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        $t_user = array();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //prepare contact person
        if ($this->get('contact_person')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                       'c.subtype = \'contact\''),
                                      'sanitize'  => true,
                                      'model_lang' => $this->get('model_lang'));
            $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
            if ($contactperson) {
                $this->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
            }
        }

        //prepare customer's branch
        if ($this->get('branch')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                     'c.subtype = \'branch\''),
                                    'sanitize'  => true,
                                    'model_lang' => $this->get('model_lang'));
            $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
            if ($branch) {
                $this->set('branch_name', $branch->get('name'), true);
            }
        }

        //prepare cashbox/bank account
        if ($this->get('container_id')) {
            if ($this->get('payment_type') == 'bank') {
                require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                           'sanitize'  => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_bic', $container->get('bic'), true);
                    $this->set('container_iban', $container->get('iban'), true);
                    $this->set('container_bank', $container->get('bank'), true);
                }
            } else {
                require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                           'sanitize'  => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_location', $container->get('location'), true);
                }
            }
        }

        $translations = $this->getTranslations();
        if (empty($translations)) {
            $translations = array($this->get('model_lang'));
        }
        $t_reason = array();

        //save the previous registry lang
        $registry_lang_old = $this->registry['lang'];

        $this->getRelatives(array('get_parent_reasons' => 1));
        $parent_reason = $this->get('parent_reason');
        $this->set('parent_num', $parent_reason->get('num'), true);
        $this->set('parent_issue_date', $parent_reason->get('issue_date'), true);
        $this->set('parent_type_name', $parent_reason->get('type_name'), true);
        $query = 'SELECT lang, name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . ' WHERE parent_id=' . $parent_reason->get('type') . ' AND lang in ("' . implode('","', $translations) . '")';
        $parent_type_names = $this->registry['db']->GetAssoc($query);
        foreach ($translations as $t_lang) {
            $this->registry->set('lang', $t_lang, true);
            $this->registry['translater']->reloadFiles($t_lang);

            $filters = array('model_lang' => $t_lang,
                             'where' => array('fa.id = ' . $this->get('id')));
            $t_reason[$t_lang] = Finance_Annulments::searchOne($this->registry, $filters);
            $t_reason[$t_lang]->set('parent_type_name', (isset($parent_type_names[$t_lang]) ? $parent_type_names[$t_lang] : ''), true);
            if ($this->get('contact_person')) {
                $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                           'c.subtype = \'contact\''),
                                          'sanitize'  => true,
                                          'model_lang' => $t_lang);
                $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
                if ($contactperson) {
                    $t_reason[$t_lang]->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                }
            }
            if ($this->get('branch')) {
                $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                         'c.subtype = \'branch\''),
                                        'sanitize'  => true,
                                        'model_lang' => $t_lang);
                $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
                if ($branch) {
                    $t_reason[$t_lang]->set('branch_name', $branch->get('name'), true);
                }
            }
            if ($this->get('container_id')) {
                if ($this->get('payment_type') == 'bank') {
                    require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                    $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                              'sanitize'  => true,
                                              'model_lang' => $t_lang);
                    $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_bic', $container->get('bic'), true);
                        $t_reason[$t_lang]->set('container_iban', $container->get('iban'), true);
                        $t_reason[$t_lang]->set('container_bank', $container->get('bank'), true);
                    }
                } else {
                    require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                    $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                              'sanitize'  => true,
                                              'model_lang' => $t_lang);
                    $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_location', $container->get('location'), true);
                    }
                }
            }
            if ($this->get('issue_by') && !$t_reason[$t_lang]->isDefined('issue_by_name')) {
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';
                $filters_users = array('where'      => array('u.id=' . $this->get('issue_by')),
                                       'sanitize'   => true,
                                       'model_lang' => $t_lang);
                $issue_by_user = Users::searchOne($this->registry, $filters_users);
                $t_reason[$t_lang]->set('issue_by_name', ($issue_by_user ? trim($issue_by_user->get('firstname') . ' ' . $issue_by_user->get('lastname')) : ''), true);
            }
        }
        $this->registry->set('lang', $registry_lang_old, true);
        $this->registry['translater']->reloadFiles($registry_lang_old);

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            $pl_source = $placeholder->get('source');
            $pl_varname = $placeholder->get('varname');
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Finance_Annulment') {
                    //reason variables
                    if (!$placeholder->get('multilang')) {
                        if (preg_match('#^company_#', $pl_source)) {
                            $vars[$pl_varname] = $this->getCompanyData(str_replace('company_', '', $pl_source));
                        } else {
                            $vars[$pl_varname] = $this->get($pl_source);
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if (preg_match('#^company_#', $pl_source)) {
                                $vars[ $t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_reason[$t_lang]->getCompanyData(str_replace('company_', '', $pl_source));
                            } else {
                                $vars[ $t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_reason[$t_lang]->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer') {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $customer->get($pl_source);
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[ $t_customer[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_customer[$t_lang]->get($pl_source);
                            } else {
                                $vars[ $customer->get('model_lang') . '_' . $pl_varname] =
                                $customer->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $user->get($pl_source);
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_user[$t_lang]->get($pl_source);
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $pl_varname] =
                                $user->get($pl_source);
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($pl_source, '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $pl_source);
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$pl_varname] = $res;
                } else {
                    $vars[$pl_varname] = $pl_source;
                }
            }
        }

        //prepare additional variables

        //get print settings for the 2nd type grouping table
        $print_properties = $this->getGT2PrintSettings($pattern_id);

        $lang = $this->get('model_lang');
        foreach ($translations as $t_lang) {
            $this->set('model_lang', $t_lang, true);
            // set no VAT reason text in current language
            $this->set('total_no_vat_reason_text', $t_reason[$t_lang]->get('total_no_vat_reason_text'), true);

            if (!$this->get('table_values_are_set')) {
                $this->getGT2Vars();
            }
            $table = $this->get('grouping_table_2');

            //prepare files in GT2
            if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                foreach ($table['values'] as $ridx => $row) {
                    foreach ($row as $rkey => $rval) {
                        if (!empty($rval) && is_object($rval)) {
                            $file = $rval;
                            if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                            } else {
                                $file = '';
                            }
                            $table['values'][$ridx][$rkey] = $file;
                        }
                    }
                }
            }

            $table_ordered = $table;
            $table_ordered['vars'] = array();
            $styles_for_template = array();

            foreach ($print_properties as $key => $property) {
                // style properties
                if (!empty($property['style'])) {
                    $styles_for_template[$key] = $property['style'];
                }
                // label for table caption
                if ($key == 'var_' . $table['id']) {
                    if (isset($property['labels'][$t_lang])) {
                        $table_ordered['label'] = $property['labels'][$t_lang];
                    }
                    continue;
                }
                foreach ($table['vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        $table_ordered['vars'][$idx] = $var;
                        // label for field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        // aggregates
                        if (isset($property['agregate'])) {
                            if ($property['agregate'] != 'none') {
                                $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                            } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                unset($table_ordered['vars'][$idx]['agregate']);
                            }
                        }
                        continue 2;
                    }
                }
                foreach ($table['plain_vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        // label for total field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        continue 2;
                    }
                }
            }

            unset($table);

            // calculate aggregates in GT2 table
            $table_ordered = $this->calculateGT2Agregates($table_ordered);

            $this->set('grouping_table_2', $table_ordered, true);

            $groupingViewer = new Viewer($this->registry);
            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');
            $groupingViewer->data['table'] = $table_ordered;
            $groupingViewer->data['styles'] = $styles_for_template;
            $groupingViewer->data['pattern_id'] = $pattern_id;
            $vars[$t_lang . '_grouping_table_2'] = $groupingViewer->fetch();

            unset($table_ordered);
        }

        $this->set('model_lang', $lang, true);
        // set no VAT reason text in current language
        $this->set('total_no_vat_reason_text', (isset($t_reason[$lang]) ? $t_reason[$lang]->get('total_no_vat_reason_text') : ''), true);

        return $vars;
    }

    /**
     * Get reasons attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'' . $this->modelName . '\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted =  0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get annulment files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get related records for annulment
     *
     * @param array $params
     */
    public function getRelatives($params = array()) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        if (isset($params['get_parent_reasons']) && $params['get_parent_reasons'] || isset($params['get_all']) && $params['get_all']) {
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
            //get parent incomes reasons relative to this model
            $query = 'SELECT frr.link_to' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  ON frr.link_to = fir.id' . "\n" .
                 'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '  AND frr.parent_model_name = "Finance_Annulment"' . "\n";
            if (!empty($params['get_parent_reasons']) && is_array($params['get_parent_reasons'])) {
                foreach ($params['get_parent_reasons'] as $clause) {
                    $query .= "\n   AND" . $clause;
                }
            }

            if ($parent_reason = $db->GetOne($query)) {

                $filters = array('where' => array('fir.id =' . $parent_reason),
                                 'sanitize' => true);
                $parent_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                $this->set('parent_reason', $parent_reason, true);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }
    }

    /**
     * update reasons relatives
     *
     * @return bool
     */
    public function updateReasonsRelatives() {
        $db = $GLOBALS['registry']['db'];

        $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                 'WHERE parent_id=' . $this->get('id') . ' AND parent_model_name="Finance_Annulment"' . "\n";
        $records = $db->GetAll($query);

        $new_rows_links = $this->get('rows_links');

        if (!empty($records) && !empty($new_rows_links['deleted'])) {
            //invoice edit - remove deleted rows form the links
            if (!empty($new_rows_links['deleted'])) {
                foreach ($records as $record) {
                    foreach ($new_rows_links['deleted'] as $deleted) {
                        if (preg_match('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', $record['rows_links'])) {
                            $record['rows_links'] = preg_replace('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', "\n", $record['rows_links']);
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                                     ' SET rows_links = \'' . $record['rows_links'] . "'\n" .
                                     'WHERE parent_id = ' . $record['parent_id'] .
                                     '  AND parent_model_name="' . $record['parent_model_name'] . '" ' . "\n" .
                                     '  AND link_to= ' . $record['link_to'] . "\n" .
                                     '  AND link_to_model_name="' . $record['link_to_model_name'] . '" ';
                            $db->Execute($query);
                        }
                    }
                }
            }
        } elseif (!empty($new_rows_links['added'])) {
            //document adding
            foreach ($new_rows_links['added'] as $new_row => $old_row) {
                $new_rows_links['added'][$new_row] = $new_row . ' => ' . $old_row;
            }

            if ($this->get('link_to_model_name')) {
                $link_to_model_name = $this->get('link_to_model_name');
            } else {
                $link_to_model_name = "Finance_Annulment";
            }

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     ' SET parent_id = ' . $this->get('id') . ",\n" .
                     '     parent_model_name = "Finance_Annulment",' . "\n" .
                     '     link_to = ' . $this->get('link_to') . ",\n" .
                     '     link_to_model_name = "' . $link_to_model_name . '"' . ",\n" .
                     '     rows_links = \'' . implode("\n", $new_rows_links['added']) . '\'';
            $db->Execute($query);

        }
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'finance_annulments', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Finance_Annulment->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Annulment ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'locked' || $this->get('status') == 'finished') {
                //locked or finished status status
                switch ($action) {
                //forbidden actions
                case 'edit':
                    return false;
                    break;
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } else {
                //opened status
                switch ($action) {
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            }
        } else {
            return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                $this,
                $action,
                $modulePermissionsKey,
                $force
            );
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Some of the invoices are issued from a contract. Sometimes the contract data is needed.
     * The annulment issued for an invoice could also be related to a contract (via invoice issued from contract)
     *
     * @param array $data - contract data to be fetched (e.g. id, trademark, etc.)
     * @return array $result - associative array with fetched data
     */
    public function getContractData($data) {
        if (empty($data)) {
            //nothing to fetch
            return array();
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        $result = array();

        //get incomes reason relative to this document(invoice, correct reason, proforma invoice)
        $query = 'SELECT frr.link_to, (SELECT fir.type FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir WHERE fir.id=link_to) AS type' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name = "Finance_Annulment"' . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                 'LIMIT 1';
        $reason = $db->GetRow($query);

        //initialize contract id
        $contract_id = 0;

        if (!empty($reason)) {
            if ($reason['type'] == PH_FINANCE_TYPE_INVOICE || $reason['type'] == PH_FINANCE_TYPE_PRO_INVOICE) {
                //check if the invoice is from contract
                $query = 'SELECT frr.link_to, frr.link_to_model_name' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                     '  ON frr.link_to = fir.id AND frr.link_to_model_name = "Finance_Incomes_Reason" ' . "\n" .
                     '  AND fir.annulled_by=0 AND fir.type > ' . PH_FINANCE_TYPE_MAX . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                     '  ON frr.link_to = co.id AND frr.link_to_model_name = "Contract" ' . "\n" .
                     'WHERE frr.parent_id = ' . $reason['link_to'] . "\n" .
                     '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     'LIMIT 1';
                $reason = $db->GetRow($query);

                if ($reason['link_to_model_name'] == 'Contract') {
                    $contract_id = $reason['link_to'];
                }
            } elseif ($reason['type'] == PH_FINANCE_TYPE_CREDIT_NOTICE || $reason['type'] == PH_FINANCE_TYPE_DEBIT_NOTICE) {
                //get the debit/credit notice invoice
                //check if the invoice is from contract
                $query = 'SELECT frr.link_to' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                         'WHERE frr.parent_id = ' . $reason['link_to'] . "\n" .
                         '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                         '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                         'LIMIT 1';
                $invoice_id = $db->GetOne($query);

                if ($invoice_id) {
                    //now get the contract
                    $query = 'SELECT frr.link_to' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                             'WHERE frr.parent_id = ' . $invoice_id . "\n" .
                             '  AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                             '  AND frr.link_to_model_name = "Contract"' . "\n" .
                             'LIMIT 1';
                    $contract_id = $db->GetOne($query);
                }
            }

            if (!empty($contract_id)) {
                $query = 'SHOW COLUMNS FROM ' . DB_TABLE_CONTRACTS;
                $main_table_columns = $db->GetCol($query);

                $query = 'SHOW COLUMNS FROM ' . DB_TABLE_CONTRACTS_I18N;
                $i18n_main_table_columns = $db->GetCol($query);

                //finally we got a contract id, now process the requested data
                $fields = array();
                foreach ($data as $param) {
                    if (in_array($param, $main_table_columns)) {
                        $fields[] = 'co.' . $param;
                    } elseif (in_array($param, $i18n_main_table_columns)) {
                        $fields[] = 'coi18n.' . $param;
                    } elseif ($param == 'trademark_name') {
                        $fields[] = 'ni18n.name AS ' . $param;
                    }
                }
                $query = 'SELECT ' . implode(', ', $fields) . "\n" .
                         'FROM ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n' . "\n" .
                         '  ON (co.id=coi18n.parent_id AND coi18n.lang="'.$this->get('model_lang').'")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                         ' ON (co.trademark=ni18n.parent_id AND ni18n.lang="' . $this->get('model_lang') . '")' . "\n" .
                         'WHERE co.id=' . $contract_id . "\n";
                $result = $db->GetRow($query);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Method to define and set group of a new model (without id) based on
     * default group type setting<br />
     * IMPORTANT: Always pass $parent_group parameter when creating secondary documents.
     *
     * @param mixed $parent_group - group of direct parent or false if there is no parent
     * @param mixed $type_default_group - default group from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setGroup($parent_group = false, $type_default_group = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_group === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $group = '';
        if ($type_default_group !== false) {
            $group = $type_default_group;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_group' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $group = $this->registry['db']->GetOne($query);
        }
        // if setting is parent group
        if ($group == '[parent_group]') {
            // if value is passed for parent group
            if ($parent_group !== false) {
                $group = $parent_group;
            } else {
                // if there is no parent document,
                // group will be defined according to current user
                $group = '[default_user_group]';
            }
        }
        // get the default group of the user
        if ($group == '[default_user_group]') {
            if ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $group = $this->registry['originalUser']->get('default_group');
            } else {
                // get the default group id from the current user
                $group = $this->registry['currentUser']->get('default_group');
            }
            // set "All" if user has no default group
            if (!$group) {
                $group = PH_ROOT_GROUP;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('group', $group, true);
    }

    /**
     * Method to define and set department of a new model (without id) based on
     * default department type setting<br />
     * IMPORTANT: Always pass $parent_department parameter when creating secondary documents.
     *
     * @param mixed $parent_department - department of direct parent or false if there is no parent
     * @param mixed $type_default_department - default department from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setDepartment($parent_department = false, $type_default_department = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_department === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $department = '';
        if ($type_default_department !== false) {
            $department = $type_default_department;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_department' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $department = $this->registry['db']->GetOne($query);
        }
        // if setting is parent department
        if ($department == '[parent_department]') {
            // if value is passed for parent department
            if ($parent_department !== false) {
                $department = $parent_department;
            } else {
                // if there is no parent document,
                // department will be defined according to current user
                $department = '[default_user_department]';
            }
        }
        // get the default department of the user
        if ($department == '[default_user_department]') {
            if ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $department = $this->registry['originalUser']->get('default_department');
            } else {
                // get the default department id from the current user
                $department = $this->registry['currentUser']->get('default_department');
            }
            // set "All" if user has no default department
            if (!$department) {
                $department = PH_DEPARTMENT_FIRST;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('department', $department, true);
    }
}

?>
