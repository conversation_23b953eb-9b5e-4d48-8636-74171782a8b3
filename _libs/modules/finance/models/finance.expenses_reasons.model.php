<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

/**
 * Finance_Expenses_Reason model class
 */
Class Finance_Expenses_Reason extends Finance_Document {
    use BelongsToTrait;

    public $modelName = 'Finance_Expenses_Reason';

    public $counter;

    public $checkPermissionsByStatus = true;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('num', 'issue_date', 'customer_name', 'name', 'company_name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->get('id') && $registry->get('getAssignments')) {
            $this->getAssignments('responsible');
            $this->getAssignments('decision');
            $this->getAssignments('observer');
            $this->getAssignments();
        }

        if ($this->get('annulled_by') && !$this->get('annulled_by_name')) {
            $query = 'SELECT CONCAT(ui18n.firstname, " ", ui18n.lastname) AS annulled_by_name ' . "\n" .
                     'FROM ' . DB_TABLE_USERS_I18N . ' AS ui18n ' . "\n" .
                     'WHERE ui18n.parent_id=' . $this->get('annulled_by') . ' AND ui18n.lang="' . $registry['lang'] . '"';
            $annulled_by_name = $registry['db']->GetOne($query);
            $this->set('annulled_by_name', $annulled_by_name, true);
        }

        if ($this->get('type')) {
            if (!$this->get('type_name')) {
                $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                         'WHERE parent_id=' . $this->get('type') . ' AND lang="' . $registry['lang'] . '"';
                $type_name = $registry['db']->GetOne($query);
                $this->set('type_name', $type_name, true);
            }

            if ($this->get('id')) {
                //get the type of the first document in the chain
                $this->set('parent_type', $this->getExpensesRootType(), true);
            } elseif ($this->get('invoice_id')) {
                //yet not saved credit/debit notice
                $rootReason = Finance_Expenses_Reasons::searchOne($this->registry, array('where' => array('fer.id = ' . $this->get('invoice_id'))));
                $this->set('parent_type', $rootReason->getExpensesRootType(), true);
                unset($rootReason);
            } elseif (in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                $this->set('parent_type', PH_FINANCE_TYPE_EXPENSES_INVOICE, true);
            }
        }

        // if payment is added then the data for it is taken from fields with prefix 'payment_'
        if ($this->isDefined('payment_container_rate')) {
            $this->set('container_rate', $this->get('payment_container_rate'), true);
            $this->unsetProperty('payment_container_rate', true);
        }
        if ($this->isDefined('payment_container_amount')) {
            $this->set('container_amount', $this->get('payment_container_amount'), true);
            $this->unsetProperty('payment_container_amount', true);
        }
        if ($this->isDefined('payment_container_currency')) {
            $this->set('container_currency', $this->get('payment_container_currency'), true);
            $this->unsetProperty('payment_container_currency', true);
        }

        // in add mode: keep company in a hidden field and get it when company_data field has no value
        if (!$this->get('company') && $this->get('original_company')) {
            $this->set('company', $this->get('original_company'), true);
            $this->unsetProperty('original_company', true);
        }

        if ($this->get('company') && !$this->get('company_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . "\n" .
                     'WHERE parent_id=' . $this->get('company');
            $company_name = $registry['db']->GetOne($query);
            $this->set('company_name', $company_name, true);
        }

        if ($this->get('office') && !$this->get('office_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                    'WHERE parent_id=' . $this->get('office');
            $company_name = $registry['db']->GetOne($query);
            $this->set('office_name', $company_name, true);
        }

        if ($this->get('cheque')) {
            $this->set('payment_type_text',
                       $registry['translater']->translate('finance_payment_type_cheque'));
        } elseif ($this->get('payment_type')) {
            $this->set('payment_type_text',
                       $registry['translater']->translate('finance_payment_type_' . $this->get('payment_type')));
        }

        if ($this->get('container_id') && !$this->get('container_name')) {
            $query = 'SELECT ti18n.name' . "\n" .
                     'FROM ' . ($this->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS : DB_TABLE_FINANCE_CASHBOXES) . ' AS t' . "\n" .
                     'LEFT JOIN ' . ($this->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N : DB_TABLE_FINANCE_CASHBOXES_I18N) . ' AS ti18n' . "\n" .
                     '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $this->get('model_lang') . '"' . "\n" .
                     'WHERE t.id=' . $this->get('container_id');
            $container_name = $registry['db']->GetOne($query);
            $this->set('container_name', $container_name, true);
        }

        if ($this->get('accounting_period') && $this->get('accounting_period') != '0000-00-00') {
            $this->set('accounting_month', General::strftime('%m', strtotime($this->get('accounting_period'))), true);
            $this->set('accounting_year', General::strftime('%Y', strtotime($this->get('accounting_period'))), true);
        }
    }

    /**
     * Gets type of root parent of current expense document
     * IMPORTANT: function searches only for expense document relatives
     *
     * @return int - type of root parent
     */
    public function getExpensesRootType() {
        $root_type = $this->get('type');
        $child_id = $this->get('id');

        // if model has no id, its relations cannot be searched
        if (!$child_id) {
            return $root_type;
        }

        $db = $this->registry['db'];

        $sql = 'SELECT fer.id, fer.type' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
               'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
               '  ON frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to=fer.id' . "\n" .
               'WHERE frr.parent_model_name="Finance_Expenses_Reason" AND frr.parent_id=%d';

        do {
            $query = sprintf($sql, $child_id);
            $root_reason_info = $db->GetAll($query);

            // use the info only when there is one parent (and not multiple proforma parents)
            if (!empty($root_reason_info) && count($root_reason_info) == 1) {
                $root_reason_info = array_shift($root_reason_info);
                // if types are the same, relation is from cloning, do not search further
                if ($root_type == $root_reason_info['type']) {
                    $root_reason_info = array();
                } else {
                    $root_type = $root_reason_info['type'];
                    $child_id = $root_reason_info['id'];
                }
            } else {
                $root_reason_info = array();
            }
        } while (!empty($root_reason_info));

        return $root_type;
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - current action
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if (!$this->get('customer') && $action != 'translate') {
            $this->raiseError('error_finance_expenses_reasons_no_customer_specified', 'customer', null,
                              array($this->getLayoutName('customer')));
        }
        if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
            $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
        }
        if ((!$this->get('company') || !$this->get('office') || !$this->get('container_id')) && $action != 'translate') {
            $this->raiseError('error_finance_expenses_reasons_no_container_id_specified', 'company_data', null,
                              array($this->getLayoutName('company_data')));
        }
        if (!$this->get('issue_date') && $action != 'translate') {
            $this->raiseError('error_finance_expenses_reasons_no_issue_date_specified', 'issue_date', null,
                              array($this->getLayoutName('issue_date')));
        }

        //check counter
        if ($this->get('company') && $this->get('type') && !$this->getCounter()) {
            $this->raiseError('error_finance_expenses_reasons_no_counter', 'type', 0,
                              array($this->get('type_name'), $this->get('company_name'), $this->get('office_name')));
        } elseif ($this->get('issue_date') && $this->get('min_issue_date') && $this->get('issue_date') < $this->get('min_issue_date')) {
            $this->raiseError('error_finance_expenses_reasons_before_min_issue_date', 'issue_date', null,
                              array($this->getLayoutName('issue_date', false),
                                    General::strftime($this->i18n('date_short'), $this->get('min_issue_date'))));
        }

        if ($this->get('finance_after_action') && preg_match('#payment#', $this->get('finance_after_action'))) {
            if (!$this->get('employee1')) {
                $this->raiseError('error_finance_expenses_reasons_no_employee_specified', 'employee1', null,
                                  array($this->getLayoutName('employee')));
            }
            if ($this->registry['action'] != 'ajax_prepare_payment_form' && round($this->get('container_rate'), 6) <= 0) {
                $this->raiseError('error_finance_payments_no_container_rate', 'container_rate', null,
                                  array($this->get('currency'), $this->get('container_currency')));
            }
        }

        if (!$this->isActivated() && $this->get('type') &&
        ($this->get('type') <= PH_FINANCE_TYPE_MAX &&
        ($this->get('status') == 'finished' || $this->get('finance_after_action')) ||
        $this->get('type') > PH_FINANCE_TYPE_MAX &&
        $this->get('finance_after_action') && preg_match('#payment#', $this->get('finance_after_action')))) {
            $this->raiseError('error_finance_deactivate_' . ($this->get('type') <= PH_FINANCE_TYPE_MAX ? 'finished' : 'payment'),
                              'activate', null, array($this->getModelTypeName()));
        }

        $query = 'SELECT mandatory_num FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' WHERE id = ' . $this->get('type');
        $mandatory_num = $this->registry['db']->GetOne($query);
        if ($mandatory_num && !$this->get('invoice_num') && $action != 'translate') {
            $this->raiseError('error_no_invoice_num', 'invoice_num', null,
                           array($this->getLayoutName('invoice_num', false)));
        }
        //check for unique invoice_num for the customer of the document
        if ($this->get('invoice_num') && $this->get('customer')) {
            $query = 'SELECT id, issue_date, fdt.name as type_name ' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . " AS fer\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . " AS fdt\n" .
                     '  ON fer.type = fdt.parent_id AND fdt.lang="' . $this->registry['lang'] . '"' . "\n".
                     'WHERE fer.invoice_num="' . General::slashesEscape($this->get('invoice_num')) . '"' . "\n" .
                     '      AND fer.customer=' . $this->get('customer') . "\n" .
                     '      AND fer.annulled=0 AND fer.active!=0';
            if ($this->get('id')) {
                $query .= ' AND id!=' . $this->get('id');
            }
            $records = $this->registry['db']->GetRow($query);

            if ($records) {
                $placeholders = array(
                    'type_name' => mb_strtolower($records['type_name'], 'UTF-8'),
                    'link' => $_SERVER['SCRIPT_NAME'] . '?' . $this->registry['module_param'] . '=finance&amp;' . $this->registry['controller_param'].'=expenses_reasons&amp;expenses_reasons=view&amp;view=' . $records['id'],
                );
                $this->raiseError('error_invoice_num_not_unique', 'invoice_num', 0, $placeholders);
            }
        }
        if ($this->get('type') < PH_FINANCE_TYPE_MAX && $this->get('type') != PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON) {
            if (!$this->get('date_of_payment') && $action != 'translate') {
                $this->raiseError('error_no_invoice_num', 'date_of_payment', null,
                                  array($this->getLayoutName('date_of_payment')));
            }
        }
        if ($this->get('date_of_payment') && $this->get('date_of_payment') != '0000-00-00' && $this->get('date_of_payment') < $this->get('issue_date')) {
            $this->raiseError('error_payment_before_issue', 'date_of_payment', null,
                              array($this->getLayoutName('date_of_payment'), $this->getLayoutName('issue_date')));
        }

        if ($this->get('type') < PH_FINANCE_TYPE_MAX && $this->get('type') != PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
            if ($this->get('admit_VAT_credit') && $this->isDefined('accounting_month') && $this->isDefined('accounting_year')) {
                //validate accounting period
                if (!$this->get('accounting_month') || !$this->get('accounting_year')) {
                    //not correct period specified
                    $this->raiseError('error_no_accounting_period', 'accounting_period', null,
                                      array($this->getLayoutName('accounting_period')));
                }
                //make chosen period
                if ($this->get('accounting_month') < 10 && !preg_match('#^0\d$#', $this->get('accounting_month'))) {
                    $accounting_period = $this->get('accounting_year') . '-0' . $this->get('accounting_month');
                } else {
                    $accounting_period = $this->get('accounting_year') . '-' . $this->get('accounting_month');
                }
                //get the day after which we cannot distribute the document for previous month
                $deny_num = $this->registry['config']->getParam('finance', 'deny_previous_month_after');
                //make the period we issue document for
                $issue_period = General::strftime('%Y-%m', strtotime($this->get('issue_date')));

                if ($accounting_period < $issue_period) {
                    $this->raiseError('error_accounting_period_before_issue_date', 'accounting_period');
                }
                $allowed_period = array();
                if (!empty($deny_num) && $deny_num < General::strftime('%d', time())) {
                    //make the allowed period
                    $allowed_period['min'] = $allowed_period['max'] = General::strftime('%Y-%m', time());
                } else {
                    //make the allowed period
                    $allowed_period['max'] = General::strftime('%Y-%m', time());
                    if (!empty($deny_num)) {
                        $allowed_period['min'] = General::strftime('%Y-%m', strtotime('-1 month', strtotime($allowed_period['max'] . '-01')));
                    } else {
                        $months_back = 1;
                        $allowed_period['min'] = General::strftime('%Y-%m', strtotime('-' . $months_back . ' month', strtotime($allowed_period['max'] . '-01')));
                    }
                }

                if ($accounting_period < $allowed_period['min'] || $accounting_period > $allowed_period['max']) {
                    $this->raiseError('error_accounting_period_out_of_allowed', 'accounting_period', null,
                                      array($this->getLayoutName('accounting_period')));
                }
            }
        }

        if (in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE)) && !trim($this->get('reason'))) {
            $this->raiseError('help_finance_incomes_reasons_cd_reason', 'reason');
        }

        if (
            ($this->get('status') == 'finished' || $this->get('finance_after_action')) &&
            $this->get('allocated_status') == 'enabled' &&
            !$this->checkContainsNonCommodities()
        ) {
            $this->raiseError('error_allocated_status_not_allowed', 'allocated_status');
        } elseif (
            !($this->get('status') == 'finished' || $this->get('finance_after_action')) &&
            $this->get('allocated_status') == 'allocated'
        ) {
            $this->raiseError('error_accounting_period_out_of_allowed', 'allocated_status', 0, array($this->i18n('finance_expenses_reasons_allocated_status')));
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively

            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * Function to validate and save credit/debit notes
     * from expenses reasons
     *
     * @return boolean - result of the operation
     */
    public function saveCreditDebit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //get invoice we issue credit/debit for
        if (!in_array($this->registry['action'], array('addcredit', 'adddebit'))) {
            //get parent invoice id
            $this->getRelatives(array('get_parent_reasons' => true));
            list($invoice) = $this->get('parent_reasons');
            $this->unsetProperty('parent_reasons', true);
            $this->set('invoice_id', $invoice->get('id'), true);
        }
        $filters = array('where' => array('fer.id = ' . $this->get('invoice_id')), 'sanitize' => true);
        $invoice = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        $this->registry->set('get_old_vars', true, true);
        $invoice->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);
        $inv_gt2 = $invoice->get('grouping_table_2');

        $new_articles = $this->registry['request']->get('article_id');
        $new_articles = array_filter($new_articles, function($a) { return !empty($a);});
        $new_deleted = $this->registry['request']->get('deleted');
        $new_articles_names = $this->registry['request']->get('article_name');
        $new_prices = $this->registry['request']->get($this->registry['request']->get('calc_price'));
        $new_quantities = $this->registry['request']->get('quantity');
        $keys = array_flip(array_keys($new_articles));

       if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
            //we have to validate gt2 rows
            $articles = array();
            //get all articles in the invoice
            foreach ($inv_gt2['values'] as $k => $v) {
                if (isset($articles[$v['article_id']])) {
                    $articles[$v['article_id']] += $v['quantity'];
                } else {
                    $articles[$v['article_id']] = $v['quantity'];
                }
            }

            foreach ($new_articles as $k => $v) {
                //TODO: Check quantities if needed
                if (empty($new_deleted[$k])) {
                    //row is active so check it
                    if (!isset($articles[$v]) && !isset($inv_gt2['system_articles'][$v])) {
                        //check if the article exists in the invoice
                        $this->raiseError('error_finance_expenses_reasons_article_not_exists', '', '', array($new_articles_names[$k], $keys[$k]+1));
                        $db->FailTrans();
                    }
                    if ($new_prices[$k] > 0 && $new_quantities[$k] > 0 || $new_prices[$k] < 0 && $new_quantities[$k] < 0) {
                        //check if we really enter negative price - for credit
                        $this->raiseError('error_finance_expenses_reasons_positive_price', '', '', array($new_articles_names[$k], $keys[$k]+1));
                        $db->FailTrans();
                    } elseif ($new_prices[$k] == 0 || $new_quantities[$k] == 0) {
                        unset($new_articles[$k]);
                    }
                }
            }
        } elseif ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE) {
            foreach ($new_articles as $k => $v) {
                if ($new_prices[$k] == 0 || $new_quantities[$k] == 0) {
                    unset($new_articles[$k]);
                }
            }
        }

        if ((empty($new_articles) && $this->registry['action'] != 'edit' || $this->get('total_with_vat') == 0)
                && ($this->get('status') == 'finished' || $this->registry['request']->get('finance_after_action'))) {
            $this->raiseError('error_finance_expenses_reasons_empty_rows');
            $db->FailTrans();
        }

        if (in_array($this->registry['action'], array('addcredit', 'adddebit'))) {
            $this->set('link_to', $this->get('invoice_id'), true);
            $this->set('id', null, true);
        }

        if (!$this->save()) {
            $db->FailTrans();
        }

        // if saving a credit note that can be fully allocated to parent invoice, do that automatically
        // make sure amount of credit note is less than 0
        if (!$db->HasFailedTrans() && $this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE &&
        $this->get('status') == 'finished' && $this->registry['request']->get('finance_after_action') != 'payment' &&
        bccomp($this->get('total_with_vat'), 0, 2) == -1 &&
        bccomp($invoice->get('total_with_vat') - $invoice->getPaidAmount(), abs($this->get('total_with_vat')), 2) > -1) {
            $this->set('relatives_payments', array($invoice->get('id') => abs(round($this->get('total_with_vat'), 2))), true);
            if ($this->updateFinanceRelatives('expenses_invoice')) {
                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_edit_payment_success',
                                                        array($this->getModelTypeName())));
            } else {
                //some error occurred
                $this->registry['messages']->setWarning($this->i18n('error_finance_expenses_reasons_edit_payment_failed',
                                                        array($this->getModelTypeName())));
            }
            $this->unsetProperty('relatives_payments', true);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->get('finance_after_action') != '') {
            $this->set('status', 'finished', true);
        }

        if ($this->get('status') == 'finished') {
            if (!$this->get('num')) {
                $set['num'] = sprintf("num='%s'", $this->getNum());
                if (!$this->counter) {
                    $db->FailTrans();
                    $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_no_counter',
                                                          array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
                } else {
                    $this->counter->increment();
                }
            }
            if ((!$this->get('fiscal_event_date') || $this->get('fiscal_event_date') == '0000-00-00')
              && in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                $set['fiscal_event_date'] = sprintf("fiscal_event_date='%s'", $this->get('issue_date'));
            }
        }

        //query to insert the main table
        $query1 = 'INSERT ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                  'SET ' . implode(",\n", $set) . "\n";

        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new finance expenses reason base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if ($this->get('transform_params')) {
            //save relation between models and update the "transform from" model
            $this->saveTransformationDetails();
        }

        //edit 2nd type grouping table data
        $this->saveGT2Vars(false, true);
        if ($this->get('transform_params') && $this->get('rows_links')) {
            //update GT2 rows links after gt2 rows for model are saved
            $this->updateRowsLinks();
        }
        if ($this->get('status') && $this->get('status') != 'opened') {
            $this->setAvailableQuantities();
        }
        if ($this->isDefined('new_handovered_status')) {
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE || $this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                // IMPORTANT: the handover status of all expenses reasons should be 'none'
                // when expense (proforma) invoice is issued from expense reason document(s)
                // Some reports/dashlets pass link_to/link_to_model_name as array (create invoice from multiple documents)
                if ($this->get('link_to')) {
                    $link_to = is_array($this->get('link_to')) ? $this->get('link_to') : array($this->get('link_to'));
                    $link_to_model_name = is_array($this->get('link_to_model_name')) ? $this->get('link_to_model_name') : array($this->get('link_to_model_name'));
                    $tmp_model = clone $this;
                    $tmp_model->set('new_handovered_status', 'none', true);
                    foreach ($link_to as $idx => $lt) {
                        if (!empty($link_to_model_name[$idx]) && $link_to_model_name[$idx] == 'Finance_Expenses_Reason') {
                            $query = 'SELECT id, type FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' WHERE id = ' . $lt;
                            $parent = $db->GetRow($query);
                            if ($parent['type'] > PH_FINANCE_TYPE_MAX) {
                                //set parent document handover status to NONE
                                $tmp_model->set('id', $parent['id'], true);
                                $tmp_model->setHandoveredStatus();
                            }
                        }
                    }
                    unset($tmp_model);
                }
            }
            if ($this->get('type') != PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                $this->setHandoveredStatus();
            }
        }
        if ($this->get('finance_after_action') == 'payment') {
            //add payment document
            if (!$this->addPayment()) {
                $db->FailTrans();
            }
        }
        if ($this->get('type') <= PH_FINANCE_TYPE_MAX && $this->get('link_to') || $this->get('link_to_model_name') == 'Contract') {
            //update invoice relatives after gt2 rows for model are saved
            $this->updateReasonsRelatives();
        }
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // move payments from parent model(s) to invoice/proforma
        $this->updateInvoicePayments();

        if ($this->get('status') == 'finished'
            && (in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE))
            || $this->get('type') > PH_FINANCE_TYPE_MAX)) {
            //update nomenclatures prices
            $this->updateNomPrices();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * "Move" payments from expenses reason to expenses invoice/proforma OR
     * from expenses proforma(s) to expenses invoice when adding child document.
     *
     * @return boolean - result of the operation
     */
    public function updateInvoicePayments() {

        if (!in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE))) {
            return true;
        }

        //if cloned the balance rows should not be modified
        if ($this->get('transform_params')) {
            $transform_params = unserialize(General::slashesStrip($this->get('transform_params')));
            if (!empty($transform_params['origin_method']) && $transform_params['origin_method'] == 'clone') {
                return true;
            }
        }

        $parent_ids = array();
        $parent_type = '';

        //get direct parent(s)
        $this->getRelatives(array('get_parent_reasons' => true));
        $reasons = $this->get('parent_reasons');
        $this->unsetProperty('parent_reasons', true);
        if (!empty($reasons)) {
            foreach ($reasons as $reason) {
                $parent_ids[] = $reason->get('id');
                if (!$parent_type) {
                    $parent_type = $reason->get('type');
                }
            }
        }
        unset($reasons);

        if ($parent_ids) {
            // get all balance rows
            $query = 'SELECT CONCAT(id, "^", paid_to_model_name, "|", paid_to) AS idx, paid_amount' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                     'WHERE parent_model_name = "Finance_Expenses_Reason" AND parent_id IN (' . implode(', ', $parent_ids) . ')';
            $payment_rows = $this->registry['db']->GetAssoc($query);

            // collect info for balance rows to update and delete
            $delete_rows = array();
            $update_rows = array();
            // there cannot be multiple relations between invoice/proforma and the same payment
            // so we sum the amount in one row and delete the other rows
            foreach ($payment_rows as $key => $paid_amount) {
                list($row, $payment_key) = explode('^', $key);
                if (array_key_exists($payment_key, $update_rows)) {
                    $update_rows[$payment_key]['paid_amount'] += $paid_amount;
                    $delete_rows[] = $row;
                } else {
                    $update_rows[$payment_key] = array('id' => $row, 'paid_amount' => $paid_amount);
                }
            }

            if ($delete_rows) {
                $query = 'DELETE FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                         'WHERE id IN (' . implode(', ', $delete_rows) . ')';
                $this->registry['db']->Execute($query);
            }

            $update_sql = 'UPDATE ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                          'SET parent_id = %d, paid_amount = %.2f, modified = now(), modified_by = %d' . "\n" .
                          'WHERE id = %d';

            foreach ($update_rows as $payment_info) {
                $query = sprintf($update_sql,
                                 $this->get('id'), $payment_info['paid_amount'],
                                 $this->registry['currentUser']->get('id'), $payment_info['id']);
                $this->registry['db']->Execute($query);
            }

            // update payment status of parent proforma(s) to 'invoiced'
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE && $parent_type == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                $this->updateProformaPaymentStatus();
            }
        }

        return true;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //get current model status
        $query = 'SELECT status FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' WHERE id = ' . $this->get('id');
        $old_status = $db->GetOne($query);

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        if ($this->get('finance_after_action') != '') {
            $this->set('status', 'finished', true);
        }

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
                return false;
            } else {
                $this->counter->increment();
            }
        }

        if ($this->get('status') == 'finished' || $this->get('edit_finished')) {
            if ((!$this->get('fiscal_event_date') || $this->get('fiscal_event_date') == '0000-00-00')
              && in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                $set['fiscal_event_date'] = sprintf("fiscal_event_date='%s'", $this->get('issue_date'));
            }
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if (!$this->get('edit_finished')) {
            //save 2nd type grouping table data
            $this->saveGT2Vars(false, true);
            if ($this->get('status') && $this->get('status') != 'opened' && $old_status == 'opened' || $this->get('correction')) {
                $this->setAvailableQuantities();
            }

            if ($this->isDefined('new_handovered_status')) {
                $this->setHandoveredStatus();
            }

            if ($this->get('finance_after_action') == 'payment') {
                //add payment document
                if (!$this->addPayment()) {
                    $db->FailTrans();
                }
            }

            if ($this->get('type') <= PH_FINANCE_TYPE_MAX && $this->get('link_to') || $this->get('link_to_model_name') == 'Contract') {
                //update reasons relatives after gt2 rows for model are saved
                $this->updateReasonsRelatives();
            }

            if ($this->get('status') == 'finished' && !in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                //update nomenclatures prices
                $this->updateNomPrices();
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Translated existing model
     *
     * @return bool - result of the operation
     */
    public function translate() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //IMPORTANT: prepare main data so that the customer name and address are fetched for the destination language!!!
        $this->prepareMainData();

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //save 2nd type grouping table data
        if (!$this->translateGT2Vars()) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('description')) {
            $update['description']  = sprintf("description='%s'", $this->get('description'));
        }
        if ($this->isDefined('reason')) {
            $update['reason']  = sprintf("reason='%s'", $this->get('reason'));
        }
        if ($this->isDefined('total_no_vat_reason_text')) {
            $update['total_no_vat_reason_text'] = sprintf("`total_no_vat_reason_text`='%s'", $this->get('total_no_vat_reason_text'));
        }

        //set customer data (escape values because they are set to model after it is escaped)
        $update['customer_name'] = sprintf("customer_name='%s'", General::slashesEscape($this->get('customer_name')));
        $update['customer_address'] = sprintf("customer_address='%s'", General::slashesEscape($this->get('customer_address')));

        $update['fin_field_1'] = sprintf("fin_field_1='%s'", $this->get('fin_field_1'));
        $update['fin_field_2'] = sprintf("fin_field_2='%s'", $this->get('fin_field_2'));

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_EXPENSES_REASONS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('translating finance expenses reason i18n details', $db, $query2);
            }
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        if (!$this->get('table_values_are_set') && !$this->get('edit_finished')) {
            //calculate totals
            $this->getGT2Vars();
            $this->calculateGT2();
            $this->set('table_values_are_set', true, true);
        }

        $set = array();

        if ($this->isDefined('type')) {
            $set['type'] = sprintf("`type`='%d'", $this->get('type'));

            // automatically set name on add, if empty
            $this->setDefaultName();
        }
        if ($this->isDefined('invoice_num')) {
            $set['invoice_num'] = sprintf("`invoice_num`='%s'", $this->get('invoice_num'));
        }
        if ($this->isDefined('company')) {
            $set['company'] = sprintf("`company`='%d'", $this->get('company'));
        }
        if ($this->isDefined('office')) {
            $set['office'] = sprintf("`office`='%d'", $this->get('office'));
        }
        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("`customer`='%d'", $this->get('customer'));

            //set customer data
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_customer = array('where' => array('c.id = ' . $this->get('customer'), 'c.deleted >= 0'),
                                      'sanitize'  => true,
                                      'model_lang' => $this->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters_customer);

            if ($this->get('import_customer_name')) {
                $this->set('customer_name', $this->get('import_customer_name'), true);
            } elseif (is_object($customer)) {
                $this->set('customer_name', trim($customer->get('name') . ' ' . $customer->get('lastname')), true);
            }
            if (is_object($customer)) {
                $this->set('customer_address', ($customer->get('is_company') ? $customer->get('registration_address') : $customer->get('address_by_personal_id')), true);
                $this->set('eik', ($customer->get('is_company') ? $customer->get('eik') : $customer->get('ucn')), true);
                $this->set('vat_num', $customer->get('in_dds'), true);
            }

            $set['eik'] = sprintf("`eik`='%s'", $this->get('eik'));
            $set['vat_num'] = sprintf("`vat_num`='%s'", $this->get('vat_num'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark'] = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("project=%d", $this->get('project'));
        }
        if ($this->isDefined('phase')) {
            $set['phase'] = sprintf("phase=%d", $this->get('phase'));
        }
        if ($this->isDefined('currency')) {
            $set['currency'] = sprintf("`currency`='%s'", $this->get('currency'));
        }
        if ($this->isDefined('container_id')) {
            $set['container_id'] = sprintf("`container_id`=%d", $this->get('container_id'));
        }
        if ($this->isDefined('employee1')) {
            $set['employee'] = sprintf("`employee`=%d", $this->get('employee1'));
        }
        if ($this->get('finance_after_action') != '') {
            $this->set('status', 'finished', true);
        }
        if ($this->isDefined('status')) {
            $set['status'] = sprintf("`status`='%s'", $this->get('status'));
            $set['status_modified'] = sprintf("status_modified=now()");
            if ($this->get('custom_modified_by')) {
                $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->get('custom_modified_by'));
            } else {
                $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
            }
        }
        $prec = $this->registry['config']->getSectionParams('precision');
        if ($this->isDefined('total_discount_surplus_field')) {
            $set['total_discount_surplus_field'] = sprintf("`total_discount_surplus_field`='%s'", $this->get('total_discount_surplus_field'));
        }
        if ($this->isDefined('total_discount_value')) {
            $set['total_discount_value'] = sprintf("`total_discount_value`='%.6f'", round($this->get('total_discount_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_discount_percentage')) {
            $set['total_discount_percentage'] = sprintf("`total_discount_percentage`='%.6f'", round($this->get('total_discount_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_value')) {
            $set['total_surplus_value'] = sprintf("`total_surplus_value`='%.6f'", round($this->get('total_surplus_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_percentage')) {
            $set['total_surplus_percentage'] = sprintf("`total_surplus_percentage`='%.6f'", round($this->get('total_surplus_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_without_discount')) {
            $set['total_without_discount'] = sprintf("`total_without_discount`='%.6f'", round($this->get('total_without_discount'), $prec['gt2_total']));
        }
        if ($this->isDefined('total')) {
            $set['total'] = sprintf("`total`='%.6f'", round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_vat_rate')) {
            $set['total_vat_rate'] = sprintf("`total_vat_rate`='%.2f'", round($this->get('total_vat_rate'), 2));
        }
        if ($this->isDefined('total_vat')) {
            $set['total_vat'] = sprintf("`total_vat`='%.6f'",
                        round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total_vat']));
        }
        if ($this->isDefined('total_with_vat')) {
            $set['total_with_vat'] = sprintf("`total_with_vat`='%.6f'",
                        round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total_vat'])
                        + round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_no_vat_reason')) {
            $set['total_no_vat_reason'] = sprintf("`total_no_vat_reason`='%d'", $this->get('total_no_vat_reason'));
        }
        if ($this->isDefined('payment_type')) {
            $set['payment_type'] = sprintf("`payment_type`='%s'", $this->get('payment_type'));
            if ($this->get('cheque')) {
                $set['cheque'] = '`cheque` = 1';
            } else {
                $set['cheque'] = '`cheque` = 0';
            }
        }
        if ($this->isDefined('date_of_payment')) {
            $set['date_of_payment'] = sprintf("`date_of_payment`='%s'", $this->get('date_of_payment'));
        }
        if ($this->isDefined('issue_date')) {
            $set['issue_date'] = sprintf("`issue_date`='%s'", $this->get('issue_date'));
        } elseif ($this->get('status') == 'finished') {
            $this->set('issue_date', General::strftime('%Y-%m-%d'), true);
            $set['issue_date'] = "`issue_date`=NOW()";
        }

        if (!$this->isDefined('distributed') && !$this->get('id')) {
            $distributable = false;
            //expense reason
            if ($this->get('type') > PH_FINANCE_TYPE_MAX) {
                //expense reasons are distributable
                $distributable = true;
            }
            //expense invoice
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE) {
                if (!$this->get('link_to') || $this->get('link_to_model_name') == 'Contract') {
                    //freely added invoice, expense invoices added from expense reason ARE NOT distributable
                    $distributable = true;
                } elseif ($this->get('link_to') && $this->get('link_to_model_name') == 'Finance_Expenses_Reason') {
                    //check if the invoice is added from expense reason
                    $query = 'SELECT fer.id ' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as fer' . "\n" .
                             'WHERE fer.type > ' . PH_FINANCE_TYPE_MAX . ' AND' . "\n" .
                             '      fer.id=' . $this->get('link_to');
                    $expense_reason_id = $this->registry['db']->GetOne($query);
                    if (!$expense_reason_id) {
                        //check if the invoice is added from freely added proforma
                        $query = 'SELECT frr.link_to ' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                                 'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as fer' . "\n" .
                                 '  ON frr.link_to=fer.id' . "\n" .
                                 'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as fer2' . "\n" .
                                 '  ON frr.parent_id=fer2.id' . "\n" .
                                 'WHERE frr.parent_model_name="Finance_Expenses_Reason" AND' . "\n" .
                                 '      frr.link_to_model_name="Finance_Expenses_Reason" AND' . "\n" .
                                 '      fer.type > ' . PH_FINANCE_TYPE_MAX . ' AND' . "\n" .
                                 '      fer2.type=' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE . ' AND' . "\n" .
                                 '      frr.parent_id=' . $this->get('link_to');
                        $expense_reason_id = $this->registry['db']->GetOne($query);
                        if (!$expense_reason_id) {
                            $distributable = true;
                        }
                    }
                }
            }
            //expense debit/credit
            if (in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE)) && $this->get('invoice_id')) {
                //expense debit/credit added from freely added invoice
                //check if the invoice is freely added
                $query = 'SELECT frr.link_to ' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as fer' . "\n" .
                         '  ON frr.link_to=fer.id' . "\n" .
                         'WHERE frr.parent_model_name="Finance_Expenses_Reason" AND' . "\n" .
                         '      frr.link_to_model_name="Finance_Expenses_Reason" AND' . "\n" .
                         '      fer.type > ' . PH_FINANCE_TYPE_MAX . ' AND' . "\n" .
                         '      frr.parent_id=' . $this->get('invoice_id');
                $expense_reason_id = $this->registry['db']->GetOne($query);
                if (!$expense_reason_id) {
                    //no check if the invoice was issued from proforma added from expense reason
                    $query = 'SELECT frr2.link_to ' . "\n" .
                             'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                             'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as fer' . "\n" .
                             '  ON frr.link_to=fer.id' . "\n" .
                             'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr2' . "\n" .
                             '  ON frr.link_to=frr2.parent_id AND frr2.parent_model_name="Finance_Expenses_Reason" AND frr.link_to_model_name="Finance_Expenses_Reason"' . "\n" .
                             'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as fer2' . "\n" .
                             '  ON frr2.link_to=fer2.id' . "\n" .
                             'WHERE frr.parent_model_name="Finance_Expenses_Reason" AND' . "\n" .
                             '      frr.link_to_model_name="Finance_Expenses_Reason" AND' . "\n" .
                             '      fer2.type > ' . PH_FINANCE_TYPE_MAX . ' AND' . "\n" .
                             '      frr.parent_id=' . $this->get('invoice_id');
                    $expense_reason_id = $this->registry['db']->GetOne($query);
                    if (!$expense_reason_id) {
                        $distributable = true;
                    }
                }
            }
            //set distributed flag:
            //PH_FINANCE_DISTRIBUTION_NONE - 0, the record is NOT distributable (default value)
            //PH_FINANCE_DISTRIBUTION_YES - 1, the record is distributable, but already distributed
            //PH_FINANCE_DISTRIBUTION_NO - 2, the record is distributable, but not yet distributed
            if ($distributable) {
                $this->set('distributed', PH_FINANCE_DISTRIBUTION_NO, true);
            }
        }
        if ($this->isDefined('distributed')) {
            $set['distributed'] = sprintf("`distributed`=%d", $this->get('distributed'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }

        if ($this->get('type') < PH_FINANCE_TYPE_MAX && $this->get('type') != PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE && $this->get('admit_VAT_credit')) {
            $set['admit_VAT_credit'] = sprintf("admit_VAT_credit=%d", $this->get('admit_VAT_credit'));
            if ($this->isDefined('accounting_month') && $this->isDefined('accounting_year')) {
                //make chosen period
                if ($this->get('accounting_month') < 10 && !preg_match('#^0\d$#', $this->get('accounting_month'))) {
                    $accounting_period = $this->get('accounting_year') . '-0' . $this->get('accounting_month') . '-01';
                } else {
                    $accounting_period = $this->get('accounting_year') . '-' . $this->get('accounting_month') . '-01';
                }
                $this->set('accounting_period', $accounting_period, true);
                $set['accounting_period'] = sprintf("accounting_period='%s'", $accounting_period);
            }
        } elseif (in_array($this->registry['action'], array('add', 'edit', 'addinvoice'))) {
            $set['admit_VAT_credit'] = sprintf("admit_VAT_credit=%d", 0);
            $set['accounting_period'] = sprintf("accounting_period='%s'", '');
        }
        if ($this->isDefined('allocated_status')) {
            $set['allocated_status'] = sprintf("allocated_status='%s'", $this->get('allocated_status'));
        }
        if ($this->get('annulled') && $this->get('annulled_by')) {
            $set['annulled'] = sprintf("annulled='%s'", $this->get('annulled'));
            $set['annulled_by'] = sprintf("annulled_by=%d", $this->get('annulled_by'));
        }

        if ($this->isDefined('fiscal_event_date')
          && in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
            $set['fiscal_event_date'] = sprintf("fiscal_event_date='%s'", $this->get('fiscal_event_date'));
        }

        $set['modified']       = sprintf("modified=now()");
        $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Sets default name of model before adding, if empty
     */
    private function setDefaultName() {
        if (!$this->get('id') && $this->isDefined('name') && !$this->get('name')) {
            $parent_name = '';
            if ($this->get('type') < PH_FINANCE_TYPE_MAX && $this->get('link_to')) {
                // derivative document of system type
                if ($this->isDefined('parent_name')) {
                    // parent name is set to model so just get it
                    $parent_name = $this->get('parent_name');
                } else {
                    if ($this->get('link_to_model_name') == 'Contract') {
                        $parent_i18n_table = DB_TABLE_CONTRACTS_I18N;
                    } else {
                        $parent_i18n_table = DB_TABLE_FINANCE_EXPENSES_REASONS_I18N;
                    }
                    $query_parent_name = 'SELECT name FROM ' . $parent_i18n_table . "\n" .
                                         'WHERE parent_id=' . $this->get('link_to') . ' AND lang="' . $this->registry['lang'] . '"';
                    $parent_name = $this->registry['db']->GetOne($query_parent_name);
                }
            }

            // construct name (type name is used by default)
            $name = $this->get('type_name') .
                    ($parent_name ? sprintf(' %s %s', $this->i18n('for'), $parent_name) : '');

            $this->set('name', $name, true);
        }
    }

    /**
     * Method to define and set group of a new model (without id) based on
     * default group type setting<br />
     * IMPORTANT: Always pass $parent_group parameter when creating secondary documents.
     *
     * @param mixed $parent_group - group of direct parent or false if there is no parent
     * @param mixed $type_default_group - default group from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setGroup($parent_group = false, $type_default_group = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_group === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $group = '';
        if ($type_default_group !== false) {
            $group = $type_default_group;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_group' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $group = $this->registry['db']->GetOne($query);
        }
        // if setting is parent group
        if ($group == '[parent_group]') {
            // if value is passed for parent group
            if ($parent_group !== false) {
                $group = $parent_group;
            } else {
                // if there is no parent document,
                // group will be defined according to current user
                $group = '[default_user_group]';
            }
        }
        // get the default group of the user
        if ($group == '[default_user_group]') {
            if ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $group = $this->registry['originalUser']->get('default_group');
            } else {
                // get the default group id from the current user
                $group = $this->registry['currentUser']->get('default_group');
            }
            // set "All" if user has no default group
            if (!$group) {
                $group = PH_ROOT_GROUP;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('group', $group, true);
    }

    /**
     * Method to define and set department of a new model (without id) based on
     * default department type setting<br />
     * IMPORTANT: Always pass $parent_department parameter when creating secondary documents.
     *
     * @param mixed $parent_department - department of direct parent or false if there is no parent
     * @param mixed $type_default_department - default department from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setDepartment($parent_department = false, $type_default_department = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_department === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $department = '';
        if ($type_default_department !== false) {
            $department = $type_default_department;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_department' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $department = $this->registry['db']->GetOne($query);
        }
        // if setting is parent department
        if ($department == '[parent_department]') {
            // if value is passed for parent department
            if ($parent_department !== false) {
                $department = $parent_department;
            } else {
                // if there is no parent document,
                // department will be defined according to current user
                $department = '[default_user_department]';
            }
        }
        // get the default department of the user
        if ($department == '[default_user_department]') {
            if ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $department = $this->registry['originalUser']->get('default_department');
            } else {
                // get the default department id from the current user
                $department = $this->registry['currentUser']->get('default_department');
            }
            // set "All" if user has no default department
            if (!$department) {
                $department = PH_DEPARTMENT_FIRST;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('department', $department, true);
    }

    /**
     * Prepares display properties in list view according to some properties of model
     *
     * @return array - display properties
     */
    public function getBackgroundColor() {
        $background_properties = array();
        if (in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE,
                                               PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE,
                                               PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE,
                                               PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE)) &&
        !$this->get('annulled_by') &&
        $this->get('status') == 'finished' &&
        !in_array($this->get('payment_status'), array('nopay', 'invoiced'))) {
            if ($this->get('payment_status') == 'paid') {
                if ($this->get('advance')) {
                    $background_properties['background_color'] = 'FFC770';
                    $background_properties['definition'] = 'paid_advance';
                } else {
                    $background_properties['background_color'] = 'A5DBA8';
                    $background_properties['definition'] = 'paid_default';
                }
                $background_properties['text_color'] = '000000';
            } else {
                $current_day = strtotime(date('Y-m-d'));
                $date_payment = '';
                // if property is not defined, payment date has not been calculated yet
                if ($this->isDefined('date_of_payment')) {
                    $date_payment = strtotime($this->get('date_of_payment'));
                }

                if ($date_payment && $date_payment < $current_day) {
                    if ($this->get('advance')) {
                        $background_properties['background_color'] = 'FF6F18';
                        $background_properties['definition'] = 'not_paid_advance';
                    } else {
                        $background_properties['background_color'] = 'EFB9B9';
                        $background_properties['definition'] = 'not_paid_default';
                    }
                    $background_properties['text_color'] = '000000';
                } elseif (($date_payment >= $current_day) && ($current_day > $date_payment-(3600*24*3))) {
                    $background_properties['background_color'] = 'FFCA2D';
                    $background_properties['definition'] = 'payment_day_soon';
                    $background_properties['text_color'] = '000000';
                }
            }
        }

        return $background_properties;
    }

    /**
     * get counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->unsanitize();
            }

            require_once 'finance.counters.factory.php';
            if (!$this->get('custom_counter')) {
                $filters = array('where' => array(
                                            'fc.model = "' . $this->modelName . '"',
                                            'fc.model_type = "' . $this->get('type') . '"',
                                            '(fco.office_id = "' . $this->get('office') . '" OR fco.office_id = 0)',
                                            'fc.company = "' . $this->get('company') . '"',
                                            'fc.active = 1',
                                            'fc.deleted_by = 0'),
                                 'sort' => array('fco.office_id DESC', 'fc.default DESC')
                                );
            } else {
                $filters = array('where' => array('fc.id = ' . $this->get('custom_counter')));
            }
            $this->counter = Finance_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Get number
     *
     * @param bool $force
     * @return string
     */
    public function getNum($force = false) {
        if (!$this->get('num') || $force) {

            //get the counter assigned to the financial document type
            $this->getCounter();

            if ($this->counter) {
                //define some the counter's fomula components
                $formula = $this->counter->get('formula');
                $prefix = $this->counter->get('prefix');
                $delimiter = $this->counter->get('delimiter');
                $zeroes = $this->counter->get('leading_zeroes');
                $date_format = $this->counter->get('date_format');

                //create extender to expand the formula components
                $extender = new Extender;

                //lock the counter for update to guarantee unique next number
                $query = 'SELECT next_number FROM ' . DB_TABLE_FINANCE_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                //set financial document number
                $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                $extender->add('num', $num);

                if ($this->counter->get('prefix')) {
                    //add this component to the extender
                    $extender->add('prefix', $prefix);
                }

                if ($this->counter->get('company_code') && $this->get('company')) {
                    //get customer code
                    $query = 'SELECT code FROM ' . DB_TABLE_FINANCE_COMPANIES . ' WHERE id=' . $this->get('company');
                    $company_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('company_code', $company_code);
                }

                if ($this->counter->get('office_code') && $this->get('office')) {
                    //get office code
                    $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
                    $office_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('office_code', $office_code);
                }

                if ($this->counter->get('user_code')) {
                    //get user code
                    //add this component to the extender
                    $extender->add('user_code', $this->registry['currentUser']->get('code'));
                }

                if ($this->counter->get('project_code') && $this->get('project')) {
                    //get project code
                    require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                    $filters = array('where' => array('p.id = ' . $this->get('project'),
                                                      'p.deleted IS NOT NULL'),
                                     'sanitize' => true);
                    $project = Projects::searchOne($this->registry, $filters);

                    //add this component to the extender
                    $extender->add('project_code', $project->get('code'));
                }

                if ($this->counter->get('document_date')) {
                    //replace the date
                    if ($this->get('issue_date')) {
                        $date = General::strftime($date_format, strtotime($this->get('issue_date')));
                    } elseif ($this->get('added')) {
                        $date = General::strftime($date_format, strtotime($this->get('added')));
                    } else {
                        $date = General::strftime($date_format);
                    }

                    //add this component to the extender
                    $extender->add('document_date', $date);
                }

                $num = $extender->expand($formula);
                if ($delimiter) {
                    //remove repeating delimiters
                    $num = preg_replace('#'. preg_quote($delimiter . $delimiter) .'#', $delimiter, $num);
                    $num = preg_replace('#'. preg_quote($delimiter) .'$#', '', $num);
                    $num = preg_replace('#^'. preg_quote($delimiter) .'#', '', $num);
                }

                $this->set('num', $num, true);
            }
        }

        return $this->get('num');
    }

    /**
     * Get paid amount for this financial document and set it as a property to model.
     *
     * @param array $params - when specified parameters
     *                        ('parent_model_name' and 'parent_id' for expense credit notes,
     *                        'paid_to_model_name' and 'paid_to' for other expense documents)
     *                        get paid amount only for specified record
     * @return float - paid amount
     */
    public function getPaidAmount($params = array()) {
        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
            $query = 'SELECT SUM(paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                     'WHERE fr.paid_to=\'' . $this->get('id') . '\'' . "\n" .
                     '  AND paid_to_model_name="' . $this->modelName . '"';
            if (isset($params['parent_model_name']) && isset($params['parent_id'])) {
                $query .= ' AND parent_model_name="' . $params['parent_model_name'] . '"' .
                          ' AND parent_id=' . $params['parent_id'];
            }
        } else {
            $query = 'SELECT SUM(paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
                     'WHERE fr.parent_id=\'' . $this->get('id') . '\'' . "\n" .
                     '  AND parent_model_name="' . $this->modelName . '"';
            if (isset($params['paid_to_model_name']) && isset($params['paid_to'])) {
                $query .= ' AND paid_to_model_name="' . $params['paid_to_model_name'] . '"' .
                          ' AND paid_to=' . $params['paid_to'];
            }
        }
        $record = (double)$this->registry['db']->GetOne($query);
        if (isset($params['paid_to_model_name']) && isset($params['paid_to'])) {
            // paid amount for $params['paid_to'] incomes reason, payment etc.
            $this->set('paid_amount_' . $params['paid_to_model_name'] . '_' . $params['paid_to'], abs($record), true);
        } elseif (isset($params['parent_model_name']) && isset($params['parent_id'])) {
            // paid amount for $params['parent_id'] expenses invoice, incomes credit notice, payment etc.
            $this->set('paid_amount_' . $params['parent_model_name'] . '_' . $params['parent_id'], abs($record), true);
        } else {
            // paid amount for all financial documents and payments
            $this->set('paid_amount', abs($record), true);
        }
        if ($unsanitize) {
            $this->sanitize();
        }

        return $record;
    }

    /**
     * Add payment
     *
     * @param array $params - input parameters (none for now)
     * @return bool - result of the operation
     */
    public function addPayment($params = array()) {

        $db = $this->registry['db'];

        $db->StartTrans();

        //get paid amount
        $paid_amount = $this->getPaidAmount();
        //amount left to pay
        $amount = round($this->get('total_with_vat'), 2) - $paid_amount;
        // check for paid relative (proforma for reason or vice versa)
        if ($other_paid_doc = $this->getPaidRelative()) {
            $amount -= (double)$this->get('relative_paid_amount');
            if ($amount <= 0) {
                $link = sprintf('<a href="%s?%s=finance&amp;%s=expenses_reasons&amp;expenses_reasons=payments&amp;payments=%s&amp;selected_tab=payment" target="_blank">%s</a>',
                                $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'],
                                $other_paid_doc->get('id'),
                                $other_paid_doc->getModelTypeName() .
                                ($other_paid_doc->get('invoice_num') ? ' ' . $other_paid_doc->get('invoice_num') : ''));
                $this->raiseError('error_finance_expenses_reasons_paid_relative', '', '', array($link));
                $db->CompleteTrans();
                return false;
            }
        }
        $amount = abs(round($amount, 2));

        // properties to be copied from financial document
        $set_from_old_model = array(
            'customer', 'customer_name',
            'trademark', 'trademark_name',
            'project', 'project_name', 'phase', 'phase_name',
            'currency', 'group', 'group_name',
            'employee_name'
        );

        //create new payment
        require_once 'finance.payments.factory.php';
        $payment = new Finance_Payment($this->registry);
        foreach ($set_from_old_model as $param) {
            $reason_value = $this->get($param);
            if ($this->slashesEscaped) {
                $reason_value = General::slashesStrip($reason_value);
            }
            $payment->set($param, $reason_value, true);
        }

        // get the data for payment container
        preg_match('#^(\d+)_(\d+)_(cash|bank|cheque)_(\d+)$#', $this->get('payment_container'), $matches);
        $payment->set('company', @$matches[1], true);
        $payment->set('office', @$matches[2], true);
        $payment_type = @$matches[3];
        $payment->set('payment_type', (($payment_type == 'cheque') ? 'bank' : $payment_type), true);
        $payment->set('container_id', @$matches[4], true);
        $payment->set('container_rate', $this->get('container_rate'), true);
        $payment->set('container_amount', $this->get('container_amount'), true);
        $payment->set('container_currency', $this->get('container_currency'), true);

        // prepare container, company and office name
        if ($payment->get('company')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N . "\n" .
                     'WHERE parent_id=' . $payment->get('company') . ' AND lang="' . $payment->get('model_lang') . '"';
            $company_name = $db->GetOne($query);
            $payment->set('company_name', $company_name, true);
        }
        if ($payment->get('container_id')) {
            $query = 'SELECT ti18n.name' . "\n" .
                     'FROM ' . ($payment->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS : DB_TABLE_FINANCE_CASHBOXES) . ' AS t' .  "\n" .
                     'LEFT JOIN ' . ($payment->get('payment_type') == 'bank' ? DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N : DB_TABLE_FINANCE_CASHBOXES_I18N) . ' AS ti18n' . "\n" .
                     '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $payment->get('model_lang') . '"' . "\n" .
                     'WHERE t.id=' . $payment->get('container_id');
            $container_name = $db->GetOne($query);
            $payment->set('container_name', $container_name, true);
        }
        if ($payment->get('office')) {
            $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N . "\n" .
                     'WHERE parent_id=' . $payment->get('office') . ' AND lang="' . $payment->get('model_lang') . '"';
            $office_name = $db->GetOne($query);
            $payment->set('office_name', $office_name, true);
        }

        if ($this->isDefined('payment_sum') && $this->get('payment_sum') > 0) {
            $payment->set('amount', $this->get('payment_sum'), true);
            if ($amount > $this->get('payment_sum')) {
                $amount = $this->get('payment_sum');
            }
        } elseif ($this->registry->get('action') != 'addpayment') {
            $payment->set('amount', $amount, true);
        }

        // define the payment date
        if ($this->get('payment_date')) {
            $payment->set('issue_date', $this->get('payment_date'), true);
        } elseif ($this->registry->get('action') != 'addpayment') {
            // in case of direct issue payment (without intermediate screen) the current date is taken
            $payment->set('issue_date', date('Y-m-d'), true);
        }

        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
            if ($payment->get('payment_type') == 'cash') {
                $payment->set('type', 'PKO', true);
            } elseif ($payment->get('payment_type') == 'bank') {
                $payment->set('type', 'BP', true);
            }
        } else {
            if ($payment->get('payment_type') == 'cash') {
                $payment->set('type', 'RKO', true);
            } elseif ($payment->get('payment_type') == 'bank') {
                $payment->set('type', 'PN', true);
            }
        }
        $payment->set('type_name', $this->i18n('finance_payments_type_' . $payment->get('type')), true);
        if ($this->isDefined('payment_reason') && $this->get('payment_reason')) {
            $reason = $this->get('payment_reason');
        } else {
            $reason = sprintf('%s %s %s',
                              $this->i18n('finance_payment'),
                              $this->i18n('for'),
                              ($this->get('name') ? $this->get('name') : $this->getModelTypeName()));
        }
        if ($this->slashesEscaped) {
            $reason = General::slashesStrip($reason);
        }
        $payment->set('reason', $reason, true);

        $payment->set('employee', $this->get('employee1'), true);
        $payment->set('status', 'finished', true);

        //save payment
        if ($payment->save()) {
            // save payment balance
            $payment->updateBalance(array('model_id' => $this->get('id'),
                                          'model_name' => 'Finance_Expenses_Reason',
                                          'amount' => $amount));
            $payment->slashesStrip();

            //add history and audit
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.history.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.audit.php';
            $old_payment = new Finance_Payment($this->registry);
            Finance_Payments_History::saveData($this->registry, array('model' => $payment, 'action_type' => 'add', 'new_model' => $payment, 'old_model' => $old_payment));

            //the transaction status could be checked only before CompleteTrans()
            $dbTransError = $db->HasFailedTrans();
            $result = !$dbTransError;
            if ($result) {
                $result = $payment->get('id');
            }
        } else {
            $result = false;
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        return $result;
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Finance_Expenses_Reason\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted =  0'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        return $files;
    }

    /**
     * Get all patterns variables - basic/system, additional
     *
     * @return array $vars - variables
     */
    public function getPatternsVars() {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Finance_Expenses_Reason", "Customer", "CurrentUser") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $customer_translations = $customer->getTranslations();
        $t_customer = array();
        foreach ($customer_translations as $t_lang) {
            if ($t_lang != $customer->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $customer->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
            }
        }

        //get the pattern
        $pattern_id = $this->registry['request']->get('pattern');
        $filters = array('where' => array('p.id = ' . $pattern_id),
            'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'),
                                          'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        $t_user = array();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //prepare contact person
        if ($this->get('contact_person')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                       'c.subtype = \'contact\''),
                                      'sanitize'  => true,
                                      'model_lang' => $this->get('model_lang'));
            $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
            if ($contactperson) {
                $this->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
            }
        }

        //prepare customer's branch
        if ($this->get('branch')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                     'c.subtype = \'branch\''),
                                    'sanitize'  => true,
                                    'model_lang' => $this->get('model_lang'));
            $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
            if ($branch) {
                $this->set('branch_name', $branch->get('name'), true);
            }
        }

        //prepare cashbox/bank account
        if ($this->get('container_id')) {
            if ($this->get('payment_type') == 'bank') {
                require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                           'sanitize'  => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_bic', $container->get('bic'), true);
                    $this->set('container_iban', $container->get('iban'), true);
                    $this->set('container_bank', $container->get('bank'), true);
                }
            } else {
                require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                           'sanitize'  => true,
                                           'model_lang' => $this->get('model_lang'));
                $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                if ($container) {
                    $this->set('container_name', $container->get('name'), true);
                    $this->set('container_location', $container->get('location'), true);
                }
            }
        }

        $translations = $this->getTranslations();
        if (empty($translations)) {
            $translations = array($this->get('model_lang'));
        }
        $t_reason = array();

        //save the previous registry lang
        $registry_lang_old = $this->registry['lang'];

        foreach ($translations as $t_lang) {
            $this->registry->set('lang', $t_lang, true);
            $this->registry['translater']->reloadFiles($t_lang);

            if ($this->get('id') > 0) {
                //when previewing the invoice templates the id of the invoice is -1
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('fer.id = ' . $this->get('id')));
                $t_reason[$t_lang] = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
            } else {
                //when previewing the invoice templates the id of the invoice is -1
                //that's why the search could not find an invoice
                $t_reason[$t_lang] = clone $this;
            }

            if ($this->get('contact_person')) {
                $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                           'c.subtype = \'contact\''),
                                          'sanitize'  => true,
                                          'model_lang' => $t_lang);
                $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
                if ($contactperson) {
                    $t_reason[$t_lang]->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                }
            }
            if ($this->get('branch')) {
                $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                   'c.subtype = \'branch\''),
                                        'sanitize'  => true,
                                        'model_lang' => $t_lang);
                $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
                if ($branch) {
                    $t_reason[$t_lang]->set('branch_name', $branch->get('name'), true);
                }
            }
            if ($this->get('container_id')) {
                if ($this->get('payment_type') == 'bank') {
                    require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.factory.php';
                    $filters_container = array('where' => array('fba.id = ' . $this->get('container_id')),
                                               'sanitize'  => true,
                                               'model_lang' => $t_lang);
                    $container = Finance_Bank_Accounts::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_bic', $container->get('bic'), true);
                        $t_reason[$t_lang]->set('container_iban', $container->get('iban'), true);
                        $t_reason[$t_lang]->set('container_bank', $container->get('bank'), true);
                    }
                } else {
                    require_once PH_MODULES_DIR . 'finance/models/finance.cashboxes.factory.php';
                    $filters_container = array('where' => array('fcb.id = ' . $this->get('container_id')),
                                               'sanitize'  => true,
                                               'model_lang' => $t_lang);
                    $container = Finance_Cashboxes::searchOne($this->registry, $filters_container);
                    if ($container) {
                        $t_reason[$t_lang]->set('container_name', $container->get('name'), true);
                        $t_reason[$t_lang]->set('container_location', $container->get('location'), true);
                    }
                }
            }
        }
        $this->registry->set('lang', $registry_lang_old, true);
        $this->registry['translater']->reloadFiles($registry_lang_old);

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            $pl_source = $placeholder->get('source');
            $pl_varname = $placeholder->get('varname');
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Finance_Expenses_Reason') {
                    //reason variables
                    if (!$placeholder->get('multilang')) {
                        if (preg_match('#^company_#', $pl_source)) {
                            $vars[$placeholder->get('varname')] = $this->getCompanyData(str_replace('company_', '', $pl_source));
                        } else {
                            $vars[$placeholder->get('varname')] = $this->get($pl_source);
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if (preg_match('#^company_#', $pl_source)) {
                                $vars[ $t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_reason[$t_lang]->getCompanyData(str_replace('company_', '', $pl_source));
                            } else {
                                $vars[ $t_reason[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_reason[$t_lang]->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer') {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $customer->get($pl_source);
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[ $t_customer[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_customer[$t_lang]->get($pl_source);
                            } else {
                                $vars[ $customer->get('model_lang') . '_' . $pl_varname] =
                                $customer->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $user->get($pl_source);
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_user[$t_lang]->get($pl_source);
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $pl_varname] =
                                $user->get($pl_source);
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($pl_source, '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $pl_source);
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$pl_varname] = $res;
                } else {
                    $vars[$pl_varname] = $pl_source;
                }
            }
        }

        //prepare additional variables

        //get print settings for the 2nd type grouping table
        $print_properties = $this->getGT2PrintSettings($pattern_id);

        $lang = $this->get('model_lang');
        foreach ($translations as $t_lang) {
            $this->set('model_lang', $t_lang, true);
            // set no VAT reason text in current language
            $this->set('total_no_vat_reason_text', $t_reason[$t_lang]->get('total_no_vat_reason_text'), true);

            if (!$this->get('table_values_are_set')) {
                $this->getGT2Vars(false);
            }
            $table = $this->get('grouping_table_2');

            //prepare files in GT2
            if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                foreach ($table['values'] as $ridx => $row) {
                    foreach ($row as $rkey => $rval) {
                        if (!empty($rval) && is_object($rval)) {
                            $file = $rval;
                            if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                            } else {
                                $file = '';
                            }
                            $table['values'][$ridx][$rkey] = $file;
                        }
                    }
                }
            }

            $table_ordered = $table;
            $table_ordered['vars'] = array();
            $styles_for_template = array();

            foreach ($print_properties as $key => $property) {
                // style properties
                if (!empty($property['style'])) {
                    $styles_for_template[$key] = $property['style'];
                }
                // label for table caption
                if ($key == 'var_' . $table['id']) {
                    if (isset($property['labels'][$t_lang])) {
                        $table_ordered['label'] = $property['labels'][$t_lang];
                    }
                    continue;
                }
                foreach ($table['vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        $table_ordered['vars'][$idx] = $var;
                        // label for field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        // aggregates
                        if (isset($property['agregate'])) {
                            if ($property['agregate'] != 'none') {
                                $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                            } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                unset($table_ordered['vars'][$idx]['agregate']);
                            }
                        }
                        continue 2;
                    }
                }
                foreach ($table['plain_vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        // label for total field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        continue 2;
                    }
                }
            }

            unset($table);

            // calculate aggregates in GT2 table
            $table_ordered = $this->calculateGT2Agregates($table_ordered);

            $this->set('grouping_table_2', $table_ordered, true);

            $groupingViewer = new Viewer($this->registry);
            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');
            $groupingViewer->data['table'] = $table_ordered;
            $groupingViewer->data['styles'] = $styles_for_template;
            $groupingViewer->data['pattern_id'] = $pattern_id;
            $vars[$t_lang . '_grouping_table_2'] = $groupingViewer->fetch();

            unset($table_ordered);
        }

        $this->set('model_lang', $lang, true);
        // set no VAT reason text in current language
        $this->set('total_no_vat_reason_text', (isset($t_reason[$lang]) ? $t_reason[$lang]->get('total_no_vat_reason_text') : ''), true);

        return $vars;
    }

    /**
     * Get reasons attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'' . $this->modelName . '\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted =  0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get reasons files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Change status/substatus
     *
     * @return bool - result of the operation
     */
    public function setStatus() {
        $flag_error = false;
        $flag_error_substatus = false;

        $db = $this->registry['db'];

        $db->StartTrans();

        $permission_unlock = $this->checkPermissions('setstatus_unlock');

        if (!$this->get('status')) {
            $flag_error = true;
        } else {
            if ($this->get('id')) {
                $id_reason = $this->get('id');
            }
            $current_status = Finance_Expenses_Reasons::getModelStatus($this->registry, $id_reason);
            $status_info = explode ('_', $this->get('status'));
            $status_name = $status_info[0];
            if ($current_status == 'locked' && $status_name == 'opened') {
                if (!$permission_unlock) {
                    $flag_error = true;
                }
            } else if ($current_status == 'finished' && ($status_name == 'opened' || $status_name == 'locked')) {
                $flag_error = true;
            }

            //takes the status properties based
            //if the status is defined in a dropdown it's a single
            // value containing the main status and the id of the substatus if such is defined
            @ $status_properties = explode('_', $this->get('status'));
            $new_status = $status_properties[0];

            if ($this->get('substatus')) {
                $substatus_properties = explode('_', $this->get('substatus'));
                if ($substatus_properties[0] != $new_status) {
                    $flag_error_substatus = true;
                } else {
                    $new_substatus = $substatus_properties[1];
                }
            } elseif (isset($status_properties[1])) {
                $new_substatus = $status_properties[1];
            }
        }

        if ($new_status == 'locked' || $new_status == 'finished') {
            //validate accounting period
            $query = 'SELECT DATE_FORMAT(accounting_period, "%Y-%m") AS accounting_period, type' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                     'WHERE id = ' . $this->get('id');
            $accounting_period = $db->GetRow($query);

            $type = $accounting_period['type'];
            $accounting_period = $accounting_period['accounting_period'];

            if (in_array($type, array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_INVOICE))) {
                if (!empty($accounting_period) && !preg_match('#0000-00#', $accounting_period)) {
                    //get the day after which we cannot distribute the document for previous month
                    $deny_num = $this->registry['config']->getParam('finance', 'deny_previous_month_after');
                    //make the period we issue document for
                    $issue_period = General::strftime('%Y-%m', strtotime($this->get('issue_date')));

                    if ($accounting_period < $issue_period) {
                        $this->raiseError('error_accounting_period_before_issue_date', 'accounting_period');
                        $flag_error = true;
                    }
                    $allowed_period = array();
                    if (!empty($deny_num) && $deny_num < General::strftime('%d', time())) {
                        //make the allowed period
                        $allowed_period['min'] = $allowed_period['max'] = General::strftime('%Y-%m', time());
                    } else {
                        //make the allowed period
                        $allowed_period['max'] = General::strftime('%Y-%m', time());
                        if (!empty($deny_num)) {
                            $allowed_period['min'] = General::strftime('%Y-%m', strtotime('-1 month', strtotime($allowed_period['max'] . '-01')));
                        } else {
                            $months_back = 1;
                            $allowed_period['min'] = General::strftime('%Y-%m', strtotime('-' . $months_back . ' month', strtotime($allowed_period['max'] . '-01')));
                        }
                    }
                    if ($accounting_period < $allowed_period['min'] || $accounting_period > $allowed_period['max']) {
                        $this->raiseError('error_accounting_period_out_of_allowed', 'accounting_period', null,
                                           array($this->getLayoutName('accounting_period')));
                        $flag_error = true;
                    }
                }
            }

            if (
                $new_status == 'finished' &&
                $this->get('allocated_status') == 'enabled' &&
                !$this->checkContainsNonCommodities()
            ) {
                $this->raiseError('error_allocated_status_not_allowed', 'allocated_status');
                $flag_error = true;
            }
        }

        if ($flag_error || $flag_error_substatus) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            if (isset($current_substatus)) {
                $this->set('substatus', $current_substatus, true);
            }
            if ($flag_error) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }
            if ($flag_error_substatus) {
                $this->raiseError('error_invalid_substatus_change', 'substatus', -3);
            }
            $db->CompleteTrans();
            return false;
        }

        $set = array();
        $set['status'] = sprintf("`status`='%s'", $new_status);
        if (isset($new_substatus)) {
            $set['substatus'] = sprintf("`substatus`=%d", $new_substatus);
        } else {
            $set['substatus'] = "`substatus`=0";
        }

        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

        if (!$this->get('num') && $new_status == 'finished') {
            //set num
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
                $db->FailTrans();
                $db->CompleteTrans();
                return false;
            } else {
                $this->counter->increment();
            }
        }

        if ($new_status == 'finished' && $current_status != 'finished') {
            if (!in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE)))  {
                //update nomenclatures prices
                $this->updateNomPrices();
            } else {
                $this->registry->set('get_old_vars', true, true);
                $gt2 = $this->getGT2Vars();
                $this->registry->set('get_old_vars', false, true);
                if (empty($gt2['values']) || $this->get('total_with_vat') == 0) {
                    $this->raiseError('error_finance_expenses_reasons_empty_rows');
                    $db->CompleteTrans();
                    return false;
                }
            }
            if ((!$this->get('fiscal_event_date') || $this->get('fiscal_event_date') == '0000-00-00')
              && in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                $set['fiscal_event_date'] = sprintf("fiscal_event_date='%s'", $this->get('issue_date'));
            }
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        // if saving a credit note that can be fully allocated to parent invoice, do that automatically
        // make sure amount of credit note is less than 0
        if ($new_status == 'finished' && $current_status != 'finished' &&
        $this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE && bccomp($this->get('total_with_vat'), 0, 2) == -1) {
            $this->getRelatives(array('get_parent_reasons' =>
                array('fer.type = ' . PH_FINANCE_TYPE_EXPENSES_INVOICE, 'fer.payment_status IN ("unpaid", "partial")', 'fer.active = 1', 'fer.annulled_by = 0')));
            $invoice = $this->get('parent_reasons') ?: array();
            $this->unsetProperty('parent_reasons', true);

            if ($invoice) {
                $invoice = reset($invoice);
                if (bccomp($invoice->get('total_with_vat') - $invoice->getPaidAmount(), abs($this->get('total_with_vat')), 2) > -1) {
                    $this->set('relatives_payments', array($invoice->get('id') => abs($this->get('total_with_vat'))), true);
                    if ($this->updateFinanceRelatives('expenses_invoice')) {
                        //show success message
                        $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_edit_payment_success',
                                                                array($this->getModelTypeName())));
                    } else {
                        //some error occurred
                        $this->registry['messages']->setWarning($this->i18n('error_finance_expenses_reasons_edit_payment_failed',
                                                                array($this->getModelTypeName())));
                    }
                    $this->unsetProperty('relatives_payments', true);
                }
            }
            unset($invoice);
        }

        if ($new_status != 'opened' && $current_status == 'opened') {
            $this->setAvailableQuantities();
        }

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
            $result = false;
        }

        return $result;
    }

    /**
     * Get distributed and available payments for the customer of this reason
     *
     * @return bool - result of the operation
     */
    public function getCustomerPayments() {
        //get paid amount
        $this->getPaidAmount();
        //remaining amount is total of the expense reason without the payments that have already been distributed to this expense reason
        $remaining_amount = abs($this->get('total_with_vat')) - $this->get('paid_amount');
        // check for paid relative (proforma for reason or vice versa)
        if ($this->getPaidRelative()) {
            $remaining_amount -= (double)$this->get('relative_paid_amount');
        }
        $remaining_amount = round($remaining_amount, 2);
        $this->set('remaining_amount', $remaining_amount, true);
        $db = $this->registry['db'];

        //get ids of payments relative to this expense reason
        $query = 'SELECT DISTINCT(fp.id) FROM ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp, ' . "\n" .
                 DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                 'WHERE fp.status="finished" AND fp.annulled_by=0 AND fr.paid_amount > 0' . "\n" .
                 ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE ?
                 ' AND fr.paid_to_model_name="' . $this->modelName . '" AND fr.paid_to=' . $this->get('id') . "\n" .
                 ' AND fr.parent_model_name="Finance_Payment" AND fr.parent_id=fp.id' :
                 ' AND fr.parent_model_name="' . $this->modelName . '" AND fr.parent_id=' . $this->get('id') . "\n" .
                 ' AND fr.paid_to_model_name="Finance_Payment" AND fr.paid_to=fp.id');
        $ids = $db->GetCol($query);

        if ($ids) {
            $paid_ids = ' OR fp.id in (' . implode(',', $ids) . ')';
        } else {
            $paid_ids = '';
        }

        //get payments for the customer in the expense reason
        $filters = array();
        $filters['where'] = array('fp.company = ' . $this->get('company'),
                                  'fp.status="finished"',
                                  'fp.currency="' . $this->get('currency') . '"',
                                  'fp.customer=' . $this->get('customer'));
        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
            //this filter gets only those payments that are not yet destributed to other financial documents
            $filters['where'][] = '((SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr WHERE fp.id=fr.parent_id AND parent_model_name="Finance_Payment" GROUP BY fr.parent_id) IS NULL OR
                                  fp.amount>(SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr WHERE fp.id=fr.parent_id AND parent_model_name="Finance_Payment" GROUP BY fr.parent_id)' . $paid_ids . ')';
            $filters['where'][] = 'fp.type IN ("PKO", "BP")';
        } else {
            $filters['where'][] = '((SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr WHERE fp.id=fr.paid_to AND paid_to_model_name="Finance_Payment" GROUP BY fr.paid_to) IS NULL OR
                            fp.amount>(SELECT SUM(fr.paid_amount) FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr WHERE fp.id=fr.paid_to AND paid_to_model_name="Finance_Payment" GROUP BY fr.paid_to)' . $paid_ids . ')';
            $filters['where'][] = 'fp.type IN ("RKO", "PN")';
        }
        $filters['model_lang'] = $this->get('lang');
        $filters['sort'] = array('fp.issue_date ASC', 'fp.added ASC', 'fp.id ASC');
        require_once 'finance.payments.factory.php';
        $payments = Finance_Payments::search($this->registry, $filters);

        $payments_auto_balance = $this->registry['config']->getParam('finance', 'payments_auto_balance');

        $records = array();
        foreach ($payments as $key => $payment) {

            //get the distributed amount of this payment for all financial documents (paid_amount)
            $payment->getPaidAmount();
            //get the distributed amount of this payment for this expense reason
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                $payment->getPaidAmount(array('paid_to_model_name' => $this->modelName, 'paid_to' => $this->get('id')));
            } else {
                $payment->getPaidAmount(array('parent_model_name' => $this->modelName, 'parent_id' => $this->get('id')));
            }
            if ($remaining_amount < 0.01) {
                $remaining_amount = 0;
            }
            //remaining amount for this payment
            $remaining_payment_amount = $payment->get('amount') - $payment->get('paid_amount');
            $remaining_payment_amount = round($remaining_payment_amount, 2);

            if ($remaining_amount > 0 && $remaining_payment_amount >= 0.01 && $payments_auto_balance != 'no') {
                //set suggest amount for every unpaid reason
                if ($remaining_amount > $remaining_payment_amount) {
                    $suggest_amount = $remaining_payment_amount;
                    $remaining_amount -= $remaining_payment_amount;
                    $remaining_amount = round($remaining_amount, 2);
                } else {
                    $suggest_amount = $remaining_amount;
                    $remaining_amount = 0;
                }
                if ($suggest_amount < 0.01) {
                    $suggest_amount = 0;
                }
                $payments[$key]->set('suggest_amount', $suggest_amount, true);
            } elseif (($remaining_payment_amount <= 0 || $this->get('remaining_amount') <= 0)
                && !in_array($payment->get('id'), $ids)) {
                //unset no depending distributed payment
                unset($payments[$key]);
            }
            if (isset($payments[$key])) {
                $records[$payment->get('id')] = $payment->sanitize();
                unset($payments[$key]);
            }
        }
        $this->set('payments', $records, true);

        return true;
    }

    /**
     * Get and propose distribution of financial documents to the expenses reason
     *
     * @param string $selected - selected tab
     * @param bool $check_only - if true, only check if there are any allocated records
     * @return bool - result of the operation
     */
    public function getPaymentsDistribution($selected, $check_only = false) {

        if (!$check_only) {
            //get paid amount
            $this->getPaidAmount();
            //remaining amount is total of the expense reason without the payments that have already been distributed to this expense reason
            $remaining_amount = abs($this->get('total_with_vat')) - $this->get('paid_amount');
            // check for paid relative (proforma for reason or vice versa)
            if ($this->getPaidRelative()) {
                $remaining_amount -= (double)$this->get('relative_paid_amount');
            }
            $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');
            $remaining_amount = round($remaining_amount, 2);
            $this->set('remaining_amount', $remaining_amount, true);
        }
        $db = $this->registry['db'];

        if (empty($selected)) {
            // if empty $selected parameter is passed then we need only the paid amount
            return true;
        }

        if ($selected == 'expenses_credit') {
            $type = '= ' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE;
            $alias = 'fer';
            $model = 'Finance_Expenses_Reason';
            $table = DB_TABLE_FINANCE_EXPENSES_REASONS;
        } elseif ($selected == 'incomes_invoice') {
            $type = '= ' . PH_FINANCE_TYPE_INVOICE;
            $alias = 'fir';
            $model = 'Finance_Incomes_Reason';
            $table = DB_TABLE_FINANCE_INCOMES_REASONS;
        } elseif ($selected == 'incomes_debit') {
            $type = '= ' . PH_FINANCE_TYPE_DEBIT_NOTICE;
            $alias = 'fir';
            $model = 'Finance_Incomes_Reason';
            $table = DB_TABLE_FINANCE_INCOMES_REASONS;
        } elseif ($selected == 'incomes_credit') {
            $type = '= ' . PH_FINANCE_TYPE_CREDIT_NOTICE;
            $alias = 'fir';
            $model = 'Finance_Incomes_Reason';
            $table = DB_TABLE_FINANCE_INCOMES_REASONS;
        } elseif ($selected == 'expenses_invoice') {
            $type = '= ' . PH_FINANCE_TYPE_EXPENSES_INVOICE;
            $alias = 'fer';
            $model = 'Finance_Expenses_Reason';
            $table = DB_TABLE_FINANCE_EXPENSES_REASONS;
        } elseif ($selected == 'expenses_debit') {
            $type = '= ' . PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE;
            $alias = 'fer';
            $model = 'Finance_Expenses_Reason';
            $table = DB_TABLE_FINANCE_EXPENSES_REASONS;
        } elseif ($selected == 'incomes_reason') {
            $type = '> ' . PH_FINANCE_TYPE_MAX;
            $alias = 'fir';
            $model = 'Finance_Incomes_Reason';
            $table = DB_TABLE_FINANCE_INCOMES_REASONS;
        }

        //get ids of models paid to this expense reason
        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
            $query = 'SELECT fr.parent_id, fr.paid_amount FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr' . "\n" .
                     'JOIN ' . $table . ' as t' . "\n" .
                     '    ON t.id = fr.parent_id AND t.type ' . $type . "\n" .
                     '    AND ABS(t.total_with_vat) > 0 AND t.annulled_by = 0' . "\n" .
                     'WHERE fr.paid_to = ' . $this->get('id') . "\n" .
                     '    AND fr.paid_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                     '    AND fr.parent_model_name = "' . $model . '" AND fr.paid_amount > 0';
        } else {
            $query = 'SELECT fr.paid_to, fr.paid_amount FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fr' . "\n" .
                    'JOIN ' . $table . ' as t' . "\n" .
                    '    ON t.id = fr.paid_to AND t.type ' . $type . "\n" .
                    '    AND ABS(t.total_with_vat) > 0 AND t.annulled_by = 0' . "\n" .
                    'WHERE fr.parent_id = ' . $this->get('id') . "\n" .
                    '    AND fr.parent_model_name = "Finance_Expenses_Reason"' . "\n" .
                    '    AND fr.paid_to_model_name = "' . $model . '" AND fr.paid_amount > 0';
        }
        $ids = $db->GetAssoc($query);
        if ($check_only) {
            if ($selected == 'incomes_reason' && array_sum($ids) > 0) {
                return true;
            }
            return false;
        }
        if ($ids) {
            $paid_ids = ' OR ' . $alias . '.id in (' . implode(',', array_keys($ids)) . ')';
        } else {
            $paid_ids = '';
        }

        //get requested models for the customer in the expense reason
        $filters = array();
        $filters['where'] = array(
            $alias . '.company = ' . $this->get('company'),
            '(' . $alias . '.type ' . $type . $paid_ids . ')',
            $alias . '.status = "finished"',
            $alias . '.active = 1',
            $alias . '.annulled_by = 0',
            $alias . '.payment_status <> "nopay"',
            'ABS(' . $alias . '.total_with_vat) > 0 ',
            $alias . '.currency = "' . $this->get('currency') . '"',
            $alias . '.customer = ' . $this->get('customer')
        );
        $filters['model_lang'] = $this->get('lang');
        $filters['sort'] = array($alias . '.issue_date ASC', $alias . '.added ASC', $alias . '.id ASC');

        require_once PH_MODULES_DIR . 'finance/models/' . General::singular2plural(strtolower(preg_replace('#_#', '.', $model, 1))) . '.factory.php';
        $model_factory = General::singular2plural($model);
        $models = $model_factory::search($this->registry, $filters);

        $payments_auto_balance = $this->registry['config']->getParam('finance', 'payments_auto_balance');

        // unallocated amount of credit notes should be suggested to parent invoice with preference
        $reserved_amounts = array();
        if ($payments_auto_balance != 'no' && bccomp($remaining_amount, 0, 2) == 1) {
            $relatives = array();
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE && $selected == 'expenses_invoice') {
                // if model is a credit note and selected tab is invoices
                $this->getRelatives(array('get_parent_reasons' =>
                    array('fer.type = ' . PH_FINANCE_TYPE_EXPENSES_INVOICE, 'fer.payment_status IN ("unpaid", "partial")', 'fer.active = 1', 'fer.annulled_by = 0')));
                $relatives = $this->get('parent_reasons') ?: array();
                $this->unsetProperty('parent_reasons', true);
            } else if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE && $selected == 'expenses_credit') {
                // if model is an invoice and selected tab is credit notes
                $this->getRelatives(array('get_credit_notices' =>
                    array('fer.payment_status IN ("unpaid", "partial")', 'fer.active = 1', 'fer.annulled_by = 0')));
                $relatives = $this->get('credit_notices') ?: array();
                // sort records by issue_date ASC, added ASC, id ASC
                usort($relatives, function($a, $b) {
                    if ($a->get('issue_date') != $b->get('issue_date')) {
                        return $a->get('issue_date') < $b->get('issue_date') ? -1: 1;
                    } elseif ($a->get('added') != $b->get('added')) {
                        return $a->get('added') < $b->get('added') ? -1 : 1;
                    } else {
                        return $a->get('id') < $b->get('id') ? -1 : 1;
                    }
                });
            }

            while (list(, $rel) = each($relatives)) {
                $reserved_amount = bcsub(abs($rel->get('total_with_vat')), $rel->getPaidAmount(), 2);
                if (bccomp($reserved_amount, $remaining_amount, 2) == 1) {
                    $reserved_amount = $remaining_amount;
                }
                $reserved_amounts[$rel->get('id')] = $reserved_amount;
                $remaining_amount = bcsub($remaining_amount, $reserved_amount, 2);
                if ($remaining_amount <= 0) {
                    break;
                }
            }
            unset($relatives);
        }

        $records = array();
        foreach ($models as $key => $model) {
            // get the distributed amount of financial document for all financial documents (paid_amount)
            $model->getPaidAmount();
            if ($model->modelName == 'Finance_Incomes_Reason' && $model->get('type') > PH_FINANCE_TYPE_MAX) {
                // get total amount of all invoices and debit/credit notes for reason
                $model->getInvoicedAmount();
                // add paid amount to non-invoiced proformas
                $model->set('paid_amount', $model->get('paid_amount') + $model->getProformasPaidAmount(), true);
            }
            // get the distributed amount of financial document for this expense reason
            if (!empty($ids[$model->get('id')])) {
                $model->set('paid_amount_Finance_Expenses_Reason_' . $this->get('id'), $ids[$model->get('id')], true);
            } else {
                $model->set('paid_amount_Finance_Expenses_Reason_' . $this->get('id'), 0.00, true);
            }

            $model->set('total_with_vat', sprintf('%.' . $gt2_total_precision . 'f', abs($model->get('total_with_vat' ))), true);

            if ($remaining_amount < 0.01) {
                $remaining_amount = 0;
            }
            //remaining amount for this notice
            $remaining_payment_amount = round($model->get('total_with_vat'), 2) - $model->get('paid_amount');
            if ($model->modelName == 'Finance_Incomes_Reason' && $model->get('type') > PH_FINANCE_TYPE_MAX) {
                $remaining_payment_amount -= $model->get('invoices_amount');
            }
            $remaining_payment_amount = round($remaining_payment_amount, 2);

            if (array_key_exists($model->get('id'), $reserved_amounts)) {
                $models[$key]->set('suggest_amount', $reserved_amounts[$model->get('id')], true);
            } elseif ($remaining_amount > 0 && $remaining_payment_amount >= 0.01 && $payments_auto_balance != 'no') {
                //set suggest amount for every unpaid notice
                if ($remaining_amount > $remaining_payment_amount) {
                    $suggest_amount = $remaining_payment_amount;
                    $remaining_amount -= $remaining_payment_amount;
                    $remaining_amount = round($remaining_amount, 2);
                } else {
                    $suggest_amount = $remaining_amount;
                    $remaining_amount = 0;
                }
                if ($suggest_amount < 0.01) {
                    $suggest_amount = 0;
                }
                $models[$key]->set('suggest_amount', $suggest_amount, true);
            } elseif (($remaining_payment_amount <= 0 || $this->get('remaining_amount') <= 0)
                && !isset($ids[$models[$key]->get('id')])) {
                //unset no depending distributed notice
                unset($models[$key]);
            }
            if (isset($models[$key])) {
                $records[$model->get('id')] = $model;
                unset($models[$key]);
            }
        }
        $this->set(General::singular2plural($selected), $records, true);

        return true;
    }

    /**
     * Gets related records for expense document
     *
     * @param array $params
     */
    public function getRelatives($params = array()) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        if (($this->get('type') > PH_FINANCE_TYPE_MAX || $this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) && (isset($params['get_invoices']) && $params['get_invoices'] || isset($params['get_all']) && $params['get_all'])) {
            //get invoices relative to this income reason
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                    '  ON frr.parent_id = fer.id AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_INVOICE . "\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            if (!empty($params['get_invoices']) && is_array($params['get_invoices'])) {
                foreach ($params['get_invoices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($invoices = $db->GetCol($query)) {

                $filters = array('where' => array('fer.id IN (' . implode(', ', $invoices) . ')'),
                                 'sanitize' => true);
                $invoices = Finance_Expenses_Reasons::search($this->registry, $filters);

                $this->set('invoices', $invoices, true);
            }
        }

        if ($this->get('type') > PH_FINANCE_TYPE_MAX && (isset($params['get_pro_invoices']) && $params['get_pro_invoices'] || isset($params['get_all']) && $params['get_all'])) {
            //get proforma invoices relative to this model
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                    '  ON frr.parent_id = fer.id AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE . "\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            if (!empty($params['get_pro_invoices']) && is_array($params['get_pro_invoices'])) {
                foreach ($params['get_pro_invoices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($pro_invoices = $db->GetCol($query)) {

                $filters = array('where' => array('fer.id IN (' . implode(', ', $pro_invoices) . ')'),
                                 'sanitize' => true);
                $pro_invoices = Finance_Expenses_Reasons::search($this->registry, $filters);

                $this->set('pro_invoices', $pro_invoices, true);
            }
        }

        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE && (isset($params['get_credit_notices']) && $params['get_credit_notices'] || isset($params['get_all']) && $params['get_all'])) {
            //get credit_notices relative to this model
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                    '  ON frr.parent_id = fer.id AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE . "\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            if (!empty($params['get_credit_notices']) && is_array($params['get_credit_notices'])) {
                foreach ($params['get_credit_notices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($credit_notices = $db->GetCol($query)) {

                $filters = array('where' => array('fer.id IN (' . implode(', ', $credit_notices) . ')'),
                                 'sanitize' => true);
                $credit_notices = Finance_Expenses_Reasons::search($this->registry, $filters);

                $this->set('credit_notices', $credit_notices, true);
            }
        }

        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE && (isset($params['get_debit_notices']) && $params['get_debit_notices'] || isset($params['get_all']) && $params['get_all'])) {
            //get debit_notices relative to this model
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                    '  ON frr.parent_id = fer.id AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE . "\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            if (!empty($params['get_debit_notices']) && is_array($params['get_debit_notices'])) {
                foreach ($params['get_debit_notices'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($debit_notices = $db->GetCol($query)) {

                $filters = array('where' => array('fer.id IN (' . implode(', ', $debit_notices) . ')'),
                                 'sanitize' => true);
                $debit_notices = Finance_Expenses_Reasons::search($this->registry, $filters);

                $this->set('debit_notices', $debit_notices, true);
            }
        }

        if ($this->get('type') > PH_FINANCE_TYPE_MAX && (isset($params['get_correct_reasons']) && $params['get_correct_reasons'] || isset($params['get_all']) && $params['get_all'])) {
            //get rectifying expense documents relatives to this expense reason
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                    '  ON frr.parent_id = fer.id AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON . "\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            if (!empty($params['get_correct_reasons']) && is_array($params['get_correct_reasons'])) {
                foreach ($params['get_correct_reasons'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($correct_reasons = $db->GetCol($query)) {
                $filters = array('where' => array('fer.id IN (' . implode(', ', $correct_reasons) . ')'),
                                 'sanitize' => true);
                $correct_reasons = Finance_Expenses_Reasons::search($this->registry, $filters);
                $this->set('correct_reasons', $correct_reasons, true);
            }
        }

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX && (isset($params['get_parent_reasons']) && $params['get_parent_reasons'] || isset($params['get_all']) && $params['get_all'])) {
            //get parent expenses reasons relatives to this expense reason
            $query = 'SELECT frr.link_to' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                 '  ON frr.link_to = fer.id' . "\n" .
                 'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                 '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            if (!empty($params['get_parent_reasons']) && is_array($params['get_parent_reasons'])) {
                foreach ($params['get_parent_reasons'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }

            if ($parent_reasons = $db->GetCol($query)) {
                $filters = array('where' => array('fer.id IN (' . implode(', ', $parent_reasons) . ')'),
                                 'sanitize' => true);
                $parent_reasons = Finance_Expenses_Reasons::search($this->registry, $filters);
                $this->set('parent_reasons', $parent_reasons, true);
            } else {
                //set to an empty array
                $this->set('parent_reasons', array(), true);
            }
        }

        if (!in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE))
            && (isset($params['get_handovers']) && $params['get_handovers'] || isset($params['get_all']) && $params['get_all'])) {
            //get handover relatives to this expense reason
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                    '    ON frr.parent_id = fwd.id AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER .
                    (!empty($params['handovers_direction']) ? sprintf(' AND direction = "%s"', $params['handovers_direction']) : '') ."\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.parent_model_name = "Finance_Warehouses_Document"' . "\n" .
                    '  AND frr.link_to_model_name ="Finance_Expenses_Reason"';
            if (!empty($params['get_handovers']) && is_array($params['get_handovers'])) {
                foreach ($params['get_handovers'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }
            if ($handovers = $db->GetCol($query)) {
                $filters = array('where' => array(
                                    'fwd.id IN (' . implode(', ', $handovers) . ')',
                                    'fwd.active = 1', 'fwd.annulled_by = 0',
                                 ),
                                 'sort' => array('fwd.id ASC'),
                                 'sanitize' => true
                );
                require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
                $handovers = Finance_Warehouses_Documents::search($this->registry, $filters);
            }
            $this->set('handovers', $handovers, true);
        }

        if ($this->get('type') > PH_FINANCE_TYPE_MAX && (isset($params['get_contract']) && $params['get_contract'] || isset($params['get_all']) && $params['get_all'])) {
            //get contract that expense document was issued for
            $query = 'SELECT frr.link_to' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name = "Finance_Expenses_Reason"' . "\n" .
                 '  AND frr.link_to_model_name ="Contract"';
            if ($contract = $db->GetOne($query)) {
                $filters = array('where' => array('co.id = ' . $contract),
                                 'sanitize' => true);
                require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                $contract = Contracts::searchOne($this->registry, $filters);

                $this->set('contract', $contract, true);
            }
        }

        if (isset($params['get_parent_doc']) && $params['get_parent_doc'] || isset($params['get_all']) && $params['get_all']) {
            //get document relative to this expenses reason
            $query = 'SELECT frr.link_to' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name = "Finance_Expenses_Reason"' . "\n" .
                 '  AND frr.link_to_model_name ="Document"';
            if ($document = $db->GetOne($query)) {

                $filters = array('where' => array('d.id = ' . $document),
                                 'sanitize' => true);
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                $document = Documents::searchOne($this->registry, $filters);

                $this->set('document', $document, true);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }
    }

    /**
     * Update reasons relatives
     *
     * @return bool - result of the operation
     */
    public function updateReasonsRelatives() {
        // If it's set to skip relatives
        $params = unserialize(General::slashesStrip($this->get('transform_params')));
        if (!empty($params['skip_relatives'])) {
            // Skip the relatives
            return true;
        }

        $db = $GLOBALS['registry']['db'];

        $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                 'WHERE parent_id=' . $this->get('id') . ' AND parent_model_name="Finance_Expenses_Reason"' . "\n";
        $records = $db->GetAll($query);

        $new_rows_links = $this->get('rows_links');

        //array to show how we change each row(price/quantity/added/deleted)
        $rows_changes = $this->get('rows_changes');
        if (empty($rows_changes)) {
            $rows_changes = array();
        }

        if (!empty($records)) {
            //invoice edit - remove deleted rows form the links
            if (!empty($new_rows_links['deleted'])) {
                foreach ($records as $record) {
                    foreach ($new_rows_links['deleted'] as $deleted) {
                        if (preg_match('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', $record['rows_links'])) {
                            $record['rows_links'] = preg_replace('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', "\n", $record['rows_links']);
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                                     ' SET rows_links = \'' . $record['rows_links'] . "'\n" .
                                     'WHERE parent_id = ' . $record['parent_id'] .
                                     '  AND parent_model_name="' . $record['parent_model_name'] . '" ' . "\n" .
                                     '  AND link_to= ' . $record['link_to'] . "\n" .
                                     '  AND link_to_model_name="' . $record['link_to_model_name'] . '" ';
                            $db->Execute($query);
                        }
                    }
                }
            }
        } elseif (!empty($new_rows_links['added']) && is_array($new_rows_links['added']) || $this->get('link_to')) {
            //document adding

            //get other relations until we get the base document
            //$relations = array();
            //$relations = $this->getRelationsRows($this->get('link_to'), $relations);

            if (!empty($new_rows_links['added'])) {
                foreach ($new_rows_links['added'] as $new_row => $old_row) {
                    $new_rows_links['added'][$new_row] = $new_row . ' => ' . $old_row;
                    if (isset($relations[$old_row])) {
                        $new_rows_links['added'][$new_row] .= ' => ' . $relations[$old_row];
                    }
                }
            } else {
                $new_rows_links['added'] = array();
            }

            if ($this->get('link_to_model_name')) {
                $link_to_model_name = $this->get('link_to_model_name');
            } else {
                $link_to_model_name = "Finance_Expenses_Reason";
            }

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     ' SET parent_id = ' . $this->get('id') . ",\n" .
                     '     parent_model_name = "Finance_Expenses_Reason",' . "\n" .
                     '     link_to = ' . $this->get('link_to') . ",\n" .
                     '     link_to_model_name = "' . $link_to_model_name . '"' . ",\n" .
                     '     rows_links = \'' . implode("\n", $new_rows_links['added']) . '\',' . "\n" .
                     '       changes = "' . implode("\n", $rows_changes) . '"';
            $db->Execute($query);
        }

        return !$db->HasFailedTrans();
    }

    public function getRelationsRows($id, &$relations) {

        $db = $this->registry['db'];

        $query = 'SELECT link_to, rows_links FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
         'WHERE parent_id=' . $id . ' AND parent_model_name="Finance_Expenses_Reason"' . "\n" .
         '  AND link_to_model_name="Finance_Expenses_Reason"';
        $records = $db->GetAssoc($query);

        foreach ($records as $id => $record) {
            $this->getRelationsRows($id, $relations);
            $rows = preg_split('#\n|\r|\r\n#', $record);
            foreach ($rows as $row) {
                if (!preg_match('#\d+\s*=>\s*\d+#', $row)) continue;
                $row = preg_split('#\s*=>\s*#', $row, 2);
                if (isset($relations[$row[1]])) {
                    $idx = $row[1];
                    $row[1] .= ' => ' . $relations[$row[1]];
                    unset($relations[$idx]);
                }
                $relations[$row[0]] = $row[1];
            }
        }

        return $relations;
    }

    /**
     * Checks permitted layouts
     *
     * (non-PHPdoc)
     * @see _libs/inc/mvc/Model::getPermittedLayouts()
     * @param string $mode - action name
     * @param string $model - model name, not used in overwrite method
     * @return array - 'view' layouts or 'edit' layouts or array of both
     */
    public function getPermittedLayouts($mode = '', $model = '') {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        //if validLogin or automation user for crontab
        if ($this->registry['validLogin'] || ($this->registry['currentUser'] && $this->registry['currentUser']->get('id') == PH_AUTOMATION_USER)) {
            if (!$this->isDefined('layouts_view')) {
                $groups = $this->registry['currentUser']->getGroups();
                if (count($groups)) {
                    // check if current user is assigned as observer of records of this type
                    $module = 'finance';
                    $set_observer =
                        in_array('observer', $this->registry['config']->getParamAsArray($module, 'assignment_types_' . $this->get('type'))) &&
                        $this->registry['currentUser']->getPersonalSettings($module, 'set_observer');

                    // make sure model has all types of assignments
                    if ($this->get('id')) {
                        if (!$this->isDefined('assignments_responsible')) {
                            $this->getAssignments('responsible');
                        }
                        if (!$this->isDefined('assignments_decision')) {
                            $this->getAssignments('decision');
                        }
                        if (!$this->isDefined('assignments_observer')) {
                            $this->getAssignments('observer');
                        }
                        if (!$this->isDefined('assignments_owner')) {
                            $this->getAssignments();
                        }
                    }

                    //get rights for layouts view
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="view")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval(($this->get('new_type') ? $this->get('new_type') : $this->get('type'))) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="view"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_view_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_view = array();
                    foreach ($layouts_view_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_view[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_view[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_view[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_view', $layouts_view);

                    //get rights for layouts edit
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="edit")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval($this->get('new_type') ? $this->get('new_type') : $this->get('type')) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="edit"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_edit_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_edit = array();
                    foreach ($layouts_edit_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_edit[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_edit[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_edit[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_edit', $layouts_edit);
                } else {
                    $this->set('layouts_view', array());
                    $this->set('layouts_edit', array());
                }
            }
        } else {
            $this->set('layouts_view', array());
            $this->set('layouts_edit', array());
        }

        if ($sanitized) {
            $this->sanitize();
        }

        if ($mode) {
            return $this->get('layouts_' . $mode);
        } else {
            return array($this->get('layouts_view'), $this->get('layouts_edit'));
        }
    }

    /**
     * Correct the expenses reason and create correction document
     *
     * @param Finance_Expenses_Reason $old_model - The old expenses reason fetched from db
     * @return bool - result of the operation
     */
    public function saveCorrect(&$old_model) {

        $db = $this->registry['db'];

        if (!$this->isActivated()) {
            $this->raiseError('error_finance_deactivate_add', 'activate', null, array($this->i18n('addcorrect')));
        }
        if (in_array($old_model->get('payment_status'), array('paid', 'partial')) &&
        bccomp($this->get('total_with_vat'), $old_model->getPaidAmount(), $this->registry['config']->getParam('precision', 'gt2_total_with_vat')) == -1) {
            $this->raiseError('error_finance_expenses_reason_overpaid', '', null,
                array($old_model->get('paid_amount'), $this->get('total_with_vat'),
                      sprintf('%s?%s=finance&amp;%s=expenses_reasons&amp;expenses_reasons=payments&amp;payments=%d',
                              $_SERVER['PHP_SELF'], $this->registry['module_param'],
                              $this->registry['controller_param'], $old_model->get('id'))
                ));
        }
        if (!$this->valid) {
            return false;
        }

        //start transaction
        $db->StartTrans();

        $name = $this->get('name');
        $description = $this->get('description');

        //set old properties
        $this->set('name', $old_model->get('name'), true);
        $this->set('description', $old_model->get('description'), true);
        $this->set('import_customer_name', $old_model->get('customer_name'), true);
        $this->set('trademark_name', $old_model->get('trademark_name'), true);
        $this->set('issue_date', $old_model->get('issue_date'), true);
        $this->set('payment_status', $old_model->get('payment_status'), true);
        $inv_num = $this->get('invoice_num');
        $this->set('invoice_num', $old_model->get('invoice_num'), true);
        $this->set('correction', true, true);

        //get current gt2 rows
        $query = 'SELECT id FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE model = "Finance_Expenses_Reason" AND model_id = ' . $this->get('id');
        $this->set('old_gt2_rows', $this->registry['db']->GetCol($query), true);

        // remove distribution data of expenses reason
        if ($old_model->get('distributed') == PH_FINANCE_DISTRIBUTION_YES) {
            $this->deleteDistribution();
            $this->set('distributed', PH_FINANCE_DISTRIBUTION_NO, true);
        }
        // disable allocation possibility if reason contains no non-commodities in new GT2 values
        if ($old_model->get('allocated_status') == 'enabled' && !$this->checkContainsNonCommodities()) {
            $this->set('allocated_status', 'disabled', true);
        }

        //save changes in the parent document
        $this->slashesEscape();
        $this->edit();
        $this->slashesStrip();

        //get model type
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $fin_type = Finance_Documents_Types::searchOne($this->registry, array('where' => array('fdt.id = ' . $this->get('type'))));
        $fin_type->sanitize();

        //get old and new values of the model
        $old_gt2 = $old_model->get('grouping_table_2');
        $old_values = $old_gt2['values'];
        $old_plain_values = $old_gt2['plain_values'];
        $new_values = $this->get('grouping_table_2');
        $new_plain_values = $new_values['plain_values'];
        $new_values = $new_values['values'];

        //create correction document
        $this->set('parent_name', $this->get('name'), true);
        $this->set('name', $name, true);
        $this->set('description', $description, true);
        $correct_reason = clone $this;
        $correct_reason->unsetProperty('id', true);
        $correct_reason->unsetProperty('num', true);
        $correct_reason->unsetProperty('payment_status', true);
        $correct_reason->unsetProperty('new_handovered_status', true);
        $correct_reason->unsetProperty('distributed', true);
        $correct_reason->set('type', PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON, true);
        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                 'WHERE parent_id = ' . $correct_reason->get('type') . ' AND lang="' . $this->registry['lang'] . '"';
        $type_name = $db->GetOne($query);
        $correct_reason->set('type_name', $type_name, true);
        $correct_reason->set('invoice_num', $inv_num, true);
        $correct_reason->set('status', 'finished', true);
        $correct_reason->set('link_to', $old_model->get('id'), true);
        if (!$this->registry['request']->isRequested('issue_date')) {
            $correct_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
        } else {
            $correct_reason->set('issue_date', $this->registry['request']->get('issue_date'), true);
        }
        unset($correct_reason->counter);

        $correct_reason->getGT2Vars();
        $correct_gt2 = $correct_reason->get('grouping_table_2');
        //clean the values
        $correct_gt2['values'] = array();
        $correct_gt2['rows'] = array();
        $rows_changes = array();

        $correctables = array('quantity', $fin_type->get('calculated_price'), 'discount_value', 'surplus_value');
        $prec = $this->registry['config']->getSectionParams('precision');

        //calculate the difference
        foreach ($old_values as $row => $values) {
            if (isset($new_values[$row])) {
                $changed = array();
                //get changed field(s)
                foreach ($correctables as $k => $v) {
                    if ($new_values[$row][$v] - $values[$v] != 0) {
                        //if price has been changed the discount/surplus values also change
                        //so do not treat this change as another one
                        if (preg_match('#discount|surplus#', $v) && in_array($fin_type->get('calculated_price'), $changed)) {
                            continue;
                        }
                        $changed[] = $v;
                    }
                }
                if (count($changed) > 1) {
                    //we can not change more than one field per row
                    $this->registry['messages']->setError($this->i18n('error_finance_correct_both_changed'));
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                } elseif (count($changed) == 1) {
                    $changed = reset($changed);
                    //process changed field
                    $correct_gt2['values'][$row] = $values;
                    $correct_gt2['values'][$row][$changed] = $new_values[$row][$changed] - $values[$changed];
                    if (preg_match('#discount|surplus#', $changed)) {
                        $correct_gt2['values'][$row][$fin_type->get('calculated_price')] = 0;
                    } elseif ($changed == $fin_type->get('calculated_price')) {
                        //recalculate the discount/surplus values in function of the percentage
                        $correct_gt2['values'][$row]['discount_value'] = round($correct_gt2['values'][$row][$changed] * $correct_gt2['values'][$row]['discount_percentage'] / 100, $prec['gt2_rows']);
                        $correct_gt2['values'][$row]['surplus_value'] = round($correct_gt2['values'][$row][$changed] * $correct_gt2['values'][$row]['surplus_percentage'] / 100, $prec['gt2_rows']);
                        if ($correct_gt2['values'][$row][$changed] < 0) {
                            //negative price - just change the surplus sign to positive
                            $correct_gt2['values'][$row]['surplus_value'] *= -1;
                        }
                    }
                    $rows_changes[$row] = $row . ' => ' . $changed;
                }
                unset($new_values[$row]);
            } else {
                //row has been deleted
                $correct_gt2['values'][$row] = $values;
                $correct_gt2['values'][$row]['quantity'] = - $values['quantity'];
                $rows_changes[$row] = $row . ' => deleted';
            }
        }

        foreach ($new_values as $row => $values) {
            //rows that are added
            if (!empty($values['article_id'])) {
                //and not empty
                $correct_gt2['values'][$row] = $values;
                $rows_changes[$row] = $row . ' => added';
            }
        }

        if (!empty($correct_gt2['values'])) {

            //get information about what we can issue with handover
            $old_model->checkAddingHandover('incoming', true);
            $handover_gt2 = $old_model->get('grouping_table_2');
            $handover_gt2 = isset($handover_gt2['values']) ? $handover_gt2['values'] : array();

            $erred_articles = $articles = array();
            foreach ($correct_gt2['values'] as $row => $values) {
                $articles[] = $values['article_id'];
            }
            if (!empty($articles)) {
                $query = 'SELECT id, subtype FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE id IN (' . implode(', ', $articles) . ')';
                $articles = $db->GetAssoc($query);
            }
            foreach ($correct_gt2['values'] as $row => $values) {
                //for each row which we decrease quantity for (negative quantity)
                //we have to check if we have quantities not received
                //from the customer with handovers
                //!!! ONLY FOR COMMODITY ARTICLES
                if ($values['article_id'] > 0 && $articles[$values['article_id']] == 'commodity' && $values['quantity'] < 0 &&
                  (empty($handover_gt2[$row]['quantity']) ||
                  $handover_gt2[$row]['quantity'] < abs($values['quantity']))) {
                    //calculate the minimal quantity we can choose
                    $minimal = $old_values[$row]['quantity'];
                    if (!empty($handover_gt2[$row]['quantity'])) {
                        $minimal -= $handover_gt2[$row]['quantity'];
                    }
                    $erred_articles[] = $values['article_name'] . ' (min. ' . $minimal . ')';
                }
            }

            if (!empty($erred_articles)) {
                //set error message for the erred articles
                $erred_articles = '<br>&nbsp;&nbsp;&nbsp;-&nbsp;' . implode('<br>&nbsp;&nbsp;&nbsp;-&nbsp;', $erred_articles);
                $this->registry['messages']->setError($this->i18n('error_finance_correct_more_in_handover') . $erred_articles);
                $db->FailTrans();
            }

            $correct_reason->set('rows_changes', $rows_changes, true);
            $correct_gt2['calculated_price'] = $old_gt2['calculated_price'];
            $correct_reason->set('grouping_table_2', $correct_gt2, true);
            $correct_reason->calculateGT2();
            $correct_reason->set('table_values_are_set', true, true);

            //save the correction document
            if (!$correct_reason->save()) {
                $db->FailTrans();
            } else {
                $old_reason = new Finance_Expenses_Reason($this->registry,
                    array(
                        'type' => PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON,
                        'parent_type' => $correct_reason->get('parent_type'),
                        'company' => $correct_reason->get('company')
                    ));
                $get_old_vars = $this->registry->get('get_old_vars');
                $this->registry->set('get_old_vars', true, true);
                $old_reason->getGT2Vars();
                $this->registry->set('get_old_vars', $get_old_vars, true);
                $old_reason->sanitize();
                $correct_reason->slashesStrip();
                require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php';
                Finance_Expenses_Reasons_History::saveData($this->registry,
                                                          array(
                                                              'action_type' => 'add',
                                                              'new_model' => $correct_reason,
                                                              'old_model' => $old_reason
                                                          ));
            }
        // if there are differences in plain values, edit the reason
        } elseif (!array_diff_assoc($new_plain_values, $old_plain_values)) {
            $this->registry['messages']->setError($this->i18n('error_finance_correct_empty'));
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;
        //restore gt2 as it could have been changed in checkAddingHandover function
        $old_model->set('grouping_table_2', $old_gt2, true);
        if ($result) {
            $old_model->set('correction_id', $correct_reason->get('id'), true);
        }

        return $result;
    }

    /**
     * Saves data related to the transformation made from document to expenses reason
     * or from some custom "transformation" action
     *
     * @return bool - result of the operation
     */
    public function saveTransformationDetails() {

        $params = unserialize(General::slashesStrip($this->get('transform_params')));

        $destination_params = array(
            'origin_method' => !empty($params['origin_method']) ? $params['origin_method'] : '',
            'origin_method_id' => !empty($params['origin_method_id']) ? $params['origin_method_id'] : '',
            'destination_model' => $this->modelName,
            'destination_id' => $this->get('id'),
            'destination_full_num' => $this->get('num'),
            'destination_name' => $this->get('name') ?: $this->get('type_name'),
        );

        // if the transformation has been triggered via report or from multiaddinvoice action,
        // creation of relation among multiple model gt2 rows to the current model is possible.
        // So all the relations have to be saved.
        if ((!empty($params['transform_from_report']) || !empty($params['transform_multiaddinvoice'])) &&
            !empty($params['origin_id']) && is_array($params['origin_id'])) {

            // this array is used to prevent the duplicated unique keys during the add of the relations
            $unique_keys = array();

            foreach ($params['origin_id'] as $key => $origin_id) {
                $unique_key = sprintf('%s-%d-%s-%d', $this->modelName, $this->get('id'), $params['origin_model'][$key], $origin_id);
                if (!in_array($unique_key, $unique_keys)) {
                    $unique_keys[] = $unique_key;
                    $set = array();

                    $set['parent_id'] = sprintf('`parent_id` = "%d"', $this->get('id'));
                    $set['parent_model_name'] = sprintf('`parent_model_name` = "%s"', $this->modelName);
                    $set['link_to'] = sprintf('`link_to` = "%d"', $origin_id);
                    $set['link_to_model_name'] = sprintf('`link_to_model_name` = "%s"', $params['origin_model'][$key]);

                    $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . " SET \n" . implode(",\n", $set);

                    $this->registry['db']->Execute($query);

                    // save history for each source model
                    $factory_name = General::singular2plural($params['origin_model'][$key]);
                    $module = strtolower($factory_name);
                    if (strpos($module, '_') !== false) {
                        list($module, $controller) = explode('_', $module, 2);
                    } else {
                        $controller = $module;
                    }

                    require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.factory.php';

                    $alias = $factory_name::getAlias($module, $controller);

                    $filters = array('where' => array($alias . '.id = ' . $origin_id));

                    $model = $factory_name::searchOne($this->registry, $filters);
                    if (!$model) {
                        continue;
                    }

                    $model->set('destination_params', $destination_params, true);

                    $this->loadI18NFiles(PH_MODULES_DIR . $module . '/i18n/' . $this->registry['lang'] . '/' . $module . '.ini');
                    require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.history.php';
                    $history_name = $factory_name . '_History';

                    $history_name::saveData(
                        $this->registry,
                        array(
                            'action_type' => 'create',
                            'model' => $model,
                            'old_model' => $model,
                            'new_model' => $model
                        ));
                }
            }

            // we need to update currency rate in parent models when adding incoming invoice to multiple proformas in different currencies
            if (!empty($params['transform_multiaddinvoice']) && !empty($params['transform_currency_rates']) && is_array($params['transform_currency_rates'])) {
                foreach ($params['transform_currency_rates'] as $currency => $info) {
                    if (!empty($info['rate']) && !empty($info['ids'])) {
                        Finance_Expenses_Reasons::updateCurrencyRate($this->registry, $info['ids'], $info['rate']);
                    }
                }
            }

            if ($this->registry['db']->ErrorMsg()) {
                $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
                return false;
            }
            $this->set('link_to', $params['origin_id'], true);
            $this->set('link_to_model_name', $params['origin_model'], true);
            $this->set('origin_gt2_relations', $params['origin_gt2_relations'], true);
            return true;
        }

        // transformation from a single parent
        $this->set('link_to', $params['origin_id'], true);
        $this->set('link_to_model_name', $params['origin_model'], true);

        // If it's not set to skip the relatives
        if (empty($params['skip_relatives'])) {
            // Make a relation
            $set['parent_id'] = sprintf('`parent_id` = "%d"', $this->get('id'));
            $set['parent_model_name'] = sprintf('`parent_model_name` = "%s"', $this->modelName);
            $set['link_to'] = sprintf('`link_to` = "%d"', $params['origin_id']);
            $set['link_to_model_name'] = sprintf('`link_to_model_name` = "%s"', $params['origin_model']);

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . " SET \n" . implode(",\n", $set);

            $this->registry['db']->Execute($query);
        }

        // save history for source model
        $factory_name = General::singular2plural($params['origin_model']);
        $module = strtolower($factory_name);
        if (strpos($module, '_') !== false) {
            list($module, $controller) = explode('_', $module, 2);
        } else {
            $controller = $module;
        }

        require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.factory.php';

        $alias = $factory_name::getAlias($module, $controller);

        $filters = array('where' => array($alias . '.id = ' . $params['origin_id']));

        $model = $factory_name::searchOne($this->registry, $filters);
        if (!$model) {
            return false;
        }

        $old_model = clone $model;
        $old_model->sanitize();

        $model->set('destination_params', $destination_params, true);

        $this->loadI18NFiles(PH_MODULES_DIR . $module . '/i18n/' . $this->registry['lang'] . '/' . $module . '.ini');
        require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.history.php';
        $history_name = $factory_name . '_History';

        $history_name::saveData(
            $this->registry,
            array(
                'action_type' => 'create',
                'model' => $model,
                'old_model' => $old_model,
                'new_model' => $model
            ));

        // change status of source model
        if (!empty($params['origin_status'])) {

            $model->set('status', $params['origin_status'], true);
            $model->set('substatus', (isset($params['origin_substatus']) ? $params['origin_substatus'] : ''), true);

            if (!$model->setStatus()) {
                return false;
            } else {
                $model = $factory_name::searchOne($this->registry, $filters);
                $model->sanitize();

                $history_name::saveData(
                    $this->registry,
                    array(
                        'action_type' => 'status',
                        'model' => $model,
                        'old_model' => $old_model,
                        'new_model' => $model
                    ));
            }
        }

        if ($this->registry['db']->ErrorMsg()) {
            $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
            return false;
        }

        return true;
    }

    /**
     * Update GT2 row links for transformations
     * @return bool - result of the operation
     */
    public function updateRowsLinks() {
        // If it's set to skip relatives
        $params = unserialize(General::slashesStrip($this->get('transform_params')));
        if (!empty($params['skip_relatives'])) {
            // Skip updating rows links
            // (there will be no record to update anyway,
            // because it will be skipped earlier)
            return true;
        }

        $new_rows_links = $this->get('rows_links');
        if (!empty($new_rows_links['added'])) {
            if ($this->get('origin_gt2_relations')) {
                // if the parameter is set then the model has been made via report or multiaddinvoice action
                //and the relations have to be established
                $update_structured = array();
                $origin_gt2_relations = $this->get('origin_gt2_relations');

                // forms the array which contains the model and model id for a key and all the gt2 rows for it
                foreach ($new_rows_links['added'] as $current_model_row => $parent_model_row) {
                    if ($parent_model_row && isset($origin_gt2_relations[$parent_model_row])) {
                        $gt2_parent_row_info = $origin_gt2_relations[$parent_model_row];
                        $key = $gt2_parent_row_info['model'] . '|' . $gt2_parent_row_info['model_id'];
                        if (!isset($update_structured[$key])) {
                            $update_structured[$key] = array();
                        }

                        $update_structured[$key][] = $current_model_row . ' => ' . $parent_model_row;
                    }
                }

                // write the rows_links in the table
                foreach ($update_structured as $key_data => $rows_relations) {
                    list($link_to_model_name, $link_to) = explode('|', $key_data);
                    if ($link_to_model_name && $link_to) {
                        $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                                 ' SET rows_links = \'' . implode("\n", $rows_relations) . '\'' . "\n" .
                                 ' WHERE parent_id = ' . $this->get('id') . "\n" .
                                 ' AND parent_model_name = "' . $this->modelName . '"' . "\n" .
                                 ' AND link_to = ' . $link_to . "\n" .
                                 ' AND link_to_model_name = "' . $link_to_model_name . '"' . "\n" ;
                        $this->registry['db']->Execute($query);
                    }
                }
            } else {
                // default case - 1:1
                foreach ($new_rows_links['added'] as $new_row => $old_row) {
                    $new_rows_links['added'][$new_row] = $new_row . ' => ' . $old_row;
                }
                if ($this->get('link_to_model_name')) {
                    $link_to_model_name = $this->get('link_to_model_name');
                } else {
                    $link_to_model_name = "Finance_Expenses_Reason";
                }
                $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                         ' SET rows_links = \'' . implode("\n", $new_rows_links['added']) . '\'' . "\n" .
                         ' WHERE parent_id = ' . $this->get('id') . "\n" .
                         ' AND parent_model_name = "Finance_Expenses_Reason"' . "\n" .
                         ' AND link_to = ' . $this->get('link_to') . "\n" .
                         ' AND link_to_model_name = "' . $link_to_model_name . '"' . "\n" ;
                $this->registry['db']->Execute($query);

            }

            if ($this->registry['db']->ErrorMsg()) {
                $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
                return false;
            }
        }

        return true;
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'finance_expenses_reasons', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Finance_Expenses_Reason->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Expense ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        // do not allow any other actions for deactivated models
        if (!$this->isActivated() && $this->origin == 'database' && !in_array($action, array('add', 'view', 'history', 'relatives', 'activate', 'deactivate', 'system_settings_active', 'system_settings_portal', 'system_settings_group'))) {
            return false;
        }

        //special conditions for the clone action
        if ($action == 'clone' && $this->get('type') < PH_FINANCE_TYPE_MAX) {
            //only user defined types of expense reasons and expense invoice with no parent expense reason could be cloned
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE) {
                $sanitize_after = false;
                if (empty($this->registry)) {
                    $this->unsanitize();
                    $sanitize_after = true;
                }

                //check if the expense invoice has a parent expense reason
                $query = 'SELECT fer.id' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                         '  ON frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to=fer.id AND fer.type>' . PH_FINANCE_TYPE_MAX . "\n" .
                         'WHERE frr.parent_model_name="Finance_Expenses_Reason" AND frr.parent_id="' . $this->get('id') . '"';
                $parent_reason_id = $this->registry['db']->GetOne($query);
                if ($sanitize_after) {
                    $this->sanitize();
                }
                if ($parent_reason_id) {
                    //only expense invoices with NO PARENT reason could be cloned
                    return false;
                }
            } else {
                return false;
            }
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'locked') {
                //locked status
                switch ($action) {
                //forbidden actions
                case 'addcorrect':
                case 'addhandover':
                case 'addinvoice':
                case 'addproformainvoice':
                case 'addcredit':
                case 'adddebit':
                case 'distribute':
                case 'allocate_costs':
                    return false;
                    break;
                case 'edit':
                    if ($this->get('type') != PH_FINANCE_TYPE_EXPENSES_INVOICE || $this->get('annulled_by')) {
                        return false;
                        break;
                    }
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } elseif ($this->get('status') == 'finished') {
                //finished status
                switch ($action) {
                //forbidden actions
                case 'observer':
                    return false;
                    break;
                case 'edit':
                    if ($this->get('type') != PH_FINANCE_TYPE_EXPENSES_INVOICE || $this->get('annulled_by')) {
                        return false;
                        break;
                    }
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } else {
                //opened status
                switch ($action) {
                //forbidden actions
                case 'addcorrect':
                case 'addhandover':
                case 'addinvoice':
                case 'addproformainvoice':
                case 'addcredit':
                case 'adddebit':
                case 'distribute':
                case 'allocate_costs':
                    return false;
                    break;
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            }
        } else {
            return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                $this,
                $action,
                $modulePermissionsKey,
                $force
            );
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Check the companies info to see if the current user have permissions for at least one bank account/cashbox
     *
     * @return bool - true if allowed, otherwise false
     */
    public function checkAddPaymentPermissions() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        // check the allowed bank accounts and cashboxes for the slelected company
        require_once(PH_MODULES_DIR . 'finance/models/finance.dropdown.php');
        $params = array($this->registry,
                        'lang'              => $this->get('model_lang'),
                        'company_id'        => ($this->get('company') ? $this->get('company') : array(0)),
                        'payment_direction' => ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE ? 'incomes' : 'expenses'),
                        'active'            => 1);
        $companies_data = Finance_Dropdown::getCompaniesData($params);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return count($companies_data);
    }

    /**
     * Check for additional conditions before allowing (de)activation of model
     *
     * @param string $status - activate or deactivate
     * @return boolean - true - action is allowed, false - not allowed
     */
    public function checkChangeActivePermissions($status) {

        $action_allowed = true;

        if ($status == 'deactivate') {
            if ($this->get('type') < PH_FINANCE_TYPE_MAX && $this->get('status') == 'finished' || $this->get('annulled_by')) {
                $action_allowed = false;
            } elseif ($this->get('type') > PH_FINANCE_TYPE_MAX && $this->get('status') == 'finished') {
                // use filtering to get only what looking for
                $filters_fer = array('fer.annulled_by = 0',
                                     'fer.active = 1');
                $filters_fwd = array('fwd.annulled_by = 0',
                                     'fwd.active = 1');
                $this->getRelatives(array('get_invoices' => $filters_fer,
                                          'get_pro_invoices' => $filters_fer,
                                          'get_handovers' => $filters_fwd));

                // if there are any related financial or warehouse documents
                // or there are distributed payments/incomes
                if ($this->get('invoices') || $this->get('pro_invoices') || $this->get('handovers') ||
                $this->getPaidAmount()) {
                    $this->unsetProperty('invoices', true);
                    $this->unsetProperty('pro_invoices', true);
                    $this->unsetProperty('handovers', true);
                    $action_allowed = false;
                }
            }
        } elseif ($status == 'activate') {
            // deactivated correction document cannot be directly activated
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON && !$this->isActivated()) {
                $action_allowed = false;
            }
        }

        // get correction documents for reason because they should be (de)activated as well
        if ($action_allowed && $this->get('type') > PH_FINANCE_TYPE_MAX && $this->get('status') == 'finished') {
            $filters_fer = array('fer.active IS NOT NULL');
            $this->getRelatives(array('get_correct_reasons' => $filters_fer));
        }

        return $action_allowed;
    }

    /**
     * Checks permission for nopay action
     *
     * @return bool - true - accessible, false - inaccessible
     */
    public function checkNopayPermission() {

        $right = false;
        if (!$this->get('annulled_by') && $this->get('status') == 'finished' && $this->checkPermissions('nopay') && in_array($this->get('payment_status'), array('unpaid', 'nopay'))) {
            if ($this->get('type') < PH_FINANCE_TYPE_MAX && !in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE))) {
                //true for invoices, credit/debit notices
                $right = true;
            } elseif ($this->get('type') > PH_FINANCE_TYPE_MAX && $this->checkAddingInvoice()) {
                $sanitize_after = false;
                if ($this->sanitized) {
                    $sanitize_after = true;
                    $this->unsanitize();
                }
                //check type settings for nopay
                require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                $financeType = Finance_Documents_Types::searchOne($this->registry, array(
                                            'where' => array('fdt.id=' . $this->get('type'))));
                if ($financeType->get('nopay')) {
                    $right = true;
                }

                if ($sanitize_after) {
                    $this->sanitize();
                }
            }
        }

        return $right;
    }

    /**
     * Change payment status to nopay or unpaid
     *
     * @return string|bool - old payment status or false
     */
    public function changeNopayStatus() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        $result = false;
        $new_payment_status = '';
        $current_payment_status = $this->get('payment_status');
        if ($current_payment_status == 'nopay') {
            $new_payment_status = 'unpaid';
        } elseif ($current_payment_status == 'unpaid') {
            $new_payment_status = 'nopay';
        }
        if ($new_payment_status) {
            $set = array();
            $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
            $set['modified']       = sprintf("modified=now()");
            $set['payment_status'] = sprintf("`payment_status`='%s'", $new_payment_status);
            //query to update payment status
            $query = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                      'SET ' . implode(', ', $set) . "\n" .
                      'WHERE id=' . $this->get('id');
            $this->registry['db']->Execute($query);

            if ($this->registry['db']->ErrorMsg()) {
                $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
                $result = false;
            } else {
                $result = $current_payment_status;
            }
        }
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Updates payment status of proforma(s) when invoice has been issued from it/them
     * or when invoice has been annulled.
     *
     * @return boolean - result of the operation
     */
    public function updateProformaPaymentStatus() {
        if (!$this->get('id') || $this->get('type') != PH_FINANCE_TYPE_EXPENSES_INVOICE) {
            return true;
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        // check if current model is annulled
        $query = 'SELECT annulled_by FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' WHERE id=' . $this->get('id');
        // specify new payment status for parent proforma(s)
        $proforma_payment_status = $db->GetOne($query) ? 'unpaid' : 'invoiced';

        $query = 'SELECT fer.id' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                 '  ON frr.link_to_model_name="Finance_Expenses_Reason" AND frr.link_to=fer.id' . "\n" .
                 'WHERE frr.parent_model_name="Finance_Expenses_Reason"' . "\n" .
                 '  AND frr.parent_id=' . $this->get('id') . "\n" .
                 '  AND fer.type=' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE;
        $proforma_ids = $db->GetCol($query);

        if (!empty($proforma_ids)) {
            $query = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                     'SET payment_status="' . $proforma_payment_status . '" ' . "\n" .
                     'WHERE id IN (' . implode(', ', $proforma_ids) . ')';
            $db->Execute($query);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Checks whether a handover could be added
     *
     * @param string $direction - 'incoming' or 'outgoing'
     * @param bool $set_table_back - sets the calculated quantities back in the model
     * @return bool
     */
    public function checkAddingHandover($direction, $set_table_back = false) {
        if (!$this->isActivated()) {
            return false;
        }

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];
        $precQuantity = $this->registry['config']->getParam('precision', 'gt2_quantity');

        //get quantities in the previous handovers
        $query = 'SELECT fdd.id AS row_id, fwd.direction, fdd.quantity' . "\n" .
                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 '  ON fdd.model_id = frr.parent_id AND frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND frr.parent_model_name = fdd.model AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                 '  ON fdd.model_id = fwd.id AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER . "\n" .
                 '  AND fwd.annulled_by = 0' . "\n" .
                 'WHERE fdd.model = "Finance_Warehouses_Document"';
        $handovered = $db->GetAssoc($query);

        //get links between handovers and expenses reason
        $query = 'SELECT frr.parent_id as idx, frr.rows_links' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                 '  ON frr.parent_id = fwd.id AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER . "\n" .
                 'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                 '  AND fwd.annulled_by = 0' . "\n" .
                 '  AND frr.parent_model_name = "Finance_Warehouses_Document"';
        $handover_links = $db->GetAssoc($query);

        if (empty($handover_links) && !$set_table_back) {
            if ($sanitize_after) {
                $this->sanitize();
            }
            if ($direction == 'incoming' && $this->get('type') != PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE ||
                $direction == 'outgoing' && $this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                //there are no handovers for this expenses reason
                return true;
            } else {
                return false;
            }
        }

        $gt2_just_fetched = false;
        if (!$this->isDefined('grouping_table_2')) {
            $gt2_just_fetched = true;
            $this->getGT2Vars();
        }

        foreach ($handover_links as $handover => $handover_rows) {
            //calculate handovered quantities
            $handover_rows = preg_split('#\n|\r|\r\n#', $handover_rows);
            foreach ($handover_rows as $row) {
                $row = preg_split('#\s*=>\s*#', $row);
                $sign = 1;
                if ($handovered[$row[0]]['direction'] == 'outgoing') {
                    $sign = -1;
                }
                if (isset($row[1]) && isset($handovered[$row[1]]) && !is_array($handovered[$row[1]])) {
                    $handovered[$row[1]] += $sign * $handovered[$row[0]]['quantity'];
                    unset($handovered[$row[0]]);
                } else {
                    $handovered[$row[1]] = $sign * $handovered[$row[0]]['quantity'];
                    unset($handovered[$row[0]]);
                }
            }
        }

        $gt2 = $this->get('grouping_table_2');
        $nom_ids = array();
        foreach ($gt2['values'] as $values) {
            if (!empty($values['article_id'])) {
                $nom_ids[] = $values['article_id'];
            }
        }
        // clear blank values from array
        $nom_ids = array_diff($nom_ids, array(''));

        //get only commodities (the nomenclatures that are saved in warehouses)
        $noms_in_warehouse = array();
        if ($nom_ids) {
            $query = 'SELECT id FROM ' . DB_TABLE_NOMENCLATURES . "\n" .
                     'WHERE `subtype`="commodity" AND id IN (' . implode(', ', $nom_ids) . ') AND deleted_by = 0';
            $noms_in_warehouse = $this->registry['db']->GetCol($query);
        }

        foreach ($gt2['values'] as $row_id => $row) {
            if (empty($row) || !in_array($row['article_id'], $noms_in_warehouse)) {
                //remove non-commodity noms
                unset($gt2['values'][$row_id]);
                continue;
            } else {
                $gt2['values'][$row_id]['quantity'] = abs($row['quantity']);
            }
        }
        if ($direction == 'incoming') {
            //calculate the difference between the reason and handovers
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                //set the handovered quantity to the reason
                //as we can return in the warehouse only handovered quantity
                foreach ($gt2['values'] as $row => $values) {
                    if (isset($handovered[$row]) && $handovered[$row] < 0) {
                        $gt2['values'][$row]['quantity'] = (-1) * $handovered[$row];
                    } else {
                        unset($gt2['values'][$row]);
                    }
                }
            } else {
                foreach ($handovered as $row => $value) {
                    if (!empty($gt2['values'][$row])) {
                        $gt2['values'][$row]['quantity'] = bcsub(
                            $gt2['values'][$row]['quantity'],
                            $value,
                            $precQuantity
                        );
                        if ($gt2['values'][$row]['quantity'] <= 0) {
                            unset($gt2['values'][$row]);
                        }
                    }
                }
            }
        } elseif ($direction == 'outgoing') {
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                foreach ($handovered as $row => $value) {
                    if (!empty($gt2['values'][$row])) {
                        $gt2['values'][$row]['quantity'] += $value;
                        if ($gt2['values'][$row]['quantity'] <= 0) {
                            unset($gt2['values'][$row]);
                        }
                    }
                }
            } else {
                //set the handovered quantity to the reason
                //as we can return in the warehouse only handovered quantity
                foreach ($gt2['values'] as $row => $values) {
                    if (isset($handovered[$row]) && $handovered[$row] > 0) {
                        $gt2['values'][$row]['quantity'] = $handovered[$row];
                    } else {
                        unset($gt2['values'][$row]);
                    }
                }
            }
        } else {
            if ($sanitize_after) {
                $this->sanitize();
            }
            return false;
        }

        if ($set_table_back) {
            if ($gt2['values']) {
                $this->set('grouping_table_2', $gt2, true);
            } else {
                $this->set('grouping_table_2', array(), true);
            }
        } elseif ($gt2_just_fetched) {
            $this->unsetProperty('grouping_table_2');
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        if ($gt2['values']) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Update expense reason payments
     *
     * @param string $selected_tab - type of payment (payment, credit note, reason, invoice)
     * @return bool - result of the operation
     */
    public function updateFinanceRelatives($selected_tab = 'payment') {
        //UPDATE RELATIVES OF THE MODEL
        $db = &$this->registry['db'];

        // if a started transaction has already failed (because of
        // before_action automations), do not perform actions
        if ($db->HasFailedTrans()) {
            return false;
        }

        $db->StartTrans();

        if (!$this->get('relatives_payments')) {
            $db->FailTrans();
            $db->CompleteTrans();
            return false;
        }

        if ($selected_tab == 'payment') {
            $this->getCustomerPayments();

            $all_amounts = 0;
            if ($this->get('relatives_payments')) {
                $all_amounts += array_sum($this->get('relatives_payments'));
                $all_amounts = round($all_amounts, 2);
                if (bccomp($all_amounts, $this->get('remaining_amount'), 2) == 1) {
                    //the distributed amount ($all_amounts) is larger than the remaining amount for this expense reason
                    // set error message
                    $this->raiseError('error_finance_expenses_reasons_allocated_amount_total');
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                }
            }

            $amounts = $this->get('relatives_payments');
            $payments = $this->get('payments');
            $distributed_amount = 0;
            foreach ($amounts as $key => $amount) {
                $amount = round($amount, 2);
                if ($amount > 0) {
                    $tmp_compare = round($payments[$key]->get('amount') - $payments[$key]->get('paid_amount'), 2);
                    if (!isset($payments[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        //the distributed amount for this payment ($amount) is larger than the remaining undistributed amount for this payment
                        // set error message
                        $this->raiseError('error_finance_expenses_reasons_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }
                    $insert = array();
                    $insert['modified'] = sprintf("modified=now()");
                    $insert['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
                    $update = $insert;
                    $update['paid_amount'] = sprintf("paid_amount=paid_amount + %f", $amount);
                    $insert['paid_amount'] = sprintf("paid_amount='%f'", $amount);
                    if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $this->get('id'));
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", $this->modelName);
                        $insert['parent_id'] = sprintf("parent_id='%d'", $key);
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", 'Finance_Payment');
                    } else {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $key);
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", 'Finance_Payment');
                        $insert['parent_id'] = sprintf("parent_id='%d'", $this->get('id'));
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", $this->modelName);
                    }
                    $insert['paid_currency'] = sprintf("paid_currency='%s'", $this->get('currency'));
                    $insert['added'] = sprintf("added=now()");
                    $insert['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

                    //query to insert/update the finance balance
                    $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);

                    $res = $db->Execute($query2);
                    if ($res) {
                        $distributed_amount += $amount;
                    }
                }

            }
        } else {
            //save amounts from other model
            $this->getPaymentsDistribution($selected_tab);

            $all_amounts = 0;
            if ($this->get('relatives_payments')) {
                $all_amounts += array_sum($this->get('relatives_payments'));
                $all_amounts = round($all_amounts, 2);
                if (bccomp($all_amounts, $this->get('remaining_amount'), 2) == 1) {
                    //the distributed amount ($all_amounts) is larger than the remaining amount for this income reason
                    // set error message
                    $this->raiseError('error_finance_expenses_reasons_allocated_amount_total');
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                }
            }

            $amounts = $this->get('relatives_payments');
            $models = $this->get(General::singular2plural($selected_tab));
            $distributed_amount = 0;
            foreach ($amounts as $key => $amount) {
                $amount = round($amount, 2);
                if ($amount > 0) {
                    $tmp_compare = round($models[$key]->get('total_with_vat') - $models[$key]->get('paid_amount'), 2);
                    if (!isset($models[$key]) || bccomp($tmp_compare, $amount, 2) == -1) {
                        //the distributed amount for this payment ($amount) is larger than the remaining undistributed amount for this payment
                        // set error message
                        $this->raiseError('error_finance_expenses_reasons_allocated_amount_row');
                        $db->FailTrans();
                        $db->CompleteTrans();
                        return false;
                    }

                    if (preg_match('#^expenses_#', $selected_tab)) {
                        $model_name = 'Finance_Expenses_Reason';
                    } elseif (preg_match('#^incomes_#', $selected_tab)) {
                        $model_name = 'Finance_Incomes_Reason';
                    }

                    $insert = array();
                    $insert['modified'] = sprintf("modified=now()");
                    $insert['modified_by'] = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
                    $update = $insert;
                    $update['paid_amount'] = sprintf("paid_amount=paid_amount + %f", $amount);
                    $insert['paid_amount'] = sprintf("paid_amount='%f'", $amount);
                    if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $this->get('id'));
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", $this->modelName);
                        $insert['parent_id'] = sprintf("parent_id='%d'", $key);
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", $model_name);
                    } else {
                        $insert['paid_to'] = sprintf("paid_to='%d'", $key);
                        $insert['paid_to_model_name'] = sprintf("paid_to_model_name='%s'", $model_name);
                        $insert['parent_id'] = sprintf("parent_id='%d'", $this->get('id'));
                        $insert['parent_model_name'] = sprintf("parent_model_name='%s'", $this->modelName);
                    }

                    $insert['paid_currency'] = sprintf("paid_currency='%s'", $this->get('currency'));
                    $insert['added'] = sprintf("added=now()");
                    $insert['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

                    //query to insert/update the finance balance
                    $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);

                    $res = $db->Execute($query2);
                    if ($res) {
                        $distributed_amount += $amount;
                        $distributed_amount = round($distributed_amount, 2);
                    }
                }

            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Remove paid amount from expenses reason
     *
     * @param int $paid_to - payment id
     * @param string $selected_tab - type of payment (payment, credit notice, invoice or debit note, reason)
     * @return bool - result of the operation
     */
    public function emptyPaidAmount($paid_to, $selected_tab = 'payment') {

        if (!$paid_to) {
            return true;
        }
        $db = &$this->registry['db'];

        // if a started transaction has already failed (because of
        // before_action automations), do not perform actions
        if ($db->HasFailedTrans()) {
            return false;
        }

        $db->StartTrans();

        if ($selected_tab == 'payment') {
            $parent_model_name = "Finance_Payment";
        } elseif (preg_match('#^incomes_#', $selected_tab)) {
            $parent_model_name = "Finance_Incomes_Reason";
        } elseif (preg_match('#^expenses_#', $selected_tab)) {
            $parent_model_name = "Finance_Expenses_Reason";
        }
        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
            // removes balance record for current model and related model
            $query = 'DELETE FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                     'WHERE parent_id=' . $paid_to . ' AND  paid_to=' . $this->get('id') . "\n" .
                     '  AND paid_to_model_name="Finance_Expenses_Reason" AND parent_model_name="' . $parent_model_name . '"';
        } else {
            // removes balance record for current model and related model
            $query = 'DELETE FROM ' . DB_TABLE_FINANCE_BALANCE . "\n" .
                     'WHERE paid_to=' . $paid_to . ' AND  parent_id=' . $this->get('id') . "\n" .
                     '  AND parent_model_name="Finance_Expenses_Reason" AND paid_to_model_name="' . $parent_model_name . '"';
        }

        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Annuls expenses reason
     *
     * @return bool - result of the operation
     */
    public function annul() {

        $db = $this->registry['db'];
        $db->StartTrans();

        $old_reason = clone $this;
        $old_reason->getGT2Vars();
        $old_reason->sanitize();

        $query = 'SELECT *' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_BALANCE . ' AS fr ' . "\n" .
                 'WHERE ' .
                 ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE ?
                 '(fr.paid_to= ' . $this->get('id') . ' AND fr.paid_to_model_name="Finance_Expenses_Reason")' :
                 '(fr.parent_id=' . $this->get('id') . ' AND fr.parent_model_name="Finance_Expenses_Reason")');
        $payments = $db->GetAll($query);
        $this->getGT2Vars();

        //remove payments
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        $pmnts = array();
        foreach ($payments as $payment) {
            if ($payment['parent_id'] == $this->get('id') && $payment['parent_model_name'] == "Finance_Expenses_Reason") {
                if (preg_match('#^finance_#i', $payment['paid_to_model_name'])) {
                    $selected_tab = strtolower(preg_replace('#finance_#i', '', $payment['paid_to_model_name']));
                } else {
                    $selected_tab = 'payment';
                }
                $this->emptyPaidAmount($payment['paid_to'], $selected_tab);
                $search_id = $payment['paid_to'];
            } elseif ($payment['paid_to'] == $this->get('id') && $payment['paid_to_model_name'] == "Finance_Expenses_Reason") {
                if (preg_match('#^finance_#i', $payment['parent_model_name'])) {
                    $selected_tab = strtolower(preg_replace('#finance_#i', '', $payment['parent_model_name']));
                } else {
                    $selected_tab = 'payment';
                }
                $this->emptyPaidAmount($payment['parent_id'], $selected_tab);
                $search_id = $payment['parent_id'];
            }

            $query = 'SELECT ' . ($selected_tab == 'payment' ? 'reason' : 'name') . "\n" .
                     'FROM ' . ($selected_tab == 'payment' ? DB_TABLE_FINANCE_PAYMENTS_I18N : (preg_match('#expenses#', $selected_tab) ? DB_TABLE_FINANCE_EXPENSES_REASONS_I18N : DB_TABLE_FINANCE_INCOMES_REASONS_I18N)) . "\n" .
                     'WHERE parent_id = ' . $search_id . ' AND lang = "' . $this->registry['lang'] . '"';
            $pmnts[] = array(
                'id' => $search_id,
                'name' => $this->registry['db']->GetOne($query),
                'controller' => ($selected_tab == 'payment' ? 'payments' : (preg_match('#expenses#', $selected_tab) ? 'expenses_reasons' : 'incomes_reasons'))
            );
            ///write some history for payment removal
            require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
            Finance_Expenses_Reasons_History::saveData($this->registry,
                array('model' => $this,
                      'action_type' => 'empty',
                      'new_model' => $this,
                      'old_model' => $old_reason));
        }
        if (!empty($pmnts)) {
            foreach ($pmnts as $k => $v) {
                $pmnts[$k] = sprintf('<a target="_blank" href="%s?%s=finance&amp;controller=%s&amp;%s=view&amp;view=%d">%s</a>',
                        $_SERVER['PHP_SELF'], $this->registry['module_param'],
                        $v['controller'], $v['controller'], $v['id'], ($v['name'] ?: $this->i18n('finance_' . General::plural2singular($v['controller']))));
            }
            $this->registry['messages']->setWarning($this->i18n('finance_removed_payments', array(implode(', ', $pmnts))));
        }

        if ($this->get('type') != PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
            $table = $this->get('grouping_table_2');
            foreach ($table['values'] as $id => $values) {
                if (empty($values)) continue;
                $table['values'][$id]['deleted'] = 1;
                $table['values'][$id]['quantity'] = 0;
            }
            $this->set('grouping_table_2', $table, true);
            $this->calculateGT2();
            $this->set('table_values_are_set', true, true);

            $this->set('new_handovered_status', 'none', true);
        }

        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE || $this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE) {
            //set parent's handovered status
            $articles = array();
            $table = $this->get('grouping_table_2');
            foreach ($table['values'] as $id => $values) {
                if (empty($values)) continue;
                $articles[] = $values['article_id'];
            }
            if ($articles) {
                $query = 'SELECT id FROM ' . DB_TABLE_NOMENCLATURES . ' WHERE subtype = "commodity" AND id IN (' . implode(', ', $articles) . ')';
                $articles = $db->GetCol($query);
            }
            if (!empty($articles)) {
                //get parent
                $query = 'SELECT fer.id' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' fer' . "\n" .
                         '  ON fer.id = frr.link_to AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . ' fdt' . "\n" .
                         '  ON fer.type = fdt.id' . "\n" .
                         'WHERE frr.parent_model_name = "Finance_Expenses_Reason" AND frr.parent_id = ' . $this->get('id') . "\n" .
                         '  AND fdt.id > ' . PH_FINANCE_TYPE_MAX . ' AND fdt.commodity != "none"';
                $parent_id = $db->GetOne($query);
                if ($parent_id) {
                    //set parent document status to NOT
                    $tmp_model = clone $this;
                    $tmp_model->set('new_handovered_status', 'not', true);
                    $tmp_model->set('id', $parent_id, true);
                    $tmp_model->setHandoveredStatus();
                    unset($tmp_model);
                }
            }
        }

        $this->set('annulled', date('Y-m-d H:i:s'), true);
        $this->set('annulled_by', $this->registry['currentUser']->get('id'), true);
        $this->slashesEscape();
        $this->edit();

        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE) {
            // update payment status of parent proforma(s) to 'unpaid' when annulling invoice
            $this->updateProformaPaymentStatus();
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php';
        $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                   array('action_type' => 'annul',
                                                                         'new_model' => $this,
                                                                         'old_model' => $old_reason));

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Checks if annulment of expenses reason is allowed
     *
     * @return bool - true if allowed, otherwise false
     */
    public function allowAnnul() {

        if ($this->get('status') != 'finished' ||
            $this->get('annulled_by') != 0 ||
            $this->get('type') == PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON ||
            $this->get('allocated_status') == 'allocated' ||
            $this->hasAllocatedCosts()
        ) {
            return false;
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        $db = $this->registry['db'];

        $result = true;
        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE) {
            // check if non-annulled credit/debit notes were issued from invoice
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                    '  ON frr.parent_id = fer.id AND fer.annulled_by = 0' . "\n" .
                    '  AND fer.type IN (' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE . ',' . PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE . ')' . "\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            $credits_debits = $db->GetAll($query);
            if ($credits_debits) {
                $result = false;
            }
        } elseif ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
            // check if non-annulled invoice was issued from proforma
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                     '  ON frr.parent_id = fer.id AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_INVOICE . "\n" .
                     '  AND fer.annulled_by = 0' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                     '  AND frr.parent_model_name = "Finance_Expenses_Reason"';
            $invoice = $db->GetOne($query);
            if ($invoice) {
                $result = false;
            }
        }

        if ($this->get('type') != PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
            // check for handovers
            $query = 'SELECT COUNT(frr.parent_id)' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                     '  ON frr.parent_id = fwd.id AND frr.parent_model_name = "Finance_Warehouses_Document" ' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                     '  AND fwd.annulled_by = 0' . "\n" .
                     '  AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER;
            $handovers = $db->GetOne($query);
            if ($handovers) {
                $result = false;
            }
        }

        if ($unsanitize) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Updates 'distributed' status of model
     * (Set to distributed after distribution is saved or
     * set to not distributed when expense reason
     * is issued from contract and no automatic distribution has been saved.
     * By default value is "there will be no distribution".)
     *
     * @return bool - Result of operation
     */
    public function updateDistributed() {
        $db = $this->registry['db'];
        $db->StartTrans();

        $query = 'UPDATE ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                 'SET distributed="' . $this->get('distributed') . '"' . "\n" .
                 'WHERE id=' . $this->get('id');
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Overwrites method in main model so that type name is
     * replaced with a type-specific string if not present.
     *
     * (non-PHPdoc)
     * @see _libs/inc/mvc/Model::getModelTypeName()
     * @return string - Name of type of model
     */
    function getModelTypeName() {
        $type_name = $this->get('type_name');
        if (!$type_name) {
            $type = $this->get('type');
            switch ($type) {
                case PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON:
                    $type_name = $this->i18n('finance_expenses_reasons_correct_reason');
                    break;
                case PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE:
                    $type_name = $this->i18n('finance_expenses_reasons_credit_notice');
                    break;
                case PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE:
                    $type_name = $this->i18n('finance_expenses_reasons_debit_notice');
                    break;
                case PH_FINANCE_TYPE_EXPENSES_INVOICE:
                    $type_name = $this->i18n('finance_expenses_reasons_invoice');
                    break;
                case PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE:
                    $type_name = $this->i18n('finance_expenses_reasons_proforma_invoice');
                    break;
                default :
                    $type_name = $this->i18n('finance_expenses_reason');
                    break;
            }
        }

        return $type_name;
    }

    /**
     * Update last delivery and average weighted prices
     * for all the nomenclatures in the model GT2
     *
     * @return bool - result of the operation
     */
    public function updateNomPrices() {

        $db = &$this->registry['db'];

        //check if this is the first document of the chain
        if ($this->get('type') != $this->getExpensesRootType()) {
            return true;
        }

        $gt2 = $this->get('grouping_table_2');
        if (empty($gt2)) {
            $gov = $this->registry->get('get_old_vars');
            $this->registry->set('get_old_vars', true, true);
            $this->getGT2Vars();
            $gt2 = $this->get('grouping_table_2');
            $this->registry->set('get_old_vars', $gov, true);
        }
        if (!$this->get('currency')) {
            $query = 'SELECT currency FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                     'WHERE id = ' . $this->get('id');
            $this->set('currency', $db->GetOne($query), true);
        }

        // get precision settings
        $prec = $this->registry['config']->getSectionParams('precision');

        $user_id = $this->registry['currentUser'] ? $this->registry['currentUser']->get('id') : PH_AUTOMATION_USER;

        $rates = array();
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        foreach ($gt2['values'] as $key => $values) {
            if (empty($values['article_id'])) {
                continue;
            }
            $now = $db->GetOne('SELECT NOW()');
            $set = array(
                'modified' => sprintf("modified = '%s'", $now),
                'modified_by' => sprintf('modified_by = %d', $user_id),
            );
            //get nomenclature average price and current quantity (if present)
            $query = 'SELECT n.* ,' . "\n" .
                     '  ROUND(n.sell_price, ' . $prec['nom_sell_price'] . ') AS sell_price,' . "\n" .
                     '  ROUND(n.last_delivery_price, ' . $prec['nom_last_delivery_price'] . ') AS last_delivery_price,' . "\n" .
                     '  ROUND(n.average_weighted_delivery_price, ' . $prec['nom_average_weighted_delivery_price'] . ') AS average_weighted_delivery_price,' . "\n" .
                     '  nti18n.name AS type_name, nti18n.name_plural AS type_name_plural,' . "\n" .
                     '  (SELECT SUM(ROUND(quantity, ' . $prec['gt2_quantity'] . '))' . "\n" .
                     '  FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . "\n" .
                     '  WHERE nomenclature_id = ' . $values['article_id'] . ') AS quantity,' . "\n" .
                     '  (SELECT calculated_price' . "\n" .
                     '  FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     '  WHERE model = "Finance_Expenses_Reason" AND id = ' . $this->get('type') . ') AS calculated_price' . "\n" .
                     'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
                     '  ON n.type = nti18n.parent_id AND nti18n.lang = \'' . $this->get('model_lang') . '\'' . "\n" .
                     'WHERE id = ' . $values['article_id'];
            $nom = $db->GetRow($query);

            if (empty($nom)) {
                continue;
            }

            $old_nomenclature = new Nomenclature(
                $this->registry,
                $nom + array(
                    'name' => $values['article_name'],
                    'code' => $values['article_code'],
                    'model_lang' => $this->get('model_lang'),
                )
            );
            $old_nomenclature->sanitize();
            $new_nomenclature = clone $old_nomenclature;

            //prepare last delivery price
            if ($nom['last_delivery_price_currency']) {
                if ($this->get('currency') != $nom['last_delivery_price_currency']) {
                    //different currencies - get rate
                    if (empty($rates[$this->get('currency') . $nom['last_delivery_price_currency']])) {
                        $rate = Finance_Currencies::getRate($this->registry, $this->get('currency'), $nom['last_delivery_price_currency']);
                        $rates[$this->get('currency') . $nom['last_delivery_price_currency']] = $rate;
                    } else {
                        $rate = $rates[$this->get('currency') . $nom['last_delivery_price_currency']];
                    }
                } else {
                    $rate = 1;
                }
            } else {
                //currency is not set so set it from the GT2
                $rate = 1;
                $set['last_delivery_price_currency'] = sprintf('last_delivery_price_currency = "%s"', $this->get('currency'));
                $nom['last_delivery_price_currency'] = $this->get('currency');
            }
            //$last_delivery_price = $values[$nom['calculated_price']] * $rate;
            $last_delivery_price = $values['price_with_discount'] * $rate;

            // round and format final calculated price according to its precision setting
            $last_delivery_price = sprintf("%.{$prec['nom_last_delivery_price']}f", round($last_delivery_price, $prec['nom_last_delivery_price']));

            $set['last_delivery_price'] = sprintf('last_delivery_price = "%s"', $last_delivery_price);

            //prepare average weighted delivery price
            if ($nom['average_weighted_delivery_price_currency']) {
                if ($nom['last_delivery_price_currency'] != $nom['average_weighted_delivery_price_currency']) {
                    //different currencies - get rate
                    if (empty($rates[$nom['last_delivery_price_currency'] . $nom['average_weighted_delivery_price_currency']])) {
                        $rate = Finance_Currencies::getRate($this->registry, $nom['last_delivery_price_currency'], $nom['average_weighted_delivery_price_currency']);
                        $rates[$nom['last_delivery_price_currency'] . $nom['average_weighted_delivery_price_currency']] = $rate;
                    } else {
                        $rate = $rates[$nom['last_delivery_price_currency'] . $nom['average_weighted_delivery_price_currency']];
                    }
                } else {
                    $rate = 1;
                }
            } else {
                //currency is not set so set it from the GT2
                $rate = 1;
                $set['average_weighted_delivery_price_currency'] = sprintf('average_weighted_delivery_price_currency = "%s"', $nom['last_delivery_price_currency']);
                $nom['average_weighted_delivery_price_currency'] = $nom['last_delivery_price_currency'];
            }

            //calculate average weighted price
            if ($nom['subtype'] == 'commodity') {
                if ($this->registry['config']->getParam('nomenclatures', 'update_delivery_prices_on_handover')) {
                    continue;
                }
                $average_weighted_delivery_price = $nom['average_weighted_delivery_price'] * $nom['quantity'];
                $average_weighted_delivery_price += $last_delivery_price * $rate * $values['quantity'];
                if ($nom['quantity'] + $values['quantity'] != 0) {
                    $average_weighted_delivery_price = $average_weighted_delivery_price / ($nom['quantity'] + $values['quantity']);
                }
            } else {
                // 'average_weighted_delivery_price' is fetched from db so
                // it is a string formatted like this: '0.000000'
                if (floatval($nom['average_weighted_delivery_price']) > 0) {
                    $average_weighted_delivery_price = ($nom['average_weighted_delivery_price'] + $last_delivery_price * $rate) / 2;
                } else {
                    $average_weighted_delivery_price = $last_delivery_price * $rate;
                }
            }
            // round and format final calculated price according to its precision setting
            $average_weighted_delivery_price = sprintf("%.{$prec['nom_average_weighted_delivery_price']}f", round($average_weighted_delivery_price, $prec['nom_average_weighted_delivery_price']));

            $set['average_weighted_delivery_price'] = sprintf('average_weighted_delivery_price = "%s"', $average_weighted_delivery_price);

            //update nomenclature data
            $query = 'UPDATE ' . DB_TABLE_NOMENCLATURES . ' SET' . "\n" . implode(",\n", $set) . "\n" .
                     'WHERE id = ' . $values['article_id'];
            $db->Execute($query);

            //  write log entry for average_weighted_delivery_price update
            $price_update_params = array(
                'parent_id' => $values['article_id'],
                'old_price' => $nom['average_weighted_delivery_price'],
                'currency' => $nom['average_weighted_delivery_price_currency'],
                'old_quantity' => $nom['quantity'] ?: 0,
                'delivery_price' => $last_delivery_price * $rate, // in AWDP currency, without rounding
                'delivery_quantity' => $values['quantity'] ?: 0,
                'new_price' => $average_weighted_delivery_price,
                'update_type' => 'delivery',
                'row_id' => $key,
                'allocated_cost_id' => 0,
                'modified' => $now,
                'modified_by' => $user_id,
            );
            $query = "
                INSERT INTO " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " SET " .
                implode(", ", array_map(function($v, $k) { return "$k = '$v'"; }, $price_update_params, array_keys($price_update_params)));
            $db->Execute($query);

            $nom['last_delivery_price'] = $last_delivery_price;
            $nom['average_weighted_delivery_price'] = $average_weighted_delivery_price;
            foreach ($nom as $k => $v) {
                $new_nomenclature->set($k, $v, true);
            }

            // write history into nomenclature for prices update
            Nomenclatures_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'edit',
                    'model' => $new_nomenclature,
                    'new_model' => $new_nomenclature,
                    'old_model' => $old_nomenclature,
                )
            );
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Checks if incoming invoice/proforma could be added to current model
     *
     * @param boolean $proforma - if true, check for proforma, otherwise check for invoice
     * @return boolean - if true, adding is possible, if false - it is not
     */
    public function checkAddingInvoice($proforma = false) {
        if (!$this->isActivated() || $this->get('annulled_by')) {
            return false;
        }

        if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE || $this->get('type') > PH_FINANCE_TYPE_MAX) {
            if ($this->sanitized) {
                $this->unsanitize();
                $db = $this->registry['db'];
                $this->sanitize();
            } else {
                $db = $this->registry['db'];
            }

            if ($proforma) {
                $type = PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE;
            } else {
                $type = PH_FINANCE_TYPE_EXPENSES_INVOICE;
            }

            //if there is an invoice for the reason/proforma, return false
            $query = 'SELECT frr.parent_id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                     '  ON frr.parent_id = fer.id AND frr.parent_model_name = "Finance_Expenses_Reason" AND fer.type = ' . $type . ' AND fer.annulled_by = 0' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . ' AND frr.link_to_model_name = "Finance_Expenses_Reason"';
            $invoice_id = $db->GetOne($query);
            if (!empty($invoice_id)) {
                return false;
            }

            //check if we have invoice directly issued from the parent reason
            if ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                $this->getRelatives(array('get_parent_reasons' => true));
                if ($reason = $this->get('parent_reasons')) {
                    $reason = array_shift($reason);
                    $this->unsetProperty('parent_reasons', true);
                    if (!$reason->checkAddingInvoice()) {
                        return false;
                    }
                }
            } else {
                //check if we have proforma with invoice issued
                $query = 'SELECT frr.parent_id' . "\n" .
                        'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                        'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                        '  ON frr.parent_id = fer.id AND frr.parent_model_name = "Finance_Expenses_Reason" AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE . "\n" .
                        'WHERE frr.link_to = ' . $this->get('id') . ' AND frr.link_to_model_name = "Finance_Expenses_Reason"';
                $invoice_id = $db->GetOne($query);

                if ($invoice_id) {
                    $query = 'SELECT frr.parent_id' . "\n" .
                            'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                            'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                            '  ON frr.parent_id = fer.id AND frr.parent_model_name = "Finance_Expenses_Reason" AND fer.type = ' . PH_FINANCE_TYPE_EXPENSES_INVOICE . ' AND fer.annulled_by = 0' . "\n" .
                            'WHERE frr.link_to = ' . $invoice_id . ' AND frr.link_to_model_name = "Finance_Expenses_Reason"';
                    $invoice_id = $db->GetOne($query);
                    if (!empty($invoice_id)) {
                        return false;
                    }
                }
            }
        } else {
            return false;
        }
        return true;
    }

    /**
     * Gets container currency, conversion rate beween currencies of model and container,
     * calculates converted amount and sets them all to model.
     * Conversion rate is used when issuing bank payment from financial document and
     * bank account is in different currency than document.
     * Petty cash payments do not use conversion rate.
     *
     * @param mixed $companies_data - array of all options for Cashbox/Bank account field available to current user OR
     *                                false when no validation should be done if container is available to current user
     */
    public function prepareContainerRate($companies_data) {
        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // currency of model
        $currency = $this->get('currency');
        if (!$currency && $this->isDefined('grouping_table_2')) {
            $gt2 = $this->get('grouping_table_2');
            if (!empty($gt2['plain_values']['currency'])) {
                $currency = $gt2['plain_values']['currency'];
            }
            unset($gt2);
        }
        if (!$currency) {
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
            $currency = Finance_Currencies::getMain($this->registry);
        }

        // currency of container
        $container_currency = $currency;
        // conversion rate from currency of financial document to currency of container
        $container_rate = 1;
        // suggested amount for payment converted into currency of container
        $container_amount = round($this->get('total_with_vat') - $this->get('paid_amount'), 2);

        if ($this->isDefined('company_data') && is_array($companies_data)) {
            // check if 'company_data' of model is among available options
            $option_found = false;
            foreach ($companies_data as $c => $c_data) {
                foreach ($c_data as $idx => $data) {
                    if ($data['option_value'] == $this->get('company_data')) {
                        $option_found = true;
                    }
                }
            }
            if (!$option_found) {
                $company_data = '0_0_0_0';
                // if there is only one available option, set it to model
                if (count($companies_data) == 1 && count(reset($companies_data)) == 1) {
                    $only_option = reset($companies_data);
                    $only_option = reset($only_option);
                    $company_data = $only_option['option_value'];
                }
                $this->set('company_data', $company_data, true);
            }
        }

        // get payment_type and container_id of financial document
        $payment_type = '';
        $container_id = 0;
        $matches = array();
        if ($this->isDefined('company_data') && preg_match('#^\d+_\d+_(cash|bank)_(\d+)$#', $this->get('company_data'), $matches)) {
            $payment_type = $matches[1];
            $container_id = $matches[2];
        } else {
            $payment_type = $this->get('payment_type');
            $container_id = $this->get('container_id');
        }
        if ($payment_type == 'bank' && $container_id > 0) {

            require_once PH_MODULES_DIR . 'finance/models/finance.bank_accounts.model.php';
            $bank_account = new Finance_Bank_Account($this->registry, array('id' => $container_id));
            $container_currency = $bank_account->getCurrency();
            unset($bank_account);

            if ($container_currency && $currency != $container_currency) {
                // get conversion rate and calculate converted suggested amount
                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $container_rate = round(Finance_Currencies::getRate($this->registry, $currency, $container_currency), 6);
                $container_amount = round($container_amount * $container_rate, 2);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        // set properties to model
        $this->set('currency', $currency, true);
        $this->set('container_currency', $container_currency, true);
        $this->set('container_rate', sprintf('%.6f', $container_rate), true);
        $this->set('container_amount', sprintf('%.2f', $container_amount), true);

        return true;
    }

    /**
     * Function to get the full paid amount, including direct payments and all of its related finance documents
     *
     * @param bool $force - defines if the function will return the value, if property exists in the model (on FALSE), or will retake it again (on TRUE)
     * @return float - full paid amount
     */
    public function getFullPaidAmount($force = false) {
        if ($this->get('full_paid_amount') && !$force) {
            return $this->get('full_paid_amount');
        }

        $direct_paid_amount = $this->getSignedPaidAmount();

        //check the type of the current incomes reason
        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            // if it is custom user defined type (type id > 100) this amount is returned
            $this->set('full_paid_amount', $direct_paid_amount, true);
            return $this->get('full_paid_amount');
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }

        // paid amount to invoices + proformas
        $sql_invoiced_amount = 'SELECT SUM(paid_amount) as amount' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
            '  ON (frr.link_to_model_name ="Finance_Expenses_Reason" AND frr.link_to=\'' . $this->get('id') . '\'' . "\n" .
            '    AND frr.parent_model_name="Finance_Expenses_Reason" AND frr.parent_id=fer.id)' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
            '  ON (fr.parent_id=fer.id AND fr.parent_model_name="Finance_Expenses_Reason")' . "\n" .
            'WHERE fer.type IN (\'' . PH_FINANCE_TYPE_EXPENSES_INVOICE . '\', \'' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE . '\')' . "\n" .
            '  AND fer.annulled_by=0 AND fer.status="finished"';
        $invoiced_amount = $this->registry['db']->GetOne($sql_invoiced_amount);

        // paid amount to debit notices
        $sql_debit_amount = 'SELECT SUM(paid_amount) as amount' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer ' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
            '  ON (frr.link_to=\'' . $this->get('id') . '\' AND frr.link_to_model_name="Finance_Expenses_Reason" AND frr.parent_model_name="Finance_Expenses_Reason")' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
            '  ON (frr2.parent_id=fer.id AND frr2.parent_model_name="Finance_Expenses_Reason" AND frr2.link_to_model_name="Finance_Expenses_Reason" AND frr2.link_to=frr.parent_id)' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
            '  ON (fr.parent_id=frr2.parent_id AND fr.parent_model_name="Finance_Expenses_Reason")' . "\n" .
            'WHERE fer.type=\'' . PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE . '\' AND fer.annulled_by=0 AND fer.status="finished"';
        $debit_amount = $this->registry['db']->GetOne($sql_debit_amount);

        // paid amount to credit notices
        $sql_credit_amount = 'SELECT SUM(paid_amount) as amount' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
            '  ON (frr.link_to=' . $this->get('id') . ' AND frr.link_to_model_name="Finance_Expenses_Reason" AND frr.parent_model_name="Finance_Expenses_Reason")' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr2' . "\n" .
            '  ON (frr2.parent_id=fer.id AND frr2.parent_model_name="Finance_Expenses_Reason" AND frr2.link_to_model_name="Finance_Expenses_Reason"  AND frr.parent_id=frr2.link_to)' . "\n" .
            'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fr' . "\n" .
            '  ON (fr.paid_to=frr2.parent_id AND fr.paid_to_model_name ="Finance_Expenses_Reason")' . "\n" .
            'WHERE fer.type=\'' . PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE . '\' AND fer.annulled_by=0 AND fer.status="finished"';
        $credit_amount = $this->registry['db']->GetOne($sql_credit_amount);

        $full_paid_amount = $direct_paid_amount + $invoiced_amount + $debit_amount - $credit_amount;


        $this->set('full_paid_amount', $full_paid_amount, true);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $this->get('full_paid_amount');
    }

    /**
     * Get paid amount for this financial document and set it as a property to model.
     * Amount is positive for all types but for credit notes.
     *
     * @param bool $force - if true, force getting value from database even when property is set to model
     * @return float - result of the operation
     */
    public function getSignedPaidAmount($force = false) {
        if (!$this->isDefined('paid_amount') || $force) {
            $this->getPaidAmount();
        }
        $this->set('signed_paid_amount', ($this->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE ? -1 : 1) * $this->get('paid_amount'), true);

        return $this->get('signed_paid_amount');
    }

    /**
     * Get total amount of all invoices and debit/credit notes related to this expense reason
     *
     * @param bool $force - if true, force getting value from database even when property is set to model
     * @return float - result of the operation
     */
    public function getInvoicedAmount($force = false) {

        if ($this->isDefined('invoices_amount') && !$force) {
            return $this->get('invoices_amount');
        }

        $unsanitize = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $unsanitize = true;
        }
        //get the total of ALL invoices related to this expense reason
        $query = 'SELECT SUM(total_with_vat) FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                 ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr ' . "\n" .
                 ' WHERE frr.link_to=' . $this->get('id') . "\n" .
                 ' AND frr.link_to_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr.parent_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr.parent_id=fer.id AND fer.type =' . PH_FINANCE_TYPE_EXPENSES_INVOICE . "\n" .
                 ' AND fer.annulled_by=0 AND fer.status="finished"';

        $total_invoices = (double)$this->registry['db']->GetOne($query);

        if (!$total_invoices && $this->get('type') > PH_FINANCE_TYPE_MAX) {
            $query = 'SELECT SUM(fer2.total_with_vat) FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                     ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr ' . "\n" .
                     ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr2 ' . "\n" .
                     ', ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as fer2 ' . "\n" .
                     ' WHERE frr.link_to=' . $this->get('id') . "\n" .
                     ' AND frr.link_to_model_name="' . $this->modelName . '"' . "\n" .
                     ' AND frr.parent_model_name="' . $this->modelName . '"' . "\n" .
                     ' AND frr.parent_id=fer.id AND frr2.parent_model_name="' . $this->modelName . '"' . "\n" .
                     ' AND fer.type in (' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE . ')' . "\n" .
                     ' AND frr2.link_to=frr.parent_id AND frr2.link_to_model_name="' . $this->modelName . '"' . "\n" .
                     ' AND fer.annulled_by=0 AND fer.status="finished"' . "\n" .
                     ' AND frr2.parent_id=fer2.id AND fer2.status="finished" AND fer2.annulled_by=0 AND fer2.type="' . PH_FINANCE_TYPE_EXPENSES_INVOICE . '"';
            $total_invoices = (double)$this->registry['db']->GetOne($query);
        }

        //get the total of ALL debit/credit notes related to the invoices of this expense reason
        $notices_list = array(PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE);
        $query = 'SELECT SUM(total_with_vat) FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                 ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr ' . "\n" .
                 ', ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr2 ' . "\n" .
                 ' WHERE frr.link_to=' . $this->get('id') . "\n" .
                 ' AND frr.link_to_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr.parent_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND frr2.parent_id=fer.id AND frr2.parent_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND fer.type in (' . implode(',', $notices_list) . ')' . "\n" .
                 ' AND frr2.link_to=frr.parent_id AND frr2.link_to_model_name="' . $this->modelName . '"' . "\n" .
                 ' AND fer.annulled_by=0 AND fer.status="finished"';
        $total_debit_credit = (double)$this->registry['db']->GetOne($query);

        $invoices_amount = $total_invoices + $total_debit_credit;

        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');
        $invoices_amount = round($invoices_amount, $gt2_total_precision);

        //assign the total of all invoices and debit/credit notes to the expense reason
        $this->set('invoices_amount', $invoices_amount, true);

        if ($unsanitize) {
            $this->sanitize();
        }

        return $invoices_amount;
    }

    /**
     * Checks if the model has any related documents (parents and children)
     *
     * @return boolean - true or false
     */
    public function checkForRelatives() {

        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $query = 'SELECT count(parent_id)' . "\n" .
                  'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                  'WHERE (link_to = ' . $this->get('id') . ' OR parent_id = ' . $this->get('id') . ")\n" .
                  '  AND link_to_model_name = "Finance_Expenses_Reason"' . "\n" .
                  '  AND parent_model_name LIKE "Finance_Expenses_Reason"';
        $this->hasRelatives = $this->registry['db']->GetOne($query);

        if (!empty($sanitize_after)) {
            $this->sanitize();
        }

        return $this->hasRelatives;
    }

    /**
     * Searches for paid parent reason of proforma or for paid child proforma of reason.
     * If found, its paid amount is set as a property of current model.
     * Method is used to verify there are no payments to other model than the one an invoice is issued form.
     * Method is also used to verify total amount of reason is not overpaid.
     *
     * @return mixed - related paid model if found, otherwise false
     */
    public function getPaidRelative() {

        $other_paid_doc = false;

        if ($this->get('type') > PH_FINANCE_TYPE_MAX || $this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
            // check for a directly paid document, other than current model

            // use filtering to get only what looking for
            $other_paid_filters = array('fer.annulled_by = 0',
                                        'fer.status = "finished"',
                                        'fer.payment_status IN ("paid", "partial")');
            $other_paid_prop = $this->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE ?
                               'parent_reasons' : 'pro_invoices';

            // search for parent reason of proforma or search for child proforma of reason
            $this->getRelatives(array('get_' . $other_paid_prop => $other_paid_filters));
            $other_paid_doc = $this->get($other_paid_prop);
            if (!empty($other_paid_doc)) {
                $other_paid_doc = array_shift($other_paid_doc);
                // only if there are direct payments to this relative
                if ($other_paid_doc->getPaidAmount() > 0) {
                    // set paid amount to relative as a property of current model
                    $this->set('relative_paid_amount', $other_paid_doc->get('paid_amount'), true);
                } else {
                    $other_paid_doc = false;
                }
            }
            $this->unsetProperty($other_paid_prop, true);
        }

        return $other_paid_doc;
    }

    /**
     * Checks if amount of expense can be allocated as costs to inventory
     * (commodities) in other expense documents - used for "allocate_costs" action
     *
     * @return boolean - result of check
     */
    public function checkAllocateCosts() {
        return
            $this->get('status') == 'finished' &&
            $this->get('payment_status') != 'nopay' &&
            !$this->get('annulled_by') &&
            $this->isActivated() &&
            in_array($this->get('allocated_status'), array('enabled', 'allocated')) &&
            !in_array($this->get('type'), array(PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE, PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON)) &&
            $this->checkContainsNonCommodities();
    }

    /**
     * Checks if allocation of expense to deliveries can be undone
     *
     * @return boolean - result of check
     */
    public function checkDeleteAllocateCosts() {
        if ($this->get('allocated_status') == 'allocated') {
            $sanitize_after = false;
            if ($this->isSanitized()) {
                $sanitize_after = $this->unsanitize();
            }
            $result = true;
            $db = &$this->registry['db'];

            $old_allocated_rows = $db->GetAssoc("
                SELECT gt2ac.id AS idx, npud.id AS nom_price_update_id, npud.parent_id AS article_id
                FROM " . DB_TABLE_GT2_ALLOCATED_COSTS . " AS gt2ac
                LEFT JOIN " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npu
                  ON gt2ac.id = npu.allocated_cost_id AND npu.update_type = 'allocated_cost'
                LEFT JOIN " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npud
                  ON npu.row_id = npud.row_id AND npud.update_type = 'delivery'
                WHERE gt2ac.expense_model_id = '{$this->get('id')}'
                GROUP BY gt2ac.id
                ORDER BY npud.id ASC
            ");

            // check if earliest found delivery for a given commodity is the last one for it
            // check will always fail when expense is allocated to multiple deliveries for the same commodity
            $max_ids = array();
            foreach ($old_allocated_rows as $row) {
                if (!array_key_exists($row['article_id'], $max_ids)) {
                    $max_ids[$row['article_id']] = sprintf('parent_id = %d AND id > %d', $row['article_id'], $row['nom_price_update_id']);
                }
            }

            // undo will be possible when no next deliveries are found
            $next_delivery_rows = array();
            if ($max_ids) {
                $next_delivery_rows = $db->GetAll("
                    SELECT npud.*
                    FROM " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npud
                    WHERE update_type = 'delivery'
                      AND allocated_cost_id = 0
                      AND (" . implode(" OR ", $max_ids) . ")
                ");
            }
            if ($next_delivery_rows) {
                $result = false;
            }

            if ($sanitize_after) {
                $this->sanitize();
            }
            return $result;
        }
        return false;
    }

    /**
     * Checks if allocated status of expense can be manually changed (toggled
     * between enabled/disabled) - used for "allocated_status" checkbox
     *
     * @return boolean - result of check
     */
    public function checkUpdateAllocatedStatus() {
        return
            // exclude types
            !in_array(
                $this->get('type'),
                array(PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE, PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON)
            ) &&
            // exclude secondary expense invoice (created from expense reason)
            !(
                $this->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE &&
                $this->get('parent_type') > PH_FINANCE_TYPE_MAX &&
                !in_array($this->get('new_type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))
            ) &&
            (
                // existing model
                $this->get('id') &&
                $this->get('payment_status') != 'nopay' &&
                !$this->get('annulled_by') &&
                $this->isActivated() &&
                !in_array($this->get('allocated_status'), array('allocated')) &&
                (
                    // check article subtype only when finished and not editable
                    $this->get('status') != 'finished' ||
                    $this->checkContainsNonCommodities()
                ) ||
                // add
                !$this->get('id') ||
                // add credit/debit
                $this->get('id') == $this->get('invoice_id') &&
                in_array($this->get('new_type'), array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))
            );
    }

    /**
     * Checks if GT2 of model contains any non-commodity articles
     *
     * @return boolean - result of check
     */
    public function checkContainsNonCommodities() {

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $sanitize_after = $this->unsanitize();
        }

        // check for non-commodities
        $article_ids = array();

        if ($this->get('gt2_requested') && is_array($this->get('article_id')) && !$this->get('table_values_are_set')) {
            // if validation is performed before GT2 values are set into GT2
            // variable and model is built from submitted data in Request
            $article_ids = $this->get('article_id');
        } elseif (is_array($this->get('grouping_table_2')) && array_key_exists('values', $this->get('grouping_table_2'))) {
            $table = $this->get('grouping_table_2');
            foreach ($table['values'] as $row) {
                if (!empty($row['article_id'])) {
                    $article_ids[] = $row['article_id'];
                }
            }
        } else {
            $article_ids = $this->registry['db']->GetCol("
                SELECT gt2.article_id
                FROM " . DB_TABLE_GT2_DETAILS . " gt2
                WHERE gt2.model = 'Finance_Expenses_Reason' AND gt2.model_id = '{$this->get('id')}'
            ");
        }
        $article_ids = array_filter(array_unique($article_ids));
        if ($article_ids) {
            $article_ids = $this->registry['db']->GetCol("
                SELECT n.id
                FROM " . DB_TABLE_NOMENCLATURES . " AS n
                WHERE n.id IN ('" . implode("', '", $article_ids) . "') AND n.subtype != 'commodity'
            ");
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return !empty($article_ids);
    }

    /**
     * Checks if current expense record has allocated cost from other expenses
     * to its commodities
     *
     * @return boolean - result of check
     */
    public function hasAllocatedCosts() {
        $sanitize_after = false;
        if ($this->isSanitized()) {
            $sanitize_after = $this->unsanitize();
        }

        $has_allocated = $this->registry['db']->GetOne("
            SELECT 1 FROM " . DB_TABLE_GT2_ALLOCATED_COSTS . " WHERE model_id = '{$this->get('id')}'
        ");

        if ($sanitize_after) {
            $this->sanitize();
        }

        return !!$has_allocated;
    }

    /**
     * Gets amount to allocate from current expense (subtotals of non-commodities)
     *
     * @return float - amount to allocate
     */
    public function getExpenseAmount() {
        if ($this->isDefined('expense_amount')) {
            return $this->get('expense_amount');
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $sanitize_after = $this->unsanitize();
        }
        $gov = $this->registry->get('get_old_vars');
        if (!$gov) {
            $this->registry->set('get_old_vars', true, true);
        }
        $registry = &$this->registry;

        list($values) = $this->getGT2Values(array());
        $values = array_filter($values);
        $article_ids = array_filter(array_unique(array_map(function($a) { return $a['article_id']; }, $values)));
        if ($article_ids) {
            $filters = array(
                'where' => array(
                    "n.id IN ('" . implode("', '", $article_ids) . "')",
                    "n.subtype != 'commodity'",
                ),
            );
            $article_ids = Nomenclatures::getIds($registry, $filters);
        }

        $expense_amount = 0;
        $var_name = $this->get('admit_VAT_credit') ? 'subtotal_with_discount' : 'subtotal_with_vat_with_discount';
        foreach ($values as $row) {
            if (!empty($row['article_id']) && in_array($row['article_id'], $article_ids)) {
                $expense_amount += $row[$var_name];
            }
        }
        $prec = $this->registry['config']->getParam('precision', 'gt2_total');

        $this->set('expense_amount', round($expense_amount, $prec), true);

        if (!$gov) {
            $this->registry->remove('get_old_vars');
        }
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->get('expense_amount');
    }

    /**
     * Saves allocation data, applies price updates to commodity nomenclatures
     * from selected GT2 rows of deliveries
     *
     * @return boolean - result of the operations
     */
    public function setAllocatedCosts() {
        $model_id_param = 'delivery';
        $model_ids = $this->get($model_id_param) ?: array();
        $amounts = $this->get('allocated_amount') ?: array();
        $expense_model_id = $this->get('id');
        $user_id = $this->registry['currentUser'] ? $this->registry['currentUser']->get('id') : PH_AUTOMATION_USER;
        $prec = $this->registry['config']->getParam('precision', 'gt2_total');
        /** @var ADODB_mysqli $db */
        $db = &$this->registry['db'];

        $allocated_rows = $row_ids = array();
        foreach ($model_ids as $model_id) {
            if (array_key_exists($model_id, $amounts)) {
                foreach ($amounts[$model_id] as $row_id => $amount) {
                    // floatval, round, > 0, prec
                    $amount = round(floatval($amount), $prec);
                    if (bccomp($amount, 0, $prec) != 0) {
                        $row_ids[] = $row_id;
                        $allocated_rows[$row_id] = array('model_id' => $model_id, 'amount' => $amount);
                    }
                }
            }
        }

        // get saved data for nomenclature price update based on deliveries -
        // each GT2 row should have exactly one matching row
        $delivery_nom_price_updates = array();
        if ($this->registry['config']->getParam('nomenclatures', 'update_delivery_prices_on_handover')) {
            //get the relations between the expense document and the handover
            $query = "
            SELECT rows_links
            FROM " . DB_TABLE_FINANCE_REASONS_RELATIVES . " AS frr
            WHERE frr.parent_model_name = 'Finance_Warehouses_Document'
              AND frr.link_to_model_name = 'Finance_Expenses_Reason'
              AND frr.link_to  IN ('" . implode("', '", $model_ids) . "')
            ORDER BY frr.parent_id DESC";
            $rowLinks = $db->GetCol($query);
            $rowLinksMap = array();
            foreach($rowLinks as $rl) {
                $rlines = preg_split('#(\r\n|\n)#', $rl);
                foreach($rlines as $rline) {
                    list($handoverRowId, $expenseRowId) = preg_split('#\s*=>\s*#', $rline);
                    $rowLinksMap[$expenseRowId] = $handoverRowId;
                }
            }

            //the price updates upon handover are saved with handover gt2 row ids
            //get the corresponding handover ids
            $query = "
            SELECT npu.row_id AS idx, npu.*
            FROM " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npu
            WHERE npu.update_type = 'delivery'
              AND npu.allocated_cost_id = 0
              AND npu.row_id IN ('" . implode("', '", $rowLinksMap) . "')
            ORDER BY npu.id ASC";
            $handover_nom_price_updates = $db->GetAssoc($query);
            foreach($row_ids as $expenseRowId) {
                if (!array_key_exists($expenseRowId, $rowLinksMap)) {
                    continue;
                }
                $handoverRowId = $rowLinksMap[$expenseRowId];
                if (array_key_exists($handoverRowId, $handover_nom_price_updates)) {
                    $delivery_nom_price_updates[$expenseRowId] = $handover_nom_price_updates[$handoverRowId];
                }
            }
        } else {
            $query = "
            SELECT npu.row_id AS idx, npu.*
            FROM " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npu
            WHERE npu.update_type = 'delivery'
              AND npu.allocated_cost_id = 0
              AND npu.row_id IN ('" . implode("', '", $row_ids) . "')
            ORDER BY npu.id ASC";
            $delivery_nom_price_updates = $db->GetAssoc($query);
        }

        // if missing log data for any GT2 row
        if (count($delivery_nom_price_updates) != count($row_ids) && $allocated_rows) {
            $this->registry['messages']->setError($this->i18n('errors_nom_price_updates_invalid'));
            return false;
        }

        // articles to update
        $article_ids = array();
        foreach ($delivery_nom_price_updates as $row) {
            if (!in_array($row['parent_id'], $article_ids)) {
                $article_ids[] = $row['parent_id'];
            }
        }

        $db->StartTrans();

        $allocated_status = 'enabled';
        // define the minimal record id from nom_price_updates table to perform price recalculation from
        $min_id = 0;

        // it is expected that current operation will require only insertion or only deletion, but process both

        // search for previous allocated rows from this expense
        $old_allocated_rows = $db->GetAssoc("
            SELECT gt2ac.id AS idx, npud.id AS nom_price_update_id, npud.parent_id AS article_id
            FROM " . DB_TABLE_GT2_ALLOCATED_COSTS . " AS gt2ac
            LEFT JOIN " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npu
              ON gt2ac.id = npu.allocated_cost_id AND npu.update_type = 'allocated_cost'
            LEFT JOIN " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npud
              ON npu.row_id = npud.row_id AND npud.update_type = 'delivery'
            WHERE gt2ac.expense_model_id = '{$expense_model_id}'
            GROUP BY gt2ac.id
        ");
        if ($old_allocated_rows) {
            // delete old allocated rows data
            $db->Execute("
                DELETE gt2ac.*, npu.*
                FROM " . DB_TABLE_GT2_ALLOCATED_COSTS . " AS gt2ac
                LEFT JOIN " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " AS npu
                  ON gt2ac.id = npu.allocated_cost_id AND npu.update_type = 'allocated_cost'
                WHERE gt2ac.id IN ('" . implode("', '", array_keys($old_allocated_rows)) . "')
            ");

            foreach ($old_allocated_rows as $row) {
                if (!empty($row['article_id']) && !in_array($row['article_id'], $article_ids)) {
                    $article_ids[] = $row['article_id'];
                }
                $row['nom_price_update_id'] = intval($row['nom_price_update_id']);
                if (!$min_id || $min_id > $row['nom_price_update_id']) {
                    $min_id = $row['nom_price_update_id'];
                }
            }
        }

        // insert new allocated rows data
        if ($allocated_rows) {
            $allocated_status = 'allocated';

            if ($delivery_nom_price_updates) {
                $delivery_min_id = min(array_map(function($a) { return intval($a['id']); }, $delivery_nom_price_updates));
                if ($min_id == 0 || $min_id > $delivery_min_id) {
                    $min_id = $delivery_min_id;
                }
            }

            // insert new data - one by one
            foreach ($allocated_rows as $row_id => $arow) {
                $db->Execute("
                    INSERT INTO " . DB_TABLE_GT2_ALLOCATED_COSTS . " (parent_id, model_id, expense_model_id, allocated_amount)
                    VALUES " . sprintf('(%d, %d, %d, %.6f)', $row_id, $arow['model_id'], $expense_model_id, $arow['amount'])
                );

                if ($aid = $db->Insert_Id()) {
                    // calculate the differential increase/decrease that allocated cost applies to the last delivery price specified in the delivery
                    $delivery_price = 0;
                    // logged increase/decrease should be converted from
                    // expense_currency into currency set in the delivery row
                    $rate = 1;
                    if ($this->get('expense_currency') != $delivery_nom_price_updates[$row_id]['currency']) {
                        $rate = Finance_Currencies::getRate($this->registry, $this->get('expense_currency'), $delivery_nom_price_updates[$row_id]['currency']);
                    }
                    // save in AWDP currency, without rounding
                    if (bccomp($delivery_nom_price_updates[$row_id]['delivery_quantity'], 0, $prec) == 1) {
                        $delivery_price = round($arow['amount'] * $rate / $delivery_nom_price_updates[$row_id]['delivery_quantity'], 6);
                    }

                    $query = "
                        INSERT INTO " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . "
                        SET parent_id = '{$delivery_nom_price_updates[$row_id]['parent_id']}',
                            currency = '{$delivery_nom_price_updates[$row_id]['currency']}',
                            delivery_price = '{$delivery_price}',
                            update_type = 'allocated_cost',
                            row_id = '{$delivery_nom_price_updates[$row_id]['row_id']}',
                            allocated_cost_id = '{$aid}',
                            modified = NOW(),
                            modified_by = '{$user_id}'
                        ";
                    $db->Execute($query);
                } else {
                    $db->FailTrans();
                }
            }
        }

        $db->Execute("
            UPDATE " . DB_TABLE_FINANCE_EXPENSES_REASONS . "
            SET allocated_status = '{$allocated_status}'
            WHERE id = '{$expense_model_id}'
        ");

        $articles = Nomenclatures::search(
            $this->registry,
            array(
                'where' => array(
                    "n.id IN ('" . implode("', '", $article_ids) . "')",
                    "n.deleted_by IS NOT NULL",
                ),
                'sanitize' => true,
                'model_lang' => $this->get('model_lang'),
            )
        );

        $articles_assoc = array();
        $article_names = array_fill_keys($article_ids, '');
        foreach ($articles as $nom) {
            $articles_assoc[$nom->get('id')] = $nom;
            $article_names[$nom->get('id')] = $nom->get('name');
        }
        unset($articles);

        $next_nom_price_updates = $db->GetAll("
            SELECT *
            FROM " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . "
            WHERE parent_id IN ('" . implode("', '", $article_ids) . "') AND id >= '{$min_id}'
            ORDER BY id ASC
        ");
        $nom_timelines = array_fill_keys($article_ids, array());

        // now we need to arrange the rows in the order that they should be applied
        foreach ($next_nom_price_updates as $row) {
            if ($row['update_type'] != 'allocated_cost') {
                $nom_timelines[$row['parent_id']][$row['row_id']] = $row + array(
                    'new_delivery_price' => $row['delivery_price'],
                    //'allocated_cost' => array(),
                );
            } elseif (array_key_exists($row['row_id'], $nom_timelines[$row['parent_id']])) {
                //$nom_timelines[$row['parent_id']][$row['row_id']]['allocated_cost'][] = $row;
                $nom_timelines[$row['parent_id']][$row['row_id']]['new_delivery_price'] += $row['delivery_price'];
            }
        }

        // precision and formatting of AWDP calculations
        $awdp_prec = $this->registry['config']->getParam('precision', 'nom_average_weighted_delivery_price');
        $awdp_prec_format = "%.{$awdp_prec}f";

        foreach ($nom_timelines as $article_id => $nom_data) {
            // now run the calculation algorithm
            $awdp = null;
            foreach ($nom_data as $delivery_data) {
                if ($awdp === null) {
                    // initial AWDP to start calculation with
                    $awdp = $delivery_data['old_price'];
                }

                // calculated price from previous iteration is different and current db row should be updated
                // we will apply changes from allocated cost to previous deliveries but not to the current one
                if (bccomp($awdp, $delivery_data['old_price'], $awdp_prec) != 0) {

                    $old_price = sprintf($awdp_prec_format, $awdp);
                    $new_price = 0;
                    if ($delivery_data['old_quantity'] + $delivery_data['delivery_quantity'] != 0) {
                        $new_price = $awdp * $delivery_data['old_quantity'] + $delivery_data['delivery_price'] * $delivery_data['delivery_quantity'];
                        $new_price = $new_price / ($delivery_data['old_quantity'] + $delivery_data['delivery_quantity']);
                    }
                    $new_price = sprintf($awdp_prec_format, $new_price);

                    // new and old price values are formatted according to
                    // precision setting because they correspond to the value
                    // in nomenclature in a given moment
                    $db->Execute("
                         UPDATE " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . "
                         SET old_price = '{$old_price}',
                             new_price = '{$new_price}'
                         WHERE id = '{$delivery_data['id']}'
                    ");
                }

                if ($delivery_data['update_type'] == 'user') {
                    // add notification that final AWDP price of article will not be changed
                    $this->registry['messages']->setWarning($this->i18n('warning_average_weighted_delivery_price_modified_by_user', array($article_names[$article_id])));
                    // we should not change next rows
                    break;
                }

                // now calculate AWDP to pass to next iteration with applied changes from allocated cost to current delivery
                if ($delivery_data['old_quantity'] + $delivery_data['delivery_quantity'] != 0) {
                    $awdp = $awdp * $delivery_data['old_quantity'] + $delivery_data['new_delivery_price'] * $delivery_data['delivery_quantity'];
                    $awdp = $awdp / ($delivery_data['old_quantity'] + $delivery_data['delivery_quantity']);
                } else {
                    $awdp = 0;
                }
                if (bccomp($awdp, 0, $awdp_prec) == -1) {
                    // price has become negative - fail action and display error
                    $this->registry['messages']->setError($this->i18n('error_average_weighted_delivery_price_negative', array($article_names[$article_id])));
                    $db->FailTrans();
                    break;
                }
            }
            // do not save data in nomenclature
            if ($db->HasFailedTrans()) {
                continue;
            }

            // set final AWDP price into nomenclature
            $awdp = sprintf($awdp_prec_format, $awdp);
            $db->Execute("
                UPDATE " . DB_TABLE_NOMENCLATURES . "
                SET average_weighted_delivery_price = '{$awdp}',
                    modified = NOW(),
                    modified_by = '{$user_id}'
                WHERE id = '{$article_id}'
            ");

            if (!empty($articles_assoc[$article_id])) {
                $old_nomenclature = clone $articles_assoc[$article_id];
                $articles_assoc[$article_id]->set('average_weighted_delivery_price', $awdp, true);
                $new_nomenclature = $articles_assoc[$article_id];

                // write history into nomenclature for prices update
                Nomenclatures_History::saveData(
                    $this->registry,
                    array(
                        'action_type' => 'edit',
                        'model' => $new_nomenclature,
                        'new_model' => $new_nomenclature,
                        'old_model' => $old_nomenclature,
                    )
                );
            }
        }

        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();
        return $result;
    }

    /**
     * Prepares allocated costs from saved data in db or from submitted data
     *
     * @return Finance_Expenses_Reason[] - delivery models with set allocated data
     */
    public function getAllocatedCosts() {
        if ($this->isSanitized()) {
            $sanitize_after = $this->unsanitize();
        }
        $registry = &$this->registry;
        $db = &$this->registry['db'];

        $model_id_param = 'delivery';
        if ($this->get($model_id_param) && is_array($this->get($model_id_param))) {
            $allocated_rows = array();
            $selected = $this->get('selected') ?: array();
            $allocated_amount = $this->get('allocated_amount') ?: array();
            foreach ($selected as $model_id => $model_data) {
                foreach ($model_data as $row_id => $selected_value) {
                    $allocated_rows[$row_id] = array(
                        'model_id' => $model_id,
                        'allocated_amount' => floatval(isset($allocated_amount[$model_id][$row_id]) ? $allocated_amount[$model_id][$row_id] : ''),
                        'selected' => !empty($selected_value) ? ' ' : '',
                    );
                }
            }
            $model_ids = $this->get($model_id_param);
        } else {
            $prec = $this->registry['config']->getParam('precision', 'gt2_total');
            $allocated_rows = $db->GetAssoc("
                SELECT parent_id AS idx, model_id, ROUND(allocated_amount, {$prec}) AS allocated_amount
                FROM " . DB_TABLE_GT2_ALLOCATED_COSTS . "
                WHERE expense_model_id = '{$this->get('id')}'
                ORDER BY id ASC
            ");
            $model_ids = array_unique(array_map(function($a) { return $a['model_id']; }, $allocated_rows));
        }
        /** @var Finance_Expenses_Reason[] $models */
        $models = Finance_Expenses_Reasons::search(
            $registry,
            array(
                'where' => array(
                    "fer.id IN ('" . implode("', '", $model_ids) . "')",
                ),
                'sort' => array(
                    "FIND_IN_SET(fer.id, '" . implode(',', $model_ids) . "')",
                ),
                'sanitize' => true,
            )
        );

        $gov = $this->registry->get('get_old_vars');
        if (!$gov) {
            $this->registry->set('get_old_vars', true, true);
        }
        $total_allocated_amount = 0;
        $allocated_data = array();
        foreach ($models as $model) {
            $model->set('expense_currency', $this->get('currency'), true);
            $model->set('expense_amount', $this->getExpenseAmount(), true);
            $model->set('expense_type', $this->get('type'), true);

            $rate = 1;
            if ($model->get('expense_currency') && $model->get('expense_currency') != $model->get('currency')) {
                $rate = Finance_Currencies::getRate($registry, $model->get('currency'), $model->get('expense_currency'));
            }
            $source_amount_field = $model->get('admit_VAT_credit') ? 'subtotal_with_discount' : 'subtotal_with_vat_with_discount';

            $gt2 = $model->prepareDeliveryGT2($model->getGT2Vars());
            if ($this->get('allocated_status') == 'allocated') {
                unset($gt2['vars']['selected']);
            }
            $gt2['values'] = array_intersect_key($gt2['values'], $allocated_rows);
            $gt2['rows'] = array_keys($gt2['values']);
            foreach ($gt2['values'] as &$row) {
                $row['subtotal_amount'] = sprintf('%.6f', round($row[$source_amount_field] * $rate, 6));
                $row = $allocated_rows[$row['id']] + $row;
                $total_allocated_amount += $allocated_rows[$row['id']]['allocated_amount'];
            }
            unset($row);
            $model->set('grouping_table_2', $gt2, true);
            $model->set('value_autocomplete', sprintf('[%s/%s] %s', $model->get('invoice_num'), General::strftime('%d.%m.%Y', $model->get('issue_date')), $model->get('customer_name')), true);
            $allocated_data[$model->get('id')] = $model;
        }
        $this->set('total_allocated_amount', $total_allocated_amount, true);

        if (!$gov) {
            $this->registry->remove('get_old_vars');
        }
        if (!empty($sanitize_after)) {
            $this->sanitize();
        }

        $this->set('allocated_costs', $allocated_data, true);

        return $allocated_data;
    }

    /**
     * Processes GT2 variables (not the data) of a delivery expense for display
     * in allocate_costs action of other (non-delivery) expense
     *
     * @param array $gt2 - GT2 table of delivery expense
     * @return array - processed GT2 table
     */
    public function prepareDeliveryGT2(array $gt2) {
        $model_id = $this->get('id');
        $gt2['width'] = '99%';
        $gt2['hide_delete'] = '1';
        $gt2['last_visible_column'] = 'none';
        $gt2['last_editable_row_index'] = count($gt2['values']) + count($gt2['plain_values']);
        $gt2['grouping'] = uniqid();
        foreach ($gt2['vars'] as $var_name => &$var) {
            if (preg_match('#^(article_(name|id|measure_name)|subtotal)|price|quantity|value|percentage#', $var_name)) {
                $var['readonly'] = '1';
                $var['disabled'] = '1';
                if (!empty($var['custom_class']) && preg_match('#\bcopy_values\b#', $var['custom_class'])) {
                    $var['custom_class'] = preg_replace('#\bcopy_values\b#', '', $var['custom_class']);
                }
                unset($var['js_methods']);
                unset($var['agregate']);
                $var['name'] .= "[$model_id]";
            } else {
                unset($gt2['vars'][$var_name]);
            }
        }
        unset($var);
        foreach ($gt2['plain_vars'] as $var_name => &$var) {
            $var['readonly'] = '1';
            $var['disabled'] = '1';
            $var['hidden'] = '1';
        }
        unset($var);

        $weight_amount_field = 'subtotal_amount';
        $gt2['vars'][$weight_amount_field] = array(
            'name' => "{$weight_amount_field}[{$model_id}]",
            'hidden' => '1',
            'readonly' => '1',
            'width' => '100',
            'custom_class' => $weight_amount_field,
            'label' => sprintf('%s %s', $this->i18n('finance_payments_amount'), $this->get('expense_currency')),
        ) + $gt2['vars']['price'];

        $allocated_amount_field = 'allocated_amount';
        $gt2['vars'][$allocated_amount_field] = array(
            'name' => "{$allocated_amount_field}[{$model_id}]",
            'hidden' => '0',
            'readonly' => '0',
            'disabled' => '0',
            'width' => '100',
            'js_methods' => array(
                'onkeyup' => 'expensesAllocateCalc(this);',
            ),
            // check if the amount to be allocated from the opposite expense is positive or negative
            'js_filter' =>
                $this->get('expense_amount') >= 0 ?
                'insertOnlyFloats' :
                'insertOnlyNegativeFloats',
            'custom_class' => $allocated_amount_field,
            'label' => $this->i18n('finance_payments_distributed_amount'),
        ) + $gt2['vars'][$weight_amount_field];

        $selected_field = 'selected';
        $gt2['vars'][$selected_field] = array(
            'name' => "{$selected_field}[{$model_id}]",
            'width' => '15',
            'js_methods' => array(
                'onload' => "toggleExpenseRow(this);",
                'onclick' => "toggleExpenseRow(this);",
                'ondrop' => 'return false;',
                'oncontextmenu' => 'return false;',
            ),
            // custom filtering function on keypress
            'js_filter' => 'toggleTextInputSelected',
            'custom_class' => "$selected_field checkall",
            'text_align' => 'center',
            'label' => ' ',
            'description' => ' ',
        ) + $gt2['vars'][$allocated_amount_field];

        return $gt2;
    }
}

?>
