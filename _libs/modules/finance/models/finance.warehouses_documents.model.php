<?php

use Nzoom\Mvc\ModelTrait\BelongsToTrait;
use Nzoom\PermissionsChecker;

/**
 * Finance_Warehouses_Document model class
 */
Class Finance_Warehouses_Document extends Finance_Document {
    use BelongsToTrait;

    public $modelName = 'Finance_Warehouses_Document';

    public $counter;

    public $checkPermissionsByStatus = true;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('num', 'date', 'customer_name', 'name', 'company_name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->get('id') && $registry->get('getAssignments')) {
            $this->getAssignments('responsible');
            $this->getAssignments('decision');
            $this->getAssignments('observer');
            $this->getAssignments();
        }

        // Check for warehouse_data (company office warehouse)
        $matches = array();
        if ($this->isDefined('warehouse_data')) {
            if (preg_match('#^(\d+)_(\d+)_(\d+)$#', $this->get('warehouse_data'), $matches)) {
                $this->set('company', $matches[1], true);
                $this->set('office', $matches[2], true);
                $this->set('warehouse', $matches[3], true);
            } else {
                $this->set('company', 0, true);
                $this->set('office', 0, true);
                $this->set('warehouse', 0, true);
            }
        } elseif ($this->get('company') && $this->get('office') && $this->get('warehouse')) {
            $this->set('warehouse_data', $this->get('company') . '_' . $this->get('office') . '_' . $this->get('warehouse'), true);
        }

        // Check for to_warehouse_data (company office warehouse)
        if ($this->isDefined('to_warehouse_data')) {
            if (preg_match('#^(\d+)_(\d+)_(\d+)$#', $this->get('to_warehouse_data'), $matches)) {
                $this->set('to_company', $matches[1], true);
                $this->set('to_office', $matches[2], true);
                $this->set('to_warehouse', $matches[3], true);
            } else {
                $this->set('to_company', 0, true);
                $this->set('to_office', 0, true);
                $this->set('to_warehouse', 0, true);
            }
        } elseif ($this->get('to_company') && $this->get('to_office') && $this->get('to_warehouse')) {
            $this->set('to_warehouse_data', $this->get('to_company') . '_' . $this->get('to_office') . '_' . $this->get('to_warehouse'), true);
        }

        if ($this->get('type') && !$this->get('type_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N .
                     ' WHERE parent_id=' . $this->get('type') . ' AND lang="' . $registry['lang'] . '"';
            $name = $registry['db']->GetOne($query);
            $this->set('type_name', $name, true);
        }

        if ($this->get('company') && !$this->get('company_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_COMPANIES_I18N .
                     ' WHERE parent_id=' . $this->get('company') . ' AND lang="' . $this->get('model_lang') . '"';
            $name = $registry['db']->GetOne($query);
            $this->set('company_name', $name, true);
        }

        if ($this->get('office') && !$this->get('office_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_OFFICES_I18N .
                     ' WHERE parent_id=' . $this->get('office') . ' AND lang="' . $this->get('model_lang') . '"';
            $name = $registry['db']->GetOne($query);
            $this->set('office_name', $name, true);
        }

        if ($this->get('warehouse') && !$this->get('warehouse_name')) {
            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_WAREHOUSES_I18N .
                     ' WHERE parent_id=' . $this->get('warehouse') . ' AND lang="' . $this->get('model_lang') . '"';
            $name = $registry['db']->GetOne($query);
            $this->set('warehouse_name', $name, true);
        }

        //prepare employees for inspections or waste
        if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION || $this->get('type') == PH_FINANCE_TYPE_WASTE) {
            $this->prepareEmployees();
        }
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - action to validate for
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {

        if ((!$this->get('company') || !$this->get('office') || !$this->get('warehouse')) && $action != 'translate') {
            $this->raiseError('error_finance_warehouses_documents_no_warehouse_specified', 'warehouse_data', null,
                              array($this->getLayoutName('warehouse_data')));
        }

        //check counter
        if ($this->get('company') && $this->get('type') && !$this->getCounter()) {
            $this->raiseError('error_finance_warehouses_documents_no_counter', 'type', 0,
                              array($this->get('type_name'), $this->get('company_name'), $this->get('office_name')));
        }

        //specific validation for different types
        switch ($this->get('type')) {
            case PH_FINANCE_TYPE_HANDOVER:
                if ($this->registry['action'] != 'translate') {
                    $requiredFields = $this->getRequiredFields();
                    $error_prefix = $error_suffix = '';
                    if (preg_match('#^(ajax_)?addhandover$#', $this->registry['action'])) {
                        $error_suffix = '_' . $this->get('warehouse');
                    } elseif (preg_match('#^(add|edit)$#', $this->registry['action']) && $this->get('direction') == 'incoming') {
                        $error_prefix = 'to_';
                    }
                    if (in_array('from', $requiredFields) && !$this->get('from')) {
                        $this->raiseError('error_handover_no_from_specified_' . $this->get('direction'), $error_prefix . 'from' . $error_suffix, 0, $this->get('warehouse_name'));
                    }
                    if (in_array('to', $requiredFields) && !$this->get('to')) {
                        $this->raiseError('error_handover_no_to_specified_' . $this->get('direction'), $error_prefix . 'to' . $error_suffix, 0, $this->get('warehouse_name'));
                    }
                    if (!$this->get('date') || $this->get('date') < General::strftime('%Y-%m-%d')) {
                        $this->raiseError('error_handover_no_date_specified', $error_prefix . 'date' . $error_suffix, 0, $this->get('warehouse_name'));
                    }
                    if (preg_match('#^(ajax_)?addhandover$#', $this->registry['action'])) {
                        if (in_array('location', $requiredFields) && !$this->get('location')) {
                            $this->raiseError('error_handover_no_location_specified', $error_prefix . 'location' . $error_suffix, 0, $this->get('warehouse_name'));
                        }
                        if (!$this->get('customer')) {
                            $this->raiseError('error_finance_warehouses_documents_no_customer_specified', 'customer', null,
                                              array($this->getLayoutName('customer', false)));
                        }
                        if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
                            $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
                        }
                    }
                }
                break;
            case PH_FINANCE_TYPE_COMMODITIES_RESERVATION:
                if ($action != 'translate') {
                    if (!$this->get('date') || $this->get('date') < General::strftime('%Y-%m-%d')) {
                        $this->raiseError('error_reservation_no_date_specified', 'date_' . $this->get('warehouse'), 0,
                                          array($this->getLayoutName('date'), $this->get('warehouse_name')));
                    }
                    if (!$this->get('customer')) {
                        $this->raiseError('error_finance_warehouses_documents_no_customer_specified', 'customer', null,
                                          array($this->getLayoutName('customer', false)));
                    }
                    if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
                        $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
                    }
                }
                break;
            case PH_FINANCE_TYPE_WASTE:
            case PH_FINANCE_TYPE_INSPECTION:
                if (!$this->get('date')) {
                    $this->raiseError('error_finance_warehouses_documents_no_date_specified', 'date', 0,
                                      array($this->getLayoutName('date'), $this->getModelTypeName()));
                }
                break;
            case PH_FINANCE_TYPE_COMMODITIES_TRANSFER:
                if ($action != 'translate') {
                    if (!$this->get('edit_locked')) {
                        if (!$this->get('from')) {
                            $this->raiseError('error_handover_no_from_specified', 'from', 0, $this->get('warehouse_name'));
                        }
                        if (!$this->get('to')) {
                            $this->raiseError('error_handover_no_to_specified', 'to', 0, $this->get('warehouse_name'));
                        }
                        if (!$this->get('date') || $this->get('date') < General::strftime('%Y-%m-%d')) {
                            $this->raiseError('error_handover_no_date_specified', 'date', 0, $this->get('warehouse_name'));
                        }
                    }
                    $to_warehouse_name = $this->get('to_warehouse_name') ?: $this->getLayoutName('to_warehouse_data');
                    if ($this->get('status') == 'finished' || $this->get('finance_after_action') == 'finish') {
                        if (!$this->get('to_company') || !$this->get('to_office') || !$this->get('to_warehouse')) {
                            $this->raiseError('error_finance_warehouses_documents_no_warehouse_specified', 'to_warehouse_data', null,
                                              array($this->getLayoutName('to_warehouse_data')));
                        }
                        if (!$this->get('to_from')) {
                            $this->raiseError('error_handover_no_from_specified', 'to_from', 0, $to_warehouse_name);
                        }
                        if (!$this->get('to_to')) {
                            $this->raiseError('error_handover_no_to_specified', 'to_to', 0, $to_warehouse_name);
                        }
                        if (!$this->get('to_date') || $this->get('to_date') < General::strftime('%Y-%m-%d')) {
                            $this->raiseError('error_handover_no_date_specified', 'to_date', 0, $to_warehouse_name);
                        }
                    } else {
                        if ($this->get('to_date') && $this->get('to_date') < General::strftime('%Y-%m-%d')) {
                            $this->raiseError('error_handover_no_date_specified', 'to_date', 0, $to_warehouse_name);
                        }
                    }
                }
                break;
        }
        if (in_array($this->get('type'), array(PH_FINANCE_TYPE_COMMODITIES_TRANSFER, PH_FINANCE_TYPE_WASTE))) {
            //validate requested articles against available articles
            $quantity = $this->registry['request']->isRequested('quantity') ? $this->registry['request']->get('quantity') : array();
            $articles = $this->registry['request']->isRequested('article_id') ? $this->registry['request']->get('article_id') : array();
            $article_names = $this->registry['request']->get('article_name');
            $deleted = $this->registry['request']->get('deleted');
            require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
            $warehouse = Finance_Warehouses::searchOne($this->registry, array('where' => array('fwh.id = ' . $this->get('warehouse'))));
            $available_articles = $warehouse->getAvailableQuantity(array('nom_id' => $articles));
            $row = 0;
            foreach ($quantity as $key => $val) {
                $row++;
                if (floatval($val) > 0 && empty($deleted[$key]) && !empty($articles[$key])) {
                    //check if we have these quantities in the warehouse
                    if (empty($available_articles[$articles[$key]]) ||
                    bccomp($val, $available_articles[$articles[$key]]['quantity'], $this->registry['config']->getParam('precision', 'gt2_quantity')) == 1) {
                        $this->raiseError('error_warehouse_quantity', 'quantity', 0,
                            array($article_names[$key], $this->i18n('finance_warehouses_documents_article'), $row));
                    }
                }
            }
        }

        return $this->valid;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        //prepare batches data from the request
        if ($this->registry['request']->isPost() && !$this->get('batches_prepared') && $this->registry['action'] != 'translate' && !$this->get('edit_locked')) {
            if (!$this->prepareRequestedBatches()) {
                return false;
            }
        }
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();

            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();
                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
            } else {
                $this->counter->increment();
            }
        }

        //query to insert the main table
        $query1 = 'INSERT ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "\n" .
                  'SET ' . implode(",\n", $set);

        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError("add new finance's warehouse document base details", $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //edit 2nd type grouping table data
        if (!$this->registry->get('no_GT2')) {
            //$this->registry->set('calculate_GT2', true, true);
            $this->saveGT2Vars(false, true);
            if ($this->get('status') && $this->get('status') != 'opened') {
                $this->setAvailableQuantities();
            }
        }

        switch ($this->get('type')) {
            case PH_FINANCE_TYPE_HANDOVER:
                //update nomenclatures prices (required: before quantities update)
                $this->updateNomPrices();
                //update quantity in the warehouse
                $this->updateWarehouseQuantity();
                $this->updateCommoditiesReservations();
                break;
            case PH_FINANCE_TYPE_WASTE:
                if ($this->get('status') == 'finished') {
                    //update quantity in the warehouse
                    $this->updateWarehouseQuantity();
                }
                break;
            case PH_FINANCE_TYPE_INSPECTION:
                // lock the warehouse for the inspection

                $db->Execute('UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES . ' SET locked = ' . $this->get('id') . ' WHERE id = ' . $this->get('warehouse'));
                if ($db->ErrorMsg()) {
                    $db->FailTrans();
                }
                if ($this->get('status') == 'locked') {
                    if (!$this->_addDifferenceDocuments()) {
                        $db->FailTrans();
                    }
                }
                break;
            case PH_FINANCE_TYPE_COMMODITIES_TRANSFER:
                if ($this->get('status') != 'opened') {
                    if (!$this->_addTransfersHandovers()) {
                        $db->FailTrans();
                    }
                }
                break;
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            // add or update relations to parent document
            $this->updateReasonsRelatives();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if ($this->get('finance_after_action') != '') {
            if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
                $this->set('status', 'locked', true);
            } elseif ($this->get('type') == PH_FINANCE_TYPE_COMMODITIES_TRANSFER) {
                if ($this->get('finance_after_action') == 'locked') {
                    $this->set('status', 'locked', true);
                } elseif ($this->get('finance_after_action') == 'finish') {
                    $this->set('status', 'finished', true);
                }
            } elseif ($this->get('type') == PH_FINANCE_TYPE_COMMODITIES_RESERVATION) {
                if ($this->get('finance_after_action') == 'locked') {
                    $this->set('status', 'locked', true);
                }
            } else {
                $this->set('status', 'finished', true);
            }
        }

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //get current model status
        $query = 'SELECT status FROM ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' WHERE id = ' . $this->get('id');
        $old_status = $db->GetOne($query);

        if (!$this->get('num') && $this->get('status') == 'finished') {
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if (!$this->counter) {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_no_counter',
                                                      array($this->get('type_name'), $this->get('company_name'), $this->get('office_name'))));
                return false;
            } else {
                $this->counter->increment();
            }
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //edit 2nd type grouping table data
        if (!$this->registry->get('no_GT2') && !$this->get('edit_locked')) {
            $this->saveGT2Vars(false, true);
            if ($this->get('status') && $this->get('status') != 'opened' && $old_status == 'opened') {
                $this->setAvailableQuantities();
            }
        }

        switch ($this->get('type')) {
            case PH_FINANCE_TYPE_HANDOVER:
                //update quantity in the warehouse
                $this->updateWarehouseQuantity();
                break;
            case PH_FINANCE_TYPE_WASTE:
                if ($this->get('status') == 'finished') {
                    //update quantity in the warehouse
                    $this->updateWarehouseQuantity();
                }
                break;
            case PH_FINANCE_TYPE_COMMODITIES_RESERVATION:
                if ($this->get('status') == 'finished') {
                    if (!$this->releaseCommodities()) {
                        $db->FailTrans();
                        $this->registry['messages']->setError(
                            $this->registry['translater']->translate('error_finance_warehouses_documents_finish_reservation'));
                    }
                }
                break;
            case PH_FINANCE_TYPE_INSPECTION:
                // lock the warehouse for the inspection
                // it must be locked in the add action....but just to be sure.....
                $db->Execute('UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES . ' SET locked = ' . $this->get('id') . ' WHERE id = ' . $this->get('warehouse'));
                if ($db->ErrorMsg()) {
                    $db->FailTrans();
                }
                if ($this->get('status') == 'locked') {
                    if (!$this->_addDifferenceDocuments()) {
                        $db->FailTrans();
                    }
                }
                break;
            case PH_FINANCE_TYPE_COMMODITIES_TRANSFER:
                if ($this->get('status') != 'opened') {
                    if (!$this->_addTransfersHandovers()) {
                        $db->FailTrans();
                    }
                }
                break;
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if ($this->get('type') <= PH_FINANCE_TYPE_MAX) {
            // add or update relations to parent document
            $this->updateReasonsRelatives();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Creates incoming and/or outgoing handover records for commodities transfer
     * depending on its status and already created handovers.
     *
     * @return boolean - if handovers were created successfully or not
     */
    private function _addTransfersHandovers() {

        if (!$this->get('id')) {
            return false;
        } elseif ($this->get('status') == 'opened') {
            return true;
        }
        $request = &$this->registry['request'];
        $error = false;

        // get saved model of commodities transfer
        $model = Finance_Warehouses_Documents::searchOne($this->registry,
                array('where' => array('fwd.id = ' . $this->get('id'))));
        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $model->getGT2Vars();
        $model->getBatchesData();

        // get handover type name
        $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                 'WHERE parent_id = ' . PH_FINANCE_TYPE_HANDOVER . ' AND lang = "' . $model->get('model_lang') . '"';
        $type_name = $this->registry['db']->GetOne($query);

        $query = 'SELECT DISTINCT(fwd.id), fwd.direction' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                 'INNER JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd ' . "\n" .
                 '  ON fwd.id=frr.parent_id AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER . "\n" .
                 'WHERE frr.link_to = \'' . $this->get('id') . '\'' . "\n" .
                 '  AND frr.link_to_model_name = "Finance_Warehouses_Document"' . "\n" .
                 '  AND frr.parent_model_name="Finance_Warehouses_Document"';
        $handovers = $this->registry['db']->GetAssoc($query);

        $empty = new Finance_Warehouses_Document($this->registry,
                                                 array('type' => PH_FINANCE_TYPE_HANDOVER,
                                                       'company' => $model->get('company')));
        $empty->getGT2Vars();

        // properties that should be removed from handovers before save
        $unset_props = array(
            'id', 'num', 'to_warehouse_data',
            'to_company', 'to_company_name', 'to_office', 'to_office_name',
            'to_warehouse', 'to_warehouse_name', 'to_date',
            'to_from', 'to_from_name', 'to_to', 'to_to_name', 'to_description'
        );

        //create outgoing handover record
        if (!in_array('outgoing', $handovers) && $this->get('status') != 'opened') {
            //always work with saved model for compatibility
            $handover = clone $model;

            //prepare gt2 of handover
            $table = $handover->get('grouping_table_2');
            $htable = $empty->get('grouping_table_2');
            $htable['values'] = $table['values'];
            $htable['plain_values'] = $table['plain_values'];
            $htable['process_batches'] = true;
            $handover->set('grouping_table_2', $htable, true);
            $handover->calculateGT2();
            $handover->set('table_values_are_set', true, true);
            $handover->set('batches_prepared', true, true);
            if (!$handover->get('from')) {
                $handover->set('from', $handover->get('from_name'), true);
            }
            if (!$handover->get('to')) {
                $handover->set('to', $handover->get('to_name'), true);
            }

            foreach ($unset_props as $prop) {
                $handover->unsetProperty($prop, true);
            }
            $handover->set('type', PH_FINANCE_TYPE_HANDOVER, true);
            $handover->set('type_name', $type_name, true);
            $handover->set('status', 'finished', true);
            // set name to an empty string (it will be set automatically before save using type name and parent name)
            $handover->set('parent_name', $this->get('name'), true);
            $handover->set('name', '', true);
            // set values for group and department
            $handover->setGroup($handover->get('group'));
            $handover->setDepartment($handover->get('department'));
            $handover->set('direction', 'outgoing', true);
            $handover->set('link_to', $this->get('id'));
            $handover->set('link_to_model_name', 'Finance_Warehouses_Document');

            //unset counter of model so we can get next number for each added handover
            unset($handover->counter);

            if ($handover->save()) {
                $filters = array('where' => array('fwd.id = ' . $handover->get('id')));
                $new_handover = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                $new_handover->getGT2Vars();

                $new_id = $new_handover->get('id');
                $new_handover->set('id', $this->get('id'), true);
                $new_handover->set('handover_id', $new_id, true);
                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                        array('action_type' => 'addhandover',
                              'new_model' => $new_handover,
                              'old_model' => $model
                        ));

                $new_handover->set('id', $new_id, true);
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                        array('action_type' => 'add',
                              'new_model' => $new_handover,
                              'old_model' => $empty
                        ));
            } else {
                $error = true;
            }
        }

        //create incoming handover record
        if (!$error && $model->get('status') == 'finished' && !in_array('incoming', $handovers)) {

            //always work with saved model for compatibility
            $handover = clone $model;

            //prepare gt2 of handover
            $table = $handover->get('grouping_table_2');
            $htable = $empty->get('grouping_table_2');
            $htable['values'] = $table['values'];
            $htable['plain_values'] = $table['plain_values'];
            $htable['process_batches'] = true;
            $handover->set('grouping_table_2', $htable, true);
            $handover->calculateGT2();
            $handover->set('table_values_are_set', true, true);
            $handover->set('batches_prepared', true, true);

            foreach ($unset_props as $prop) {
                // set destination company, office, warehouse, date, from, to, description
                // from commodities transfer into incoming handover
                if (preg_match('#^to_((company|office|warehouse|from|to)(_name)?|warehouse_data|date|description)$#', $prop, $matches)) {
                    $handover->set($matches[1], $handover->get($prop), true);
                }
                $handover->unsetProperty($prop, true);
            }
            if (!$handover->get('from')) {
                $handover->set('from', $handover->get('from_name'), true);
            }
            if (!$handover->get('to')) {
                $handover->set('to', $handover->get('to_name'), true);
            }
            $handover->set('type', PH_FINANCE_TYPE_HANDOVER, true);
            $handover->set('type_name', $type_name, true);
            $handover->set('status', 'finished', true);
            // set name to an empty string (it will be set automatically before save using type name and parent name)
            $handover->set('parent_name', $this->get('name'), true);
            $handover->set('name', '', true);
            // set values for group and department
            $handover->setGroup($handover->get('group'));
            $handover->setDepartment($handover->get('department'));
            $handover->set('direction', 'incoming', true);
            $handover->set('link_to', $this->get('id'));
            $handover->set('link_to_model_name', 'Finance_Warehouses_Document');

            //unset counter of model so we can get next number for each added handover
            unset($handover->counter);

            if ($handover->save()) {
                $filters = array('where' => array('fwd.id = ' . $handover->get('id')));
                $new_handover = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                $new_handover->getGT2Vars();

                $new_id = $new_handover->get('id');
                $new_handover->set('id', $this->get('id'), true);
                $new_handover->set('handover_id', $new_id, true);
                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                        array('action_type' => 'addhandover',
                              'new_model' => $new_handover,
                              'old_model' => $model
                        ));

                $new_handover->set('id', $new_id, true);
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                        array('action_type' => 'add',
                              'new_model' => $new_handover,
                              'old_model' => $empty
                        ));
            } else {
                $error = true;
            }
        }
        $this->registry->set('get_old_vars', $gov, true);

        return !$error;
    }

    /**
     * Adds difference documents after inspection is finished
     * (i.e. set in 'locked' status)
     *
     * @return boolean - result of the operation
     */
    private function _addDifferenceDocuments() {
        $request = &$this->registry['request'];

        //prepare the quantities of the missing and surplus documents
        //the rows with quantities 0 are NOT SAVED!!!
        $articles_ids = $request->get('article_id');
        $difference = $request->get('difference');
        $quantity = $request->get('quantity');
        $current_quantity = $request->get('current_quantity');
        $deleted = $request->get('deleted');
        $quantities_missing = $quantities_surplus = $batch_missing = $batch_surplus = $new_codes = array();

        $prec = $this->registry['config']->getSectionParams('precision');

        //get warehouse and its quantities
        $warehouse = $this->get('warehouse');
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
        $warehouse = Finance_Warehouses::searchOne($this->registry, array('where' => array('fwh.id = ' . $warehouse)));
        $qParams = array(
            'nom_id' => $articles_ids,
            'force' => true,
            'reservation' => 'none',
            'get_zeros' => true
        );
        $available_articles = $warehouse->getAvailableQuantity($qParams);
        $unique = array();
        foreach ($available_articles as $k => $v) {
            if (!empty($v['batch_data'])) {
                foreach ($v['batch_data'] as $b => $bd) {
                    $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'nomenclature_id' => $k,
                            'batch_id'        => $bd['batch'],
                            'expire_date'     => $bd['expire'] ? : '0000-00-00',
                            'serial_number'   => $bd['serial'],
                            'cstm_number'     => $bd['custom'],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                            'currency'        => $bd['currency'],
                        ]
                    );
                    $unique[$uk] = $bd['quantity'];
                }
            }
        }

        //get articles
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        $models = Nomenclatures::search($this->registry, array('where' => array('n.id IN (\'' . implode('\', \'', $articles_ids) . '\')')));
        $articles = array();
        foreach ($models as $k => $v) {
            $articles[$v->get('id')] = $v;
        }
        foreach ($difference as $row_idx => $diff) {
            // row without article
            if (empty($articles_ids[$row_idx])) {
                continue;
            }

            //process batches
            if ($articles[$articles_ids[$row_idx]]->get('has_batch')) {
                //get batches details if present
                $batch_warehouses = $request->get('article_' . $row_idx . '_wh') ?: array();
                $batch_expire = $request->get('article_' . $row_idx . '_expire') ? : array();
                $batch_quantities = $request->get('article_' . $row_idx . '_quantity');
                $batch_serials = $request->get('article_' . $row_idx . '_serial') ? : array();
                $batch_custom = $request->get('article_' . $row_idx . '_custom');
                $new_codes[$row_idx] = $batch_codes = $request->get('article_' . $row_idx . '_batch') ? : array();
                $batch_prices = $request->get('article_' . $row_idx . '_delivery_price') ? : array();
                $batch_currencies = $request->get('article_' . $row_idx . '_currency') ? : array();
                $batch_isCustom = $request->get('article_' . $row_idx . '_batch_isCustom') ? : array();
                foreach ($batch_warehouses as $b => $wh) {
                    $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'nomenclature_id' => $articles_ids[$row_idx],
                            'batch_id'        => isset($batch_codes[$b]) ? $batch_codes[$b] : '',
                            'expire_date'     => isset($batch_expire[$b]) ? $batch_expire[$b] : '0000-00-00',
                            'serial_number'   => isset($batch_serials[$b]) ? $batch_serials[$b] : '',
                            'cstm_number'     => isset($batch_custom[$b]) ? $batch_custom[$b] : '',
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", isset($batch_prices[$b]) ? round($batch_prices[$b], $prec['gt2_rows']) : 0),
                            'currency'        => isset($batch_currencies[$b]) ? $batch_currencies[$b] : $request->get('currency'),
                        ]
                    );
                    if (isset($unique[$uk])) {
                        //this batch is present in the warehouse
                        if (!$batch_quantities[$b]) {
                            $batch_quantities[$b] = 0;
                        }
                        $q = $batch_quantities[$b] - $unique[$uk];
                        if ($q > 0) {
                            //put quantity in the surplus array
                            $batch_surplus[$row_idx][$b] = abs($q);
                            if (!isset($quantities_surplus[$row_idx])) {
                                $quantities_surplus[$row_idx] = 0;
                            }
                            $quantities_surplus[$row_idx] += abs($q);
                        } elseif ($q < 0) {
                            //put quantity in the missing array
                            $batch_missing[$row_idx][$b] = abs($q);
                            if (!isset($quantities_missing[$row_idx])) {
                                $quantities_missing[$row_idx] = 0;
                            }
                            $quantities_missing[$row_idx] += abs($q);
                        }
                    } else {
                        //brand new quantity
                        $batch_surplus[$row_idx][$b] = $batch_quantities[$b];
                        if (!isset($quantities_surplus[$row_idx])) {
                            $quantities_surplus[$row_idx] = 0;
                        }
                        $quantities_surplus[$row_idx] += $batch_quantities[$b];
                    }

                    //change custom batches with their IDs as they have already been saved in the inspection
                    if (!empty($batch_isCustom[$b])) {
                        //custom batch entered by hand
                        $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_BATCHES . ' WHERE code = "' . General::slashesEscape($batch_codes[$b]) . '"';
                        $new_codes[$row_idx][$b] = $this->registry['db']->GetOne($query);
                    } elseif (empty($batch_codes[$b])) {
                        //custom batch - not filled in
                        //we will do something more complicated here
                        //get batch id form the row of the inspection
                        $query = 'SELECT gtb.batch_id FROM ' . DB_TABLE_GT2_DETAILS . ' gt2' . "\n" .
                                 'JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' gtb' . "\n" .
                                 '  ON gt2.id = gtb.parent_id' . "\n" .
                                 '    AND gtb.quantity = ' . (!empty($batch_quantities[$b]) ? $batch_quantities[$b] : 0) . "\n" .
                                 '    AND gtb.serial_number = "' . General::slashesEscape(!empty($batch_serials[$b]) ? $batch_serials[$b] : '') . '"'. "\n" .
                                 '    AND gtb.expire_date = "' . (!empty($batch_expire[$b]) ? $batch_expire[$b] : '0000-00-00') . '"' . "\n" .
                                 '    AND gtb.cstm_number = "' . General::slashesEscape(!empty($batch_custom[$b]) ? $batch_custom[$b] : '') . '"'. "\n" .
                                 '    AND gtb.delivery_price = ' . (!empty($batch_prices[$b]) ? $batch_prices[$b] : 0) . "\n" .
                                 'WHERE gt2.model = "Finance_Warehouses_Document" AND gt2.model_id = ' . $this->get('id') . "\n" .
                                 '  AND gt2.article_id = ' . $articles_ids[$row_idx];
                        $new_codes[$row_idx][$b] = $this->registry['db']->GetOne($query);
                    }
                }
            } else {
                if ($deleted[$row_idx]) {
                    continue;
                }
                if (isset($quantity[$row_idx]) && $quantity[$row_idx] === '' && isset($available_articles[$articles_ids[$row_idx]]['quantity'])) {
                    // quantity field has not been filled so difference field has no value or its value is out of date;
                    // get difference from the available quantity
                    $diff = -$available_articles[$articles_ids[$row_idx]]['quantity'];
                }
                if ($diff > 0) {
                    //the difference is positive - that's a surplus
                    $quantities_surplus[$row_idx] = abs($diff); //not really smart, but keep it for consistency
                } elseif ($diff < 0) {
                    //the difference is negative - that's a miss
                    $quantities_missing[$row_idx] = abs($diff); //remove the negative sign the missing record should have positive quantities as well
                }
            }
        }

        //set deleted rows in both documents
        $missing_deleted = array_fill_keys(array_keys(array_diff_key($difference, $quantities_missing)), 1) + $deleted;
        $surplus_deleted = array_fill_keys(array_keys(array_diff_key($difference, $quantities_surplus)), 1) + $deleted;

        //create record of missing stock
        $error = false;
        if (!empty($quantities_missing)) {
            $missing = clone $this;
            $missing->unsetProperty('id', true);
            $missing->set('type', PH_FINANCE_TYPE_INSPECTION_MISSING, true);
            //get type name
            $type_name = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N .
                         ' WHERE parent_id = ' . PH_FINANCE_TYPE_INSPECTION_MISSING .
                         ' AND lang = "' . ($this->get('model_lang') ? : $this->registry['lang']) . '"';
            $type_name = $this->registry['db']->GetOne($type_name);
            $missing->set('type_name', $type_name, true);

            $request->set('deleted', $missing_deleted, 'post', true);
            $request->set('quantity', $quantities_missing, 'post', true);
            foreach ($quantities_missing as $k => $v) {
                //set batch quantities
                if (!empty($batch_missing[$k])) {
                    $request->set('article_' . $k . '_quantity', $batch_missing[$k], 'post', true);
                    $request->set('article_' . $k . '_batch', $new_codes[$k], 'post', true);
                    $request->set('article_' . $k . '_batch_isCustom', array(), 'post', true);
                }
            }
            $missing->set('name', $missing->get('type_name'), true);
            // set values for group and department
            $missing->setGroup($missing->get('group'));
            $missing->setDepartment($missing->get('department'));
            $missing->set('link_to', $this->get('id'), true);
            $missing->set('link_to_model_name', 'Finance_Warehouses_Document', true);
            $missing->set('status', 'locked', true);
            $missing->set('direction', 'outgoing', true);
            $missing->unsetProperty('finance_after_action', true);
            unset($missing->counter);
            $request->set('finance_after_action', '', 'post', true);
            $missing->set('table_values_are_set', false, true);
            $this->registry->set('get_old_vars', false, true);
            $missing->unsetProperty('grouping_table_2', true);
            if ($missing->save()) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_add_success',
                                                                    array($missing->getModelTypeName())));
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_missing_add_failed',
                                                                  array($missing->getModelTypeName())), '', -1);
                $error = true;
            }
        }

        //create record of surplus
        if (!empty($quantities_surplus)) {
            $surplus = clone $this;
            $surplus->unsetProperty('id', true);
            $surplus->set('type', PH_FINANCE_TYPE_INSPECTION_SURPLUS, true);

            //get type name
            $type_name = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N .
                         ' WHERE parent_id = ' . PH_FINANCE_TYPE_INSPECTION_SURPLUS .
                         ' AND lang = "' . ($this->get('model_lang') ? : $this->registry['lang']) . '"';
            $type_name = $this->registry['db']->GetOne($type_name);
            $surplus->set('type_name', $type_name, true);
            $request->set('deleted', $surplus_deleted, 'post', true);
            $request->set('quantity', $quantities_surplus, 'post', true);
            foreach ($quantities_surplus as $k => $v) {
                //set batch quantities
                if (!empty($batch_surplus[$k])) {
                    $request->set('article_' . $k . '_quantity', $batch_surplus[$k], 'post', true);
                    $request->set('article_' . $k . '_batch', $new_codes[$k], 'post', true);
                    $request->set('article_' . $k . '_batch_isCustom', array(), 'post', true);
                }
            }
            $surplus->set('name', $surplus->get('type_name'), true);
            // set values for group and department
            $surplus->setGroup($surplus->get('group'));
            $surplus->setDepartment($surplus->get('department'));
            $surplus->set('link_to', $this->get('id'), true);
            $surplus->set('link_to_model_name', 'Finance_Warehouses_Document', true);
            $surplus->set('status', 'locked', true);
            $surplus->set('direction', 'incoming', true);
            $surplus->unsetProperty('finance_after_action', true);
            unset($surplus->counter);
            $request->set('finance_after_action', '', 'post', true);
            $surplus->set('table_values_are_set', false, true);
            $this->registry->set('get_old_vars', false, true);
            $surplus->unsetProperty('grouping_table_2', true);
            if ($surplus->save()) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_add_success',
                                                                    array($surplus->getModelTypeName())));
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_surplus_add_failed',
                                                                  array($surplus->getModelTypeName())), '', -1);
                $error = true;
            }
        }

        //restore the status of the inspection document with FORCE!
        return !$error;
    }

    /**
     * Translated existing model
     *
     * @return bool - result of the operation
     */
    public function translate() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //save 2nd type grouping table data
        if (!$this->translateGT2Vars()) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            $update['name'] = sprintf("name='%s'", $this->get('name'));
        }
        if ($this->isDefined('description')) {
            $update['description'] = sprintf("description='%s'", $this->get('description'));
        }
        if ($this->isDefined('notes')) {
            $update['notes'] = sprintf("notes='%s'", $this->get('notes'));
        }
        if ($this->isDefined('location')) {
            $update['location'] = sprintf("location='%s'", $this->get('location'));
        }
        if ($this->isDefined('from')) {
            $update['from_name'] = sprintf("from_name='%s'", (is_string($this->get('from')) && !is_numeric($this->get('from')) ? $this->get('from') : ''));
        }
        if ($this->isDefined('to')) {
            $update['to_name'] = sprintf("to_name='%s'", (is_string($this->get('to')) && !is_numeric($this->get('to')) ? $this->get('to') : ''));
        }
        if ($this->isDefined('to_description')) {
            $update['to_description'] = sprintf("to_description='%s'", $this->get('to_description'));
        }
        if ($this->isDefined('to_from') && is_string($this->get('to_from')) && !is_numeric($this->get('to_from'))) {
            $update['to_from_name'] = sprintf("to_from_name='%s'", $this->get('to_from'));
        }
        if ($this->isDefined('to_to') && is_string($this->get('to_to')) && !is_numeric($this->get('to_to'))) {
            $update['to_to_name'] = sprintf("to_to_name='%s'", $this->get('to_to'));
        }
        if ($this->isDefined('total_no_vat_reason_text')) {
            $update['total_no_vat_reason_text'] = sprintf("`total_no_vat_reason_text`='%s'", $this->get('total_no_vat_reason_text'));
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('translating finance i18n details', $db, $query2);
            }
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        if (!$this->registry->get('no_GT2') && !$this->get('table_values_are_set') && !$this->get('edit_locked')) {
            //claculate totals
            $this->getGT2Vars();
            $this->calculateGT2();
            $this->set('table_values_are_set', true, true);
        }

        $prec = $this->registry['config']->getSectionParams('precision');
        $set = array();

        if ($this->isDefined('type')) {
            $set['type'] = sprintf("`type`='%d'", $this->get('type'));

            // automatically set name on add, if empty
            $this->setDefaultName();
        }

        if ($this->isDefined('company')) {
            $set['company'] = sprintf("`company`='%d'", $this->get('company'));
        }
        if ($this->isDefined('office')) {
            $set['office'] = sprintf("`office`='%d'", $this->get('office'));
        }
        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("`customer`='%d'", $this->get('customer'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark'] = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("project=%d", $this->get('project'));
        }
        if ($this->isDefined('phase')) {
            $set['phase'] = sprintf("phase=%d", $this->get('phase'));
        }
        if ($this->isDefined('currency')) {
            $set['currency'] = sprintf("`currency`='%s'", $this->get('currency'));
        }
        if ($this->isDefined('warehouse')) {
            $set['warehouse'] = sprintf("`warehouse`='%s'", $this->get('warehouse'));
        }
        if ($this->isDefined('direction')) {
            $set['direction'] = sprintf("`direction`='%s'", $this->get('direction'));
        }
        if ($this->isDefined('status') || $this->get('finance_after_action')) {
            if ($this->isDefined('status')) {
                $set['status'] = sprintf("`status`='%s'", $this->get('status'));
            }
            if ($this->get('finance_after_action') == 'finish') {
                $set['status'] = sprintf("`status`='%s'", 'finished');
                $this->set('status', 'finished', true);
            } elseif ($this->get('finance_after_action') == 'locked') {
                $set['status'] = sprintf("`status`='%s'", 'locked');
                $this->set('status', 'locked', true);
            }
            $set['status_modified'] = sprintf("status_modified=now()");
            $set['status_modified_by'] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
        }
        if ($this->isDefined('total_discount_surplus_field')) {
            $set['total_discount_surplus_field'] = sprintf("`total_discount_surplus_field`='%s'", $this->get('total_discount_surplus_field'));
        }
        if ($this->isDefined('total_discount_value')) {
            $set['total_discount_value'] = sprintf("`total_discount_value`='%.6f'", round($this->get('total_discount_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_discount_percentage')) {
            $set['total_discount_percentage'] = sprintf("`total_discount_percentage`='%.6f'", round($this->get('total_discount_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_value')) {
            $set['total_surplus_value'] = sprintf("`total_surplus_value`='%.6f'", round($this->get('total_surplus_value'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_surplus_percentage')) {
            $set['total_surplus_percentage'] = sprintf("`total_surplus_percentage`='%.6f'", round($this->get('total_surplus_percentage'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_without_discount')) {
            $set['total_without_discount'] = sprintf("`total_without_discount`='%.6f'", round($this->get('total_without_discount'), $prec['gt2_total']));
        }
        if ($this->isDefined('total')) {
            $set['total'] = sprintf("`total`='%.6f'", round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_vat_rate')) {
            $set['total_vat_rate'] = sprintf("`total_vat_rate`='%.2f'", round($this->get('total_vat_rate'), 2));
        }
        if ($this->isDefined('total_vat')) {
            $set['total_vat'] = sprintf("`total_vat`='%.6f'",
                        round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total']));
        }
        if ($this->isDefined('total_with_vat')) {
            $set['total_with_vat'] = sprintf("`total_with_vat`='%.6f'",
                        round($this->get('total') * $this->get('total_vat_rate') / 100, $prec['gt2_total']) + round($this->get('total'), $prec['gt2_total']));
        }
        if ($this->isDefined('total_no_vat_reason')) {
            $set['total_no_vat_reason'] = sprintf("`total_no_vat_reason`='%d'", $this->get('total_no_vat_reason'));
        }
        if ($this->isDefined('employees') && is_array($this->get('employees'))) {
            $set['employees'] = sprintf("`employees` = '%s'",
                                        (is_array($this->get('employees')) ?
                                        implode(', ', array_unique(array_diff($this->get('employees'), array('')))) :
                                        $this->get('employees'))
                                       );
        }
        if ($this->isDefined('date')) {
            $set['date'] = sprintf("`date`='%s'", $this->get('date'));
        }
        if ($this->isDefined('from')) {
            $set['from'] = sprintf("`from`='%d'", (is_numeric($this->get('from')) ? $this->get('from') : 0));
        }
        if ($this->isDefined('to')) {
            $set['to'] = sprintf("`to`='%d'", (is_numeric($this->get('to')) ? $this->get('to') : 0));
        }

        if ($this->isDefined('to_company')) {
            $set['to_company'] = sprintf("`to_company`='%d'", $this->get('to_company'));
        }
        if ($this->isDefined('to_office')) {
            $set['to_office'] = sprintf("`to_office`='%d'", $this->get('to_office'));
        }
        if ($this->isDefined('to_warehouse')) {
            $set['to_warehouse'] = sprintf("`to_warehouse`='%s'", $this->get('to_warehouse'));
        }
        if ($this->isDefined('to_date')) {
            $set['to_date'] = sprintf("`to_date`='%s'", $this->get('to_date'));
        }
        if ($this->isDefined('to_from')) {
            $set['to_from'] = sprintf("`to_from`='%d'", (is_numeric($this->get('to_from'))? $this->get('to_from') : 0));
        }
        if ($this->isDefined('to_to')) {
            $set['to_to'] = sprintf("`to_to`='%d'", (is_numeric($this->get('to_to')) ? $this->get('to_to') : 0));
        }

        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }

        $set['modified']       = sprintf("modified=now()");
        $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        return $set;
    }

    /**
     * Prepares default name of model before adding, if empty
     */
    private function setDefaultName() {
        if (!$this->get('id') && $this->isDefined('name') && !$this->get('name')) {
            $parent_name = '';
            if ($this->get('link_to')) {
                // derivative document of system type
                if ($this->isDefined('parent_name')) {
                    // parent name is set to model so just get it
                    $parent_name = $this->get('parent_name');
                } else {
                    if ($this->get('link_to_model_name') == 'Finance_Warehouses_Document') {
                        $parent_i18n_table = DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS_I18N;
                    } elseif ($this->get('link_to_model_name') == 'Finance_Expenses_Reason') {
                        $parent_i18n_table = DB_TABLE_FINANCE_EXPENSES_REASONS_I18N;
                    } elseif ($this->get('link_to_model_name') == 'Contract') {
                        $parent_i18n_table = DB_TABLE_CONTRACTS_I18N;
                    } elseif ($this->get('link_to_model_name') == 'Document') {
                        $parent_i18n_table = DB_TABLE_DOCUMENTS_I18N;
                    } else {
                        // default link is to an incomes reason
                        $parent_i18n_table = DB_TABLE_FINANCE_INCOMES_REASONS_I18N;
                    }
                    $query_parent_name = 'SELECT name FROM ' . $parent_i18n_table . "\n" .
                                         'WHERE parent_id=' . $this->get('link_to') . ' AND lang="' . $this->registry['lang'] . '"';
                    $parent_name = $this->registry['db']->GetOne($query_parent_name);
                }
            }

            // construct name (type name is used by default)
            $name = $this->get('type_name') .
                    ($parent_name ? sprintf(' %s %s', $this->i18n('for'), $parent_name) : '');

            $this->set('name', $name, true);
        }
    }

    /**
     * Method to define and set group of a new model (without id) based on
     * default group type setting<br />
     * IMPORTANT: Always pass $parent_group parameter when creating secondary documents.
     *
     * @param mixed $parent_group - group of direct parent or false if there is no parent
     * @param mixed $type_default_group - default group from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setGroup($parent_group = false, $type_default_group = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_group === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $group = '';
        if ($type_default_group !== false) {
            $group = $type_default_group;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_group' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $group = $this->registry['db']->GetOne($query);
        }
        // if setting is parent group
        if ($group == '[parent_group]') {
            // if value is passed for parent group
            if ($parent_group !== false) {
                $group = $parent_group;
            } else {
                // if there is no parent document,
                // group will be defined according to current user
                $group = '[default_user_group]';
            }
        }
        // get the default group of the user
        if ($group == '[default_user_group]') {
            if ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $group = $this->registry['originalUser']->get('default_group');
            } else {
                // get the default group id from the current user
                $group = $this->registry['currentUser']->get('default_group');
            }
            // set "All" if user has no default group
            if (!$group) {
                $group = PH_ROOT_GROUP;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('group', $group, true);
    }

    /**
     * Method to define and set department of a new model (without id) based on
     * default department type setting<br />
     * IMPORTANT: Always pass $parent_department parameter when creating secondary documents.
     *
     * @param mixed $parent_department - department of direct parent or false if there is no parent
     * @param mixed $type_default_department - default department from type (optional parameter)
     * @return boolean - result of the operation
     */
    public function setDepartment($parent_department = false, $type_default_department = false) {
        // if model has an id and is a primary document or has no type, do not continue
        if ($this->get('id') && $parent_department === false || !$this->get('type')) {
            return;
        }

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $department = '';
        if ($type_default_department !== false) {
            $department = $type_default_department;
        } else {
            // do not check for $modelName because sometimes it does not match
            // model name of created model (in addhandover for example)
            $query = 'SELECT default_department' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     'WHERE id=\'' . $this->get('type') . '\'';
            $department = $this->registry['db']->GetOne($query);
        }
        // if setting is parent department
        if ($department == '[parent_department]') {
            // if value is passed for parent department
            if ($parent_department !== false) {
                $department = $parent_department;
            } else {
                // if there is no parent document,
                // department will be defined according to current user
                $department = '[default_user_department]';
            }
        }
        // get the default department of the user
        if ($department == '[default_user_department]') {
            if ($this->registry->isRegistered('originalUser')) {
                // check if automation process is started
                // in this case the current user is stored in registry's originalUser
                $department = $this->registry['originalUser']->get('default_department');
            } else {
                // get the default department id from the current user
                $department = $this->registry['currentUser']->get('default_department');
            }
            // set "All" if user has no default department
            if (!$department) {
                $department = PH_DEPARTMENT_FIRST;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('department', $department, true);
    }

    /**
     * Sets new quantities in warehouse for a set of articles.
     *
     * @return bool - result of operation
     */
    public function setWarehouseQuantities() {

        $db = $this->registry['db'];

        $db->StartTrans();
        $gt2 = $this->get('grouping_table_2');

        $articles = array();
        foreach ($gt2['values'] as $row) {
            if (!empty($row['article_id'])) {
                $articles[] = $row['article_id'];
            }
        }

        if (empty($articles)) {
            // unlock warehouse
            if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
                $this->updateWarehouseLocked();
            }
            $error = $db->HasFailedTrans();
            $db->CompleteTrans();
            return $error;
        }

        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        $models = Nomenclatures::search($this->registry, array('where' => array('n.id in (' . implode(', ', $articles) . ')'), 'sanitize' => true));
        $articles = array();
        foreach ($models as $k => $v) {
            $articles[$v->get('id')] = $v;
        }

        $wid = $this->get('warehouse');
        require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php');
        $old_warehouse = Finance_Warehouses::searchOne($this->registry,
                array('where' => array('fwh.id=' . $wid),
                      'sanitize' => true));
        $new_warehouse = clone $old_warehouse;
        $unique = array('nom', 'batch', 'serial', 'expire', 'custom', 'delivery_price', 'currency');
        $qParams = array(
            'nom_id' => array_keys($articles),
            'force' => true,
            'get_zeros' => true,
            'create_key' => $unique,
            'reservation' => 'none'
        );
        $available = $old_warehouse->getAvailableQuantity($qParams);

        $prec = $this->registry['config']->getSectionParams('precision');

        $inserts = array();
        foreach ($gt2['values'] as $row) {
            if ($articles[$row['article_id']]->get('has_batch')) {
                foreach ($row['batch_data'] as $k => $v) {
                    // create batch row unique key
                    // check current quantities
                    $batch_row_unique_key = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'nomenclature_id' => $row['article_id'],
                            'batch_id'        => $v['batch'],
                            'serial_number'   => $articles[$row['article_id']]->get('has_serial') ? $v['serial'] : '',
                            'expire_date'     => $articles[$row['article_id']]->get('has_expire') ? $v['expire'] : '0000-00-00',
                            'cstm_number'     => $v['custom'],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($v['delivery_price'], $prec['gt2_rows'])),
                            'currency'        => $v['currency'],
                        ]
                    );
                    if (isset($available[$row['article_id']]['batch_data'][$batch_row_unique_key])) {
                        // we have new quantity for this key
                        unset($available[$row['article_id']]['batch_data'][$batch_row_unique_key]);
                    }
                    if (empty($available[$row['article_id']]['batch_data'])) {
                        unset($available[$row['article_id']]);
                    }
                    if (!$v['quantity'] || $v['quantity'] <= 0) {
                        continue;
                    }
                    $inserts[] = sprintf('(%d, %d, %d, "%s", "%s", "%s", "%s", "%s", "%s", NOW(), %d)',
                        $this->get('warehouse'), $v['batch'], $row['article_id'], $v['quantity'],
                        $articles[$row['article_id']]->get('has_serial') ? $v['serial'] : "",
                        $v['custom'],
                        $articles[$row['article_id']]->get('has_expire') ? $v['expire'] : "",
                        $v['delivery_price'], $v['currency'],
                        $this->registry['currentUser']->get('id')
                    );
                }
                if (!empty($available[$row['article_id']]['batch_data'])) {
                    foreach ($available[$row['article_id']]['batch_data'] as $v) {
                        // fill again this row untouched as we will delete all current articles rows and insert them again
                        $inserts[] = sprintf('(%d, %d, %d, "%s", "%s", "%s", "%s", "%s", "%s", NOW(), %d)',
                                $this->get('warehouse'), $v['batch'], $row['article_id'], $v['quantity'],
                                $v['serial'], $v['custom'], $v['expire'], $v['delivery_price'], $v['currency'],
                                $this->registry['currentUser']->get('id')
                        );
                    }
                }
            } elseif ($row['quantity'] && $row['quantity'] > 0) {
                $inserts[] = sprintf('(%d, 0, %d, "%s", "", "", "0000-00-00", 0, "", NOW(), %d)',
                    $this->get('warehouse'), $row['article_id'], $row['quantity'], $this->registry['currentUser']->get('id')
                );
            }
        }

        //clear the nom quantities for this warehouse
        $query = "DELETE FROM " . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . "\n" .
                 "WHERE parent_id = " . $this->get('warehouse') . "\n" .
                 "  AND nomenclature_id IN (" . implode(', ', array_keys($articles)) . ")";
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
            $this->registry['logger']->dbError('delete warehouse quantity details', $db, $query);
        }

        //insert the nom quantities for this warehouse
        if (!empty($inserts)) {
            $query = "INSERT INTO " . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES .
                     " (`parent_id`, batch_id, `nomenclature_id`, `quantity`, serial_number, cstm_number, expire_date, delivery_price, currency, `updated`, `updated_by`) VALUES\n" .
                     implode(",\n", $inserts);
            $db->Execute($query);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
            $this->registry['logger']->dbError('insert warehouse quantity details', $db, $query);
        }
        $qParams = array(
            'nom_id' => array_keys($articles),
            'force' => true,
            'get_zeros' => true,
            'create_key' => $unique,
            'reservation' => 'none'
        );
        $new_warehouse->getAvailableQuantity($qParams);

        //save warehouse history entry
        $new_warehouse->set('warehouse_document_id', $this->get('id'), true);
        $new_warehouse->set('warehouse_document_action', $this->registry['action'], true);
        require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses.history.php');
        $audit_parent = Finance_Warehouses_History::saveData($this->registry,
                                                             array('action_type' => 'change_quantities',
                                                                   'new_model' => $new_warehouse,
                                                                   'old_model' => $old_warehouse
                                                             ));

        unset($old_warehouse);
        unset($new_warehouse);
        // unlock warehouse
        if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
            $this->updateWarehouseLocked();
        }
        $error = $db->HasFailedTrans();
        $db->CompleteTrans();

        return !$error;
    }

    /**
     * Unlocks warehouse or locks it with another unfinished inspection.
     * Method is executed when an inspection is finished or annulled.
     *
     * @return bool - result of the operation
     */
    private function updateWarehouseLocked() {
        // check conditions whether action should be executed
        if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION && $this->get('id') && $this->get('warehouse') && ($this->get('status') == 'finished' || $this->get('annulled'))) {
            // query to check if warehouse has another unfinished inspection
            // we don't exclude current model because function should be called only when it is finished
            $locked_query = "
                SELECT id
                FROM " . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "
                WHERE type = '" . PH_FINANCE_TYPE_INSPECTION . "'
                  AND warehouse = '{$this->get('warehouse')}'
                  AND active = 1
                  AND annulled_by = 0
                  AND status IN ('opened', 'locked')
                ORDER BY status = 'opened' DESC, modified DESC, id DESC
                LIMIT 0, 1";

            $query = "
                UPDATE " . DB_TABLE_FINANCE_WAREHOUSES . "
                SET locked = '" . ($this->registry['db']->GetOne($locked_query) ?: '0') . "'
                WHERE id = '{$this->get('warehouse')}'";
            $this->registry['db']->Execute($query);

            return !$this->registry['db']->HasFailedTrans();
        }
        return false;
    }

    /**
     * Create commodities release document for all non-released commodities from reservation.
     *
     * @return boolean - result of the operation
     */
    public function releaseCommodities() {

        if ($this->sanitized) {
            $this->unsanitize();
            $sa = true;
        }
        $release_doc = clone $this;
        $release_doc->unsetProperty('id', true);
        $release_doc->unsetProperty('num', true);
        $release_doc->unsetProperty('type_name', true);
        $release_doc->set('type', PH_FINANCE_TYPE_COMMODITIES_RELEASE, true);
        // set name to type name
        $release_doc->set('name', $release_doc->getModelTypeName(), true);
        // set values for group and department
        $release_doc->setGroup($release_doc->get('group'));
        $release_doc->setDepartment($release_doc->get('department'));
        unset($release_doc->counter);
        $release_doc->set('status', 'finished', true);
        $release_doc->set('link_to', $this->get('id'), true);
        $release_doc->set('link_to_model_name', $this->modelName, true);
        $table = $release_doc->get('grouping_table_2');
        $table['rows'] = array();
        $table['process_batches'] = true;
        $release_doc->set('grouping_table_2', $table, true);
        $release_doc->calculateGT2();
        $release_doc->slashesEscape();
        if (!$release_doc->save()) {
            return false;
        } else {
            $this->set('release_id', $release_doc->get('id'), true);
            // write history for commodities release
            $old_model = new Finance_Warehouses_Document($this->registry);
            // set type and company in order to prepare GT2 table in old model
            $old_model->set('type', $release_doc->get('type'), true);
            $old_model->set('company', $release_doc->get('company'), true);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();

            $filters = array('where' => array('fwd.id = ' . $release_doc->get('id')));
            $new_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
            $new_warehouses_document->getGT2Vars();

            require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php';
            $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                           array('action_type' => 'add',
                                                                                 'new_model' => $new_warehouses_document,
                                                                                 'old_model' => $old_model));

            unset($old_model);
            unset($new_warehouses_document);
        }
        if (!empty($sa)) {
            $this->sanitize();
        }

        return true;
    }

    /**
     * Updates/adds quantities in warehouse for a set of articles.
     */
    public function updateWarehouseQuantity() {

        $old_model = $this->get('old_model');

        $gt2 = $this->get('grouping_table_2');

        //get old and new warehouse models
        $wid = $this->get('warehouse');
        require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php');
        $old_warehouse = Finance_Warehouses::searchOne($this->registry,
                                                       array('where' => array('fwh.id=' . $wid),
                                                             'sanitize' => true));
        $new_warehouse = clone $old_warehouse;

        $sign = -1;
        if ($this->get('direction') == 'incoming') {
            $sign = 1;
        }

        $prec = $this->registry['config']->getSectionParams('precision');

        $quantity = array();
        $article_ids = array();
        $batchesData = [];
        foreach ($gt2['values'] as $key => $vals) {
            // gt2 could have rows without article_id if data comes directly from request,
            // they have been skipped in Model::saveGT2Vars() and should be skipped here as well
            if (empty($vals['deleted']) && !empty($vals['article_id'])) {
                $article_ids[] = $vals['article_id'];
                if (empty($vals['batch_data'])) {
                    //check for one article in more than 1 row
                    if (isset($quantity[$vals['article_id']])) {
                        $quantity[$vals['article_id']] += $vals['quantity'];
                    } else {
                        $quantity[$vals['article_id']] = $vals['quantity'];
                    }
                } else {
                    foreach ($vals['batch_data'] as $data) {
                        if (($this->get('link_to_model_name') == 'Finance_Incomes_Reason' || $this->get('link_to_model_name') == 'Contract') && $this->get('direction') == 'incoming') {
                            //we have to get batches data from the DB as there could be differences between the prices
                            //so the key will not match
                            $query = 'SELECT delivery_price, currency FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . "\n" .
                                     'WHERE nomenclature_id = ' . $vals['article_id'] . "\n" .
                                     '  AND batch_id = ' . $data['batch'];
                            $pc = $this->registry['db']->GetRow($query);
                            if (empty($pc)) {
                                //get batch data from the first warehouses document for this batch(incoming handover or inspection)
                                $query = 'SELECT gb.delivery_price, gb.currency' . "\n" .
                                         'FROM ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                                         'JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' gb' . "\n" .
                                         '  ON g.id = gb.parent_id AND gb.batch_id = ' . $data['batch'] . "\n" .
                                         'WHERE model = "Finance_Warehouses_Document" AND g.article_id = ' . $vals['article_id'] . "\n" .
                                         'ORDER BY g.id ASC';
                                $pc = $this->registry['db']->GetRow($query);
                            }
                            if (empty($pc)) {
                                $this->registry['messages']->setError($this->i18n('error_system_calculation'));
                                $this->registry['db']->FailTrans();
                            } else {
                                $data['delivery_price'] = $pc['delivery_price'];
                                $data['currency'] = $pc['currency'];
                            }
                        }
                        //create unique key
                        $batchData = [
                            'nomenclature_id' => $vals['article_id'],
                            'batch_id'        => $data['batch'],
                            'serial_number'   => $data['serial'],
                            'expire_date'     => $data['expire'],
                            'cstm_number'     => $data['custom'],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($data['delivery_price'], $prec['gt2_rows'])),
                            'currency'        => $data['currency'],
                        ];
                        $unique = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            $batchData
                        );
                        $batchesData[$unique] = $batchData;
                        if (isset($quantity[$unique])) {
                            $quantity[$unique] += $data['quantity'];
                        } else {
                            $quantity[$unique] = $data['quantity'];
                        }
                    }
                }
            }
        }

        $article_ids = array_unique($article_ids);

        $unique = array('nom', 'batch', 'serial', 'expire', 'custom', 'delivery_price', 'currency');
        $qParams = array(
            'nom_id' => $article_ids,
            'force' => true,
            'get_zeros' => true,
            'create_key' => $unique,
            'reservation' => 'none'
        );
        $old_warehouse->getAvailableQuantity($qParams);

        foreach ($quantity as $key => $value) {
            $insert = array();
            $update = array();
            $update['quantity'] = 'quantity = quantity + (' . $value * $sign . ')';
            $update['updated'] = 'updated = NOW()';
            $update['updated_by'] = sprintf('updated_by = %d', $this->registry['currentUser']->get('id'));
            $insert = $update;
            if (!is_numeric($key)) {
                $batchData = $batchesData[$key];
                $key = $batchData['nomenclature_id'];
                $insert['batch_id']       = sprintf('batch_id = %d',         $batchData['batch_id']);
                $insert['serial_number']  = sprintf('serial_number = "%s"',  $batchData['serial_number']);
                $insert['expire_date']    = sprintf('expire_date = "%s"',    $batchData['expire_date']);
                $insert['cstm_number']    = sprintf('cstm_number = "%s"',    $batchData['cstm_number']);
                $insert['delivery_price'] = sprintf('delivery_price = %.6F', $batchData['delivery_price']);
                $insert['currency']       = sprintf('currency = "%s"',       $batchData['currency']);
            }
            $insert['nomenclature_id'] = sprintf('nomenclature_id = %d', $key);
            $insert['parent_id'] = sprintf('parent_id = %d', $this->get('warehouse'));
            $insert['quantity'] = 'quantity = (' . $value * $sign . ')';
            $query = "INSERT INTO " . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . ' SET ' . "\n" . implode(",\n", $insert) . "\n" .
                     "ON DUPLICATE KEY UPDATE " . "\n" . implode(",\n", $update);
            $this->registry['db']->Execute($query);
        }
        $qParams = array(
            'nom_id' => $article_ids,
            'force' => true,
            'get_zeros' => true,
            'create_key' => $unique,
            'reservation' => 'none'
        );
        $new_warehouse->getAvailableQuantity($qParams);
        //save warehouse history entry
        $new_warehouse->set('warehouse_document_id', $this->get('id'), true);
        $new_warehouse->set('warehouse_document_action', $this->registry['action'], true);
        require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses.history.php');

        Finance_Warehouses_History::saveData($this->registry,
                                             array('action_type' => 'change_quantities',
                                                   'new_model' => $new_warehouse,
                                                   'old_model' => $old_warehouse
                                             ));

        unset($old_warehouse);
        unset($new_warehouse);
    }

    /**
     * Updates commodities reservations for customer and warehouse
     * of a handover (the current model) when it is added.
     *
     * @return boolean - result of the operation
     */
    public function updateCommoditiesReservations() {

        // if this is an incoming handover or a handover from commodities transfer,
        // it will not change commodities reservations, so just return
        if ($this->get('direction') !== 'outgoing' || !$this->get('customer')) {
            return true;
        }

        $warehouse_id = $this->get('warehouse');
        $filters = array(
            'where' => array(
                'fwd.customer = ' . $this->get('customer'),
                'fwd.type = ' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION,
                'fwd.status = \'locked\'',
                'fwd.warehouse = ' . $warehouse_id
            ),
            'sort' => array('fwd.date DESC')
        );
        // if flag is set, it specifies that only quantities from listed reservations could be released
        // apply flag even if empty
        if ($this->isDefined('reservation_ids') && is_array($this->get('reservation_ids'))) {
            $filters['where'][] = "fwd.id IN ('" . implode("', '", $this->get('reservation_ids')) . "')";
        }

        $reservations = Finance_Warehouses_Documents::search($this->registry, $filters);
        if (empty($reservations)) {
            return true;
        }

        $prec = $this->registry['config']->getSectionParams('precision');

        $gt2 = $this->get('grouping_table_2');
        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $article_ids = array();
        $unique = array();
        foreach ($gt2['values'] as $key => $vals) {
            $article_ids[$key] = $vals['article_id'];
            //create unique keys to compare against
            if (!empty($vals['batch_data'])) {
                foreach ($vals['batch_data'] as $b => $bd) {
                    $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'nomenclature_id' => $vals['article_id'],
                            'batch_id'        => $bd['batch'],
                            'expire_date'     => $bd['expire'] ? : '0000-00-00',
                            'serial_number'   => $bd['serial'],
                            'cstm_number'     => $bd['custom'],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                            'currency'        => $bd['currency'],
                        ]
                    );
                    if (!isset($unique[$uk])) {
                        $unique[$uk] = 0;
                    }
                    $unique[$uk] += $bd['quantity'];
                }
            }
        }

        foreach ($reservations as $reservation) {
            $reservation->getGT2Vars();
            $reservation->getBatchesData();
            $old_res = clone $reservation;
            if ($old_res->isSanitized()) {
                $old_res->unsanitize();
            }
            // reduce quantities and remove articles and batches that were already released
            // from GT2 table of commodities reservation
            $reservation->updateFromReleaseCommodities();
            $reservation_gt2 = $reservation->get('grouping_table_2');
            // prepare quantities and batches to be released based on quantities and batches in handover
            $tmp_vals = array();
            foreach ($reservation_gt2['values'] as $key => $vals) {
                if (($index = array_search($vals['article_id'], $article_ids)) != false) {
                    if ($gt2['values'][$index]['quantity'] > 0) {
                        if (empty($gt2['values'][$index]['batch_data'])) {
                            if (!empty($vals['batch_data'])) {
                                $this->raiseError('error_system_calculation');
                                $this->registry['db']->FailTrans();
                                $this->registry->set('get_old_vars', $gov, true);
                                return false;
                            }
                            $rel_quantity = min($gt2['values'][$index]['quantity'], $vals['quantity']);
                            $vals['quantity'] = $rel_quantity;
                            $gt2['values'][$index]['quantity'] -= $rel_quantity;
                            $tmp_vals[$vals['id']] = $vals;
                        } else {
                            if (empty($vals['batch_data'])) {
                                $this->raiseError('error_system_calculation');
                                $this->registry['db']->FailTrans();
                                $this->registry->set('get_old_vars', $gov, true);
                                return false;
                            }
                            $vals['quantity'] = 0;
                            //batch data processing
                            foreach ($vals['batch_data'] as $b => $bd) {
                                $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                    $this->registry['db'],
                                    [
                                        'nomenclature_id' => $vals['article_id'],
                                        'batch_id'        => $bd['batch'],
                                        'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                        'serial_number'   => $bd['serial'],
                                        'cstm_number'     => $bd['custom'],
                                        'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                                        'currency'        => $bd['currency'],
                                    ]
                                );
                                if (isset($unique[$uk])) {
                                    $rel_quantity = min($bd['quantity'], $unique[$uk]);
                                    $vals['quantity'] += $rel_quantity;
                                    $vals['batch_data'][$b]['quantity'] = $rel_quantity;
                                    $gt2['values'][$index]['quantity'] -= $rel_quantity;
                                    $unique[$uk] -= $rel_quantity;
                                    $tmp_vals[$vals['id']] = $vals;
                                }
                            }
                        }
                    }
                }
            }
            if (!empty($tmp_vals)) {
                $reservation_gt2['values'] = $tmp_vals;
                $reservation->set('grouping_table_2', $reservation_gt2, true);
                $reservation->set('table_values_are_set', true, true);

                if ($reservation->releaseCommodities()) {
                    $old_res->updateFromReleaseCommodities();
                    $old_res_gt2 = $old_res->get('grouping_table_2');
                    $update_status = true;
                    foreach ($old_res_gt2['values'] as $key => $vals) {
                        if ($old_res_gt2['values'][$key]['quantity'] > 0) {
                            $update_status = false;
                        }
                    }
                    // if everything from reservation has been released, it can be finished
                    if ($update_status) {
                        $set = array();
                        $set['status'] = sprintf("`status`='%s'", 'finished');
                        $set['modified'] = sprintf("`modified`=now()");
                        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
                        $set['status_modified'] = sprintf("`status_modified`=now()");
                        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

                        if (!$old_res->get('num')) {
                            //set num
                            $set['num'] = sprintf("num='%s'", $old_res->getNum());
                            if ($old_res->counter) {
                                $old_res->counter->increment();
                            }
                        }

                        //query to update the main table
                        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "\n" .
                                  'SET ' . implode(', ', $set) . "\n" .
                                  'WHERE id=' . $old_res->get('id');
                        $this->registry['db']->Execute($query1);

                        // write history for commodities reservation
                        $reservation->sanitize();
                        $reservation->unsetProperty('grouping_table_2', true);

                        $filters = array('where' => array('fwd.id = ' . $reservation->get('id')));
                        $new_reservation = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

                        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php';
                        $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                array('action_type' => 'finish_reservation',
                                        'new_model' => $new_reservation,
                                        'old_model' => $reservation));

                    }
                } else {
                    $this->registry['db']->FailTrans();
                }
            }
        }
        $this->registry->set('get_old_vars', $gov, true);

        return true;
    }

    /**
     * Update the commodities reservation by releasing quantities
     * The model contains updated (decreased) commodity quantities
     * IMPORTANT: the GT2 with the newly set quantities of the commodities should be set already
     * IMPORTANT: not allowed to increase quantities, justo to decrease them
     *
     * @return boolean - result of the operation
     */
    public function partlyReleaseReservation() {

        // if this is an incoming handover or a handover from commodities transfer,
        // it will not change commodities reservations, so just return
        if ($this->get('type') != PH_FINANCE_TYPE_COMMODITIES_RESERVATION) {
            return true;
        }

        $gt2 = $this->get('grouping_table_2');

        //get the previous state of the reservation document
        $filters = array(
            'where' => array(
                'fwd.id = ' . $this->get('id'),
            ),
        );
        $old_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $old_model->getGT2Vars();
        $old_model->getBatchesData();
        $old_model->updateFromReleaseCommodities(true);

        $prec = $this->registry['config']->getSectionParams('precision');

        $old_gt2 = $old_model->get('grouping_table_2');
        $article_ids = array();
        $unique = array();
        foreach ($gt2['values'] as $key => $vals) {
            $article_ids[$key] = $vals['article_id'];
            //create unique keys to compare against
            if (!empty($vals['batch_data'])) {
                foreach ($vals['batch_data'] as $b => $bd) {
                    $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'nomenclature_id' => $vals['article_id'],
                            'batch_id'        => $bd['batch'],
                            'expire_date'     => $bd['expire'] ? : '0000-00-00',
                            'serial_number'   => $bd['serial'],
                            'cstm_number'     => $bd['custom'],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                            'currency'        => $bd['currency'],
                        ]
                    );
                    if (!isset($unique[$uk])) {
                        $unique[$uk] = 0;
                    }
                    $unique[$uk] += $bd['quantity'];
                }
            }
        }


        // prepare quantities and batches to be released based on difference if quantities now and before
        $tmp_vals = array();
        $valid = true;
        foreach ($old_gt2['values'] as $key => $vals) {
            $row_num = array_search($key, array_keys($old_gt2['values'])) + 1;
            if (($index = array_search($vals['article_id'], $article_ids)) != false) {
                if (empty($gt2['values'][$index]['batch_data'])) {
                    if (!empty($vals['batch_data'])) {
                        $this->raiseError('error_system_calculation');
                        $this->registry->set('get_old_vars', $gov, true);
                        return false;
                    }
                    if ($vals['quantity'] < $gt2['values'][$index]['quantity']) {
                        $this->raiseError(
                            'error_invalid_reservation_partly_relase_row',
                            'quantity',
                            0,
                            array($row_num, $gt2['values'][$index]['quantity'], $vals['quantity'])
                        );
                        $valid = false;
                        continue;
                    }
                    $rel_quantity = bcsub($vals['quantity'], $gt2['values'][$index]['quantity'], $prec['gt2_rows']);
                    $vals['quantity'] = $rel_quantity;
                    //$gt2['values'][$index]['quantity'] = $rel_quantity;
                    $tmp_vals[$vals['id']] = $vals;
                } else {
                    if (empty($vals['batch_data'])) {
                        $this->raiseError('error_system_calculation');
                        $this->registry->set('get_old_vars', $gov, true);
                        return false;
                    }
                    if ($vals['quantity'] < $gt2['values'][$index]['quantity']) {
                        $this->raiseError(
                            'error_invalid_reservation_partly_relase_row',
                            'quantity',
                            0,
                            array($row_num, $gt2['values'][$index]['quantity'], $vals['quantity'])
                        );
                        $valid = false;
                        continue;
                    }

                    $vals['quantity'] = 0;
                    //batch data processing
                    foreach ($vals['batch_data'] as $b => $bd) {
                        $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'nomenclature_id' => $vals['article_id'],
                                'batch_id'        => $bd['batch'],
                                'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                'serial_number'   => $bd['serial'],
                                'cstm_number'     => $bd['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $bd['currency'],
                            ]
                        );
                        if (isset($unique[$uk])) {
                            $rel_quantity = bcsub($bd['quantity'], $unique[$uk], $prec['gt2_rows']);
                            $vals['quantity'] += $rel_quantity;
                            $vals['batch_data'][$b]['quantity'] = $rel_quantity;
                            ///$gt2['values'][$index]['quantity'] = $rel_quantity;
                            $unique[$uk] -= $rel_quantity;
                            $tmp_vals[$vals['id']] = $vals;
                        }
                    }
                }
            } else {
                //the row has been deleted
                //so include the all remaining from the old
                $tmp_vals[$vals['id']] = $vals;
            }

        }

        if (!$valid) {
            $this->registry->set('get_old_vars', $gov, true);
            return false;
        }

        $result = true;
        $db = $this->registry['db'];

        //remove the zero quantities
        $tmp_vals = array_filter($tmp_vals, function($row) {
            return $row['quantity'] != 0;
        });

        if (!empty($tmp_vals)) {
            //start transaction
            $db->StartTrans();

            $release_gt2 = $gt2;
            $release_gt2['values'] = $tmp_vals;
            $this->set('grouping_table_2', $release_gt2, true);
            $this->set('table_values_are_set', true, true);
            if ($this->releaseCommodities()) {
                //if all the quantities are 0 then the reservation document should be finished
                $update_status = true;
                foreach ($gt2['values'] as $key => $vals) {
                    if ($vals['quantity'] > 0) {
                        //at least one is more than zero, do not update the document
                        $update_status = false;
                        break;
                    }
                }

                // if everything from reservation has been released, it can be finished
                if ($update_status) {
                    $set = array();
                    $set['status'] = sprintf("`status`='%s'", 'finished');
                    $set['modified'] = sprintf("`modified`=now()");
                    $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
                    $set['status_modified'] = sprintf("`status_modified`=now()");
                    $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

                    if (!$old_model->get('num')) {
                        //set num
                        $set['num'] = sprintf("num='%s'", $old_model->getNum());
                        if ($old_model->counter) {
                            $old_model->counter->increment();
                        }
                    }

                    //query to update the main table
                    $query1 = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "\n" .
                        'SET ' . implode(', ', $set) . "\n" .
                        'WHERE id=' . $old_model->get('id');
                    $this->registry['db']->Execute($query1);
                }
            } else {
                $db->FailTrans();
            }

            $result = !$db->HasFailedTrans();
            $db->CompleteTrans();
        } else {
            $this->raiseError('error_finance_warehouses_documents_release_reservation_nothing_to_release');
            $result = false;
        }

        $this->registry->set('get_old_vars', $gov, true);

        return $result;
    }

    /**
     * Update GT2 table of commodities reservation from related commodities releases
     * (reduce quantities and remove rows and batches that were fully released).
     * This is applied only to model, data in DB is not modified.
     */
    public function updateFromReleaseCommodities($includeZeroQuantities = false) {
        if ($this->sanitized) {
            $this->unsanitize();
            $sa = true;
        }
        if (!$this->get('grouping_table_2')) {
            $this->getGT2Vars();
            $this->getBatchesData();
        }
        $gt2 = $this->get('grouping_table_2');
        $this->getRelatives('child');
        $releases = $this->get('relatives');
        $releases = $releases['child'];

        $prec = $this->registry['config']->getSectionParams('precision');

        foreach ($releases as $k => $v) {
            if ($v->modelName != 'Finance_Warehouses_Document' || $v->get('type') != PH_FINANCE_TYPE_COMMODITIES_RELEASE) {
                unset($releases[$k]);
                continue;
            }
            $releases[$k]->getGT2Vars();
            $releases[$k]->getBatchesData();
        }
        if (empty($releases)) {
            if (!empty($sa)) {
                $this->sanitize();
            }
            return true;
        }
        $unique = array();
        //prepare unique key for batch processing
        foreach ($gt2['values'] as $key => $vals) {
            if (!empty($vals['batch_data'])) {
                foreach ($vals['batch_data'] as $b => $bd) {
                    $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'nomenclature_id' => $vals['article_id'],
                            'batch_id'        => $bd['batch'],
                            'expire_date'     => $bd['expire'] ? : '0000-00-00',
                            'serial_number'   => $bd['serial'],
                            'cstm_number'     => $bd['custom'],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                            'currency'        => $bd['currency'],
                        ]
                    );
                    $unique[$uk] = array('quantity' => $bd['quantity'], 'key' => $key, 'b' => $b);
                }
            } else {
                $unique[$vals['article_id']] = array('quantity' => $vals['quantity'], 'key' => $key);
            }
        }

        foreach ($releases as $release) {
            $rgt2 = $release->get('grouping_table_2');
            foreach ($rgt2['values'] as $key => $vals) {
                if (!empty($vals['batch_data'])) {
                    foreach ($vals['batch_data'] as $b => $bd) {
                        $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'nomenclature_id' => $vals['article_id'],
                                'batch_id'        => $bd['batch'],
                                'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                'serial_number'   => $bd['serial'],
                                'cstm_number'     => $bd['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $bd['currency'],
                            ]
                        );
                        $gt2['values'][$unique[$uk]['key']]['batch_data'][$unique[$uk]['b']]['quantity'] = bcsub(
                            $gt2['values'][$unique[$uk]['key']]['batch_data'][$unique[$uk]['b']]['quantity'],
                            $bd['quantity'],
                            $prec['gt2_quantity']
                        );
                        $gt2['values'][$unique[$uk]['key']]['quantity'] = bcsub(
                            $gt2['values'][$unique[$uk]['key']]['quantity'],
                            $bd['quantity'],
                            $prec['gt2_quantity']
                        );
                        if (!$includeZeroQuantities &&
                            $gt2['values'][$unique[$uk]['key']]['batch_data'][$unique[$uk]['b']]['quantity'] <= 0) {
                            unset($gt2['values'][$unique[$uk]['key']]['batch_data'][$unique[$uk]['b']]);
                        }
                        if (!$includeZeroQuantities &&
                            $gt2['values'][$unique[$uk]['key']]['quantity'] <= 0) {
                            unset($gt2['values'][$unique[$uk]['key']]);
                        }
                        if (isset($gt2['values'][$unique[$uk]['key']]) &&
                            array_key_exists('released', $gt2['values'][$unique[$uk]['key']])) {
                            $gt2['values'][$unique[$uk]['key']]['released'] += $bd['quantity'];
                        }
                    }
                } else {
                    $gt2['values'][$unique[$vals['article_id']]['key']]['quantity'] = bcsub(
                        $gt2['values'][$unique[$vals['article_id']]['key']]['quantity'],
                        $vals['quantity'],
                        $prec['gt2_quantity']
                    );
                    if (!$includeZeroQuantities &&
                        $gt2['values'][$unique[$vals['article_id']]['key']]['quantity'] <= 0) {
                        unset($gt2['values'][$unique[$vals['article_id']]['key']]);
                    }
                    if (isset($gt2['values'][$unique[$vals['article_id']]['key']]) &&
                        array_key_exists('released', $gt2['values'][$unique[$vals['article_id']]['key']])) {
                        $gt2['values'][$unique[$vals['article_id']]['key']]['released'] += $vals['quantity'];
                    }
                }
            }
        }
        $this->set('grouping_table_2', $gt2, true);
        if (!empty($sa)) {
            $this->sanitize();
        }
    }

    /**
     * Finish commodities reservation (set status to 'finished').
     *
     * @return boolean - result of the operation
     */
    public function finishReservation() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        $set = array();
        $set['status'] = sprintf("`status`='%s'", 'finished');
        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

        if (!$this->get('num')) {
            //set num
            $set['num'] = sprintf("num='%s'", $this->getNum());
            if ($this->counter) {
                $this->counter->increment();
            }
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $this->registry['db']->Execute($query1);

        $this->registry->set('get_old_vars', true, true);
        $this->updateFromReleaseCommodities();
        $this->set('table_values_are_set', true, true);
        if ($this->releaseCommodities()) {
            // write history for commodities reservation
            $old_warehouses_document = clone $this;
            $old_warehouses_document->sanitize();
            $old_warehouses_document->unsetProperty('grouping_table_2', true);

            $filters = array('where' => array('fwd.id = ' . $this->get('id')));
            $new_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

            require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php';
            $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                           array('action_type' => 'finish_reservation',
                                                                                 'new_model' => $new_warehouses_document,
                                                                                 'old_model' => $old_warehouses_document));

            unset($old_warehouses_document);
            unset($new_warehouses_document);
        } else {
            $db->FailTrans();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Gets the model type name from the database
     *
     * @return string - Name of type of model
     */
    public function getModelTypeName() {
        if (!$this->get('type_name')) {
            if (!$this->get('type')) {
                return '';
            }

            $sanitize_after = false;
            if ($this->sanitized) {
                $this->unsanitize();
                $sanitize_after = true;
            }

            $query = 'SELECT name FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N . "\n" .
                     'WHERE parent_id=' . $this->get('type') . ' AND lang="' . $this->get('model_lang') . '"';
            $type_name = $this->registry['db']->GetOne($query);
            $this->set('type_name', $type_name, true);

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $this->get('type_name');
    }

    /**
     * get counter for this model
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->unsanitize();
            }

            require_once 'finance.counters.factory.php';
            if (!$this->get('custom_counter')) {
                $filters = array('where' => array(
                                            'fc.model = "' . $this->modelName . '"',
                                            'fc.model_type = "' . $this->get('type') . '"',
                                            '(fco.office_id = "' . $this->get('office') . '" OR fco.office_id = 0)',
                                            'fc.company = "' . $this->get('company') . '"',
                                            'fc.active = 1',
                                            'fc.deleted_by = 0'),
                                 'sort' => array('fco.office_id DESC', 'fc.default DESC')
                                );
            } else {
                $filters = array('where' => array('fc.id = ' . $this->get('custom_counter')));
            }
            $this->counter = Finance_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Get number
     *
     * @param bool $force
     * @return string
     */
    public function getNum($force = false) {
        if (!$this->get('num') || $force) {
            //get the counter assigned to the warehouse document type
            $this->getCounter();

            if ($this->counter) {
                //define some the counter's fomula components
                $formula = $this->counter->get('formula');
                $prefix = $this->counter->get('prefix');
                $delimiter = $this->counter->get('delimiter');
                $zeroes = $this->counter->get('leading_zeroes');
                $date_format = $this->counter->get('date_format');

                //create extender to expand the formula components
                $extender = new Extender;

                //lock the counter for update to guarantee unique next number
                $query = 'SELECT next_number FROM ' . DB_TABLE_FINANCE_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                //set warehouse document number
                $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                $extender->add('num', $num);

                if ($this->counter->get('prefix')) {
                    //add this component to the extender
                    $extender->add('prefix', $prefix);
                }

                if ($this->counter->get('company_code') && $this->get('company')) {
                    //get customer code
                    $query = 'SELECT code FROM ' . DB_TABLE_FINANCE_COMPANIES . ' WHERE id=' . $this->get('company');
                    $company_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('company_code', $company_code);
                }

                if ($this->counter->get('office_code') && $this->get('office')) {
                    //get office code
                    $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
                    $office_code = $this->registry['db']->GetOne($query);

                    //add this component to the extender
                    $extender->add('office_code', $office_code);
                }

                if ($this->counter->get('user_code')) {
                    //get user code
                    //add this component to the extender
                    $extender->add('user_code', $this->registry['currentUser']->get('code'));
                }

                if ($this->counter->get('project_code') && $this->get('project')) {
                    //get project code
                    require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
                    $filters = array('where' => array('p.id = ' . $this->get('project'),
                                                      'p.deleted IS NOT NULL'),
                                     'sanitize' => true);
                    $project = Projects::searchOne($this->registry, $filters);

                    //add this component to the extender
                    $extender->add('project_code', $project->get('code'));
                }

                if ($this->counter->get('document_date')) {
                    //replace the date
                    if ($this->get('date')) {
                        $date = General::strftime($date_format, strtotime($this->get('date')));
                    } elseif ($this->get('added')) {
                        $date = General::strftime($date_format, strtotime($this->get('added')));
                    } else {
                        $date = General::strftime($date_format);
                    }

                    //add this component to the extender
                    $extender->add('document_date', $date);
                }

                $num = $extender->expand($formula);
                if ($delimiter) {
                    //remove repeating delimiters
                    $num = preg_replace('#' . preg_quote($delimiter . $delimiter) . '#', $delimiter, $num);
                    $num = preg_replace('#' . preg_quote($delimiter) . '$#', '', $num);
                    $num = preg_replace('#^' . preg_quote($delimiter) . '#', '', $num);
                }

                $this->set('num', $num, true);
            }
        }

        return $this->get('num');
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Finance_Warehouses_Document\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted = 0'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        return $files;
    }

    /**
     * Get all patterns variables - basic/system, additional
     *
     * @return array $vars - variables
     */
    public function getPatternsVars() {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Finance_Warehouses_Document", "Customer", "CurrentUser") OR p.type="system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $t_customer = array();
        if ($customer) {
            $customer_translations = $customer->getTranslations();
            foreach ($customer_translations as $t_lang) {
                if ($t_lang != $customer->get('model_lang')) {
                    $filters = array('model_lang' => $t_lang,
                                     'sanitize' => true,
                                     'where' => array('c.id = ' . $customer->get('id')));
                    $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
                }
            }
        }

        //get the pattern
        $pattern_id = $this->registry['request']->get('pattern');
        $filters = array('where' => array('p.id = ' . $pattern_id),
            'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        $t_user = array();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //prepare contact person
        if ($this->get('contact_person')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                       'c.subtype = \'contact\''),
                                      'sanitize'  => true,
                                      'model_lang' => $this->get('model_lang'));
            $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
            if ($contactperson) {
                $this->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
            }
        }

        //prepare customer's branch
        if ($this->get('branch')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                     'c.subtype = \'branch\''),
                                    'sanitize'  => true,
                                    'model_lang' => $this->get('model_lang'));
            $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
            if ($branch) {
                $this->set('branch_name', $branch->get('name'), true);
            }
        }

        //get the parent reason
        $relatives = $this->getRelatives('parent');
        if (!empty($relatives['parent'])) {
            $parent = reset($relatives['parent']);
            $this->set('parent_num', $parent->get('num'), true);
            //for expense reason parents
            $this->set('parent_invoice_num', $parent->get('invoice_num'), true);
        }

        $t_reason = array();
        $translations = $this->getTranslations();
        foreach ($translations as $t_lang) {
            if ($t_lang != $this->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'sanitize' => true,
                                 'where' => array('fwd.id = ' . $this->get('id')));
                $t_reason[$t_lang] = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                if ($this->get('contact_person')) {
                    $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                               'c.subtype = \'contact\''),
                                      'sanitize'  => true,
                                      'model_lang' => $t_lang);
                    $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
                    if ($contactperson) {
                        $t_reason[$t_lang]->set('contact_person_name',
                        $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                    }
                }
                if ($this->get('branch')) {
                    $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                             'c.subtype = \'branch\''),
                                      'sanitize'  => true,
                                      'model_lang' => $t_lang);
                    $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
                    if ($branch) {
                        $t_reason[$t_lang]->set('branch_name', $branch->get('name'), true);
                    }
                }
            }
        }

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Finance_Warehouses_Document') {
                    //reason variables
                    if (!$placeholder->get('multilang')) {
                        if (preg_match('#^company_#', $placeholder->get('source'))) {
                            $vars[$placeholder->get('varname')] = $this->getCompanyData(str_replace('company_', '', $placeholder->get('source')));
                        } else {
                            $vars[$placeholder->get('varname')] = $this->get($placeholder->get('source'));
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if (preg_match('#^company_#', $placeholder->get('source'))) {
                                if ($t_lang != $this->get('model_lang')) {
                                    $vars[$t_reason[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')] =
                                    $t_reason[$t_lang]->getCompanyData(str_replace('company_', '', $placeholder->get('source')));
                                } else {
                                    $vars[$this->get('model_lang') . '_' . $placeholder->get('varname')] =
                                    $this->getCompanyData(str_replace('company_', '', $placeholder->get('source')));
                                }
                            } else {
                                if ($t_lang != $this->get('model_lang')) {
                                    $vars[$t_reason[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                    = $t_reason[$t_lang]->get($placeholder->get('source'));
                                } else {
                                    $vars[$this->get('model_lang') . '_' . $placeholder->get('varname')] =
                                    $this->get($placeholder->get('source'));
                                }
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer' && $customer) {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$placeholder->get('varname')] = $customer->get($placeholder->get('source'));
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[ $t_customer[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_customer[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $customer->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $customer->get($placeholder->get('source'));
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$placeholder->get('varname')] = $user->get($placeholder->get('source'));
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_user[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $user->get($placeholder->get('source'));
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($placeholder->get('source'), '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $placeholder->get('source'));
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$placeholder->get('varname')] = $res;
                } else {
                    $vars[$placeholder->get('varname')] = $placeholder->get('source');
                }
            }
        }

        //prepare additional variables

        //get print settings for the 2nd type grouping table
        $print_properties = $this->getGT2PrintSettings($pattern_id);

        $lang = $this->get('model_lang');
        // model in current lang is not available in $t_reason array, unlike in other classes, so keep original value in a variable
        $total_no_vat_reason_text = $this->get('total_no_vat_reason_text');

        foreach ($translations as $t_lang) {
            $this->set('model_lang', $t_lang, true);
            // set no VAT reason text in current language
            $this->set('total_no_vat_reason_text', ($lang != $t_lang ? $t_reason[$t_lang]->get('total_no_vat_reason_text') : $total_no_vat_reason_text), true);

            $this->getGT2Vars(false);
            $this->getBatchesData();
            $this->prepareAvailableQuantity();
            $this->prepareGT2DynamicFields();
            $table = $this->get('grouping_table_2');

            //prepare files in GT2
            if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                foreach ($table['values'] as $ridx => $row) {
                    foreach ($row as $rkey => $rval) {
                        if (!empty($rval) && is_object($rval)) {
                            $file = $rval;
                            if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                            } else {
                                $file = '';
                            }
                            $table['values'][$ridx][$rkey] = $file;
                        }
                    }
                }
            }

            $table_ordered = $table;
            $table_ordered['vars'] = array();
            $styles_for_template = array();

            foreach ($print_properties as $key => $property) {
                if ($key == 'batch_vars') {
                    //only for the labels
                    foreach ($table['values'] as $k => $v) {
                        foreach ($property as $p) {
                            if (array_key_exists($t_lang, $print_properties['var_' . $p['var_id']]['labels'])) {
                                $table_ordered['values'][$k]['vars_settings'][$p['name']]['label'] = $print_properties['var_' . $p['var_id']]['labels'][$t_lang];
                            }
                        }
                    }
                    continue;
                }
                // style properties
                if (!empty($property['style'])) {
                    $styles_for_template[$key] = $property['style'];
                }
                // label for table caption
                if ($key == 'var_' . $table['id']) {
                    if (isset($property['labels'][$t_lang])) {
                        $table_ordered['label'] = $property['labels'][$t_lang];
                    }
                    continue;
                }

                foreach ($table['vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        $table_ordered['vars'][$idx] = $var;
                        // label for field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        // aggregates
                        if (isset($property['agregate'])) {
                            if ($property['agregate'] != 'none') {
                                $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                            } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                unset($table_ordered['vars'][$idx]['agregate']);
                            }
                        }
                        continue 2;
                    }
                }
                foreach ($table['plain_vars'] as $idx => $var) {
                    if ($key == 'var_' . $var['id']) {
                        // label for total field
                        if (isset($property['labels'][$t_lang])) {
                            $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                        }
                        continue 2;
                    }
                }
            }

            // calculate aggregates in GT2 table
            $table_ordered = $this->calculateGT2Agregates($table_ordered);

            $this->set('grouping_table_2', $table_ordered, true);

            $groupingViewer = new Viewer($this->registry);
            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');
            $groupingViewer->data['table'] = $table_ordered;
            $groupingViewer->data['styles'] = $styles_for_template;
            $groupingViewer->data['pattern_id'] = $pattern_id;
            $vars[$t_lang . '_grouping_table_2'] = $groupingViewer->fetch();

            unset($table_ordered);
        }

        $this->set('model_lang', $lang, true);
        // set no VAT reason text in current language
        $this->set('total_no_vat_reason_text', $total_no_vat_reason_text, true);

        return $vars;
    }

    /**
     * Get reasons attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'' . $this->modelName . '\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted = 0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get reasons files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $sql = array();
        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get related parent and/or children of warehouse document
     * (incomes reasons, expenses reasons, warehouse documents and contracts)
     *
     * @param string $get - 'all' or 'parent' or 'child'
     * @return array - array with parent and children models
     */
    public function getRelatives($get = 'all') {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }
        $db = $this->registry['db'];

        $records = array();
        if ($get == 'all' || $get == 'parent') {
            //get warehouses document relatives
            $query = 'SELECT frr.link_to AS id, frr.link_to_model_name AS model' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'WHERE frr.parent_id = ' . $this->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Warehouses_Document"' . "\n";
            $records = $db->GetAll($query);
        }

        $models = array();
        foreach ($records as $key => $record) {
            if (!isset($models[$record['model']])) {
                $models[$record['model']] = array();
            }
            $models[$record['model']][] = $record['id'];
        }
        $parents = array();
        foreach ($models as $model_name => $ids) {
            switch ($model_name) {
                case 'Finance_Incomes_Reason':
                    $filters = array('where' => array('fir.id IN (' . implode(', ', $ids) . ')', 'fir.annulled_by = 0'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
                    $parents = Finance_Incomes_Reasons::search($this->registry, $filters);
                    break;
                case 'Finance_Expenses_Reason':
                    $filters = array('where' => array('fer.id IN (' . implode(', ', $ids) . ')', 'fer.annulled_by = 0'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
                    $parents = Finance_Expenses_Reasons::search($this->registry, $filters);
                    break;
                case 'Finance_Warehouses_Document':
                    $filters = array('where' => array('fwd.id IN (' . implode(', ', $ids) . ')', 'fwd.annulled_by = 0'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
                    $parents = Finance_Warehouses_Documents::search($this->registry, $filters);
                    break;
                case 'Contract':
                    $filters = array('where' => array('co.id IN (' . implode(', ', $ids) . ')'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                    $parents = Contracts::search($this->registry, $filters);
                    foreach ($parents as $key => $record) {
                        $parents[$key]->getGT2Vars();
                        $table = $parents[$key]->get('grouping_table_2');
                        foreach ($table['plain_values'] as $pv_key => $pv_value) {
                            $parents[$key]->set($pv_key, $pv_value, true);
                        }
                        unset($table);
                        $parents[$key]->unsetProperty('grouping_table_2', true);
                    }
                    break;
            }
        }

        $records = array();
        if ($get == 'all' || $get == 'child') {
            //get warehouses document relatives
            $query = 'SELECT frr.parent_id AS id, frr.link_to_model_name AS model' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                     'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Warehouses_Document"' . "\n";
            $records = $db->GetAll($query);
        }
        $models = array();
        foreach ($records as $key => $record) {
            if (!isset($models[$record['model']])) {
                $models[$record['model']] = array();
            }
            $models[$record['model']][] = $record['id'];
        }
        $children = array();
        foreach ($models as $model_name => $ids) {
            switch ($model_name) {
                case 'Finance_Incomes_Reason':
                    $filters = array('where' => array('fir.id IN (' . implode(', ', $ids) . ')', 'fir.annulled_by = 0'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
                    $children = Finance_Incomes_Reasons::search($this->registry, $filters);
                    break;
                case 'Finance_Expenses_Reason':
                    $filters = array('where' => array('fer.id IN (' . implode(', ', $ids) . ')', 'fer.annulled_by = 0'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
                    $children = Finance_Expenses_Reasons::search($this->registry, $filters);
                    break;
                case 'Finance_Warehouses_Document':
                    $filters = array('where' => array('fwd.id IN (' . implode(', ', $ids) . ')', 'fwd.annulled_by = 0'),
                                     'sanitize' => true);
                    require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
                    $children = Finance_Warehouses_Documents::search($this->registry, $filters);
                    break;
            }
        }

        $this->set('relatives', array('parent' => $parents, 'child' => $children), true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return array('parent' => $parents, 'child' => $children);
    }

    /**
     * update reasons relatives
     *
     * @return bool
     */
    public function updateReasonsRelatives() {
        $db = $GLOBALS['registry']['db'];

        $query = 'SELECT * FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                 'WHERE parent_id=' . $this->get('id') . ' AND parent_model_name="Finance_Warehouses_Document"' . "\n";
        $records = $db->GetAll($query);

        $new_rows_links = $this->get('rows_links');

        if (!empty($records) && !empty($new_rows_links['deleted'])) {
            //invoice edit - remove deleted rows form the links
            if (!empty($new_rows_links['deleted'])) {
                foreach ($records as $record) {
                    foreach ($new_rows_links['deleted'] as $deleted) {
                        if (preg_match('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', $record['rows_links'])) {
                            $record['rows_links'] = preg_replace('#(^|\n)' . $deleted . '\s*=>\s*\d+(\n|$)#', "\n", $record['rows_links']);
                            $query = 'UPDATE ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                                     ' SET rows_links = \'' . $record['rows_links'] . "'\n" .
                                     'WHERE parent_id = ' . $record['parent_id'] .
                                     '  AND parent_model_name="' . $record['parent_model_name'] . '" ' . "\n" .
                                     '  AND link_to= ' . $record['link_to'] . "\n" .
                                     '  AND link_to_model_name="' . $record['link_to_model_name'] . '" ';
                            $db->Execute($query);
                        }
                    }
                }
            }
        } elseif (!empty($new_rows_links['added'])) {
            $model_name = "Finance_Incomes_Reason";
            if ($this->get('link_to_model_name')) {
                $model_name = $this->get('link_to_model_name');
            }
            if (empty($new_rows_links['added'])) {
                $new_rows_links['added'] = array();
            }
            //document adding
            foreach ($new_rows_links['added'] as $new_row => $old_row) {
                $new_rows_links['added'][$new_row] = $new_row . ' => ' . $old_row;
            }

            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     ' SET parent_id = ' . $this->get('id') . ",\n" .
                     '     parent_model_name = "Finance_Warehouses_Document",' . "\n" .
                     '     link_to = \'' . $this->get('link_to') . "',\n" .
                     '     link_to_model_name = "' . $model_name . '"' . ",\n" .
                     '     rows_links = \'' . implode("\n", $new_rows_links['added']) . '\'';
            $db->Execute($query);

        }
    }

    /**
     * Annul model
     */
    public function annul() {

        $db = $this->registry['db'];
        $db->StartTrans();

        $old_warehouses_document = clone $this;
        $old_warehouses_document->getGT2Vars();
        $old_warehouses_document->getBatchesData();
        $old_warehouses_document->sanitize();

        $prec = $this->registry['config']->getSectionParams('precision');

        if ($this->get('type') == PH_FINANCE_TYPE_HANDOVER) {
            $warehouse = $this->get('warehouse');

            // validate against locked warehouse
            $query = 'SELECT wh.locked as inspection, wd.status' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_WAREHOUSES . ' wh' . "\n".
                    'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' wd' . "\n" .
                    'ON wh.locked = wd.id AND wd.annulled_by = 0 AND wd.status != "finished"' . "\n" .
                    'WHERE wh.id = ' . $this->get('warehouse');
            $whLocker = $this->registry['db']->GetRow($query);
            if (!empty($whLocker)) {
                $inspectionLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                    $_SERVER['PHP_SELF'],
                    $this->registry['module_param'], 'finance',
                    $this->registry['controller_param'], 'warehouses_documents',
                    'warehouses_documents', 'view',
                    'view', $whLocker['inspection']);
                if ($whLocker['status'] == 'opened') {
                    // if inspection is opened, nothing with the warehouse could be done
                    // as we can add, remove, etc. articles/quantities and do any other stuff
                    $db->FailTrans();
                    $this->raiseError('error_finance_warehouse_locked', null, null, array($this->get('warehouse_name'), $inspectionLink));
                } else if ($whLocker['status'] == 'locked') {
                    // when the inspection is locked we will check for articles(with batch) intersection
                    $query = 'SELECT CONCAT(g.article_id, "_", IF(gb.parent_id IS NOT NULL, gb.batch_id, "")), fb.code' . "\n" .
                            'FROM ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' gb' . "\n" .
                            '  ON g.id = gb.parent_id' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_FINANCE_BATCHES . ' fb' . "\n" .
                            '  ON fb.id = gb.batch_id' . "\n" .
                            'WHERE g.model = "Finance_Warehouses_Document" AND g.model_id = ' . $whLocker['inspection'];
                    $whLocker['rows'] = $this->registry['db']->GetAssoc($query);
                }
            }

            //get old and new warehouse models
            require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php');
            $old_warehouse = Finance_Warehouses::searchOne($this->registry,
                                                           array('where' => array('fwh.id=' . $warehouse),
                                                                 'sanitize' => true));
            $new_warehouse = clone $old_warehouse;

            if ($this->get('direction') == 'incoming') {
                $sign = -1;
            } elseif ($this->get('direction') == 'outgoing') {
                $sign = 1;
            }
            $gt2 = $old_warehouses_document->get('grouping_table_2');

            // collect total quantity for each article in document
            $quantity = array();
            foreach ($gt2['values'] as $key => $vals) {
                if (empty($vals['deleted'])) {
                    if (empty($vals['batch_data'])) {
                        //check for one article in more than 1 row
                        if (isset($quantity[$vals['article_id']])) {
                            $quantity[$vals['article_id']] += $vals['quantity'];
                        } else {
                            $quantity[$vals['article_id']] = $vals['quantity'];
                        }
                        //we have to check if this article is inspected at the moment
                        if (isset($whLocker['rows'][$vals['article_id'] . '_'])) {
                            $db->FailTrans();
                            $this->raiseError('error_finance_warehouse_locked_article', null, null, array($vals['article_name'] , $inspectionLink));
                        }
                    } else {
                        foreach ($vals['batch_data'] as $b => $bd) {
                            $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                $this->registry['db'],
                                [
                                    'parent_id'       => $warehouse,
                                    'batch_id'        => $bd['batch'],
                                    'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                    'serial_number'   => $bd['serial'],
                                    'cstm_number'     => $bd['custom'],
                                    'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                                    'currency'        => $bd['currency'],
                                ]
                            );
                            if (isset($quantity[$vals['article_id']][$uk])) {
                                $quantity[$vals['article_id']][$uk] += $bd['quantity'];
                            } else {
                                $quantity[$vals['article_id']][$uk] = $bd['quantity'];
                            }
                            //we have to check if this article is inspected at the moment
                            if (isset($whLocker['rows'][$vals['article_id'] . '_' . $bd['batch']])) {
                                $db->FailTrans();
                                $this->raiseError('error_finance_warehouse_locked_article', null, null,
                                        array($vals['article_name'] . ' (' . $whLocker['rows'][$vals['article_id'] . '_' . $bd['batch']] . ')', $inspectionLink));
                            }
                        }
                    }
                }
            }

            $article_ids = array_keys($quantity);

            $create_key = array('warehouse', 'batch', 'expire', 'serial', 'custom', 'delivery_price', 'currency');
            $qParams = array(
                'nom_id' => $article_ids,
                'force' => true,
                'get_zeros' => true,
                'create_key' => $create_key,
            );
            $old_warehouse->getAvailableQuantity($qParams);

            // available quantities of specified articles in warehouse
            $available_quantities = $old_warehouse->get('available_quantities');

            $row = 0;
            foreach ($gt2['values'] as $gt2row) {
                $row++;
                if (empty($gt2row['batch_data']) && ($sign == 1 ||
                  isset($available_quantities[$gt2row['article_id']]['quantity']) &&
                  $available_quantities[$gt2row['article_id']]['quantity'] >= $quantity[$gt2row['article_id']])) {
                    $update                 = array();
                    $update['quantity']     = 'quantity = quantity + (' . $sign * $gt2row['quantity'] . ')';
                    $update['updated']      = 'updated = NOW()';
                    $update['updated_by']   = sprintf('updated_by = %d', $this->registry['currentUser']->get('id'));
                    $insert = $update;
                    $insert['nomenclature_id'] = 'nomenclature_id = ' . $gt2row['article_id'];
                    $insert['parent_id'] = 'parent_id = ' . $warehouse;
                    $query = 'INSERT INTO ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . "\n" .
                             'SET ' . implode(", ", $insert) . "\n" .
                             'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);
                    $this->registry['db']->Execute($query);
                } elseif (!empty($gt2row['batch_data']) && ($sign == 1 ||
                  isset($available_quantities[$gt2row['article_id']]['quantity']) &&
                  $available_quantities[$gt2row['article_id']]['quantity'] >= array_sum($quantity[$gt2row['article_id']]))) {
                    foreach ($gt2row['batch_data'] as $b => $bd) {
                        $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'parent_id'       => $warehouse,
                                'batch_id'        => $bd['batch'],
                                'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                'serial_number'   => $bd['serial'],
                                'cstm_number'     => $bd['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $bd['currency'],
                            ]
                        );
                        if ($sign == 1 || isset($available_quantities[$gt2row['article_id']]['batch_data'][$uk]) &&
                          $available_quantities[$gt2row['article_id']]['batch_data'][$uk] >= $quantity[$gt2row['article_id']][$uk]) {
                            $update                 = array();
                            $update['quantity']     = 'quantity = quantity + (' . $sign * $bd['quantity'] . ')';
                            $update['updated']      = 'updated = NOW()';
                            $update['updated_by']   = sprintf('updated_by = %d', $this->registry['currentUser']->get('id'));
                            $insert = $update;
                            $insert['nomenclature_id'] = 'nomenclature_id = ' . $gt2row['article_id'];
                            $insert['parent_id'] = 'parent_id = ' . $warehouse;
                            $insert['batch_id'] = 'batch_id = ' . $bd['batch'];
                            $insert['serial_number'] = 'serial_number = "' . $bd['serial'] . '"';
                            $insert['cstm_number'] = 'cstm_number = "' . $bd['custom'] . '"';
                            $insert['expire_date'] = 'expire_date = "' . $bd['expire'] . '"';
                            $insert['delivery_price'] = 'delivery_price = ' . $bd['delivery_price'];
                            $insert['currency'] = 'currency = "' . $bd['currency'] . '"';
                            $query = 'INSERT INTO ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . "\n" .
                                     'SET ' . implode(", ", $insert) . "\n" .
                                     'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);
                            $this->registry['db']->Execute($query);
                        } else {
                            $db->FailTrans();
                            $this->raiseError('error_warehouse_quantity', 'quantity', 0,
                                              array($gt2row['article_name'], $this->i18n('finance_warehouses_documents_article'), $row));
                        }
                    }
                } else {
                    $db->FailTrans();
                    $this->raiseError('error_warehouse_quantity', 'quantity', 0,
                                      array($gt2row['article_name'], $this->i18n('finance_warehouses_documents_article'), $row));
                }
            }

            if ($db->HasFailedTrans()) {
                $db->CompleteTrans();
                return false;
            }

            $qParams = array(
                'nom_id' => $article_ids,
                'force' => true,
                'get_zeros' => true,
                'create_key' => $create_key,
            );
            $new_warehouse->getAvailableQuantity($qParams);
            //save warehouse history entry
            $new_warehouse->set('warehouse_document_id', $this->get('id'), true);
            $new_warehouse->set('warehouse_document_action', $this->registry['action'], true);
            require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses.history.php');
            $audit_parent = Finance_Warehouses_History::saveData($this->registry,
                                                                 array('action_type' => 'change_quantities',
                                                                       'new_model' => $new_warehouse,
                                                                       'old_model' => $old_warehouse
                                                                 ));

            unset($old_warehouse);
            unset($new_warehouse);

        } elseif ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
            $relatives = $this->getRelatives();
            $set = array(
                'annulled = NOW()',
                'annulled_by = ' . $this->registry['currentUser']->get('id'),
                'status = "finished"',
                'status_modified = NOW()',
                'status_modified_by = ' . $this->registry['currentUser']->get('id'),
            );
            //we will annul the differences documents first
            if (!empty($relatives['child'])) {
                //there should be max two difference documents (record of missing stock or record of surplus stock)
                foreach ($relatives['child'] as $diff) {
                    if (is_object($diff)) {
                        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "\n" .
                                'SET ' . implode(', ', $set) . "\n" .
                                'WHERE id=' . $diff->get('id');
                        $db->Execute($query1);
                    }
                }
            }
        }
        $set = array();
        $set['annulled'] = sprintf("annulled='%s'", date('Y-m-d H:i:s'));
        $set['annulled_by'] = sprintf("annulled_by=%d", $this->registry['currentUser']->get('id'));
        if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
            $set['status'] = 'status = "finished"';
            $set['status_modified'] = 'status_modified = NOW()';
            $set['status_modified_by'] = 'status_modified_by = ' . $this->registry['currentUser']->get('id');
        }
        $query1 = 'UPDATE ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        $filters = array('where' => array('fwd.id = ' . $this->get('id')));
        $new_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
        $new_warehouses_document->getGT2Vars();
        $new_warehouses_document->sanitize();

        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php';
        $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry, array('action_type' => 'annul', 'new_model' => $new_warehouses_document, 'old_model' => $old_warehouses_document));

        if ($this->get('type') == PH_FINANCE_TYPE_HANDOVER) {
            //set parent's handovered status
            $relatives = $this->getRelatives('parent');
            foreach ($relatives['parent'] as $parent) {
                if (in_array($parent->modelName, array('Finance_Incomes_Reason', 'Finance_Expenses_Reason'))) {
                    $parent->defineHandoveredStatus();
                    $parent->setHandoveredStatus();
                }
            }
        } elseif ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
            // unlock warehouse or lock it with another unfinished inspection
            $this->updateWarehouseLocked();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        if (!$dbTransError) {
            foreach (array('annulled', 'annulled_by') as $prop) {
                $this->set($prop, $new_warehouses_document->get($prop), true);
            }
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * check for annul
     */
    public function allowAnnul() {

        if ($this->get('annulled_by') == 0 && (
            $this->get('type') == PH_FINANCE_TYPE_HANDOVER && $this->get('status') == 'finished' ||
            $this->get('type') == PH_FINANCE_TYPE_INSPECTION && $this->get('status') != "finished")
        ) {
            return true;
        }

        return false;
    }

    /**
     * Update field `user_permissions`
     */
    public function updateUserPermissions() {
        $db = $this->registry['db'];
        $query = 'SELECT DISTINCT assigned_to ' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_DOCUMENTS_ASSIGNMENTS . "\n" .
                 'WHERE model="Finance_Warehouses_Document" AND parent_id=' . $this->get('id') . "\n";
        $record = $db->GetCol($query);
        if (count($record)) {
            $user_permissions = "'," . implode(',', $record) . ",'";
        } else {
            $user_permissions = 'NULL';
        }
        $query = "UPDATE " . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS .
                 " SET user_permissions = " . $user_permissions . " WHERE id=" . $this->get('id');
        $db->Execute($query);
    }

    /**
     * Checks permitted layouts
     *
     * (non-PHPdoc)
     * @see _libs/inc/mvc/Model::getPermittedLayouts()
     * @param string $mode - action name
     * @param string $model - not used in overwrite method
     * @return array - 'view' layouts or 'edit' layouts or array of both
     */
    public function getPermittedLayouts($mode = '', $model = '') {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        //if validLogin or automation user for crontab
        if ($this->registry['validLogin'] || ($this->registry['currentUser'] && $this->registry['currentUser']->get('id') == PH_AUTOMATION_USER)) {
            if (!$this->isDefined('layouts_view')) {
                $groups = $this->registry['currentUser']->getGroups();
                if (count($groups)) {
                    // check if current user is assigned as observer of records of this type
                    /* $module = 'finance';
                    $set_observer =
                        in_array('observer', $this->registry['config']->getParamAsArray($module, 'assignment_types_' . $this->get('type'))) &&
                        $this->registry['currentUser']->getPersonalSettings($module, 'set_observer'); */
                    // NOTE: default assignments cannot be specified in personal settings yet
                    // as warehouse documents are created in different ways and not just by add method
                    // so default assignment should be applied in all of them
                    $set_observer = false;

                    // make sure model has all types of assignments
                    if ($this->get('id')) {
                        if (!$this->isDefined('assignments_responsible')) {
                            $this->getAssignments('responsible');
                        }
                        if (!$this->isDefined('assignments_decision')) {
                            $this->getAssignments('decision');
                        }
                        if (!$this->isDefined('assignments_observer')) {
                            $this->getAssignments('observer');
                        }
                        if (!$this->isDefined('assignments_owner')) {
                            $this->getAssignments();
                        }
                    }

                    //get rights for layouts view
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="view")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval($this->get('type')) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="view"' . "\n";
                    $records = $this->registry['db']->GetAll($query);

                    $layouts_view_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_view = array();
                    foreach ($layouts_view_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_view[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_view[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_view[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_view', $layouts_view);

                    //get rights for layouts edit
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="edit")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND l.model_type="' . intval($this->get('type')) . '" AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="edit"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_edit_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_edit = array();
                    foreach ($layouts_edit_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_edit[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_edit[] = $layout_id;
                                }
                            } else {
                                if ((in_array('added', $lva)) ||
                                    (in_array('observer', $lva) && $set_observer)) {
                                    $layouts_edit[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_edit', $layouts_edit);
                } else {
                    $this->set('layouts_view', array());
                    $this->set('layouts_edit', array());
                }
            }
        } else {
            $this->set('layouts_view', array());
            $this->set('layouts_edit', array());
        }

        if ($sanitized) {
            $this->sanitize();
        }

        if ($mode) {
            return $this->get('layouts_' . $mode);
        } else {
            return array($this->get('layouts_view'), $this->get('layouts_edit'));
        }
    }

    /**
     * Checks permissions for certain action
     *
     * @param string $action - action name
     * @param string $modulePermissionsKey - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     * @throws Exception
     */
    public function checkPermissions($action, $modulePermissionsKey = 'finance_warehouses_documents', $force = false) {
        // Log invalid $action params
        // TODO: Set type hint for $action, after setting type hint for it in the Model->checkPermissions method too
        if (!is_string($action)) {
            General::log(
                $this->registry,
                'Finance_Warehouses_Document->checkPermissions() called',
                sprintf(
                    <<<LOG
                    Action: %s
                    Module Permissions Key: %s
                    Force: %s
                    Warehouse document ID: %s
                    Backtrace:
                    %s
                    LOG,
                    var_export($action, true),
                    var_export($modulePermissionsKey, true),
                    var_export($force, true),
                    $this->get('id'),
                    print_r(debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS), true)
                )
            );
        }

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'locked') {
                //locked status
                switch ($action) {
                //forbidden actions
                case 'edit':
                    if ($this->get('type') != PH_FINANCE_TYPE_COMMODITIES_TRANSFER) {
                        return false;
                        break;
                    }
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } elseif ($this->get('status') == 'finished') {
                //finished status
                switch ($action) {
                //forbidden actions
                case 'edit':
                case 'observer':
                    return false;
                    break;
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            } else {
                //opened status
                switch ($action) {
                //forbidden actions
                //allowed actions
                default:
                    return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                        $this,
                        $action,
                        $modulePermissionsKey,
                        $force
                    );
                }
            }
        } else {
            return PermissionsChecker::getCurrentUserInstance()->isAllowed(
                $this,
                $action,
                $modulePermissionsKey,
                $force
            );
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Prepares empoyees for finance warehouses document of type inspection or waste.
     *
     * @return bool - result of the operation
     */
    private function prepareEmployees() {
        $employees = $this->get('employees');
        // if coming from POST, employees property is an array of ids
        if (is_array($employees)) {
            $employees = implode(',', array_diff($employees, array('')));
        }

        $employees_list = '';

        if ($employees) {
            $employees = '(' . $employees . ')';

            // store old value of flag while searching for employees
            $getOneRequested = $this->registry->get('getOneRequested');
            $this->registry->set('getOneRequested', false, true);

            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('where' => array('c.id IN ' . $employees),
                             'sort' => array('c.id IN ' . $employees),
                             'sanitize' => true);
            $records = Customers::search($this->registry, $filters);

            // restore flag in registry
            $this->registry->set('getOneRequested', $getOneRequested, true);

            $employees = array();
            $i = 0;
            foreach ($records as $record) {
                $i++;
                $employees[$record->get('id')] = array('id' => $record->get('id'),
                                                       'code' => $record->get('code'),
                                                       'name' => $record->get('name') . ($record->get('lastname') ? ' ' . $record->get('lastname') : '')
                                                       );
                $employees_list .= sprintf('%s. %s', $i, $employees[$record->get('id')]['name']);
                if ($i < count($records)) {
                    $employees_list .= '<br />';
                }
            }
        } else {
            $employees = array();
        }

        $this->set('employees_names', $employees, true);
        $this->set('employees_list', $employees_list, true);

        return true;
    }

    /**
     * Prepares current_quantity and difference fields of inspection
     */
    public function prepareGT2DynamicFields() {
        $model_lang = $this->registry['request']->get('model_lang') ?: $this->registry['lang'];
        $model = new Finance_Documents_Type(
            $this->registry,
            array(
                'id' => $this->get('type'),
                'model' => $this->modelName,
                'model_lang' => $model_lang,
            ));
        $qty_fields = $model->getGT2DynamicFields();
        if (!$qty_fields || !$this->isDefined('grouping_table_2')) {
            return;
        }

        $gt2 = $this->get('grouping_table_2');
        $hidden = !$this->registry['currentUser']->checkRights('finance_warehouses_documents', 'setstatus');
        foreach ($qty_fields as $field) {
            $field = $field + array(
                'id' => $field['var_id'],
                'type' => 'text',
                'hidden' => $hidden,
                'readonly' => 1,
                'width' => '50',
                'text_align' => 'right',
                'js_filter' => 'insertOnlyFloats',
                'label' => array_key_exists($model_lang, $field['labels']) ? $field['labels'][$model_lang] : '',
            );
            unset($field['labels']);
            General::injectInArray(array($field['name'] => $field), $gt2['vars'], $field['position'], $field['needle']);
        }

        $this->set('grouping_table_2', $gt2, true);
    }

    /**
     * Prepares up-to-date available quantity of model is in 'opened' status and sets it into GT2.
     * Prepares current_quantity and difference for inspection and sets them into GT2
     */
    public function prepareAvailableQuantity() {

        $gt2 = $this->get('grouping_table_2');
        if (!$gt2) {
            return;
        }

        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // get precision for GT2 quantity
        $gt2_quantity_precision = $this->registry['config']->getParam('precision', 'gt2_quantity');
        $gt2_quantity_prec_format = '%.' . $gt2_quantity_precision . 'F';

        $prec = $this->registry['config']->getSectionParams('precision');

        if ($this->get('status') == 'opened') {
            require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
            $filters = array('where' => array('fwh.id = ' . $this->get('warehouse')));
            $warehouse = Finance_Warehouses::searchOne($this->registry, $filters);

            foreach ($gt2['values'] as $key => $values) {
                if (empty($values)) {
                    continue;
                }
                $available = $warehouse->getAvailableQuantity(array('nom_id' => $values['article_id'], 'customer' => $this->get('customer')));
                if (!empty($available[$values['article_id']])) {
                    $gt2['values'][$key]['available_quantity'] = sprintf($gt2_quantity_prec_format, $available[$values['article_id']]['quantity']);
                    if (!empty($values['batch_data'])) {
                        $current = 0;
                        foreach ($gt2['values'][$key]['batch_data'] as $k => $v) {
                            $bu = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                $this->registry['db'],
                                [
                                    'batch_id'        => $v['batch'],
                                    'expire_date'     => $v['expire'],
                                    'cstm_number'     => $v['custom'],
                                    'serial_number'   => $v['serial'],
                                    'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($v['delivery_price'], $prec['gt2_rows'])),
                                    'currency'        => $v['currency'],
                                ]
                            );
                            foreach ($available[$values['article_id']]['batch_data'] as $k1 => $v1) {
                                $au = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                    $this->registry['db'],
                                    [
                                        'batch_id'        => $v1['batch'],
                                        'expire_date'     => $v1['expire'],
                                        'cstm_number'     => $v1['custom'],
                                        'serial_number'   => $v1['serial'],
                                        'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($v1['delivery_price'], $prec['gt2_rows'])),
                                        'currency'        => $v1['currency'],
                                    ]
                                );
                                if ($bu == $au) {
                                    $gt2['values'][$key]['batch_data'][$k]['available_quantity'] = sprintf($gt2_quantity_prec_format, $v1['quantity']);
                                    $current += $v1['quantity'];
                                    break;
                                }
                            }
                        }
                    } else {
                        $current = $gt2['values'][$key]['available_quantity'];
                    }
                    $gt2['values'][$key]['current_quantity'] = sprintf($gt2_quantity_prec_format, $current);
                } else {
                    $gt2['values'][$key]['available_quantity'] = 0;
                }
            }
        }

        if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
            foreach ($gt2['values'] as $k => $v) {
                if (empty($v)) {
                    continue;
                }
                if (!isset($v['current_quantity'])) {
                    if (!empty($v['batch_data'])) {
                        $v['current_quantity'] = 0;
                        foreach ($v['batch_data'] as $k1 => $v1) {
                            $v['current_quantity'] += $v1['available_quantity'];
                        }
                    } else {
                        $v['current_quantity'] = $v['available_quantity'];
                    }
                    $gt2['values'][$k]['current_quantity'] = sprintf($gt2_quantity_prec_format, $v['current_quantity']);
                }

                $gt2['values'][$k]['difference'] = sprintf($gt2_quantity_prec_format, $v['quantity'] - $v['current_quantity']);
            }
        }

        $this->set('grouping_table_2', $gt2, true);

        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
    }

    /**
     * Gets batch data (if any) for warehouse document and sets it into
     * corresponding rows of GT2 data
     */
    public function getBatchesData($round_price = false) {

        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $table = $this->get('grouping_table_2');
        if ($this->registry['action'] == 'generate') {
            $vars_settings = $this->getBatchVarsSettings(true);
        } else {
            $vars_settings = $this->getBatchVarsSettings();
        }
        //TODO: check this parameter
        $round_price = true;
        if (!empty($table['rows'])) {
            //get extended data from the DB
            $prec = $this->registry['config']->getSectionParams('precision');
            $query = 'SELECT ROUND(gt2.quantity, ' . $prec['gt2_quantity'] . ') as quantity,' . "\n" .
                     '  ROUND(gt2.available_quantity, ' . $prec['gt2_quantity'] . ') as available_quantity,' . "\n".
                     '    gt2.batch_id as batch, gt2.expire_date as expire, gt2.cstm_number as custom,' . "\n" .
                     '    gt2.serial_number as serial, fb.code as batch_code,' . "\n" .
                     ($round_price ? 'ROUND(gt2.delivery_price, ' . $prec['gt2_rows'] . ') as delivery_price,' : 'gt2.delivery_price,') . "\n" .
                     '    gt2.currency, gt2.parent_id, ' . $this->get('warehouse') . ' as warehouse' . "\n" .
                     'FROM ' . DB_TABLE_GT2_BATCHES_DATA . ' gt2' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_BATCHES . ' fb' . "\n" .
                     '    ON gt2.batch_id = fb.id' . "\n" .
                     ' WHERE gt2.parent_id IN (' . implode(', ', $table['rows']) . ')';
            $batch_data = $this->registry['db']->GetAll($query);
            foreach ($batch_data as $bd) {
                if (empty($table['values'][$bd['parent_id']]['batch_data'])) {
                    $table['values'][$bd['parent_id']]['batch_data'] = array();
                    $query = 'SELECT has_batch, has_serial, has_expire FROM ' . DB_TABLE_NOMENCLATURES . "\n" .
                             'WHERE `subtype`="commodity" AND id = ' . $table['values'][$bd['parent_id']]['article_id'];
                    $nom_settings = $this->registry['db']->GetRow($query);
                    $table['values'][$bd['parent_id']] = array_merge($table['values'][$bd['parent_id']], $nom_settings);
                }
                $table['values'][$bd['parent_id']]['batch_data'][] = $bd;
                if (!isset($table['values'][$bd['parent_id']]['vars_settings'])) {
                    $table['values'][$bd['parent_id']]['vars_settings'] = $vars_settings;
                }
            }
        }
        $this->set('grouping_table_2', $table, true);
        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
    }

    /**
     * Prepare batches data from $_REQUEST for saving for different warehouses documents
     *
     * @return boolean - result of the operation
     */
    public function prepareRequestedBatches() {
        if (!$this->get('warehouse')) {
            $this->raiseError(
                'error_finance_warehouses_documents_no_warehouse_specified',
                'warehouse_data',
                null,
                array($this->getLayoutName('warehouse_data'))
            );
            return false;
        }
        if ($this->get('type') == PH_FINANCE_TYPE_COMMODITIES_RELEASE) {
            return true;
        }
        $gt2 = $this->get('grouping_table_2');
        if (empty($gt2)) {
            $gov = $this->registry->get('get_old_vars');
            $this->registry->set('get_old_vars', false, true);
            $gt2 = $this->getGT2Vars();
            $this->registry->set('get_old_vars', $gov, true);
        }
        /** @var Request $request */
        $request = &$this->registry['request'];
        if (empty($gt2['quantity_is_set'])) {
            $quantities = $request->get('quantity');
        } else {
            $quantities = array();
        }
        $article_ids = $request->isRequested('article_id') ? $request->get('article_id') : array();
        $deleted = $request->get('deleted');
        //$prices = $request->get($gt2['calculated_price']);
        $prices = $request->get('price_with_discount');
        $wid = $this->get('warehouse');

        $prec = $this->registry['config']->getSectionParams('precision');
        //$vars_settings = $this->getBatchVarsSettings();

        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
        /** @var Finance_Warehouse $warehouse */
        $warehouse = Finance_Warehouses::searchOne(
            $this->registry,
            array(
                'where' => array(
                    "fwh.id = '$wid'",
                ),
            )
        );

        $filters = array(
            'where' => array(
                'n.id IN ("' . implode('", "', $article_ids) . '")',
            ),
        );
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        $models = Nomenclatures::search($this->registry, $filters);
        /** @var Nomenclature[] $articles */
        $articles = array();
        foreach ($models as $m) {
            $articles[$m->get('id')] = $m;
        }

        $reservation = false;
        $customer = false;
        $zero = false;
        if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
            //get batches really present into the warehouse
            $query = 'SELECT batch_id FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . ' WHERE parent_id = ' . $wid;
            $real_batches = $this->registry['db']->GetCol($query);
            $not_real_batches = array();
        } elseif ($this->get('type') == PH_FINANCE_TYPE_COMMODITIES_RESERVATION) {
            $reservation = $this->get('id');
        } elseif ($this->get('type') == PH_FINANCE_TYPE_HANDOVER) {
            $customer = $this->get('customer');
        } elseif ($this->get('type') == PH_FINANCE_TYPE_INSPECTION_SURPLUS) {
            //get batches really present into the warehouse
            $query = 'SELECT batch_id FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . ' WHERE parent_id = ' . $wid;
            $real_batches = $this->registry['db']->GetCol($query);
            $not_real_batches = array();
            $reservation = 'none';
            $gt2['rows'] = array();
        } elseif ($this->get('type') == PH_FINANCE_TYPE_INSPECTION_MISSING) {
            $gt2['rows'] = array();
            $reservation = 'none';
        }

        //get available quantities in the warehouse
        $unique = array();
        $qParams = array(
            'nom_id' => $article_ids,
            'force' => true,
            'customer_id' => $customer,
            'reservation' => $reservation,
            'get_zeros' => $zero,
        );
        $available_articles = array_filter($article_ids) ? $warehouse->getAvailableQuantity($qParams) : array();

        if ($this->get('direction') != 'incoming' && $this->get('type') != PH_FINANCE_TYPE_INSPECTION) {
            //create unique key for each quantity found
            foreach ($available_articles as $article_id => $data) {
                if ($articles[$article_id]->get('has_batch')) {
                    foreach ($data['batch_data'] as $b => $bd) {
                        $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'parent_id'       => $wid,
                                'nomenclature_id' => $article_id,
                                'batch_id'        => $bd['batch'],
                                'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                'serial_number'   => $bd['serial'],
                                'cstm_number'     => $bd['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($bd['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $bd['currency'],
                            ]
                        );
                        if (!isset($unique[$k])) {
                            $unique[$k] = 0;
                        }
                        $unique[$k] += $bd['quantity'];
                    }
                }
            }
        }

        /**
         * Existing batches are prepared before reverse incoming handover is
         * created in order to validate quantities and batch data against
         * prevoius outgoing+incoming handovers
         */
        if ($this->registry->isRegistered('existing_batches')) {
            $existing_batches = $this->registry->get('existing_batches');
        }

        /**
         * Initial value to start assigning ids of new batches from. When
         * batch data cannot belong to the same batch, variable value is
         * incremented and assigned as id of next allocated new batch.
         *
         * @var number $max_batch
         * @var number $batch
         */
        $max_batch = $batch = Finance_Warehouses::getNextBatch($this->registry);

        /**
         * Collected info for processed batch data for the whole document:
         *
         * @var array $articles_prices - used for price validation: keys are article ids, values are arrays with batch ids as keys and price+currency as values
         * @var array $batches_data - collected batch data for the whole table [gt2 row id][batch row index][data for a batch row]
         * @var array $used_codes - batches to be created: keys are assigned batch ids, values are specified/autogenerated batch codes
         * @var array $used_manual_codes - only the manually entered codes from $used_codes
         * @var array $expire_batches - used for expiry date validation: keys are unique expire_date values, values are an index array of batch ids used for batches with this expire date
         */
        $articles_prices = $batches_data = $used_codes = $used_manual_codes = $expire_batches = array();

        $error = false;
        $i = 0;
        foreach ($article_ids as $key => $val) {
            $wh_quantities = 0;
            if (empty($deleted[$key])) {
                $i++;
            }
            if ($val && !$articles[$val]->get('has_batch') || !empty($deleted[$key])) {
                //non batch article or deleted row
                continue;
            }

            if (!empty($gt2['quantity_is_set'])) {
                if (empty($gt2['values'][$key]['quantity'])) {
                    //this row is not present here
                    continue;
                }
                $quantities[$key] = $gt2['values'][$key]['quantity'];
            }

            // avoid a situation where row has batch article and main quantity
            // but no corresponding batch data in request
            // in case of an interface error or when prepared from a plugin
            if ($val && $articles[$val]->get('has_batch') && bccomp($quantities[$key], 0, $prec['gt2_quantity']) == 1 && !$request->isRequested('article_' . $key . '_wh')) {
                $this->registry['messages']->setError(
                    $this->i18n(
                        'error_system_calculation',
                        array(
                            sprintf('<a href="#article_name_%d">%s</a>', $i, $articles[$val]->get('name'))
                        )
                    )
                );
                $error = true;
            }

            //get batches details if present
            $batch_warehouses = $request->get('article_' . $key . '_wh');
            $batch_expire = $request->get('article_' . $key . '_expire') ? : array();
            $batch_quantities = $request->get('article_' . $key . '_quantity');
            $batch_serials = $request->get('article_' . $key . '_serial') ? : array();
            $batch_custom = $request->get('article_' . $key . '_custom');
            $batch_codes = $request->get('article_' . $key . '_batch') ? : array();
            $batch_prices = $request->get('article_' . $key . '_delivery_price') ? : array();
            $batch_currencies = $request->get('article_' . $key . '_currency') ? : array();

            //remove the whitespaces in the codes of the batches
            $batch_codes = array_map('trim', $batch_codes);

            if (!empty($batch_warehouses)) {
                $batch_row_idx_position_array = array_keys($batch_warehouses);
                $batch_warehouses = array_filter($batch_warehouses, function($el) use($wid) {return $el == $wid;});
            }
            if (empty($batch_warehouses)) {
                //this article is not present in warehouses chosen
                //or warehouses have not been chosen
                continue;
            }
            if ($this->get('direction') == 'incoming' || $this->get('type') == PH_FINANCE_TYPE_INSPECTION
                || $this->get('type') == PH_FINANCE_TYPE_COMMODITIES_RESERVATION) {
                $batch_codes_custom = $request->get('article_' . $key . '_batch_isCustom') ? : array();

                if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION) {
                    //make some tricks to validate batches data during inspection edit
                    //set the batch as custom(this is a new batch added during inspection add/edit and saved after)
                    foreach ($batch_warehouses as $idx => $wh) {
                        if (empty($batch_codes_custom[$idx]) && !empty($batch_codes[$idx]) && !in_array($batch_codes[$idx], $real_batches)) {
                            $batch_codes_custom[$idx] = true;
                            $not_real_batches[$batch_codes[$idx]] = $batch_codes[$idx] = Finance_Warehouses::getBatchCode($this->registry, $batch_codes[$idx]);
                        }
                    }
                }

                $erred_articles = array();
                if ($this->get('link_to_model_name') == 'Finance_Incomes_Reason' || $this->get('link_to_model_name') == 'Contract') {
                    foreach ($batch_quantities as $bq => $qq) {
                        if ((!$qq || empty($batch_codes[$bq]) && !isset($erred_articles[$val]))) {
                            if ($this->get('direction') == 'incoming' && !$qq) {
                                unset($batch_quantities[$bq]);
                                continue;
                            }
                            $erred_articles[$val] = true;
                            $this->registry['messages']->setError($this->i18n('error_finance_whd_empty_batch', array($articles[$val]->get('name'))));
                            $row_error = $error = true;
                        }
                    }
                }

                if ($articles[$val]->get('has_expire')) {
                    $batch_quantities = array_intersect_key($batch_quantities, $batch_warehouses);
                    if ($this->get('type') != PH_FINANCE_TYPE_INSPECTION && $this->get('type') != PH_FINANCE_TYPE_COMMODITIES_RESERVATION) {
                        //get not empty quantities
                        $batch_quantities = array_filter($batch_quantities, function($el) {return $el > 0;});
                    }
                    //preserve dates for the rows with quantities and warehouse chosen
                    $batch_expire = array_intersect_key($batch_expire, $batch_quantities);
                    //add not entered new batch to the array with custom batches
                    $batch_codes_custom = $batch_codes_custom + array_filter($batch_codes, function ($e) {return empty($e);});
                    //get the new unique dates for this article
                    $exp_batches = array_intersect_key($batch_expire, $batch_codes_custom);
                    $exp_batches = array_flip(array_values(array_unique($exp_batches)));
                    if (count($exp_batches)) {
                        //this article will need several batches (expiry date as key, batch row index as value)
                        foreach ($exp_batches as $d => $ind) {
                            if (!empty($d) && !isset($expire_batches[$d])) {
                                $expire_batches[$d] = array($max_batch);
                                $max_batch++;
                            }
                        }
                    }
                }

                $j = 0;
                foreach ($batch_warehouses as $idx => $wh) {
                    $j ++;
                    // calculate what the batch row index will be after page reload (there might have been deleted rows)
                    $idx_position = array_search($idx, $batch_row_idx_position_array) + 1;
                    $row_error = false;
                    if (empty($batch_quantities[$idx]) || $batch_quantities[$idx] <= 0) {
                        if (in_array($this->get('type'), array(PH_FINANCE_TYPE_INSPECTION, PH_FINANCE_TYPE_COMMODITIES_RESERVATION))) {
                            $batch_quantities[$idx] = 0;
                        } else {
                            //the quantities have been filtered already, but.....
                            continue;
                        }
                    }
                    $wh_quantities += $batch_quantities[$idx];

                    //set the price to be rounded as in the DB
                    $price = isset($batch_prices[$idx]) ? $batch_prices[$idx] : $prices[$key];
                    $price = round($price, $prec['gt2_rows']);

                    $currency = isset($batch_currencies[$idx]) ? $batch_currencies[$idx] : $this->get('currency');

                    $batches_data[$key][$idx] = array(
                        'custom' => $batch_custom[$idx],
                        'quantity' => $batch_quantities[$idx],
                        'warehouse' => $wh,
                        'serial' => '',
                        'expire' => '0000-00-00',
                        'delivery_price' => $price,
                        'currency' => $currency,
                    );
                    if (empty($batch_codes_custom[$idx]) && !empty($batch_codes[$idx])) {
                        //we have chosen some of the existing batches
                        //so all the fields are filled already and readonly(or dropdown)
                        //we don't have to validate, but just set the values
                        $batches_data[$key][$idx]['serial'] = !empty($batch_serials[$idx]) ? $batch_serials[$idx] : '';
                        $batches_data[$key][$idx]['expire'] = !empty($batch_expire[$idx]) ? $batch_expire[$idx] : '0000-00-00';
                        $batches_data[$key][$idx]['batch'] = $batch_codes[$idx];
                        $batches_data[$key][$idx]['batch_code'] = Finance_Warehouses::getBatchCode($this->registry, $batch_codes[$idx]);

                        if (isset($existing_batches)) {
                            //we are here ONLY when return articles for an incomes reason or contract
                            $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                $this->registry['db'],
                                [
                                    'nomenclature_id' => $val,
                                    'batch_id'        => $batches_data[$key][$idx]['batch'],
                                    'expire_date'     => $batches_data[$key][$idx]['expire'] ? : '0000-00-00',
                                    'serial_number'   => $batches_data[$key][$idx]['serial'],
                                    'cstm_number'     => $batches_data[$key][$idx]['custom'],
                                    'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($batches_data[$key][$idx]['delivery_price'], $prec['gt2_rows'])),
                                    'currency'        => $batches_data[$key][$idx]['currency'],
                                ]
                            );
                            if (!isset($existing_batches[$uk]['quantity']) || bccomp($existing_batches[$uk]['quantity'], $batches_data[$key][$idx]['quantity'], $prec['gt2_quantity']) < 0) {
                                //requested quantity is more than the available quantity
                                $this->registry['messages']->setError($this->i18n('error_invalid_batch_quantity', array(
                                    $articles[$val]->get('name'), $warehouse->get('name'), sprintf('<a href="#article_%d_quantity_%d">%d.%d</a>', $key, $j, $i, $j))));
                                if (!isset($existing_batches[$uk]['quantity'])) {
                                    $existing_batches[$uk]['quantity'] = 0;
                                }
                                $existing_batches[$uk]['quantity'] -= $batches_data[$key][$idx]['quantity'];
                                $error = $row_error = true;
                            }
                            // validate per-row (same article can be present in multiple rows of reason)
                            if (array_key_exists('rows_quantity', $existing_batches[$uk])) {
                                if (!array_key_exists($key, $existing_batches[$uk]['rows_quantity'])) {
                                    $existing_batches[$uk]['rows_quantity'][$key] = 0;
                                }
                                if (bccomp($existing_batches[$uk]['rows_quantity'][$key], $batches_data[$key][$idx]['quantity'], $prec['gt2_quantity']) < 0) {
                                    if (!$row_error) {
                                        // specified batch quantity is more than handed over for GT2 row
                                        $this->registry['messages']->setError($this->i18n('error_invalid_batch_quantity', array(
                                            $articles[$val]->get('name'), $warehouse->get('name'), sprintf('<a href="#article_%d_quantity_%d">%d.%d</a>', $key, $j, $i, $j))));
                                        $error = $row_error = true;
                                    }
                                }
                                $existing_batches[$uk]['rows_quantity'][$key] -= $batches_data[$key][$idx]['quantity'];
                            }
                            $existing_batches[$uk]['quantity'] -= $batches_data[$key][$idx]['quantity'];
                        }

                        $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'parent_id'       => $wid,
                                'nomenclature_id' => $val,
                                'batch_id'        => $batches_data[$key][$idx]['batch'],
                                'expire_date'     => $batches_data[$key][$idx]['expire'] ? : '0000-00-00',
                                'serial_number'   => $batches_data[$key][$idx]['serial'],
                                'cstm_number'     => $batches_data[$key][$idx]['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($batches_data[$key][$idx]['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $batches_data[$key][$idx]['currency'],
                            ]
                        );
                        if (isset($unique[$uk]) && isset($batches_data[$key][$unique[$uk]]) && $unique[$uk] < $idx) {
                            // we have to check that the batches data at specified position has the same unique key as current batches data
                            // and that it was not set from a previous GT2 row processing
                            $idx_check = $unique[$uk];
                            $uk_check = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                $this->registry['db'],
                                [
                                    'parent_id'       => $wid, // warehouse is the same - we are processing data per one warehouse within the whole function
                                    'nomenclature_id' => $val, // article id is the same
                                    'batch_id'        => $batches_data[$key][$idx_check]['batch'],
                                    'expire_date'     => $batches_data[$key][$idx_check]['expire'] ? : '0000-00-00',
                                    'serial_number'   => $batches_data[$key][$idx_check]['serial'],
                                    'cstm_number'     => $batches_data[$key][$idx_check]['custom'],
                                    'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($batches_data[$key][$idx_check]['delivery_price'], $prec['gt2_rows'])),
                                    'currency'        => $batches_data[$key][$idx_check]['currency'],
                                ]
                            );

                            //we have a row with the same key....just merge the quantities
                            // !!! only when it belongs to the same GT2 row, not another one with the same article !!!
                            if ($uk_check == $uk) {
                                $batches_data[$key][$unique[$uk]]['quantity'] += $batches_data[$key][$idx]['quantity'];
                                unset($batches_data[$key][$idx]);
                            }
                        } else {
                            $unique[$uk] = $idx;
                        }
                        continue;
                    }
                    if ($articles[$val]->get('has_serial')) {
                        if (empty($batch_serials[$idx])) {
                            //serial number is a mandatory field
                            $this->registry['messages']->setError($this->i18n('error_finance_whd_empty_serial',
                                array($articles[$val]->get('name'), $warehouse->get('name'),
                                sprintf('<a href="#article_%d_serial_%d">%d.%d</a>', $key, $idx_position, $i, $idx_position))));
                            $row_error = $error = true;
                        } else {
                            $batches_data[$key][$idx]['serial'] = $batch_serials[$idx];
                        }
                    }
                    $search_batch = $batch;
                    if ($articles[$val]->get('has_expire')) {
                        if (empty($batch_expire[$idx])) {
                            //expire date is a mandatory field
                            $this->registry['messages']->setError($this->i18n('error_finance_whd_empty_expire',
                                array($articles[$val]->get('name'), $warehouse->get('name'),
                                sprintf('<a href="#article_%d_expire_formatted_%d">%d.%d</a>', $key, $idx_position, $i, $idx_position))));
                            $row_error = $error = true;
                        } else {
                            $batches_data[$key][$idx]['expire'] = $batch_expire[$idx];
                            if (!empty($expire_batches) && isset($expire_batches[$batch_expire[$idx]])) {
                                //define batches in function of the expire date
                                if (!empty($batch_codes_custom[$idx])) {
                                    // we have custom batch here
                                    // search for batch code in the already used codes assigned to batch ids
                                    $used_batch = !empty($batch_codes[$idx]) ? array_search($batch_codes[$idx], $used_codes) : false;
                                    if ($used_batch === false) {
                                        //this custom batch has not been used yet
                                        //assign a new batch for current expire date
                                        $batches_data[$key][$idx]['batch'] = $expire_batches[$batch_expire[$idx]][] = $max_batch = $max_batch + 1;
                                        //we don't need to search for this batch in the prices - it is created here
                                        $search_batch = false;
                                        if (!empty($batch_codes[$idx])) {
                                            $used_manual_codes[$max_batch] = $used_codes[$max_batch] = $batch_codes[$idx];
                                        } else {
                                            $used_codes[$max_batch] = $batch_codes[$idx] = uniqid();
                                        }
                                    } else {
                                        // we need to check if this batch code is used with a different expire date
                                        // for the same article (for different articles that is acceptable)
                                        // so we will have to go through all other expire dates and search for the batch id assigned to this batch code;
                                        // if found, we will have to go through all prepared batch data so far
                                        // and check if that combination of expire date and batch id was used for the same article as the current one
                                        foreach ($expire_batches as $expire_date => $batches) {
                                            if ($expire_date != $batch_expire[$idx] && in_array($used_batch, $batches)) {
                                                foreach ($batches_data as $row_key => $row_batches_data) {
                                                    // gt2 row with same article (or maybe current gt2 row)
                                                    if (!empty($article_ids[$row_key]) && $article_ids[$row_key] == $val) {
                                                        foreach ($row_batches_data as $batch_row_idx => $rbd) {
                                                            if (!empty($rbd['batch']) && $rbd['batch'] == $used_batch && $rbd['expire'] != $batch_expire[$idx]) {
                                                                // set error message
                                                                if (empty($row_error)) {
                                                                    $this->registry['messages']->setError(
                                                                        $this->i18n('error_finance_whd_batch_codes',
                                                                        array(
                                                                            $articles[$val]->get('name'),
                                                                            $warehouse->get('name'),
                                                                            sprintf('<a href="#article_%d_expire_formatted_%d">%d.%d</a>', $key, $idx_position, $i, $idx_position),
                                                                            $batch_codes[$idx],
                                                                            $this->i18n('finance_warehouses_documents_expire')
                                                                        )));
                                                                }
                                                                $row_error = $error = true;
                                                                // break on first error; do not check $batches_data any more
                                                                break 2;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        // only if check above was ok
                                        if (empty($row_error)) {
                                            if (!in_array($used_batch, $expire_batches[$batch_expire[$idx]])) {
                                                $expire_batches[$batch_expire[$idx]][] = $used_batch;
                                            }
                                            //used custom batch and the expire date is the same
                                            $search_batch = $batches_data[$key][$idx]['batch'] = $used_batch;
                                        }
                                    }
                                } else {
                                    // this can happen when we have combobox for batch code and leave it empty but specify other parameters for the batch (in inspection)
                                    // we take first allocated batch for the date and set it as search batch
                                    $search_batch = $batches_data[$key][$idx]['batch'] = $expire_batches[$batch_expire[$idx]][0];

                                    // if there is no batch code but the search batch corresponds to a manually entered batch code
                                    // THEN: assign new batch to it
                                    if (array_key_exists($search_batch, $used_codes) && array_key_exists($search_batch, $used_manual_codes)) {
                                        $batches_data[$key][$idx]['batch'] = $max_batch = $max_batch + 1;
                                        $search_batch = false;
                                    }
                                }
                            } else {
                                //assign new batch for current expire date
                                if (empty($expire_batches[$batch_expire[$idx]])) {
                                    $expire_batches[$batch_expire[$idx]] = array();
                                }
                                $batches_data[$key][$idx]['batch'] = $expire_batches[$batch_expire[$idx]][] = $max_batch = $max_batch + 1;

                                //we dont need to search for this batch in the prices - its created here
                                $search_batch = false;
                            }
                        }
                    } else {
                        // batch without expire date
                        $used_batch = !empty($batch_codes[$idx]) ? array_search($batch_codes[$idx], $used_codes) : false;
                        if ($used_batch) {
                            // batch code is already used - get the id
                            $search_batch = $batches_data[$key][$idx]['batch'] = $used_batch;
                        } elseif (array_key_exists($search_batch, $used_codes)) {
                            // search batch id is aready used for another batch code
                            if (!empty($batch_codes[$idx]) || array_key_exists($search_batch, $used_manual_codes)) {
                                // 1) if there is no batch code but the search batch corresponds to a manually entered batch code OR
                                // 2) if there is batch code specified but it is different from the one of search batch
                                // THEN: assign new batch to it
                                if (empty($batch_codes[$idx]) || $batch_codes[$idx] != $used_codes[$search_batch]) {
                                    $batches_data[$key][$idx]['batch'] = $max_batch = $max_batch + 1;
                                    $search_batch = false;

                                    if (!empty($batch_codes[$idx])) {
                                        $used_manual_codes[$max_batch] = $used_codes[$max_batch] = $batch_codes[$idx];
                                    } else {
                                        $used_codes[$max_batch] = $batch_codes[$idx] = uniqid();
                                    }
                                }
                            } else {
                                // there is no batch code specified
                            }
                        } else {
                            $batches_data[$key][$idx]['batch'] = $search_batch;
                        }
                    }

                    // initialize specified batch prices for article
                    if (!array_key_exists($val, $articles_prices)) {
                        $articles_prices[$val] = array();
                    }
                    // $articles_prices sub-arrays:
                    // for each article (this includes multiple GT2 rows having the same article):
                    // keys are batch ids, values are price+currency
                    // this way we can have different batches with the same price+currency (acceptable)
                    // and we can forbid different price+currency for the same batch of the article (not acceptable)
                    if ($search_batch) {
                        //we have to check for several prices for one article

                        if (!array_keys($articles_prices[$val], $price.$currency)) {
                            if (array_key_exists($search_batch, $articles_prices[$val])) {
                                //if the same batch is assigned to another price
                                //create new batch and assign it to the row
                                //or not batch has been assigned
                                $batches_data[$key][$idx]['batch'] = $max_batch = $max_batch + 1;
                            } else {
                                //we get current batch
                                $batches_data[$key][$idx]['batch'] = $search_batch;
                            }
                            //set this batch in the array with prices
                            $articles_prices[$val][$batches_data[$key][$idx]['batch']] = $price.$currency;
                        } else {
                            if (!empty($batches_data[$key][$idx]['batch']) && array_key_exists($batches_data[$key][$idx]['batch'], $articles_prices[$val]) &&
                            $articles_prices[$val][$batches_data[$key][$idx]['batch']] != $price.$currency) {
                                //if the same batch is assigned to another price
                                //create a new batch and assign it to the row
                                $batches_data[$key][$idx]['batch'] = $max_batch = $max_batch + 1;
                            } elseif (empty($batches_data[$key][$idx]['batch'])) {
                                //no expire batch is defined so get the price batch - get first found
                                $batches_data[$key][$idx]['batch'] = array_search($price.$currency, $articles_prices[$val]);
                            }
                        }
                    } elseif (array_key_exists('batch', $batches_data[$key][$idx]) && !array_key_exists($batches_data[$key][$idx]['batch'], $articles_prices[$val])) {
                        //set this batch in the array with prices
                        $articles_prices[$val][$batches_data[$key][$idx]['batch']] = $price.$currency;
                    }

                    if (empty($batch_codes[$idx])) {
                        //batch code has not been set
                        if (isset($used_codes[$batches_data[$key][$idx]['batch']])) {
                            //we have code generated for this batch
                            $batches_data[$key][$idx]['batch_code'] = $used_codes[$batches_data[$key][$idx]['batch']];
                        } else {
                            //we need to generate a new batch code
                            $batches_data[$key][$idx]['batch_code'] = $used_codes[$batches_data[$key][$idx]['batch']] = uniqid();
                        }
                    } else {
                        $batches_data[$key][$idx]['batch_code'] = $batch_codes[$idx];
                        $used_batch = array_search($batch_codes[$idx], $used_codes);
                        if ($used_batch !== false) {
                            //this code has been used already
                            if ($batches_data[$key][$idx]['batch'] == $used_batch) {
                                //this row will be in the same batch....great
                            } else {
                                //rows from different batches and with the same code
                                // set error message only when difference is due to price (if it is due to expiry date, error message was already set)
                                if (!array_key_exists($used_batch, $articles_prices[$val]) || $articles_prices[$val][$used_batch] != $price.$currency) {
                                    $this->registry['messages']->setError(
                                        $this->i18n('error_finance_whd_batch_codes',
                                        array(
                                            $articles[$val]->get('name'),
                                            $warehouse->get('name'),
                                            sprintf('<a href="#article_%d_batch_%d">%d.%d</a>', $key, $idx_position, $i, $idx_position),
                                            $batch_codes[$idx],
                                            $this->i18n('finance_documents_types_gt2_last_delivery_price')
                                        )));
                                }
                                $row_error = $error = true;
                            }
                        } else {
                            //this batch code has not been used yet so add it
                            if (isset($used_codes[$batches_data[$key][$idx]['batch']])) {
                                //the batch for this row is used but with different code
                                if ($batches_data[$key][$idx]['batch'] == $batch) {
                                    //main batch is used -  just assign new batch id
                                    $batches_data[$key][$idx]['batch'] = $max_batch = $max_batch + 1;
                                } else {
                                    //custom assigned batch
                                    $this->registry['messages']->setError(
                                        $this->i18n('error_finance_whd_batch_codes',
                                        array(
                                            $articles[$val]->get('name'),
                                            $warehouse->get('name'),
                                            sprintf('<a href="#article_%d_batch_%d">%d.%d</a>', $key, $idx_position, $i, $idx_position),
                                            $batch_codes[$idx],
                                            $this->i18n('finance_warehouses_documents_batch')
                                        )));
                                    $row_error = $error = true;
                                }
                            }
                            $used_manual_codes[$batches_data[$key][$idx]['batch']] = $used_codes[$batches_data[$key][$idx]['batch']] = $batch_codes[$idx];
                        }
                        if (!empty($not_real_batches) && ($b = array_search($batch_codes[$idx], $not_real_batches))) {
                            // unset data which becomes invalid because batch is set to the old existing id
                            unset($used_codes[$batches_data[$key][$idx]['batch']]);
                            unset($articles_prices[$val][$batches_data[$key][$idx]['batch']]);
                            //restore the batch id in inspection edit
                            $batches_data[$key][$idx]['batch'] = $b;
                            // set the up-to-date data for batch id and code - it is necessary for validation of next batch rows
                            $used_codes[$batches_data[$key][$idx]['batch']] = $batches_data[$key][$idx]['batch_code'];
                            $articles_prices[$val][$batches_data[$key][$idx]['batch']] = $price.$currency;
                            if ($articles[$val]->get('has_expire') && !in_array($batches_data[$key][$idx]['batch'], $expire_batches[$batches_data[$key][$idx]['expire']])) {
                                $expire_batches[$batches_data[$key][$idx]['expire']][] = $batches_data[$key][$idx]['batch'];
                            }
                        }
                    }

                    // make a string
                    $batches_data[$key][$idx]['batch'] = strval($batches_data[$key][$idx]['batch']);
                    if (!$row_error) {
                        $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'parent_id'       => $wid,
                                'nomenclature_id' => $val,
                                'batch_id'        => $batches_data[$key][$idx]['batch'],
                                'expire_date'     => $batches_data[$key][$idx]['expire'] ? : '0000-00-00',
                                'serial_number'   => $batches_data[$key][$idx]['serial'],
                                'cstm_number'     => $batches_data[$key][$idx]['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($batches_data[$key][$idx]['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $batches_data[$key][$idx]['currency'],
                            ]
                        );
                        if (isset($unique[$uk]) && array_key_exists($unique[$uk], $batches_data[$key]) && $unique[$uk] < $idx) {
                            // we have to check that the batches data at specified position has the same unique key as current batches data
                            // and that it was not set from a previous GT2 row processing
                            $idx_check = $unique[$uk];
                            $uk_check = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                $this->registry['db'],
                                [
                                    'parent_id'       => $wid,
                                    'nomenclature_id' => $val,
                                    'batch_id'        => $batches_data[$key][$idx_check]['batch'],
                                    'expire_date'     => $batches_data[$key][$idx_check]['expire'] ? : '0000-00-00',
                                    'serial_number'   => $batches_data[$key][$idx_check]['serial'],
                                    'cstm_number'     => $batches_data[$key][$idx_check]['custom'],
                                    'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($batches_data[$key][$idx_check]['delivery_price'], $prec['gt2_rows'])),
                                    'currency'        => $batches_data[$key][$idx_check]['currency'],
                                ]
                            );
                            //we have a row with the same key....just merge the quantities
                            // !!! only when it belongs to the same GT2 row, not another one with the same article !!!
                            if ($uk_check == $uk) {
                                $batches_data[$key][$unique[$uk]]['quantity'] += $batches_data[$key][$idx]['quantity'];
                                unset($batches_data[$key][$idx]);
                            }
                        } else {
                            $unique[$uk] = $idx;
                        }
                    }
                }
            } else {
                //outgoing document
                $internal_unique = array();
                foreach ($batch_warehouses as $idx => $wh) {
                    $row_error = false;

                    if (empty($batch_quantities[$idx]) || $batch_quantities[$idx] <= 0) {
                        continue;
                    }
                    //set the price to be rounded
                    $price = isset($batch_prices[$idx]) ? $batch_prices[$idx] : $prices[$key];
                    $price = round($price, $prec['gt2_rows']);

                    $currency = isset($batch_currencies[$idx]) ? $batch_currencies[$idx] : $this->get('currency');

                    //create unique key with requested quantity to compare
                    //against the available quantity in the warehouse
                    $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'parent_id'       => $wid,
                            'nomenclature_id' => $val,
                            'batch_id'        => $batch_codes[$idx],
                            'expire_date'     => !empty($batch_expire[$idx]) ? $batch_expire[$idx] : '0000-00-00',
                            'serial_number'   => !empty($batch_serials[$idx]) ? $batch_serials[$idx] : '',
                            'cstm_number'     => $batch_custom[$idx],
                            'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", $price),
                            'currency'        => $currency,
                        ]
                    );
                    if (!isset($internal_unique[$uk])) {
                        $internal_unique[$uk] = $idx;
                        $batches_data[$key][$idx] = array(
                            'warehouse' => $wid,
                            'batch' => $batch_codes[$idx],
                            'expire' => !empty($batch_expire[$idx]) ? $batch_expire[$idx] : '0000-00-00',
                            'serial' => !empty($batch_serials[$idx]) ? $batch_serials[$idx] : '',
                            'custom' => $batch_custom[$idx],
                            'delivery_price' => $price,
                            'currency' => $currency,
                            'quantity' => $batch_quantities[$idx],
                        );
                    } else {
                        $batches_data[$key][$internal_unique[$uk]]['quantity'] += $batch_quantities[$idx];
                    }
                    if (isset($unique[$uk])) {
                        $unique[$uk] -= $batch_quantities[$idx];
                    }
                    if (!isset($unique[$uk]) || bccomp($unique[$uk], 0, $prec['gt2_quantity']) < 0) {
                        //requested quantity is more than the available quantity
                        $this->registry['messages']->setError(
                            $this->i18n(
                                'error_invalid_batch_quantity',
                                array(
                                    $articles[$val]->get('name'),
                                    $warehouse->get('name'),
                                    sprintf(
                                        '<a href="#article_%d_quantity_%d">%d.%d</a>',
                                        $key,
                                        $internal_unique[$uk] + 1,
                                        $i,
                                        $internal_unique[$uk] + 1
                                    ) .
                                    sprintf(' (%s: %s)', $this->i18n('available'), isset($unique[$uk]) ? max($unique[$uk] + $batch_quantities[$idx], 0) : 0)
                                )
                            )
                        );
                        $row_error = $error = true;
                    }
                    $wh_quantities += $batch_quantities[$idx];

                    if (isset($existing_batches)) {
                        //we are here ONLY when returning articles for an expenses reason
                        $uk = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'nomenclature_id' => $val,
                                'batch_id'        => $batch_codes[$idx],
                                'expire_date'     => !empty($batch_expire[$idx]) ? $batch_expire[$idx] : '0000-00-00',
                                'serial_number'   => !empty($batch_serials[$idx]) ? $batch_serials[$idx] : '',
                                'cstm_number'     => $batch_custom[$idx],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", $price),
                                'currency'        => $currency,
                            ]
                        );

                        // validate per-row (same article can be present in multiple rows of reason)
                        // we have to validate whether we can return specified quantity from exactly this batch for exactly this GT2 row of reason
                        if (array_key_exists('rows_quantity', $existing_batches[$uk])) {
                            if (!array_key_exists($key, $existing_batches[$uk]['rows_quantity'])) {
                                $existing_batches[$uk]['rows_quantity'][$key] = 0;
                            }
                            if (bccomp($existing_batches[$uk]['rows_quantity'][$key], $batch_quantities[$idx], $prec['gt2_quantity']) < 0) {
                                if (!$row_error) {
                                    // specified batch quantity is more than stored for GT2 row
                                    $this->registry['messages']->setError($this->i18n(
                                        'error_invalid_batch_quantity',
                                        array(
                                            $articles[$val]->get('name'),
                                            $warehouse->get('name'),
                                            sprintf('<a href="#article_%d_quantity_%d">%d.%d</a>', $key, $idx + 1, $i, $idx + 1) .
                                                sprintf(' (%s: %s)', $this->i18n('gt2_tagregates_max'), max($existing_batches[$uk]['rows_quantity'][$key], 0))
                                        )
                                    ));
                                    $error = $row_error = true;
                                }
                            }
                            $existing_batches[$uk]['rows_quantity'][$key] -= $batch_quantities[$idx];
                        }
                        $existing_batches[$uk]['quantity'] -= $batch_quantities[$idx];
                    }
                }
            }
            $gt2['process_batches'] = true;
            $this->set('grouping_table_2', $gt2, true);
            $this->set('table_values_are_set', true, true);

            if (round($wh_quantities, $prec['gt2_quantity']) != round($quantities[$key], $prec['gt2_quantity'])) {
                //total_quantity in the main row is different from the warehouses distributed quantities
                //this is not normal so raise an error
                $this->registry['messages']->setError(
                    $this->i18n(
                        'error_system_calculation',
                        array(
                            sprintf('<a href="#article_name_%d">%s</a>', $i, $articles[$val]->get('name'))
                        )
                    )
                );
                $error = true;
            }
            if (!empty($batches_data[$key])) {
                $gt2['values'][$key]['batch_data'] = $batches_data[$key];
            } else {
                $gt2['values'][$key]['batch_data'] = array();
            }
        }
        // end foreach $article_ids

        if (!empty($used_codes)) {
            //check for already used custom codes
            $query = 'SELECT id, code FROM ' . DB_TABLE_FINANCE_BATCHES .
                    ' WHERE (' . implode(' OR ', array_map(function($a) { return 'code LIKE "' . $a . '"'; }, General::slashesEscape($used_codes))) . ')' .
                    (!empty($not_real_batches) ? ' AND id NOT IN ("' . implode('", "', array_keys($not_real_batches)) . '")' : '');
            $used_codes = $this->registry['db']->GetAssoc($query);

            if (!empty($used_codes)) {
                if ($this->get('type') == PH_FINANCE_TYPE_INSPECTION || $this->get('type') == PH_FINANCE_TYPE_HANDOVER && $this->get('direction') == 'incoming') {
                    //check if this used codes are assigned to a batch for the same article with all other unique parameters equal
                    //or has never been assigned for the articles in the requested batch
                    $where = array();
                    $batchUniqueKeyElementsNames = Finance_Warehouses_Documents::getBatchUniqueKeyElementsNames($this->registry['db']);
                    $checkExpireDate = array_key_exists('expire_date', $batchUniqueKeyElementsNames);
                    $checkDeliveryPrice = array_key_exists('delivery_price', $batchUniqueKeyElementsNames);
                    $checkCurrency = array_key_exists('currency', $batchUniqueKeyElementsNames);
                    foreach ($gt2['values'] as $key => $values) {
                        if (empty($values['batch_data'])) {
                            continue;
                        }
                        foreach ($values['batch_data'] as $b => $bd) {
                            if (!$bc = array_search($bd['batch_code'], $used_codes)) {
                                continue;
                            }
                            $batchSpecificWhere = [];
                            if ($checkExpireDate) {
                                $batchSpecificWhere[] = 'gb.expire_date != "' . $bd['expire'] . '"';
                            }
                            if ($checkDeliveryPrice) {
                                $batchSpecificWhere[] = 'gb.delivery_price != ' . floatval($bd['delivery_price']);
                            }
                            if ($checkCurrency) {
                                $batchSpecificWhere[] = 'gb.currency != "' . $bd['currency'] . '"';
                            }
                            $batchSpecificWhere = implode(' OR ', $batchSpecificWhere);
                            $where[] =  'g.article_id = ' . $values['article_id'] . ' AND gb.batch_id = ' . $bc .
                                ($batchSpecificWhere ? " AND ({$batchSpecificWhere})" : '');
                            $gt2['values'][$key]['batch_data'][$b]['batch'] = $bc;
                        }
                    }
                    if ($where) {
                        $query = 'SELECT fb.id, fb.code, GROUP_CONCAT(DISTINCT g.article_id) AS article_ids' . "\n" .
                                 'FROM ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                                 'JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' gb' . "\n" .
                                 '  ON g.id = gb.parent_id AND g.model = "Finance_Warehouses_Document" AND' . "\n" .
                                 '    ((' . implode(") OR\n(", $where) . '))' . "\n" .
                                 'JOIN ' . DB_TABLE_FINANCE_BATCHES . ' fb' . "\n" .
                                 '  ON fb.id = gb.batch_id' . "\n" .
                                 'GROUP BY fb.id' . "\n" .
                                 'ORDER BY g.id DESC';
                        $used_codes = $this->registry['db']->GetAssoc($query);
                        if (!empty($used_codes)) {
                            foreach ($used_codes as $used_batch => $used_batch_data) {
                                $used_codes[$used_batch] = sprintf(
                                    '- %s (%s)<br />',
                                    $used_codes[$used_batch]['code'],
                                    implode(', ',
                                        array_map(
                                            function($a) { return $a->get('name') ?: ''; },
                                            array_intersect_key(
                                                $articles,
                                                array_flip(explode(',', $used_codes[$used_batch]['article_ids']))
                                            )
                                        )
                                    )
                                );
                            }
                            $this->registry['messages']->setError(
                                $this->i18n(
                                    'error_finance_whd_used_batch_codes',
                                    array('<br />' . implode('', $used_codes))
                                )
                            );
                            $error = true;
                        }
                    }
                } else {
                    $this->registry['messages']->setError($this->i18n('error_finance_whd_used_batch_codes',
                        array(implode(', ', $used_codes))));
                    $error = true;
                }
            }

            // when one batch is created for inspection and then manually
            // used once again for another article when inspection is edited
            // make sure it gets the same id
            // because inserting same code once again will fail and lead to invalid data
            if (!empty($not_real_batches)) {
                $query = 'SELECT id, code FROM ' . DB_TABLE_FINANCE_BATCHES .
                        ' WHERE (' . implode(' OR ', array_map(function($a) { return 'code LIKE "' . $a . '"'; }, General::slashesEscape($not_real_batches))) . ')';
                $not_real_batches = $this->registry['db']->GetAssoc($query);
                if ($not_real_batches) {
                    foreach ($gt2['values'] as $key => $values) {
                        if (empty($values['batch_data'])) {
                            continue;
                        }
                        foreach ($values['batch_data'] as $b => $bd) {
                            $bc = array_search($bd['batch_code'], $not_real_batches);
                            if ($bc === false || $bc == $bd['batch']) {
                                continue;
                            } else {
                                $gt2['values'][$key]['batch_data'][$b]['batch'] = $bc;
                                $batches_data[$key][$b]['batch'] = $bc;
                            }
                        }
                    }
                }
            }
        }
        if (isset($existing_batches)) {
            $this->registry->set('existing_batches', $existing_batches, true);
        }

        $gt2['process_batches'] = true;
        $this->set('grouping_table_2', $gt2, true);
        $this->calculateGT2();
        $this->set('table_values_are_set', true, true);

        return !$error;
    }

    /**
     * Get permissions/visibility settings for the batch variables.
     *
     * @return array - array with data for variables
     */
    public function getBatchVarsSettings($print = false) {

        if (!$this->get('type')) {
            return array();
        }
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        if (!$lang = $this->get('model_lang')) {
            $lang = $this->registry['lang'];
        }
        $query = 'SELECT fm.name, fm.id, fm.hidden, fm.readonly, fm.source, fm.width,' . "\n" .
                 '  fi18n.content AS label, fi18n2.content AS help' . "\n" .
                 'FROM ' . DB_TABLE_FIELDS_META . " fm \n" .
                 'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fi18n' . "\n".
                 '  ON fm.id = fi18n.parent_id AND fi18n.content_type = "label" AND fi18n.lang = "' . $lang . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fi18n2' . "\n".
                 '  ON fm.id = fi18n2.parent_id AND fi18n2.content_type = "help" AND fi18n2.lang = "' . $lang . '"' . "\n" .
                 'WHERE model="Finance_Batch_Data" AND model_type = ' . $this->get('type');
        $records = $this->registry['db']->GetAssoc($query);

        foreach ($records as $k => $v) {
            $source = preg_split('#(\n|\r|\r\n)#', $v['source']);
            foreach ($source as $row) {
                if (preg_match('#^\s*$#', $row)) continue;
                $row = preg_split('#\s*:=\s*#', $row);
                $records[$k][$row[0]] = trim($row[1]);
            }
            unset($records[$k]['source']);
            $records[$k]['permissions_view'] = preg_split('#\s*,\s*#', $records[$k]['permissions_view']);
            $records[$k]['permissions_edit'] = preg_split('#\s*,\s*#', $records[$k]['permissions_edit']);

            if ($this->registry['action'] == 'ajax_get_quantity') {
                //edit mode
                $groups = array_intersect($records[$k]['permissions_edit'], $this->registry['currentUser']->get('groups'));
                if (empty($groups)) {
                    $records[$k]['readonly'] = 1;
                }
                $groups = array_intersect($records[$k]['permissions_view'], $this->registry['currentUser']->get('groups'));
                if (empty($groups)) {
                    $records[$k]['hidden'] = 1;
                }
            }
        }
        if (!empty($sanitize_after)) {
            $this->sanitize();
        }
        return $records;
    }

    /**
     * Get default release date for commodity reservation documents
     * IMPORTANT: default date is stored in default_payment_date
     *
     * @return array - array with data for variables
     */
    public function getDefaultReleaseDate() {
        //super default
        $releaseDate = General::strftime(
            '%Y-%m-%d',
            strtotime(
                PH_FINANCE_DEFAULT_RESERVATION_PERIOD,
                time()
            )
        );

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $finDocType = Finance_Documents_Types::searchOne($this->registry, array(
            'where' => array('fdt.id = ' . PH_FINANCE_TYPE_COMMODITIES_RESERVATION)
        ));

        if ($finDocType->isDefined('default_date_of_payment_count') &&
            $finDocType->get('default_date_of_payment_count') !== '') {
            if ($finDocType->get('default_date_of_payment_period_type') == 'working') {
                require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                $releaseDate = Calendars_Calendar::calcDateOnWorkingDays(
                    $this->registry,
                    General::strftime('%Y-%m-%d'),
                    $finDocType->get('default_date_of_payment_count'),
                    'after'
                );
            } else {
                $releaseDate = General::strftime(
                    '%Y-%m-%d',
                    strtotime('+' . $finDocType->get('default_date_of_payment_count') . ' day')
                );
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $releaseDate;
    }

    /**
     * Gets settings for additional required fields
     *
     * @return array $requiredFields - array with required field names
     */
    public function getRequiredFields() {
        $settingsSection = strtolower($this->modelName) . 's';

        $sanitize_after = false;
        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $requiredFields = $this->registry['config']->getParamAsArray(
            $settingsSection, 'validate_' . $this->get('type')
        );

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $requiredFields;
    }

    /**
     * Update last delivery and average weighted delivery prices
     * for all the nomenclatures in the model GT2
     *
     * @return bool - result of the operation
     */
    public function updateNomPrices() {
        // Update delivery prices only if the setting is set and the direction is "incoming"
        if (!$this->registry['config']->getParam('nomenclatures', 'update_delivery_prices_on_handover') || $this->get('direction') != 'incoming' || $this->get('link_to_model_name') != 'Finance_Expenses_Reason') {
            return true;
        }

        $db = $this->registry['db'];

        // //check if this is the first document of the chain
        // if ($this->get('type') != $this->getExpensesRootType()) {
        //     return true;
        // }

        $gt2 = $this->get('grouping_table_2');
        if (empty($gt2)) {
            $gov = $this->registry->get('get_old_vars');
            $this->registry->set('get_old_vars', true, true);
            $this->getGT2Vars();
            $gt2 = $this->get('grouping_table_2');
            $this->registry->set('get_old_vars', $gov, true);
        }
        if (!$this->get('currency')) {
            $query = 'SELECT currency FROM ' . DB_TABLE_FINANCE_EXPENSES_REASONS . "\n" .
                     'WHERE id = ' . $this->get('id');
            $this->set('currency', $db->GetOne($query), true);
        }

        // get precision settings
        $prec = $this->registry['config']->getSectionParams('precision');

        $user_id = $this->registry['currentUser'] ? $this->registry['currentUser']->get('id') : PH_AUTOMATION_USER;

        $rates = array();
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        foreach ($gt2['values'] as $key => $values) {
            if (empty($values['article_id'])) {
                continue;
            }
            $now = $db->GetOne('SELECT NOW()');
            $set = array(
                'modified' => sprintf("modified = '%s'", $now),
                'modified_by' => sprintf('modified_by = %d', $user_id),
            );
            //get nomenclature average price and current quantity (if present)
            $query = 'SELECT n.* ,' . "\n" .
                     '  ROUND(n.sell_price, ' . $prec['nom_sell_price'] . ') AS sell_price,' . "\n" .
                     '  ROUND(n.last_delivery_price, ' . $prec['nom_last_delivery_price'] . ') AS last_delivery_price,' . "\n" .
                     '  ROUND(n.average_weighted_delivery_price, ' . $prec['nom_average_weighted_delivery_price'] . ') AS average_weighted_delivery_price,' . "\n" .
                     '  nti18n.name AS type_name, nti18n.name_plural AS type_name_plural,' . "\n" .
                     '  (SELECT SUM(ROUND(quantity, ' . $prec['gt2_quantity'] . '))' . "\n" .
                     '  FROM ' . DB_TABLE_FINANCE_WAREHOUSES_QUANTITIES . "\n" .
                     '  WHERE nomenclature_id = ' . $values['article_id'] . ') AS quantity,' . "\n" .
                     '  (SELECT calculated_price' . "\n" .
                     '  FROM ' . DB_TABLE_FINANCE_DOCUMENTS_TYPES . "\n" .
                     '  WHERE model = "Finance_Warehouses_Document" AND id = ' . $this->get('type') . ') AS calculated_price' . "\n" .
                     'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . ' AS nti18n' . "\n" .
                     '  ON n.type = nti18n.parent_id AND nti18n.lang = \'' . $this->get('model_lang') . '\'' . "\n" .
                     'WHERE id = ' . $values['article_id'];
            $nom = $db->GetRow($query);

            if (empty($nom)) {
                continue;
            }

            $old_nomenclature = new Nomenclature(
                $this->registry,
                $nom + array(
                    'name' => $values['article_name'],
                    'code' => $values['article_code'],
                    'model_lang' => $this->get('model_lang'),
                )
            );
            $old_nomenclature->sanitize();
            $new_nomenclature = clone $old_nomenclature;

            //prepare last delivery price
            if ($nom['last_delivery_price_currency']) {
                if ($this->get('currency') != $nom['last_delivery_price_currency']) {
                    //different currencies - get rate
                    if (empty($rates[$this->get('currency') . $nom['last_delivery_price_currency']])) {
                        $rate = Finance_Currencies::getRate($this->registry, $this->get('currency'), $nom['last_delivery_price_currency']);
                        $rates[$this->get('currency') . $nom['last_delivery_price_currency']] = $rate;
                    } else {
                        $rate = $rates[$this->get('currency') . $nom['last_delivery_price_currency']];
                    }
                } else {
                    $rate = 1;
                }
            } else {
                //currency is not set so set it from the GT2
                $rate = 1;
                $set['last_delivery_price_currency'] = sprintf('last_delivery_price_currency = "%s"', $this->get('currency'));
                $nom['last_delivery_price_currency'] = $this->get('currency');
            }
            //$last_delivery_price = $values[$nom['calculated_price']] * $rate;
            $last_delivery_price = $values['price_with_discount'] * $rate;
            // round and format final calculated price according to its precision setting
            $last_delivery_price = sprintf("%.{$prec['nom_last_delivery_price']}f", round($last_delivery_price, $prec['nom_last_delivery_price']));

            $set['last_delivery_price'] = sprintf('last_delivery_price = "%s"', $last_delivery_price);

            //prepare average weighted delivery price
            if ($nom['average_weighted_delivery_price_currency']) {
                if ($nom['last_delivery_price_currency'] != $nom['average_weighted_delivery_price_currency']) {
                    //different currencies - get rate
                    if (empty($rates[$nom['last_delivery_price_currency'] . $nom['average_weighted_delivery_price_currency']])) {
                        $rate = Finance_Currencies::getRate($this->registry, $nom['last_delivery_price_currency'], $nom['average_weighted_delivery_price_currency']);
                        $rates[$nom['last_delivery_price_currency'] . $nom['average_weighted_delivery_price_currency']] = $rate;
                    } else {
                        $rate = $rates[$nom['last_delivery_price_currency'] . $nom['average_weighted_delivery_price_currency']];
                    }
                } else {
                    $rate = 1;
                }
            } else {
                //currency is not set so set it from the GT2
                $rate = 1;
                $set['average_weighted_delivery_price_currency'] = sprintf('average_weighted_delivery_price_currency = "%s"', $nom['last_delivery_price_currency']);
                $nom['average_weighted_delivery_price_currency'] = $nom['last_delivery_price_currency'];
            }

            //calculate average weighted price
            if ($nom['subtype'] == 'commodity') {
                $average_weighted_delivery_price = $nom['average_weighted_delivery_price'] * $nom['quantity'];
                $average_weighted_delivery_price += $last_delivery_price * $rate * $values['quantity'];
                if ($nom['quantity'] + $values['quantity'] != 0) {
                    $average_weighted_delivery_price = $average_weighted_delivery_price / ($nom['quantity'] + $values['quantity']);
                }
            } else {
                // There can't be non-commodity nomenclatures here
                continue;
            }
            // round and format final calculated price according to its precision setting
            $average_weighted_delivery_price = sprintf("%.{$prec['nom_average_weighted_delivery_price']}f", round($average_weighted_delivery_price, $prec['nom_average_weighted_delivery_price']));

            $set['average_weighted_delivery_price'] = sprintf('average_weighted_delivery_price = "%s"', $average_weighted_delivery_price);

            //update nomenclature data
            $query = 'UPDATE ' . DB_TABLE_NOMENCLATURES . ' SET' . "\n" . implode(",\n", $set) . "\n" .
                     'WHERE id = ' . $values['article_id'];
            $db->Execute($query);

            //  write log entry for average_weighted_delivery_price update
            $price_update_params = array(
                'parent_id' => $values['article_id'],
                'old_price' => $nom['average_weighted_delivery_price'],
                'currency' => $nom['average_weighted_delivery_price_currency'],
                'old_quantity' => $nom['quantity'] ?: 0,
                'delivery_price' => $last_delivery_price * $rate, // in AWDP currency, without rounding
                'delivery_quantity' => $values['quantity'] ?: 0,
                'new_price' => $average_weighted_delivery_price,
                'update_type' => 'delivery',
                'row_id' => $key,
                'allocated_cost_id' => 0,
                'modified' => $now,
                'modified_by' => $user_id,
            );
            $query = "
                INSERT INTO " . DB_TABLE_NOMENCLATURES_PRICE_UPDATES . " SET " .
                implode(", ", array_map(function($v, $k) { return "$k = '$v'"; }, $price_update_params, array_keys($price_update_params)));
            $db->Execute($query);

            $nom['last_delivery_price'] = $last_delivery_price;
            $nom['average_weighted_delivery_price'] = $average_weighted_delivery_price;
            foreach ($nom as $k => $v) {
                $new_nomenclature->set($k, $v, true);
            }

            // write history into nomenclature for prices update
            Nomenclatures_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'edit',
                    'model' => $new_nomenclature,
                    'new_model' => $new_nomenclature,
                    'old_model' => $old_nomenclature,
                )
            );
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Gets value for export
     *
     * @param array $field - array as prepared by the Outlook (getOutlookSettings)
     * @param bool $force - IMPORTANT: set to true to get data without formatting, set to 0 to get autocompleter as link, if having such settings
     * @param bool $ignore_permissions - if true, do not check layout permission (when getting data for autocompleter fill options, for example)
     * @return mixed - the value of the variable
     */
    public function getExportVarValue($field, $force = false, $ignore_permissions = false) {
        switch($field['name']) {
            case 'status':
                $status_label_type = $this->i18n('finance_documents_status_' . $this->get('status') . "_" . $this->get('type'));
                return $status_label_type ?:$this->i18n('finance_documents_status_' . $this->get('status'));
            default:
                return parent::getExportVarValue($field, $force = false, $ignore_permissions = false);
        }
    }
}
