<?php
require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';

class Finance_Expenses_Reasons_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Finance_Expenses_Reason';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Finance_Expenses_Reasons';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'export', 'add', 'clone', 'edit', 'view', 'generate',
        'attachments', 'setstatus', 'assign', 'tag', 'annul',
        'observer', 'print', 'manage_outlooks', 'printlist',
        'payments', 'relatives', 'distribute', 'allocate_costs', 'history',
        'comments', 'emails', 'communications'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'payments', 'relatives', 'distribute', 'allocate_costs', 'history', 'communications'
    );

    /**
     * Action which are at the up right position (without tabs)
     */
    public $actionDefinitionsUpRight = array(
        'observer', 'print', 'manage_outlooks', 'printlist'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'edit', 'view', 'translate',
        'attachments', 'assign', 'generate', 'relatives', 'distribute'
    );

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_select',
    );

    /**
     * The fields which will switch the additional variables in the search panel
     */
    public static $searchAdditionalVarsSwitch = 'fer.type';

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'assign':
            $this->_assign();
            break;
        case 'ajax_assign':
            $this->_getAssignments();
            break;
        case 'observer':
            $this->_observer();
            break;
        case 'search':
            $this->_search();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'relatives':
            $this->_relatives();
            break;
        case 'payments':
            $this->_payments();
            break;
        case 'addpayment':
            $this->_addPayment();
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'ajax_select':
            $this->_select();
            break;
        case 'addcredit':
        case 'adddebit':
            $this->_addCreditDebit();
            break;
        case 'addinvoice':
            $this->_addInvoice();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'setstatus':
            $this->_status();
            break;
        case 'ajax_status':
            $this->_getStatus();
            break;
        case 'multistatus':
            $this->_multiStatus();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'addcorrect':
            $this->_addCorrect();
            break;
        case 'addhandover':
            $this->_addHandover();
            break;
        case 'manageGT2config':
            $this->_manageGT2config();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_check_num':
            $this->_checkNum();
            break;
        case 'ajax_preview_invoice':
            $this->_previewGT2Invoice();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'generate':
            $this->_generate();
            break;
        case 'print':
            $this->_print();
            break;
        case 'multiprint':
            $this->_multiPrint();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'history':
            $this->_history();
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'distribute':
            $this->_distribute();
            break;
        case 'ajax_change_item':
            $this->_changeItem();
            break;
        case 'allocate_costs':
            $this->_allocateCosts();
            break;
        case 'ajax_update_allocated_status':
            $this->_updateAllocatedStatus();
            break;
        case 'ajax_load_delivery_rows':
            $this->_loadDeliveryRows();
            break;
        case 'annul':
            $this->_annul();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'tag':
            $this->_tag();
            break;
        case 'multitag':
            $this->_multiTag();
            break;
        case 'multiremovetag':
            $this->_multiRemoveTag();
            break;
        case 'ajax_tag':
            $this->_getTags();
            break;
        case 'change_unpaid_status':
            $this->_changeUnpaidStatus();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'ajax_get_type_fields':
            $this->_getTypeFields();
            break;
        case 'ajax_prepare_payment_form':
            $this->_preparePaymentForm();
            break;
        case 'ajax_get_warehouses':
            $this->_getWarehouses();
            break;
        case 'recalc_payment_date':
            $this->_recalcPaymentDate();
            break;
        case 'multiaddinvoice':
        case 'ajax_multiaddinvoice':
            $this->_multiAddInvoice();
            break;
        case 'clone':
            $this->_clone();
            break;
        case 'ajax_import_table':
            $this->_importTable();
            break;
        case 'ajax_import_table_configurator':
            $this->_importTableConfigurator();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //only for update user_permissions field for all finance expenses reasons
        // EXECUTE TEMPORARY METHOD
        //Finance_Expenses_Reasons::updateUserPermissions($this->registry);

        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {

        $request = &$this->registry['request'];
        if (!$request->isPost()) {
            if ($request->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                $this->redirect('finance', 'addcredit', array('addcredit' => $request->get('invoice_id')));
            } elseif ($request->get('type') == PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE) {
                $this->redirect('finance', 'adddebit', array('adddebit' => $request->get('invoice_id')));
            }
        }

        $this->registry->set('get_old_vars', false, true);
        $finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);
        $skip_get_gt2_vars_for_new_model = false;

        // special case if the add action has been activated via report
        //IMPORTANT: this mechanism is also used by the clone action
        if ($request->get('report_session_param')) {
            $report_model_session_param = $request->get('report_session_param');
            $finance_expenses_reason_custom_model = $this->registry['session']->get('report_custom_model', $report_model_session_param);

            if ($finance_expenses_reason_custom_model) {
                $finance_expenses_reason_custom_model = unserialize($finance_expenses_reason_custom_model);
                $this->registry['session']->remove('', $report_model_session_param);
                $this->registry['session']->remove($report_model_session_param, 'selected_items');

                $finance_expenses_reason = $finance_expenses_reason_custom_model;
                $skip_get_gt2_vars_for_new_model = true;
                $finance_expenses_reason->unsanitize();
                unset($finance_expenses_reason_custom_model);
            }
        }
        $this->settings_assign = $this->registry['config']->getParamAsArray('finance', 'assignment_types_' . $finance_expenses_reason->get('type'));

        //check if details are submitted via POST and the skip_post_save_model flag is not set
        if (!empty($finance_expenses_reason) && $this->registry['request']->isPost() && !$this->registry['request']->get('skip_post_save_model')) {
            //get requested after action
            $after_action = $request->get('after_action');

            $old_model = new Finance_Expenses_Reason($this->registry);
            // set type and company in order to prepare GT2 table of old model
            $old_model->set('type', $finance_expenses_reason->get('type'), true);
            $old_model->set('company', $finance_expenses_reason->get('company'), true);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);
            $this->old_model = $old_model;

            $transform_params = $finance_expenses_reason->get('transform_params');

            if ($finance_expenses_reason->save()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fer.id = ' . $finance_expenses_reason->get('id')));
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
                $finance_expenses_reason->getGT2Vars();
                if ($transform_params) {
                    $finance_expenses_reason->set('transform_params', $transform_params, true);
                }

                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                           array('action_type' => 'add',
                                                                                 'new_model' => $finance_expenses_reason,
                                                                                 'old_model' => $old_model
                                                                           ));

                if ($request->get('finance_after_action') == 'payment') {
                    $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                               array('action_type' => 'addpayment',
                                                                                     'new_model' => $finance_expenses_reason,
                                                                                     'old_model' => $finance_expenses_reason
                                                                               ));
                }

                //assign the document to the users set in the default assignments
                // and the selected users can be assigned according to the assignments rights by types in the settings
                $current_user_assignments = $this->registry['currentUser']->getAssignmentPermissions('Finance_Expenses_Reason');

                $filters_assign['type'] = $finance_expenses_reason->get('type');
                $filters_assign['department'] = $finance_expenses_reason->get('department');

                $current_user_default_assignments = $this->registry['currentUser']->getDefaultAssignments('finance', $filters_assign);
                if (empty($current_user_default_assignments)) {
                    $filters_assign['department'] = -1;
                    $current_user_default_assignments = $this->registry['currentUser']->getDefaultAssignments('finance', $filters_assign);
                }

                require_once PH_MODULES_DIR . 'users/models/users.factory.php';

                $assignments_owner = array();
                $assignments_responsible = array();
                $assignments_observer = array();
                $assignments_decision = array();

                // collect user assignment permissions per role
                // (as they are the same for all users with the same role)
                // and reuse them to reduce multiple fetching of same data from database
                $user_available_assignments_per_role = array();

                foreach ($current_user_default_assignments as $default_assign) {
                    if (in_array($default_assign['assignment_type'], $this->settings_assign)) {
                        $filters = array('model_lang'   => $request->get('model_lang'),
                                         'where'        => array('u.id ="' . $default_assign['assignee'] . '"',
                                                                 'u.hidden = 0',
                                                                 'u.active = 1'),
                                         'sort'         => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'),
                                         'sanitize'     => true
                                        );
                        $user = Users::searchOne($this->registry, $filters);
                        if ($user) {
                            // get assignment permissions for this user and set them
                            // to the array per role
                            if (empty($user_available_assignments_per_role[$user->get('role')])) {
                                $user_available_assignments_per_role[$user->get('role')] = $user->getAssignmentPermissions('Finance_Expenses_Reason');
                            }
                            $user_available_assignments = $user_available_assignments_per_role[$user->get('role')];

                            if (in_array($finance_expenses_reason->get('type'), $user_available_assignments[$default_assign['assignment_type']])) {
                                switch ($default_assign['assignment_type']) {
                                    case 'owner':
                                        $assignments_owner[] = $default_assign['assignee'];
                                        break;
                                    case 'responsible':
                                        $assignments_responsible[] = $default_assign['assignee'];
                                        break;
                                    case 'observer':
                                        $assignments_observer[] = $default_assign['assignee'];
                                        break;
                                    case 'decision':
                                        $assignments_decision[] = $default_assign['assignee'];
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                    }
                }

                if (in_array('observer', $this->settings_assign) &&
                    $this->registry['currentUser']->getPersonalSettings('finance', 'set_observer') &&
                    in_array($finance_expenses_reason->get('type'), $current_user_assignments['observer'])) {
                    $assignments_observer[] = $this->registry['currentUser']->get('id');
                    $assignments_observer = array_unique($assignments_observer);
                }

                // set properties to model only for enabled assignment types
                if (in_array('owner', $this->settings_assign)) {
                    $finance_expenses_reason->set('assignments_owner', $assignments_owner, true);
                }
                if (in_array('responsible', $this->settings_assign)) {
                    $finance_expenses_reason->set('assignments_responsible', $assignments_responsible, true);
                }
                if (in_array('observer', $this->settings_assign)) {
                    $finance_expenses_reason->set('assignments_observer', $assignments_observer, true);
                }
                if (in_array('decision', $this->settings_assign)) {
                    $finance_expenses_reason->set('assignments_decision', $assignments_decision, true);
                }

                if (!empty($assignments_owner) || !empty($assignments_responsible) || !empty($assignments_observer) || !empty($assignments_decision)) {

                    $this->registry->set('getAssignments', true, true);
                    $filters = array('where'      => array('fer.id = ' . $finance_expenses_reason->get('id')),
                                     'model_lang' => $finance_expenses_reason->get('model_lang'));
                    $current_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

                    if ($finance_expenses_reason->assign()) {
                        $this->registry['messages']->setMessage($this->i18n('message_model_assign_success',
                                                                array($finance_expenses_reason->getModelTypeName())), '', 0);

                        $filters = array('where'      => array('fer.id = ' . $finance_expenses_reason->get('id')),
                                         'model_lang' => $finance_expenses_reason->get('model_lang'));
                        $assigned_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

                        Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                   array('model' => $finance_expenses_reason,
                                                                         'action_type' => 'assign',
                                                                         'new_model' => $assigned_reason,
                                                                         'old_model' => $current_reason));
                    } else {
                        $this->registry['messages']->setWarning($this->i18n('error_model_not_assigned',
                                                                array($finance_expenses_reason->getModelTypeName())));
                    }
                }

                //set after action to 'view' if not having permission
                if (!$finance_expenses_reason->checkPermissions($after_action)) {
                    $after_action = 'view';
                    $request->set('after_action', $after_action, 'get', true);
                }

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_add_success',
                                                        array($finance_expenses_reason->getModelTypeName())), '', -1);
                if ($this->registry['request']->get('finance_after_action') == 'payment') {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_addpayment_success'));
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;
            } else {
                // model should not have an id
                $finance_expenses_reason->unsetProperty('id', true);

                $finance_expenses_reason->getGT2Vars();
                $grouping_table_2 = $finance_expenses_reason->get('grouping_table_2');
                if (!empty($grouping_table_2['values'])) {
                    //get original row indexes for transformations
                    $rows = array_keys($grouping_table_2['values']);
                }
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_add_failed',
                                                      array($finance_expenses_reason->getModelTypeName())), '', -2);
                $add_error = true;
            }
        }

        if (!empty($finance_expenses_reason)) {
            $this->registry->set('get_old_vars', false, true);

            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $filters = array('where' => array('fdt.model="Finance_Expenses_Reason"',
                                              'fdt.id = "' . $finance_expenses_reason->get('type') . '"',
                                              'fdt.active=1'),
                             'sanitize' => true);
            $financeType = Finance_Documents_Types::searchOne($this->registry, $filters);

            $type_permission = $financeType ? $this->checkActionPermissions($this->module . '_' . $this->controller . $financeType->get('id'), 'add') : false;

            if (empty($financeType) || !$type_permission || !$finance_expenses_reason->get('company') && empty($add_error)) {
                if (empty($add_error)) {
                    //invalid type or missing company, redirect to list
                    $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_add_failed',
                                                          array($finance_expenses_reason->getModelTypeName())), '', -1);
                }
                if (empty($financeType) || !$type_permission) {
                    $error_msg = !$finance_expenses_reason->get('type') ? 'error_finance_expenses_reasons_no_type_specified' : 'error_finance_expenses_reasons_invalid_type';
                    $this->registry['messages']->setError($this->i18n($error_msg));
                }
                if (!$finance_expenses_reason->get('company') && empty($add_error)) {
                    $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_no_company_specified'));
                }
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'list');
            }

            $finance_expenses_reason->set('type_payment_way', $financeType->get('payment_way'), true);
            $finance_expenses_reason->set('type_name',        $financeType->get('name'), true);
            if (!$skip_get_gt2_vars_for_new_model) {
                $finance_expenses_reason->getGT2Vars();
            }

            if (!empty($rows)) {
                //set original row indexes for transformations
                $grouping_table_2 = $finance_expenses_reason->get('grouping_table_2');
                $grouping_table_2['rows'] = $rows;
                $finance_expenses_reason->set('grouping_table_2', $grouping_table_2, true);
            }

            if (!$this->registry['request']->isPost()) {
                //set default values from the type
                $finance_expenses_reason->setGroup(false, $financeType->get('default_group'));
                $finance_expenses_reason->setDepartment(false, $financeType->get('default_department'));

                $finance_expenses_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);

                if (!$finance_expenses_reason->get('employee1') && $this->registry['currentUser']->get('employee')) {
                    $finance_expenses_reason->set('employee1', $this->registry['currentUser']->get('employee'), true);
                    $finance_expenses_reason->set('employee1_name', $this->registry['currentUser']->get('employee_name'), true);
                }
            }

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
        }

        return true;
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get requested after action
            $after_action = $request->get('after_action');

            //build the model from the POST
            $finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fer.id = ' . $finance_expenses_reason->get('id')));
            $old_model = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);
            $this->old_model = $old_model;

            $this->checkAccessOwnership($this->old_model);

            $save_action = 'save';
            if (
                in_array(
                    $finance_expenses_reason->get('type'),
                    array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE)
                ) &&
                !$finance_expenses_reason->get('edit_finished')
            ) {
                $save_action = 'saveCreditDebit';
            }
            if ($finance_expenses_reason->$save_action()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fer.id = ' . $finance_expenses_reason->get('id')));
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
                $finance_expenses_reason->getGT2Vars();

                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                Finance_Expenses_Reasons_History::saveData($this->registry,
                                                           array('action_type' => 'edit',
                                                                 'new_model' => $finance_expenses_reason,
                                                                 'old_model' => $old_model
                                                           ));
                if (!$finance_expenses_reason->get('edit_finished')) {
                    if ($request->get('finance_after_action') == 'payment') {
                        Finance_Expenses_Reasons_History::saveData(
                            $this->registry,
                            array(
                                'action_type' => 'addpayment',
                                'new_model' => $finance_expenses_reason,
                                'old_model' => $finance_expenses_reason
                            ));
                    } elseif ($finance_expenses_reason->get('payment_status') == 'paid') {
                        Finance_Expenses_Reasons_History::saveData(
                            $this->registry,
                            array(
                                'action_type' => 'payments',
                                'new_model' => $finance_expenses_reason,
                                'old_model' => $finance_expenses_reason
                            ));
                    }
                }

                //set after action to 'view' if not having permission
                if (!$finance_expenses_reason->checkPermissions($after_action)) {
                    $after_action = 'view';
                    $request->set('after_action', $after_action, 'get', true);
                }

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_edit_success',
                                                        array($finance_expenses_reason->getModelTypeName())), '', -1);
                if ($this->registry['request']->get('finance_after_action') == 'payment') {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_addpayment_success'));
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;
            } else {
                //some error occurred
                $finance_expenses_reason->set('status', $old_model->get('status'), true);
                if (!$finance_expenses_reason->get('company_data')) {
                    // set properties from old model (used in getting available options for Cashbox/Bank account field)
                    $company_data_properties = array('company', 'office', 'payment_type');
                    foreach ($company_data_properties as $prop) {
                        $finance_expenses_reason->set($prop, $old_model->get($prop), true);
                    }
                }
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_edit_failed',
                                                      array($finance_expenses_reason->getModelTypeName())), '', -2);
                //ToDo - set non editable properties as currency, amount ...
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fer.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

            if ($finance_expenses_reason) {
                $this->checkAccessOwnership($finance_expenses_reason);
            }
        }

        if (!empty($finance_expenses_reason)) {
            $this->registry->set('get_old_vars', false, true);
            $finance_expenses_reason->getGT2Vars();

            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeType = Finance_Documents_Types::searchOne($this->registry,
                                                              array('where' => array('fdt.id=' . $finance_expenses_reason->get('type'))));
            $finance_expenses_reason->set('type_payment_way', $financeType->get('payment_way'), true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add correct reason
     */
    private function _addCorrect() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fer.id = ' . $finance_expenses_reason->get('id')));
            $old_model = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);

            if ($finance_expenses_reason->validate('edit') && $finance_expenses_reason->saveCorrect($old_model)) {
                $filters = array('where' => array('fer.id = ' . $finance_expenses_reason->get('id')));
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $finance_expenses_reason->getGT2Vars();
                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                           array('action_type' => ($old_model->get('correction_id') ? 'addcorrect' : 'edit'),
                                                                                 'new_model' => $finance_expenses_reason,
                                                                                 'old_model' => $old_model
                                                                           ));
                //show success message
                if ($old_model->get('correction_id')) {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_addcorrect_success'), '', -1);
                    $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_quantity_change_success',
                                                            array($old_model->getModelTypeName())));
                } else {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_edit_success',
                                                            array($finance_expenses_reason->getModelTypeName())), '', -1);
                }
                if ($old_model->get('distributed') == PH_FINANCE_DISTRIBUTION_YES) {
                    $url = sprintf('<a target="_blank" href="%s?%s=%s&amp;%s=%s&amp;%s=distribute&amp;distribute=%d">%s</a>',
                                   $_SERVER['PHP_SELF'],
                                   $this->registry['module_param'], 'finance',
                                   $this->registry['controller_param'], 'expenses_reasons',
                                   'expenses_reasons', $old_model->get('id'),
                                   $old_model->getModelTypeName());
                    $this->registry['messages']->setWarning($this->i18n('warning_finance_expenses_reasons_distribution_deleted',
                                                            array('reason_type_name' => $url)));
                }
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
                $this->redirect($this->module, 'view', 'view=' . ($old_model->get('correction_id') ?: $old_model->get('id')));
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_addcorrect_failed',
                                                                  array($old_model->getModelTypeName())), '', -2);
                //ToDo - set non editable properties as currency, amount ...
                $fer = clone $finance_expenses_reason;
                $finance_expenses_reason = $old_model;
                $finance_expenses_reason->set('parent_name', ($old_model->get('name') ? $old_model->get('name') : $old_model->get('type_name')), true);
                $finance_expenses_reason->set('name', $request->get('name'), true);
                $finance_expenses_reason->set('invoice_num', $request->get('invoice_num'), true);
                $finance_expenses_reason->set('company_data', $fer->get('company_data'), true);
                $finance_expenses_reason->set('employee1', $fer->get('employee1'), true);
                $finance_expenses_reason->set('issue_date', $request->get('issue_date'), true);
                $finance_expenses_reason->set('date_of_payment', $request->get('date_of_payment'), true);
                $finance_expenses_reason->set('description', $request->get('description'), true);
                $finance_expenses_reason->set('department', $fer->get('department'), true);
                $finance_expenses_reason->set('group', $fer->get('group'), true);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fer.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_expenses_reason)) {
            $this->checkAccessOwnership($finance_expenses_reason);
            $this->registry->set('get_old_vars', false, true);
            $old_type = $finance_expenses_reason->get('type');
            $finance_expenses_reason->set('parent_type', $finance_expenses_reason->get('type'), true);
            $finance_expenses_reason->set('type', PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON, true);

            if (!$request->isPost()) {
                // set name to an empty string
                $finance_expenses_reason->set('parent_name', ($finance_expenses_reason->get('name') ? $finance_expenses_reason->get('name') : $finance_expenses_reason->get('type_name')), true);
                $finance_expenses_reason->set('name', '', true);
                $finance_expenses_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);

                // set default values for group and department
                $finance_expenses_reason->setGroup($finance_expenses_reason->get('group'));
                $finance_expenses_reason->setDepartment($finance_expenses_reason->get('department'));
            }
            $old_status = $finance_expenses_reason->get('status');
            $finance_expenses_reason->set('status', 'opened', true);
            $finance_expenses_reason->getGT2Vars();
            $finance_expenses_reason->set($old_status, 'opened', true);
            $gt2 = $finance_expenses_reason->get('grouping_table_2');
            $finance_expenses_reason->set('type', $old_type, true);

            if (empty($gt2)) {
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_invoice_no_quantity',
                                                      $finance_expenses_reason->getModelTypeName()));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $finance_expenses_reason->get('id'));
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add payment
     */
    private function _addPayment() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        // the model from the DB
        $filters['where'] = array('fer.id = ' . $id, 'fer.annulled_by = 0');
        $filters['model_lang'] = $request->get('model_lang');
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        $this->old_model = clone $finance_expenses_reason;

        if (!empty($finance_expenses_reason)) {
            // check if action is allowed for model
            $this->checkAccessOwnership($finance_expenses_reason);

            // get paid amount
            $finance_expenses_reason->getPaidAmount();
            $finance_expenses_reason->set('total_with_vat', abs($finance_expenses_reason->get('total_with_vat')), true);
            $old_status = $finance_expenses_reason->get('status');

            // check if details are submitted via POST
            if ($request->isPost()) {
                // set values from request to model
                $request_properties = array('payment_sum', 'container_currency', 'container_rate', 'container_amount', 'payment_date', 'company_data', 'payment_reason');
                foreach ($request_properties as $prop) {
                    if ($prop == 'company_data') {
                        $finance_expenses_reason->set('payment_container', $request->get($prop), true);
                    } else {
                        $finance_expenses_reason->set($prop, $request->get($prop), true);
                    }
                }
                if ($payment_id = $finance_expenses_reason->addPayment()) {
                    require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                    $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                               array('action_type' => 'addpayment',
                                                                                     'new_model' => $finance_expenses_reason,
                                                                                     'old_model' => $finance_expenses_reason
                                                                               ));
                    //show success message
                    $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_addpayment_success',
                                                            array($finance_expenses_reason->getModelTypeName())), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    $this->actionCompleted = true;
                    //redirect the user to the newly created payment
                    $url = sprintf('%s?%s=finance&%s=payments&payments=view&view=%d',
                        $_SERVER['PHP_SELF'], $this->registry['module_param'],
                        $this->registry['controller_param'], $payment_id);
                    $this->registry->set('redirect_to_url', $url, true);
                    $this->registry->set('exit_after', true, true);
                } else {
                    //some error occurred
                    $finance_expenses_reason->set('status', $old_status, true);
                    $container_amount = round(($finance_expenses_reason->get('total_with_vat') - $finance_expenses_reason->get('paid_amount')) * $finance_expenses_reason->get('container_rate'), 2);
                    $finance_expenses_reason->set('container_amount', $container_amount, true);
                    $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_addpayment_failed',
                                                          array($finance_expenses_reason->getModelTypeName())), '', -2);
                    $finance_expenses_reason->set('payment_container_data', $this->registry['request']->get('company_data'), true);
                }
            } else {
                // sets container currency, suggested conversion rate and converted amount to model
                $finance_expenses_reason->prepareContainerRate(false);
            }
            $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * View model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fer.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_expenses_reason)) {
            // check if action is allowed for model
            $this->checkAccessOwnership($finance_expenses_reason);

            $finance_expenses_reason->getGT2Vars();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get the model and its old values
            $filters = array('where' => array ('fer.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $old_model = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);
            $finance_expenses_reason->set('id', $id, true);

            //the customer is necessary because the customer name and address should be translated as well
            $finance_expenses_reason->set('customer', $old_model->get('customer'), true);
            $this->registry->set('get_old_vars', false, true);

            if ($finance_expenses_reason->save()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fer.id = ' . $id),
                                 'model_lang' => $request->get('model_lang'));
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
                $finance_expenses_reason->getGT2Vars();

                //show success message
                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                           array('action_type' => 'translate',
                                                                                 'new_model' => $finance_expenses_reason,
                                                                                 'old_model' => $old_model
                                                                           ));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_translate_success',
                                                        array($finance_expenses_reason->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //register the model, with all the posted details
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_translate_failed',
                                                      array($finance_expenses_reason->getModelTypeName())), '', -2);
                //register the model, with all the posted details
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('fer.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_expenses_reason)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * History of the reason
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'fer.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($finance_expenses_reason && $this->checkAccessOwnership($finance_expenses_reason, false)) {
                if (!$this->registry->isRegistered('finance_expenses_reason')) {
                    $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
                }

                require_once $this->viewersDir . 'finance.expenses_reasons.history.viewer.php';
                $viewer = new Finance_Expenses_Reasons_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($finance_expenses_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_expenses_reason);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Communication concerning the expense reason (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fer.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        if (!empty($finance_expenses_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_expenses_reason);

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Check access and ownership of the current user to a certain model object.
     * Also performs action-specific additional checks.
     *
     * (non-PHPdoc)
     * @see Controller::checkAccessOwnership()
     *
     * @param Finance_Expenses_Reason $model - model to check for
     * @param bool $redirect - if failure of check should cause a redirect or return false as result
     * @param string $action - action to check for
     * @return bool - result of check
     */
    public function checkAccessOwnership($model, $redirect = true, $action = '') {
        $result = parent::checkAccessOwnership($model, $redirect, $action);

        // if general check has passed and there is a model object, check
        // action-specific conditions
        if ($result && is_object($model)) {
            if (empty($action)) {
                $action = $this->action;
            }
            switch ($action) {
                case 'edit':
                    if (
                        $model->get('status') != 'opened' &&
                        $model->get('type') != PH_FINANCE_TYPE_EXPENSES_INVOICE
                    ) {
                        $result = false;
                    }
                    break;
                case 'distribute':
                    if (
                        $model->get('status') != 'finished' ||
                        $model->get('payment_status') == 'nopay' ||
                        $model->get('distributed') == PH_FINANCE_DISTRIBUTION_NONE ||
                        in_array($model->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE))
                    ) {
                        $result = false;
                    }
                    break;
                case 'allocate_costs':
                    $result = $model->checkAllocateCosts();
                    break;
                case 'ajax_update_allocated_status':
                    $result = $model->checkUpdateAllocatedStatus();
                    break;
                case 'addhandover':
                    if (!$this->actionCompleted) {
                        //get the type of the first document in the chain
                        if (!$model->isDefined('parent_type')) {
                            $model->set('parent_type', $model->getExpensesRootType(), true);
                        }
                        $root_type = $model->get('parent_type');
                        // if proforma invoice has been issued as a primary document, get the settings of the invoice
                        if ($root_type != $model->get('type') && $root_type == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                            $root_type = PH_FINANCE_TYPE_EXPENSES_INVOICE;
                        }
                        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                        $financeType = Finance_Documents_Types::searchOne(
                            $this->registry,
                            array('where' => array("fdt.id = '{$root_type}'"))
                        );

                        $direction = $this->registry['request']->get('handover_direction');

                        if (
                            $financeType->get('commodity') == 'none' ||
                            !$model->checkAddingHandover($direction, true) ||
                            $model->get('payment_status') == 'nopay' ||
                            // we cannot have both handovers and invoice/proforma for a reason
                            $model->get('type') > PH_FINANCE_TYPE_MAX && !($model->checkAddingInvoice() && $model->checkAddingInvoice(true))
                        ) {
                            if ($redirect) {
                                $this->registry['messages']->setError($this->i18n('error_finance_handover_issue_failed'), '', -1);
                                $this->registry['messages']->insertInSession($this->registry);
                            }
                            $result = false;
                        }
                    }
                    break;
                case 'payments':
                    if (
                        $model->get('status') != 'finished' ||
                        in_array($model->get('payment_status'), array('nopay', 'invoiced')) ||
                        $model->get('type') == PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON ||
                        (
                            $model->get('type') > PH_FINANCE_TYPE_MAX ||
                            $model->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE
                        ) &&
                        !$model->checkAddingInvoice()
                    ) {
                        $result = false;
                    }
                    break;
                case 'addcorrect':
                    if (
                        $model->get('payment_status') == 'nopay' ||
                        $model->get('type') > PH_FINANCE_TYPE_MAX && !($model->checkAddingInvoice() && $model->checkAddingInvoice(true)) ||
                        $model->get('allocated_status') == 'allocated' ||
                        $model->hasAllocatedCosts()
                    ) {
                        $result = false;
                    }
                    break;
                case 'addpayment':
                    if (
                        $model->get('status') != 'finished' ||
                        in_array($model->get('payment_status'), array('nopay', 'paid', 'invoiced')) ||
                        $model->get('type') == PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON ||
                        (
                            $model->get('type') > PH_FINANCE_TYPE_MAX ||
                            $model->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE
                        ) && !$model->checkAddingInvoice() ||
                        !$model->checkAddPaymentPermissions() ||
                        !$this->checkActionPermissions('finance_payments', 'add')
                    ) {
                        $result = false;
                    }
                    break;
                case 'addcredit':
                case 'adddebit':
                    if ($model->get('type') != PH_FINANCE_TYPE_EXPENSES_INVOICE || $model->get('payment_status') == 'nopay') {
                        $result = false;
                    }
                    break;
                case 'annul':
                    $result = $model->allowAnnul();
                    break;
            }

            // redirect to view mode of model
            if (!$result && $redirect) {
                $this->redirect('finance', 'view', 'view=' . $model->get('id'));
            }
        }

        return $result;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $this->settings_assign = array();
        if ($this->model && $this->model->get('type')) {
            $this->settings_assign = $this->registry['config']->getParamAsArray('finance', 'assignment_types_' . $this->model->get('type'));
        }

        $actions = parent::getActions($action_defs);

        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        if ($this->model && $this->model->get('type')) {
            $financeType = Finance_Documents_Types::searchOne($this->registry,
                                                              array('where' => array('fdt.id=' . $this->model->get('type'))));
        }

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['edit']) && $this->model->get('status') != 'opened') {
                if ($this->model->get('type') != PH_FINANCE_TYPE_EXPENSES_INVOICE || $this->model->get('annulled_by')) {
                    unset($actions['edit']);
                } else {
                    //we can partially edit finished expenses invoices
                    $this->model->editFinished = true;
                }
            }
        }

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        if (isset($actions['add']) || isset($actions['printlist'])) {
            $customize = '';
            $found = 0;

            $custom_filters = array();
            if ($this->registry['request']->get('type')) {
                $customize = 'fdt.id="' . $this->registry['request']->get('type') . '"';
                $found++;
            } else if ($this->registry['request']->get('type_section')) {
                $customize = 'fdt.type_section="' . $this->registry['request']->get('type_section') . '"';
                $found++;
            } else if ($this->registry['request']->isRequested('search_fields')) {
                $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
                $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
                $custom_filters['values'] = $this->registry['request']->get('values');
            } else if ($this->registry['session']->isRequested($this->action . '_finance_expenses_reason')) {
                $custom_filters = $this->registry['session']->get($this->action . '_finance_expenses_reason');
            }

            if (!empty($custom_filters)) {
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#fer.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize = 'fdt.id="' . $custom_filters['values'][$key] . '"';
                            $found++;
                        }
                    }
                }
            }
        }

        if (isset($actions['add'])) {
            $filters = array('where' => array('fdt.model = "Finance_Expenses_Reason"',
                                              'fdt.id != ' . PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON,
                                              'fdt.active=1'),
                             'sort' => array('fdti18n.name ASC'),
                             'sanitize' => true);

            // if there is a model, the only available type for add and multiadd
            // will be the options for the current type or the current section
            if ($found == 1 && $customize) {
                $filters['where'][] = $customize;
            }
            $financeTypes = Finance_Documents_Types::search($this->registry, $filters);
            $options = array();
            foreach ($financeTypes as $type) {
                //check permissions by type
                if ($this->checkActionPermissions($module_check . $type->get('id'), 'add')) {
                    $options[] = array('label' => $type->get('name'), 'option_value' => $type->get('id'));
                } elseif ($this->model && $this->model->get('id') && $this->model->get('type') == $type->get('id')) {
                    //if the user cannot add model, he should not be able to clone models of this type
                    unset($actions['clone']);
                }
            }

            //prepare companies
            $customize_company = '';
            $found_company = 0;
            if (!empty($custom_filters)) {
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#fer.company#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize_company = 'fc.id="' . $custom_filters['values'][$key] . '"';
                            $found_company++;
                        }
                    }
                }
            }
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $filters = array('lang' => $this->registry['lang'],
                             'sanitize' => true,
                             'where' => array('fc.active=1'));
            // if there one company has been searched, the only available company to add document for
            // will be the options for the current type or the current section
            if ($found_company == 1 && $customize_company) {
                $filters['where'][] = $customize_company;
            }
            $companies_list = Finance_Companies::search($this->registry, $filters);
            $default_company = array();
            $default_company_id = '';
            $companies = array();

            foreach ($companies_list as $key => $company) {
                if ($this->registry['currentUser']->get('default_company') == $company->get('id')) {
                    $default_company_id = $company->get('id');
                    $default_company = array('label' => $company->get('name'),
                                             'option_value' => $company->get('id'));
                } else {
                    $companies[] = array('label' => $company->get('name'),
                                         'option_value' => $company->get('id'));
                }
            }

            if ($default_company) {
                array_unshift($companies, $default_company);
            }

            if (!empty($options)) {
                $add_options = array (
                    array (
                        'custom_id' => 'type_',
                        'name' => 'type',
                        'type' => 'dropdown',
                        'required' => 1,
                        'really_required' => 1,
                        'label' => $this->i18n('finance_documents_type'),
                        'help' => $this->i18n('finance_documents_add_legend'),
                        'options' => $options,
                        'onchange' => 'showTypeAdditionalFields(this)',
                        'value' => ($this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : '',
                        'custom_class' => (count($options) > 1 && !$this->registry['request']->get('type')) ? 'undefined' : ''
                    ),
                    array (
                        'custom_id' => 'company_',
                        'name' => 'company',
                        'type' => 'dropdown',
                        'required' => 1,
                        'really_required' => 1,
                        'label' => $this->i18n('finance_expenses_reasons_company'),
                        'help' => $this->i18n('finance_expenses_reasons_company'),
                        'options' => $companies,
                        'value' => ($this->registry['request']->get('company')) ?
                                    $this->registry['request']->get('company') :
                                    $default_company_id,
                        'custom_class' => (count($companies) > 1 && !$this->registry['request']->get('company') && !$default_company_id) ? 'undefined' : ''
                    ),
                );
                if (count($options) == 1 && in_array($options[0]['option_value'], array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                    //hide the company row (there is no need to select company when adding credit/debit, it is inheritted from the expense invoice)
                    $add_options[1]['hidden'] = true;

                    $add_options[] = array (
                        'custom_id' => 'invoice_id',
                        'name' => 'invoice_id',
                        'type' => 'autocompleter',
                        'required' => 1,
                        'label' => $this->i18n('finance_expenses_reasons_for_invoice'),
                        'help' => $this->i18n('finance_expenses_reasons_for_invoice'),
                        'autocomplete_type' => 'finance_expenses_reasons',
                        'min_chars' => 2,
                        'autocomplete_buttons_hide' => 'search,refresh,add',
                        'autocomplete_buttons' => 'clear',
                        'filters' => array('<type>' => strval(PH_FINANCE_TYPE_EXPENSES_INVOICE), '<status>' => 'finished')
                    );
                }
                $actions['add']['options'] = $add_options;

                // check if there is only one type and company and if so
                //  disables the pre-add screen
                $selected_type = '';
                $selected_company = '';
                if (count($options)==1 && count($companies)==1) {
                    $first_type = reset($options);
                    $selected_type = $first_type['option_value'];

                    $first_company = reset($companies);
                    $selected_company = $first_company['option_value'];
                }

                // set options if there is only one type available
                if ($selected_type &&
                    !in_array($selected_type, array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE,
                                                    PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE)) &&
                    $selected_company) {
                    $actions['add']['url'] .= '&amp;type=' . $selected_type . '&amp;company=' . $selected_company;
                    $actions['add']['options'] = '';
                } else {
                    $actions['add']['ajax_no'] = 1;
                }
            } else {
                unset($actions['add']);
            }
        } else {
            unset($actions['add']);
            //IMPORTANT: the clone action is dependent on the add action,
            //because cloning is actually adding new record
            unset($actions['clone']);
        }

        //get permissions of the currently logged user
        $permissions = $this->getUserPermissions();

        if (isset($actions['assign']) && !empty($this->settings_assign) && !$this->model->get('annulled_by')) {
            $actions['assign']['options'] = 0;
        } else {
            unset($actions['assign']);
        }
        if (isset($actions['observer']) && in_array('observer', $this->settings_assign) && !$this->model->get('annulled_by')) {
            $actions['observer']['options'] = 0;
            if (Finance_Expenses_Reasons::checkObserver($this->registry, $this->model)) {
                $actions['observer']['img'] = 'stopobserve';
                $actions['observer']['label'] = $this->i18n('stopobserve');
            } else {
                $actions['observer']['img'] = 'observe';
                $actions['observer']['label'] = $this->i18n('observe');
            }
        } else {
            unset($actions['observer']);
        }

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add pattern filter: get patterns only if they are for this company or they are not for a specific company
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            if (empty($patterns)) {
                //remove generate action
                unset($actions['generate']);
            } else {
                $actions['generate']['options'] = 1;
            }
        }

        // communications action
        if (isset($actions['communications'])) {
            $actions['communications']['ajax_no'] = 1;
            $actions['communications']['drop_menu'] = true;
            $actions['communications']['hide_label'] = true;

            if (isset($actions['emails']) || isset($actions['comments'])) {
                if (isset($actions['comments'])) {
                    $actions['communications']['options']['comments']['img'] = 'comments';
                    $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                    $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    unset($actions['comments']);
                }
                if (isset($actions['emails'])) {
                    $actions['communications']['options']['emails']['img'] = 'email';
                    $actions['communications']['options']['emails']['label'] = $this->i18n('finance_expenses_reasons_action_email');
                    $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    unset($actions['emails']);
                }
            } else {
                unset($actions['communications']);
            }
        } else {
            if (isset($actions['emails'])) {
                unset($actions['emails']);
            }
            if (isset($actions['comments'])) {
                unset($actions['comments']);
            }
        }

        if (isset($actions['print'])) {
            $patterns_options = array();

            //get the id of the default document type print template
            $default_pattern_id = 0;
            if ($financeType && $financeType->get('default_pattern')) {
                $default_pattern_id = $financeType->get('default_pattern');
            }

            //get all generate/print patterns for this type
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array('where' => array(
                                              'p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                                      'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                                      'model_lang' => $this->getModelLang(),
                                      'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add filter: get patterns only if they are for this company or they are not for a specific company
                $filters_patterns['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters_patterns);

            $available_patterns = array();
            foreach ($patterns as $pattern) {
                $available_patterns[] = $pattern->get('id');
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['print']['url'] . '&amp;pattern=' . $pattern->get('id'),
                    'target'    => '_blank',
                );
            }

            if (empty($patterns_options)) {
                unset($actions['print']);
            } else {
                if ($default_pattern_id && in_array($default_pattern_id, $available_patterns)) {
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $default_pattern_id;
                } elseif (count($available_patterns) == 1) {
                    // sets the first pattern in the list as default and assigns a link to the direct print
                    list($first_pattern) = $patterns_options;
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $first_pattern['id'];
                } else {
                    $actions['print']['url'] = '#';
                }
                $actions['print']['drop_menu'] = true;
                $actions['print']['no_tab'] = true;
                $actions['print']['label'] = '';
                $actions['print']['target'] = '_blank';
                $actions['print']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'print';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['print']['img'] .= '_plus';
                }
                $actions['print']['options'] = $patterns_options;
            }
        }

        if (isset($actions['setstatus']) && $this->model && $this->model->get('id') && !$this->model->get('annulled_by')) {
            //prepare substatuses
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_substatuses.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array('fdt.model = \'' . $this->model->modelName . '\'',
                                              'fdt.id = ' . $this->model->get('type')),
                                              'fds.active = 1');
            $substatuses = Finance_Documents_Substatuses::search($this->registry, $filters);
            $this->model->set('substatuses', $substatuses, true);

            $actions['setstatus']['options'] = array('label' => $this->i18n('finance_status_btn'), 'form_method' => 'post');
            $actions['setstatus']['ajax_no'] = 1;
            $actions['setstatus']['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
            $actions['setstatus']['template'] = PH_MODULES_DIR . 'finance/templates/' . '_action_status.html';
            $actions['setstatus']['model_id'] = $this->model->get('id');
        } else {
            unset($actions['setstatus']);
        }

        if (isset($actions['tag']) && $this->model && $this->model->get('id') && $this->model->getAvailableTags()) {

            $this->model->getTags();

            if ($this->model->checkPermissions('tags_view') && ($this->model->get('tags') && array_intersect($this->model->get('tags'), array_keys($this->model->get('available_tags'))) || $this->model->checkPermissions('tags_edit'))) {
                $actions['tag']['options'] = array('label' => $this->i18n('confirm_tags'));
                $actions['tag']['ajax_no'] = 1;
                $actions['tag']['template'] = '_action_tag.html';
                $actions['tag']['model_id'] = $this->model->get('id');
            } else {
                unset($actions['tag']);
            }
        } else {
            unset($actions['tag']);
        }

        if ($this->model && $this->model->get('id')) {
            $add_invoice = $this->model->checkAddingInvoice();
        } else {
            $add_invoice = false;
        }

        // !!! IMPORTANT !!!: If these conditions are changed, then update the conditions here too: Finance_Documents_Type->updateRoleDefinitions()
        if (isset($actions['annul']) && $this->model && $this->model->get('id') && $this->model->allowAnnul()
            && ($add_invoice && $this->model->get('type') > PH_FINANCE_TYPE_MAX || $this->model->get('type') < PH_FINANCE_TYPE_MAX)) {

            $actions['annul']['confirm'] = 1;
            if ($this->model->get('type') == PH_FINANCE_TYPE_EXPENSES_INVOICE) {
                $actions['annul']['confirm_label'] = 'annul_expenses_invoice';
            } elseif ($this->model->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                $actions['annul']['confirm_label'] = 'annul_expenses_proforma_invoice';
            } elseif ($this->model->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE) {
                $actions['annul']['confirm_label'] = 'annul_expenses_credit_notice';
            } elseif ($this->model->get('type') == PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE) {
                $actions['annul']['confirm_label'] = 'annul_expenses_debit_notice';
            } else {
                $actions['annul']['confirm_label'] = 'annul_expenses_reason';
            }
        } else {
            unset($actions['annul']);
        }

        // !!! IMPORTANT !!!: If these conditions are changed, then update the conditions here too: Finance_Documents_Type->updateRoleDefinitions()
        if (!isset($actions['payments']) || !$this->model || !$this->model->get('id') || $this->model->get('status') != 'finished'
                || $this->model->get('annulled_by')
                || in_array($this->model->get('payment_status'), array('nopay', 'invoiced'))
                || $this->model->get('type') == PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON
                || ($this->model->get('type') > PH_FINANCE_TYPE_MAX ||
                    $this->model->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) && !$add_invoice) {
            unset($actions['payments']);
        }

        if (isset($actions['distribute']) && $this->model && $this->model->get('id') &&
        $this->model->get('status') == 'finished' && !$this->model->get('annulled_by') &&
        $this->model->get('payment_status') != 'nopay' &&
        $this->model->get('distributed') != PH_FINANCE_DISTRIBUTION_NONE &&
        !in_array($this->model->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE))) {

        } else {
            unset($actions['distribute']);
        }

        if (array_key_exists('allocate_costs', $actions) && $this->model && $this->model->checkAllocateCosts()) {

        } else {
            unset($actions['allocate_costs']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {
            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^fdt\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^fdt\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        // check the current action and sets the alternative actions
        // for view and edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } elseif ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit nor view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } elseif (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && !empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActionOptions($action_name = '') {
        //get model for this class
        $this->getModel();

        //get permissions of the currently logged user
        $this->getUserPermissions();

        $actions = parent::getActions(array($action_name));

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once(PH_MODULES_DIR . 'patterns/models/patterns.factory.php');
            $filters = array('where' => array('p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add filter: get patterns only if they are for this company or they are not for a specific company
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            $_options_patterns = array();
            foreach ($patterns as $pattern) {
                $_options_patterns[] = array(
                    'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                    'option_value' => $pattern->get('id'));
            }

            if (empty($_options_patterns)) {
                //remove generate action
                unset($actions['generate']);
            } else {
                //prepare generate options
                $generate_options = array (
                    array (
                        'custom_id' => 'pattern_',
                        'name' => 'pattern',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('finance_pattern'),
                        'help' => $this->i18n('finance_pattern'),
                        'options' => $_options_patterns,
                        'value' => ($this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : ''),
                );
                $actions['generate']['options'] = $generate_options;
            }
        }

        return $actions;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _generate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('generate'));
        $filters = array('where' => array('fer.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_expenses_reason)) {
            //get the specified pattern
            $pattern_id = $request->get('pattern');
            if (empty($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_expenses_reason->get('id'));
            }

            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $pattern_id,
                                              'p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $finance_expenses_reason->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0',
                                              '(p.company = \'' . $finance_expenses_reason->get('company') . '\' OR p.company = \'0\')'),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);
            if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //invalid pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_expenses_reason->get('id'));
            }

            //get all generated files for the selected pattern
            $files = $finance_expenses_reason->getGeneratedFiles(array('pattern_id' => $pattern_id));

            //get file details
            $revision_id = $request->get('revision');
            if ($revision_id && $files) {
                foreach ($files as $file) {
                    if ($file->get('id') == $revision_id) {
                        //prepare selected revision details
                        $finance_expenses_reason->set('revision', $file, true);
                        break;
                    }
                }
            }

            //get revision details
            if (!empty($files)) {
                $finance_expenses_reason->set('revisions', $files, true);
            }
        }

        if ($request->isPost()) {
            if (!empty($finance_expenses_reason)) {
                $patterns_vars = $finance_expenses_reason->getPatternsVars();
                $finance_expenses_reason->extender = new Extender();
                $finance_expenses_reason->extender->model_lang = $finance_expenses_reason->get('model_lang');
                $finance_expenses_reason->extender->module = $this->module;
                foreach ($patterns_vars as $key => $value) {
                    $finance_expenses_reason->extender->add($key, $value);
                }
                switch ($request->get('format')) {
                    case 'xls':
                        // TO DO
                        break;
                    case 'csv':
                        // TO DO add option fields and also create a pattern which indicates the header columns
                        break;
                    case 'pdf':
                    default:
                    $this->old_model = clone $finance_expenses_reason;
                    if ($file_id = $finance_expenses_reason->generatePDF()) {
                        if ($request->get('submit_button') != 'preview') {
                            $this->registry['messages']->setMessage($this->i18n('message_finance_generate_success'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);

                            //save history
                            require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                            Finance_Expenses_Reasons_History::saveData($this->registry, array('model' => $finance_expenses_reason,
                                                                                              'action_type' => 'generate',
                                                                                              'pattern' => $pattern->get('id'),
                                                                                              'generated_file' => $file_id));

                            $finance_expenses_reason->set('file_id', $file_id, true);
                            $this->actionCompleted = true;
                        }
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                    }
                    break;
                }
            }
        }

        if (!empty($finance_expenses_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_expenses_reason);

            //register pattern to the registry
            if ($pattern) {
                $this->registry->set('pattern', $pattern->sanitize());
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _print() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('print'));
        $filters = array('where' => array('fer.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_expenses_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_expenses_reason);

            //get the specified pattern
            $pattern_id = $request->get('pattern');
            if (empty($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_expenses_reason->get('id'));
            }

            //get the pattern
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $pattern_id,
                                              'p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $finance_expenses_reason->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0',
                                              '(p.company = \'' . $finance_expenses_reason->get('company') . '\' OR p.company = \'0\')'),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);
            if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //invalid pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_expenses_reason->get('id'));
            }

            $patterns_vars = $finance_expenses_reason->getPatternsVars();
            $finance_expenses_reason->extender = new Extender();
            $finance_expenses_reason->extender->model_lang = $finance_expenses_reason->get('model_lang');
            $finance_expenses_reason->extender->module = $this->module;
            foreach ($patterns_vars as $key => $value) {
                $finance_expenses_reason->extender->add($key, $value);
            }

            if (!empty($pattern) && $pattern->get('force_generate')) {
                //generate and save file and get its id
                $this->old_model = clone $finance_expenses_reason;

                $result = false;
                if ($pattern->get('not_regenerate_finished_record') && $finance_expenses_reason->get('status')=='finished') {
                    $previous_generated_file = $finance_expenses_reason->getLastGeneratedFile($pattern);

                    if ($previous_generated_file) {
                        $result = $previous_generated_file->get('id');
                    }
                }

                if (!$result) {
                    $result = $finance_expenses_reason->generatePDF();
                }

                if ($result) {
                    $finance_expenses_reason->set('file_id', $result, true);
                    if (!$this->registry->isRegistered('finance_expenses_reason')) {
                        $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
                    }

                    //save history
                    require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                    Finance_Expenses_Reasons_History::saveData($this->registry, array('model' => $finance_expenses_reason,
                                                                                      'action_type' => 'print',
                                                                                      'pattern' => $pattern->get('id'),
                                                                                      'generated_file' => $result));

                    // show the file
                    require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                    $file_id = $request->get('file');
                    $filters = array('where'    => array('f.id = ' . $result),
                                     'sanitize' => true);
                    $file = Files::searchOne($this->registry, $filters);

                    $file->viewFile();
                    exit;
                }
            } else {

                //save history
                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                Finance_Expenses_Reasons_History::saveData($this->registry, array('model' => $finance_expenses_reason,
                                                                                  'action_type' => 'print',
                                                                                  'pattern' => $pattern->get('id'),
                                                                                  'generated_file' => false));

                //generate file to the browser window
                $result = $finance_expenses_reason->generatePDF('browser_mode');
            }

            if ($result) {
                //the pdf content is displayed in the browser window
                exit;
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'view', array('view' => $finance_expenses_reason->get('id')));
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param mixed $ids Array of ids or crypted string with array of ids
     */
    private function _multiPrint($ids = '') {
        $request = &$this->registry['request'];

        if (isset($_SERVER['HTTP_REFERER'])) {
            preg_match('/&expenses_reasons=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'list';
        }

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        if (!is_array($ids)) {
            Finance_Expenses_Reasons::decryptIdsMultiprint($this->registry, $ids);
            //set to request as decrypted array
            $request->set('items', $ids, 'all', true);
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $finance_expenses_reasons = array();
        if ($ids) {
            $filters = array('where' => array('fer.id IN (' . implode(', ', $ids) . ')',
                                              'fer.annulled_by = 0'),
                             'model_lang' => $this->registry->get('lang'),
                             'sanitize' => true);
            $finance_expenses_reasons = Finance_Expenses_Reasons::search($this->registry, $filters);
        }

        //if no models or annulled checked
        if (empty($finance_expenses_reasons) || count($ids) != count($finance_expenses_reasons)) {
            $this->registry['messages']->setError($this->i18n('error_no_finance_expenses_reasons_or_annulled'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, $after_action);
        }

        $type = $finance_expenses_reasons[0]->get('type');
        foreach ($finance_expenses_reasons as $finance_expenses_reason) {
            $type_i = $finance_expenses_reason->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, $after_action);
                return true;
            }
        }

        require_once $this->modelsDir . 'finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.id = ' . $type),
                         'sanitize' => true);
        $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $type_name_plural = $fin_doc_type && $fin_doc_type->get('name_plural') ? $fin_doc_type->get('name_plural') : $this->i18n('finance_expenses_reasons');

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        if (empty($pattern_id)) {
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        //get the pattern
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1',
                                          'p.format = "pdf"'),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        // Check if the models corresponds to the selected pattern
        // Check: company
        $pattern_company = $pattern->get('company');
        if ($pattern_company && !Finance_Expenses_Reasons::checkModelsForCompany($this->registry, Finance_Expenses_Reasons::$modelName, $pattern_company, $ids)) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $company_filters = array('sanitize'   => true,
                                     'model_lang' => $this->registry->get('lang'),
                                     'where'      => array('id = ' . $pattern_company));
            $company = Finance_Companies::searchOne($this->registry, $company_filters);
            $this->registry['messages']->setError($this->i18n('error_finance_print_pattern_companies', array($company->get('name'))));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        $result = Finance_Expenses_Reasons::multiPrint($this->registry, $this);
        if ($result) {
            //the pdf content is displayed in the browser window
            exit;
        } else {
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
        }

        $this->redirect($this->module, $after_action);
    }

    /**
     * Attaches files to model
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        //$id = $request->get($this->action);
        $id = $request->get('attachments');

        $filters = array('where' => array('fer.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        $added_files = array(0 => array());

        if ($request->isPost()) {

            $modified_files = array();
            $modified_genfiles = array();

            $erred_modified_files = array();
            $erred_modified_genfiles = array();
            $erred_added_files = array();
            $success_added_files = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //edit existing attachments
            $names        = $request->get('file_names');
            $descriptions = $request->get('file_descriptions');
            $permissions  = $request->get('file_permissions');
            $revisions    = $request->get('file_revisions');
            $filenames    = $request->get('file_filenames');
            $files        = !empty($_FILES['file_paths']) ? $_FILES['file_paths'] : array();
            $indices      = $request->get('file_indices');

            if (!empty($names)) {
                foreach ($names as $idx => $name) {
                    $index = $indices[$idx];

                    if (!empty($files['tmp_name'][$idx])) {
                        $file = array(
                            'name'     => $files['name'][$idx],
                            'type'     => $files['type'][$idx],
                            'tmp_name' => $files['tmp_name'][$idx],
                            'error'    => $files['error'][$idx],
                            'size'     => $files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($names[$idx])) {
                        $names[$idx] = $files['name'][$idx];
                    }
                    $params = array(
                        'id'          => $idx,
                        'name'        => $names[$idx],
                        'filename'    => $filenames[$idx],
                        'description' => $descriptions[$idx],
                        'revision'    => $revisions[$idx],
                        'permission'  => $permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $finance_expenses_reason->sanitize())) {
                        $erred_modified_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_edit') . ' ' . $index, 'edit_attachment_' . $idx);
                    }
                    $finance_expenses_reason->unsanitize();
                    $modified_files[$idx] = $params;
                }
            }

            //edit existing generated files
            $generated_names        = $request->get('g_file_names');
            $generated_descriptions = $request->get('g_file_descriptions');
            $generated_permissions  = $request->get('g_file_permissions');
            $generated_revisions    = $request->get('g_file_revisions');
            $generated_indices      = $request->get('g_file_indices');

            if (!empty($generated_names)) {
                foreach ($generated_names as $idx => $name) {
                    $index = $generated_indices[$idx];

                    $file = array();
                    $params = array(
                        'id'          => $idx,
                        'name'        => $generated_names[$idx],
                        'description' => $generated_descriptions[$idx],
                        'permission'  => $generated_permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $finance_expenses_reason->sanitize())) {
                        $erred_modified_genfiles[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_gen_edit') . ' ' . $index, 'edit_gen_attachment_' . $idx);
                    }
                    $finance_expenses_reason->unsanitize();
                    $modified_genfiles[$idx] = $params;
                }
            }

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            $additional_descriptions = $request->get('a_file_descriptions');
            $additional_permissions  = $request->get('a_file_permissions');
            $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files['name'])) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx] ?? '',
                        'description' => $additional_descriptions[$idx] ?? '',
                        'revision'    => $additional_revisions[$idx] ?? '',
                        'permission'  => $additional_permissions[$idx] ?? 'all');

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $finance_expenses_reason->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }
                    $finance_expenses_reason->unsanitize();
                    $added_files[$idx] = $params;
                }
            }

            if ($modified_files && empty($erred_modified_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'modified_attachments'));*/
            } elseif (!empty($modified_files)) {
                $this->registry['modified_files'] = $modified_files;
                $this->registry['erred_modified_files'] = $erred_modified_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($modified_genfiles && empty($erred_modified_genfiles)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_gen_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'modified_gen'));*/
            } elseif (!empty($modified_genfiles)) {
                $this->registry['modified_genfiles'] = $modified_genfiles;
                $this->registry['erred_modified_genfiles'] = $erred_modified_genfiles;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'add_attachments'));*/
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files) && empty($erred_modified_files) && empty($erred_modified_genfiles)) {
                $this->actionCompleted = true;
                if (!$this->registry->isRegistered('finance_expenses_reason')) {
                    $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
                }

                return true;
            }

        }

        $this->registry['added_files'] = $added_files;

        if (!empty($finance_expenses_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_expenses_reason);

            $finance_expenses_reason->getAttachments();
            $finance_expenses_reason->getGeneratedFiles();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Fetches generated invoice file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'generate' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fer.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_expenses_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_expenses_reason);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $filters = array('where' => array('f.id = ' . $request->get('file')),
                             'sanitize' => true);
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $finance_expenses_reason->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $finance_expenses_reason->get('id'));
                    }
                    break;
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_expenses_reason->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_expenses_reason->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_expenses_reason->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_expenses_reason->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'attachments', 'attachments=' . $finance_expenses_reason->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Annul model
     */
    private function _annul() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $matches = array();
        if (isset($_SERVER['HTTP_REFERER'])) {
            //get referer's action
            preg_match('/&expenses_reasons=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $request->set('id', $id, 'all', true);
        $filters = array('where' => array('fer.id = ' . $request->get('id'), 'fer.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'));
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (empty($finance_expenses_reason)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        $this->checkAccessOwnership($finance_expenses_reason);

        if ($finance_expenses_reason->annul()) {
            //show message 'message_finance_annul_success'
            $this->registry['messages']->setMessage($this->i18n('message_finance_annul_success',
                                                    array($finance_expenses_reason->getModelTypeName())), '', -2);

            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_finance_annul_failed',
                                                  array($finance_expenses_reason->getModelTypeName())), '', -1);
        }

        //manually set custom after action so that the navigation is redirected to previous action
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        $this->redirect($this->module, $after_action, $after_action . '=' . $finance_expenses_reason->get('id'));

        return true;
    }

    /**
     * Change status of model
     */
    private function _status() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //get referer's action
        preg_match('/&expenses_reasons=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $request->set('id', $id, 'all', true);
        $finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);
        $filters = array('where' => array('fer.id = ' . $request->get('id')),
                         'model_lang' => $request->get('model_lang'));
        $old_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        $old_reason->sanitize();
        $this->old_model = clone $old_reason;

        //set some of the properties so that the counter could get a number
        $finance_expenses_reason->set('type', $old_reason->get('type'), true);
        $finance_expenses_reason->set('office', $old_reason->get('office'), true);
        $finance_expenses_reason->set('company', $old_reason->get('company'), true);

        $finance_expenses_reason->set('num', $old_reason->get('num'), true);
        $finance_expenses_reason->set('type_name', $old_reason->get('type_name'), true);
        $finance_expenses_reason->set('company_name', $old_reason->get('company_name'), true);
        // properties are required to check 'setstatus_unlock' permissions
        $finance_expenses_reason->set('added_by', $old_reason->get('added_by'), true);
        $finance_expenses_reason->set('group', $old_reason->get('group'), true);
        $finance_expenses_reason->set('department', $old_reason->get('department'), true);
        // needed for validation and automatic saving of balance of credit note
        $finance_expenses_reason->set('total_with_vat', $old_reason->get('total_with_vat'), true);
        $finance_expenses_reason->set('currency', $old_reason->get('currency'), true);
        $finance_expenses_reason->set('customer', $old_reason->get('customer'), true);
        // needed for allocated_status validation
        $finance_expenses_reason->set('allocated_status', $old_reason->get('allocated_status'), true);
        $finance_expenses_reason->set('issue_date', $old_reason->get('issue_date'), true);
        $finance_expenses_reason->set('fiscal_event_date', $old_reason->get('fiscal_event_date'), true);

        if ($finance_expenses_reason->setStatus()) {
            //show message 'message_documents_edit_success'
            $this->registry['messages']->setMessage($this->i18n('message_finance_status_success',
                                                    array($old_reason->getModelTypeName())), '', -2);

            $comment = '';
            if ($request->get('comment')) {
                require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                $comment = Comments::buildModel($this->registry);

                $comment->set('content', $request->get('comment'), true);
                $comment->set('subject', $this->i18n('finance_status_change_comment'), true);
                $comment->set('model', 'Finance_Expenses_Reason', true);
                $comment->set('model_id', $id, true);
                $comment->set('is_portal', ($request->get('is_portal') ? '1' : '0'), true);
                $comment->unsetProperty('id', true);

                if ($comment->save()) {
                    //show corresponding message
                    $this->registry['messages']->setMessage($this->i18n('message_finance_comments_add_success'), '', -1);
                } else {
                    //some error occurred
                    //show corresponding error(s)
                    $this->registry['messages']->setError($this->i18n('error_comments_add_failed'), '', -1);
                }
            }

            $filters = array('where' => array('fer.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $new_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
            $this->model = clone $new_reason;
            $this->model->sanitize();

            require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php';
            Finance_Expenses_Reasons_History::saveData($this->registry,
                                                       array('action_type' => 'status',
                                                             'new_model' => $new_reason,
                                                             'old_model' => $old_reason
                                                       ));

            if ($comment && $comment->get('id')) {
                $comment->saveHistory($new_reason);
            }

            if ($new_reason->get('payment_status') == 'paid') {
                Finance_Expenses_Reasons_History::saveData($this->registry,
                                                           array('action_type' => 'payments',
                                                                 'new_model' => $new_reason,
                                                                 'old_model' => $new_reason
                                                           ));
            }

            //set after action to view if have not permission
            if (!$new_reason->checkPermissions($after_action)) {
                $after_action = 'view';
            }

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_finance_status_failed',
                                                  array($old_reason->getModelTypeName())), '', -1);
        }

        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        if (!isset($matches[1]) || $matches[1] == 'search' || $matches[1] == 'list') {
            //set parameters in registry - check them in router
            //set redirect url
            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);
            //header("Location: " . $_SERVER['HTTP_REFERER']);exit;
        } else {
            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            $this->registry->set('exit_after', true, true);
            //$this->redirect($this->module, $after_action, $after_action . '=' . $finance_expenses_reason->get('id'));
        }

        return true;
    }

    /**
     * Status of models
     */
    private function _getStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('model_id');
        $filters = array('where' => array('fer.id = ' . $request->get('id'),
                                          'fer.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => true);
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        //prepare substatuses
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_substatuses.factory.php';
        $filters = array('model_lang' => $finance_expenses_reason->get('model_lang'),
                         'where' => array('fds.doc_type = ' . $finance_expenses_reason->get('type'),
                                          'fds.active = 1'));
        $substatuses = Finance_Documents_Substatuses::search($this->registry, $filters);
        $finance_expenses_reason->set('substatuses', $substatuses, true);

        $setstatus['options'] = array('label' => $this->i18n('finance_status_btn'), 'form_method' => 'post');
        $setstatus['model_id'] = $finance_expenses_reason->get('id');
        $setstatus['module'] = 'finance';
        $setstatus['action'] = 'setstatus';
        $setstatus['module_param'] = $this->registry['module_param'];
        $setstatus['controller'] = 'expenses_reasons';
        $setstatus['controller_param'] = $this->registry['controller_param'];
        $setstatus['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
        $setstatus['show_form'] = 1;
        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_action_status.html');
        $this->viewer->data['model'] = $finance_expenses_reason;
        $this->viewer->data['available_action'] = $setstatus;
        $this->viewer->data['hide_status_label'] = true;
        $this->viewer->display();
        exit;
    }

    /**
     * Change status of multiple models
     */
    private function _multiStatus($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('fer.id IN (' . implode(', ', $ids) . ')',
                                          'fer.annulled_by = 0'),
                         'model_lang' => $this->registry->get('lang'),
                         'sanitize' => false);
        $finance_expenses_reasons = Finance_Expenses_Reasons::search($this->registry, $filters);

        //if no documents or checked annulled documents
        if (empty($finance_expenses_reasons) || count($ids) != count($finance_expenses_reasons)) {
            $this->registry['messages']->setError($this->i18n('error_no_finance_expenses_reasons_or_annulled'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_document = $finance_expenses_reasons[0];

        $type = ($first_document->get('type'));
        foreach ($finance_expenses_reasons as $finance_expenses_reason) {
            $type_i = $finance_expenses_reason->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        require_once $this->modelsDir . 'finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.id = ' . $type),
                         'sanitize' => true);
        $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $type_name_plural = $fin_doc_type && $fin_doc_type->get('name_plural') ? $fin_doc_type->get('name_plural') : $this->i18n('finance_expenses_reasons');

        $result = Finance_Expenses_Reasons::multiStatus($this->registry, $this);
        if ($result) {
            $this->actionCompleted = true;
            if ($result > 0) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_multistatus_success',
                    array(($result === true ? '' : $this->i18n('num_of_selected_items', array($result)) . ' ') . $type_name_plural)), '', -1);
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_finance_change_status_not_all', array($type_name_plural)));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_finance_multistatus_failed', array($type_name_plural)), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Finance_Expenses_Reasons::changeStatus($this->registry, $ids, $status);
        if ($result) {
            //change status successful
            if ($result > 0) {
                $this->registry['messages']->setMessage(
                    $this->i18n('message_items_' . ($result !== true ? 'some_' : '') .
                                ($status == 'activate' ? 'activated' : 'deactivated'),
                                array($result)));
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_finance_' . $status . '_not_all'));
            }
        } else {
            //change status failed
            $this->registry['messages']->setError(
                $this->i18n('error_items_not_' . ($status == 'activate' ? 'activated' : 'deactivated')));
        }
        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Relatives of the model
     */
    private function _relatives() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        if ($id) {
            $filters = array('where' => array('fer.id = ' . $id));
            if ($model_lang = $this->registry['request']->get('model_lang')) {
                $filters['model_lang'] = $model_lang;
            }
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        }
        if (!empty($finance_expenses_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_expenses_reason);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }

            //get the relatives tree
            $this->registry->set('relatives_tree', $finance_expenses_reason->getRelativesTree());
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Set relations to payment documents
     */
    private function _payments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        //search model
        $filters = array();
        $filters['where'] = array('fer.id = ' . $id, 'fer.annulled_by = 0');
        $filters['model_lang'] = $request->get('model_lang');
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_expenses_reason)) {
            $this->checkAccessOwnership($finance_expenses_reason);

            //get selected tab
            if (!$request->get('selected_tab')) {
                $selected_tab = 'payment';
            } else {
                $selected_tab = $request->get('selected_tab');
            }
            $finance_expenses_reason->set('selected_tab', $selected_tab, true);

            if ($request->get('use_ajax') == 1) {
                //ajax request

                $old_finance_expenses_reason = clone $finance_expenses_reason;
                $this->old_model = $old_finance_expenses_reason;
                //$old_finance_expenses_reason->getRelatives();

                if ($request->get('empty')) {
                    //remove paid amount from document
                    $paid_to = $request->get('empty');
                    if ($finance_expenses_reason->emptyPaidAmount($paid_to, $selected_tab)) {
                        //$finance_expenses_reason->getRelatives();
                        //ToDo - save history and audit
                        require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                        $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                                   array('model' => $finance_expenses_reason,
                                                                                         'action_type' => 'empty',
                                                                                         'new_model' => $finance_expenses_reason,
                                                                                         'old_model' => $old_finance_expenses_reason));

                        //show success message
                        $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_edit_payment_success',
                                                                array($finance_expenses_reason->getModelTypeName())), '', -1);
                        $this->actionCompleted = true;
                    } else {
                        //some error occurred
                        $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_edit_payment_failed',
                                                              array($finance_expenses_reason->getModelTypeName())), '', -2);
                    }
                } elseif ($request->get('relatives_payments')) {
                    //save amounts to documents
                    $finance_expenses_reason->set('relatives_payments', $request->get('relatives_payments'), true);
                    $finance_expenses_reason->set('selected_tab', $selected_tab, true);
                    if ($finance_expenses_reason->updateFinanceRelatives($selected_tab)) {
                        //$finance_incomes_reason->getRelatives();
                        //ToDo - save history and audit
                        require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                        $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                                   array('model' => $finance_expenses_reason,
                                                                                         'action_type' => 'payments',
                                                                                         'new_model' => $finance_expenses_reason,
                                                                                         'old_model' => $old_finance_expenses_reason));

                        //show success message
                        $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_edit_payment_success',
                                                                array($finance_expenses_reason->getModelTypeName())), '', -1);
                        $this->actionCompleted = true;
                    } else {
                        //some error occurred
                        $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_edit_payment_failed',
                                                              array($finance_expenses_reason->getModelTypeName())), '', -2);
                    }
                }
            }

            if ($id) {
                //check status
                if ($finance_expenses_reason->get('status') != 'finished') {
                    $this->redirect($this->module, 'view', 'view=' . $finance_expenses_reason->get('id'));
                }

                //prepare selected tab data
                if ($selected_tab == 'payment') {
                    $finance_expenses_reason->getCustomerPayments();
                } else {
                    $finance_expenses_reason->getPaymentsDistribution($selected_tab);
                }
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        if ($request->get('use_ajax') == 1) {
            //use ajax
            require_once PH_MODULES_DIR . 'finance/viewers/finance.expenses_reasons.payments.viewer.php';
            $viewer = new Finance_Expenses_Reasons_Payments_Viewer($this->registry);
            $viewer->model = $finance_expenses_reason;
            $this->registry->set('finance_expenses_reason', $finance_expenses_reason, true);
            $viewer->prepare();
            $result = array();
            //main ajax content
            $result['content'] = $viewer->fetch();
            //prepare messages
            if ($items = $this->registry['messages']->getErrors()) {
                $display = 'error';
            } elseif ($items = $this->registry['messages']->getMessages()) {
                $display = 'message';
            }
            if (!empty($items)) {
                $financeViewer = new Viewer($this->registry);
                $financeViewer->setFrameset('message.html');
                $financeViewer->data['items'] = $items;
                $financeViewer->data['display'] = $display;
                $result['errors'] = $financeViewer->fetch();
            }
            //print json encoded result of operation
            $this->registry->set('ajax_result', json_encode($result), true);
        }

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions();

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.model = "Finance_Expenses_Reason"',
                                          'fdt.id != ' . PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON,
                                          'fdt.active=1'),
                         'sort' => array('fdti18n.name ASC'),
                         'sanitize' => true);
        $financeTypes = Finance_Documents_Types::search($this->registry, $filters);

        $types = array();
        foreach ($financeTypes as $type) {
            //check permissions by type
            if ($this->checkActionPermissions($module_check . $type->get('id'), 'add')) {
                $types[] = array('label' => $type->get('name'), 'option_value' => $type->get('id'));
            }
        }

        if (isset($actions['add']) && !empty($types)) {
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $filters = array('lang' => $this->registry['lang'],
                             'sanitize' => true,
                             'where' => array('fc.active=1'));
            $companies_list = Finance_Companies::search($this->registry, $filters);
            $default_company = array();
            $default_company_id = '';
            $companies = array();

            foreach ($companies_list as $key => $company) {
                if ($this->registry['currentUser']->get('default_company') == $company->get('id')) {
                    $default_company_id = $company->get('id');
                    $default_company = array('label' => $company->get('name'),
                                             'option_value' => $company->get('id'));
                } else {
                    $companies[] = array('label' => $company->get('name'),
                                         'option_value' => $company->get('id'));
                }
            }

            if ($default_company) {
                array_unshift($companies, $default_company);
            }

            $add_options = array (
                array (
                    'custom_id' => 'type_____',
                    'name' => 'aa2_type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'really_required' => 1,
                    'label' => $this->i18n('finance_expenses_reasons_type'),
                    'help' => $this->i18n('finance_expenses_reasons_add_legend'),
                    'options' => $types,
                    'onchange' => 'showTypeAdditionalFields(this)',
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : '',
                    'custom_class' => (count($types) > 1 && !$this->registry['request']->get('type')) ? 'undefined' : ''
                ),
                array (
                    'custom_id' => 'company_____',
                    'name' => 'aa2_company',
                    'type' => 'dropdown',
                    'required' => 1,
                    'really_required' => 1,
                    'label' => $this->i18n('finance_expenses_reasons_company'),
                    'help' => $this->i18n('finance_expenses_reasons_company'),
                    'options' => $companies,
                    'value' => ($this->registry['request']->get('company')) ?
                                $this->registry['request']->get('company') :
                                $default_company_id,
                    'custom_class' => (count($companies) > 1 && !$this->registry['request']->get('company') && !$default_company_id) ? 'undefined' : ''
                ),
            );
            if (count($types) == 1 && in_array($types[0]['option_value'], array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
                //hide the company row (there is no need to select company when adding credit/debit, it is inheritted from the expense invoice)
                $add_options[1]['hidden'] = true;

                $add_options[] = array (
                    'custom_id' => 'invoice_id_____',
                    'name' => 'aa2_invoice_id',
                    'type' => 'autocompleter',
                    'required' => 1,
                    'label' => $this->i18n('finance_expenses_reasons_for_invoice'),
                    'help' => $this->i18n('finance_expenses_reasons_for_invoice'),
                    'autocomplete_type' => 'finance_expenses_reasons',
                    'min_chars' => 2,
                    'autocomplete_buttons_hide' => 'search,refresh,add',
                    'autocomplete_buttons' => 'clear',
                    'filters' => array('<type>' => strval(PH_FINANCE_TYPE_EXPENSES_INVOICE), '<status>' => 'finished')
                );
            }
           $actions['add']['options'] = $add_options;
        } else {
            unset($actions['add']);
        }

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once(PH_MODULES_DIR . 'patterns/models/patterns.factory.php');
            $filters = array('where' => array('p.model = \'' . $this->model->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add filter: get patterns only if they are for this company or they are not for a specific company
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            $_options_patterns = array();
            foreach ($patterns as $pattern) {
                $_options_patterns[] = array(
                                'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                                'option_value' => $pattern->get('id'));
            }

            if (empty($_options_patterns)) {
                //remove generate action, the document type does not define the types to generate to
                unset($actions['generate']);
            } else {
                //prepare generate options
                $generate_options = array (
                    array (
                        'custom_id' => 'pattern__',
                        'name' => 'aa2_pattern',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('finance_pattern'),
                        'help' => $this->i18n('finance_pattern'),
                        'options' => $_options_patterns,
                        'value' => ($this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : ''),
                );
                $actions['generate']['options'] = $generate_options;
            }
        }

        if (isset($actions['distribute']) && $this->model && $this->model->get('id') &&
        $this->model->get('status') == 'finished' && !$this->model->get('annulled_by') &&
        $this->model->get('payment_status') != 'nopay' &&
        $this->model->get('distributed') != PH_FINANCE_DISTRIBUTION_NONE &&
        !in_array($this->model->get('type'), array(PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE))) {

        } else {
            unset($actions['distribute']);
        }

        return $actions;
    }

    /**
     * Add protocol of handover
     */
    private function _addHandover() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        if ($request->get('setWarehouses', 'post')) {
            //we make a small trick here as we don't want warehouses IDs
            //to be transfered in the GET string
            $post = $request->getAll('post');
            foreach ($post as $k => $v) {
                $request->remove($k);
                $request->set($k, $v, 'all', true);
            }
        }
        //check if details are submitted via POST
        if ($request->isPost()) {

            //get the model and its old values
            $filters = array('where' => array ('fer.id = ' . $request->get('link_to')));
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

            $old_model = clone $finance_expenses_reason;
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);
            $this->old_model = $old_model;

            $old_name = $finance_expenses_reason->get('name') ? $finance_expenses_reason->get('name') : $finance_expenses_reason->get('type_name');
            $params = array(
                'model' => $finance_expenses_reason,
                'type' => PH_FINANCE_TYPE_HANDOVER,
            );
            require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php');
            $result = Finance_Warehouses_Documents::addMultipleDocuments($this->registry, $params);

            if ($result['status']) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_handover_success_' . $request->get('handover_direction')) . ' ' . count($result['created']), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;

                $redirect_url = sprintf('%s://%s%sindex.php?%s=finance&%s=warehouses_documents&warehouses_documents=',
                                        (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                        $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'],
                                        $this->registry['controller_param']);

                if (count($result['created']) == 1) {
                    $created = reset($result['created']);
                    $redirect_url .= sprintf('view&view=%d', $created->get('id'));
                } else {
                    $redirect_url .= sprintf('list&type=%d', PH_FINANCE_TYPE_HANDOVER);
                }

                $this->registry->set('redirect_to_url', $redirect_url, true);
                //set exit parameter
                $this->registry->set('exit_after', true, true);
            } else {
                // keep submitted values on unsuccessful adding of handovers
                $finance_expenses_reason->set('parent_name', $old_name, true);
                $finance_expenses_reason->set('name', $request->get('name'), true);
                $finance_expenses_reason->set('department', $request->get('department'), true);
                $finance_expenses_reason->set('group', $request->get('group'), true);
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_handover_failed_' . $request->get('handover_direction')), '', -1);
            }

        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fer.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_expenses_reason)) {
            $this->registry->set('get_old_vars', false, true);
            if (!$this->actionCompleted) {
                $old_type = $finance_expenses_reason->get('type');
                $finance_expenses_reason->set('handover_direction', $request->get('handover_direction'), true);
                // IMPORTANT: expenses reason model DOES ALREADY HAVE parent_type set (it is set in the constructor)!!!
                // Set link_to_model_name to get VAT settings from the root expenses reason
                $finance_expenses_reason->set('link_to_model_name', $finance_expenses_reason->modelName, true);
                $finance_expenses_reason->set('type', PH_FINANCE_TYPE_HANDOVER, true);

                if (!$request->isPost()) {
                    // set name to an empty string
                    $finance_expenses_reason->set('parent_name', ($finance_expenses_reason->get('name') ? $finance_expenses_reason->get('name') : $finance_expenses_reason->get('type_name')), true);
                    $finance_expenses_reason->set('name', '', true);

                    // set default values for group and department
                    $finance_expenses_reason->setGroup($finance_expenses_reason->get('group'));
                    $finance_expenses_reason->setDepartment($finance_expenses_reason->get('department'));
                }
                //prepare gt2 vars settings for warehouses document
                $finance_expenses_reason->set('gt2_model_name', 'Finance_Warehouses_Document', true);
                $finance_expenses_reason->getGT2Vars();
                $finance_expenses_reason->set('type', $old_type, true);
                $this->checkAccessOwnership($finance_expenses_reason, true);
                $gt2 = $finance_expenses_reason->get('grouping_table_2');

                if (empty($gt2)) {
                    $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_handover_no_quantity',
                                                          array($finance_expenses_reason->getModelTypeName())));
                    $this->registry['messages']->insertInSession($this->registry);
                    $this->redirect($this->module, 'view', 'view=' . $finance_expenses_reason->get('id'));
                }
            } else {
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne(
                    $this->registry,
                    array(
                        'where' => array(
                            'fer.id = ' . $finance_expenses_reason->get('id')
                        ),
                        'model_lang' => $request->get('model_lang')
                    )
                );
                $finance_expenses_reason->getGT2Vars();
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Gets warehouses to be shown in the lightbox
     * for handovers issue
     * IMPORTANT!!! for outgoing handovers there are very special things
     */
    private function _getWarehouses() {
        $request = &$this->registry['request'];
        $direction = $request->get('direction');
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fer.id = ' . $id));
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        if (empty($finance_expenses_reason)) {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            exit(json_encode(array('errors' => $this->i18n('error_no_such_finance_expenses_reason'))));
        }
        $parent_gt2 = $finance_expenses_reason->getGT2Vars();

        //get all active warehouses
        require_once $this->modelsDir . 'finance.warehouses.factory.php';
        $filters = array('sanitize' => true,
                'model_lang' => $finance_expenses_reason->get('model_lang'),
                'where' => array('fwh.active = 1',
                                 'fwh.company = "' . $finance_expenses_reason->get('company') . '"',
                                 'fwh.office = "' . $finance_expenses_reason->get('office') . '"'),
                'sort' => array('fwhi18n.name ASC'));

        $prec = $this->registry['config']->getSectionParams('precision');

        if ($direction == 'outgoing') {
            //get all incoming handovers for the expenses reason
            $finance_expenses_reason->getRelatives(array('get_handovers' => array('fwd.annulled_by = 0', 'fwd.active = 1'), 'handovers_direction' => 'incoming'));
            $handovers = $finance_expenses_reason->get('handovers');
            //prepare special where for the batch articles as we can return exactly the same articles
            //that have been received with the incoming handovers
            //IMPORTANT!!! Between the incoming and current(outgoing) handover
            //the articles could be moved between the warehouses so we make additional
            //where clause to define the warehouses where the articles are in the moment
            $non_batch = array();
            foreach ($handovers as $h) {
                $h->getGT2Vars();
                $h->getBatchesData();
                $gt2 = $h->get('grouping_table_2');
                foreach ($gt2['values'] as $v) {
                    //start to create unique key
                    if (empty($v['batch_data'])) {
                        $non_batch[] = $v['article_id'];
                    } else {
                        foreach ($v['batch_data'] as $b) {
                            $batchData = [
                                'nomenclature_id' => $v['article_id'],
                                'batch_id'        => $b['batch'],
                                'expire_date'     => $b['expire'] ? : '0000-00-00',
                                'serial_number'   => $b['serial'],
                                'cstm_number'     => $b['custom'],
                                'delivery_price'  => sprintf("%.{$prec['gt2_rows']}F", round($b['delivery_price'], $prec['gt2_rows'])),
                                'currency'        => $b['currency'],
                            ];
                            $key = Finance_Warehouses_Documents::buildBatchUniqueKey(
                                $this->registry['db'],
                                $batchData
                            );
                            $keyData = Finance_Warehouses_Documents::buildBatchUniqueData(
                                $this->registry['db'],
                                $batchData
                            );
                            $unique[$key] = $keyData;
                        }
                    }
                }
            }
            $where = array();
            if (!empty($non_batch)) {
                $where[] = 'fwq.nomenclature_id IN (' . implode(', ', $non_batch) . ')';
            }
            if (!empty($unique)) {
                foreach ($unique as $v) {
                    $w = [];
                    if (array_key_exists('nomenclature_id', $v)) {
                        $w[] = sprintf('fwq.nomenclature_id = %d', $v['nomenclature_id']);
                    }
                    if (array_key_exists('batch_id', $v)) {
                        $w[] = sprintf('fwq.batch_id = %s', $v['batch_id']);
                    }
                    if (array_key_exists('expire_date', $v)) {
                        $w[] = sprintf('fwq.expire_date = "%s"', $v['expire_date']);
                    }
                    if (array_key_exists('serial_number', $v)) {
                        $w[] = sprintf('fwq.serial_number = "%s"', $v['serial_number']);
                    }
                    if (array_key_exists('cstm_number', $v)) {
                        $w[] = sprintf('fwq.cstm_number = "%s"', $v['cstm_number']);
                    }
                    if (array_key_exists('delivery_price', $v)) {
                        $w[] = sprintf('fwq.delivery_price = %s', $v['delivery_price']);
                    }
                    if (array_key_exists('currency', $v)) {
                        $w[] = sprintf('fwq.currency = "%s"', $v['currency']);
                    }
                    $where[] = implode(' AND ', $w);
                }
            }
            $filters['where'][] = sprintf('(%s)', implode(" OR\n", $where));
            $filters['where'][] = 'fwq.quantity > 0';
        }
        $warehouses = Finance_Warehouses::search($this->registry, $filters);
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_setWarehouses.html');
        $viewer->data['warehouses'] = $warehouses;
        $viewer->data['default_warehouse'] = $this->registry['currentUser']->get('default_warehouse');
        $viewer->data['submit_link'] = sprintf('%s?%s=finance&amp;%s=expenses_reasons&amp;expenses_reasons=addhandover&amp;addhandover=%d&amp;handover_direction=%s',
                                      $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'],
                                      $id, $direction);
        exit(json_encode(array('content' => $viewer->fetch())));
    }

    /**
     * Preview the GT2 of an expense document by expenses reason id
     */
    public function _previewGT2Invoice() {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $db = &$this->registry['db'];

        $filters = array('where' => array('fer.id = ' . $request->get('expenses_reason')));
        $expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (is_object($expenses_reason)) {
            $this->registry->set('get_old_vars', true, true);
            $expenses_reason->getGT2Vars();

            //this is the preview of GT2 itself
            $viewer = new Viewer($registry);
            $viewer->setFrameset('_gt2_view.html');
            $viewer->data['table'] = $expenses_reason->get('grouping_table_2');
            $viewer->display();
            exit;
        }

        //the script should not go further but anyway display a user-friendly and polite error
        printf('<span class="error">%s</span>', $this->i18n('error_finance_expenses_reason_preview'));
        exit;
    }

    /**
     * Distribute expenses reason
     *
     * @return bool - result of operation
     */
    private function _distribute() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get the model and its old values
            $filters = array('where' => array ('fer.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $old_model = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
            $old_model->sanitize();

            //build the model from the POST
            $finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);
            $finance_expenses_reason->set('id', $id, true);

            if ($finance_expenses_reason->saveDistribution()) {
                $finance_expenses_reason->set('distributed', PH_FINANCE_DISTRIBUTION_YES, true);
                $finance_expenses_reason->updateDistributed();

                $filters = array('where' => array('fer.id = ' . $id),
                                 'model_lang' => $request->get('model_lang'));
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

                require_once(PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php');
                $audit_parent = Finance_Expenses_Reasons_History::saveData($this->registry,
                                                                           array('action_type' => 'distribute',
                                                                                 'new_model' => $finance_expenses_reason,
                                                                                 'old_model' => $old_model
                                                                           ));

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_distribute_success',
                                                        array($old_model->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //register the model, with all the posted details
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_distribute_failed',
                                                      array($old_model->getModelTypeName())), '', -2);
            }
        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('fer.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

            if (!empty($finance_expenses_reason)) {
                $this->checkAccessOwnership($finance_expenses_reason);

                $finance_expenses_reason->getDistribution();
            }
        }

        if (!empty($finance_expenses_reason)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show 'no such record' error
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Change payment status to nopay or unpaid
     */
    private function _changeUnpaidStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fer.id = ' . $id,
                                          'fer.annulled_by=0',
                                          'fer.active=1'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => true);
        $model = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
        if ($model && $model->checkNopayPermission()) {
            $res = $model->changeNopayStatus();
            if ($res) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_nopay_changed'));
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_nopay_change'));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_finance_nopay_denied'));
        }
        //prepare messages
        $this->registry['messages']->insertInSession($this->registry);
        $this->redirect('finance', 'view', array('view' => $id));
    }

    /**
     * AJAX action to show different additional fields
     * when we choose document type to add
     */
    private function _getTypeFields() {
        $result = array('invoice_autocompleter' => '');

        $type = $this->registry['request']->get('type');
        if (empty($type) || !in_array($type, array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE))) {
            echo json_encode($result);
            die;
        }
        //check if the type provided is a real expenses reason
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $type = Finance_Documents_Types::searchOne($this->registry, array('where' => array('fdt.id = "' . $type . '"')));
        if (empty($type) || $type->get('model') != 'Finance_Expenses_Reason') {
            echo json_encode(array('error' => 1));
            return;
        }

        $viewer = new Viewer($this->registry, false);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->setTemplate('_expenses_reasons_add_options.html');
        $viewer->data['ac_field'] = $this->registry['request']->get('context') != 'action' ? 'aa2_invoice_id' : 'invoice_id';
        $content = $viewer->fetch();
        if (!empty($content)) {
            $result['invoice_autocompleter'] = $content;
            $result['label'] = $this->i18n('finance_expenses_reasons_for_invoice');
            $result['required'] = $this->i18n('required');
        }
        echo json_encode($result);
        die;
    }

    /**
    * Selects certain items by specified parameter.
    * This method prints unordered list and exits
    */
    public function _select($autocomplete = array()) {

        $request = &$this->registry['request'];

        // get/set fields to search by
        $search_fields = array();
        if (!$search_fields = $request->get('search')) {
            $search_fields = array('<invoice_num>', '<issue_date>', '<customer_name>');
        }
        if (!is_array($search_fields)) {
            $search_fields = array($search_fields);
        }
        $i18n_columns = array_keys($this->registry['db']->MetaColumns(DB_TABLE_FINANCE_EXPENSES_REASONS_I18N, false));
        $main_alias = Finance_Expenses_Reasons::getAlias('finance', 'expenses_reasons');
        foreach ($search_fields as $idx => $field) {
            $field = preg_replace('#<|>#', '', $field);
            switch ($field) {
                case 'customer_name':
                    //custom search by customer name
                    $search_fields[$idx] = 'ci18n.name';
                    break;
                case 'issue_date':
                    //custom search by issue_date
                    $search_fields[$idx] = 'DATE_FORMAT(' . $main_alias . '.issue_date, "%d.%m.%Y")';
                    break;
                default:
                    //search by main field
                    $alias = $main_alias;
                    if (preg_match('#^a__*#', $field)) {
                        //search by additional variable (NOT APPLICABLE in the module)
                        unset($search_fields[$idx]);
                        continue 2;
                    } elseif (in_array(strtoupper($field), $i18n_columns)) {
                        //search by main field in i18n table
                        $alias .= 'i18n';
                    }
                    $search_fields[$idx] = $alias . '.' . $field;
                    break;
            }
        }

        //prepare sort if is requested
        $sort = array();
        if (!$r_sort = $request->get('sort')) {
            $r_sort = array('<num>');
        }
        foreach ($r_sort as $key => $field) {
            preg_match('#<([^>]*)>(\s+(ASC|DESC))?#i', $field, $sort_matches);
            if (!empty($sort_matches[1])) {
                //$order: ASC/DESC
                $order = (!empty($sort_matches[3]) ? $sort_matches[3] : 'ASC');
                if (preg_match('#^a__*#', $sort_matches[1])) {
                    //sort by additional variable (NOT APPLICABLE in this module)
                    continue;
                } else {
                    //sort by main field
                    $alias = $main_alias;
                    if (in_array(strtoupper($sort_matches[1]), $i18n_columns)) {
                        //sort by main field in i18n table
                        $alias .= 'i18n';
                    }
                    $sort[] = $alias . '.' . $sort_matches[1] . ' ' . $order;
                }
            }
        }

        $additional_where = array();
        if ($req_filters = $request->get('filters')) {
            foreach ($req_filters as $filter => $value) {
                $alias = $main_alias . '.';
                $field = preg_replace('#<|>#', '', $filter);
                // escape value for the SQL search query
                $value = General::slashesEscape($value);

                switch($filter) {
                    case '<tag>':
                        $alias = 'tags.';
                        $field = 'tag_id';
                        break;
                    default:
                        if (preg_match('#^a__*#', $sort_matches[1])) {
                            //sort by additional variable (NOT APPLICABLE in this module)
                            continue 2;
                        } elseif (in_array(strtoupper($field), $i18n_columns)) {
                            //search by main field in i18n table
                            $alias = $main_alias . 'i18n.';
                        }
                }

                if (preg_match('#^\s*(!?=)(.*)#', $value, $matches)) {
                    // search expression for a single value
                    $additional_where[] =
                        sprintf('%s%s %s \'%s\' AND',
                                $alias, $field, trim($matches[1]), trim($matches[2]));
                    continue;
                } elseif (preg_match('#^\s*((not\s+)?in\s*)\((.*)\)\s*#i', $value, $matches)) {
                    // search expression for multiple values
                    $negative_search = preg_match('#not#i', $matches[1]);
                    $compare_operator = $negative_search ? '!=' : '=';
                    $amatches = preg_split('#\s*,\s*#', trim($matches[3]));
                    $count_or_clauses = count($amatches);
                    foreach ($amatches as $idx => $amatch) {
                        $logical_operator = $negative_search || $idx == ($count_or_clauses - 1) ? 'AND' : 'OR';
                        $additional_where[] =
                            sprintf('%s%s %s \'%s\' %s',
                                    $alias, $field, $compare_operator, $amatch, $logical_operator);
                    }
                    continue;
                }

                $vals = preg_split('#\s*,\s*#', $value);
                if (count($vals) > 1) {
                    $count_or_clauses = count($vals);
                    foreach ($vals as $idx => $val) {
                        $clause = $alias . $field . ' = \'' . $val . '\'';
                        if ($idx < $count_or_clauses - 1) {
                            $clause .= ' OR';
                        } else {
                            $clause .= ' AND';
                        }
                        $additional_where[] = $clause;
                    }
                } else {
                    $additional_where[] = $alias . $field . ' = \'' . $vals[0] . '\' AND';
                }
            }
        }
        $additional_where[] = 'fer.annulled_by = 0 AND';

        //prepare suggestions format
        if (!$suggestions_format = $request->get('suggestions')) {
            $suggestions_format = '[<invoice_num>/<issue_date>] <customer_name>';
        }

        $s_field = $request->get('field');
        //prepare fill option definitions
        if (!$fill_options = $request->get('fill_options')) {
            //we must be in the basic vars
            //so get the autocomplete field
            $fill_options = array(
                    '$' . $s_field . ' => [<invoice_num>/<issue_date>] <customer_name>',
                    '$' . preg_replace('#_autocomplete$#', '', $s_field). '_oldvalue' . ' => [<invoice_num>/<issue_date>] <customer_name>');
            if (preg_match('#_autocomplete$#', $s_field)) {
                $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field) . ' => <id>';
            }
        } else {
            $fill_oldvalue = '[<invoice_num>/<issue_date>] <customer_name>';
            foreach ($fill_options as $fill_option) {
                if (preg_match('#^\$' . $s_field . '#', $fill_option)) {
                    @list($notimportant, $fill_oldvalue) = preg_split('#\s*=>\s*#', $fill_option);
                }
            }
            $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field). '_oldvalue' . '=>' . $fill_oldvalue;
        }

        $autocomplete = array(
            'search' => $search_fields,
            'sort' => $sort,
            'suggestions_format' => $suggestions_format,
            'fill_options' => $fill_options,
            'additional_where' => $additional_where,
            'type' => strtolower($this->modelFactoryName),
        );

        parent::_select($autocomplete);

        exit;
    }

    /**
     * Add incoming invoice/proforma to parent model
     */
    private function _addInvoice() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        if ($id) {
            // the model from the DB
            $filters = array();
            $filters['where'] = array('fer.id = ' . $id, 'fer.annulled_by = 0');
            $filters['model_lang'] = $request->get('model_lang');
            $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

            if ($finance_expenses_reason) {
                if ($finance_expenses_reason->get('type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE || $finance_expenses_reason->get('type') > PH_FINANCE_TYPE_MAX || $request->get('use_ajax') == 1) {
                    // display lightbox for confirmation
                    if ($request->get('use_ajax') == 1) {

                        $finance_expenses_reason->getGT2Vars();
                        if (!$finance_expenses_reason->checkAddingInvoice()) {
                            // invoice cannot be issued
                            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_invoice_failed'), '', -1);
                        } elseif ($finance_expenses_reason->getPaymentsDistribution('incomes_reason', true)) {
                            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_incomes_reason_payment'), '', -1);
                        } elseif ($other_paid_doc = $finance_expenses_reason->getPaidRelative()) {
                            // if the invoice is added to a reason, there should not be a directly paid proforma and vice versa
                            $link = sprintf('<a href="%s?%s=finance&amp;%s=expenses_reasons&amp;expenses_reasons=payments&amp;payments=%s&amp;selected_tab=payment" target="_blank">%s</a>',
                                            $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'],
                                            $other_paid_doc->get('id'),
                                            $other_paid_doc->getModelTypeName() .
                                            ($other_paid_doc->get('invoice_num') ? ' ' . $other_paid_doc->get('invoice_num') : ''));
                            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_paid_relative',
                                                                  array($link)), '', -1);
                        } else {
                            $this->registry['messages']->setMessage($this->i18n('finance_expenses_reasons_issue_invoice'), '', -1);
                        }
                        $viewer = new Viewer($this->registry, false);
                        $viewer->data['messages'] = $this->registry['messages'];
                        $new_finance_expenses_reason = clone $finance_expenses_reason;
                        $new_finance_expenses_reason->sanitize();
                        $new_finance_expenses_reason->set('old_id', $new_finance_expenses_reason->get('id'), true);
                        $new_finance_expenses_reason->unsetProperty('id', true);
                        $new_finance_expenses_reason->unsetProperty('invoice_num', true);
                        $new_finance_expenses_reason->set('old_type', $new_finance_expenses_reason->get('type'), true);
                        $new_finance_expenses_reason->set('type', PH_FINANCE_TYPE_EXPENSES_INVOICE, true);
                        // set default value for 'admit_VAT_credit' from customer
                        $new_finance_expenses_reason->getCustomer();
                        $new_finance_expenses_reason->set('admit_VAT_credit', $new_finance_expenses_reason->customer->get('admit_VAT_credit'), true);
                        unset($new_finance_expenses_reason->customer);
                        $issue_date = General::strftime('%Y-%m-%d');

                        if (!$finance_expenses_reason->checkAddingInvoice(true)) {
                            $new_finance_expenses_reason->set('has_proforma', true, true);
                        }

                        $filters = array('where' => array('fdt.id = ' . PH_FINANCE_TYPE_EXPENSES_INVOICE));
                        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                        $type = Finance_Documents_Types::searchOne($this->registry, $filters);
                        $filters = array('where' => array('fdt.id = ' . PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE));
                        $proforma_type = Finance_Documents_Types::searchOne($this->registry, $filters);

                        $new_finance_expenses_reason->set('issue_date', $issue_date, true);
                        $dop = array(
                                'point' => 'start',
                                'period_start' => $new_finance_expenses_reason->get('issue_date') ? $new_finance_expenses_reason->get('issue_date') : date('Y-m-d'),
                                'count' => $type->get('default_date_of_payment_count'),
                                'period_type' => $type->get('default_date_of_payment_period_type'),
                                'direction' => 'after');
                        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
                        $dop = Calendars_Calendar::calcDateBySettings($this->registry, $dop);
                        $new_finance_expenses_reason->set('date_of_payment', $dop, true);
                        $viewer->data['new_finance_expenses_reason'] = $new_finance_expenses_reason;
                        $viewer->data['invoice_mandatory_num'] = $type->get('mandatory_num');
                        $viewer->data['proforma_mandatory_num'] = $proforma_type->get('mandatory_num');
                        $viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_expenses_invoice_confirm.html');
                        $viewer->display();
                        exit;
                    } else {
                        $error = 0;
                        $issue_date = $request->get('issue_date');
                        $date_of_payment = $request->get('date_of_payment');

                        // issue date of invoice cannot be before issue date of the parent
                        if ($issue_date < $finance_expenses_reason->get('issue_date')) {
                            $issue_date = $finance_expenses_reason->get('issue_date');
                            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reason_issue_date'));
                            $error = 1;
                        }

                        if ($request->get('new_type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE) {
                            $doc = '_proforma_invoice_';
                        } else {
                            $doc = '_invoice_';
                        }

                        // check if invoice/proforma can be added
                        if (!$finance_expenses_reason->checkAddingInvoice($request->get('new_type') == PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE)) {
                            // invoice cannot be added
                            $error = 1;
                        }

                        // if any validation errors
                        if ($error) {
                            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons' . $doc . 'failed'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);
                            $this->redirect($this->module, 'view', 'view=' . $id);
                        }

                        //create invoice from proforma invoice or create invoice/proforma from expenses reason
                        $result_id = Finance_Expenses_Reasons::issueInvoice($this->registry, array($id));

                        if (!$result_id) {
                            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons' . $doc . 'failed'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);
                            $this->redirect($this->module, 'view', 'view=' . $id);
                        } else {
                            //now we have only one ID and can redirect to the view action
                            //of the document
                            $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons' . $doc . 'success'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);
                            $this->actionCompleted = true;
                            $this->redirect($this->module, 'view', 'view=' . $result_id);
                        }
                    }
                } else {
                    // set name to an empty string
                    $finance_expenses_reason->set('name', '', true);
                }
            }
        }

        if (empty($finance_expenses_reason)) {
            // AJAX action
            if ($request->get('use_ajax') == 1) {
                exit;
            }

            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add invoice from multiple proformas
     *
     * @return boolean - result of the operation
     */
    private function _multiAddInvoice() {
        $db = &$this->registry['db'];
        $request = &$this->registry['request'];
        $lang = $this->registry['lang'];

        // get document type
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.model="Finance_Expenses_Reason"',
                                          'fdt.id = "' . PH_FINANCE_TYPE_EXPENSES_INVOICE . '"',
                                          'fdt.active=1'),
                         'sanitize' => true);
        $financeType = Finance_Documents_Types::searchOne($this->registry, $filters);

        $type_permission = $financeType ? $this->checkActionPermissions($this->module . '_' . $this->controller . $financeType->get('id'), 'add') : false;

        if (empty($financeType) || !$type_permission) {
            //invalid type, redirect to list
            $type_name = $financeType && $financeType->get('name') ? $financeType->get('name') : $this->i18n('finance_expenses_reasons_invoice');
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_add_failed',
                                                  array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        }

        $after_action = 'list';
        if (!empty($_SERVER['HTTP_REFERER'])) {
            preg_match('/&expenses_reasons=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
            if (isset($matches[1])) {
                $after_action = $matches[1];
            }
        }

        //get the previous list filters
        $prev_filters = Finance_Expenses_Reasons::saveSearchParams($this->registry, array(), $after_action . '_');

        // get available filter for the current action or selected items from the request
        if ($request->get('items')) {
            $filters = array('where' => array('fer.id IN (' . implode(', ', $request->get('items')) . ')'));
            $filters['sort'] = $prev_filters['sort'];
            //we don't need to check again for the permissions for types
            //as the IDs are already filtered through these filters
            $filters['skip_permissions_check'] = true;
        } else {
            //$filters = $prev_filters;
            $this->registry['messages']->setError($this->i18n('no_selected_records'), '');
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, $after_action, array(), $this->controller);
        }

        // get models for the defined filters
        $models = Finance_Expenses_Reasons::search($this->registry, $filters);

        // properties to validate and check for equality in proforma models
        $check_properties = array('company', 'type', 'customer',
                                  'total_vat_rate', 'status',
                                  'annulled_by', 'active');
        $check_properties = array_combine($check_properties, array_fill(0, count($check_properties), ''));

        // collect currencies of proformas (per model)
        $proforma_currencies = array();
        // flag if any proformas are (partially) paid
        $paid_proformas = false;
        // max issue date of proformas is min issue date of invoice
        $min_issue_date = '';

        $error = false;
        foreach ($models as $idx => $model) {
            if (!$idx) {
                foreach ($check_properties as $key => $value) {
                    if ($model->get($key) === '' || $model->get($key) === false ||
                        (!in_array($key, array('total_vat_rate', 'annulled_by')) && !$model->get($key)) ||
                        $key == 'type' && $model->get($key) != PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE ||
                        $key == 'status' && $model->get($key) != 'finished' ||
                        $key == 'annulled_by' && $model->get($key)) {
                        $error = true;
                        break 2;
                    }
                    $check_properties[$key] = $model->get($key);
                }
            } else {
                foreach ($check_properties as $key => $value) {
                    if ($value != $model->get($key)) {
                        $error = true;
                        break 2;
                    }
                }
            }
            // check if proforma is non-invoiced, make sure all models have currency
            if ($model->get('payment_status') == 'invoiced' || !$model->checkAddingInvoice() || !$model->get('currency')) {
                $error = true;
                break;
            }
            $proforma_currencies[$model->get('id')] = $model->get('currency');
            if ($model->get('payment_status') != 'unpaid') {
                $paid_proformas = true;
            }
            if (!$min_issue_date || $min_issue_date < $model->get('issue_date')) {
                $min_issue_date = $model->get('issue_date');
            }
        }

        // selected incoming proformas differ in parameters or cannot be invoiced
        if ($error) {
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_invoice_failed'), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_multiaddinvoice_different_proformas'), '');
            if ($this->action == 'multiaddinvoice') {
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, $after_action, array(), $this->controller);
            }
        }
        // end of preliminary validation

        // unique proforma currencies
        $proforma_currencies_unique = array_values(array_unique($proforma_currencies));

        // display lightbox and exit
        if ($this->action == 'ajax_multiaddinvoice') {
            $result = array();
            if ($error) {
                $viewer = new Viewer($this->registry);
                $viewer->data['items'] = $this->registry['messages']->getErrors();
                $viewer->data['display'] = 'error';
                $viewer->setFrameset('message.html');
                $result['errors'] = $viewer->fetch();
            } else {
                $viewer = new Viewer($this->registry);
                // distinct currencies of proformas
                $viewer->data['proforma_currencies'] = $proforma_currencies_unique;
                // get available currencies of installation
                $currency_options = Dropdown::getCurrencies(array($this->registry));
                $viewer->data['currency_options'] = $currency_options;
                // specify invoice currency (for now use main currency, i.e. first option)
                $viewer->data['invoice_currency'] = $currency_options ? $currency_options[0]['option_value'] : '';
                $viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_expenses_multiaddinvoice.html');
                $result['content'] = $viewer->fetch();
            }
            //print json encoded result of operation
            print json_encode($result);
            exit;
        }
        // end of display lightbox

        // get and validate request values from lightbox
        $error = false;
        $invoice_currency = $request->get('invoice_currency');
        $rates = array();

        if (!$invoice_currency) {
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_multiaddinvoice_currency'));
            $error = true;
        } else {
            foreach ($proforma_currencies_unique as $pc) {
                if ($pc == $invoice_currency) {
                    $rates[$pc] = 1;
                } else {
                    $r = $request->get('rate_' . $pc);
                    if (!preg_match('#^\d*\.?\d+$#', $r)) {
                        $r = 0;
                    }
                    $rates[$pc] = round($r, 6);
                    if ($rates[$pc] <= 0) {
                        $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_multiaddinvoice_currency_rates'));
                        $error = true;
                        break;
                    }
                }
            }
            // if proformas are in different currency and they are paid, do not allow adding of invoice
            if ($paid_proformas) {
                $paid_other_currency = array();
                foreach ($models as $model) {
                    if ($model->get('payment_status') != 'unpaid' && $model->get('currency') != $invoice_currency) {
                        $paid_other_currency[$model->get('id')] = $model->get('invoice_num') ? $model->get('invoice_num') : $model->getModelTypeName();
                        $error = true;
                    }
                }
                if ($paid_other_currency) {
                    $links = array();
                    foreach ($paid_other_currency as $pid => $pnum) {
                        $links[] =
                            sprintf('<a href="%s?%s=finance&amp;%s=expenses_reasons&amp;expenses_reasons=payments&amp;payments=%s&amp;selected_tab=payment" target="_blank">%s</a>',
                                    $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'],
                                    $pid, $pnum);
                    }
                    $links = implode(', ', $links);
                    $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_multiaddinvoice_paid_proforma_currency', array($links)));
                }
            }
        }

        if ($error) {
            $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_invoice_failed'), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, $after_action, array(), $this->controller);
        }

        $first_proforma = reset($models);
        $finance_expenses_reason = new Finance_Expenses_Reason($this->registry);

        // properties to copy from proformas
        $copy_props = array('company', 'company_name', 'customer', 'total_vat_rate');
        foreach ($copy_props as $prop) {
            $finance_expenses_reason->set($prop, $first_proforma->get($prop), true);
        }

        // properties to set from type
        $finance_expenses_reason->set('type',             PH_FINANCE_TYPE_EXPENSES_INVOICE);
        $finance_expenses_reason->set('type_name',        $financeType->get('name'), true);
        $finance_expenses_reason->set('type_payment_way', $financeType->get('payment_way'), true);
        $finance_expenses_reason->setDepartment(false, $financeType->get('default_department'));
        $finance_expenses_reason->setGroup(false, $financeType->get('default_group'));

        // set default value for 'admit_VAT_credit' from customer
        $finance_expenses_reason->getCustomer();
        $finance_expenses_reason->set('admit_VAT_credit', $finance_expenses_reason->customer->get('admit_VAT_credit'), true);
        unset($finance_expenses_reason->customer);

        // set default value for issue date
        $finance_expenses_reason->set('issue_date',       General::strftime('%Y-%m-%d'), true);
        $finance_expenses_reason->set('min_issue_date',   $min_issue_date, true);

        // properties to set from current user
        $finance_expenses_reason->set('employee1',        $this->registry['currentUser']->get('employee'), true);
        $finance_expenses_reason->set('employee1_name',   $this->registry['currentUser']->get('employee_name'), true);

        $this->registry->set('get_old_vars', true, true);
        // get GT2 vars for incoming invoice
        $finance_expenses_reason->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);

        // get the rows to calculate the quantities to be entered in the new document
        $query = 'SELECT gt2.id AS idx, gt2.*, gt2i18n.*' . "\n" .
                 'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                 '  ON (gt2.id=gt2i18n.parent_id AND gt2i18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                 'WHERE gt2.model="Finance_Expenses_Reason" AND gt2.model_id IN (' . implode(', ', $request->get('items')) . ')';
        $gt2_rows = $this->registry['db']->getAssoc($query);

        $gt2 = $finance_expenses_reason->get('grouping_table_2');

        // recalculate the 'calculated_price' field
        foreach ($gt2_rows as $idx => $row) {
            $gt2_rows[$idx][$gt2['calculated_price']] *= $rates[$proforma_currencies[$row['model_id']]];
        }
        // set invoice currency
        $gt2['plain_values']['currency'] = $invoice_currency;

        // set values and rows from proformas
        $gt2['values'] = $gt2_rows;
        $gt2['rows'] = array_keys($gt2_rows);

        $total_props = array('total', 'total_surplus_value', 'total_discount_value', 'total_discount_surplus_field', 'total_without_discount', 'total_vat', 'total_with_vat');
        foreach ($models as $model) {
            foreach ($total_props as $prop) {
                if ($prop == 'total_discount_surplus_field') {
                    $gt2['plain_values'][$prop] = $model->get($prop);
                } else {
                    $gt2['plain_values'][$prop] += $model->get($prop);
                }
            }
        }
        if ($gt2['plain_values']['total_without_discount'] != 0) {
            // use max precision so that total surplus/discount values are set to the same values in calculateGT2()
            $gt2['plain_values']['total_discount_percentage'] =
                round($gt2['plain_values']['total_discount_value'] * 100 / $gt2['plain_values']['total_without_discount'], 6);
            $gt2['plain_values']['total_surplus_percentage'] =
                round($gt2['plain_values']['total_surplus_value'] * 100 / $gt2['plain_values']['total_without_discount'], 6);
        }

        unset($models);

        // set GT2 table back to model
        $finance_expenses_reason->set('grouping_table_2', $gt2, true);
        $finance_expenses_reason->calculateGT2();
        $finance_expenses_reason->set('table_values_are_set', true, true);

        // pass conversion rates, ids and currencies of proformas in transform params
        // so that rates are updated in proformas after successful save of invoice
        $rates_model_ids = array();
        foreach ($rates as $currency => $rate) {
            $rates_model_ids[$currency] = array('rate' => $rate, 'ids' => array());
        }
        foreach ($proforma_currencies as $model_id => $currency) {
            $rates_model_ids[$currency]['ids'][] = $model_id;
        }

        // set some values in transform params in order to modify what is displayed in "Add" action
        $transform_params = array(
            'origin_method'             => 'addinvoice',
            'origin_model'              => array(),
            'origin_id'                 => array(),
            'origin_gt2_relations'      => array(),
            'transform_multiaddinvoice' => true,
            'transform_paid_proformas'  => $paid_proformas,
            'transform_filter_company'  => $finance_expenses_reason->get('company'),
            'transform_currency_rates'  => $rates_model_ids,
        );
        foreach ($gt2_rows as $gt2_inf) {
            $transform_params['origin_model'][] = $this->modelName;
            $transform_params['origin_id'][] = $gt2_inf['model_id'];
            $transform_params['origin_gt2_relations'][$gt2_inf['id']] = array(
                                                                            'model'    => $this->modelName,
                                                                            'model_id' => $gt2_inf['model_id']
                                                                        );
        }
        $finance_expenses_reason->set('transform_params', serialize($transform_params), true);

        // set model to registry
        $this->registry->set('finance_expenses_reason', $finance_expenses_reason, true);

        // go to "Add" action
        $this->action = 'add';
        $this->registry->set('action', 'add', true);

        // remove selected items from session
        $this->registry['session']->remove($after_action . '_' . strtolower($this->modelName), 'selected_items');

        return true;
    }

    /**
     * Checks if expense document with specified "invoice_num" already exists
     * and if so, prints a message.
     */
    private function _checkNum() {

        //TODO: Add filter by parent_id when needed
        $num_other_document = Finance_Expenses_Reasons::checkNum(
            $this->registry,
            $this->registry['request']->get($this->action),
            $this->registry['request']->get('type'),
            $this->registry['request']->get('num')
        );
        if ($num_other_document) {
            echo $this->i18n('warning_document_with_the_same_num_exists');
        }
        exit;
    }

    /**
     * Add incoming credit or debit note to incoming invoice
     */
    private function _addCreditDebit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);
            //allow 'action' automations adding old_model to the controller
            $this->old_model = new Finance_Expenses_Reason($this->registry, array());

            $id = $finance_expenses_reason->get('invoice_id');
            if ($finance_expenses_reason->saveCreditDebit()) {
                $old_model = Finance_Expenses_Reasons::searchOne($this->registry, array('where' => array('fer.id = ' . $id), 'sanitize' => true));
                $new_model = Finance_Expenses_Reasons::searchOne($this->registry, array('where' => array('fer.id = ' . $finance_expenses_reason->get('id'), 'sanitize' => true)));
                $this->registry->set('get_old_vars', true, true);
                $new_model->getGT2Vars();
                //Write history for the newly created document
                require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php';
                Finance_Expenses_Reasons_History::saveData($this->registry,
                                                           array('action_type' => $this->action,
                                                                 'new_model' => $old_model,
                                                                 'old_model' => $old_model
                                                           ));
                Finance_Expenses_Reasons_History::saveData($this->registry,
                                                           array('action_type' => 'add',
                                                                 'new_model' => $new_model,
                                                                 'old_model' => $this->old_model
                                                           ));
                if ($new_model->get('payment_status') == 'paid') {
                    Finance_Expenses_Reasons_History::saveData($this->registry,
                                                               array('action_type' => 'payments',
                                                                     'new_model' => $new_model,
                                                                     'old_model' => $new_model
                                                               ));
                }

                $this->registry['messages']->setMessage($this->i18n('message_finance_expenses_reasons_add_success', array($finance_expenses_reason->getModelTypeName())), '', -5);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
                //allow 'action' automations by not redirecting instantly to view mode
                $credit_debit_view_url = sprintf('%s://%s%sindex.php?%s=finance&%s=expenses_reasons&expenses_reasons=view&view=%d',
                                                 (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                                 $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'],
                                                 $this->registry['controller_param'], $finance_expenses_reason->get('id'));
                $this->registry->set('redirect_to_url', $credit_debit_view_url, true);
                $this->registry->set('exit_after', true, true);
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_add_failed', array($finance_expenses_reason->getModelTypeName())), '', -2);
                $this->registry['db']->FailTrans();
            }
        }
        if (!$this->actionCompleted && $id) {
            // the model from the DB
            if (!$request->isPost()) {
                // make sure model is an expenses invoice
                $filters = array('where' => array('fer.id = ' . $id,
                                                  'fer.annulled_by = 0',
                                                  'fer.type = \'' . PH_FINANCE_TYPE_EXPENSES_INVOICE . '\''),
                                 'model_lang' => $request->get('model_lang'));
                $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

                if (!empty($finance_expenses_reason)) {

                    $this->checkAccessOwnership($finance_expenses_reason);

                    $finance_expenses_reason->unsetProperty('invoice_num', true);
                    $finance_expenses_reason->unsetProperty('date_of_payment', true);
                    $finance_expenses_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
                    $finance_expenses_reason->set('invoice_id', $id, true);
                    $finance_expenses_reason->unsetProperty('allocated_status', true);
                    // set name to an empty string
                    $finance_expenses_reason->set('parent_name', ($finance_expenses_reason->get('name') ? $finance_expenses_reason->get('name') : $finance_expenses_reason->get('type_name')), true);
                    $finance_expenses_reason->set('name', '', true);
                    if ($this->action == 'addcredit') {
                        $finance_expenses_reason->set('type', PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, true);
                    } else {
                        $finance_expenses_reason->set('type', PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE, true);
                    }
                    $finance_expenses_reason->unsetProperty('type_name', true);

                    require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                    $filters = array('where' => array('fdt.model="Finance_Expenses_Reason"',
                                                      'fdt.id = "' . $finance_expenses_reason->get('type') . '"',
                                                      'fdt.active=1'),
                                     'sanitize' => true);
                    $financeType = Finance_Documents_Types::searchOne($this->registry, $filters);
                    $type_permission = $financeType ? $this->checkActionPermissions($this->module . '_' . $this->controller . $financeType->get('id'), 'add') : false;

                    // set type name to model
                    $finance_expenses_reason->set('type_name', ($financeType ? $financeType->get('name') : $finance_expenses_reason->getModelTypeName()), true);

                    if (empty($financeType) || !$type_permission) {
                        //invalid type, redirect to list
                        $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_add_failed',
                                                              array($finance_expenses_reason->getModelTypeName())), '', -1);
                        $this->registry['messages']->setError($this->i18n('error_finance_expenses_reasons_invalid_type'));
                        $this->registry['messages']->insertInSession($this->registry);
                        $this->redirect($this->module, 'list');
                    }

                    // set default values for group and department
                    $finance_expenses_reason->setGroup($finance_expenses_reason->get('group'), $financeType->get('default_group'));
                    $finance_expenses_reason->setDepartment($finance_expenses_reason->get('department'), $financeType->get('default_department'));
                }
            } else {
                $finance_expenses_reason->set('status', 'finished', true);
                $finance_expenses_reason->set('id', $id, true);
                // get outgoing invoice model
                $old_model = Finance_Expenses_Reasons::searchOne($this->registry,
                                                                 array('where' => array('fer.id = ' . $id),
                                                                       'sanitize' => true));
                // set properties from parent model (displayed in navigation bar)
                $finance_expenses_reason->set('num', $old_model->get('num'), true);
                $finance_expenses_reason->set('parent_name', ($old_model->get('name') ? $old_model->get('name') : $old_model->get('type_name')), true);
                if (!$finance_expenses_reason->get('company_data')) {
                    // get company data from incoming invoice and set it to model
                    // (used in getting available options for Cashbox/Bank account field)
                    $company_data_properties = array('company', 'office', 'payment_type');
                    foreach ($company_data_properties as $prop) {
                        $finance_expenses_reason->set($prop, $old_model->get($prop), true);
                    }
                }
            }
            if (!empty($finance_expenses_reason)) {
                $finance_expenses_reason->set('new_type', $finance_expenses_reason->get('type'), true);
                $finance_expenses_reason->set('type', PH_FINANCE_TYPE_EXPENSES_INVOICE, true);
            }

            //set the viewer
            $this->viewer = $this->getViewer('addcreditdebit');
        }

        if (!empty($finance_expenses_reason)) {

            $this->registry->set('get_old_vars', false, true);
            $finance_expenses_reason->getGT2Vars();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_invoice'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }
        return true;
    }

    /**
     * Prepare the form for adding payment data
     */
    private function _preparePaymentForm() {
        $registry = &$this->registry;
        $request = &$registry['request'];

        // set finance_after_action for validation
        $request->set('finance_after_action', 'payment', 'all', true);

        $temp_finance_expenses_reason = Finance_Expenses_Reasons::buildModel($this->registry);

        $result = array();

        // validate form
        if (!$temp_finance_expenses_reason->validate($temp_finance_expenses_reason->get('id') ? 'edit' : 'add') &&
        $items = $this->registry['messages']->getErrors()) {

            $financeViewer = new Viewer($this->registry);
            $financeViewer->setFrameset('message.html');
            $financeViewer->data['items'] = $items;
            $financeViewer->data['display'] = 'error';
            $result['errors'] = $financeViewer->fetch();

            print json_encode($result);
            exit;
        }

        // get the company data for payment info
        $company_id = 0;
        $office_id = 0;
        $payment_type = '';
        $matches = array();
        if (preg_match('#^(\d+)_(\d+)_(cash|bank|cheque)_\d+$#', $temp_finance_expenses_reason->get('company_data'), $matches)) {
            // get company, office and container
            $company_id = $matches[1];
            $office_id = $matches[2];
            $payment_type = $matches[3];
        }

        // if the required data are completed the option for cashboxes and bank accounts are taken
        if ($company_id && $payment_type && $office_id) {
            //prepare companies, offices, cashboxes/bank accounts
            require_once $this->modelsDir . 'finance.dropdown.php';
            $params = array($registry,
                            'lang'              => $temp_finance_expenses_reason->get('model_lang'),
                            'company_id'        => ($company_id ? $company_id : array(0)),
                            'payment_direction' => ($temp_finance_expenses_reason->get('type') == PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE ? 'incomes' : 'expenses'),
                            'active'            => 1);
            $companies_data = Finance_Dropdown::getCompaniesData($params);
        } else {
            $companies_data = array();
        }

        // prepare the viewer
        $viewer = new Viewer($registry);
        // sets container currency, suggested conversion rate and converted amount to model
        $viewer->data['payment_info_container'] = $temp_finance_expenses_reason->get('company_data');
        $temp_finance_expenses_reason->prepareContainerRate($companies_data);

        $viewer->data['temp_finance_reason'] = $temp_finance_expenses_reason->sanitize();
        // payment_info_container
        $viewer->data['container_options'] = $companies_data;
        $viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_incomes_expenses_reasons_add_payment.html');

        $result['result'] = $viewer->fetch();

        print json_encode($result);
        exit;
    }

    /**
     * Recalculate new date of payment in function of the issue_date and type settings
     */
    private function _recalcPaymentDate() {
        $issue_date = $this->registry['request']->get('issue_date');
        if (!$issue_date) {
            $issue_date = date('Y-m-d');
        }
        $type = $this->registry['request']->get('type');
        $filters = array('where' => array('fdt.id = ' . $type));
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $type = Finance_Documents_Types::searchOne($this->registry, $filters);
        if (empty($type)) {
            exit;
        }
        $dop = array(
                'point' => 'start',
                'period_start' => $issue_date,
                'count' => $type->get('default_date_of_payment_count'),
                'period_type' => $type->get('default_date_of_payment_period_type'),
                'direction' => 'after');

        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
        $dop = Calendars_Calendar::calcDateBySettings($this->registry, $dop);
        exit($dop);
    }

    /**
     * Clone document
     */
    private function _clone() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('fer.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $clone_model = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if (empty($clone_model)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        } else {
            //check access and ownership of the model
            //IMPORTANT: only user defined types of expense reasons could be cloned
            //           this is defined in the model's checkPermissions method
            $this->checkAccessOwnership($clone_model);

            //prepare transform params
            //these are used to store relation between the parent and the clone
            $transform_params = array(
                'origin_method'         => 'clone',
                'origin_model'          => $clone_model->modelName,
                'origin_id'             => $clone_model->get('id'),
                'origin_full_num'       => $clone_model->get('num'),
                'origin_name'           => $clone_model->get('name') ?: $clone_model->get('type_name'),
                'origin_gt2_relations'  => array(),
                'transform_from_report' => false,
            );
            if ($request->isRequested('skip_relatives')) {
                $transform_params['skip_relatives'] = $request->get('skip_relatives');
            }

            //IMPORTANT: cloning the expense reason is actually moving to add action
            // set status to opened to get up-to-date available quantity of articles
            $clone_model->set('status', 'opened', true);
            if ($clone_model->get('allocated_status') == 'allocated') {
                $clone_model->set('allocated_status', 'enabled', true);
            }
            $clone_model->getGT2Vars();
            $clone_model->set('transform_params', serialize($transform_params), true);
            $clone_model->sanitize();

            //use the session to store the parent model so that it could be loaded in add mode
            $this->registry['session']->set('report_custom_model', serialize($clone_model), 'clone', true);

            $this->redirect('finance', 'add', array('type' => $clone_model->get('type'), 'company' => $clone_model->get('company'), 'report_session_param' => 'clone'), 'expenses_reasons');
        }
    }

    /**
     * Allocates amount of non-commodities from current expense document to
     * inventory (commodities) from expense documents (deliveries)
     *
     * @return boolean - result of the operation
     */
    private function _allocateCosts() {
        /** @var Request $request */
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array(
            'where' => array("fer.id = '{$id}'"),
            'model_lang' => $request['lang'],
        );
        /** @var Finance_Expenses_Reason $finance_expenses_reason */
        $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);

        if ($finance_expenses_reason) {
            $this->checkAccessOwnership($finance_expenses_reason);

            // Set old model, so action automations can be executed
            $oldModel = clone $finance_expenses_reason;
            $getOldVars = $this->registry->get('get_old_vars');
            $this->registry->set('get_old_vars', true, true);
            $oldModel->getGT2Vars();
            $oldModel->sanitize();
            $this->registry->set('get_old_vars', $getOldVars, true);
            $oldModel->getAllocatedCosts();
            $this->old_model = $oldModel;

            if ($request->isPost()) {
                // set submitted data into model
                foreach ($request->getAll('post') as $key => $value) {
                    if (!$finance_expenses_reason->isDefined($key)) {
                        $finance_expenses_reason->set($key, $value, true);
                    }
                }
                if ($finance_expenses_reason->setAllocatedCosts()) {

                    $finance_expenses_reason = Finance_Expenses_Reasons::searchOne($this->registry, $filters);
                    $action_type = $finance_expenses_reason->get('allocated_status') == 'allocated' ? 'allocate_costs' : 'delete_allocate_costs';

                    // history for save/removal of allocation
                    require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.history.php';
                    Finance_Expenses_Reasons_History::saveData(
                        $this->registry,
                        array(
                            'action_type' => $action_type,
                            'new_model' => $finance_expenses_reason,
                            'old_model' => $finance_expenses_reason,
                        )
                    );

                    $this->registry['messages']->setMessage(
                        $this->i18n(
                            "message_finance_expenses_reasons_{$action_type}_success",
                            array($finance_expenses_reason->getModelTypeName())
                        ), '', -1
                    );
                    $this->registry['messages']->insertInSession($this->registry);

                    $this->actionCompleted = true;
                } else {
                    $this->registry['messages']->setError(
                        $this->i18n('error_finance_expenses_reasons_allocate_costs_failed',
                        array($finance_expenses_reason->getModelTypeName())), '', -2
                    );
                }
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_expenses_reason')) {
                $this->registry->set('finance_expenses_reason', $finance_expenses_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Updates allocated status of model from AJAX request for partial model update
     *
     * @return boolean - always return true
     */
    private function _updateAllocatedStatus() {
        /** @var Registry $registry */
        $registry = &$this->registry;
        /** @var Request $request */
        $request = $this->registry['request'];

        $id = $request->get($this->action);
        $model = null;
        if (is_numeric($id) && $id) {
            $model = Finance_Expenses_Reasons::searchOne(
                $registry,
                array(
                    'where' => array(
                        "fer.id = '{$id}'",
                    ),
                )
            );
        }

        if ($model) {
            if ($this->checkAccessOwnership($model, false)) {
                foreach (array('admit_VAT_credit', 'accounting_year', 'accounting_month') as $prop) {
                    $model->unsetProperty($prop, true);
                }
                $request->loadPatchData($registry, $model);
                $request->set('edit_finished', '1', 'post', true);
                // do not change action to edit, keep the current one
                $this->_edit();
            } else {
                $registry['messages']->setError($this->i18n('error_status_change'));
            }
        } else {
            $registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
        }
        $result = $this->getMessagesForAJAX($this->actionCompleted ? 'message' : 'error');
        if (
            $this->actionCompleted &&
            $request->get('allocated_status') == 'enabled' &&
            $model->set('allocated_status', 'enabled', true) &&
            $this->checkAccessOwnership($model, false, 'allocate_costs')
        ) {
            $result = json_encode(json_decode($result, true) + array('redirect' => 1));
        }

        $registry->set('ajax_result', $result, true);
        $registry['messages']->removeFromSession($registry);

        header('Content-Type: application/json');
        return true;
    }

    /**
     * Returns commodity rows of selected expense for allocate_costs action.
     * Method is called from execute_after of delivery expense autocompleters.
     *
     * @return boolean - always return true
     */
    private function _loadDeliveryRows() {
        /** @var Registry $registry */
        $registry = &$this->registry;
        /** @var Request $request */
        $request = $this->registry['request'];

        // id of selected delivery expense
        $id = $request->get($this->action);
        /** @var Finance_Expenses_Reason $model */
        $model = null;
        if (is_numeric($id) && $id) {
            $model = Finance_Expenses_Reasons::searchOne(
                $registry,
                array(
                    'where' => array(
                        "fer.id = '{$id}'",
                    ),
                )
            );
        }
        if (!$model) {
            $registry['messages']->setError($this->i18n('error_no_such_finance_expenses_reason'));
        } else {
            if ($model->get('type') < PH_FINANCE_TYPE_MAX && $model->get('parent_type') > PH_FINANCE_TYPE_MAX) {
                // secondary expense documents cannot be allocated to because
                // they don't update nomenclature prices and have no
                // corresponding logged data for that action (at least for now)
                $registry['messages']->setError($this->i18n('error_delivery_no_nom_price_updates'));
            } elseif (
                // primary expense documents that issue no handovers should not
                // be allocated to unless they are invoiced reasons (in which
                // case handovers are issued from secondary documents)
                $model->get('handovered_status') == 'none' &&
                !(
                    $model->get('type') > PH_FINANCE_TYPE_MAX &&
                    $model->getModelType()->get('commodity') != 'none' &&
                    !($model->checkAddingInvoice() && $model->checkAddingInvoice(true))
                )
            ) {
                $registry['messages']->setError($this->i18n('error_delivery_no_commodities'));
            }
        }

        if ($registry['messages']->getErrors()) {
            $result = $this->getMessagesForAJAX('error', 'array');
            $registry->set('ajax_result', $result['errors'], true);
            return true;
        }

        $gov = $registry->get('get_old_vars');
        if (!$gov) {
            $registry->set('get_old_vars', true, true);
        }
        $model->set('expense_currency', $request['expense_currency'], true);
        $model->set('expense_amount', $request['expense_amount'], true);
        $model->set('expense_type', $request['expense_type'], true);

        $gt2 = $model->prepareDeliveryGT2($model->getGT2Vars());

        // process values:
        // commodities
        $article_ids = array();
        foreach ($gt2['values'] as $row) {
            if (!empty($row['article_id'])) {
                $article_ids[] = $row['article_id'];
            }
        }
        $article_ids = array_unique($article_ids);
        $nom_filters = array(
            'where' => array(
                "n.id IN ('" . implode("', '", $article_ids) . "')",
                "n.subtype = 'commodity'",
            ),
        );
        $article_ids = Nomenclatures::getIds($registry, $nom_filters);
        if ($article_ids) {
            // "weight" - admit_VAT_credit and currency (use today's rate)
            $rate = 1;
            if ($model->get('expense_currency') && $model->get('expense_currency') != $model->get('currency')) {
                $rate = Finance_Currencies::getRate($registry, $model->get('currency'), $model->get('expense_currency'));
            }
            $source_amount_field = $model->get('admit_VAT_credit') ? 'subtotal_with_discount' : 'subtotal_with_vat_with_discount';

            foreach ($gt2['values'] as $idx => $row) {
                if (empty($row['article_id']) || !in_array($row['article_id'], $article_ids)) {
                    unset($gt2['values'][$idx]);
                } else {
                    $gt2['values'][$idx]['subtotal_amount'] = sprintf('%.6f', round($row[$source_amount_field] * $rate, 6));
                    $gt2['values'][$idx]['allocated_amount'] = '';
                    $gt2['values'][$idx]['selected'] = ' ';
                }
            }
        } else {
            $gt2['values'] = array();
        }
        $gt2['rows'] = array_keys($gt2['values']);

        $result = '';
        if ($gt2['values']) {
            $viewer = new Viewer($registry);
            $viewer->setFrameset('_gt2_edit.html');
            $viewer->data['table'] = $gt2;
            $result = $viewer->fetch();
            $result = strstr(strstr($result, '<table'), '</table>', true) . '</table>';
        }
        if (!$result) {
            $registry['messages']->setError($this->i18n('no_items_found'));
            $result = $this->getMessagesForAJAX('error', 'array');
            $result = $result['errors'];
        }

        if (!$gov) {
            $registry->remove('get_old_vars');
        }

        $registry->set('ajax_result', $result, true);

        return true;
    }
}
