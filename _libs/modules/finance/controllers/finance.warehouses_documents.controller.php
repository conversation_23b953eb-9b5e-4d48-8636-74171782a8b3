<?php
require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';

class Finance_Warehouses_Documents_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Finance_Warehouses_Document';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Finance_Warehouses_Documents';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'export', 'add', 'edit', 'view', 'generate',
        'attachments', 'assign', 'tag',
        'observer', 'print', 'manage_outlooks', 'annul',
        'relatives', 'history', 'comments', 'emails', 'communications', 'printlist'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'relatives', 'history', 'communications'
    );

    /**
     * Action which are at the up right position (without tabs)
     */
    public $actionDefinitionsUpRight = array(
        'observer', 'print', 'manage_outlooks', 'printlist'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'edit', 'view', 'translate',
        'attachments', 'assign', 'generate', 'relatives'
    );

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_get_existing_batch_data',
    );

    /**
     * The fields which will switch the additional variables in the search panel
     */
    public static $searchAdditionalVarsSwitch = 'fwd.type';

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'search':
            $this->_search();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'relatives':
            $this->_relatives();
            break;
        case 'assign':
            $this->_assign();
            break;
        case 'ajax_assign':
            $this->_getAssignments();
            break;
        case 'observer':
            $this->_observer();
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'finish_inspection':
            $this->_finishInspection();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'generate':
            $this->_generate();
            break;
        case 'print':
            $this->_print();
            break;
        case 'multiprint':
            $this->_multiPrint();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'history':
            $this->_history();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'ajax_change_depending_options':
            $this->_changeDependingOptions();
            break;
        case 'annul':
            $this->_annul();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'tag':
            $this->_tag();
            break;
        case 'multitag':
            $this->_multiTag();
            break;
        case 'multiremovetag':
            $this->_multiRemoveTag();
            break;
        case 'ajax_tag':
            $this->_getTags();
            break;
        case 'release_reservation':
            $this->_releaseReservation();
            break;
        case 'finish_reservation':
            $this->_finishReservation();
            break;
        case 'ajax_get_existing_batch_data':
            $this->_getExistingBatchData();
            break;
        case 'ajax_import_table':
            $this->_importTable();
            break;
        case 'ajax_import_table_configurator':
            $this->_importTableConfigurator();
            break;
        case 'ajax_get_batch_unique_key_elements_names':
            $this->ajaxGetBatchUniqueKeyElementsNames();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //only for update user_permissions field for all finance warehouses documents
        // EXECUTE TEMPORARY METHOD
        //Finance_Warehouses_Documents::updateUserPermissions($this->registry);

        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {

        $this->registry->set('get_old_vars', false, true);
        $finance_warehouses_document = Finance_Warehouses_Documents::buildModel($this->registry);
        $request = &$this->registry['request'];

        //check if details are submitted via POST
        if ($this->registry['request']->isPost()) {
            $old_model = new Finance_Warehouses_Document($this->registry);
            // set type and company in order to prepare GT2 table in old model
            $old_model->set('type', $finance_warehouses_document->get('type'), true);
            $old_model->set('company', $finance_warehouses_document->get('company'), true);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);

            if ($this->_prepareModel($finance_warehouses_document) && $finance_warehouses_document->save()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fwd.id = ' . $finance_warehouses_document->get('id')));
                $new_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                $new_model->getGT2Vars();
                //show success message
                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                               array('action_type' => 'add',
                                                                                     'new_model' => $new_model,
                                                                                     'old_model' => $old_model
                                                                               ));

                $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_add_success',
                                                                    array($finance_warehouses_document->getModelTypeName())));
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;
                $this->old_model = $old_model;
                $finance_warehouses_document = $new_model;
                $finance_warehouses_document->set('table_values_are_set', true, true);
            } else {
                $finance_warehouses_document->unsetProperty('id', true);
                $finance_warehouses_document->set('status', 'opened', true);
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_add_failed',
                                                                  array($finance_warehouses_document->getModelTypeName())), '', -1);
            }
        } else {
            $error = false;
            //check if all required fields are filled in, check permissions to selected option
            if (!$finance_warehouses_document->get('company') ||
                !$finance_warehouses_document->get('office') ||
                !$finance_warehouses_document->get('warehouse')) {
                $wh = $this->i18n('finance_warehouses_documents_warehouse');
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_no_warehouse_specified',
                                                                  array(mb_strtolower($wh, mb_detect_encoding($wh)))), 'warehouse_data_');
                $error = true;
            }
            if (!$error) {
                require_once $this->modelsDir . 'finance.dropdown.php';
                $wh_active = Finance_Dropdown::getWarehouseData(
                    array(
                        0 => $this->registry,
                        'active' => 1,
                        'company_id' => $finance_warehouses_document->get('company'),
                        'office_id' => $finance_warehouses_document->get('office'),
                        'warehouse_id' => $finance_warehouses_document->get('warehouse')
                    )
                );
                $wh_active = reset($wh_active);
                if ($wh_active) {
                    $wh_active = array_shift($wh_active);
                    if ($wh_active['locker_status'] == 'opened') {
                        $link = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                                    $_SERVER['PHP_SELF'],
                                    $this->registry['module_param'], $this->module,
                                    $this->registry['controller_param'], $this->controller,
                                    $this->registry['action_param'], "view",
                                    "view", $wh_active['locker']);
                        $this->registry['messages']->setError($this->i18n('error_finance_warehouse_locked', array($finance_warehouses_document->get('warehouse_name'), $link)));
                        $error = true;
                    }
                } else {
                    $error = true;
                }
            }
            if (!$finance_warehouses_document->get('type') ||
            in_array($finance_warehouses_document->get('type'), array(
                PH_FINANCE_TYPE_HANDOVER, PH_FINANCE_TYPE_COMMODITIES_RELEASE,
                PH_FINANCE_TYPE_INSPECTION_MISSING, PH_FINANCE_TYPE_INSPECTION_SURPLUS)) ||
            !$this->checkActionPermissions($this->module . '_' . $this->controller . $finance_warehouses_document->get('type'), 'add')) {
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_invalid_type'));
                $error = true;
            }

            if ($error) {
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_add_failed',
                                                      array($finance_warehouses_document->getModelTypeName() ?: $this->i18n('finance_warehouses_document'))), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                if (isset($_SERVER['HTTP_REFERER']) && !preg_match('#=add(&|$)#', $_SERVER['HTTP_REFERER'])) {
                    header("Location: " . $_SERVER['HTTP_REFERER']);
                    exit;
                } else {
                    $this->redirect($this->module, 'list');
                }
            }
        }

        if (!empty($finance_warehouses_document)) {
            if (!$finance_warehouses_document->get('table_values_are_set')) {
                $this->registry->set('get_old_vars', false, true);
                $finance_warehouses_document->getGT2Vars();
            }

            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeType = Finance_Documents_Types::searchOne($this->registry,
                                                              array('where' => array('fdt.id=' . $finance_warehouses_document->get('type'))));
            $finance_warehouses_document->set('type_name', $financeType->get('name'), true);

            if (!$this->registry['request']->isPost()) {
                //set default values from the type
                $finance_warehouses_document->setDepartment(false, $financeType->get('default_department'));
                $finance_warehouses_document->setGroup(false, $financeType->get('default_group'));
            }

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
        }

        return true;
    }

    /**
     * Prepare commodities transfer
     *
     * @param object $model - model built from request
     * @return boolean - true if model is not a commodities transfer or there are some rows with > 0 quantity, otherwise false
     */
    private function _prepareModel(&$model) {

        if ($model->get('type') == PH_FINANCE_TYPE_COMMODITIES_TRANSFER) {
            $request = &$this->registry['request'];

            require_once PH_MODULES_DIR . 'finance/models/finance.dropdown.php';
            $params = array($this->registry,
                            'company' => $model->get('company'),
                            'office' => $model->get('office'),
                            'model_lang' => $model->get('model_lang'));
            $warehouses = Finance_Dropdown::getWarehouses($params);
            foreach ($warehouses as $wh) {
                if ($wh['option_value'] == $model->get('warehouse')) {
                    $model->set('warehouse_name', $wh['label'], true);
                    break;
                }
            }

            $params = array($this->registry,
                            'company' => $model->get('to_company'),
                            'office' => $model->get('to_office'),
                            'model_lang' => $model->get('model_lang'));
            $warehouses = Finance_Dropdown::getWarehouses($params);
            foreach ($warehouses as $wh) {
                if ($wh['option_value'] == $model->get('to_warehouse')) {
                    $model->set('to_warehouse_name', $wh['label'], true);
                    break;
                }
            }

            // if commodities transfer is locked, GT2 cannot be modified
            if ($model->get('status') == 'locked') {
                $model->set('edit_locked', true, true);
                return true;
            }

            $model->set('status', 'opened', true);

            $quantity = $request->isRequested('quantity') ? $request->get('quantity') : array();
            $quantity = array_filter($quantity, function($el) {return $el > 0;});

            $article_id = $request->isRequested('article_id') ? $request->get('article_id') : array();
            $article_id = array_filter($article_id, function($el) {return $el > 0;});

            $deleted = $request->isRequested('deleted') ? $request->get('deleted') : array();
            $deleted = array_filter($deleted, function($el) {return $el > 0;});

            foreach ($deleted as $idx => $val) {
                if (isset($quantity[$idx])) {
                    unset($quantity[$idx]);
                }
                if (isset($article_id[$idx])) {
                    unset($article_id[$idx]);
                }
            }

            return !empty($quantity) && !empty($article_id);
        } else {
            return true;
        }
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($this->registry['request']->isPost()) {
            //build the model from the POST
            $finance_warehouses_document = Finance_Warehouses_Documents::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fwd.id = ' . $finance_warehouses_document->get('id')));
            $old_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);

            $this->checkAccessOwnership($old_model);

            $finance_warehouses_document->set('status', $old_model->get('status'), true);

            if ($this->_prepareModel($finance_warehouses_document) && $finance_warehouses_document->save()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fwd.id = ' . $finance_warehouses_document->get('id')));
                $new_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                $new_model->getGT2Vars();

                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                               array('action_type' => 'edit',
                                                                                     'new_model' => $new_model,
                                                                                     'old_model' => $old_model
                                                                               ));

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_edit_success',
                        array($finance_warehouses_document->getModelTypeName())));
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;
                $this->old_model = $old_model;
                $finance_warehouses_document = $new_model;
                $finance_warehouses_document->set('table_values_are_set', true, true);
            } else {
                $finance_warehouses_document->set('status', $old_model->get('status'), true);
                if ($finance_warehouses_document->get('edit_locked')) {
                    $finance_warehouses_document->set('grouping_table_2', $old_model->get('grouping_table_2'), true);
                    $finance_warehouses_document->set('table_values_are_set', true, true);
                }
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_edit_failed',
                        array($finance_warehouses_document->getModelTypeName())), '', -1);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fwd.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

            if ($finance_warehouses_document) {
                $this->checkAccessOwnership($finance_warehouses_document);
            }
        }

        if (!empty($finance_warehouses_document)) {
            if (!$finance_warehouses_document->get('table_values_are_set')) {
                $this->registry->set('get_old_vars', false, true);
                $finance_warehouses_document->getGT2Vars();
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fwd.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if (!empty($finance_warehouses_document)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_warehouses_document);

            $finance_warehouses_document->getGT2Vars();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Finish inspection, finish surplus and missing records,
     * set new warehouse quantities for articles from inspection
     *
     * @return boolean - result of the operation
     */
    private function _finishInspection() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fwd.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $old_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if (!empty($old_model)) {
            $old_model->getGT2Vars();
            $old_model->getBatchesData();
            $this->registry->set('no_GT2', true, true);
            if ($old_model->get('type') == PH_FINANCE_TYPE_INSPECTION) {
                $inspection = clone $old_model;
                $relatives = $inspection->getRelatives();
            }

            //there should be max two difference documents (record of missing stock or record of surplus stock)
            if (!empty($relatives['child'][0]) && is_object($relatives['child'][0])) {
                $difference1 = $relatives['child'][0];
                $difference1->unsanitize();
                $difference1->getGT2Vars();
                $difference1->getBatchesData();
                //change the status of difference document to finished
                $difference1->set('status', 'finished', true);
            }

            //second difference (missing/surplus)
            if (!empty($relatives['child'][1]) && is_object($relatives['child'][1])) {
                $difference2 = $relatives['child'][1];
                $difference2->unsanitize();
                $difference2->getGT2Vars();
                $difference2->getBatchesData();
                //change the status of difference document to finished
                $difference2->set('status', 'finished', true);
            }

            //change the status the inspection
            $inspection->set('status', 'finished', true);

            //start transaction!!!
            $this->registry['db']->StartTrans();

            if ($inspection->save()) {
                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                            array('action_type' => 'status',
                                                                                  'new_model' => $inspection,
                                                                                  'old_model' => $old_model
                                                                            ));

                if ($inspection->setWarehouseQuantities()) {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_quantities_set',
                                                            array($inspection->getModelTypeName())));
                }

                //prepare names of the records for missing stock and surplus
                $name_missing = $this->i18n('finance_warehouses_documents_inspection_missing');
                $name_missing .= ' ' . sprintf($this->i18n('finance_warehouses_documents_for_inspection'), $inspection->get('num'));

                $name_surplus = $this->i18n('finance_warehouses_documents_inspection_surplus');
                $name_surplus .= ' ' . sprintf($this->i18n('finance_warehouses_documents_for_inspection'), $inspection->get('num'));
            }

            //save the two difference documents
            if (!empty($difference1)) {
                if ($difference1->get('type') == PH_FINANCE_TYPE_INSPECTION_SURPLUS) {
                    $difference1->set('name', $name_surplus, true);
                } elseif ($difference1->get('type') == PH_FINANCE_TYPE_INSPECTION_MISSING) {
                    $difference1->set('name', $name_missing, true);
                }
                $difference1->save();
            }
            if (!empty($difference2)) {
                if ($difference2->get('type') == PH_FINANCE_TYPE_INSPECTION_SURPLUS) {
                    $difference2->set('name', $name_surplus, true);
                } elseif ($difference2->get('type') == PH_FINANCE_TYPE_INSPECTION_MISSING) {
                    $difference2->set('name', $name_missing, true);
                }
                $difference2->save();
            }
            $this->registry->set('no_GT2', false, true);

            //the transaction status could be checked only before CompleteTrans()
            $dbTransError = $this->registry['db']->HasFailedTrans();

            //complete the transaction (commit/rollback whether SQL failed or not)
            $this->registry['db']->CompleteTrans();

            if ($dbTransError) {
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_quantities_not_set'));
            } else {
                $this->actionCompleted = true;
                $this->registry['messages']->insertInSession($this->registry);
                if (empty($difference2)) {
                    if (!empty($difference1)) {
                        // redirect to only difference document (missing or suprplus)
                        $this->redirect($this->module, 'view', array('view' => $difference1->get('id')));
                    } else {
                        // redirect to inspection
                        $this->redirect($this->module, 'view', array('view' => $id));
                    }
                } else {
                    // two difference documents created (missing+surplus), redirect to general list
                    $this->redirect($this->module, 'list', array('type' => ''));
                }
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
        }
        $this->registry['messages']->insertInSession($this->registry);
        //redirect to the listing
        if ($old_model) {
            $this->redirect($this->module, 'view', 'view=' . $old_model->get('id'));
        } else {
            $this->redirect($this->module, 'list');
        }
        return true;
    }

    /**
     * Finish commodities reservation
     *
     * @return boolean - result of the operation
     */
    private function _finishReservation() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        /**
         * @var $reservation Finance_Warehouses_Document
         */
        $filters = array('where' => array('fwd.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $reservation = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if (!empty($reservation)) {
            if ($reservation->get('status') == 'locked' && $reservation->finishReservation()) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_finish_reservation'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', array('view' => $reservation->get('release_id')));
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_finish_reservation'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', array('view' => $reservation->get('id')));
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
        }

        $this->registry['messages']->insertInSession($this->registry);
        //redirect to the listing
        $this->redirect($this->module, 'list');

        return true;
    }

    /**
     * Release some quantities of commodities reservation document
     *
     * @return boolean - result of the operation
     */
    private function _releaseReservation() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($this->registry['request']->isPost()) {
            //build the model from the POST
            /**
             * @var Finance_Warehouses_Document $finance_warehouses_document
             */
            $filters = array('where' => array ('fwd.id = ' . $id));
            $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
            $finance_warehouses_document->getGT2Vars();
            $finance_warehouses_document->getBatchesData();
            $finance_warehouses_document->prepareRequestedBatches();

            //get the model and its old values
            $filters = array('where' => array ('fwd.id = ' . $finance_warehouses_document->get('id')));
            $old_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);

            if ($finance_warehouses_document->partlyReleaseReservation()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fwd.id = ' . $finance_warehouses_document->get('id')));
                $new_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_model->getGT2Vars();
                $new_model->getBatchesData();

                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                               array('action_type' => 'release_reservation',
                                                                                   'new_model' => $new_model,
                                                                                   'old_model' => $old_model
                                                                               ));


                if ($new_model->get('status') == 'finished') {
                    //the reservation is thoroughly released and finished
                    $this->registry['messages']->setMessage(
                        $this->i18n('message_finance_warehouses_documents_finish_reservation')
                    );
                } else {
                    //show success message for partial release
                    $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_release_reservation_success',
                                                                        array($finance_warehouses_document->getModelTypeName())));
                }

                $addedReleaseDocId = $finance_warehouses_document->get('release_id');
                $addedReleaseDoc = Finance_Warehouses_Documents::searchOne($this->registry, array(
                    'where' => array('fwd.id = ' . $addedReleaseDocId)
                ));
                if ($addedReleaseDoc) {
                    $link = sprintf(
                        '%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                        $_SERVER['PHP_SELF'],
                        $this->registry['module_param'],
                        $this->module,
                        $this->registry['controller_param'],
                        $this->controller,
                        $this->registry['action_param'],
                        "view",
                        "view",
                        $addedReleaseDoc->get('id')
                    );
                    $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_release_reservation_success2',
                                                                        array($link, $addedReleaseDoc->get('num'))));
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'view', array('view' => $new_model->get('id')));

            } else {
                //$finance_warehouses_document->set('grouping_table_2', $old_model->get('grouping_table_2'), true);
                //$finance_warehouses_document->set('table_values_are_set', true, true);
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_release_reservation_failed',
                                                                  array($finance_warehouses_document->getModelTypeName())), '', -1);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fwd.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

            if ($finance_warehouses_document) {
                $this->checkAccessOwnership($finance_warehouses_document);
            }

       }

        if (!empty($finance_warehouses_document)) {
            if ($finance_warehouses_document->get('type') != PH_FINANCE_TYPE_COMMODITIES_RESERVATION) {
                $this->registry['messages']->setError(
                    $this->i18n('error_no_such_finance_warehouses_document')
                );
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'list');
            }
            if ($finance_warehouses_document->get('status') == 'finished') {
                $this->registry['messages']->setError(
                    $this->i18n('error_finance_warehouses_document_already_released'),
                    array($finance_warehouses_document->getModelTypeName())
                );
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', array('view' => $finance_warehouses_document->get('id')));
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Changes options of related dropdowns
     */
    private function _changeDependingOptions() {
        $request = $this->registry['request'];
        require_once $this->modelsDir . 'finance.dropdown.php';
        if (preg_match('/employees/', $request->get('s_id'))) {
            $employees = array();
            if ($request->get('warehouse')) {
                $params = array($this->registry,
                                'lang' => $request->get('model_lang'),
                                'warehouse' => $request->get('warehouse'),
                                'active' => 1);
                $employees = Finance_Dropdown::getWarehousesEmployees($params);
            }
            print json_encode($employees);
            exit;
        }
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get the model and it's old values
            $filters = array('where' => array ('fwd.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $old_model = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $finance_warehouses_document = Finance_Warehouses_Documents::buildModel($this->registry);
            $finance_warehouses_document->set('id', $id, true);
            //$this->registry->set('get_old_vars', false, true);

            if ($finance_warehouses_document->save()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fwd.id = ' . $id),
                                 'model_lang' => $request->get('model_lang'));
                $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
                $finance_warehouses_document->getGT2Vars();

                //show success message
                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                $audit_parent = Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                            array('action_type' => 'translate',
                                                                                  'new_model' => $finance_warehouses_document,
                                                                                  'old_model' => $old_model
                                                                            ));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_finance_warehouses_documents_translate_success',
                                                        array($finance_warehouses_document->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //register the model, with all the posted details
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_translate_failed',
                                                      array($finance_warehouses_document->getModelTypeName())), '', -2);
                //register the model, with all the posted details
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('fwd.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
        }

        if (!empty($finance_warehouses_document)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * History of warehouse document
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'fwd.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($finance_warehouses_document && $this->checkAccessOwnership($finance_warehouses_document, false)) {
                if (!$this->registry->isRegistered('finance_warehouses_document')) {
                    $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
                }

                require_once $this->viewersDir . 'finance.warehouses_documents.history.viewer.php';
                $viewer = new Finance_Warehouses_Documents_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($finance_warehouses_document)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_warehouses_document);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Communication concerning the warehouse document (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fwd.id = ' . $id ));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
        if (!empty($finance_warehouses_document)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_warehouses_document);

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    public function checkAccessOwnership($model, $redirect = true, $action = '') {
        $result = parent::checkAccessOwnership($model);

        if ($result) {
            if (is_object($model) && $this->action == 'edit') {
                require_once $this->modelsDir . 'finance.dropdown.php';
                $wh_active = Finance_Dropdown::getWarehouseData(
                    array(
                        0 => $this->registry,
                        'active' => 1,
                        'company_id' => $model->get('company'),
                        'office_id' => $model->get('office'),
                        'warehouse_id' => $model->get('warehouse')
                    )
                );
                $error = false;
                if (!$wh_active) {
                    $this->registry['messages']->setError($this->i18n('error_no_access_to_model'));
                    $this->registry['messages']->insertInSession($this->registry);
                    $error = true;
                } else {
                    $wh_active = reset($wh_active);
                    $wh_active = array_shift($wh_active);
                    if ($wh_active['locker_status'] == 'opened' && $model->get('status') == 'opened' && $wh_active['locker'] != $model->get('id')) {
                        $link = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                                $_SERVER['PHP_SELF'],
                                $this->registry['module_param'], $this->module,
                                $this->registry['controller_param'], $this->controller,
                                $this->registry['action_param'], "view",
                                "view", $wh_active['locker']);
                        $this->registry['messages']->setError($this->i18n('error_finance_warehouse_locked', array($model->get('warehouse_name'), $link)));
                        $this->registry['messages']->insertInSession($this->registry);
                        $error = true;
                    }
                }
                if (!($model->get('status') == 'opened' || $model->get('status') == 'locked' && $model->get('type') == PH_FINANCE_TYPE_COMMODITIES_TRANSFER) || $error) {
                    if ($redirect) {
                        $this->redirect('finance', 'view', 'view='.$model->get('id'));
                    } else {
                        return false;
                    }
                }
            }
            if (is_object($model) && $this->action == 'relatives' && $model->get('status') != 'finished') {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view='.$model->get('id'));
                } else {
                    return false;
                }
            }
            return true;
        }
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $this->settings_assign = array();
        if ($this->model && $this->model->get('type')) {
            $this->settings_assign = $this->registry['config']->getParamAsArray('finance', 'assignment_types_' . $this->model->get('type'));
        }

        $actions = parent::getActions($action_defs);

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['edit']) && !($this->model->get('status') == 'opened' || $this->model->get('status') == 'locked' && $this->model->get('type') == PH_FINANCE_TYPE_COMMODITIES_TRANSFER)) {
                unset($actions['edit']);
            }
        }

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        if (isset($actions['add']) || isset($actions['printlist'])) {
            $customize = '';
            $found = 0;

            $custom_filters = array();
            if ($this->registry['request']->get('type')) {
                $customize = 'fdt.id="' . $this->registry['request']->get('type') . '"';
                $found++;
            } else if ($this->registry['request']->get('type_section')) {
                $customize = 'fdt.type_section = "' . $this->registry['request']->get('type_section') . '"';
                $found++;
            } else if ($this->registry['request']->isRequested('search_fields')) {
                $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
                $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
                $custom_filters['values'] = $this->registry['request']->get('values');
            } else if ($this->registry['session']->isRequested($this->action . '_finance_warehouses_document')) {
                $custom_filters = $this->registry['session']->get($this->action . '_finance_warehouses_document');
            }

            if (!empty($custom_filters)) {
                // shows if there is a type defined and if so doesn't add the type section filter
                $type_defined = false;
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#fwd\.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize = 'fdt.id = "' . $custom_filters['values'][$key] . '"';
                            if ($type_defined) {
                                $found++;
                            } else {
                                $type_defined = true;
                                $found = 1;
                            }
                        }
                    }
                }
            }
        }

        if (isset($actions['add'])) {
            // if there is a model, the only available options for add
            // will be the options for the current type or the current section
            $filters = array(
                'where' => array(
                    'fdt.model = "Finance_Warehouses_Document"',
                    'fdt.id IN (' . implode(',', array(
                        PH_FINANCE_TYPE_COMMODITIES_TRANSFER,
                        PH_FINANCE_TYPE_COMMODITIES_RESERVATION,
                        PH_FINANCE_TYPE_WASTE,
                        PH_FINANCE_TYPE_INSPECTION)) .
                    ')',
                    'fdt.active = 1',
                ),
                'sort' => array('fdti18n.name ASC'),
                'sanitize' => true
            );
            if ($found == 1 && $customize) {
                if (!preg_match('#fdt\.id#', $customize)) {
                    $filters['where'][] = $customize;
                }
            }

            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeTypes = Finance_Documents_Types::search($this->registry, $filters);

            $types = array();
            foreach ($financeTypes as $type) {
                //check permissions by type
                if ($this->checkActionPermissions($module_check . $type->get('id'), 'add')) {
                    $types[] = array('label' => $type->get('name'), 'option_value' => $type->get('id'));
                }
            }

            if (!empty($types)) {
                $sel_type = $this->registry['request']->get('type') ?: $types[0]['option_value'];

                require_once $this->modelsDir . 'finance.dropdown.php';
                $params = array(0 => $this->registry,
                                'active' => 1);
                $wd_options = Finance_Dropdown::getWarehouseData($params);

                $default_warehouse_data = $this->registry['currentUser']->getDefaultWarehouseData($wd_options);
                $warehouse_data = $this->registry['request']->get('warehouse_data') ?: $default_warehouse_data;
                $to_warehouse_data = $this->registry['request']->get('to_warehouse_data') ?: ($default_warehouse_data != $warehouse_data ? $default_warehouse_data : '');

                $add_options = array (
                    array (
                        'custom_id' => 'type_',
                        'name' => 'type',
                        'type' => 'dropdown',
                        'required' => 1,
                        'onchange' => 'toggleCommoditiesTransferAddFields(this, ' . PH_FINANCE_TYPE_COMMODITIES_TRANSFER . ')',
                        'label' => $this->i18n('finance_warehouses_documents_type'),
                        'options' => $types,
                        'value' => $sel_type),
                    array (
                        'custom_id' => 'warehouse_data_',
                        'name' => 'warehouse_data',
                        'type' => 'dropdown',
                        'required' => 1,
                        'really_required' => 1,
                        'label' => $this->i18n('finance_warehouses_documents_' . ($sel_type == PH_FINANCE_TYPE_COMMODITIES_TRANSFER ? 'from_' : '') . 'warehouse'),
                        'optgroups' => $wd_options,
                        'value' => $warehouse_data,
                        'custom_class' => (count($wd_options) > 1 && !$warehouse_data) ? 'undefined' : ''),
                    array (
                        'custom_id' => 'to_warehouse_data_',
                        'name' => 'to_warehouse_data',
                        'type' => 'dropdown',
                        'required' => 0,
                        'hidden' => ($sel_type == PH_FINANCE_TYPE_COMMODITIES_TRANSFER ? 0 : 1),
                        'label' => $this->i18n('finance_warehouses_documents_to_warehouse'),
                        'optgroups' => $wd_options,
                        'value' => $to_warehouse_data)
                );

                $actions['add']['options'] = $add_options;
                $actions['add']['ajax_no'] = 1;
            } else {
                unset($actions['add']);
            }
        } else {
            unset($actions['add']);
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['assign']) && !empty($this->settings_assign) && !$this->model->get('annulled_by')) {
            $actions['assign']['options'] = 0;
        } else {
            unset($actions['assign']);
        }
        if (isset($actions['observer']) && in_array('observer', $this->settings_assign) && !$this->model->get('annulled_by')) {
            $actions['observer']['options'] = 0;
            if (Finance_Warehouses_Documents::checkObserver($this->registry, $this->model)) {
                $actions['observer']['img'] = 'stopobserve';
                $actions['observer']['label'] = $this->i18n('stopobserve');
            } else {
                $actions['observer']['img'] = 'observe';
                $actions['observer']['label'] = $this->i18n('observe');
            }
        } else {
            unset($actions['observer']);
        }

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            // filter patterns for handover by direction
            if ($this->model->get('type') == PH_FINANCE_TYPE_HANDOVER) {
                $filters['where'][] = 'p.handover_direction IN ("' . $this->model->get('direction') . '", "both")';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // filter patterns for company
            if ($this->model->get('company')) {
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            if (empty($patterns)) {
                //remove generate action
                unset($actions['generate']);
            } else {
                $actions['generate']['options'] = 1;
            }
        } else {
            unset($actions['generate']);
        }

        if (isset($actions['print'])) {
            $patterns_options = array();
            if ($this->model->get('type')) {
                $filters_type = array(
                    'where' => array(
                        'fdt.id = ' . $this->model->get('type'),
                        'fdt.active = 1'
                    ),
                    'model_lang' => $this->getModelLang(),
                    'sanitize' => true
                );
                require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                $document_type = Finance_Documents_Types::searchOne($this->registry, $filters_type);

                //get the id of the default document type print template
                $default_pattern_id = 0;
                if ($document_type && $document_type->get('default_pattern')) {
                    $default_pattern_id = $document_type->get('default_pattern');
                }

                //get all generate/print patterns for this type
                require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                $filters_patterns = array('where' => array(
                                                  'p.model = \'' . $this->modelName . '\'',
                                                  'p.model_type = \'' . $this->model->get('type') . '\'',
                                                  'p.active = 1',
                                                  'p.list = 0'),
                                          'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                                          'model_lang' => $this->getModelLang(),
                                          'sanitize' => true);
                // filter patterns for handover by direction
                if ($this->model->get('type') == PH_FINANCE_TYPE_HANDOVER) {
                    $filters_patterns['where'][] = 'p.handover_direction IN ("' . $this->model->get('direction') . '", "both")';
                }
                if ($this->registry['currentUser']->get('is_portal')) {
                    $filters_patterns['where'][] = 'p.is_portal = 1';
                }
                // filter patterns for company
                if ($this->model->get('company')) {
                    $filters_patterns['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
                }
                $patterns = Patterns::search($this->registry, $filters_patterns);

                $available_patterns = array();
                foreach ($patterns as $pattern) {
                    $available_patterns[] = $pattern->get('id');
                    $patterns_options[] = array(
                        'id'        => $pattern->get('id'),
                        'label'     => $pattern->get('name'),
                        'img'       => $pattern->getIcon(),
                        'url'       => $actions['print']['url'] . '&amp;pattern=' . $pattern->get('id'),
                        'target'    => '_blank',
                    );
                }
            }

            if (empty($patterns_options)) {
                unset($actions['print']);
            } else {
                if ($default_pattern_id && in_array($default_pattern_id, $available_patterns)) {
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $default_pattern_id;
                } elseif (count($available_patterns) == 1) {
                    // sets the first pattern in the list as default and assigns a link to the direct print
                    list($first_pattern) = $patterns_options;
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $first_pattern['id'];
                } else {
                    $actions['print']['url'] = '#';
                }
                $actions['print']['drop_menu'] = true;
                $actions['print']['no_tab'] = true;
                $actions['print']['label'] = '';
                $actions['print']['target'] = '_blank';
                $actions['print']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'print';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['print']['img'] .= '_plus';
                }
                $actions['print']['options'] = $patterns_options;
            }
        }

        // communications action
        if (isset($actions['communications'])) {
            $actions['communications']['ajax_no'] = 1;
            $actions['communications']['drop_menu'] = true;
            $actions['communications']['hide_label'] = true;

            if (isset($actions['emails']) || isset($actions['comments'])) {
                if (isset($actions['comments'])) {
                    $actions['communications']['options']['comments']['img'] = 'comments';
                    $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                    $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    unset($actions['comments']);
                }
                if (isset($actions['emails'])) {
                    $actions['communications']['options']['emails']['img'] = 'email';
                    $actions['communications']['options']['emails']['label'] = $this->i18n('finance_warehouses_documents_action_email');
                    $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    unset($actions['emails']);
                }
            } else {
                unset($actions['communications']);
            }
        } else {
            if (isset($actions['emails'])) {
                unset($actions['emails']);
            }
            if (isset($actions['comments'])) {
                unset($actions['comments']);
            }
        }

        if (isset($actions['history']) && $this->model && $this->model->get('id')
            && $this->model->get('type') != PH_FINANCE_TYPE_INSPECTION_MISSING
            && $this->model->get('type') != PH_FINANCE_TYPE_INSPECTION_SURPLUS) {

        } else {
            unset($actions['history']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {
            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^fdt\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^fdt\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options)==1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        if (isset($actions['tag']) && $this->model && $this->model->get('id') && $this->model->getAvailableTags()) {

            $this->model->getTags();

            if ($this->model->checkPermissions('tags_view') && ($this->model->get('tags') && array_intersect($this->model->get('tags'), array_keys($this->model->get('available_tags'))) || $this->model->checkPermissions('tags_edit'))) {
                $actions['tag']['options'] = array('label' => $this->i18n('confirm_tags'));
                $actions['tag']['ajax_no'] = 1;
                $actions['tag']['template'] = '_action_tag.html';
                $actions['tag']['model_id'] = $this->model->get('id');
            } else {
                unset($actions['tag']);
            }
        } else {
            unset($actions['tag']);
        }

        if (isset($actions['annul']) && $this->model && $this->model->get('id') && $this->model->allowAnnul()) {
            $actions['annul']['confirm'] = 1;
            $actions['annul']['confirm_label'] = 'annul_warehouse_document';
        } else {
            unset($actions['annul']);
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        // check the current action and sets the alternative actions for view, edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } elseif ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit nor view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } elseif (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && !empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActionOptions($action_name = '') {
        //get model for this class
        $this->getModel();

        //get permissions of the currently logged user
        $this->getUserPermissions();

        $actions = parent::getActions(array($action_name));

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once(PH_MODULES_DIR . 'patterns/models/patterns.factory.php');
            $filters = array('where' => array('p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            // filter patterns for handover by direction
            if ($this->model->get('type') == PH_FINANCE_TYPE_HANDOVER) {
                $filters['where'][] = 'p.handover_direction IN ("' . $this->model->get('direction') . '", "both")';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // filter patterns for company
            if ($this->model->get('company')) {
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            $_options_patterns = array();
            foreach ($patterns as $pattern) {
                $_options_patterns[] = array(
                    'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                    'option_value' => $pattern->get('id'));
            }

            if (empty($_options_patterns)) {
                //remove generate action
                unset($actions['generate']);
            } else {
                //prepare generate options
                $generate_options = array (
                    array (
                        'custom_id' => 'pattern_',
                        'name' => 'pattern',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('finance_pattern'),
                        'help' => $this->i18n('finance_pattern'),
                        'options' => $_options_patterns,
                        'value' => ($this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : ''),
                );
                $actions['generate']['options'] = $generate_options;
            }
        }

        return $actions;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _generate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('generate'));
        $filters = array('where' => array('fwd.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if (!empty($finance_warehouses_document)) {
            //get the specified pattern
            $pattern_id = $request->get('pattern');
            if (empty($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_warehouses_document->get('id'));
            }

            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $pattern_id,
                                              'p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $finance_warehouses_document->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0',
                                              '(p.company = \'' . $finance_warehouses_document->get('company') . '\' OR p.company = \'0\')'),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);
            if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //invalid pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_warehouses_document->get('id'));
            } elseif ($pattern && in_array($pattern->get('handover_direction'), array('incoming', 'outgoing')) &&
            $finance_warehouses_document->get('type') == PH_FINANCE_TYPE_HANDOVER &&
            $pattern->get('handover_direction') != $finance_warehouses_document->get('direction')) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_pattern_not_allowed'));
                $this->registry['messages']->insertInSession($this->registry);

                //print with this pattern is not allowed for handover of this direction
                $this->redirect($this->module, 'view', 'view=' . $finance_warehouses_document->get('id'));
            }

            //get all generated files for the selected pattern
            $files = $finance_warehouses_document->getGeneratedFiles(array('pattern_id' => $pattern_id));

            //get file details
            $revision_id = $request->get('revision');
            if ($revision_id && $files) {
                foreach ($files as $file) {
                    if ($file->get('id') == $revision_id) {
                        //prepare selected revision details
                        $finance_warehouses_document->set('revision', $file, true);
                        break;
                    }
                }
            }

            //get revision details
            if (!empty($files)) {
                $finance_warehouses_document->set('revisions', $files, true);
            }
        }

        if ($request->isPost()) {
            if (!empty($finance_warehouses_document)) {
                $patterns_vars = $finance_warehouses_document->getPatternsVars();
                $finance_warehouses_document->extender = new Extender();
                $finance_warehouses_document->extender->model_lang = $finance_warehouses_document->get('model_lang');
                $finance_warehouses_document->extender->module = $this->module;
                foreach ($patterns_vars as $key => $value) {
                    $finance_warehouses_document->extender->add($key, $value);
                }
                switch ($request->get('format')) {
                    case 'xls':
                        // TO DO
                        break;
                    case 'csv':
                        // TO DO add option fields and also create a pattern which indicates the header columns
                        break;
                    case 'pdf':
                    default:
                    $this->old_model = clone $finance_warehouses_document;
                    if ($file_id = $finance_warehouses_document->generatePDF()) {
                        if ($request->get('submit_button') != 'preview') {
                            $this->registry['messages']->setMessage($this->i18n('message_finance_generate_success'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);

                            //save history
                            require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                            Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                           array('model' => $finance_warehouses_document,
                                                                                 'action_type' => 'generate',
                                                                                 'pattern' => $pattern->get('id'),
                                                                                 'generated_file' => $file_id));

                            $finance_warehouses_document->set('file_id', $file_id, true);
                            $this->actionCompleted = true;
                        }
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                    }
                    break;
                }
            }
        }

        if (!empty($finance_warehouses_document)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_warehouses_document);

            //register pattern to the registry
            if ($pattern) {
                $this->registry->set('pattern', $pattern->sanitize());
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _print() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('print'));
        $filters = array('where' => array('fwd.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if (!empty($finance_warehouses_document)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_warehouses_document);

            //get the specified pattern
            $pattern_id = $request->get('pattern');
            if (empty($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_warehouses_document->get('id'));
            }

            //get the pattern
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $pattern_id,
                                              'p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $finance_warehouses_document->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0',
                                              '(p.company = \'' . $finance_warehouses_document->get('company') . '\' OR p.company = \'0\')'),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);
            if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //invalid pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_warehouses_document->get('id'));
            } elseif ($pattern && in_array($pattern->get('handover_direction'), array('incoming', 'outgoing')) &&
            $finance_warehouses_document->get('type') == PH_FINANCE_TYPE_HANDOVER &&
            $pattern->get('handover_direction') != $finance_warehouses_document->get('direction')) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_pattern_not_allowed'));
                $this->registry['messages']->insertInSession($this->registry);

                //print with this pattern is not allowed for handover of this direction
                $this->redirect($this->module, 'view', 'view=' . $finance_warehouses_document->get('id'));
            }

            $patterns_vars = $finance_warehouses_document->getPatternsVars();
            $finance_warehouses_document->extender = new Extender();
            $finance_warehouses_document->extender->model_lang = $finance_warehouses_document->get('model_lang');
            $finance_warehouses_document->extender->module = $this->module;
            foreach ($patterns_vars as $key => $value) {
                $finance_warehouses_document->extender->add($key, $value);
            }

            if (!empty($pattern) && $pattern->get('force_generate')) {
                //generate and save file and get its id
                $this->old_model = clone $finance_warehouses_document;

                $result = false;
                if ($pattern->get('not_regenerate_finished_record') && $finance_warehouses_document->get('status')=='finished') {
                    $previous_generated_file = $finance_warehouses_document->getLastGeneratedFile($pattern);

                    if ($previous_generated_file) {
                        $result = $previous_generated_file->get('id');
                    }
                }

                if (!$result) {
                    $result = $finance_warehouses_document->generatePDF();
                }

                if ($result) {
                    $finance_warehouses_document->set('file_id', $result, true);
                    if (!$this->registry->isRegistered('finance_warehouses_document')) {
                        $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
                    }

                    //save history
                    require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                    Finance_Warehouses_Documents_History::saveData($this->registry,
                                                                   array('model' => $finance_warehouses_document,
                                                                         'action_type' => 'print',
                                                                         'pattern' => $pattern->get('id'),
                                                                         'generated_file' => $result));

                    // show the file
                    require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                    $file_id = $request->get('file');
                    $filters = array('where'    => array('f.id = ' . $result),
                                     'sanitize' => true);
                    $file = Files::searchOne($this->registry, $filters);

                    $file->viewFile();
                    exit;
                }
            } else {

                //save history
                require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
                Finance_Warehouses_Documents_History::saveData($this->registry,
                                                               array('model' => $finance_warehouses_document,
                                                                     'action_type' => 'print',
                                                                     'pattern' => $pattern->get('id'),
                                                                     'generated_file' => false));

                //generate file to the browser window
                $result = $finance_warehouses_document->generatePDF('browser_mode');
            }

            if ($result) {
                //the pdf content is displayed in the browser window
                exit;
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'view', array('view' => $finance_warehouses_document->get('id')));
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param mixed $ids Array of ids or crypted string with array of ids
     *                  or crypted string containing from_id and to_id
     */
    private function _multiPrint($ids = '') {
        $request = &$this->registry['request'];

        if (isset($_SERVER['HTTP_REFERER'])) {
            preg_match('/&warehouses_documents=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'list';
        }

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        if (!is_array($ids)) {
            Finance_Warehouses_Documents::decryptIdsMultiprint($this->registry, $ids);
            //set to request as decrypted array
            $request->set('items', $ids, 'all', true);
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $finance_warehouses_documents = array();
        if ($ids) {
            $filters = array('where' => array('fwd.id IN (' . implode(', ', $ids) . ')'),
                             'model_lang' => $this->registry->get('lang'),
                             'sanitize' => true);
            $finance_warehouses_documents = Finance_Warehouses_Documents::search($this->registry, $filters);
        }

        //if no models
        if (empty($finance_warehouses_documents) || count($ids) != count($finance_warehouses_documents)) {
            $this->registry['messages']->setError($this->i18n('error_no_finance_warehouses_documents'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, $after_action);
        }

        $type = $finance_warehouses_documents[0]->get('type');
        foreach ($finance_warehouses_documents as $finance_warehouses_document) {
            $type_i = $finance_warehouses_document->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, $after_action);
                return true;
            }
        }

        require_once $this->modelsDir . 'finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.id = ' . $type),
                         'sanitize' => true);
        $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $type_name_plural = $fin_doc_type && $fin_doc_type->get('name_plural') ? $fin_doc_type->get('name_plural') : $this->i18n('finance_warehouses_documents');

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        if (empty($pattern_id)) {
            $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        //get the pattern
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1',
                                          'p.format = "pdf"'),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
            $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        // Check if the models corresponds to the selected pattern
        // Check: company
        $pattern_company = $pattern->get('company');
        if ($pattern_company && !Finance_Warehouses_Documents::checkModelsForCompany($this->registry, Finance_Warehouses_Documents::$modelName, $pattern_company, $ids)) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $company_filters = array('sanitize'   => true,
                                     'model_lang' => $this->registry->get('lang'),
                                     'where'      => array('id = ' . $pattern_company));
            $company = Finance_Companies::searchOne($this->registry, $company_filters);
            $this->registry['messages']->setError($this->i18n('error_finance_print_pattern_companies', array($company->get('name'))));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        $result = Finance_Warehouses_Documents::multiPrint($this->registry, $this);
        if ($result) {
            //the pdf content is displayed in the browser window
            exit;
        } else {
            $this->registry['messages']->setError($this->i18n('error_finance_warehouses_documents_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
        }

        $this->redirect($this->module, $after_action);
    }

    /**
     * Attaches files to warehouse document
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        //$id = $request->get($this->action);
        $id = $request->get('attachments');

        $filters = array('where' => array('fwd.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        $added_files = array(0 => array());

        if ($request->isPost()) {

            $modified_files = array();
            $modified_genfiles = array();

            $erred_modified_files = array();
            $erred_modified_genfiles = array();
            $erred_added_files = array();
            $success_added_files = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //edit existing attachments
            $names        = $request->get('file_names');
            $descriptions = $request->get('file_descriptions');
            $permissions  = $request->get('file_permissions');
            $revisions    = $request->get('file_revisions');
            $filenames    = $request->get('file_filenames');
            $files        = !empty($_FILES['file_paths']) ? $_FILES['file_paths'] : array();
            $indices      = $request->get('file_indices');

            if (!empty($names)) {
                foreach ($names as $idx => $name) {
                    $index = $indices[$idx];

                    if (!empty($files['tmp_name'][$idx])) {
                        $file = array(
                            'name'     => $files['name'][$idx],
                            'type'     => $files['type'][$idx],
                            'tmp_name' => $files['tmp_name'][$idx],
                            'error'    => $files['error'][$idx],
                            'size'     => $files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($names[$idx])) {
                        $names[$idx] = $files['name'][$idx];
                    }
                    $params = array(
                        'id'          => $idx,
                        'name'        => $names[$idx],
                        'filename'    => $filenames[$idx],
                        'description' => $descriptions[$idx],
                        'revision'    => $revisions[$idx],
                        'permission'  => $permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $finance_warehouses_document->sanitize())) {
                        $erred_modified_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_edit') . ' ' . $index, 'edit_attachment_' . $idx);
                    }
                    $finance_warehouses_document->unsanitize();
                    $modified_files[$idx] = $params;
                }
            }

            //edit existing generated files
            $generated_names        = $request->get('g_file_names');
            $generated_descriptions = $request->get('g_file_descriptions');
            $generated_permissions  = $request->get('g_file_permissions');
            $generated_revisions    = $request->get('g_file_revisions');
            $generated_indices      = $request->get('g_file_indices');

            if (!empty($generated_names)) {
                foreach ($generated_names as $idx => $name) {
                    $index = $generated_indices[$idx];

                    $file = array();
                    $params = array(
                        'id'          => $idx,
                        'name'        => $generated_names[$idx],
                        'description' => $generated_descriptions[$idx],
                        'permission'  => $generated_permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $finance_warehouses_document->sanitize())) {
                        $erred_modified_genfiles[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_gen_edit') . ' ' . $index, 'edit_gen_attachment_' . $idx);
                    }
                    $finance_warehouses_document->unsanitize();
                    $modified_genfiles[$idx] = $params;
                }
            }

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            $additional_descriptions = $request->get('a_file_descriptions');
            $additional_permissions  = $request->get('a_file_permissions');
            $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files['name'])) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx] ?? '',
                        'description' => $additional_descriptions[$idx] ?? '',
                        'revision'    => $additional_revisions[$idx] ?? '',
                        'permission'  => $additional_permissions[$idx] ?? 'all');

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $finance_warehouses_document->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }
                    $finance_warehouses_document->unsanitize();
                    $added_files[$idx] = $params;
                }
            }

            if ($modified_files && empty($erred_modified_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'modified_attachments'));*/
            } elseif (!empty($modified_files)) {
                $this->registry['modified_files'] = $modified_files;
                $this->registry['erred_modified_files'] = $erred_modified_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($modified_genfiles && empty($erred_modified_genfiles)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_gen_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'modified_gen'));*/
            } elseif (!empty($modified_genfiles)) {
                $this->registry['modified_genfiles'] = $modified_genfiles;
                $this->registry['erred_modified_genfiles'] = $erred_modified_genfiles;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'add_attachments'));*/
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files) && empty($erred_modified_files) && empty($erred_modified_genfiles)) {
                $this->actionCompleted = true;
                if (!$this->registry->isRegistered('finance_warehouses_document')) {
                    $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
                }

                return true;
            }

        }

        $this->registry['added_files'] = $added_files;

        if (!empty($finance_warehouses_document)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_warehouses_document);

            $finance_warehouses_document->getAttachments();
            $finance_warehouses_document->getGeneratedFiles();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Fetches generated warehouse document file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'generate' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fwd.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if (!empty($finance_warehouses_document)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_warehouses_document);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $filters = array('where' => array('f.id = ' . $request->get('file')),
                             'sanitize' => true);
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $finance_warehouses_document->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $finance_warehouses_document->get('id'));
                    }
                    break;
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_warehouses_document->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_warehouses_document->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_warehouses_document->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_warehouses_document->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'attachments', 'attachments=' . $finance_warehouses_document->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Annul model
     */
    private function _annul() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $matches = array();
        if (isset($_SERVER['HTTP_REFERER'])) {
            //get referer's action
            preg_match('/&warehouses_documents=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $request->set('id', $id, 'all', true);
        $filters = array('where' => array('fwd.id = ' . $request->get('id'), 'fwd.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'));
        $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);

        if (empty($finance_warehouses_document)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        $this->checkAccessOwnership($finance_warehouses_document);

        if ($request->get('description')) {
            $finance_warehouses_document->set('annul_description', $request->get('description'), true);
        }
        $this->old_model = clone $finance_warehouses_document;
        $this->old_model->sanitize();

        if ($finance_warehouses_document->annul()) {
            //show message 'message_finance_annul_success'
            $this->registry['messages']->setMessage($this->i18n('message_finance_annul_success',
                                                    array($finance_warehouses_document->getModelTypeName())), '', -2);

            $this->model = clone $finance_warehouses_document;
            $this->model->sanitize();

            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_finance_annul_failed',
                                                  array($finance_warehouses_document->getModelTypeName())), '', -1);
        }

        //manually set custom after action so that the navigation is redirected to previous action
        $this->registry['messages']->insertInSession($this->registry);

        $url = sprintf(
            '%s?%s=finance&%s=warehouses_documents&warehouses_documents=%s&%s=%d',
            $_SERVER['PHP_SELF'], $this->registry['module_param'],
            $this->registry['controller_param'], $after_action,
            $after_action, $finance_warehouses_document->get('id')
        );
        $this->registry->set('redirect_to_url', $url, true);
        $this->registry->set('exit_after', true, true);

        return true;
    }

    /**
     * Relatives of the model
     */
    private function _relatives() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        if ($id) {
            $filters = array('where' => array('fwd.id = ' . $id));
            if ($model_lang = $this->registry['request']->get('model_lang')) {
                $filters['model_lang'] = $model_lang;
            }
            $finance_warehouses_document = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
            /* if (!empty($finance_warehouses_document)) {
                //$finance_warehouses_document->getRelatives();
                $finance_warehouses_document->getGT2Vars();
            } */
        }
        if (!empty($finance_warehouses_document)) {
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_warehouses_document')) {
                $this->registry->set('finance_warehouses_document', $finance_warehouses_document->sanitize());
            }

            //get the relatives tree
            $this->registry->set('relatives_tree', $finance_warehouses_document->getRelativesTree());
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_warehouses_document'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions();

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        if (isset($actions['add'])) {
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $filters = array('where' => array('fdt.model = "Finance_Warehouses_Document"',
                                              'fdt.id IN (' . implode(',', array(
                                                  PH_FINANCE_TYPE_COMMODITIES_TRANSFER,
                                                  PH_FINANCE_TYPE_COMMODITIES_RESERVATION,
                                                  PH_FINANCE_TYPE_WASTE,
                                                  PH_FINANCE_TYPE_INSPECTION)) .
                                              ')',
                                              'fdt.active=1'),
                             'sort' => array('fdti18n.name ASC'),
                             'sanitize' => true);
            $financeTypes = Finance_Documents_Types::search($this->registry, $filters);

            $types = array();
            foreach ($financeTypes as $type) {
                //check permissions by type
                if ($this->checkActionPermissions($module_check . $type->get('id'), 'add')) {
                    $types[] = array('label' => $type->get('name'), 'option_value' => $type->get('id'));
                }
            }

            if (!empty($types)) {
                $sel_type = $this->registry['request']->get('type') ?: $types[0]['option_value'];

                require_once $this->modelsDir . 'finance.dropdown.php';
                $params = array(0 => $this->registry,
                                'active' => 1);
                $wd_options = Finance_Dropdown::getWarehouseData($params);

                $default_warehouse_data = $this->registry['currentUser']->getDefaultWarehouseData($wd_options);
                $warehouse_data = $this->registry['request']->get('warehouse_data') ?: $default_warehouse_data;
                $to_warehouse_data = $this->registry['request']->get('to_warehouse_data') ?: ($default_warehouse_data != $warehouse_data ? $default_warehouse_data : '');

                $add_options = array (
                    array (
                        'custom_id' => 'type_____',
                        'name' => 'aa2_type',
                        'type' => 'dropdown',
                        'required' => 1,
                        'onchange' => 'toggleCommoditiesTransferAddFields(this, ' . PH_FINANCE_TYPE_COMMODITIES_TRANSFER . ')',
                        'label' => $this->i18n('finance_warehouses_documents_type'),
                        'options' => $types,
                        'value' => $sel_type),
                    array (
                        'custom_id' => 'warehouse_data_____',
                        'name' => 'aa2_warehouse_data',
                        'type' => 'dropdown',
                        'required' => 1,
                        'really_required' => 1,
                        'label' => $this->i18n('finance_warehouses_documents_' . ($sel_type == PH_FINANCE_TYPE_COMMODITIES_TRANSFER ? 'from_' : '') . 'warehouse'),
                        'optgroups' => $wd_options,
                        'value' => $warehouse_data,
                        'custom_class' => (count($wd_options) > 1 && !$warehouse_data) ? 'undefined' : ''),
                    array (
                        'custom_id' => 'to_warehouse_data_____',
                        'name' => 'aa2_to_warehouse_data',
                        'type' => 'dropdown',
                        'required' => 0,
                        'hidden' => ($sel_type == PH_FINANCE_TYPE_COMMODITIES_TRANSFER ? 0 : 1),
                        'label' => $this->i18n('finance_warehouses_documents_to_warehouse'),
                        'optgroups' => $wd_options,
                        'value' => $to_warehouse_data)
                );

                $actions['add']['options'] = $add_options;
            } else {
                unset($actions['add']);
            }
        } else {
            unset($actions['add']);
        }

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once(PH_MODULES_DIR . 'patterns/models/patterns.factory.php');
            $filters = array('where' => array('p.model = \'' . $this->model->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            // filter patterns for handover by direction
            if ($this->model->get('type') == PH_FINANCE_TYPE_HANDOVER) {
                $filters['where'][] = 'p.handover_direction IN ("' . $this->model->get('direction') . '", "both")';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add filter: get patterns only if they are for this company or they are not for a specific company
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            $_options_patterns = array();
            foreach ($patterns as $pattern) {
                $_options_patterns[] = array(
                                            'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                                            'option_value' => $pattern->get('id'));
            }

            if (empty($_options_patterns)) {
                //remove generate action, the document type does not define the types to generate to
                unset($actions['generate']);
            } else {
                //prepare generate options
                $generate_options = array (
                    array (
                        'custom_id' => 'pattern__',
                        'name' => 'aa2_pattern',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('finance_pattern'),
                        'help' => $this->i18n('finance_pattern'),
                        'options' => $_options_patterns,
                        'value' => ($this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : ''));
                $actions['generate']['options'] = $generate_options;
            }
        }

        return $actions;
    }

    /**
     * get batches data from other handovers issued for the current document
     * for specified article and batch
     */
    private function _getExistingBatchData() {
        $request = &$this->registry['request'];
        $params['article'] = $request->get('article');
        $params['batch'] = $request->get('batch');
        $params['model'] = $request->get('model');
        $params['id'] = $request->get($this->action);

        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
        $data = Finance_Warehouses_Documents::getExistingBatchData($this->registry, $params);
        $result = array();
        if (!empty($data)) {
            $result = array_shift($data);
            $result['serial'] = array(array('label' => $result['serial'], 'option_value' => $result['serial']));
            $result['expire_formatted'] = General::strftime('%d.%m.%Y', $result['expire']);
        }
        foreach ($data as $k => $v) {
            $result['serial'][] = array('label' => $v['serial'], 'option_value' => $v['serial']);
        }

        echo json_encode($result);
        exit;
    }

    /**
     * @return string
     * @todo: We're not exactly simulating what Finance_Warehouses_Documents::buildBatchUniqueKey()
     *        it will cost more time to make it work using the same method.
     */
    public function ajaxGetBatchUniqueKeyElementsNames() : string
    {
        echo json_encode(array_values(Finance_Warehouses_Documents::getBatchUniqueKeyElementsNames($this->registry['db'])));
        exit;
    }
}
