{if !empty($issued_records) && is_array($issued_records) && count($issued_records)}
  {strip}
    <table border="1" cellpadding="5" cellspacing="0">
      {foreach from=$issued_records item='record_info' key='k'}
        {if $record_info.ids && $record_info.patterns}
        <tr>
          <th nowrap="nowrap" align="left">{$record_info.type_name|escape}{if $record_info.company} ({$record_info.company_name|escape}){/if} ({$record_info.ids|@count})</th>
        </tr>
        {capture assign='multiprint_url'}{if $record_info.model_name eq 'Finance_Incomes_Reason'}{$incomes_base_url}{else}{$expenses_base_url}{/if}&items={$record_info.ids_enc}&pattern={/capture}
        {assign var='patterns_count' value=$record_info.patterns|@count}
        {foreach from=$record_info.patterns item='pattern_name' key='pattern_id'}
        <tr style="background-color: {cycle name=$k values='#F8F8F8,#ECECEC'};">
          <td align="left"><a href="{$multiprint_url}{$pattern_id}" target="_blank">{#finance_print#|escape}{if $patterns_count gt 1} ({$pattern_name|escape|default:'&nbsp;'}){/if}</a></td>
        </tr>
        {/foreach}
        {/if}
      {/foreach}
    </table>
  {/strip}
{/if}
