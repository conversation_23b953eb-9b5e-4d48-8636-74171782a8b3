<?php
include "LevinsGreenCardClaimTrait.php";
include "LevinsNonpropertyDamageClaimTrait.php";
class Levins_Autocompleters extends Autocompleters
{
    use LevinsGreenCardClaimTrait;
    use LevinsNonpropertyDamageClaimTrait;
    static $registry;
    static $acParams;

    /**
     * Function to execute search for information of green card claim info
     * in Insys (insurance software used by LEVINS)
     *
     * @param object $registry - registry
     * @param array $autocomplete - array containing the settings of the autocompleter
     *
     * @return array - array of models cnotaining the data for the found adresses
     */
    public static function searchInsysClaimInfoGC(&$registry, &$autocomplete)
    {
        self::$registry = $registry;
        self::$acParams = $params = json_decode($autocomplete['plugin']['params'], true);

        $request = &$registry['request'];

        $models = array();

        $test_mode = false;
        if (isset($params['test_mode'])) {
            $test_mode = intval($params['test_mode']);
        }

        if (!$test_mode) {
            $security_key = self::getInsysSecurityKey();
        }
        if (empty($security_key) && !$test_mode) {
            $autocomplete['no_results_error'] = sprintf('<span class="error">%s</span>', $registry['translater']->translate('error_trying_to_initialize_session_insys_service'));
            return array($models);
        }

        $claim_no = $request->get($request->get('field'));

        // execute address search
        if (!$test_mode) {
            $request = [
                'jsonrpc' => '2.0',
                'method' => 'getClaimInfoGC',
                'params' => [
                    'security_key' => $security_key,
                    'claim_no' => $claim_no
                ],
                'id' => 1
            ];
            $response = self::_requestInsys($params['insys_url'], 'POST', json_encode($request));
        } else {
            $response = '{
                "jsonrpc": "2.0",
                "result": {
                    "CLAIM_NO": "' . $claim_no . '",
                    "CLAIM_INFO": {
                        "DB_REF_NO_CLAIM": "' . $claim_no . '",
                        "DB_REF_NO_CORR": "01DEMTPLGC9875202011",
                        "DB_CORR_NAME": "BAUEN DOO",
                        "DB_EVENT_DATE": "2019-09-08",
                        "DB_EVENT_COUNTRY": "SE",
                        "DB_EVENT_DESCRIPTION": "уврежда врата",
                        "DB_POLICY_NO": "BG\/22\/118002961121",
                        "DB_REG_NO": "E3491MT",
                        "DB_MAKE_MODEL": "MAN - TGX",
                        "DB_OWNER_INFO": "УНИКРЕДИТ ЛИЗИНГ ЕАД (121887948)",
                        "DB_POLICY_COVERED": "14-10-2019"
                    }
                },
                "id": 1
            }';
        }

        $response = json_decode($response, true);
        $data = isset($response['result']) ? $response['result']['CLAIM_INFO'] : [];

        if (!empty($data)) {
            $model = new Model($registry, array());

            $model->set('full_num', $data['DB_REF_NO_CLAIM'], true);
            $model->set('name', $data['DB_REF_NO_CLAIM'], true);
            $model->set('custom_num', $data['DB_REF_NO_CORR'], true);
            //the date is in format DD-MM-YYYY
            $date = preg_replace('#(\d{2})-(\d{2})-(\d{4})#', '$3-$2-$1', $data['DB_EVENT_DATE']);
            $model->set('date', $date, true);
            $model->set('description_insys', $data['DB_EVENT_DESCRIPTION'], true);
            $model->set('shelf_num', $data['DB_POLICY_NO'], true);
            $model->set('num_car_our', $data['DB_REG_NO'], true);
            $model->set('mark_car', $data['DB_MAKE_MODEL'], true);
            $model->set('owner_car', $data['DB_OWNER_INFO'], true);
            //the date is in format DD-MM-YYYY
            $date_policy = preg_replace('#(\d{2})-(\d{2})-(\d{4})#', '$3-$2-$1', $data['DB_POLICY_COVERED']);
            $model->set('shelf_prescription', $date_policy, true);
            $caused_damage_customer = Customers::searchOne(self::$registry, array(
                'where' => array(
                    'TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) = "' . General::slashesEscape($data['DB_CORR_NAME']) . '"'
                ),
                'sort' => ['c.id ASC']
            ));
            if ($caused_damage_customer) {
                $model->set('caused_damage', $caused_damage_customer->get('full_name'), true);
                $model->set('caused_damage_id', $caused_damage_customer->get('id'), true);
            }

            $country = Nomenclatures::searchOne(self::$registry, array(
                'where' => array(
                    'n.code = "' . General::slashesEscape($data['DB_EVENT_COUNTRY']) . '"'
                )
            ));
            if ($country) {
                $model->set('country_name', $country->get('name'), true);
                $model->set('country_id', $country->get('id'), true);
            }

            $models[] = $model->sanitize();
        }

        return array($models);
    }


    /**
     * Function to execute search for information of non-property damage claim info
     * in Insys (insurance software used by LEVINS)
     *
     * @param object $registry - registry
     * @param array $autocomplete - array containing the settings of the autocompleter
     *
     * @return array - array of models cnotaining the data for the found adresses
     */
    public static function searchInsysClaimInfoND(&$registry, &$autocomplete)
    {
        self::$registry = $registry;
        self::$acParams = $params = json_decode($autocomplete['plugin']['params'], true);
        self::_loginInsysND();
        $request = &$registry['request'];

        $models = array();

        $claim_no = $request->get($request->get('field'));
        $response = self::_requestInsysND($params['insys_url'] . "?in_claim_regid=" . $claim_no);
        $response = json_decode($response, true);
        $data = isset($response['items']) ? $response['items'] : [];
        if (!empty($data)) {
            foreach ($data as $item) {
                $model = new Model($registry, array());
                $model->set('name', $item['ref_no_claim'], true);
                $model->set('full_num', $item['ref_no_claim'], true);
                $model->set('ref_no_claim', $item['ref_no_claim'], true);
                $date = new DateTime($data[0]['event_date']);
                $model->set('event_day', $date->format('Y-m-d'), true);
                $date = new DateTime($data[0]['claim_reg_date']);
                $model->set('claim_reg_moment', $date->format('Y-m-d H:i:s'), true);
                $injuredCustomer = Customers::searchOne(self::$registry, array(
                    'where' => array(
                        'c.id = 265915'
                    ),
                    'sort' => ['c.id ASC']
                ));
                if (!empty($item['injured_name'])) {
                    $customer = Customers::searchOne(self::$registry, array(
                        'where' => array(
                            'TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) = "' . General::slashesEscape($item['injured_name']) . '"'
                        ),
                        'sort' => ['c.id ASC']
                    ));
                    if ($customer) {
                        $injuredCustomer = $customer;
                    }
                }
                $model->set('customer_name', $injuredCustomer->get('full_name'), true);
                $model->set('customer', $injuredCustomer->get('id'), true);
                $model->set('state_name', $item['state_name'], true);
                if (!empty($item['state_name'])) {
                    $district = Nomenclatures::searchOne(self::$registry, array(
                        'where' => array(
                            'n.type = 12',
                            'ni18n.name LIKE "%' . General::slashesEscape($item['state_name']) . '"'
                        ),
                        'sort' => ['n.id ASC']
                    ));
                    if ($district) {
                        $model->set('state_id', $district->get('id'), true);
                        $model->set('state_name', $district->get('name'), true);
                    } else {
                        $model->set('state_name', "", true);
                    }
                }
                $model->set('city_name', $item['city_name'], true);
                if (!empty($item['city_name'])) {
                    $city = Nomenclatures::searchOne(self::$registry, array(
                        'where' => array(
                            'n.type = 8',
                            'ni18n.name LIKE "%' . General::slashesEscape($item['city_name']) . '"'
                        ),
                        'sort' => ['n.id ASC']
                    ));
                    if ($city) {
                        $model->set('city_id', $city->get('id'), true);
                        $model->set('city_name', $city->get('name'), true);
                    } else {
                        $model->set('city_name', "", true);
                    }
                }
                $model->set('policy_no', $item['policy_no'], true);
                $model->set('owner_info', $item['owner_info'], true);
                $model->set('guilty_reg_no', $item['guilty_reg_no'], true);
                $model->set('guilty_make', $item['guilty_make'], true);
                $model->set('guilty_model', $item['guilty_model'], true);
                $model->set('last_reserv_amnt', $item['last_reserv_amnt'], true);
                $model->set('inheritor_name', $item['inheritor_name'], true);
                $model->set('inheritor_code', $item['inheritor_code'], true);
                $model->set('event_description', $item['event_description'], true);
                $model->set('inheritor_code', $item['inheritor_code'], true);
                $models[] = $model->sanitize();

            }
        }

        return array($models);
    }


}

?>
