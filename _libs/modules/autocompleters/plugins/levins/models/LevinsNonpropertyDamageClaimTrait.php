<?php

trait LevinsNonpropertyDamageClaimTrait
{
    private static string $oauthToken;

    /**
     * Retrieve the Oauth Bearer Token
     * @return bool|string
     */
    private static function _getOauthTokenInsysND()
    {
        if (empty(self::$oauthToken)) {
            $response = json_decode(self::_loginInsysND(), true);
            self::$oauthToken = $response['access_token'];
        }
        return self::$oauthToken;
    }

    /**
     *
     * Login into the insys non-property damage API.
     * @return bool|string result of the authentication
     */
    private static function _loginInsysND()
    {
        $params = self::$acParams;
        $request = http_build_query([
            'grant_type' => 'client_credentials',
        ]);
        $ch = curl_init($params['insys_auth_url']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Content-Type: application/x-www-form-urlencoded',
            'Content-Length: ' . strlen($request),
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $request);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERNAME, $params['insys_auth_user']);
        curl_setopt($ch, CURLOPT_PASSWORD, General::decrypt($params['insys_auth_pass'], $params['insys_auth_user'], 'xtea'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $error_number = curl_errno($ch);
        curl_close($ch);
        if ($error_number) {
            $response = '{"errors": ' . $error_number . ':' . $error . '}';
        }

        $log_message = "Insys Endpoint:\n{$params['insys_auth_url']}\nAction: Login\n\nInsys Request:\n {$request}\n\nInsys Response:\n {$response}";
        General::log(self::$registry, __METHOD__, $log_message);

        return $response;
    }

    /**
     * Request Insys for the Non-property damage API and return the response
     *
     * @param string $url - address of the API
     * @param string $action - REST method: GET, POST, PUT, DELETE
     * @param string $json - the request string in JSON
     * @param string $content_type - content type of the header
     * @return string $response - the response string in JSON
     */
    private static function _requestInsysND($url, $action = 'GET', $json = '', $content_type = 'application/json')
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Content-Type: ' . $content_type,
            'Content-Length: ' . ($content_type == 'application/json' ? mb_strlen($json) : strlen($json)),
            'Authorization: Bearer ' . self::_getOauthTokenInsysND(),
        ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $action);
        if ($json) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);

        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $error_number = curl_errno($ch);
        curl_close($ch);

        if ($error_number) {
            $response = '{"errors": ' . $error_number . ':' . $error . '}';
        }

        $log_message = "Insys Endpoint:\n{$url}\nAction: {$action}\n\nInsys Request:\n {$json}\n\nInsys Response:\n {$response}";
        General::log(self::$registry, __METHOD__, $log_message);

        return $response;
    }
}
