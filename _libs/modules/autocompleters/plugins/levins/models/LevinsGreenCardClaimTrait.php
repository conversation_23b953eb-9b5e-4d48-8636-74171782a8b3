<?php

trait Levin<PERSON>GreenCardClaimTrait
{
    /**
     * Login to Insys and get security key
     *
     * @param object $registry - registry
     * @param array $autocomplete - array containing the settings of the autocompleter
     *
     * @return array - array of models cnotaining the data for the found adresses
     */
    private static function getInsysSecurityKey()
    {

        $session_key = '';
        $insys_data = self::loginInsys();
        if (!empty($insys_data['SECURITY_KEY'])) {
            $session_key = $insys_data['SECURITY_KEY'];
        }

        return $session_key;
    }

    /**
     * Login to Insys and get security key
     *
     * @param array $response - Insys data
     *
     * @return mixed - false or the security key
     */
    private static function setInsysSecurityKey($data)
    {
        $result = false;
        if (!empty($data['SECURITY_KEY'])) {
            $cookie = &self::$registry['cookie'];
            $cookie->set('session_key', $data['SECURITY_KEY'], 'insys_data');
            $cookie->set('expires', $data['EXPIRE_DATETIME'], 'insys_data');

            $result = $data['SECURITY_KEY'];
        }

        return $result;
    }

    /**
     * Login to Insys
     *
     * @return array $data - response result of Insys login
     */
    private static function loginInsys()
    {
        $params = self::$acParams;

        $request = [
            'jsonrpc' => '2.0',
            'method' => 'logIn',
            'params' => [
                'user_name' => $params['insys_user'],
                'password' => General::decrypt($params['insys_pass'], $params['insys_user'], 'xtea')
            ],
            'id' => 1
        ];
        $response = self::_requestInsys($params['insys_url'], 'POST', json_encode($request));

        if (!empty($response)) {
            $response = json_decode($response, true);
        }

        $data = array();
        if (isset($response['result'])) {
            $data = $response['result'];
        }

        return $data;
    }

    /**
     * Request Insys and return the response
     *
     * @param string $url - address of the API
     * @param string $action - REST method: GET, POST, PUT, DELETE
     * @param string $json - the request string in JSON
     * @param string $content_type - content type of the header
     * @return string $response - the response string in JSON
     */
    private static function _requestInsys($url, $action = 'GET', $json = '', $content_type = 'application/json')
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Content-Type: ' . $content_type,
            'Content-Length: ' . ($content_type == 'application/json' ? mb_strlen($json) : strlen($json)),
        ));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $action);
        if ($json) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $error_number = curl_errno($ch);
        curl_close($ch);

        if ($error_number) {
            $response = '{"errors": ' . $error_number . ':' . $error . '}';
        }

        $log_message = "Insys Endpoint:\n{$url}\nAction: {$action}\n\nInsys Request:\n {$json}\n\nInsys Response:\n {$response}";
        General::log(self::$registry, __METHOD__, $log_message);

        return $response;
    }

    /**
     * Function to search (and add if the quarter or the address does not exists in the DB) the nomenclatures for the addresses
     *
     * @param object $registry - registry
     * @param array $autocomplete - array containing the settings of the autocompleter
     * @param array $data - the processed data which have to be completed with the settings if needed
     *
     * @return array - processed data with the new options
     */
    public static function searchAddressElementsNomenclatures(&$registry, &$autocomplete, &$data)
    {
        $address_id_var = array('name' => '', 'value' => '');
        $address_name_var = array('name' => '', 'value' => '');
        $city_id_var = array('name' => '', 'value' => '');
        $city_name_var = array('name' => '', 'value' => '');
        $quarter_id_var = array('name' => '', 'value' => '');
        $quarter_name_var = array('name' => '', 'value' => '');
        $district_id_var = array('name' => '', 'value' => '');
        $district_name_var = array('name' => '', 'value' => '');

        $plugin_params = $autocomplete['plugin_params'];
        $address_fields = preg_split('#\s*,\s*#', $plugin_params['address_fields']);
        $address_fields_id = preg_split('#\s*,\s*#', $plugin_params['address_fields_id']);
        $city_fields = preg_split('#\s*,\s*#', $plugin_params['city_fields']);
        $city_fields_id = preg_split('#\s*,\s*#', $plugin_params['city_fields_id']);
        $quarter_fields = preg_split('#\s*,\s*#', $plugin_params['quarter_fields']);
        $quarter_fields_id = preg_split('#\s*,\s*#', $plugin_params['quarter_fields_id']);
        $district_id = preg_split('#\s*,\s*#', $plugin_params['quarter_fields']);
        $district_name = preg_split('#\s*,\s*#', $plugin_params['quarter_fields_id']);

        foreach ($data as $key => $value) {
            if (preg_match('#^\$#', $key)) {
                $current_var = preg_replace('#^\$#', '', $key);
                if (in_array($current_var, $address_fields)) {
                    $address_name_var['name'] = $current_var;
                    $address_name_var['value'] = $value;
                } elseif (in_array($current_var, $address_fields_id)) {
                    $address_id_var['name'] = $current_var;
                    $address_id_var['value'] = $value;
                } elseif (in_array($current_var, $city_fields)) {
                    $city_name_var['name'] = $current_var;
                    $city_name_var['value'] = $value;
                } elseif (in_array($current_var, $city_fields_id)) {
                    $city_id_var['name'] = $current_var;
                    $city_id_var['value'] = $value;
                } elseif (in_array($current_var, $quarter_fields)) {
                    $quarter_name_var['name'] = $current_var;
                    $quarter_name_var['value'] = $value;
                } elseif (in_array($current_var, $quarter_fields_id)) {
                    $quarter_id_var['name'] = $current_var;
                    $quarter_id_var['value'] = $value;
                } elseif ($current_var == 'district') {
                    $district_id_var['name'] = $current_var;
                    $district_id_var['value'] = $value;
                } elseif ($current_var == 'district_name') {
                    $district_name_var['name'] = $current_var;
                    $district_name_var['value'] = $value;
                }
            }
        }

        // get the needed vars additional vars for nomenclatures
        $sql_for_document_add_vars = 'SELECT fm.id, fm.name, fm.model_type FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Nomenclature" AND' . "\n" .
            '((fm.name LIKE "' . $plugin_params['quarter_city_var'] . '" AND fm.model_type="' . $plugin_params['quarter_nomenclature_id'] . '") OR ' . "\n" .
            ' (fm.name LIKE "' . $plugin_params['quarter_district_var'] . '" AND fm.model_type="' . $plugin_params['quarter_nomenclature_id'] . '") OR ' . "\n" .
            ' (fm.name LIKE "' . $plugin_params['district_city_var'] . '" AND fm.model_type="' . $plugin_params['district_nomenclature_id'] . '") OR ' . "\n" .
            ' (fm.name LIKE "' . $plugin_params['address_city_var'] . '" AND fm.model_type="' . $plugin_params['address_nomenclature_id'] . '") OR ' . "\n" .
            ' (fm.name LIKE "' . $plugin_params['address_quarter_var'] . '" AND fm.model_type="' . $plugin_params['address_nomenclature_id'] . '"))' . "\n";
        $nomeclatures_additional_vars = $registry['db']->GetAll($sql_for_document_add_vars);

        $nom_quarter_city_var_id = '';
        $nom_district_city_var_id = '';
        $nom_district_quarter_var_id = '';
        $nom_address_city_var_id = '';
        $nom_address_quarter_var_id = '';
        foreach ($nomeclatures_additional_vars as $nom_add_var) {
            if ($nom_add_var['name'] == $plugin_params['quarter_city_var'] && $nom_add_var['model_type'] == $plugin_params['quarter_nomenclature_id']) {
                $nom_quarter_city_var_id = $nom_add_var['id'];
            } elseif ($nom_add_var['name'] == $plugin_params['quarter_district_var'] && $nom_add_var['model_type'] == $plugin_params['quarter_nomenclature_id']) {
                $nom_district_quarter_var_id = $nom_add_var['id'];
            } elseif ($nom_add_var['name'] == $plugin_params['district_city_var'] && $nom_add_var['model_type'] == $plugin_params['district_nomenclature_id']) {
                $nom_district_city_var_id = $nom_add_var['id'];
            } elseif ($nom_add_var['name'] == $plugin_params['address_city_var'] && $nom_add_var['model_type'] == $plugin_params['address_nomenclature_id']) {
                $nom_address_city_var_id = $nom_add_var['id'];
            } elseif ($nom_add_var['name'] == $plugin_params['address_quarter_var'] && $nom_add_var['model_type'] == $plugin_params['address_nomenclature_id']) {
                $nom_address_quarter_var_id = $nom_add_var['id'];
            }
        }

        // include needed classes for nomenclatures
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.audit.php';

        if (!empty($district_name_var['name']) && !empty($district_name_var['value'])) {
            // DISTRICT NOMENCLATURE
            // check for existing district
            $sql_district_exists = 'SELECT `id` FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                '  ON (nomi18n.parent_id=nom.id AND nomi18n.lang="' . $registry['lang'] . '")' . "\n" .
                'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm' . "\n" .
                '  ON (nom_cstm.model_id=nom.id AND nom_cstm.var_id="' . $nom_district_city_var_id . '" AND nom_cstm.value="' . $city_id_var['value'] . '")' . "\n" .
                'WHERE nom.type="' . $plugin_params['district_nomenclature_id'] . '" AND nomi18n.name="' . $district_name_var['value'] . '" AND nom.active=1 AND nom.deleted_by=0' . "\n";
            $district_id_var['value'] = $registry['db']->GetOne($sql_district_exists);

            if (empty($district_id_var['value'])) {
                // the nomenclature does not exists in the DB so it has to be added
                $nom = new Nomenclature($registry);
                $nom = Nomenclatures::buildModel($registry);
                $nom->set('type', $plugin_params['district_nomenclature_id'], true);
                $registry->set('get_old_vars', true, true);
                $nom->getVars();
                $registry->set('get_old_vars', false, true);
                $old_nomenclature = clone $nom;

                $nom->set('plain_vars', null, true);
                $vars = $nom->get('vars');
                foreach ($vars as $idx => $var) {
                    if ($var['name'] == $plugin_params['district_city_var']) {
                        $vars[$idx]['value'] = $city_id_var['value'];
                    } elseif ($var['name'] == $plugin_params['district_city_name_var']) {
                        $vars[$idx]['value'] = $city_name_var['value'];
                    }
                }
                $nom->set('vars', $vars, true);
                $nom->set('group', 1, true);
                $nom->set('active', 1, true);
                $nom->set('name', $district_name_var['value'], true);
                $nom->set('code', '', true);

                // save the nomenclature
                if ($nom->save()) {
                    $filters = array('where' => array('n.id = ' . $nom->get('id')),
                        'model_lang' => $nom->get('model_lang'));
                    $new_nomenclature = Nomenclatures::searchOne($registry, $filters);
                    $registry->set('get_old_vars', true, true);
                    $new_nomenclature->getVars();
                    $registry->set('get_old_vars', false, true);
                    $district_id_var['value'] = $nom->get('id');

                    Nomenclatures_History::saveData($registry, array('model' => $nom, 'action_type' => 'add', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));
                }
            }
        }

        if (!empty($quarter_name_var['name']) && !empty($quarter_name_var['value'])) {
            // QUARTER NOMENCLATURE
            // check for existing quarter
            $sql_quarter_exists = 'SELECT `id` FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                '  ON (nomi18n.parent_id=nom.id AND nomi18n.lang="' . $registry['lang'] . '")' . "\n" .
                'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm' . "\n" .
                '  ON (nom_cstm.model_id=nom.id AND nom_cstm.var_id="' . $nom_quarter_city_var_id . '" AND nom_cstm.value="' . $city_id_var['value'] . '")' . "\n" .
                'WHERE nom.type="' . $plugin_params['quarter_nomenclature_id'] . '" AND nomi18n.name="' . $quarter_name_var['value'] . '" AND nom.active=1 AND nom.deleted_by=0' . "\n" .
                'ORDER BY `id` ASC' . "\n";
            $quarter_id_var['value'] = $registry['db']->GetOne($sql_quarter_exists);

            if (empty($quarter_id_var['value'])) {
                // the nomenclature does not exists in the DB so it has to be added
                $nom = new Nomenclature($registry);
                $nom = Nomenclatures::buildModel($registry);
                $nom->set('type', $plugin_params['quarter_nomenclature_id'], true);
                $registry->set('get_old_vars', true, true);
                $nom->getVars();
                $registry->set('get_old_vars', false, true);
                $old_nomenclature = clone $nom;

                $nom->set('plain_vars', null, true);
                $vars = $nom->get('vars');
                foreach ($vars as $idx => $var) {
                    if ($var['name'] == $plugin_params['quarter_city_var']) {
                        $vars[$idx]['value'] = $city_id_var['value'];
                    } elseif ($var['name'] == $plugin_params['quarter_city_name_var']) {
                        $vars[$idx]['value'] = $city_name_var['value'];
                    } elseif ($var['name'] == $plugin_params['district_city_var']) {
                        $vars[$idx]['value'] = $district_id_var['value'];
                    } elseif ($var['name'] == $plugin_params['district_city_name_var']) {
                        $vars[$idx]['value'] = $district_name_var['value'];
                    }
                }
                $nom->set('vars', $vars, true);
                $nom->set('group', 1, true);
                $nom->set('active', 1, true);
                $nom->set('name', $quarter_name_var['value'], true);
                $nom->set('code', '', true);

                // save the nomenclature
                if ($nom->save()) {
                    $filters = array('where' => array('n.id = ' . $nom->get('id')),
                        'model_lang' => $nom->get('model_lang'));
                    $new_nomenclature = Nomenclatures::searchOne($registry, $filters);
                    $registry->set('get_old_vars', true, true);
                    $new_nomenclature->getVars();
                    $registry->set('get_old_vars', false, true);
                    $quarter_id_var['value'] = $nom->get('id');

                    Nomenclatures_History::saveData($registry, array('model' => $nom, 'action_type' => 'add', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));
                }
            } elseif (!empty($district_id_var['value'])) {
                // check the quarter's district
                $sql_district = 'SELECT nom_cstm.value FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                    'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm' . "\n" .
                    '  ON (nom_cstm.model_id=nom.id AND nom_cstm.var_id="' . $nom_district_quarter_var_id . '")' . "\n" .
                    'WHERE nom.id="' . $quarter_id_var['value'] . '"' . "\n";
                $selected_district = $registry['db']->GetOne($sql_district);

                if ($selected_district != $district_id_var['value']) {
                    // get the district and update it
                    $filters = array('where' => array('n.id = ' . $quarter_id_var['value']),
                        'model_lang' => $registry['lang']);
                    $selected_quarter = Nomenclatures::searchOne($registry, $filters);

                    $registry->set('get_old_vars', true, true);
                    $selected_quarter->getVars();
                    $registry->set('get_old_vars', false, true);
                    $old_quarter = clone $selected_quarter;

                    $selected_quarter->set('plain_vars', null, true);
                    $vars = $selected_quarter->get('vars');
                    foreach ($vars as $idx => $var) {
                        if ($var['name'] == $plugin_params['quarter_district_var']) {
                            $vars[$idx]['value'] = $district_id_var['value'];
                        } elseif ($var['name'] == $plugin_params['quarter_district_name_var']) {
                            $vars[$idx]['value'] = $district_name_var['value'];
                        }
                    }
                    $selected_quarter->set('vars', $vars, true);

                    if ($selected_quarter->save()) {
                        $filters = array('where' => array('n.id = ' . $selected_quarter->get('id')),
                            'model_lang' => $selected_quarter->get('model_lang'));
                        $new_quarter = Nomenclatures::searchOne($registry, $filters);
                        $registry->set('get_old_vars', true, true);
                        $new_quarter->getVars();
                        $registry->set('get_old_vars', false, true);

                        Nomenclatures_History::saveData($registry, array('model' => $selected_quarter, 'action_type' => 'edit', 'new_model' => $new_quarter, 'old_model' => $old_quarter));
                    }
                }
            }
        }

        if (!empty($address_name_var['name']) && !empty($address_name_var['value'])) {
            // ADDRESS NOMENCLATURE
            // check for existing address
            $sql_address_exists = 'SELECT `id` FROM ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                '  ON (nomi18n.parent_id=nom.id AND nomi18n.lang="' . $registry['lang'] . '")' . "\n" .
                'INNER JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm_city' . "\n" .
                '  ON (nom_cstm_city.model_id=nom.id AND nom_cstm_city.var_id="' . $nom_address_city_var_id . '" AND nom_cstm_city.value="' . $city_id_var['value'] . '")' . "\n" .
                'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm_quarter' . "\n" .
                '  ON (nom_cstm_quarter.model_id=nom.id AND nom_cstm_quarter.var_id="' . $nom_address_quarter_var_id . '")' . "\n" .
                'WHERE nom.type="' . $plugin_params['address_nomenclature_id'] . '" AND nomi18n.name="' . $address_name_var['value'] . '" AND nom.active=1 AND nom.deleted_by=0 AND ' . (!empty($quarter_id_var['value']) ? 'nom_cstm_quarter.value="' . $quarter_id_var['value'] . '"' : ('(nom_cstm_quarter.value="" OR nom_cstm_quarter.value IS NULL)')) . "\n";
            $address_id_var['value'] = $registry['db']->GetOne($sql_address_exists);

            if (empty($address_id_var['value'])) {
                // the nomenclature does not exists in the DB so it has to be added
                $nom = new Nomenclature($registry);
                $nom = Nomenclatures::buildModel($registry);
                $nom->set('type', $plugin_params['address_nomenclature_id'], true);
                $registry->set('get_old_vars', true, true);
                $nom->getVars();
                $registry->set('get_old_vars', false, true);
                $old_nomenclature = clone $nom;

                $nom->set('plain_vars', null, true);
                $vars = $nom->get('vars');
                foreach ($vars as $idx => $var) {
                    if ($var['name'] == $plugin_params['address_city_var']) {
                        $vars[$idx]['value'] = $city_id_var['value'];
                    } elseif ($var['name'] == $plugin_params['address_city_name_var']) {
                        $vars[$idx]['value'] = $city_name_var['value'];
                    } elseif ($var['name'] == $plugin_params['address_quarter_var']) {
                        $vars[$idx]['value'] = $quarter_id_var['value'];
                    }
                }
                $nom->set('vars', $vars, true);
                $nom->set('group', 1, true);
                $nom->set('active', 1, true);
                $nom->set('name', $address_name_var['value'], true);
                $nom->set('code', '', true);

                // save the nomenclature
                if ($nom->save()) {
                    $filters = array('where' => array('n.id = ' . $nom->get('id')),
                        'model_lang' => $nom->get('model_lang'));
                    $new_nomenclature = Nomenclatures::searchOne($registry, $filters);
                    $registry->set('get_old_vars', true, true);
                    $new_nomenclature->getVars();
                    $registry->set('get_old_vars', false, true);
                    $address_id_var['value'] = $nom->get('id');

                    Nomenclatures_History::saveData($registry, array('model' => $nom, 'action_type' => 'add', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));
                }
            }
        }

        // complete the data with the found results
        $data['$' . $address_id_var['name']] = $address_id_var['value'];
        if (!empty($quarter_id_var['name'])) {
            $data['$' . $quarter_id_var['name']] = $quarter_id_var['value'];
        }

        echo(json_encode($data));
        exit;
    }
}
