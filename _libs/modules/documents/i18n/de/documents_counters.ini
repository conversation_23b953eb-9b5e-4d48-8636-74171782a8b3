documents_counters = Dokumentenzähler
documents_counters_name = Name
documents_counters_formula = Formel
documents_counters_description = Beschreibung
documents_counters_next_number = Nächste Nummer
documents_counters_count_documents = Anzahl der Dokumente
documents_counters_types_used = verwendet in Typen von Dokumenten
documents_counters_status = Status
documents_counters_status_active = Aktiv
documents_counters_status_inactive = Nicht aktiv
documents_counters_added_by = Hinzugefügt von
documents_counters_modified_by = Geändert von
documents_counters_added = Hinzugefügt am
documents_counters_modified = Geändert am

documents_counters_add = Dokumentenzähler hinzufügen
documents_counters_edit = Dokumentenzähler bearbeiten
documents_counters_view = Dokumentenzähler einsehen
documents_counters_translate = Dokumentenzähler übersetzen

message_documents_counters_add_success = Die Daten für den Zähler wurden erfolgreich hinzugefügt!
message_documents_counters_edit_success = Die Daten für den Zähler wurden erfolgreich editiert!
message_documents_counters_translate_success = Der Zähler wurde erfolgreich übersetzt!

error_documents_counters_edit_failed = Die Daten für den Zähler wurden nicht erfolgreich bearbeitet
error_documents_counters_add_failed = Die Daten für den Zähler wurden nicht hinzugefügt.
error_documents_counters_translate_failed = Der Zähler wurde nicht erfolgreich übersetzt:

error_no_counter_name_specified = Sie haben keinen Namen hinzugefügt!
error_no_counter_formula_specified = Sie haben keine Formel hinzugefügt!
error_invalid_next_number = Bitte nächste Nummer für den Zähler, bestehend nur aus Ziffern und mit einem Wert größer als Nulle ingeben!
error_documents_counter_mutex_num = Die Zählerformel kann nur eins der Elemente [document_num] und [customer_num] enthalten!

error_no_types_used = in keinem Dokumententyp verwendet

error_no_medial_number_index_specified = Sie haben keinen Index für die Zwischendokumente eingegeben!
error_missing_index_delimiter = Sie haben kein Trennzeichen für Zwischendokumente eingegeben!
error_no_selected_medial_number_index_position = Sie haben keine Position für den Zwischenindex gewählt!

documents_counters_medial_number_index = Index für Zwischennummer
documents_counters_medial_number_delimiter = Trennzeichen für Zwischennummer
documents_counters_medial_number_index_position = Position des Indexes für die Zwischennummer
documents_counters_medial_number_index_number = Zahlen (0-9)
documents_counters_medial_number_index_latin_capital_letters = lateinische Großbuchstaben (A-Z)
documents_counters_medial_number_index_latin_small_letters = lateinische Kleinbuchstaben (a-z)
documents_counters_medial_number_index_cyrilic_capital_letters = kyrillische Großbuchstaben (А-Я)
documents_counters_medial_number_index_cyrilic_small_letters = kyrillische Kleinbuchstaben (а-я)
documents_counters_medial_number_index_position_prefix = Präfix
documents_counters_medial_number_index_position_suffix = Suffix

documents_counters_formula_delimiter = Trennzeichen
documents_counters_empty_delimiter = ohne Trennzeichen
documents_counters_formula_leading_zeroes = Anzahl der führenden Nullen
documents_counters_formula_date_format = Format
documents_counters_formula_date_delimiter = mit Trennzeichen

documents_counters_formula_date_format_year = JJJJ
documents_counters_formula_date_format_year_short = JJ
documents_counters_formula_date_format_month = MM
documents_counters_formula_date_format_day = TT

documents_counters_formula_date_format1 = JJJJ
documents_counters_formula_date_format2 = MM/JJJJ
documents_counters_formula_date_format3 = MM/JJ
documents_counters_formula_date_format4 = JJJJ/MM
documents_counters_formula_date_format5 = JJ/MM
documents_counters_formula_date_format6 = TT/MM/JJJJ
documents_counters_formula_date_format7 = TT/MM/JJ
documents_counters_formula_date_format8 = MM/TT/JJJJ
documents_counters_formula_date_format9 = MM/TT/JJ
documents_counters_formula_date_format10 = JJJJ/TT/MM
documents_counters_formula_date_format11 = JJ/TT/MM
documents_counters_formula_date_format12 = JJJJ/MM/TT
documents_counters_formula_date_format13 = JJ/MM/TT
documents_counters_formula_date_format14 = JJ
documents_counters_formula_date_format15 = JJJ/MM

documents_counters_formula_legend = Legende für das Ausfüllen der Zählerformel
documents_counters_formula_prefix = Präfix
documents_counters_formula_document_num = Dokumentnummer
documents_counters_formula_customer_code = Kode des Vertragspartners
documents_counters_formula_project_code = Kode des Projekts
documents_counters_formula_office_code = Kode vom Büro
documents_counters_formula_user_code = Kode des Benutzers
documents_counters_formula_document_type_code = Kode des Dokumenttyps
documents_counters_formula_trademark_code = Entitätscode
documents_counters_formula_transform_subnum = Laufende Nummer der Transformation
documents_counters_formula_parent_doc_num = Nummer des Elterndokuments (für transformierte Dokumente)
documents_counters_formula_customer_num = Nummer des Dokuments zum Vertragspartner
documents_counters_formula_customer_year = Nur für das laufende Jahr
documents_counters_formula_document_added = Datum zum Hinzufügen des Dokuments
documents_counters_formula_office_num = Dokumentennummer pro Büro
documents_counters_formula_office_year =  Nur für das laufende Jahr
documents_counters_formula_trademark_num = Dokumentnummer pro Entität
documents_counters_formula_trademark_year = Nur für das laufende Jahr

documents_counters_formula_prefix_descr = direkt mit 2-3 Buchstaben ausfüllen, so z.B OFF für Angebote.
documents_counters_formula_document_num_descr = füllt die laufende Nummer eines Dokuments aus.
documents_counters_formula_customer_code_descr = füllt den Kode des für dieses Dokument gewählten Vertragspartners aus.
documents_counters_formula_project_code_descr = füllt den Kode des für dieses Dokument gewählten Projekts aus.
documents_counters_formula_office_code_descr = füllt den Kode des für dieses Dokument gewählten Büros aus.
documents_counters_formula_user_code_descr = füllt den Kode des Benutzers aus, der das Dokument erstellt hatte.
documents_counters_formula_document_type_code_descr = füllt den Kode für den Dokumententyp aus.
documents_counters_formula_trademark_code_descr = füllt den Code der im Dokument ausgewählten Entität aus.
documents_counters_formula_transform_subnum_descr = .01.01 usw. bei Transformation aus einem primären/sekundären Dokument zu einem sekundären Dokument.
documents_counters_formula_parent_doc_num_descr = füllt die Nummer des Elterndokuments (für transformierte Dokumente) aus.
documents_counters_formula_customer_num_descr = füllt laufende Nummer des Dokuments zum Vertragspartner aus. Die Nummer kann in Bezug auf die innerhalb des laufenden Jahres hinzugefügten oder in Beuzg auf alle Dokumente vergeben werden.
documents_counters_formula_office_num_descr = füllt die nächste Dokumentennummer für das Büro aus. Die Nummer kann nur für das aktuelle Jahr oder für alle Dokumente angegeben werden.
documents_counters_formula_trademark_num_descr = füllt die nächste Dokumentennummer für die Entität aus. Die Nummer kann nur für das aktuelle Jahr oder für alle Dokumente angegeben werden.
documents_counters_formula_document_added_descr = Datum des Hinzufügens des Dokuments.

documents_counters_formula_note = <strong>HINSWEIS:</strong> <strong>Nur 5 Elemente</strong> können zur Zählerformel hinzugefügt werden

#Help SECTION for label info

help_documents_counters_name =
help_documents_counters_formula =
help_documents_counters_count_documents =
help_documents_counters_types_used =
help_documents_counters_status =
help_documents_counters_description =
help_documents_counters_next_number = Nächste Nummer. Dieses Feld verwenden, um eine Nummer zu setzen, von der an der Zähler zu zählen anfängt.
help_documents_counters_medial_number_index = Bestimmt die Art und Weise der Bildung der Indexnummer für diesen Dokumenttyp
help_documents_counters_medial_number_delimiter = Trennzeichen zwischen Dokumentnummer und Zwischenindex
help_documents_counters_medial_number_index_position = Indexposition in Bezug auf die Dokumentnummer
