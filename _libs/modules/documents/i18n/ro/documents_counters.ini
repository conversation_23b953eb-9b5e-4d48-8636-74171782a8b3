documents_counters = Numeratoare documente
documents_counters_name = Nume
documents_counters_formula = Formulă
documents_counters_description = Descriere
documents_counters_next_number = Număr următor
documents_counters_count_documents = Număr de documente
documents_counters_types_used = Folosit în tipuri documente
documents_counters_status = Status
documents_counters_status_active = Activ
documents_counters_status_inactive = Inactiv
documents_counters_added_by = Adăugat de
documents_counters_modified_by = Modificat de
documents_counters_added = Adăugat la
documents_counters_modified = Modificat la

documents_counters_add = Adăugare numerator documente
documents_counters_edit = Editare numerator documente
documents_counters_view = Vizualizare numerator documente
documents_counters_translate = Traducere numerator documente

message_documents_counters_add_success = Datele pentru numeratorul au fost adăugate cu succes!
message_documents_counters_edit_success = Datele pentru numeratorul au fost editate cu succes!
message_documents_counters_translate_success = Numeratorul a fost tradus cu succes!

error_documents_counters_edit_failed = Datele pentru numeratorul nu au fost editate cu succes:
error_documents_counters_add_failed = Datele pentru numeratorul nu au fost adăugate:
error_documents_counters_translate_failed = Numeratorul nu a fost tradus cu succes:

error_no_counter_name_specified = Nu ați introdus nume!
error_no_counter_formula_specified = Nu ați introdus formulă!
error_invalid_next_number = Vă rugăm să introduceți numărul următor pentru numaratorului, compus numai din cifre și cu valoare mai mare de 0!
error_documents_counter_mutex_num = Formula pentru numaratorul poate conține numai unul din elementele [document_num] și [customer_num]!

error_no_types_used = Nu este folosit în nici un tip de documente

error_no_medial_number_index_specified = Nu ați introdus index pentru documentele intermediare!
error_missing_index_delimiter = Nu ați introdus separator pentru documentele intermediare!
error_no_selected_medial_number_index_position = Nu este selectată poziție pentru indexul intermediar!

documents_counters_medial_number_index = Index număr intermediar
documents_counters_medial_number_delimiter = Separator număr intermediar
documents_counters_medial_number_index_position = Poziția indexului de număr intermediar
documents_counters_medial_number_index_number = cifre (0-9)
documents_counters_medial_number_index_latin_capital_letters = Litere latine mari (A-Z)
documents_counters_medial_number_index_latin_small_letters = Litere latine mici (a-z)
documents_counters_medial_number_index_cyrilic_capital_letters = Litere cirilice mari (А-Я)
documents_counters_medial_number_index_cyrilic_small_letters = Litere cirilice mici (а-я)
documents_counters_medial_number_index_position_prefix = prefix
documents_counters_medial_number_index_position_suffix = sufix

documents_counters_formula_delimiter = Separator
documents_counters_empty_delimiter = fără separator
documents_counters_formula_leading_zeroes = Număr zerouri de frunte
documents_counters_formula_date_format = format
documents_counters_formula_date_delimiter = cu separator

documents_counters_formula_date_format_year = aaaa
documents_counters_formula_date_format_year_short = aaaa
documents_counters_formula_date_format_month = ll
documents_counters_formula_date_format_day = zz

documents_counters_formula_date_format1 = aaaa
documents_counters_formula_date_format2 = ll/aaaa
documents_counters_formula_date_format3 = ll/aa
documents_counters_formula_date_format4 = aaaa/ll
documents_counters_formula_date_format5 = aa/ll
documents_counters_formula_date_format6 = zz/ll/aaaa
documents_counters_formula_date_format7 = zz/ll/aa
documents_counters_formula_date_format8 = ll/zz/aaaa
documents_counters_formula_date_format9 = ll/zz/aa
documents_counters_formula_date_format10 = aaaa/zz/ll
documents_counters_formula_date_format11 = aa/zz/ll
documents_counters_formula_date_format12 = aaaa/ll/zz
documents_counters_formula_date_format13 = aa/ll/zz
documents_counters_formula_date_format14 = aa
documents_counters_formula_date_format15 = aaa/ll

documents_counters_formula_legend = Legendă pentru completarea formulei numaratorului
documents_counters_formula_prefix = Prefix
documents_counters_formula_document_num = Număr document
documents_counters_formula_customer_code = Cod contragent
documents_counters_formula_project_code = Cod proiect
documents_counters_formula_office_code = Cod birou
documents_counters_formula_user_code = Cod utilizator
documents_counters_formula_document_type_code = Cod tip document
documents_counters_formula_trademark_code = Cod entitate
documents_counters_formula_transform_subnum = Număr de ordine transformare
documents_counters_formula_parent_doc_num = Număr document părinte (pentru documentele transformate)
documents_counters_formula_customer_num = Număr document pentru contragent
documents_counters_formula_customer_year = Numai anul curent
documents_counters_formula_document_added = Data adăugării documentului
documents_counters_formula_office_num = Document number per office
documents_counters_formula_office_year = Numai anul curent
documents_counters_formula_trademark_num = Număr document per entitate
documents_counters_formula_trademark_year = Numai pentru anul curent

documents_counters_formula_prefix_descr = se completează direct cu 2-3 litere, de exemplu pentru oferte OFF.
documents_counters_formula_document_num_descr = completează numărul de ordine al documentului.
documents_counters_formula_customer_code_descr = completează codul contragentului, selectat pentru documentul.
documents_counters_formula_project_code_descr = completează codul proiectului, selectat pentru documentul.
documents_counters_formula_office_code_descr = completează codul biroului, selectat pentru documentul.
documents_counters_formula_user_code_descr = completează codul utilizatorului, care a creat documentul.
documents_counters_formula_document_type_code_descr = completează codul tipului de document.
documents_counters_formula_trademark_code_descr = completează codul entității selectate în document.
documents_counters_formula_transform_subnum_descr = .01.01 etc. la transformarea din document primar/secundar la document secundar.
documents_counters_formula_parent_doc_num_descr = completează numărul documentului părinte (pentru documentente transformate).
documents_counters_formula_customer_num_descr = completează număr de ordine al documentului pentru contragent. Numărul poate fi definit numai pentru documentele, adăugate în cursul anului curent sau toate.
documents_counters_formula_office_num_descr = completează următorul număr al documentului pentru birou. Numărul poate fi specificat în funcție de documentele doar pentru anul curent sau pentru toate documentele.
documents_counters_formula_trademark_num_descr = completează următorul număr al documentului pentru entitate. Numărul poate fi specificat în funcție de documentele doar pentru anul curent sau pentru toate documentele.
documents_counters_formula_document_added_descr = data adăugării documentului.

documents_counters_formula_note = <strong>NOTĂ:</strong> La formula numaratorului se pot adăuga <strong>numai 5 elemente</strong>

#Help SECTION for label info

help_documents_counters_name =
help_documents_counters_formula =
help_documents_counters_count_documents =
help_documents_counters_types_used =
help_documents_counters_status =
help_documents_counters_description =
help_documents_counters_next_number = Număr următor. Folosiți acest câmp pentru a defini numărul din care începe numaratorul.
help_documents_counters_medial_number_index = definește cum se va forma numărul indexului pentru acest tip document
help_documents_counters_medial_number_delimiter = Separator între numărul documentului și indexul intermediar
help_documents_counters_medial_number_index_position = Poziția indexului fața de numărul documentului
