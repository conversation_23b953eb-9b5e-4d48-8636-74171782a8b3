@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.toolbar_btn_loading {
    position: relative;
}
.toolbar_btn_loading::after {
    content: '';
    animation: spin 500ms infinite;
    animation-timing-function: linear;
    display: block;
    width: 1.25rem;
    height:  1.25rem;
    position: absolute;
    top: 50%;
    right: -0.7rem;
    transform: translate(-50%, 0);
    border-radius: 55%;
    border: 4px solid transparent;
    border-top: 4px solid rgba(255, 165, 0, 1);
    opacity: 1;
}
.toolbar_btn_success {
    background-color: rgba(0, 255, 0, 0.3);
    animation: pulse 1s 2;
}
.toolbar_btn_failed {
    background-color: rgba(255, 0, 0, 0.3);
    animation: pulse 1s 2;
}
.e-toolbar-item {
    transition: background-color 200ms ease-out;
}

.filters-container {
    width: fit-content;
    padding-top: 1em;
}
.filters-container-modern {
    margin-top: 1rem;
}
.filters_table_wrapper {
    border-collapse: collapse;
}
.display-objects-container {
    margin-top: 1rem;
    padding-top: 1em;
}

.toolbars_container--top,
.toolbars_container--bottom {
    margin-top: 10px;
}
