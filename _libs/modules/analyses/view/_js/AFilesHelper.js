class AFilesHelper {
    /**
     * Downloads the given content as a file with the specified file name.
     *
     * @param {string} fileName - The name of the file to be created and downloaded.
     * @param {Blob} content - The content to be written to the file.
     * @return {void} This method does not return a value. It initiates the download of the file
     */
    static downloadContentAsFile(fileName, content) {
        let link = document.createElement('a');
        link.href = URL.createObjectURL(content);
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Creates a ZIP file from the provided files and triggers its download.
     *
     * @requires JSZip The JSZip library must be loaded globally
     * @param {string} fileName - The name to be assigned to the downloaded ZIP file.
     * @param {Array<{name: string, content: string|Blob}>} files - An array of objects representing files to be added to the ZIP. Each object must contain a 'name' (file name) and 'content' (file data).
     * @return {Promise<void>} A promise that resolves when the ZIP file is successfully generated and downloaded, or rejects if there is an error.
     */
    static downloadZip(fileName, files) {
        let zip = new JSZip();
        files.forEach(file => {
            zip.file(file.name, file.content);
        });
        return zip.generateAsync({ type: 'blob' }).then(content => {
            this.downloadContentAsFile(fileName, content);
        }).catch(error => {
            let errMsg = 'Failed to download ZIP:';
            console.error(errMsg, error.message);
            throw new Error(errMsg, { cause: error });
        })
    }
}
