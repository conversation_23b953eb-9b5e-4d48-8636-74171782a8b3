class ACcrHelper {
    /**
     * Exports manifest and CSV exports of specified grids.
     *
     * @param {string} zipFileName The name of the ZIP file to be downloaded.
     * @param {string} manifestFileName The name of the manifest file to include in the ZIP.
     * @param {string} manifestFileContent The content of the manifest file.
     * @param {Array<Object>} gridsCsvExportParams An array of parameters for exporting grids to CSV. Each object should include:
     *     - `gridName` {string}: The name of the grid to export.
     *     - `csvFileName` {string}: The name of the CSV file to include in the ZIP.
     *     - `skipColumns` {Array<string>} [optional]: A list of column field names to exclude in the CSV file.
     *     - `colSeparator` {string} [optional]: The column separator to use in the CSV file (default: ';').
     *     - `rowSeparator` {string} [optional]: The row separator to use in the CSV file (default: '\r\n').
     * @param {string} errMsg An error message to display in case the ZIP creation or download fails.
     * @return {void} This method does not return any value. It initiates the download of the generated ZIP file and handles errors if they occur.
     */
    static exportCcrZip(zipFileName, manifestFileName, manifestFileContent, gridsCsvExportParams, errMsg) {
        // Prepare to collect files for ZIP
        let files = [];

        // Collect the manifest file for ZIP
        files.push({
            name: manifestFileName,
            content: manifestFileContent,
        });

        // Collect CSV files for ZIP
        gridsCsvExportParams.forEach(gridCsvExportParams => {
            // Get the grid based on it's name
            let grid = AHelper.getGridComponent(gridCsvExportParams.gridName);

            // Prepare CSV data
            let columns = grid.getColumns();
            if (gridCsvExportParams.hasOwnProperty('skipColumns')) {
                columns = columns.filter(col => !gridCsvExportParams.skipColumns.includes(col.field));
            }
            let colSeparator = gridCsvExportParams.colSeparator ?? ';';
            let headers = columns.map(col => (col.headerText ?? col.field ?? '')).join(colSeparator);
            let rows = grid.currentViewData.map(
                row => columns.map(col => row[col.field] ?? '').join(colSeparator));
            let rowSeparator = gridCsvExportParams.rowSeparator ?? '\r\n';
            let csvText = [headers, ...rows].join(rowSeparator) + rowSeparator;

            // Collect the CSV for ZIP
            files.push({
                name:    gridCsvExportParams.csvFileName,
                content: csvText,
            });
        });

        // Download the ZIP
        AFilesHelper.downloadZip(zipFileName, files).
            catch(error => {
                setMessages([
                    {
                        message: errMsg,
                        type:    'error',
                    },
                ])
            });
    }
}
