class AHelper {
    static buildActionUrl(module, controller, action, getParams) {
        const url = new URL(env.base_host + env.base_url);
        url.searchParams.set(env.module_param, module);
        if (typeof controller === 'string' && controller !== '' && controller !== module) {
            url.searchParams.set(env.controller_param, controller);
        }
        url.searchParams.set(module, action);

        if (typeof getParams !== 'undefined' && getParams !== null) {
            Object.entries(getParams).forEach(([getParamKey, getParamValue]) => {
              url.searchParams.append(getParamKey, String(getParamValue));
            });
        }

        return url;
    }

    static getColUniqueValues(data, col) {
        return [...new Set(data.map(item => item[col] ?? null))];
    }

    static messageOnComplete(promise, messageOnSuccess, messageOnFail, reloadPage = false) {
        nzShowLoading();
        promise
        .then((response) => {
            alert(messageOnSuccess);
            if (reloadPage) {
                window.location.reload();
            }
        })
        .catch((error) => {
            console.error(error);
            alert(messageOnFail);
        })
        .finally(() => {
            nzHideLoading();
        });
    }

    static showEditForm(module, modelId, analysisUniqid = this.detectAnalysisUniqid()) {
        if (analysisUniqid === undefined) {
            throw new Error('Undefined analysis uniqid!');
        }

        // TODO: The action could be different for some modules, so prepare it depending the module
        ajaxForm('ajax_edit', module, modelId, `analysis-messages-container-${analysisUniqid}`, () => {
            const analysisForm = document.querySelector(`#analysis-form-${analysisUniqid}`);
            if (!analysisForm) {
                throw new Error(`Failed to find form for analysis with uniqid "${analysisUniqid}"`);
            }
            analysisForm.requestSubmit();
        });
    }

    static detectAnalysisUniqid() {
        const analysisContainer = document.querySelector('.analysis_container');
        if (!analysisContainer) {
            return undefined;
        }
        return analysisContainer.id.replace('analysis-', '');
    }

    static showEditFormByTarget(module, modelIdDataPath, targetElement) {
        const displayObject = this.getDisplayObjectByTarget(targetElement);
        const rowData = displayObject.getRowDataByTarget(targetElement);
        // TODO: Use some common function for parsing the data path and extracting the data from rowData
        if (!rowData.hasOwnProperty(modelIdDataPath)) {
            console.log(`Cannot find model ID with modelIdDataPath "${modelIdDataPath}" in data:`, rowData);
            throw new Error('Failed to show the edit form!');
        }
        const modelId = modelIdDataPath.split('.').reduce((acc, key) => acc?.[key], rowData);

        this.showEditForm(module, modelId, displayObject.analysisUniqid);
    }

    static getDisplayObjectByTarget(target) {
        const displayObjectContainer = target.closest('.display_object_container');
        if (!displayObjectContainer) {
            console.error(`Unable to get parent display object container for target:`, target);
            throw new Error('Failed to get display object!');
        }

        if (!displayObjectContainer.hasOwnProperty('analysisDisplayObject')) {
            console.error(`Unable to get parent display object for target:`, target);
            throw new Error('Failed to get display object!');
        }

        return displayObjectContainer.analysisDisplayObject;
    }

    // Currently not used, just good to have it
    static getDisplayObjectByName(analysisUniqId, displayObjectName) {
        const displayObjectContainer = document.querySelector(`.display_object_${displayObjectName}[data-analysis-uniqid="${analysisUniqId}"]`);
        if (!displayObjectContainer) {
            throw new Error(`Unable to get parent display object container for analysis uniqid "${analysisUniqId}" and display object "${displayObjectName}"!`);
        }

        if (displayObjectContainer.hasOwnProperty('analysisDisplayObject')) {
            throw new Error(`Unable to get parent display object for analysis uniqid "${analysisUniqId}" and display object "${displayObjectName}"!`);
        }

        return displayObjectContainer.analysisDisplayObject;
    }

    static getDisplayObjectComponent(displayObjectName, componentType) {
        let element = document.querySelector(`.display_object_${displayObjectName}`);
        if (!element) {
            let errMsg = `Unable to find "${componentType}" with name "${displayObjectName}"!`;
            console.error(errMsg);
            throw new Error(errMsg);
        }
        return ej.base.getComponent(element, componentType);
    }

    static getGridComponent(displayObjectName) {
        return this.getDisplayObjectComponent(displayObjectName, 'grid');
    }

    static removeTags(module, modelIds, tagIds, messageNoTags, messageOnSuccess, messageOnFail, reloadPage = false) {
        if (modelIds.length === 0) {
            alert(messageNoTags);
            return;
        }
        AHelper.messageOnComplete(
            (new AMulti(module)).removeTags(
                tagIds,
                modelIds
            ),
            messageOnSuccess,
            messageOnFail,
            reloadPage
        );
    }

    static getGridsColsUniqueValues(gridsCols) {
        const values = Object.entries(gridsCols).
            flatMap(([gridName, colName]) => AHelper.getColUniqueValues(
                AHelper.getGridComponent(gridName).getCurrentViewRecords(),
                colName));
        return [...new Set(values)];
    }
}
