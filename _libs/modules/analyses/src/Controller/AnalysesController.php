<?php

namespace Module\Analyses\Controller;

use Exception;
use General;
use Module\Analyses\Model\AdditionalFilters;
use Module\Analyses\Model\AdditionalFiltersAggregator;
use Module\Analyses\Model\Analyses;
use Module\Analyses\Model\AnalysesConfigExtractor;
use Module\Analyses\Model\Analysis;
use Module\Analyses\DisplayObjectsFactory;
use Module\Analyses\Model\DisplayObject;
use Module\Analyses\Model\FiltersViewer;
use Module\Analyses\Model\FilterViewModelsFactory;
use Module\Analyses\Model\Toolbars\ToolbarsListFactory;
use Module\Analyses\Viewer\AnalysesViewer;
use Module\Analyses\Model\AbstractFiltersValidation;
use Module\Analyses\Model\FiltersValidator;
use Module\Analyses\Model\FiltersValidationsResult;
use Module\Analyses\Model\FilterValidationResult;
use Nzoom\I18n\I18nService;
use Nzoom\Mvc\AbstractController;
use Nzoom\Mvc\ControllerTrait\CheckAccessTrait;
use Nzoom\Mvc\ControllerTrait\I18nTrait;
use Nzoom\Mvc\ControllerTrait\RedirectTrait;
use Nzoom\Navigation;
use Registry;

class AnalysesController extends AbstractController
{
    use CheckAccessTrait;
    use I18nTrait;
    use RedirectTrait;

    public const MODULE = 'analyses';
    private Registry $registry;
    private AnalysesViewer $viewer;
    // TODO: Check if we need both ... we need $defaultAction, but I'm not sure for $action ...
    private string $action = 'index';
    public $defaultAction = 'index';
    private array $analyses = [];
    private array $analysesFiltersParams = [];

    public function __construct($registry) {
        $this->registry = $registry;

//        $this->registry['timer']->setCheckpoint('controller', 'Controller execution started');
        // Check access to module
        // This should not be here, but it's this way in the other modules
        // One problem is that if we miss this check in some module, it will be accessible even if not authenticated
        $this->checkAccessModule(true, self::MODULE, $this->registry['action'], self::MODULE);
    }

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public function __get($name)
    {
        return $this->$name??null;
    }

    private function getViewer(): AnalysesViewer
    {
        if (!isset($this->viewer)) {
            $this->viewer = new AnalysesViewer($this->registry, true);
        }
        return $this->viewer;
    }

    public function actionIndex(): AnalysesViewer
    {
        $module = self::MODULE;
        $action = 'index';

        $viewer = $this->getViewer();

        // Load the module language vars
        $viewer->loadI18nFromDb($module);
        I18nService::getInstance($this->registry)->loadInTranslater($module);

        // Prepare scripts
        $viewer->addAdditionalScript(PH_MODULES_URL . "{$module}/view/_js/{$action}.js");

        // Prepare styles
        $viewer->addAdditionalStyle(PH_MODULES_URL . "{$module}/view/css/{$action}.css");

        $viewer->template = 'index.html';
        $viewer->data['title'] = $viewer->i18n($module);
        $viewer->data['page_title'] = $viewer->i18n($module) ;
        $viewer->data['dont_wrap_content'] = true;
        $viewer->data['analyses_dropdown'] = $this->prepareAnalysesDropdown($viewer);

        return $viewer;
    }

    private function prepareAnalysesDropdown(AnalysesViewer $viewer): array
    {
        $analysesDropdown = [];

        $allAnalyses = Analyses::search($this->registry, ['sanitize' => true]);
        $analysesOptgroups = [];
        $sectionNameOther = $viewer->i18n('analyses_section_other');
        $register = self::MODULE;
        foreach ($allAnalyses as $analysis) {
            $analysisIsPermitted = ($this->registry['currentUser']->checkRights($register . $analysis->get('id'), 'generate_analysis') && $analysis->checkPermissions('generate_analysis', $register));
            if (!$analysisIsPermitted || !$analysis->get('visible')) {
                continue;
            }

            //prepare a dropdown with all analyses
            //grouped by section name and ordered by position
            //all the analyses that do not have section name are pushed into OTHERS
            //remove the numbering in the name of the section
            $section = ($analysis->get('section')) ?
                preg_replace(
                    '#^[0-9]*\. *(.*)$#',
                    '$1',
                    $analysis->get('section')
                ) :
                $sectionNameOther;
            $analysesOptgroups[$section][] = [
                'label'        => $analysis->get('name'),
                'option_value' => $analysis->get('id'),
            ];
        }

        if ($analysesOptgroups) {
            if (array_key_exists($sectionNameOther, $analysesOptgroups)) {
                $sectionOtherOptions = $analysesOptgroups[$sectionNameOther];
                unset($analysesOptgroups[$sectionNameOther]);
                if ($analysesOptgroups) {
                    // Put section "Other" to the bottom of the list
                    $analysesOptgroups[$sectionNameOther] = $sectionOtherOptions;
                } else {
                    // There are analyses only for section "Others", so prepare options, instead of optgroups
                    $analysesOptions = $sectionOtherOptions;
                }
            }

            $analysesDropdown = [
                'name'               => 'analysis',
                'type'               => 'dropdown',
                'label'              => $viewer->i18n('analysis_selection_title'),
                'value'              => (string)$this->getRequestedAnalysisId() ?: '',
                'first_option_label' => $viewer->i18n('analyses_select_option'),
            ];
            if (isset($analysesOptions)) {
                $analysesDropdown['options'] = $analysesOptions;
            } else {
                $analysesDropdown['optgroups'] = $analysesOptgroups;
            }
        }

        return $analysesDropdown;
    }

    /**
     * @return AnalysesViewer
     * @throws Exception
     */
    public function actionGenerateAnalysis(): AnalysesViewer
    {
        $module = self::MODULE;
        $action = 'generate_analysis';

        I18nService::getInstance($this->registry)->loadInTranslater($module);

        $analysisId = $this->getRequestedAnalysisId();
        $analysis = Analyses::getAnalysis($this->registry, $analysisId);

        if (empty($analysis) || !$this->hasUserAccessToAnalysis($analysisId)) {
            $this->registry['messages']->setError($this->i18n('no_rights_to_generate_analysis'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect(self::MODULE);
        }

        $viewer = $this->getViewer();

        // Load the module language vars
        $viewer->loadI18nFromDb($module);

        /*
         * Prepare scripts
         */
        // TODO: This is a fast fix, because zapatec didn't worked for a date filter, rethink
        $this->registry['include_calendar'] = true;

        $viewer->addAdditionalScript(PH_JAVASCRIPT_URL . 'ej2/ej2.min.js');
        $viewer->addAdditionalScript(PH_JAVASCRIPT_URL . 'ej2/ej2.helper.js');
        $viewer->addAdditionalScript(PH_MODULES_URL . "{$module}/view/_js/AnalysisDataAdaptor.js");
        $viewer->addAdditionalScript(PH_MODULES_URL . "{$module}/view/_js/AnalysisGenerator.js");
        $viewer->addAdditionalScript(PH_MODULES_URL . "{$module}/view/_js/AnalysisDisplayObject.js");
        $viewer->addAdditionalScript(PH_MODULES_URL . "{$module}/view/_js/AHelper.js");
        $viewer->addAdditionalScript(PH_MODULES_URL . "{$module}/view/_js/AMulti.js");

        // Prepare styles
        $viewer->addAdditionalStyle(PH_JAVASCRIPT_URL . 'ej2/material.css');
        $viewer->addAdditionalStyle(PH_MODULES_URL . "{$module}/view/css/{$action}.css");

        try {
            $filtersViewer = $this->getAnalysisFiltersViewer($analysis, $viewer);
        } catch (Exception $e) {
            throw new Exception("Failed to get filters viewer for analysis with ID: {$analysis->get('id')}!", $e->getCode(), $e);
        }

        try {
            $displayObjects = $this->getAnalysisDisplayObjects($analysis);
        } catch (Exception $e) {
            throw new Exception("Failed to get display objects for analysis {$analysis->get('id')}!", $e->getCode(), $e);
        }

        $toolbarsList = ToolbarsListFactory::fromAnalysis($this->registry, $analysis);

        $additionalScripts = [];
        foreach ($displayObjects as $displayObject) {
            // TODO: Maybe getModeJsFileName() should return the full file path, not just the file name?
            $additionalScriptName = PH_MODULES_URL . "{$module}/view/_js/{$displayObject->getModeJsFileName()}";
            $additionalScripts[$additionalScriptName] = null;
        }
        $viewer->addAdditionalScripts(array_keys($additionalScripts));

        if ($analysisAdditionalScripts = $this->getAnalysisAdditionalScripts($analysis)) {
            $viewer->addAdditionalScripts($analysisAdditionalScripts);
        }

        if ($toolbarsAdditionalScripts = $toolbarsList->getScripts()) {
            $viewer->addAdditionalScripts($toolbarsAdditionalScripts);
        }

        $viewer->template = 'generate_analysis.html';
        $viewer->data['title'] = $analysis->get('name');
        $viewer->data['page_title'] = $viewer->i18n($module) ;
        $viewer->data['analysis_id'] = $analysisId;
        $viewer->data['analysis_uniqid'] = str_replace('.', '', uniqid('analysis', true));
        if ($filtersViewer) {
            $viewer->data['filters_viewer'] = $filtersViewer;
        }
        $viewer->data['analyses_dropdown'] = $this->prepareAnalysesDropdown($viewer);
        $viewer->data['display_objects'] = $displayObjects;
        $viewer->data['dont_wrap_content'] = true;
        // TODO: Check if I really need this, because the filters form works correctly without it too
        $viewer->data['generate_analysis_url'] = Navigation::buildNzoomUrl(
            self::MODULE,
            $action,
            null,
            [
                'analysis' => $analysisId,
                'generate' => 'generate',
            ]
        );
        $viewer->data['analysis_generate'] = $this->registry['request']->getGet('generate');

        $viewer->data['toolbarsList'] = $toolbarsList;

        $viewer->data['css'] = $this->getAnalysisCss($analysis);;

        return $viewer;
    }

    /**
     * @return array[]
     * @throws Exception
     */
    public function actionGetData(): array
    {
        try {
            $analysisId = $this->getRequestedAnalysisId();
            if (!$analysisId) {
                throw new Exception("No analysis ID provided!");
            }

            if (!$this->hasUserAccessToAnalysis($analysisId)) {
                throw new Exception("No access to analysis with ID: {$analysisId}");
            }

            $dataSourceName = $this->getDataSourceName();
            if (!$dataSourceName) {
                throw new Exception("No data source name for analysis with ID: {$analysisId}");
            }

            $requestParams = $this->registry['request']->getGet();
            $filtersValues = [];
            if ($requestParams) {
                if (array_key_exists('additional_filters', $requestParams)) {
                    $additionalFiltersJson = $requestParams['additional_filters'];
                    unset($requestParams['additional_filters']);
                }
                $filtersValues = General::slashesEscape($requestParams);
            }
            $additionalFilters = new AdditionalFilters(new AdditionalFiltersAggregator($additionalFiltersJson??json_encode([])));

            $analysis = Analyses::getAnalysis($this->registry, $analysisId);

            $filtersParams = $this->getAnalysisFiltersParams($analysis);

            I18nService::getInstance($this->registry)->loadInTranslater(self::MODULE, ['filters_validation']);
            $filtersValidator = new FiltersValidator($filtersParams, $filtersValues, $this->registry['translater'], $this->registry['lang']);

            // Validate
            try {
                $filtersValidationsResult = $filtersValidator->run();
            } catch (Exception $e) {
                // TODO: User message.
                throw new Exception("Filters validations failed for analysis with ID \"{$analysisId}\"", $e->getCode(), $e);
            }

            $data = [
                'messages' => $this->extractFiltersValidationsMessages($filtersValidationsResult),
            ];

            // Get data
            if ($filtersValidationsResult->isValid()) {
                foreach ($this->getData($analysisId, $dataSourceName, $filtersValues, $additionalFilters) as $k => $v) {
                    if ($k === 'messages') {
                        $data[$k][] = $v;
                    } else if (array_key_exists($k, $data)) {
                        throw new Exception('Unexpected data overlap!');
                    } else {
                        $data[$k] = $v;
                    }
                }
            }

            return $data;
        } catch (Exception $e) {
            $messages = [];
            do {
                $messages[] = $e->getMessage();
                $e = $e->getPrevious();
            } while ($e);

            // TODO: Maybe return array with keys 'data' and 'messages', so the frontend tobe able to show som debug info,
            //   but maybe only if some debug mode is enabled
            throw new Exception(implode(" -> ", $messages));
        }
    }

    /**
     * @param FiltersValidationsResult $filtersValidationsResult
     * @return array
     * @throws Exception
     */
    private function extractFiltersValidationsMessages(FiltersValidationsResult $filtersValidationsResult): array
    {
        $messages = [];
        if ($filtersValidationsResult->hasMessages() && $filtersValidationsResults = $filtersValidationsResult->getResults()) {
            foreach ($filtersValidationsResults as $filterValidationsResult) {
                if (!$filterValidationsResult->hasMessages() || !($filterValidationsResults = $filterValidationsResult->getResults())) {
                    continue;
                }
                foreach ($filterValidationsResults as $filterValidationResult) {
                    try {
                        $message = $this->extractFilterValidationMessage($filterValidationResult);
                    } catch (Exception $e) {
                        throw new Exception("Extracting filter validation message failed!", $e->getCode(), $e);
                    }
                    if ($message) {
                        $messages[] = $message;
                    }
                }
            }
        }

        return $messages;
    }

    /**
     * @param FilterValidationResult $filterValidationResult
     * @return array
     * @throws Exception
     */
    private function extractFilterValidationMessage(FilterValidationResult $filterValidationResult) : array
    {
        if (!$filterValidationResult->hasMessages()) {
            return [];
        }

        $filterValidationErrorLevel = $filterValidationResult->getErrorLevel();
        switch ($filterValidationErrorLevel) {
            case AbstractFiltersValidation::ERROR_LEVEL_MESSAGE:
                $messageType = 'message';
                break;
            case AbstractFiltersValidation::ERROR_LEVEL_WARNING:
                $messageType = 'warning';
                break;
            case AbstractFiltersValidation::ERROR_LEVEL_ERROR:
                $messageType = 'error';
                break;
            default:
                throw new Exception("Unknown filter validation level: {$filterValidationErrorLevel}");
        }

        return [
            'type'    => $messageType,
            'message' => $filterValidationResult->getMessageText() ?? '',
            'name'    => $filterValidationResult->getFilterName() ?? '',
        ];
    }

    /**
     * @param int $analysisId
     * @param string $dataSourceName
     * @param array $filtersValues
     * @param AdditionalFilters $additionalFilters
     * @return array
     * @throws Exception
     */
    private function getData(
        int $analysisId,
        string $dataSourceName,
        array $filtersValues,
        AdditionalFilters $additionalFilters
    ): array
    {
        try {
            $analysisDataSet = Analyses::getAnalysisDataSet($this->registry, $analysisId, $dataSourceName, $filtersValues, $additionalFilters);
        } catch (Exception $e) {
            throw new Exception("Couldn't get dataset for datasource '{$dataSourceName}' of analysis with ID '{$analysisId}'!", $e->getCode(), $e);
        }

        $this->actionCompleted = true;

        $response = [
            'Items' => $analysisDataSet->getItems(),
        ];
        $response['Count'] = count($response['Items']);

        $dataTypes = $analysisDataSet->getTypes();
        if ($dataTypes) {
            $response['data_types'] = $dataTypes;
        }

        return $response;
    }

    private function getRequestedAnalysisId(): int
    {
        return (int)$this->registry['request']->getGet('analysis');
    }

    private function getDataSourceName(): string
    {
        return (string)$this->registry['request']->getGet('data_source');
    }

    private function hasUserAccessToAnalysis(int $analysisId): bool
    {
        return $this->registry['currentUser']->checkRights(self::MODULE, 'generate_analysis')
            && $this->registry['currentUser']->checkRights(self::MODULE . $analysisId, 'generate_analysis');
    }

    public static function buildAnalysisDataSourceBaseUrl(string $analysisId, string $dataSourceName): string
    {
        // TODO: Move this to the Analysis class
        return Navigation::buildNzoomUrl(self::MODULE,
            'get_data',
            null,
            [
                'analysis'    => $analysisId,
                'data_source' => $dataSourceName,
            ]
        );
    }

    /**
     * @param Analysis $analysis
     * @return DisplayObject[]
     * @throws Exception
     */
    private function getAnalysisDisplayObjects(Analysis $analysis): array
    {
        return (new DisplayObjectsFactory($analysis))(
            (new AnalysesConfigExtractor($this->registry, $analysis))->getDisplayObjectsParams()
        );
    }

    /**
     * @param Analysis $analysis
     * @return array
     * @throws Exception
     */
    private function getAnalysisSettings(Analysis $analysis): array
    {
        return (new AnalysesConfigExtractor($this->registry, $analysis))->getSettings();
    }

    /**
     * @param Analysis $analysis
     * @return string
     * @throws Exception
     */
    private function getAnalysisCss(Analysis $analysis): string
    {
        return (new AnalysesConfigExtractor($this->registry, $analysis))->getCss();
    }

    /**
     * @param Analysis $analysis
     * @param AnalysesViewer $analysesViewer
     * @return FiltersViewer
     * @throws Exception
     */
    private function getAnalysisFiltersViewer(Analysis $analysis, AnalysesViewer $analysesViewer): ?FiltersViewer
    {
        $filtersParams = $this->getAnalysisFiltersParams($analysis);
        if (!$filtersParams) {
            return null;
        }
        $filtersValues = $this->registry['request']->getGet();
        $filterViewModels = (new FilterViewModelsFactory())($filtersParams, $filtersValues);
        $filtersDefaultVarsProvider = function () use ($analysesViewer) {
            return [
                'template_vars' => $analysesViewer->getRenderer()->getTemplateVars(),
                'config_vars'   => $analysesViewer->getRenderer()->getConfigVars(),
            ];
        };

        return new FiltersViewer(
            $filterViewModels,
            $filtersDefaultVarsProvider,
            $this->registry['theme']
        );
    }

    /**
     * @param Analysis $analysis
     * @return array
     * @throws Exception
     */
    private function getAnalysisFiltersParams(Analysis $analysis): array
    {
        if (!array_key_exists($analysis->get('id'), $this->analysesFiltersParams)) {
            try {
                $this->analysesFiltersParams[$analysis->get('id')] = (new AnalysesConfigExtractor($this->registry, $analysis))->getFilters();
            } catch (Exception $e) {
                throw new Exception("Failed to extract filters!", $e->getCode(), $e);
            }
        }
        return $this->analysesFiltersParams[$analysis->get('id')];
    }

    /**
     * @param Analysis $analysis
     * @return array
     * @throws Exception
     */
    private function getAnalysisAdditionalScripts(Analysis $analysis): array
    {
        $settingsAdditionalScripts = $this->getAnalysisSettings($analysis)['additional_scripts'] ?? [];
        if (!is_array($settingsAdditionalScripts)) {
            throw new Exception("Additional scripts must be an array!");
        }

        return $settingsAdditionalScripts;
    }
}
