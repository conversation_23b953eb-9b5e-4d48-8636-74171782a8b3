<?php

namespace Module\Analyses\Model;

use Exception;
use Registry;
use Symfony\Component\Yaml\Yaml;

class AnalysesConfigExtractor
{
    private Registry $registry;
    private Analysis $analysis;

    public function __construct(Registry $registry, Analysis $analysis)
    {
        $this->registry = $registry;
        $this->analysis = $analysis;
    }

    /**
     * @param string $configString
     * @param array $userPlaceholders
     * @param array $systemPlaceholders
     * @return string
     * @throws Exception
     */
    private function applyPlaceholders(
        string $configString,
        array $userPlaceholders,
        array $systemPlaceholders
    ): string {
        try {
            $configString = (new AnalysesConfigRendererFactory)(
                AnalysesConfigRendererFactory::MODE_USR,
                $userPlaceholders,
                $configString
            );
        } catch (Exception $e) {
            throw new Exception("Failed to apply user placeholders!", $e->getCode(), $e);
        }

        try {
            return (new AnalysesConfigRendererFactory)(
                AnalysesConfigRendererFactory::MODE_SYS,
                $systemPlaceholders,
                $configString
            );
        } catch (Exception $e) {
            throw new Exception("Failed to apply system placeholders!", $e->getCode(), $e);
        }
    }

    /**
     * @return array
     */
    private function getSystemPlaceholders(): array
    {
        return (new SystemPlaceholdersFactory($this->registry, $this->analysis))();
    }

    /**
     * Parse config string
     *
     * @param string $configString
     * @return mixed
     */
    private function parseConfigString(string $configString)
    {
        return Yaml::parse($configString);
    }

    /**
     * @throws Exception
     */
    public function getFilters(): array
    {
        $filtersConfigString = $this->analysis->get('filters');

        // It's possible to have no description for filters
        if (!is_string($filtersConfigString) || $filtersConfigString === '') {
            return [];
        }

        // Parse the filters description
        try {
            $filters = $this->parseConfigString(
                $this->applyPlaceholders(
                    $filtersConfigString,
                    $this->analysis->getPlaceholders(),
                    $this->getSystemPlaceholders()
                )
            );
        } catch (Exception $e) {
            throw new Exception("Failed to parse filters!", $e->getCode(), $e);
        }

        // Allow only array as description of filters
        if (!is_array($filters)) {
            throw new Exception("Invalid filters for analysis {$this->analysis->get('id')}!");
        }

        return $filters;
    }

    /**
     * @throws Exception
     */
    public function getDisplayObjectsParams(): array
    {
        $displayObjectsConfigString = $this->analysis->get('display_objects');

        // Let's say ... it's possible to have no description for display objects
        if (!is_string($displayObjectsConfigString) || $displayObjectsConfigString === '') {
            return [];
        }

        // Parse the display objects description
        try {
            $displayObjects = $this->parseConfigString(
                $this->applyPlaceholders(
                    $displayObjectsConfigString,
                    $this->analysis->getPlaceholders(),
                    $this->getSystemPlaceholders()
                )
            );
        } catch (Exception $e) {
            throw new Exception("Failed to parse display objects!", $e->getCode(), $e);
        }

        // Allow only array as description of display objects
        if (!is_array($displayObjects)) {
            throw new Exception("Invalid display objects for analysis {$this->analysis->get('id')}!");
        }

        return $displayObjects;
    }

    /**
     * @throws Exception
     */
    public function getSettings(): array
    {
        $settingsString = $this->analysis->get('settings');

        if (!is_string($settingsString) || $settingsString === '') {
            return [];
        }

        try {
            $settings = $this->parseConfigString(
                $this->applyPlaceholders(
                    $settingsString,
                    $this->analysis->getPlaceholders(),
                    $this->getSystemPlaceholders()
                )
            );
        } catch (Exception $e) {
            throw new Exception("Failed to parse settings!", $e->getCode(), $e);
        }

        // Only array settings are supported
        if (!is_array($settings)) {
            throw new Exception("Invalid settings for analysis {$this->analysis->get('id')}!");
        }

        return $settings;
    }

    /**
     * @throws Exception
     */
    public function getCss(): string
    {
        $css = $this->analysis->get('css');

        if (!is_string($css) || $css === '') {
            return '';
        }

        try {
            $css = $this->applyPlaceholders(
                $css,
                $this->analysis->getPlaceholders(),
                $this->getSystemPlaceholders()
            );
        } catch (Exception $e) {
            throw new Exception("Failed to parse CSS!", $e->getCode(), $e);
        }

        return $css;
    }

    /**
     * @throws Exception
     */
    public function getDataSources(array $filtersValues, AdditionalFilters $additionalFilters): array
    {
        $dataSourcesConfigString = $this->analysis->get('data_sources');

        // It's possible to have no description for data sources
        if (!is_string($dataSourcesConfigString) || $dataSourcesConfigString === '') {
            return [];
        }

        $userPlaceholders = $this->analysis->getPlaceholders();
        $userPlaceholders['_filters'] = $filtersValues;
        $userPlaceholders['_additional_filters'] = $additionalFilters;

        // Parse the data sources description
        try {
            $dataSources = $this->parseConfigString(
                $this->applyPlaceholders(
                    $dataSourcesConfigString,
                    $userPlaceholders,
                    $this->getSystemPlaceholders()
                )
            );
        } catch (Exception $e) {
            throw new Exception("Failed to parse data sources!", $e->getCode(), $e);
        }

        // Allow only array as description of data sources
        if (!is_array($dataSources)) {
            throw new Exception("Invalid data sources for analysis {$this->analysis->get('id')}!");
        }

        return $dataSources;
    }
}
