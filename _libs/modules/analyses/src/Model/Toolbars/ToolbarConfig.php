<?php

namespace Module\Analyses\Model\Toolbars;

use Exception;
use Module\Analyses\Model\Toolbars\Elements\AbstractToolbarElement;

class ToolbarConfig
{
    private array $params;

    private string $position;
    public const SUPPORTED_POSITIONS = ['top', 'bottom'];
    private string $cssClass;
    /** @var array|AbstractToolbarElement[] */
    private array $elements;

    /**
     * @throws Exception
     */
    public function __construct(array $elements, array $params)
    {
        $this->setElements($elements);
        $this->setPosition($params['position']??'');
        $this->setCssClass($params['cssClass']??'');
        $this->setParams($params);
    }

    public function getParams(): array
    {
        return $this->params;
    }

    public function setParams(array $params): void
    {
        $this->params = $params;
    }

    public function getPosition(): string
    {
        return $this->position;
    }

    public function setPosition(string $position): void
    {
        if (!in_array($position, self::SUPPORTED_POSITIONS)) {
            throw new \InvalidArgumentException('Invalid toolbar position!');
        }
        $this->position = $position;
    }

    public function getCssClass(): string
    {
        return $this->cssClass;
    }

    public function setCssClass(string $cssClass): void
    {
        $this->cssClass = $cssClass;
    }

    /**
     * @return array|AbstractToolbarElement[]
     */
    public function getElements(): array
    {
        return $this->elements;
    }

    /**
     * @param array|AbstractToolbarElement[] $elements
     * @return void
     * @throws Exception
     */
    public function setElements(array $elements): void
    {
        foreach ($elements as $element) {
            if (!($element instanceof AbstractToolbarElement)) {
                throw new \Exception('Toolbar elements list can contain only instances of AbstractToolbarElement!');
            }
        }
        $this->elements = $elements;
    }
}
