<?php

namespace Module\Analyses\Model\Toolbars;

use Exception;
use Module\Analyses\Model\AnalysesConfigExtractor;
use Module\Analyses\Model\Analysis;
use Module\Analyses\Model\Toolbars\Elements\ToolbarElementFactory;
use Registry;

class ToolbarsListFactory
{
    /**
     * @throws Exception
     */
    public function __invoke(array $toolbarsParams): ToolbarsList
    {
        return $this->createToolbars($toolbarsParams);
    }

    /**
     * @throws Exception
     */
    public static function fromAnalysis(Registry $registry, Analysis $analysis): ToolbarsList
    {
        $settings = (new AnalysesConfigExtractor($registry, $analysis))->getSettings();

        $toolbarsParams = $settings['toolbars']??[];

        if (!is_array($toolbarsParams)) {
            throw new Exception('The "toolbars" setting must be an array!');
        }

        return (new ToolbarsListFactory())($toolbarsParams);
    }

    /**
     * @throws Exception
     */
    private function createToolbars(array $toolbarsParams): ToolbarsList
    {
        $toolbars = [];
        foreach ($toolbarsParams as $toolbarParams) {
            if (!array_key_exists('elements', $toolbarParams)) {
                throw new \Exception('Elements are required for toolbars!');
            }

            $elements = [];
            foreach ($toolbarParams['elements'] as $elementParams) {
                if (!array_key_exists('type', $elementParams)) {
                    throw new \Exception('Type is required for toolbar elements!');
                }
                $elements[] = (new ToolbarElementFactory())($elementParams);
            }

            $toolbarConfig = new ToolbarConfig($elements, $toolbarParams);

            $toolbars[] = new Toolbar($toolbarConfig);
        }

        return new ToolbarsList($toolbars);
    }
}
