<?php

namespace Module\Analyses\Model\Toolbars;

use Module\Analyses\Model\Toolbars\Elements\AbstractToolbarElement;

class Toolbar
{
    private ToolbarConfig $config;

    public function __construct(ToolbarConfig $config)
    {
        $this->setConfig($config);
    }

    private function getConfig(): ToolbarConfig
    {
        return $this->config;
    }

    private function setConfig(ToolbarConfig $config): void
    {
        $this->config = $config;
    }

    /**
     * @return array|AbstractToolbarElement[]
     */
    public function getElements(): array
    {
        return $this->getConfig()->getElements();
    }

    public function getScripts(): array
    {
        $scripts = [];
        foreach ($this->getElements() as $element) {
            $scripts[] = $element->getScripts();
        }
        return array_merge([], ...$scripts);
    }

    public function render(): string
    {
        $elementsHtml = '';

        foreach ($this->getElements() as $element) {
            $elementsHtml .= $element->render();
        }

        return <<<HTML
            <div class="analysis_toolbar {$this->getConfig()->getCssClass()}">
              {$elementsHtml}
            </div>
            HTML;

    }

    public function getPosition(): string
    {
        return $this->getConfig()->getPosition();
    }
}
