<?php

namespace Module\Analyses\Model\Toolbars;

use InvalidArgumentException;

class ToolbarsList
{

    /**
     * Array with position string keys, where each element is an array of Toolbar elements
     * @var array<string,Toolbar[]>
     */
    private array $toolbarsByPositions = [];

    /**
     * @param array|Toolbar[] $toolbars
     */
    public function __construct(array $toolbars)
    {
        foreach ($toolbars as $toolbar) {
            if (!$toolbar instanceof Toolbar) {
                throw new InvalidArgumentException('All elements must be instances of Toolbar');
            }
            $this->toolbarsByPositions[$toolbar->getPosition()][] = $toolbar;
        }
    }

    public function getScripts(): array
    {
        $scripts = [];

        foreach ($this->toolbarsByPositions as $toolbars) {
            foreach ($toolbars as $toolbar) {
                $scripts[] = $toolbar->getScripts();
            }
        }

        return array_merge([], ...$scripts);
    }

    public function render(string $position): string
    {
        if (!in_array($position, ToolbarConfig::SUPPORTED_POSITIONS)) {
            throw new InvalidArgumentException('Position must be one of: ' . implode(', ', ToolbarConfig::SUPPORTED_POSITIONS));
        }

        if (!array_key_exists($position, $this->toolbarsByPositions)) {
            return '';
        }

        $html = '';
        foreach ($this->toolbarsByPositions[$position] as $toolbar) {
            $html .= $toolbar->render();
        }

        return <<<HTML
            <div class="analysis_toolbars toolbars_container--{$position}">
              $html
            </div>
            HTML;
    }
}
