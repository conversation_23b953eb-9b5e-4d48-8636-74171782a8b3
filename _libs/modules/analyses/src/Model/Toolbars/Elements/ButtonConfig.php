<?php

namespace Module\Analyses\Model\Toolbars\Elements;

class ButtonConfig implements ConfigInterface
{
    private string $label;
    private string $cssClass;
    private string $onClick;

    public function __construct(string $label, string $onClick, string $cssClass = '')
    {
        $this->setLabel($label);
        $this->setOnClick($onClick);
        $this->setCssClass($cssClass);
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    private function setLabel(string $label): void
    {
        $this->label = $label;
    }

    public function getCssClass(): string
    {
        return $this->cssClass;
    }

    private function setCssClass(string $cssClass): void
    {
        $this->cssClass = $cssClass;
    }

    public function getOnClick(): string
    {
        return $this->onClick;
    }

    private function setOnClick(string $onClick): void
    {
        $this->onClick = $onClick;
    }

    public static function createFromArray(array $params): self
    {
        $requiredParams = ['label', 'onClick'];
        $missingParams = array_diff($requiredParams, array_keys($params));
        if (count($missingParams)) {
            throw new \InvalidArgumentException('Missing required params: ' . implode(', ', $missingParams));
        }
        return new self($params['label'], $params['onClick'], $params['cssClass']??'');
    }
}
