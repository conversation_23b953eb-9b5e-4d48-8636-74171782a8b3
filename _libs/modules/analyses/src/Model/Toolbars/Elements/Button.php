<?php

namespace Module\Analyses\Model\Toolbars\Elements;

use Nzoom\Renderer\SmartyRenderer;

class But<PERSON> extends AbstractToolbarElement
{
    private ButtonConfig $config;

    private string $template = 'button.html';

    public function __construct(ButtonConfig $config)
    {
        $this->setConfig($config);
    }

    public function getConfig(): ButtonConfig
    {
        return $this->config;
    }

    private function setConfig(ButtonConfig $config): void
    {
        $this->config = $config;
    }

    protected function getTemplate(): string
    {
        return $this->template;
    }

    public function getRenderer(): SmartyRenderer
    {
        $renderer = parent::getRenderer();
        $renderer->assign('element', $this);
        return $renderer;
    }

    public function getLabel(): string
    {
        return $this->getConfig()->getLabel();
    }

    public function getCssClass(): string
    {
        return $this->getConfig()->getCssClass();
    }

    public function getOnClick(): string
    {
        return $this->getConfig()->getOnClick();
    }
}
