<?php

namespace Module\Analyses\Model\Toolbars\Elements;

use Nzoom\Renderer\SmartyRenderer;
use Nzoom\Renderer\SmartyRendererFactory;

abstract class AbstractToolbarElement
{
    protected array $scripts = [];
    protected SmartyRenderer $renderer;
    private static int $lastElementId = 0;
    protected int $id;

    public function getScripts(): array
    {
        return $this->scripts;
    }

    protected function addScripts(array $scripts): void
    {
        $this->scripts = array_merge($this->scripts, $scripts);
    }

    protected function getRenderer(): SmartyRenderer
    {
        if (!isset($this->renderer)) {
            $this->renderer = (new SmartyRendererFactory())(
                dirname(__DIR__, 4) . '/view/toolbars/elements/'
            );
        }
        return $this->renderer;
    }

    public function render(): string
    {
        return $this->getRenderer()->fetch($this->getTemplate());
    }

    abstract protected function getTemplate(): string;

    public function getId(): int
    {
        if (!isset($this->id)) {
            $this->id = ++self::$lastElementId;
        }
        return $this->id;
    }
}
