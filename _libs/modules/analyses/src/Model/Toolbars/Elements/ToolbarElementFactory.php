<?php

namespace Module\Analyses\Model\Toolbars\Elements;

use Exception;

class ToolbarElementFactory
{
    /**
     * @param array $elementParams
     * @return AbstractToolbarElement
     * @throws Exception
     */
    public function __invoke(array $elementParams): AbstractToolbarElement
    {
        if (!array_key_exists('type', $elementParams)) {
            throw new \Exception('Type is required for toolbar elements!');
        }
        return $this->createElement($elementParams['type'], $elementParams);
    }

    /**
     * @return AbstractToolbarElement|Button|ExportCcrZip
     * @throws Exception
     */
    private function createElement(string $type, array $params): AbstractToolbarElement
    {
        $typeUcf = ucfirst($type);

        $configClass = __NAMESPACE__ . "\\{$typeUcf}Config";
        $elementClass = __NAMESPACE__ . "\\{$typeUcf}";

        if (!class_exists($configClass)) {
            throw new Exception("Config class {$configClass} does not exist!");
        }
        if (!class_exists($elementClass)) {
            throw new Exception("Element class {$elementClass} does not exist!");
        }

        if (!in_array(ConfigInterface::class, class_implements($configClass))) {
            throw new Exception("Config class {$configClass} must implement:" . ConfigInterface::class);
        }
        if (!is_subclass_of($elementClass, AbstractToolbarElement::class)) {
            throw new Exception("Element class {$elementClass} must extend:" . AbstractToolbarElement::class);
        }

        /** @var ConfigInterface $configClass */
        $config = $configClass::createFromArray($params);
        return new $elementClass($config);
    }
}
