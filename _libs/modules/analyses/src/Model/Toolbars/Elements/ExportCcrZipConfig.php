<?php

namespace Module\Analyses\Model\Toolbars\Elements;

class ExportCcrZipConfig implements ConfigInterface
{
    private string $label;
    private string $messageOnFail;
    private array $manifestParams;
    private string $cssClass;
    private string $onClick;

    public function __construct(string $label, string $messageOnFail, array $manifestParams, string $cssClass = '')
    {
        $this->setLabel($label);
        $this->setMessageOnFail($messageOnFail);
        $this->setManifestParams($manifestParams);
        $this->setCssClass($cssClass);
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    private function setLabel(string $label): void
    {
        $this->label = $label;
    }

    public function getZipFileName(): string
    {
        $manifestParams = $this->getManifestParams();
        return "{$manifestParams['InputFormCode']}-{$manifestParams['InputFormVersion']}.zip";
    }

    public function getMessageOnFail(): string
    {
        return $this->messageOnFail;
    }

    private function setMessageOnFail(string $messageOnFail): void
    {
        $this->messageOnFail = $messageOnFail;
    }

    public function getManifestParams(): array
    {
        return $this->manifestParams;
    }

    private function setManifestParams(array $manifestParams): void
    {
        $this->manifestParams = $manifestParams;
    }

    public function getCssClass(): string
    {
        return $this->cssClass;
    }

    private function setCssClass(string $cssClass): void
    {
        $this->cssClass = $cssClass;
    }

    public function getOnClick(): string
    {
        if (isset($this->onClick)) {
            return $this->onClick;
        }

        $manifestParams = $this->getManifestParams();

        $manifestContent = [
            'CsvDelimiter: ;',
            'CsvEscape:    "',
            'CsvDecimal:   .',
            'CsvEncoding:  UTF-8',
            '',
            'InputFormCode:    ' . $manifestParams['InputFormCode']??'',
            'InputFormVersion: ' . $manifestParams['InputFormVersion']??'',
            '',
            'Subject:          ' . $manifestParams['Subject']??'',
            'SubjectSend:      ' . $manifestParams['SubjectSend']??'',
            'SubjectType:      ' . $manifestParams['SubjectType']??'',
            'SubjectSendType:  ' . $manifestParams['SubjectSendType']??'',
        ];
        $gridNum = 1;
        $gridsCsvExportParams = [];
        foreach ($manifestParams['grids'] as $grid) {
            $csvFileName = "{$grid['Code']}-{$grid['Tag']}.csv";

            $manifestContent[] = '';
            $manifestContent[] = "Code.{$gridNum}:   {$grid['Code']}";
            $manifestContent[] = "Tag.{$gridNum}:    {$grid['Tag']}";
            $manifestContent[] = "File.{$gridNum}:   {$csvFileName}";
            $manifestContent[] = "Sign.{$gridNum}.1: {$csvFileName}.p7s";
            $manifestContent[] = "Records_control.{$gridNum}: {$gridNum}";

            $gridsCsvExportParams[] = [
                'gridName'     => $grid['gridName'],
                'csvFileName'  => $csvFileName,
                'skipColumns'  => ['num'],
                'colSeparator' => ';',
                'rowSeparator' => "\r\n",
            ];

            $gridNum++;
        }
        $manifestContent[] = '';
        $manifestContent[] = '# vim:fileencoding=utf-8:et:ft=dosini';
        $manifestContentText = implode('\r\n', $manifestContent);
        $gridsCsvExportParamsJson = json_encode($gridsCsvExportParams);

        $this->onClick = <<<JS
            ACcrHelper.exportCcrZip(
                '{$this->getZipFileName()}',
                'manifest.txt',
                '{$manifestContentText}',
                {$gridsCsvExportParamsJson},
                '{$this->getMessageOnFail()}'
            );
            JS;
        return $this->onClick;
    }

    public static function createFromArray(array $params): self
    {
        $requiredParams = ['label', 'messageOnFail', 'manifest'];
        $missingParams = array_diff($requiredParams, array_keys($params));
        if (count($missingParams)) {
            throw new \InvalidArgumentException('Missing required params: ' . implode(', ', $missingParams));
        }

        if (!array_key_exists('InputFormCode', $params['manifest'])
            || $params['manifest']['InputFormCode'] === ''
            || !array_key_exists('InputFormVersion', $params['manifest'])
            || $params['manifest']['InputFormVersion'] === ''
            || empty($params['manifest']['grids'])) {
            throw new \InvalidArgumentException('Missing required params: InputFormCode or InputFormVersion');
        }

        return new self($params['label'], $params['messageOnFail'], $params['manifest'], $params['cssClass']??'');
    }
}
