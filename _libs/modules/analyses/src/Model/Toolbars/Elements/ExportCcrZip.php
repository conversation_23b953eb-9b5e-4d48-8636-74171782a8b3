<?php

namespace Module\Analyses\Model\Toolbars\Elements;


use Nzoom\Renderer\SmartyRenderer;

class ExportCcrZip extends AbstractToolbarElement
{
    private ExportCcrZipConfig $config;

    private string $template = 'button.html';

    public function __construct(ExportCcrZipConfig $config)
    {
        $this->setConfig($config);

        $this->addScripts([PH_MODULES_URL . 'analyses/view/_js/jszip/jszip.min.js']);
        $this->addScripts([PH_MODULES_URL . 'analyses/view/_js/AFilesHelper.js']);
        $this->addScripts([PH_MODULES_URL . 'analyses/view/_js/ACcrHelper.js']);
    }

    public function getConfig(): ExportCcrZipConfig
    {
        return $this->config;
    }

    private function setConfig(ExportCcrZipConfig $config): void
    {
        $this->config = $config;
    }

    protected function getTemplate(): string
    {
        return $this->template;
    }

    public function getRenderer(): SmartyRenderer
    {
        $renderer = parent::getRenderer();
        $renderer->assign('element', $this);
        return $renderer;
    }

    public function getLabel(): string
    {
        return $this->getConfig()->getLabel();
    }

    public function getCssClass(): string
    {
        return $this->getConfig()->getCssClass();
    }

    public function getOnClick(): string
    {
        return $this->getConfig()->getOnClick();
    }
}
