<?php

namespace Module\Analyses\Model;

use <PERSON><PERSON><PERSON>\Analyses\Controller\AnalysesController;
use Nzoom\Navigation;
use Registry;

class SystemPlaceholdersFactory
{
    private Registry $registry;
    private Analysis $analysis;

    public function __construct(Registry $registry, Analysis $analysis)
    {
        $this->registry = $registry;
        $this->analysis = $analysis;
    }

    public function __invoke(array $dataSourcesNames = null): array
    {
        $systemPlaceholders = [
            'registry'          => $this->registry,
            'lang'              => $this->registry['lang'],
            'module_param'      => $this->registry['module_param'],
            'ph_javascript_url' => PH_JAVASCRIPT_URL,
            'ph_modules_url'    => PH_MODULES_URL,
        ];

        // TODO: move to separate function, so it can be called separately
        if (is_array($dataSourcesNames)) {
            foreach ($dataSourcesNames as $dataSourceName) {
                $url = Navigation::buildNzoomUrl(AnalysesController::MODULE, 'get_data', null, [
                    'analysis'    => $this->analysis->get('id'),
                    'data_source' => $dataSourceName,
                ]);
                // TODO: here new lines are problem when parsing YAML
                // TODO: use parameter "query" instead of sending the parameters through the url
                // TODO: Add something inside the JS to catch if data manager failed (for example: if there's no such data source)
                $systemPlaceholders["_{$dataSourceName}"] = <<<JS
                    new ej.data.DataManager({url: '{$url}', adaptor: new AnalysisDataAdaptor(), crossDomain: true })
                    JS;
            }
        }

        return $systemPlaceholders;
    }
}
