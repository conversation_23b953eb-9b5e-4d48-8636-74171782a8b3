<?php

namespace Module\Analyses\Model;

use Exception;
use Nzoom\Renderer\SmartyRendererFactory;

class AnalysesConfigRendererFactory
{
    CONST MODE_SYS = 'sys';
    CONST MODE_USR = 'usr';
    CONST MODE_DYN = 'dyn';

    /**
     * @param string $mode
     * @param array $placeholders
     * @param string $configString
     * @return string
     * @throws Exception
     */
    public function __invoke(string $mode, array $placeholders, string $configString): string
    {
        switch ($mode) {
            case self::MODE_SYS:
                $left_delimiter = '{%';
                $right_delimiter = '%}';
                break;
            case self::MODE_USR:
                $left_delimiter = '{|';
                $right_delimiter = '|}';
                break;
            case self::MODE_DYN:
                $left_delimiter = '{';
                $right_delimiter = '}';
                break;
            default:
                return $configString;
        }

        $renderer = (new SmartyRendererFactory)();
        $renderer->assign('error_reporting', true);
        $renderer->left_delimiter = $left_delimiter;
        $renderer->right_delimiter = $right_delimiter;
        $renderer->assign($placeholders);
        try {
            return (string)$renderer->fetchString($configString);
        } catch (Exception $e) {
            throw new Exception("Failed to fetch config string!", $e->getCode(), $e);
        }
    }
}
