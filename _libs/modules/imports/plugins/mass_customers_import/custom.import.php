<?php

/**
 * Custom Import class to import information from
 * a spreadsheet file that contains data for customers
 */
Class Custom_Import extends Model_Factory {

    /**
     * Set the name of the model
     */
    public static $modelName = 'Import';

    /**
     * Set an empty var for the language of the model
     */
    public static $model_lang = '';

    /**
     * Set allowed file extensions
     */
    public static $allowed_file_extensions = ['xlsx'];

    /**
     * The data, extracted from the file and prepared for import
     */
    private static $_data = [];

    /**
     * Flag to point if the import has valid data
     */
    private static $is_valid = true;


    /*
     * Vars map of each separate sheet
     */
    private static $_vars_map = [];

    /**
     * Success messages for each data row
     */
    private static $_success = [];

    /**
     * Error messages for each data row
     */
    private static $_errors = [];

    /**
     * Warning messages for each data row
     */
    private static $_warnings = [];

    /**
     * The registry
     */
    private static $_registry = [];
    /**
     * @var array $salutations List of all the salutations, value => label
     */
    private static array $salutations = [];
    /**
     * @var array The import settings
     */
    private static array $settings = [];

    /**
     * The model type taken from custom Excel properties
     */
    private static string $modelType  = '';

    /**
     * Function to do the import of the file
     *
     * @param Registry $registry - the registry
     * @return boolean
     */
    public static function import(&$registry, $params) {
        // Prepare the registry for local usage
        self::$_registry = &$registry;
        self::$settings = $params;
        self::prepareSalutations();
        // Get the database object
        $messages = $registry['messages'];
        $messages->flush();
        set_time_limit(0);
        ini_set('memory_limit', '512M');

        $registry['translater']->loadFile(PH_MODULES_DIR . 'customers/i18n/' . $registry['lang'] . '/customers.ini');

        // Make validation and prepare the data for import (add customers if needed)
        self::validate();
        self::readFileData();

        // If the file and its data are valid and there is any data prepared for import
        if (!self::$is_valid || empty(self::$_data)) {
            // No imported data
            $result = false;
            $registry['messages']->setError($registry['translater']->translate('error_no_imported_data'), '', -1);
        }

        //IMPORTANT: only can import one sheet
        self::processData();

        $logdata = array(
            'import_type'   => 'mass_customers_import',
            'file'          => $_FILES['upload_file'],
            'success'       => !empty(self::$_success),
            'log'           => array('success' => self::$_success, 'error' => self::$_errors, 'warning' => self::$_warnings)
        );
        Imports::importLog($registry, $logdata);

        // show the messages
        //$registry['messages']->flush();
        foreach (self::$_success as $success_msg) {
            $registry['messages']->setMessage($success_msg);
        }
        foreach (self::$_warnings as $warning_msg) {
            $registry['messages']->setWarning($warning_msg);
        }
        foreach (self::$_errors as $err_msg) {
            $registry['messages']->setError($err_msg);
        }

        //return false to reload filters
        return false;
    }

    /**
     * Simple function to add errors into the Messages object
     *
     * @param string $msg_key - name of label to use for translation
     * @param array|string $msg_params - optional values for replacement in label
     */
    static function error($msg_key, $msg_params = array()) {
        if (!is_array($msg_params)) {
            $msg_params = array($msg_params);
        }
        self::$_registry['messages']->setError(
            vsprintf(self::$_registry['translater']->translate($msg_key), $msg_params)
        );
    }

    /**
     * Validates the filled filters and parses the data from the excel file to self::$_data
     *
     * @return bool - if false import is not executing.
     */
    public static function validate() {
        // Get the registry
        $registry = &self::$_registry;

        // Get the messages object
        $messages = &$registry['messages'];

        // Check the file
        $file = &$_FILES['upload_file'];
        if (empty($file) || $file['error'] == 4) {
            self::error('error_no_file_for_import');
        } elseif (!empty($file['error'])) {
            self::error('error_file_upload');
        } elseif (!in_array(pathinfo($file['name'], PATHINFO_EXTENSION), self::$allowed_file_extensions)) {
            self::error('error_invalid_file',
                array(pathinfo($file['name'], PATHINFO_EXTENSION), implode(', ', self::$allowed_file_extensions)));
        }

        // If there are any errors with the file
        if ($messages->getErrors()) {
            // Validation fails
            self::$is_valid = false;
        }
        return self::$is_valid;
    }

    /**
     * Read the posted file and get the needed data
     */
    public static function readFileData() {
        if (!self::$is_valid) {
            return;
        }

        // initial validation of file upload data
        $file = &$_FILES['upload_file'];

        set_time_limit(0);
        ini_set('memory_limit', '2048M');
        require_once PH_PHPEXCEL_DIR . 'PHPExcel/IOFactory.php';
        $objReader = PHPExcel_IOFactory::createReader('Excel2007');
        /**
         * @var PHPExcel $excelFile
         */
        $excelFile = $objReader->load($file['tmp_name']);

        $sheet_names = $excelFile->getSheetNames();

        //get all named ranges
        $ranges = $excelFile->getNamedRanges();
        self::$modelType = $excelFile->getProperties()->getCustomPropertyValue('model_type');
        foreach ($sheet_names as $idx => $name) {
            $excelFile->setActiveSheetIndex($idx);

            $sheet = $excelFile->getActiveSheet();
            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();

            if (empty(self::$_vars_map)) {

                foreach ($ranges as $rng_name => $rng) {
                    if (!preg_match('#^' . preg_quote($name) . '\!#', $rng_name)) {
                        continue;
                    }

                    $cell_range = $rng->getRange();
                    if (!preg_match('#^([A-Z]+)([0-9]+)$#', $cell_range, $matches)) {
                        continue;
                    }
                    self::$_vars_map[$matches[1]] = preg_replace('#^' . preg_quote($name) . '\!(.*)$#', '$1', $rng_name);
                }
            }

            //  Loop through each row of the worksheet in turn
            for ($row = 2; $row <= $highestRow; $row++){
                //  Read a row of data into an array
                $row_data = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row,
                    NULL,
                    TRUE,
                    TRUE);
                $row_data = reset($row_data);
                $check_empty_row = array_filter($row_data);

                if (!empty($check_empty_row)) {
                    $new_row = array();
                    foreach ($row_data as $v_colmn_num => $v_val) {
                        $col = PHPExcel_Cell::stringFromColumnIndex($v_colmn_num);
                        if (!empty(self::$_vars_map[$col])) {
                            $new_row[self::$_vars_map[$col]] = $v_val;
                        }
                    }
                    self::$_data[] = $new_row;
                }
            }
        }
        return self::$_data;
    }

    /**
     * Create records from the data read in the Excel file
     */
    public static function processData() {
        $successfully_added = 0;
        $db = self::$_registry['db'];
        $basic_vars = self::getBasicVars('Customer');
        foreach (self::$_data as $rowNum => $files_vars) {
            $model = null;
            $id = $files_vars['id'] ?? '';
            if ($id) {
                if (empty(self::$settings['allowEdit'])) {
                    continue;
                }
                $model = Customers::searchOne(self::$_registry, array('where' => array('c.id = ' . $id)));
                if (!$model) {
                    self::$_warnings[] = sprintf(self::$_registry['translater']->translate('warning_customer_not_found'), $rowNum + 1);
                    continue;
                }
                $customer_type = $model->get('type');
            } elseif(!empty($files_vars['type'])) {
                $customer_type = self::$modelType;
                if (empty($customer_type)) {
                    $customer_type = $db->getOne('SELECT parent_id FROM ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' WHERE name="' . General::strftime($files_vars['type']) . '"');
                    if (!$customer_type) {
                        self::$_warnings[] = sprintf(self::$_registry['translater']->translate('warning_customer_type_not_found'), $rowNum + 1);
                        continue;
                    }
                }

                if ($files_vars['is_company'] === '') {
                    self::$_warnings[] = sprintf(self::$_registry['translater']->translate('warning_customer_is_company_not_filled'), $rowNum + 1);
                    continue;
                }

                // create the model
                $params = array(
                    'type'       => $customer_type,
                    'group'      => 1,
                    'model_lang' => self::$_registry['config']->getParam('i18n', 'default_lang')
                );
                $model = new Customer(self::$_registry, $params);
            }

            if (!$model) {
                self::$_warnings[] = sprintf(self::$_registry['translater']->translate('warning_customer_not_found'), $rowNum + 1);
                continue;
            }

            $old_model = clone $model;
            $assoc_vars = $model->getAssocVars();

            foreach ($files_vars as $v_name => $v_val) {
                $stripped_var_name = $v_name;

                if (in_array($stripped_var_name, array('id', 'type', 'subtype'))) {
                    continue;
                } elseif (in_array($stripped_var_name, $basic_vars)) {
                    self::processBasicVarValue($model, $stripped_var_name, $v_val);
                } elseif(array_key_exists($stripped_var_name, $assoc_vars)) {

                    switch ($assoc_vars[$stripped_var_name]['type']) {
                        case 'autocompleter':
                            $import_table = new Imports_Table(
                                self::$_registry,
                                array(
                                    'persist_phpexcel' => true,
                                    'model' => $model->modelName,
                                    'model_type' => $model->get('type'),
                                    'grouping' => 0,
                                    'column_file' => array(),
                                    'column_table' => $files_vars
                                )
                            );
                            $import_table->prepareAutocompleteValues($stripped_var_name, $assoc_vars, $files_vars);
                            foreach($files_vars as $fvar => $fvalue) {
                                if (array_key_exists($fvar, $assoc_vars)) {
                                    $assoc_vars[$fvar]['value'] = $fvalue;
                                }
                            }
                            break;
                        case 'date':
                            $files_vars[$v_name] = (!empty($v_val) ? General::strftime('%Y-%m-%d', $v_val) : '');
                            break;
                        case 'dropdown':
                        case 'radio':
                            $match_value = trim($v_val);
                            $value_found = '';
                            foreach ($assoc_vars[$stripped_var_name]['options'] as $opt) {
                                if (!strcasecmp($opt['label'], $match_value)) {
                                    $value_found = $opt['option_value'];
                                    break;
                                }
                            }
                            $files_vars[$v_name] = $value_found;
                            break;
                    }
                    $assoc_vars[$v_name]['value'] = $files_vars[$v_name];
                }
            }

            self::$_registry['db']->StartTrans();

            // set currencies and currency price
            $model->set('vars', array_values($assoc_vars), true);
            $model->set('assoc_vars', $assoc_vars, true);

            //ignore permissions of layouts
            self::$_registry->set('edit_all', true, true);

            if ($model->save()) {
                // write history for the main vars
                $filters = array('where'      => array('c.id = ' . $model->get('id')),
                    'model_lang' => $model->get('model_lang'));
                $new_model = Customers::searchOne(self::$_registry, $filters);
                self::$_registry->set('get_old_vars', true, true);
                $new_model->getVars();

                Customers_History::saveData(
                    self::$_registry,
                    array(
                        'model' => $new_model,
                        'action_type' => $id ? 'edit' : 'add',
                        'new_model' => $new_model,
                        'old_model' => $old_model
                    )
                );

                self::$_registry->set('get_old_vars', false, true);
            } else {
                self::$_errors[] = sprintf(self::$_registry['translater']->translate('error_customer_add_failed'), $model->get('code'));
                self::$_registry['db']->FailTrans();
            }

            self::$_registry['request']->remove('update_categories');

            if (!self::$_registry['db']->HasFailedTrans()) {
                $successfully_added++;
            }
            self::$_registry['db']->CompleteTrans();
        }

        if ($successfully_added) {
            self::$_success[] = sprintf(self::$_registry['translater']->translate('message_successful_import'), $successfully_added);
        }

        return true;
    }

    /*
     * Function to get the basic vars based on the columns of the main tables for the selected model
     *
     * @param string $model_name - the model name where we want to take the vars from
     * @return array $basic_vars - the names of the columns of the main table (+ the columns of the I18N table)
     */
    public static function getBasicVars($model_name) {
        $basic_vars = array();
        $model_name = General::singular2plural($model_name);

        if (defined('DB_TABLE_' . strtoupper($model_name))) {
            // get the columns of the main table
            $sql = 'SHOW COLUMNS FROM ' . constant('DB_TABLE_' . strtoupper($model_name));
            $columns_base_table = self::$_registry['db']->getAll($sql);
            $basic_vars = array_merge($basic_vars, array_column($columns_base_table, 'Field'));
        }

        if (defined('DB_TABLE_' . strtoupper($model_name) . '_I18N')) {
            // get the columns of the i18n table
            $sql = 'SHOW COLUMNS FROM ' . constant(strtoupper('DB_TABLE_' . strtoupper($model_name) . '_I18N'));
            $columns_base_table = self::$_registry['db']->getAll($sql);
            $basic_vars = array_merge($basic_vars, array_column($columns_base_table, 'Field'));
        }

        return $basic_vars;
    }

    public static function processBasicVarValue(&$model, $varName, $varVal)
    {

        if (in_array($varName, ['gsm', 'phone', 'email', 'web', 'skype', 'fax', 'othercontact'])) {
            $processedVal = '';
            if (!empty($varVal)) {
                $split = explode(',', $varVal);
                $processedVal = array_map('trim', $split);
            }
            $model->set($varName, $processedVal, true);
        } elseif ($varName === 'name' && $model->get('is_company') === '0') {
            $split = explode(' ', $varVal);
            $salutation = array_search($split[0], self::$salutations);
            if ($salutation) {
                $model->set('salutation', $salutation, true);
                unset($split[0]);
            }
            $lastName = array_pop($split);
            $model->set('lastname', $lastName, true);
            $model->set($varName, implode(' ', $split), true);

        } elseif ($varName === 'department') {
            $department = self::findDepartment($varVal, $model->get('department'));
            $model->set($varName, $department, true);
        } elseif (preg_match('/^\d{2}\.\d{2}\.\d{4}$/', $varVal)) {
            $dt = DateTime::createFromFormat('d.m.Y', $varVal);
            $model->set($varName, $dt->format('Y-m-d'), true);
        } elseif (preg_match('/^\d{2}\.\d{2}\.\d{4} (?:[01]\d|2[0-3]):[0-5]\d$/', $varVal)) {
            $dt = DateTime::createFromFormat('d.m.Y H:i', $varVal);
            $model->set($varName, $dt->format('Y-m-d H:i:s'), true);
        } else {
            $model->set($varName, (string) $varVal, true);
        }

    }

    public static function findDepartment($varVal, $default = '')
    {
        $department = Departments::searchOne(self::$_registry, [
            'where' => [
                sprintf("di18n1.name = '%s'", $varVal),
                sprintf("di18n1.lang = '%s'", self::$_registry['lang']),
            ],
        ]);
        return $department ? $department->get('id') : $default;
    }

    public static function prepareSalutations()
    {
        $temp = ['mr', 'mrs', 'ms'];
        foreach ($temp as $t) {
            self::$salutations[$t] = self::$_registry['translater']->translate("salutation_$t");;
        }
    }

}
