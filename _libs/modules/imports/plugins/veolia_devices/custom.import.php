<?php
include_once __DIR__ . '/models/DeviceMeasurementImportSettings.php';
/**
 * Custom import of (main or individual) "Devices" nomenclatures for installation VEOLIA
 */
class Custom_Import extends Model_Factory
{

    /**
     * Set allowed file extensions
     */
    public static $allowed_file_extensions = array('csv', 'xls', 'xlsx');
    public static $siemens_id;
    public static $settings;
    public static $params;
    /**
     * The registry
     */
    private static Registry $_registry;
    private static $docIdMap = [
        'main' => "8",
        'distance' => "6",
    ];
    private static $docClosedStatus = [
        'main' => "12",
        'distance' => "15",
    ];
    private static $docOpenedStatus = [
        'main' => "11",
        'distance' => "14",
    ];
    private static $siemensTypes = [
        "heat" => "2",
        "water" => "1",
    ];
    private static $rowsLogText;
    private static array $rowsLog = [];
    private static $logTypesMsgMethods = [
        'required' => 'setError',
        'invalid' => 'setError',
        'noStationFound' => 'setError',
        'notFound' => 'setError',
        'noNomenclatureFound' => 'setError',
        'duplicated' => 'setWarning',
        'exist' => 'setWarning',
        'failed' => 'setError',
        'added' => 'setMessage',
        'addedMain' => 'setMessage',
    ];

    public static function import($registry, $params)
    {
        self::$_registry = &$registry;
        self::$settings = array_flip($params);
        self::$params = $params;
        self::$siemens_id = $params['nom_siemens_id'];
        // Get the database object
        //$db = $registry['db'];
        $request = &$registry['request'];
        $db = &$registry['db'];

        set_time_limit(0);
        ini_set('memory_limit', $params['memory_limit']);

        // Include necessary files for documents (used in this method and in the validate() method too)
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';


        // Set a flag for the result
        $result = true;
        $isValid = self::validate();
        $log_arr = array('tests' => array());
        if ($isValid) {
            $file = &$_FILES['upload_file'];
            $pathExt = pathinfo($file['name'], PATHINFO_EXTENSION);
            $subimport_type = $request->get("subimport_type");
            if ($subimport_type === 'distance') {
                $manufacturer = $request->get('manufacturer');
                if ($manufacturer === self::$siemens_id) {
                    $key = "nom_siemens_id_{$request->get("siemens_type")}";
                } else {
                    $key = self::$settings[$manufacturer];
                }
                /**
                 * @var $nomSettings DeviceMeasurementImportSettings
                 */
                $nomSettings = include "settings/$key.php";
            }
            $validDataFormat = true;
            switch ($pathExt) {
                case "csv":
                    $rawData = [];
                    $handle = fopen($file['tmp_name'], "r");
                    $row = 0;
                    $separator = ";";
                    //read csv data
                    while (($rawData[$row++] = fgetcsv($handle, 0, $separator)) !== FALSE) {
                    }
                    break;
                default:
                    $sr = new Spreadsheet_Manager($file['tmp_name'], array('take_only_data' => true));
                    if (!$sr->selectWorksheet(0)) {
                        self::error('error_select_worksheet');
                        return false;
                    }
                    if ($subimport_type === 'distance') {
                        $firstCol = $nomSettings->getStartCol();
                        $lastCol = $nomSettings->getLastCol();
                        $startRow = $nomSettings->getStartRow();
                        // Get the data from the sheet
                        $rawData = $sr->readActiveCellsBetweenColumnsByRows($firstCol, $lastCol, $startRow, '', 'row');
                    }
            }
            if ($nomSettings ?? false) {
                $validDataFormat = self::checkDataFormat($nomSettings, $sr ?? $rawData ?? false);
            }

            if (!$validDataFormat){
                self::error('error_invalid_data_format');
                return false;
            }

            $document_type_id = self::$docIdMap[$subimport_type];

            $filters = array(
                'where' => array(
                    'd.type = ' . $document_type_id,
                    'd.active = 1',
                    'd.deleted = 0',
                    "d.status = 'opened'",
                    'd.date = "' . $request->get('date') . '"',
                    'a__report_period_id = ' . $request->get('report_period'),
                )
            );
            if ($subimport_type === "distance") {
                $filters['where'][] = 'a__brands_id = ' . $request->get('manufacturer');
                if($manufacturer === self::$siemens_id){
                    $filters['where'][] = 'a__siemens_import_type = ' . self::$siemensTypes[$request->get('siemens_type')];
                }
            }
            $document = Documents::searchOne($registry, $filters);
            $addNewDoc = true;
            if ($document) {
                $addNewDoc = false;
            }

            $gtValues = [];
            switch ($subimport_type) {
                case "main":
                    $gtValues = self::importMain($sr);
                    break;
                case "distance":
                    $gtValues = self::importDistance($rawData ?? [], $nomSettings ?? []);
                    break;
            }


            if ($addNewDoc) {
                $action = 'add';
                // Get document type
                $filters = array('where' => array('dt.id = \'' . $document_type_id . '\'',
                    'dt.active = 1'),
                    'sanitize' => true);
                $document_type = Documents_Types::searchOne($registry, $filters);
                // Add this document
                $document = Documents::buildModel($registry);

                //sets the properties of the type to the current model
                $document->set('generate_system_task', $document_type->get('generate_system_task'), true);
                $document->set('direction', $document_type->get('direction'), true);
                $document->set('type', $document_type_id, true);
                $document->set('type_name', $document_type->get('name'), true);
                $document->set('name', $document_type->get('default_name'), true);
                $document->set('customer', $document_type->get('default_customer'), true);
                $document->set('group', $document_type->getDefaultGroup(), true);
                $document->set('department', $document_type->getDefaultDepartment(), true);
                $document->set('date', $request->get('date'), true);
                $document->set('employee', $registry['currentUser']->get('employee'), true);

                // build a blank model, just set the type to get the additional variables
                $old_document = new Document($registry, array('type' => $document_type_id));
            } else {
                $action = 'edit';
                $old_document = clone $document;
                $document->set('active', 1, true);
                $document->set('deleted', 0, true);
                $document->set('deleted_by', 0, true);
            }


            // get additional vars and values:
            $registry->set('get_old_vars', true, true);
            $old_document->getVars();

            //get assoc vars
            $assocVars = $document->getAssocVars();

            if ($action === 'add') {
                //set the report period
                $assocVars['report_period_id']['value'] = $request->get('report_period');
                $assocVars['report_period']['value'] = $request->get('report_period_autocomplete');
                if ($subimport_type === "distance") {
                    $assocVars['brands_id']['value'] = $request->get('manufacturer');
                    $assocVars['brands']['value'] = $request->get('manufacturer_autocomplete');
                    if($manufacturer === self::$siemens_id){
                        $assocVars['siemens_import_type']['value'] = self::$siemensTypes[$request->get('siemens_type')];
                    }
                }
            }
            foreach ($assocVars as $var) {
                if (array_key_exists($var['name'], $gtValues)) {
                    $var['value'] = $gtValues[$var['name']];
                }
                $assocVars[$var['name']] = $var;
            }
            $assocVars['indication_grp']['values'] = $gtValues;
            $registry->set('edit_all', true, true);
            $document->set('vars', array_values($assocVars), true);
            // Load the documents language file
            $document->loadI18NFiles(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');

            $document->slashesEscape();

            // Start a transaction
            $db->StartTrans();
            if ($document->save()) {
                $filters = array(
                    'where' => array(
                        'd.id = ' . $document->get('id')
                    ),
                    'model_lang' => $document->get('model_lang'),
                    'skip_assignments' => true,
                    'skip_permissions_check' => true
                );
                $new_document = Documents::searchOne($registry, $filters);
                $registry->set('get_old_vars', true, true);
                $new_document->getVars();
                $new_document->set('status', "opened", true);
                $new_document->set('substatus', "opened_" . self::$docOpenedStatus[$subimport_type], true);
                $new_document->setStatus();
                Documents_History::saveData($registry,
                    array(
                        'model' => $document,
                        'action_type' => $action,
                        'new_model' => $new_document,
                        'old_model' => $old_document
                    )
                );

                if ($action == 'add' && !$new_document->defaultAssign(true, false)) {
                    $db->FailTrans();
                }

                if (!$db->HasFailedTrans()) {

                    /////////////////////////////////////
                    // Execute action type automations //
                    /////////////////////////////////////
                    $get_old_vars = $registry->get('get_old_vars');

                    // Temporary change the module name and the action for correct execution of the automations
                    $registry->set('module', 'documents', true);
                    $registry->set('controller', 'documents', true);
                    $registry->set('action', 'add', true);

                    // Build an automation model
                    include_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php';
                    $automation = new Automations_Controller($registry);

                    // Set to get vars from the DB (as it is when submitting the documents add page)

                    // Perform action type automations
                    $registry_controller = $registry['controller'];
                    $registry->set('controller', $registry['module'], true);
                    $automation->performAutomation($new_document, $old_document);
                    $registry->set('controller', $registry_controller, true);

                    // Set back the value of the get_old_vars flag
                    $registry->set('get_old_vars', $get_old_vars, true);

                    // Change back the module name and the action
                    $registry->set('module', 'imports', true);
                    $registry->set('controller', 'imports.controller.php', true);
                    $registry->set('action', 'index', true);
                    /////////////////////////////////////////////
                    // END OF: Execute action type automations //
                    /////////////////////////////////////////////

                }
            } else {
                $db->FailTrans();
            }
            // If everything is OK
            $dbTransError = $db->HasFailedTrans();

            if (empty($dbTransError)) {
                // Raise a success message
                //set success message
                $document_url = sprintf('%s?%s=documents&amp;documents=view&amp;view=%s',
                    $_SERVER['PHP_SELF'], $registry['module_param'], $document->get('id'));
                $registry['messages']->setMessage(sprintf($registry['translater']->translate('message_successful_import'), $document_url, $document->get('name'), $document->get('full_num')), '', 0);
            } else {
                // Raise an error message
                $registry['messages']->setError($registry['translater']->translate('error_no_imported_data'), '', -1);
            }

            $db->CompleteTrans();

        } else {
            $result = false;
            $registry['messages']->setError($registry['translater']->translate('error_no_imported_data'), '', -1);
        }
        foreach (self::getRowsLog() as $typeKey => $type) {
            $method = $type['msgMethod'];
            $rows = implode(', ', $type['rows']);
            $msg = "{$type['name']}: {$rows}";
            $registry['messages']->$method($msg);
        }
        $logdata = array(
            'import_type' => 'veolia_devices',
            'file' => $_FILES['upload_file'],
            'success' => intval($result),
            'log' => self::getRowsLogForImportLog(),
        );
        Imports::importLog($registry, $logdata);


        //return false to reload filters
        return false;
    }

    /**
     * Prepares the filters according to the needs of this plugin
     *
     * @param object $registry - the main registry
     */
    public function processFilters(&$registry, &$viewer = null)
    {
        $import_plugin = Imports::searchOne($registry, array('name' => $registry['request']->get('import_type')));
        $import_plugin->loadPreparedSettings();
        $settings = $import_plugin->get('settings');
        $viewer->data['subimport_type_options'] = [
            [
                'label' => $registry['translater']->translate('imports_subimport_type_main'),
                'option_value' => 'main',
            ],
            [
                'label' => $registry['translater']->translate('imports_subimport_type_distance'),
                'option_value' => 'distance',
            ]
        ];
        $viewer->data['siemens_id'] = $settings['nom_siemens_id'];
        $viewer->data['report_period'] = [
            'type' => 'nomenclatures',
            'fill_options' => array(
                '$report_period => <id>',
                '$report_period_autocomplete => <name>'
            ),
            'suggestions' => '<name>',
            'search' => [
                '<name>'
            ],
            'combobox' => '1',
            'combobox_mode' => 'empty',
            'sort' => [
                '<code> DESC'
            ],
            'filters' => array(
                '<type>' => '24',
            ),
            'url' => $_SERVER['PHP_SELF'] . '?' . $registry['module_param'] . '=nomenclatures&nomenclatures=ajax_select',
            'buttons' => 'clear'
        ];

        $viewer->data['manufacturer'] = [
            'type' => 'nomenclatures',
            'fill_options' => array(
                '$manufacturer => <id>',
                '$manufacturer_autocomplete => <name>'
            ),
            'suggestions' => '<name>',
            'search' => '<name>',
            'combobox' => '1',
            'combobox_mode' => 'empty',
            'filters' => array(
                '<type>' => '25',
                '<tag>' => '2,3',
            ),
            'url' => $_SERVER['PHP_SELF'] . '?' . $registry['module_param'] . '=nomenclatures&nomenclatures=ajax_select',
            'buttons' => 'clear'
        ];
        $viewer->data['siemens_type_options'] = [
            [
                'label' => $registry['translater']->translate('imports_siemens_type_water'),
                'option_value' => 'water',
            ],
            [
                'label' => $registry['translater']->translate('imports_siemens_type_heat'),
                'option_value' => 'heat',
            ]
        ];

    }

    static function validate()
    {
        // Get the registry
        $registry = &self::$_registry;
        $request = &$registry['request'];


        // Get the messages object
        $messages = &$registry['messages'];

        // Check the file
        $file = &$_FILES['upload_file'];
        if (empty($file) || $file['error'] == 4) {
            self::error('error_no_file_for_import');
        } elseif (!empty($file['error'])) {
            self::error('error_file_upload');
        } elseif (!in_array(pathinfo($file['name'], PATHINFO_EXTENSION), self::$allowed_file_extensions)) {
            self::error('error_invalid_file', array(pathinfo($file['name'], PATHINFO_EXTENSION), implode(', ', self::$allowed_file_extensions)));
        }

        //check the subimport
        if (!$request->get('subimport_type')) {
            $registry['messages']->setError($registry['translater']->translate('error_invalid_subimport_type'), 'subimport_type');
        } else {
            if ($request->get('subimport_type') == 'distance') {
                if (!$request->get('manufacturer')) {
                    $registry['messages']->setError($registry['translater']->translate('error_invalid_manufacturer'), 'manufacturer');
                } else if ($request->get('manufacturer') === self::$siemens_id) {
                    if (!$request->get('siemens_type')) {
                        $registry['messages']->setError($registry['translater']->translate('error_invalid_siemens_type'), 'siemens_type');
                    }
                }
            }
        }

        if (!$request->get('date')) {
            $registry['messages']->setError($registry['translater']->translate('error_invalid_date'), 'date');
        }
        if (!$request->get('report_period')) {
            $registry['messages']->setError($registry['translater']->translate('error_invalid_report_period'), 'report_period');
        }


        // If there are any errors with the file
        if ($messages->getErrors()) {
            // Validation fails
            return false;
        }

        return true;
    }

    /**
     * Simple function to add errors into the Messages object
     *
     * @param string $msg_key - name of label to use for translation
     * @param array|string $msg_params - optional values for replacement in label
     */
    static function error($msg_key, $msg_params = array())
    {
        if (!is_array($msg_params)) {
            $msg_params = array($msg_params);
        }
        self::$_registry['messages']->setError(
            vsprintf(self::$_registry['translater']->translate($msg_key), $msg_params)
        );
    }

    static function importMain($spreadsheet): array
    {

        $registry = &self::$_registry;
        $firstCol = 'A';
        $lastCol = "F";
        $startRow = '2';
        $indications = [
            "D" => '16',
            "E" => '17',
            "F" => '18',
        ];

        // Get the data from the sheet
        $rawData = $spreadsheet->readActiveCellsBetweenColumnsByRows($firstCol, $lastCol, $startRow, '', 'row');
        $gtValues = [];
        foreach ($rawData as $rowNum => $rowData) {
            if (empty(array_filter($rowData))) {
                continue;
            }

            $rowData = array_map("trim", $rowData);
            if (is_float($rowData['B']) || is_int($rowData['B'])) {
                $code = intval($rowData['B']);
                $rowData['B'] = "{$code}";
            }
            $name = General::slashesEscape($rowData['A']);
            $station = Nomenclatures::searchOne($registry, ["skip_permissions_check" => true, "where" => [
                "n.type = 15",
                "a__psiro_code = \"{$rowData['B']}\"",
            ]]);

            if (!$station) {
                self::logRow('noStationFound', $rowNum);
                if (self::$params['skipNotFound']  ?? false){
                    continue;
                }
            }
            $unixTimestamp = !empty($rowData['C']) ? ($rowData['C'] - 25569) * 86400 : strtotime(self::$_registry['request']->get('date'));
            foreach ($indications as $col => $nomType) {
                if ($rowData[$col] === 'NULL') {
                    $rowData[$col] = '';
                }
                $device = false;
                if ($station) {
                    $device = Nomenclatures::searchOne($registry, [
                        "skip_permissions_check" => true,
                        "where" => [
                            "n.type = {$nomType}",
                            "a__to_station_id = {$station->get('id')}",
                            'a__nom_status IN (1,2)',
                        ]
                    ]);
                }

                if ($device) {
                    $oldIndication = $device->getAdditionalVarValue('indication_new');
                    $gtValues['device_id'][] = $device->get('id');
                    $gtValues['device'][] = $device->get('name');
                    $gtValues['device_psiro_code'][] = $device->get('code');
                    $gtValues['indication_old'][] = $oldIndication ?: "";
                    self::logRow('addedMain', $rowNum . "-" . $col);
                } else {
                    $gtValues['device_id'][] = "";
                    $gtValues['device'][] = "";
                    $gtValues['device_psiro_code'][] = "";
                    $gtValues['indication_old'][] = "";
                    self::logRow("noNomenclatureFound", $rowNum . "-" . $col);
                }

                $gtValues['to_station_id'][] = $station ? $station->get('id') : "";
                $gtValues['to_station'][] = $station ? $station->get('name') : "";
                $gtValues['to_station_psiro_code'][] = $station ? $station->getAdditionalVarValue('psiro_code') : $rowData['A'];
                $gtValues['indication_new'][] = $rowData[$col];
                $gtValues['indication_date'][] = date('Y-m-d', $unixTimestamp);
                $gtValues['indication_alert'][] = "";
                $gtValues['indication_deviation'][] = "";
            }

        }
        return $gtValues;
    }

    static function importDistance($rawData, DeviceMeasurementImportSettings $nomSettings): array
    {
        $registry = &self::$_registry;
        $gtValues = [];
        if ($nomSettings->hasFilterMethod()) {
            $rawData = $nomSettings->filterData($rawData);
        }
        foreach ($rawData as $rowNum => $rowData) {
            $rowData = array_map("trim", $rowData);
            $dateVal = $rowData[$nomSettings->getDateColumn()];

            if (is_int($dateVal) || is_float($dateVal)) {
                $dateStr = strval(intval($dateVal));
            }
            $dateObj = DateTime::createFromFormat($nomSettings->getDateFormat(), $dateStr ?? $dateVal);

            try {
                $device = $nomSettings->searchNomenclature($registry, $rowData);
            } catch (TooManyDevicesFoundException $e) {
                $device = false;
            }
            if (!$device) {
                self::logRow("noNomenclatureFound", $rowNum);
                if (self::$params['skipNotFound'] ?? false){
                    continue;
                }
            }
            if ($dateObj) {
                $date = $dateObj->format("Y-m-d");
            } else {
                if (is_numeric($dateVal)) {
                    $unixTimestamp = (intval($dateVal) - 25569) * 86400;
                    $date = date('Y-m-d', $unixTimestamp);
                }
            }
            $gtValues['indication_date'][] = $date ?? "";
            foreach ($nomSettings->getFillMapping() as $var => $col) {
                if (strlen($col) > 1 && $device) {
                    $data = $device->get($col);
                    if (!$data) {
                        $data = $device->getAdditionalVarValue($col);
                    }
                } else {
                    $data = $rowData[$col] ?? "";
                }
                if ($device && $var === 'device_serial_num') {
                    $gtValues[$var][] = "";
                } else {
                    $gtValues[$var][] = $data;
                }
            }

        }
        return $gtValues;
    }

    private static function checkDataFormat(DeviceMeasurementImportSettings $settings, $data)
    {
        if (!$data) {
            return false;
        }
        $expected = $settings->getHeaderColumns();
        if (is_array($data)) {
            $target = $data[0];
            if (count($target) > 10) {
                $target = mb_convert_encoding($target, "UTF-8", "CP1251");
            }
        } else {
            $row = $settings->getHeaderRow();
            $target = $data->readCells(sprintf("%s{$row}", $settings->getStartCol()), sprintf("%s{$row}", $settings->getLastCol()), 'row')[$row];
        }
        return count(array_diff($expected, $target)) === 0;
    }

    private static function getRowsLogForImportLog(): array
    {
        if (self::$rowsLogText) {
            return self::$rowsLogText;
        }
        $rowsLogText = [];
        $rowsLog = self::getRowsLog();
        foreach ($rowsLog as $id => $type) {
            $rowsLogText[$type['name']] = [];
            $rowsLogText[$type['name']] = " " . implode(",", $type['rows']) . "\n";
        }
        self::$rowsLogText = $rowsLogText;

        return self::$rowsLogText;
    }

    private static function getRowsLog(): array
    {
        // Move added to beginning of array
        if (array_key_exists('added', self::$rowsLog)) {
            self::$rowsLog = array_merge(['added' => self::$rowsLog['added']], self::$rowsLog);
        }
        return self::$rowsLog;
    }

    private static function logRow($type, $rowNum)
    {
        if (!array_key_exists($type, self::$rowsLog)) {
            self::$rowsLog[$type] = [
                'name' => self::$_registry['translater']->translate("rows_log_type_{$type}") ?: $type,
                'msgMethod' => self::$logTypesMsgMethods[$type] ?? 'setError',
                'rows' => [],
            ];
        }
        if (!in_array($rowNum, self::$rowsLog[$type]['rows'])) {
            self::$rowsLog[$type]['rows'][] = $rowNum;
        }

    }

}
