<?php
ini_set("soap.wsdl_cache_enabled", 0);

return array(
    'loaderPath' => array(
        __DIR__ . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'vendor',
        __DIR__ . DIRECTORY_SEPARATOR . '..'
    ),
    'templatePath' => __DIR__ . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'App' .DIRECTORY_SEPARATOR . 'View' . DIRECTORY_SEPARATOR,
    'masterLayout' => __DIR__ . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'App' .DIRECTORY_SEPARATOR . 'View' . DIRECTORY_SEPARATOR . 'master.phtml',
    'public_url_noprotocol' => '//' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']),
    'pic_thumbnail_width' => 150,
    'pic_thumbnail_height' => 150,
    'pic_max_width' => 900,
    'pic_max_height' => 450,
    'soap' => array(
        'url' => 'http://' . $_SERVER['HTTP_HOST'] . '/vistaawt/soap/vistaawt/getWSDL',
        'headers' => array(
            //local
            'login'            => 'bgs_admin',
            'password'         => sha1('Bgs12345'),
            //remote (special no permissions user)
            //'login'            => 'services',
            //'password'         => '045cfa64caba60585f115d738b6599646c5d14bc', //hIMOIkCL
            'allow_self_signed' => true,
            'verify_peer' => false,
            'authentication'   => SOAP_AUTHENTICATION_DIGEST,
            'cache_wsdl'       => WSDL_CACHE_NONE,
            'features'         => SOAP_SINGLE_ELEMENT_ARRAYS,
            'stream_context' => stream_context_create(array(
                'ssl' => array(
                    // set some SSL/TLS specific options
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            ))
        ),
        'methods' => array(
            'login' => 'nzLogin',
            'getOffers' => 'nzGetOffers',
            'addBid' => 'nzAddBid',
        ),
    ),
    'offset' => 0,
    'nzDateFormat' => 'Y-m-d H:i:s',
    'portalDateFormat' => 'd.m.Y H:i:s',
    'limit' => 5,
);