<?php

class Tasks_Audit extends Audit {
    //module name
    public static $module = 'tasks';

    /**
     * prepare audit data for saving
     *
     * @return bool
     */
    public static function prepareData(&$registry, &$params) {

        //replace ids with labels for basic variables
        $replace_labels = array('customer'  => 'customer_name',
                                'project'   => 'project_name',
                                'department'=> 'department_name',
                                'status'    => 'status_name',
                                'substatus' => 'substatus_name',
                                'trademark' => 'trademark_name'
                                );
        $basic_vars = parent::getBasicAuditVars($registry, self::$module);

        $audit_vars = array();

        switch ($params['action_type']) {
        case 'add':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    if ($params['model']->get($var)) {
                        $audit_vars[$i]['field_name'] = $var;
                        $audit_vars[$i]['field_value'] = $params['model']->get($var);
                        $audit_vars[$i]['old_value'] = '';
                        if (isset($replace_labels[$var])) {
                            $audit_vars[$i]['label'] = $params['model']->get($replace_labels[$var]);
                        } else {
                            switch ($var) {
                                case 'active':
                                    $audit_vars[$i]['label'] = ($params['model']->get('active') ? ($registry['translater']->translate('activated')) : $registry['translater']->translate('deactivated'));
                                    break;
                                case 'severity':
                                    $audit_vars[$i]['label'] = $registry['translater']->translate('tasks_' . $params['model']->get('severity'));
                                    break;
                                case 'ownership':
                                    $audit_vars[$i]['label'] = $registry['translater']->translate('tasks_ownership_' . $params['model']->get($var));
                                    $audit_vars[$i]['old_value'] = $registry['translater']->translate('tasks_ownership_' . $params['old_model']->get($var));
                                    break;
                                default:
                                    $audit_vars[$i]['label'] = $params['model']->get($var);
                                    break;
                            }
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                    }
                }
            }
            break;
        case 'multiadd':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    if ($params['model']->get($var)) {
                        $audit_vars[$i]['field_name'] = $var;
                        $audit_vars[$i]['field_value'] = $params['model']->get($var);
                        $audit_vars[$i]['old_value'] = '';
                        if (isset($replace_labels[$var])) {
                            $audit_vars[$i]['label'] = $params['model']->get($replace_labels[$var]);
                        } else {
                            switch ($var) {
                                case 'active':
                                    $audit_vars[$i]['label'] = ($params['model']->get('active') ? ($registry['translater']->translate('activated')) : $registry['translater']->translate('deactivated'));
                                    break;
                                case 'severity':
                                    $audit_vars[$i]['label'] = $registry['translater']->translate('tasks_' . $params['model']->get('severity'));
                                    break;
                                default:
                                    $audit_vars[$i]['label'] = $params['model']->get($var);
                                    break;
                            }
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                    }
                }
            }

            $vars = $params['model']->get('vars');
            $i = count($audit_vars);
            if (is_array($vars) && count($vars)) {
                foreach ($vars as $var) {
                    if ($var['auditable']) {
                        $audit_vars[$i]['field_name'] = $var['name'];
                        $audit_vars[$i]['field_value'] = $var['value'];
                        $audit_vars[$i]['old_value'] = '';
                        if (!empty($var['options'])) {
                            foreach ($var['options'] as $opt) {
                                if ($opt['option_value'] == $var['value']) {
                                    $audit_vars[$i]['label'] = $opt['label'];
                                    break;
                                }
                            }
                        } else {
                            $audit_vars[$i]['label'] = $var['value'];
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_ADDITIONAL;
                        $i++;
                    }
                }
            }
            break;
        case 'translate':
        case 'edit':
        case 'status':
        case 'multistatus':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    if ($var == 'status') {
                        if ($params['old_model']->get('status') != $params['model']->get('status')) {
                            $audit_vars[$i]['field_name'] = 'status';
                            $audit_vars[$i]['field_value'] = $params['model']->get('status');
                            $audit_vars[$i]['label'] = ($params['model']->get('status') ? ($registry['translater']->translate('tasks_status_' . $params['model']->get('status'))) : ('-'));
                            $audit_vars[$i]['old_value'] = ($params['old_model']->get('status') ? ($registry['translater']->translate('tasks_status_' . $params['old_model']->get('status'))) : ('-'));
                            $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                            $i++;
                        }
                    } elseif ($var == 'substatus') {
                        if (($params['old_model']->get('substatus') != $params['model']->get('substatus')) || (($params['old_model']->get('substatus') == $params['model']->get('substatus')) && ($params['old_model']->get('status') != $params['model']->get('status')))) {
                            $audit_vars[$i]['field_name'] = 'substatus';
                            $audit_vars[$i]['field_value'] = $params['model']->get('substatus');
                            $audit_vars[$i]['label'] = ($params['model']->get('substatus') ? ($params['model']->get('substatus_name')) : ('-'));
                            $audit_vars[$i]['old_value'] = ($params['old_model']->get('substatus') ? ($params['old_model']->get('substatus_name')) : ('-'));
                            $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                            $i++;
                        }
                    } elseif ($params['model']->get($var) != $params['old_model']->get($var)) {
                        $audit_vars[$i]['field_name'] = $var;
                        $audit_vars[$i]['field_value'] = $params['model']->get($var);
                        if (isset($replace_labels[$var])) {
                            $audit_vars[$i]['label'] = $params['model']->get($replace_labels[$var]);
                            $audit_vars[$i]['old_value'] = $params['old_model']->get($replace_labels[$var]);
                        } else {
                            switch ($var) {
                                case 'active':
                                    $audit_vars[$i]['label'] = ($params['model']->get('active') ? ($registry['translater']->translate('activated')) : $registry['translater']->translate('deactivated'));
                                    $audit_vars[$i]['old_value'] = ($params['old_model']->get('active') ? ($registry['translater']->translate('activated')) : $registry['translater']->translate('deactivated'));
                                    break;
                                case 'severity':
                                    $audit_vars[$i]['label'] = $registry['translater']->translate('tasks_' . $params['model']->get('severity'));
                                    $audit_vars[$i]['old_value'] = $registry['translater']->translate('tasks_' . $params['old_model']->get('severity'));
                                    break;
                                case 'ownership':
                                    $audit_vars[$i]['label'] = $registry['translater']->translate('tasks_ownership_' . $params['model']->get($var));
                                    $audit_vars[$i]['old_value'] = $registry['translater']->translate('tasks_ownership_' . $params['old_model']->get($var));
                                    break;
                                default:
                                    $audit_vars[$i]['label'] = $params['model']->get($var);
                                    $audit_vars[$i]['old_value'] = $params['old_model']->get($var);
                                    break;
                            }
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                    }
                }
            }
            break;
        case 'multiedit':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    if ($params['model']->get($var) != $params['old_model']->get($var)) {
                        $audit_vars[$i]['field_name'] = $var;
                        $audit_vars[$i]['field_value'] = $params['model']->get($var);
                        if (isset($replace_labels[$var])) {
                            $audit_vars[$i]['label'] = $params['model']->get($replace_labels[$var]);
                            $audit_vars[$i]['old_value'] = $params['old_model']->get($replace_labels[$var]);
                        } else {
                            $audit_vars[$i]['label'] = $params['model']->get($var);
                            $audit_vars[$i]['old_value'] = $params['old_model']->get($var);
                        }
                        $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                        $i++;
                    }
                }
            }

            $vars = $params['model']->get('vars');
            $old_vars = $params['old_model']->get('vars');
            $i = count($audit_vars);
            if (is_array($vars) && count($vars)) {
                foreach ($vars as $var) {
                    if ($var['auditable']) {
                        $add_audit = false;
                        $old_not_exist = true;
                        $old_value = '';
                        foreach ($old_vars as $old_var) {
                            if ($old_var['name'] == $var['name']) {
                                $old_not_exist = false;
                                if ($var['value'] != $old_var['value']) {
                                    $add_audit = true;
                                    $old_value = $old_var['value'];
                                }
                                break;
                            }
                        }
                        if ($add_audit || $old_not_exist) {
                            $audit_vars[$i]['field_name'] = $var['name'];
                            $audit_vars[$i]['field_value'] = $var['value'];
                            if (!empty($var['options'])) {
                                foreach ($var['options'] as $opt) {
                                    if ($opt['option_value'] == $var['value']) {
                                        $audit_vars[$i]['label'] = $opt['label'];
                                    }
                                    if ($opt['option_value'] == $old_value) {
                                        $audit_vars[$i]['old_value'] = $opt['label'];
                                    }
                                }
                            } else {
                                $audit_vars[$i]['label'] = $var['value'];
                                $audit_vars[$i]['old_value'] = $old_value;
                            }
                            $audit_vars[$i]['var_type'] = PH_VAR_ADDITIONAL;
                            $i++;
                        }
                    }
                }
            }
            break;
        case 'tag':
        case 'multitag':
            $i = count($audit_vars);

            if (in_array('tag', $basic_vars)) {
                $diff = array_diff($params['old_model']->get('tags'), $params['model']->get('tags'));
                $diff2 = array_diff($params['model']->get('tags'), $params['old_model']->get('tags'));
                if (!empty($diff) || !empty($diff2)) {
                    $new_var_value =
                        $params['model']->get('tag_names_for_audit') ?
                        implode("\n", $params['model']->get('tag_names_for_audit')) :
                        '';
                    $old_var_value =
                        $params['old_model']->get('tag_names_for_audit') ?
                        implode("\n", $params['old_model']->get('tag_names_for_audit')) :
                        '';
                    $audit_vars[$i]['field_name'] = 'tags';
                    $audit_vars[$i]['field_value'] = serialize($params['model']->get('tags'));
                    $audit_vars[$i]['label'] = $new_var_value;
                    $audit_vars[$i]['old_value'] = $old_var_value;
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = 1;
                }
            }
            break;
        case 'assign':
        case 'remove_assignments':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars)) {
                foreach ($basic_vars as $var) {
                    if ($var=='assignments_owner' || $var=='assignments_responsible' || $var=='assignments_decision' || $var=='assignments_observer') {
                        if ($var=='assignments_owner') {
                            $var_name_label = 'assign_owner';
                        } elseif ($var=='assignments_responsible') {
                            $var_name_label = 'assign_responsible';
                        } elseif ($var=='assignments_decision') {
                            $var_name_label = 'assign_decision';
                        } elseif ($var=='assignments_observer') {
                            $var_name_label = 'assign_observer';
                        }
                        if (!empty($params['model']) && $params['model']->isDefined($var)) {
                            $new_var_array = $params['model']->get($var);
                        } else {
                            $new_var_array = array();
                        }
                        if (!empty($params['old_model']) && $params['old_model']->isDefined($var)) {
                            $old_var_array = $params['old_model']->get($var);
                        } else {
                            $old_var_array = array();
                        }

                        $diff_old = array_diff_key($old_var_array, $new_var_array);
                        $diff_new = array_diff_key($new_var_array, $old_var_array);

                        if (!empty($diff_old) || !empty($diff_new)) {
                            $new_var_value = '';
                            foreach ($new_var_array as $assignee) {
                                $new_var_value .= $assignee['assigned_to_name'] . "\n";
                            }

                            $old_var_value = '';
                            foreach ($old_var_array as $assignee_old) {
                                $old_var_value .= $assignee_old['assigned_to_name'] . "\n";
                            }

                            $audit_vars[$i]['field_name'] = $var_name_label;
                            $audit_vars[$i]['field_value'] = $new_var_value;
                            $audit_vars[$i]['label'] = $new_var_value;
                            $audit_vars[$i]['old_value'] = $old_var_value;
                            $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                            $i++;
                        }
                    }
                }
            } else {
                switch ($var) {
                    case 'ownership':
                        $audit_vars[$i]['label'] = $registry['translater']->translate('tasks_ownership_' . $params['model']->get($var));
                        $audit_vars[$i]['old_value'] = $registry['translater']->translate('tasks_ownership_' . $params['old_model']->get($var));
                        break;
                }
            }
            break;
        case 'add_attachments':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars) && in_array('add_attachments', $basic_vars)) {
                $old_model_attachments = array();
                $new_model_attachments = array();
                foreach ($params['old_model']->get('attachments') as $file) {
                    $old_model_attachments[] = $file->get('id');
                }
                foreach ($params['model']->get('attachments') as $file) {
                    $new_model_attachments[] = $file->get('id');
                }
                $new_attachments_ids = array_diff($new_model_attachments, $old_model_attachments);
                if (!empty($new_attachments_ids)) {
                    $new_files = array();
                    foreach ($params['model']->get('attachments') as $file) {
                        if (in_array($file->get('id'), $new_attachments_ids)) {
                            $new_files[] = $file;
                        }
                    }
                    $new_var_value = '';

                    foreach ($new_files as $new_file) {
                        $new_var_value .= $new_file->get('name') . ' (' . $new_file->get('revision') . ')' . "\n";
                    }

                    $audit_vars[$i]['field_name'] = 'added_attachments';
                    $audit_vars[$i]['field_value'] = serialize($new_attachments_ids);
                    $audit_vars[$i]['label'] = $new_var_value;
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = 1;
                    $i++;
                }
            }
            break;
        case 'del_attachments':
            $i = count($audit_vars);
            if (is_array($basic_vars) && count($basic_vars) && in_array('del_attachment', $basic_vars)) {
                $old_model_attachments = array();
                $new_model_attachments = array();
                foreach ($params['old_model']->get('attachments') as $file) {
                    $old_model_attachments[] = $file->get('id');
                }
                foreach ($params['model']->get('attachments') as $file) {
                    $new_model_attachments[] = $file->get('id');
                }
                $deleted_ids = array_diff($old_model_attachments, $new_model_attachments);
                if (!empty($deleted_ids)) {
                    $deleted_files = array();

                    foreach ($params['old_model']->get('attachments') as $file) {
                        if (in_array($file->get('id'), $deleted_ids)) {
                            $deleted_files[] = $file;
                        }
                    }

                    $new_var_value = '';

                    foreach ($deleted_files as $deleted_file) {
                        $new_var_value .= $deleted_file->get('name') . ' (' . $deleted_file->get('revision') . ')' . "\n";
                    }

                    $audit_vars[$i]['field_name'] = 'deleted_attachments';
                    $audit_vars[$i]['field_value'] = serialize($deleted_ids);
                    $audit_vars[$i]['label'] = $new_var_value;
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = 1;
                    $i++;
                }
            }
            break;
        case 'receive_email':
        case 'email':
            $basic_vars = array('mail_from', 'mail_code', 'mail_to', 'mail_cc', 'mail_bcc', 'mail_subject', 'mail_content', 'attached_files');
            $basic_vars_array = array('mail_to', 'mail_cc', 'mail_bcc', 'attached_files');
            $i = count($audit_vars);

            foreach ($basic_vars as $var) {
                $field_value = '';
                if (in_array($var, $basic_vars_array)) {
                    $mails = $params['model']->get($var) ?: array();
                    $mail_names = $params['model']->get("{$var}_name") ?: array();
                    $field_value = array();
                    foreach ($mails as $key => $value) {
                        $field_value[] = !empty($mail_names[$key]) ? $mail_names[$key] . ' (' . $value . ')' : $value;
                    }
                    $field_value = $field_value ? serialize($field_value) : '';
                } else {
                    switch ($var) {
                        case 'mail_subject':
                            $prop = 'email_subject';
                            break;
                        case 'mail_content':
                            $prop = 'body_formated';
                            break;
                        default:
                            $prop = $var;
                            break;
                    }
                    $field_value = $params['model']->get($prop) ?: '';
                }
                if ($field_value) {
                    $audit_vars[$i]['field_value'] = $field_value;
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $audit_vars[$i]['is_array'] = in_array($var, $basic_vars_array);
                    $audit_vars[$i]['label'] = '';
                    $i++;
                }
            }
            break;
        case 'add_comment':
        case 'edit_comment':
            $basic_vars = array('subject', 'content', 'is_portal');
            $comment = $params['model']->get('comment');
            $old_comment = $params['old_model']->get('comment');
            $i = count($audit_vars);
            $is_add_action = $params['action_type'] == 'add_comment';

            foreach ($basic_vars as $var) {
                if ($is_add_action && $comment->get($var) || !$is_add_action && $old_comment->get($var) != $comment->get($var) || $var == 'is_portal' && $comment->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $comment->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    if (isset($replace_labels[$var])) {
                        $audit_vars[$i]['label'] = $comment->get($replace_labels[$var]);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get($replace_labels[$var]);
                        }
                    } else {
                        $audit_vars[$i]['label'] = $comment->get($var);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_comment->get($var);
                        }
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'add_minitask':
            $auditable_minitask_vars = array('model_id', 'customer', 'description', 'deadline', 'assigned_to', 'severity');
            $replace_labels['model_id'] = 'record_name';
            $replace_labels['assigned_to'] = 'assigned_to_name';
            $replace_labels['severity'] = 'severity_name';
            $minitask = $params['model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($minitask->isDefined($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    $audit_vars[$i]['label'] = isset($replace_labels[$var]) ?
                                               $minitask->get($replace_labels[$var]) :
                                               $minitask->get($var);
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'edit_minitask':
            $auditable_minitask_vars = array('model_id', 'customer', 'description', 'deadline', 'assigned_to', 'severity');
            $replace_labels['model_id'] = 'record_name';
            $replace_labels['assigned_to'] = 'assigned_to_name';
            $replace_labels['severity'] = 'severity_name';
            $minitask = $params['model']->get('minitask');
            $old_minitask = $params['old_model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($minitask->get($var) != $old_minitask->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    if (isset($replace_labels[$var])) {
                        $audit_vars[$i]['label'] = $minitask->get($replace_labels[$var]);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($replace_labels[$var]);
                    } else {
                        $audit_vars[$i]['label'] = $minitask->get($var);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($var);
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'status_minitask':
        case 'multistatus_minitask':
            $auditable_minitask_vars = array('status', 'comment');
            $minitask = $params['model']->get('minitask');
            $old_minitask = $params['old_model']->get('minitask');

            $i = count($audit_vars);
            foreach ($auditable_minitask_vars as $var) {
                if ($old_minitask->get($var) != $minitask->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $minitask->get($var);
                    if ($var == 'status') {
                    $audit_vars[$i]['label'] = ($minitask->get('status') ? ($registry['translater']->translate('minitasks_status_' . $minitask->get('status'))) : ('-'));
                    $audit_vars[$i]['old_value'] = ($old_minitask->get('status') ? ($registry['translater']->translate('minitasks_status_' . $old_minitask->get('status'))) : ('-'));
                    } else {
                        $audit_vars[$i]['label'] = $minitask->get($var);
                        $audit_vars[$i]['old_value'] = $old_minitask->get($var);
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        case 'add_timesheet':
        case 'edit_timesheet':
            $basic_vars = array('event_id', 'activity', 'subject', 'content', 'startperiod', 'endperiod', 'duration', 'duration_billing', 'user_id', 'office');
            $replace_labels = array_merge($replace_labels, array(
                'event_id' => 'event_name',
                'activity' => 'activity_name',
                'user_id' => 'user_id_name',
                'office' => 'office_name',
            ));
            $timesheet = $params['model']->get('timesheet');
            $old_timesheet = $params['old_model']->get('timesheet');
            $i = count($audit_vars);
            $is_add_action = $params['action_type'] == 'add_timesheet';

            foreach ($basic_vars as $var) {
                if ($is_add_action && $timesheet->get($var) || !$is_add_action && $old_timesheet->get($var) != $timesheet->get($var)) {
                    $audit_vars[$i]['field_name'] = $var;
                    $audit_vars[$i]['field_value'] = $timesheet->get($var);
                    $audit_vars[$i]['old_value'] = '';
                    if (isset($replace_labels[$var])) {
                        $audit_vars[$i]['label'] = $timesheet->get($replace_labels[$var]);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_timesheet->get($replace_labels[$var]);
                        }
                    } else {
                        $audit_vars[$i]['label'] = $timesheet->get($var);
                        if (!$is_add_action) {
                            $audit_vars[$i]['old_value'] = $old_timesheet->get($var);
                        }
                    }
                    $audit_vars[$i]['var_type'] = PH_VAR_BASIC;
                    $i++;
                }
            }
            break;
        }

        $params['data'] = $audit_vars;
        return true;
    }

    /**
     * prepare audit data for view
     *
     * @return bool
     */
    public static function prepareGetData($records, $params) {
        foreach ($records as $k => $rec) {
            if ($rec['is_array']) {
                $arr = unserialize($rec['field_value']);
                if (empty($arr)) {
                    $arr = array();
                }
                if (is_array($arr)) {
                    if (count($arr) == count($arr, COUNT_RECURSIVE)) {
                        //array is NOT multidimensional, it is safe to implode it into string
                        $records[$k]['field_value'] = implode("\n", $arr);
                    } else {
                        //array is multidimensional, usually wrong assignments array is saved
                        //it should not be displayed
                        unset($records[$k]);
                        continue;
                    }
                }

                if (!empty($rec['old_value']) &&
                preg_match("/(a|O|s|b)\x3a[0-9]*?((\x3a((\x7b?(.+)\x7d)|(\x22(.+)\x22\x3b)))|(\x3b))/", $rec['old_value'])) {
                    $arr = unserialize($rec['old_value']);
                    if (empty($arr)) {
                        $arr = array();
                    }
                    $records[$k]['old_value'] = implode("\n", $arr);
                }

                if (empty($records[$k]['field_value']) && empty($records[$k]['old_value'])) {
                    unset($records[$k]);
                }
            }
        }

        return $records;
    }

    /**
     * save model audit
     *
     * @return bool
     */
    public static function saveData(&$registry, $params) {
        self::prepareData($registry, $params);
        return parent::saveData($registry, $params);
    }

    /**
     * get model audit
     *
     * @return array data
     */
    public static function getData(&$registry, $params) {
        $records = parent::getData($registry, $params);
        $records = self::prepareGetData($records, $params);

        return array('vars' => $records);
    }
}

?>
