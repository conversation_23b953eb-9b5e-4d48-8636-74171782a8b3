<?php

class Tasks_Sections_List_Viewer extends Viewer {
    public $template = 'sections_list.html';
    public $filters = array();

    /**
     * Sortable columns in the list view
     */
    public $sortables = array('name', 'position', 'description');

    public function prepare() {
        require_once $this->modelsDir . 'tasks.sections.factory.php';

        $filters = Tasks_Sections::saveSearchParams($this->registry);

        list($tasks_sections, $pagination) = Tasks_Sections::pagedSearch($this->registry, $filters);
        $this->data['tasks_sections'] = $tasks_sections;
        $this->data['pagination'] = $pagination;

        //prepare sort array for the listing
        $this->prepareSort($filters);

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('tasks');
        $href = sprintf('%s=%s&amp;type=&amp;type_section=', 
                            $this->registry['module_param'], $this->module);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $title = $this->i18n('tasks_sections');
        $href = sprintf('%s=%s&amp;%s=%s', 
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller);
        $navbarlink[] = array('href' => $href, 'text' => $title);

        $this->data['title'] = $title;
        $this->data['navbarlink'] = $navbarlink;
    }
}

?>
