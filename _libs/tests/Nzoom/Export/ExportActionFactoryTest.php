<?php

namespace Tests\Nzoom\Export;

use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\ExportActionFactory;
use Nzoom\I18n\I18n;

/**
 * Mock Export Plugin class for testing
 */
class MockExportPlugin
{
    private $data;
    
    public function __construct($data)
    {
        $this->data = $data;
    }
    
    public function get($key)
    {
        return $this->data[$key] ?? null;
    }
}

/**
 * Mock Session class for testing
 */
class MockSession
{
    private $data = [];
    
    public function get($key)
    {
        return $this->data[$key] ?? null;
    }
    
    public function set($key, $value)
    {
        $this->data[$key] = $value;
    }
}

/**
 * Test case for ExportActionFactory
 */
class ExportActionFactoryTest extends ExportTestCase
{
    private ExportActionFactory $factory;
    private RegistryMock $registry;
    private $translator;
    private MockSession $session;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../TestHelpers/GlobalMocks.php';

        // Create mock session
        $this->session = new MockSession();

        // Create registry mock
        $this->registry = new RegistryMock();
        $this->registry->set('session', $this->session);

        // Create mock translator using PHPUnit
        $this->translator = $this->createMock(I18n::class);
        $this->translator->method('translate')->willReturnCallback(function($key, $params = []) {
            $translations = [
                'standard_export' => 'Standard Export',
                'export_plugin' => 'Export Plugin',
                'help_export_plugin' => 'Select an export plugin',
                'file_name' => 'File Name',
                'file_format' => 'File Format',
                'include_group_tables' => 'Include Group Tables',
                'yes' => 'Yes',
                'no' => 'No',
                'separator' => 'Separator',
                'delimiter_comma' => 'Comma (,)',
                'delimiter_semicolon' => 'Semicolon (;)',
                'delimiter_tab' => 'Tab'
            ];
            return $translations[$key] ?? $key;
        });

        // Create factory instance
        $this->factory = new ExportActionFactory(
            $this->registry,
            'test_module',
            'test_controller',
            'TestModel',
            'TestFactory',
            $this->translator
        );
    }

    public function testConstructor(): void
    {
        $factory = new ExportActionFactory(
            $this->registry,
            'users',
            'list',
            'User',
            'UserFactory',
            $this->translator
        );
        
        $this->assertInstanceOf(ExportActionFactory::class, $factory);
    }

    public function testConstructorWithNullModelNames(): void
    {
        $factory = new ExportActionFactory(
            $this->registry,
            'test_module',
            'test_controller',
            null,
            null,
            $this->translator
        );
        
        $this->assertInstanceOf(ExportActionFactory::class, $factory);
    }

    public function testSetModelName(): void
    {
        $result = $this->factory->setModelName('NewModel');
        
        $this->assertSame($this->factory, $result); // Test fluent interface
    }

    public function testSetModelFactoryName(): void
    {
        $result = $this->factory->setModelFactoryName('NewFactory');
        
        $this->assertSame($this->factory, $result); // Test fluent interface
    }

    public function testInvokeMethod(): void
    {
        $types = [1];
        $typeSections = [2];
        
        $result = $this->factory->__invoke($types, $typeSections);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('options', $result);
        $this->assertArrayHasKey('ajax_no', $result);
        $this->assertArrayHasKey('disable_items_before_execute', $result);
        $this->assertEquals(1, $result['ajax_no']);
        $this->assertEquals('diselectItemsBeforeMultiAction(this);', $result['disable_items_before_execute']);
    }

    public function testCreateExportAction(): void
    {
        $types = [1, 2];
        $typeSections = [3, 4];
        
        $result = $this->factory->createExportAction($types, $typeSections);
        
        $this->assertIsArray($result);
        $this->assertArrayHasKey('options', $result);
        $this->assertArrayHasKey('ajax_no', $result);
        $this->assertArrayHasKey('disable_items_before_execute', $result);
        
        // Verify the structure
        $this->assertEquals(1, $result['ajax_no']);
        $this->assertEquals('diselectItemsBeforeMultiAction(this);', $result['disable_items_before_execute']);
        $this->assertIsArray($result['options']);
    }

    public function testPrepareExportOptionsBasic(): void
    {
        $types = [1];
        $sections = [2];
        $filtersHide = [];
        
        $result = $this->factory->prepareExportOptions($types, $sections, $filtersHide);
        
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        
        // Check for basic required fields
        $fieldNames = array_column($result, 'name');
        $this->assertContains('export_previous_action', $fieldNames);
        $this->assertContains('file_name', $fieldNames);
        $this->assertContains('format', $fieldNames);
        $this->assertContains('group_tables', $fieldNames);
        $this->assertContains('separator', $fieldNames);
    }

    public function testPrepareExportOptionsWithSingleType(): void
    {
        $types = [5]; // Single type
        $sections = [];
        
        $result = $this->factory->prepareExportOptions($types, $sections);
        
        // Should include type_id field
        $fieldNames = array_column($result, 'name');
        $this->assertContains('type_id', $fieldNames);
        
        // Find the type_id field and verify its value
        $typeIdField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'type_id') {
                $typeIdField = $field;
                break;
            }
        }
        
        $this->assertNotNull($typeIdField);
        $this->assertEquals(5, $typeIdField['value']);
        $this->assertEquals('hidden', $typeIdField['type']);
    }

    public function testPrepareExportOptionsWithSingleSection(): void
    {
        $types = [1, 2]; // Multiple types
        $sections = [7]; // Single section
        
        $result = $this->factory->prepareExportOptions($types, $sections);
        
        // Should include type_section_id field
        $fieldNames = array_column($result, 'name');
        $this->assertContains('type_section_id', $fieldNames);
        
        // Find the type_section_id field and verify its value
        $sectionIdField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'type_section_id') {
                $sectionIdField = $field;
                break;
            }
        }
        
        $this->assertNotNull($sectionIdField);
        $this->assertEquals(7, $sectionIdField['value']);
        $this->assertEquals('hidden', $sectionIdField['type']);
    }

    public function testPrepareExportOptionsWithFiltersHide(): void
    {
        $types = [1];
        $sections = [2];
        $filtersHide = [
            'file_name' => true,
            'format' => true,
            'separator' => false
        ];
        
        $result = $this->factory->prepareExportOptions($types, $sections, $filtersHide);
        
        // Find fields and check their hidden status
        foreach ($result as $field) {
            if ($field['name'] === 'file_name') {
                $this->assertEquals(1, $field['hidden']);
            } elseif ($field['name'] === 'format') {
                $this->assertEquals(1, $field['hidden']);
            } elseif ($field['name'] === 'separator') {
                $this->assertEquals(0, $field['hidden']);
            }
        }
    }

    public function testFormatOptions(): void
    {
        $result = $this->factory->prepareExportOptions();
        
        // Find the format field
        $formatField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'format') {
                $formatField = $field;
                break;
            }
        }
        
        $this->assertNotNull($formatField);
        $this->assertEquals('dropdown', $formatField['type']);
        $this->assertEquals(1, $formatField['required']);
        $this->assertEquals('File Format', $formatField['label']);
        $this->assertEquals('toggleExportFormat(this)', $formatField['onchange']);
        
        // Check format options
        $this->assertIsArray($formatField['options']);
        $this->assertCount(3, $formatField['options']);
        
        $optionValues = array_column($formatField['options'], 'option_value');
        $this->assertContains('csv', $optionValues);
        $this->assertContains('xls', $optionValues);
        $this->assertContains('xlsx', $optionValues);
    }

    public function testGroupTablesOptions(): void
    {
        $result = $this->factory->prepareExportOptions();
        
        // Find the group_tables field
        $groupTablesField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'group_tables') {
                $groupTablesField = $field;
                break;
            }
        }
        
        $this->assertNotNull($groupTablesField);
        $this->assertEquals('radio', $groupTablesField['type']);
        $this->assertEquals(1, $groupTablesField['required']);
        $this->assertEquals('horizontal', $groupTablesField['options_align']);
        $this->assertEquals(0, $groupTablesField['value']);
        $this->assertEquals(1, $groupTablesField['hidden']);
        
        // Check options
        $this->assertIsArray($groupTablesField['options']);
        $this->assertCount(2, $groupTablesField['options']);
        
        $optionValues = array_column($groupTablesField['options'], 'option_value');
        $this->assertContains(1, $optionValues);
        $this->assertContains(0, $optionValues);
    }

    public function testDelimiterOptions(): void
    {
        $result = $this->factory->prepareExportOptions();
        
        // Find the separator field
        $separatorField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'separator') {
                $separatorField = $field;
                break;
            }
        }
        
        $this->assertNotNull($separatorField);
        $this->assertEquals('dropdown', $separatorField['type']);
        $this->assertEquals(1, $separatorField['required']);
        $this->assertEquals('comma', $separatorField['value']);
        
        // Check delimiter options
        $this->assertIsArray($separatorField['options']);
        $this->assertCount(3, $separatorField['options']);
        
        $optionValues = array_column($separatorField['options'], 'option_value');
        $this->assertContains('comma', $optionValues);
        $this->assertContains('semicolon', $optionValues);
        $this->assertContains('tab', $optionValues);
    }

    public function testBaseExportOptionsStructure(): void
    {
        $result = $this->factory->prepareExportOptions();

        // Find the export_previous_action field
        $previousActionField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'export_previous_action') {
                $previousActionField = $field;
                break;
            }
        }

        $this->assertNotNull($previousActionField);
        $this->assertEquals('hidden', $previousActionField['type']);
        $this->assertEquals('list', $previousActionField['value']);
        $this->assertEquals(1, $previousActionField['hidden']);

        // Find the file_name field
        $fileNameField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'file_name') {
                $fileNameField = $field;
                break;
            }
        }

        $this->assertNotNull($fileNameField);
        $this->assertEquals('text', $fileNameField['type']);
        $this->assertEquals(1, $fileNameField['required']);
        $this->assertEquals('File Name', $fileNameField['label']);
        $this->assertEquals('TestFactory', $fileNameField['value']);
    }

    public function testEmptyTypesAndSections(): void
    {
        $result = $this->factory->prepareExportOptions([], []);

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        // Should not include type_id or type_section_id fields
        $fieldNames = array_column($result, 'name');
        $this->assertNotContains('type_id', $fieldNames);
        $this->assertNotContains('type_section_id', $fieldNames);
    }

    public function testMultipleTypesAndSections(): void
    {
        $types = [1, 2, 3];
        $sections = [4, 5, 6];

        $result = $this->factory->prepareExportOptions($types, $sections);

        // Should not include type_id or type_section_id fields when multiple values
        $fieldNames = array_column($result, 'name');
        $this->assertNotContains('type_id', $fieldNames);
        $this->assertNotContains('type_section_id', $fieldNames);
    }

    public function testTranslationIntegration(): void
    {
        $result = $this->factory->prepareExportOptions();

        // Check that translations are being used
        foreach ($result as $field) {
            if (isset($field['label'])) {
                // Labels should be translated (not the original keys)
                $this->assertNotEquals('file_name', $field['label']);
                $this->assertNotEquals('file_format', $field['label']);
                $this->assertNotEquals('separator', $field['label']);
            }
        }
    }

    public function testFieldCustomIds(): void
    {
        $result = $this->factory->prepareExportOptions();

        // Check that custom_id fields are set correctly
        $customIds = [];
        foreach ($result as $field) {
            if (isset($field['custom_id'])) {
                $customIds[] = $field['custom_id'];
            }
        }

        $this->assertContains('file_name', $customIds);
        $this->assertContains('format', $customIds);
        $this->assertContains('group_tables', $customIds);
        $this->assertContains('separator', $customIds);
    }

    public function testComplexScenario(): void
    {
        // Test a complex scenario with multiple configurations
        $types = [10];
        $sections = [20, 30];
        $filtersHide = [
            'file_name' => false,
            'format' => true,
            'separator' => false,
            'unknown_filter' => true
        ];

        $result = $this->factory->prepareExportOptions($types, $sections, $filtersHide);

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);

        // Should include type_id since types has only one element
        $fieldNames = array_column($result, 'name');
        $this->assertContains('type_id', $fieldNames);
        $this->assertNotContains('type_section_id', $fieldNames);

        // Check filter visibility
        foreach ($result as $field) {
            if ($field['name'] === 'file_name') {
                $this->assertEquals(0, $field['hidden']);
            } elseif ($field['name'] === 'format') {
                $this->assertEquals(1, $field['hidden']);
            } elseif ($field['name'] === 'separator') {
                $this->assertEquals(0, $field['hidden']);
            }
        }

        // Verify type_id value
        foreach ($result as $field) {
            if ($field['name'] === 'type_id') {
                $this->assertEquals(10, $field['value']);
                break;
            }
        }
    }

    public function testFieldRequirements(): void
    {
        $result = $this->factory->prepareExportOptions();

        // Check required fields
        foreach ($result as $field) {
            if (in_array($field['name'], ['file_name', 'format', 'group_tables', 'separator'])) {
                $this->assertEquals(1, $field['required'], "Field {$field['name']} should be required");
            }
        }
    }

    public function testFieldTypes(): void
    {
        $result = $this->factory->prepareExportOptions();

        $expectedTypes = [
            'export_previous_action' => 'hidden',
            'file_name' => 'text',
            'format' => 'dropdown',
            'group_tables' => 'radio',
            'separator' => 'dropdown'
        ];

        foreach ($result as $field) {
            if (isset($expectedTypes[$field['name']])) {
                $this->assertEquals(
                    $expectedTypes[$field['name']],
                    $field['type'],
                    "Field {$field['name']} should have type {$expectedTypes[$field['name']]}"
                );
            }
        }
    }

    public function testDefaultValues(): void
    {
        $result = $this->factory->prepareExportOptions();

        $expectedDefaults = [
            'export_previous_action' => 'list',
            'file_name' => 'TestFactory',
            'group_tables' => 0,
            'separator' => 'comma'
        ];

        foreach ($result as $field) {
            if (isset($expectedDefaults[$field['name']])) {
                $this->assertEquals(
                    $expectedDefaults[$field['name']],
                    $field['value'],
                    "Field {$field['name']} should have default value {$expectedDefaults[$field['name']]}"
                );
            }
        }
    }

    public function testPluginOptionsWithNoPlugins(): void
    {
        // Use a model name that won't match any plugins
        $this->factory->setModelName('NonExistentModel');

        $result = $this->factory->prepareExportOptions([1], []);

        // Should not include plugin fields when no plugins are found
        $fieldNames = array_column($result, 'name');
        $this->assertNotContains('plugin', $fieldNames);
        $this->assertNotContains('previous_plugin', $fieldNames);
    }

    public function testPluginOptionsWithPlugins(): void
    {
        // Use TestModel which has plugins in our mock
        $this->factory->setModelName('TestModel');

        $result = $this->factory->prepareExportOptions([1], []);

        // Should include plugin fields when plugins are found
        $fieldNames = array_column($result, 'name');
        $this->assertContains('plugin', $fieldNames);
        $this->assertContains('previous_plugin', $fieldNames);

        // Find the plugin field
        $pluginField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'plugin') {
                $pluginField = $field;
                break;
            }
        }

        $this->assertNotNull($pluginField);
        $this->assertEquals('dropdown', $pluginField['type']);
        $this->assertEquals(1, $pluginField['required']);
        $this->assertEquals('Export Plugin', $pluginField['label']);
        $this->assertStringContainsString('selectExport', $pluginField['onchange']);

        // Check plugin options
        $this->assertIsArray($pluginField['options']);
        $this->assertGreaterThanOrEqual(2, count($pluginField['options'])); // 2 plugins minimum

        // Check that plugin options are included
        $optionValues = array_column($pluginField['options'], 'option_value');
        $this->assertContains(1, $optionValues);
        $this->assertContains(2, $optionValues);
    }

    public function testPluginOptionsWithSessionData(): void
    {
        // Set up session data for last used plugin
        $this->session->set('last_export_plugins', [
            'test_module_test_controller_type_1' => 2
        ]);

        $this->factory->setModelName('TestModel');

        $result = $this->factory->prepareExportOptions([1], []);

        // Find the plugin field
        $pluginField = null;
        $previousPluginField = null;
        foreach ($result as $field) {
            if ($field['name'] === 'plugin') {
                $pluginField = $field;
            } elseif ($field['name'] === 'previous_plugin') {
                $previousPluginField = $field;
            }
        }

        $this->assertNotNull($pluginField);
        $this->assertNotNull($previousPluginField);

        // Should use the session value as default
        $this->assertEquals(2, $pluginField['value']);
        $this->assertEquals(2, $previousPluginField['value']);
        $this->assertEquals('hidden', $previousPluginField['type']);
    }

    public function testPluginOptionsWithDifferentTypeAndSection(): void
    {
        $this->factory->setModelName('TestModel');

        // Test with type
        $resultWithType = $this->factory->prepareExportOptions([5], []);

        // Test with section (when multiple types)
        $resultWithSection = $this->factory->prepareExportOptions([1, 2], [3]);

        // Both should include plugin options
        $typeFieldNames = array_column($resultWithType, 'name');
        $sectionFieldNames = array_column($resultWithSection, 'name');

        $this->assertContains('plugin', $typeFieldNames);
        $this->assertContains('plugin', $sectionFieldNames);
    }

    public function testFirstOrZeroMethod(): void
    {
        // Test with single element array
        $result1 = $this->factory->prepareExportOptions([42], []);

        // Should include type_id with value 42
        $typeIdField = null;
        foreach ($result1 as $field) {
            if ($field['name'] === 'type_id') {
                $typeIdField = $field;
                break;
            }
        }
        $this->assertNotNull($typeIdField);
        $this->assertEquals(42, $typeIdField['value']);

        // Test with empty array
        $result2 = $this->factory->prepareExportOptions([], []);
        $fieldNames = array_column($result2, 'name');
        $this->assertNotContains('type_id', $fieldNames);

        // Test with multiple elements
        $result3 = $this->factory->prepareExportOptions([1, 2, 3], []);
        $fieldNames = array_column($result3, 'name');
        $this->assertNotContains('type_id', $fieldNames);
    }

    public function testFilterVisibilityInitialization(): void
    {
        // Test that all filters from Exports::$filtersToStore are initialized
        $result = $this->factory->prepareExportOptions();

        // This test verifies that the initializeFilterVisibility method works
        // by checking that all expected fields are present
        $fieldNames = array_column($result, 'name');

        // All basic fields should be present
        $this->assertContains('file_name', $fieldNames);
        $this->assertContains('format', $fieldNames);
        $this->assertContains('separator', $fieldNames);
        $this->assertContains('group_tables', $fieldNames);
    }

    public function testCompleteWorkflow(): void
    {
        // Test a complete workflow from construction to export action creation
        $factory = new ExportActionFactory(
            $this->registry,
            'users',
            'list',
            'User',
            'UserFactory',
            $this->translator
        );

        // Set model name to one that has plugins
        $factory->setModelName('TestModel');
        $factory->setModelFactoryName('TestModelFactory');

        // Create export action
        $types = [10];
        $typeSections = [20];

        $result = $factory->createExportAction($types, $typeSections);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('options', $result);
        $this->assertArrayHasKey('ajax_no', $result);
        $this->assertArrayHasKey('disable_items_before_execute', $result);

        // Verify options structure
        $options = $result['options'];
        $this->assertIsArray($options);
        $this->assertNotEmpty($options);

        // Should include all expected fields
        $fieldNames = array_column($options, 'name');
        $expectedFields = [
            'export_previous_action',
            'file_name',
            'type_id',
            'plugin',
            'previous_plugin',
            'format',
            'group_tables',
            'separator'
        ];

        foreach ($expectedFields as $expectedField) {
            $this->assertContains($expectedField, $fieldNames, "Missing field: {$expectedField}");
        }

        // Verify file name uses the updated factory name
        foreach ($options as $field) {
            if ($field['name'] === 'file_name') {
                $this->assertEquals('TestModelFactory', $field['value']);
                break;
            }
        }
    }
}
