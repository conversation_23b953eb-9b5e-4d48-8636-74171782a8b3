<?php

namespace Tests\Nzoom\Export\Adapter;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;
use Nzoom\Export\Entity\ExportData;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\I18n\I18n;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

/**
 * Mock logger for testing
 */
class MockExcelLogger
{
    public $errors = [];
    public $infos = [];

    public function error($message)
    {
        $this->errors[] = $message;
    }

    public function info($message)
    {
        $this->infos[] = $message;
    }
}

/**
 * Test case for ExcelExportFormatAdapter
 * 
 * Focuses on Excel-specific functionality, avoiding duplication with AbstractExportFormatAdapterTest
 */
class ExcelExportFormatAdapterTest extends ExportTestCase
{
    private ExcelExportFormatAdapter $adapter;
    private RegistryMock $registry;
    private $translator;
    private MockExcelLogger $logger;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        // Create mock dependencies
        $this->translator = $this->createMock(I18n::class);
        $this->translator->method('translate')->willReturnCallback(function($key, $params = []) {
            return $key; // Simple pass-through for testing
        });

        $this->logger = new MockExcelLogger();

        // Create registry mock
        $this->registry = new RegistryMock();
        $this->registry->set('translater', $this->translator);
        $this->registry->set('logger', $this->logger);

        // Create adapter instance
        $this->adapter = new ExcelExportFormatAdapter($this->registry, 'test_module', 'test_controller');
    }

    // Test Excel-specific interface methods (not covered by abstract tests)

    public function testGetSupportedExtensions(): void
    {
        $extensions = ExcelExportFormatAdapter::getSupportedExtensions();
        $this->assertIsArray($extensions);
        $this->assertContains('xlsx', $extensions);
        $this->assertContains('xls', $extensions);
        $this->assertCount(2, $extensions);
    }

    public function testSupportsFormat(): void
    {
        $this->assertTrue(ExcelExportFormatAdapter::supportsFormat('xlsx'));
        $this->assertTrue(ExcelExportFormatAdapter::supportsFormat('xls'));
        $this->assertTrue(ExcelExportFormatAdapter::supportsFormat('XLSX')); // Case insensitive
        $this->assertTrue(ExcelExportFormatAdapter::supportsFormat('XLS'));
        $this->assertFalse(ExcelExportFormatAdapter::supportsFormat('csv'));
        $this->assertFalse(ExcelExportFormatAdapter::supportsFormat('pdf'));
        $this->assertFalse(ExcelExportFormatAdapter::supportsFormat(''));
    }

    public function testGetMimeType(): void
    {
        // Test default (xlsx)
        $this->assertEquals(
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            $this->adapter->getMimeType()
        );

        // Test xlsx explicitly
        $this->assertEquals(
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            $this->adapter->getMimeType('xlsx')
        );

        // Test xls
        $this->assertEquals(
            'application/vnd.ms-excel',
            $this->adapter->getMimeType('xls')
        );

        // Test case insensitive
        $this->assertEquals(
            'application/vnd.ms-excel',
            $this->adapter->getMimeType('XLS')
        );

        // Test unsupported format (should return default)
        $this->assertEquals(
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            $this->adapter->getMimeType('unsupported')
        );
    }

    public function testGetDefaultExtension(): void
    {
        $this->assertEquals('xlsx', $this->adapter->getDefaultExtension());
    }

    public function testGetFormatName(): void
    {
        $this->assertEquals('excel', $this->adapter->getFormatName());
    }

    public function testGetFormatOptions(): void
    {
        $options = $this->adapter->getFormatOptions();
        
        $this->assertIsArray($options);
        $this->assertArrayHasKey('extension', $options);
        $this->assertArrayHasKey('chunk_size', $options);
        $this->assertArrayHasKey('max_column_width', $options);
        $this->assertArrayHasKey('max_row_height', $options);

        // Test extension options
        $extensionOption = $options['extension'];
        $this->assertEquals('select', $extensionOption['type']);
        $this->assertEquals('File Format', $extensionOption['label']);
        $this->assertArrayHasKey('xlsx', $extensionOption['options']);
        $this->assertArrayHasKey('xls', $extensionOption['options']);
        $this->assertEquals('xlsx', $extensionOption['default']);

        // Test chunk_size options
        $chunkOption = $options['chunk_size'];
        $this->assertEquals('number', $chunkOption['type']);
        $this->assertEquals(1000, $chunkOption['default']);
        $this->assertEquals(100, $chunkOption['min']);
        $this->assertEquals(5000, $chunkOption['max']);

        // Test max_column_width options
        $widthOption = $options['max_column_width'];
        $this->assertEquals('number', $widthOption['type']);
        $this->assertEquals(50.0, $widthOption['default']);
        $this->assertEquals(10.0, $widthOption['min']);
        $this->assertEquals(255.0, $widthOption['max']);
        $this->assertEquals(0.1, $widthOption['step']);
    }

    // Test Excel-specific export functionality

    public function testExportWithPhpSpreadsheetNotAvailable(): void
    {
        // This test would require mocking class_exists, which is complex
        // Instead, we'll test that the method exists and can be called
        $this->assertTrue(method_exists($this->adapter, 'export'));
    }

    public function testExportWithUnsupportedType(): void
    {
        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_test_');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unsupported export type: pdf');

        $this->adapter->export($tempFile, 'pdf', $exportData);

        // Clean up
        if (file_exists($tempFile)) {
            unlink($tempFile);
        }
    }

    public function testExportToTempFile(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_test_') . '.xlsx';

        try {
            $this->adapter->export($tempFile, 'xlsx', $exportData);
            
            // Verify file was created
            $this->assertFileExists($tempFile);
            $this->assertGreaterThan(0, filesize($tempFile));
            
            // Verify it's a valid Excel file (basic check)
            $fileContent = file_get_contents($tempFile);
            $this->assertStringContainsString('PK', $fileContent); // ZIP signature for xlsx
            
        } finally {
            // Clean up
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportToPhpOutput(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $exportData = $this->createTestExportData();

        // Capture output
        ob_start();
        
        try {
            $this->adapter->export('php://output', 'xlsx', $exportData);
            $output = ob_get_contents();
            
            // Should have some output
            $this->assertNotEmpty($output);
            
        } finally {
            ob_end_clean();
        }
    }

    public function testExportWithCustomOptions(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_test_') . '.xlsx';

        $options = [
            'chunk_size' => 500,
            'max_column_width' => 30.0,
            'max_row_height' => 100.0
        ];

        try {
            $this->adapter->setConfiguration($options);
            $this->adapter->export($tempFile, 'xlsx', $exportData);
            
            // Verify file was created
            $this->assertFileExists($tempFile);
            $this->assertGreaterThan(0, filesize($tempFile));
            
        } finally {
            // Clean up
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportXlsFormat(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $exportData = $this->createTestExportData();
        $tempFile = tempnam(sys_get_temp_dir(), 'excel_test_') . '.xls';

        try {
            $this->adapter->export($tempFile, 'xls', $exportData);
            
            // Verify file was created
            $this->assertFileExists($tempFile);
            $this->assertGreaterThan(0, filesize($tempFile));
            
            // XLS files have different signature than XLSX
            $fileContent = file_get_contents($tempFile);
            $this->assertNotEmpty($fileContent);
            
        } finally {
            // Clean up
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    // Helper method to create test export data
    private function createTestExportData(): ExportData
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('email', 'Email', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('salary', 'Salary', ExportValue::TYPE_FLOAT));
        $header->addColumn(new ExportColumn('active', 'Active', ExportValue::TYPE_BOOLEAN));
        $header->addColumn(new ExportColumn('created_at', 'Created At', ExportValue::TYPE_DATETIME));

        $record1 = new ExportRecord();
        $record1->addValue('id', 1, ExportValue::TYPE_INTEGER);
        $record1->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $record1->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);
        $record1->addValue('salary', 50000.50, ExportValue::TYPE_FLOAT);
        $record1->addValue('active', true, ExportValue::TYPE_BOOLEAN);
        $record1->addValue('created_at', '2024-01-15 10:30:00', ExportValue::TYPE_DATETIME);

        $record2 = new ExportRecord();
        $record2->addValue('id', 2, ExportValue::TYPE_INTEGER);
        $record2->addValue('name', 'Jane Smith', ExportValue::TYPE_STRING);
        $record2->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);
        $record2->addValue('salary', 75000.00, ExportValue::TYPE_FLOAT);
        $record2->addValue('active', false, ExportValue::TYPE_BOOLEAN);
        $record2->addValue('created_at', '2024-02-20 14:45:30', ExportValue::TYPE_DATETIME);

        $exportData = new ExportData($header);
        $exportData->addRecord($record1);
        $exportData->addRecord($record2);

        return $exportData;
    }

    // Test Excel-specific private methods via reflection

    public function testExtractSizingOptions(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('extractSizingOptions');
        $method->setAccessible(true);

        $options = [
            'max_column_width' => 75.5,
            'max_row_height' => 150.0,
            'chunk_size' => 2000,
            'other_option' => 'ignored'
        ];

        $method->invoke($this->adapter, $options);

        // Verify that sizing options were extracted and set
        $chunkSizeProperty = $reflection->getProperty('chunkSize');
        $chunkSizeProperty->setAccessible(true);
        $this->assertEquals(2000, $chunkSizeProperty->getValue($this->adapter));

        $maxWidthProperty = $reflection->getProperty('maxColumnWidth');
        $maxWidthProperty->setAccessible(true);
        $this->assertEquals(75.5, $maxWidthProperty->getValue($this->adapter));

        $maxHeightProperty = $reflection->getProperty('maxRowHeight');
        $maxHeightProperty->setAccessible(true);
        $this->assertEquals(150.0, $maxHeightProperty->getValue($this->adapter));
    }

    public function testExtractSizingOptionsWithDefaults(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('extractSizingOptions');
        $method->setAccessible(true);

        // Test with empty options - should keep defaults
        $method->invoke($this->adapter, []);

        $chunkSizeProperty = $reflection->getProperty('chunkSize');
        $chunkSizeProperty->setAccessible(true);
        $this->assertEquals(1000, $chunkSizeProperty->getValue($this->adapter)); // Default

        $maxWidthProperty = $reflection->getProperty('maxColumnWidth');
        $maxWidthProperty->setAccessible(true);
        $this->assertEquals(50.0, $maxWidthProperty->getValue($this->adapter)); // Default

        $maxHeightProperty = $reflection->getProperty('maxRowHeight');
        $maxHeightProperty->setAccessible(true);
        $this->assertEquals(200.0, $maxHeightProperty->getValue($this->adapter)); // Default
    }

    public function testCreateWriter(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('createWriter');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();

        // Test XLSX writer
        $xlsxWriter = $method->invoke($this->adapter, $spreadsheet, 'xlsx');
        $this->assertInstanceOf(\PhpOffice\PhpSpreadsheet\Writer\Xlsx::class, $xlsxWriter);

        // Test XLS writer
        $xlsWriter = $method->invoke($this->adapter, $spreadsheet, 'xls');
        $this->assertInstanceOf(\PhpOffice\PhpSpreadsheet\Writer\Xls::class, $xlsWriter);

        // Test case insensitive
        $xlsxWriter2 = $method->invoke($this->adapter, $spreadsheet, 'XLSX');
        $this->assertInstanceOf(\PhpOffice\PhpSpreadsheet\Writer\Xlsx::class, $xlsxWriter2);

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testCreateWriterWithUnsupportedFormat(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('createWriter');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unsupported Excel format: pdf');

        try {
            $method->invoke($this->adapter, $spreadsheet, 'pdf');
        } finally {
            $spreadsheet->disconnectWorksheets();
        }
    }

    public function testGetExcelFormatFromExportValue(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('getExcelFormatFromExportValue');
        $method->setAccessible(true);

        // Test different ExportValue types
        $stringValue = new ExportValue('text', ExportValue::TYPE_STRING);
        $this->assertEquals(NumberFormat::FORMAT_TEXT, $method->invoke($this->adapter, $stringValue));

        $intValue = new ExportValue(123, ExportValue::TYPE_INTEGER);
        $this->assertEquals(NumberFormat::FORMAT_NUMBER, $method->invoke($this->adapter, $intValue));

        $floatValue = new ExportValue(123.45, ExportValue::TYPE_FLOAT);
        $this->assertEquals(NumberFormat::FORMAT_NUMBER_00, $method->invoke($this->adapter, $floatValue));

        $dateValue = new ExportValue('2024-01-15', ExportValue::TYPE_DATE);
        $this->assertEquals('dd.mm.yyyy', $method->invoke($this->adapter, $dateValue));

        $datetimeValue = new ExportValue('2024-01-15 10:30:00', ExportValue::TYPE_DATETIME);
        $this->assertEquals('dd.mm.yyyy hh:mm', $method->invoke($this->adapter, $datetimeValue));

        $boolValue = new ExportValue(true, ExportValue::TYPE_BOOLEAN);
        $this->assertEquals(NumberFormat::FORMAT_GENERAL, $method->invoke($this->adapter, $boolValue));
    }

    public function testGetExcelFormatFromExportValueWithCustomFormat(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('getExcelFormatFromExportValue');
        $method->setAccessible(true);

        // Test with custom format
        $floatValue = new ExportValue(123.456, ExportValue::TYPE_FLOAT, '#,##0.000');
        $result = $method->invoke($this->adapter, $floatValue);
        // Should convert custom format or return default
        $this->assertIsString($result);

        $dateValue = new ExportValue('2024-01-15', ExportValue::TYPE_DATE, 'Y-m-d');
        $result = $method->invoke($this->adapter, $dateValue);
        // Should convert custom date format or return default
        $this->assertIsString($result);
    }

    public function testConvertCustomNumberFormat(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('convertCustomNumberFormat');
        $method->setAccessible(true);

        // Test various number format conversions
        $this->assertEquals('#,##0.00', $method->invoke($this->adapter, '#,##0.00'));
        $this->assertEquals('0.000', $method->invoke($this->adapter, '0.000'));
        $this->assertEquals('#,##0', $method->invoke($this->adapter, '#,##0'));

        // Test decimal places format
        $this->assertEquals('0.00', $method->invoke($this->adapter, '2'));
        $this->assertEquals(NumberFormat::FORMAT_NUMBER, $method->invoke($this->adapter, '0'));

        // Test fallback for unrecognized formats (returns original)
        $this->assertEquals('invalid_format', $method->invoke($this->adapter, 'invalid_format'));
    }

    public function testConvertCustomDateFormat(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('convertCustomDateFormat');
        $method->setAccessible(true);

        // Test various date format conversions
        $this->assertEquals('yyyy-mm-dd', $method->invoke($this->adapter, 'Y-m-d'));
        $this->assertEquals('yyyy-mm-dd hh:mm', $method->invoke($this->adapter, 'Y-m-d H:i'));
        $this->assertEquals('yyyy-mm-dd hh:mm:ss', $method->invoke($this->adapter, 'Y-m-d H:i:s'));

        // Test fallback for unrecognized formats (returns original)
        $this->assertEquals('invalid_date_format', $method->invoke($this->adapter, 'invalid_date_format'));
    }

    public function testGetExcelFormatting(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('getExcelFormatting');
        $method->setAccessible(true);

        // Test specific field formats
        $this->assertEquals(NumberFormat::FORMAT_TEXT, $method->invoke($this->adapter, 'email'));
        $this->assertEquals(NumberFormat::FORMAT_TEXT, $method->invoke($this->adapter, 'phone'));
        $this->assertEquals(NumberFormat::FORMAT_TEXT, $method->invoke($this->adapter, 'web'));
        $this->assertEquals(NumberFormat::FORMAT_TEXT, $method->invoke($this->adapter, 'code'));

        $this->assertEquals('dd.mm.yyyy', $method->invoke($this->adapter, 'deadline'));
        $this->assertEquals('dd.mm.yyyy', $method->invoke($this->adapter, 'event_start'));

        $this->assertEquals('dd.mm.yyyy hh:mm', $method->invoke($this->adapter, 'added'));
        $this->assertEquals('dd.mm.yyyy hh:mm', $method->invoke($this->adapter, 'created'));
        $this->assertEquals('dd.mm.yyyy hh:mm', $method->invoke($this->adapter, 'modified'));

        $this->assertEquals(NumberFormat::FORMAT_NUMBER_00, $method->invoke($this->adapter, 'sell_price'));
        $this->assertEquals(NumberFormat::FORMAT_NUMBER, $method->invoke($this->adapter, 'age'));

        // Test unknown field
        $this->assertEquals(NumberFormat::FORMAT_GENERAL, $method->invoke($this->adapter, 'unknown_field'));
    }

    public function testOptimizeMemoryForExport(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('optimizeMemoryForExport');
        $method->setAccessible(true);

        // This method sets memory limits and garbage collection
        // We can't easily test the actual memory optimization, but we can test it runs without error
        $method->invoke($this->adapter);

        // If we get here without exception, the method executed successfully
        $this->assertTrue(true);
    }

    public function testSetDocumentProperties(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('setDocumentProperties');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $method->invoke($this->adapter, $spreadsheet, 'Test Export');

        $properties = $spreadsheet->getProperties();
        $this->assertEquals('Test Export', $properties->getTitle());
        $this->assertEquals('Export from nZoom', $properties->getDescription());
        $this->assertEquals('nZoom', $properties->getCreator());
        $this->assertEquals('nZoom', $properties->getLastModifiedBy());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testSetSpreadsheetLocale(): void
    {
        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('setSpreadsheetLocale');
        $method->setAccessible(true);

        // This method sets locale for PhpSpreadsheet
        // We can test it runs without error
        $method->invoke($this->adapter);

        // If we get here without exception, the method executed successfully
        $this->assertTrue(true);
    }

    public function testCreateSpreadsheet(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('createSpreadsheet');
        $method->setAccessible(true);

        $exportData = $this->createTestExportData();
        $spreadsheet = $method->invoke($this->adapter, $exportData);

        $this->assertInstanceOf(Spreadsheet::class, $spreadsheet);

        // Check that the sheet was created and has data
        $sheet = $spreadsheet->getActiveSheet();
        $this->assertEquals('Export', $sheet->getTitle());

        // Check that headers were added
        $this->assertEquals('ID', $sheet->getCell('A1')->getValue());
        $this->assertEquals('Name', $sheet->getCell('B1')->getValue());
        $this->assertEquals('Email', $sheet->getCell('C1')->getValue());

        // Check that data was added
        $this->assertEquals(1, $sheet->getCell('A2')->getValue());
        $this->assertEquals('John Doe', $sheet->getCell('B2')->getValue());
        $this->assertEquals('<EMAIL>', $sheet->getCell('C2')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testProcessExportData(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('processExportData');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $exportData = $this->createTestExportData();

        $method->invoke($this->adapter, $sheet, $exportData);

        // Verify headers were added
        $this->assertEquals('ID', $sheet->getCell('A1')->getValue());
        $this->assertEquals('Name', $sheet->getCell('B1')->getValue());
        $this->assertEquals('Email', $sheet->getCell('C1')->getValue());
        $this->assertEquals('Salary', $sheet->getCell('D1')->getValue());
        $this->assertEquals('Active', $sheet->getCell('E1')->getValue());
        $this->assertEquals('Created At', $sheet->getCell('F1')->getValue());

        // Verify data was added
        $this->assertEquals(1, $sheet->getCell('A2')->getValue());
        $this->assertEquals('John Doe', $sheet->getCell('B2')->getValue());
        $this->assertEquals('<EMAIL>', $sheet->getCell('C2')->getValue());
        $this->assertEquals(50000.5, $sheet->getCell('D2')->getValue());
        $this->assertEquals(true, $sheet->getCell('E2')->getValue());

        $this->assertEquals(2, $sheet->getCell('A3')->getValue());
        $this->assertEquals('Jane Smith', $sheet->getCell('B3')->getValue());
        $this->assertEquals('<EMAIL>', $sheet->getCell('C3')->getValue());
        $this->assertEquals(75000.0, $sheet->getCell('D3')->getValue());
        $this->assertEquals(false, $sheet->getCell('E3')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testAddHeaders(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('addHeaders');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $headers = ['ID', 'Name', 'Email', 'Status'];

        $method->invoke($this->adapter, $sheet, $headers);

        // Verify headers were added correctly
        $this->assertEquals('ID', $sheet->getCell('A1')->getValue());
        $this->assertEquals('Name', $sheet->getCell('B1')->getValue());
        $this->assertEquals('Email', $sheet->getCell('C1')->getValue());
        $this->assertEquals('Status', $sheet->getCell('D1')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testStyleHeaderRow(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('styleHeaderRow');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Add some headers first
        $sheet->setCellValue('A1', 'Header 1');
        $sheet->setCellValue('B1', 'Header 2');
        $sheet->setCellValue('C1', 'Header 3');

        $method->invoke($this->adapter, $sheet, 3);

        // Verify styling was applied (we can check if the style object exists)
        $headerStyle = $sheet->getStyle('A1:C1');
        $this->assertNotNull($headerStyle);

        // Check that font is bold
        $this->assertTrue($headerStyle->getFont()->getBold());

        // Check that background fill is set
        $this->assertEquals(
            \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
            $headerStyle->getFill()->getFillType()
        );

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testProcessExportRecord(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('processExportRecord');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Create a test record
        $record = new ExportRecord();
        $record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $record->addValue('name', 'Test User', ExportValue::TYPE_STRING);
        $record->addValue('salary', 50000.75, ExportValue::TYPE_FLOAT);
        $record->addValue('active', true, ExportValue::TYPE_BOOLEAN);

        $method->invoke($this->adapter, $sheet, $record, 2);

        // Verify values were set correctly
        $this->assertEquals(123, $sheet->getCell('A2')->getValue());
        $this->assertEquals('Test User', $sheet->getCell('B2')->getValue());
        $this->assertEquals(50000.75, $sheet->getCell('C2')->getValue());
        $this->assertEquals(true, $sheet->getCell('D2')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testSetCellValueWithFormatting(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('setCellValueWithFormatting');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Test different value types
        $stringValue = new ExportValue('Test String', ExportValue::TYPE_STRING);
        $method->invoke($this->adapter, $sheet, 'A1', $stringValue);
        $this->assertEquals('Test String', $sheet->getCell('A1')->getValue());

        $intValue = new ExportValue(42, ExportValue::TYPE_INTEGER);
        $method->invoke($this->adapter, $sheet, 'B1', $intValue);
        $this->assertEquals(42, $sheet->getCell('B1')->getValue());

        $floatValue = new ExportValue(123.45, ExportValue::TYPE_FLOAT);
        $method->invoke($this->adapter, $sheet, 'C1', $floatValue);
        $this->assertEquals(123.45, $sheet->getCell('C1')->getValue());

        $boolValue = new ExportValue(true, ExportValue::TYPE_BOOLEAN);
        $method->invoke($this->adapter, $sheet, 'D1', $boolValue);
        $this->assertEquals(1, $sheet->getCell('D1')->getValue()); // Excel converts true to 1

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testExportWithLargeDataset(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        // Create a larger dataset to test chunking
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));

        $exportData = new ExportData($header);

        // Add multiple records
        for ($i = 1; $i <= 50; $i++) {
            $record = new ExportRecord();
            $record->addValue('id', $i, ExportValue::TYPE_INTEGER);
            $record->addValue('name', "User {$i}", ExportValue::TYPE_STRING);
            $exportData->addRecord($record);
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'excel_large_test_') . '.xlsx';

        try {
            // Set small chunk size to test chunking
            $this->adapter->setConfiguration(['chunk_size' => 10]);
            $this->adapter->export($tempFile, 'xlsx', $exportData);

            // Verify file was created
            $this->assertFileExists($tempFile);
            $this->assertGreaterThan(0, filesize($tempFile));

        } finally {
            // Clean up
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    public function testExportWithSpecialCharacters(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        // Create data with special characters
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('description', 'Description', ExportValue::TYPE_STRING));

        $record = new ExportRecord();
        $record->addValue('name', 'Müller & Söhne', ExportValue::TYPE_STRING);
        $record->addValue('description', 'Special chars: àáâãäåæçèéêë', ExportValue::TYPE_STRING);

        $exportData = new ExportData($header);
        $exportData->addRecord($record);

        $tempFile = tempnam(sys_get_temp_dir(), 'excel_special_test_') . '.xlsx';

        try {
            $this->adapter->export($tempFile, 'xlsx', $exportData);

            // Verify file was created
            $this->assertFileExists($tempFile);
            $this->assertGreaterThan(0, filesize($tempFile));

        } finally {
            // Clean up
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    // Test error handling methods

    public function testHandleExportRecordCellError(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('handleExportRecordCellError');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Create a test exception
        $exception = new \Exception('Test cell processing error');

        // Create a test record
        $record = new ExportRecord();
        $record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $record->addValue('name', 'Test User', ExportValue::TYPE_STRING);

        // Call the error handler
        $method->invoke($this->adapter, $sheet, 'B', 5, $exception, 1, $record);

        // Verify error was logged
        $this->assertNotEmpty($this->logger->errors);
        $this->assertStringContainsString('Excel export ExportRecord cell error: Test cell processing error', $this->logger->errors[0]);
        $this->assertStringContainsString('in column 1 for record at row 5', $this->logger->errors[0]);

        // Verify error placeholder was set in the cell
        $this->assertEquals('[ERROR]', $sheet->getCell('B5')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testHandleExportRecordCellErrorWithoutLogger(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        // Remove logger from registry
        $this->registry->remove('logger');

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('handleExportRecordCellError');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $exception = new \Exception('Test error without logger');
        $record = new ExportRecord();

        // Should not throw an exception even without logger
        $method->invoke($this->adapter, $sheet, 'A', 3, $exception, 0, $record);

        // Verify error placeholder was still set
        $this->assertEquals('[ERROR]', $sheet->getCell('A3')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testHandleExportRecordCellErrorWithComplexException(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('handleExportRecordCellError');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Create a more complex exception with nested information
        $innerException = new \InvalidArgumentException('Invalid cell value');
        $exception = new \RuntimeException('Cell processing failed', 500, $innerException);

        $record = new ExportRecord();
        $record->addValue('complex_field', 'Complex Value', ExportValue::TYPE_STRING);

        $method->invoke($this->adapter, $sheet, 'C', 10, $exception, 2, $record);

        // Verify detailed error was logged
        $this->assertNotEmpty($this->logger->errors);
        $errorMessage = $this->logger->errors[0];
        $this->assertStringContainsString('Excel export ExportRecord cell error: Cell processing failed', $errorMessage);
        $this->assertStringContainsString('in column 2 for record at row 10', $errorMessage);

        // Verify error placeholder was set
        $this->assertEquals('[ERROR]', $sheet->getCell('C10')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testHandleExportRecordCellErrorWithDifferentCellPositions(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('handleExportRecordCellError');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $exception = new \Exception('Position test error');
        $record = new ExportRecord();

        // Test different cell positions
        $testCases = [
            ['A', 1, 0],
            ['Z', 100, 25],
            ['AA', 1000, 26],
            ['AB', 50, 27]
        ];

        foreach ($testCases as [$colLetter, $row, $colIndex]) {
            $method->invoke($this->adapter, $sheet, $colLetter, $row, $exception, $colIndex, $record);

            // Verify error placeholder was set in correct position
            $this->assertEquals('[ERROR]', $sheet->getCell("{$colLetter}{$row}")->getValue());

            // Verify error was logged with correct position info
            $lastError = end($this->logger->errors);
            $this->assertStringContainsString("in column {$colIndex} for record at row {$row}", $lastError);
        }

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testHandleExportRecordCellErrorWithRecordInformation(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('handleExportRecordCellError');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $exception = new \Exception('Record information test');

        // Create a record with multiple values
        $record = new ExportRecord();
        $record->addValue('id', 456, ExportValue::TYPE_INTEGER);
        $record->addValue('name', 'Error Test User', ExportValue::TYPE_STRING);
        $record->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);

        $method->invoke($this->adapter, $sheet, 'B', 7, $exception, 1, $record);

        // Verify the record parameter is properly handled (method should not fail with complex records)
        $this->assertEquals('[ERROR]', $sheet->getCell('B7')->getValue());
        $this->assertNotEmpty($this->logger->errors);

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testHandleExportRecordCellErrorPreservesOtherCells(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('handleExportRecordCellError');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set some existing cell values
        $sheet->setCellValue('A1', 'Header 1');
        $sheet->setCellValue('B1', 'Header 2');
        $sheet->setCellValue('A2', 'Good Value');
        $sheet->setCellValue('C2', 'Another Good Value');

        $exception = new \Exception('Isolated error test');
        $record = new ExportRecord();

        // Handle error in B2
        $method->invoke($this->adapter, $sheet, 'B', 2, $exception, 1, $record);

        // Verify error cell was set
        $this->assertEquals('[ERROR]', $sheet->getCell('B2')->getValue());

        // Verify other cells were not affected
        $this->assertEquals('Header 1', $sheet->getCell('A1')->getValue());
        $this->assertEquals('Header 2', $sheet->getCell('B1')->getValue());
        $this->assertEquals('Good Value', $sheet->getCell('A2')->getValue());
        $this->assertEquals('Another Good Value', $sheet->getCell('C2')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testHandleExportRecordCellErrorWithEmptyException(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $reflection = new \ReflectionClass($this->adapter);
        $method = $reflection->getMethod('handleExportRecordCellError');
        $method->setAccessible(true);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Test with exception that has empty message
        $exception = new \Exception('');
        $record = new ExportRecord();

        $method->invoke($this->adapter, $sheet, 'D', 15, $exception, 3, $record);

        // Should still log something and set error placeholder
        $this->assertEquals('[ERROR]', $sheet->getCell('D15')->getValue());
        $this->assertNotEmpty($this->logger->errors);

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }

    public function testHandleExportRecordCellErrorIntegrationWithProcessExportRecord(): void
    {
        if (!class_exists(Spreadsheet::class)) {
            $this->markTestSkipped('PhpSpreadsheet not available');
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Create a record that might cause an error during processing
        $record = new ExportRecord();
        $record->addValue('normal_field', 'Normal Value', ExportValue::TYPE_STRING);

        // Add a value that might cause issues (we'll simulate this by testing the error handling path)
        $record->addValue('problematic_field', null, ExportValue::TYPE_STRING);

        // Test that processExportRecord can handle errors gracefully
        $reflection = new \ReflectionClass($this->adapter);
        $processMethod = $reflection->getMethod('processExportRecord');
        $processMethod->setAccessible(true);

        // This should not throw an exception even if individual cells fail
        $processMethod->invoke($this->adapter, $sheet, $record, 3);

        // Verify that normal processing continued
        $this->assertEquals('Normal Value', $sheet->getCell('A3')->getValue());

        // The null value should be handled gracefully (converted to empty string)
        $this->assertEquals('', $sheet->getCell('B3')->getValue());

        // Clean up
        $spreadsheet->disconnectWorksheets();
    }
}
