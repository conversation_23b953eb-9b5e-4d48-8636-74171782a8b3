<?php

namespace Tests\Nzoom\Export\Adapter;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Adapter\AbstractExportFormatAdapter;
use Nzoom\Export\Adapter\ExportFormatAdapterInterface;
use Nzoom\Export\Entity\ExportData;
use Nzoom\I18n\I18n;

/**
 * Concrete test implementation of AbstractExportFormatAdapter
 * This allows us to test the abstract class functionality
 */
class TestExportFormatAdapter extends AbstractExportFormatAdapter
{
    public function export($output, string $type, ExportData $data, array $options = []): void
    {
        // Simple test implementation
        if (is_resource($output)) {
            fwrite($output, "Test export data for type: {$type}");
        }
    }

    public static function getSupportedExtensions(): array
    {
        return ['test'];
    }

    public function getMimeType(): string
    {
        return 'application/test';
    }

    public function getDefaultExtension(): string
    {
        return 'test';
    }

    public static function supportsFormat(string $format): bool
    {
        return $format === 'test';
    }

    public function getFormatName(): string
    {
        return 'test';
    }

    // Expose protected methods for testing
    public function testGetExportFilename($prefix, string $extension = null): string
    {
        return $this->getExportFilename($prefix, $extension);
    }

    public function testSendHeaders(string $filename, string $contentType = null): void
    {
        $this->sendHeaders($filename, $contentType);
    }

    public function testHandleExportError(string $message, int $statusCode = 400): void
    {
        $this->handleExportError($message, $statusCode);
    }

    public function testGetRecordHeaders($record): array
    {
        return $this->getRecordHeaders($record);
    }

    public function testValidateAndPrepareSaveTarget($file)
    {
        return $this->validateAndPrepareSaveTarget($file);
    }

    public function getConfiguration(): array
    {
        return $this->configuration;
    }
}

/**
 * Mock record class for testing getRecordHeaders
 */
class MockRecord
{
    private $data;

    public function __construct($data = [])
    {
        $this->data = $data;
    }

    public function getAll()
    {
        return $this->data;
    }

    public function getVars()
    {
        return $this->data;
    }
}

/**
 * Mock logger for testing
 */
class MockLogger
{
    public $errors = [];

    public function error($message)
    {
        $this->errors[] = $message;
    }
}

/**
 * Test case for AbstractExportFormatAdapter
 */
class AbstractExportFormatAdapterTest extends ExportTestCase
{
    private TestExportFormatAdapter $adapter;
    private RegistryMock $registry;
    private $translator;
    private MockLogger $logger;

    protected function setUp(): void
    {
        parent::setUp();

        // Include global mock classes
        require_once __DIR__ . '/../../TestHelpers/GlobalMocks.php';

        // Create mock dependencies
        $this->translator = $this->createMock(I18n::class);
        $this->translator->method('translate')->willReturnCallback(function($key, $params = []) {
            return $key; // Simple pass-through for testing
        });

        $this->logger = new MockLogger();

        // Create registry mock
        $this->registry = new RegistryMock();
        $this->registry->set('translater', $this->translator);
        $this->registry->set('logger', $this->logger);

        // Create adapter instance
        $this->adapter = new TestExportFormatAdapter($this->registry, 'test_module', 'test_controller');
    }

    public function testConstructor(): void
    {
        $adapter = new TestExportFormatAdapter($this->registry, 'users', 'list');
        $this->assertInstanceOf(AbstractExportFormatAdapter::class, $adapter);
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
    }

    public function testConstructorWithoutTranslator(): void
    {
        $registryWithoutTranslator = new RegistryMock();
        $adapter = new TestExportFormatAdapter($registryWithoutTranslator, 'test', 'test');
        $this->assertInstanceOf(AbstractExportFormatAdapter::class, $adapter);
    }

    public function testSetConfiguration(): void
    {
        $config = ['option1' => 'value1', 'option2' => 'value2'];
        $result = $this->adapter->setConfiguration($config);

        // Should return self for fluent interface
        $this->assertSame($this->adapter, $result);

        // Configuration should be set
        $this->assertEquals($config, $this->adapter->getConfiguration());
    }

    public function testSetConfigurationMerging(): void
    {
        $config1 = ['option1' => 'value1', 'option2' => 'value2'];
        $config2 = ['option2' => 'new_value2', 'option3' => 'value3'];

        $this->adapter->setConfiguration($config1);
        $this->adapter->setConfiguration($config2);

        $expected = ['option1' => 'value1', 'option2' => 'new_value2', 'option3' => 'value3'];
        $this->assertEquals($expected, $this->adapter->getConfiguration());
    }

    public function testGetExportFilenameWithString(): void
    {
        $filename = $this->adapter->testGetExportFilename('export_file', 'csv');
        $this->assertEquals('export_file.csv', $filename);
    }

    public function testGetExportFilenameWithArray(): void
    {
        $filename = $this->adapter->testGetExportFilename(['export_file', 'ignored'], 'xlsx');
        $this->assertEquals('export_file.xlsx', $filename);
    }

    public function testGetExportFilenameWithEmptyArray(): void
    {
        $filename = $this->adapter->testGetExportFilename([], 'json');
        
        // Should generate filename with module_controller_export pattern
        $this->assertStringContainsString('test_module_test_controller_export_', $filename);
        $this->assertStringEndsWith('.json', $filename);
    }

    public function testGetExportFilenameWithNull(): void
    {
        $filename = $this->adapter->testGetExportFilename(null, 'pdf');
        
        // Should generate filename with module_controller_export pattern
        $this->assertStringContainsString('test_module_test_controller_export_', $filename);
        $this->assertStringEndsWith('.pdf', $filename);
    }

    public function testGetExportFilenameWithEmptyString(): void
    {
        $filename = $this->adapter->testGetExportFilename('', 'txt');
        
        // Should generate filename when empty string is provided
        $this->assertStringContainsString('test_module_test_controller_export_', $filename);
        $this->assertStringEndsWith('.txt', $filename);
    }

    public function testGetExportFilenameWithDefaultExtension(): void
    {
        $filename = $this->adapter->testGetExportFilename('export_file');
        $this->assertEquals('export_file.test', $filename);
    }

    public function testGetExportFilenameWithExistingExtension(): void
    {
        $filename = $this->adapter->testGetExportFilename('already_has.csv', 'csv');
        $this->assertEquals('already_has.csv', $filename);
    }

    public function testGetExportFilenameWithDifferentExtension(): void
    {
        $filename = $this->adapter->testGetExportFilename('file.txt', 'csv');
        $this->assertEquals('file.txt.csv', $filename);
    }

    public function testSendHeaders(): void
    {
        // We can't actually test header sending in unit tests, but we can test the method exists
        // and doesn't throw exceptions when called with valid parameters
        $this->assertTrue(method_exists($this->adapter, 'testSendHeaders'));

        // Test that the method can be called without fatal errors
        // Note: In a real environment, this would send headers
        try {
            // Use output buffering to capture any output
            if (!headers_sent()) {
                ob_start();
                $this->adapter->testSendHeaders('test.csv', 'text/csv');
                ob_end_clean();
            }
            $this->assertTrue(true); // Method executed without fatal error
        } catch (\Throwable $e) {
            // In test environment, header functions may not work as expected
            $this->assertTrue(true); // Still consider this a pass
        }
    }

    public function testSendHeadersWithDefaultContentType(): void
    {
        // Similar to above test but with default content type
        $this->assertTrue(method_exists($this->adapter, 'testSendHeaders'));

        try {
            if (!headers_sent()) {
                ob_start();
                $this->adapter->testSendHeaders('test.test');
                ob_end_clean();
            }
            $this->assertTrue(true);
        } catch (\Throwable $e) {
            $this->assertTrue(true);
        }
    }

    public function testHandleExportErrorWithLogger(): void
    {
        ob_start();

        try {
            $this->adapter->testHandleExportError('Test error message', 500);
        } catch (\Exception $e) {
            // Expected - method may exit
        }

        ob_end_clean();

        // Check that error was logged
        $this->assertContains('Export error (test): Test error message', $this->logger->errors);

        // Check that ajax_result was set
        $ajaxResult = $this->registry->get('ajax_result');
        $this->assertNotNull($ajaxResult);

        $decoded = json_decode($ajaxResult, true);
        $this->assertEquals('Test error message', $decoded['error']);
        $this->assertEquals('error', $decoded['status']);
        $this->assertEquals('test', $decoded['format']);
        $this->assertArrayHasKey('timestamp', $decoded);
    }

    public function testHandleExportErrorWithoutLogger(): void
    {
        // Remove logger from registry
        $this->registry->remove('logger');

        ob_start();

        try {
            $this->adapter->testHandleExportError('Test error without logger');
        } catch (\Exception $e) {
            // Expected - method may exit
        }

        ob_end_clean();

        // Check that ajax_result was still set
        $ajaxResult = $this->registry->get('ajax_result');
        $this->assertNotNull($ajaxResult);

        $decoded = json_decode($ajaxResult, true);
        $this->assertEquals('Test error without logger', $decoded['error']);
    }

    public function testHandleExportErrorWithAjaxRequest(): void
    {
        // Mock AJAX request
        $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';

        ob_start();

        try {
            $this->adapter->testHandleExportError('AJAX error test', 400);
        } catch (\Exception $e) {
            // Expected - method will exit for AJAX requests
        }

        ob_end_clean();

        // Check that ajax_result was set
        $ajaxResult = $this->registry->get('ajax_result');
        $this->assertNotNull($ajaxResult);
        $this->assertJson($ajaxResult);

        $decoded = json_decode($ajaxResult, true);
        $this->assertEquals('AJAX error test', $decoded['error']);

        // Clean up
        unset($_SERVER['HTTP_X_REQUESTED_WITH']);
    }

    public function testGetRecordHeadersWithGetAllMethod(): void
    {
        $record = new MockRecord(['id' => 1, 'name' => 'Test', 'email' => '<EMAIL>']);
        $headers = $this->adapter->testGetRecordHeaders($record);

        $this->assertEquals(['id', 'name', 'email'], $headers);
    }

    public function testGetRecordHeadersWithGetVarsMethod(): void
    {
        // Create a mock object that only has getVars method
        $record = new class {
            public function getVars() {
                return ['field1' => 'value1', 'field2' => 'value2'];
            }
        };

        $headers = $this->adapter->testGetRecordHeaders($record);
        $this->assertEquals(['field1', 'field2'], $headers);
    }

    public function testGetRecordHeadersWithArray(): void
    {
        $record = ['column1' => 'data1', 'column2' => 'data2', 'column3' => 'data3'];
        $headers = $this->adapter->testGetRecordHeaders($record);

        $this->assertEquals(['column1', 'column2', 'column3'], $headers);
    }

    public function testGetRecordHeadersWithUnsupportedType(): void
    {
        $record = 'string_record';
        $headers = $this->adapter->testGetRecordHeaders($record);

        $this->assertEquals([], $headers);
    }

    public function testGetRecordHeadersWithEmptyArray(): void
    {
        $record = [];
        $headers = $this->adapter->testGetRecordHeaders($record);

        $this->assertEquals([], $headers);
    }

    public function testGetFormatOptions(): void
    {
        $options = $this->adapter->getFormatOptions();
        $this->assertIsArray($options);
        $this->assertEmpty($options); // Default implementation returns empty array
    }

    public function testValidateAndPrepareSaveTargetWithValidFilePath(): void
    {
        $tempDir = sys_get_temp_dir();
        $filePath = $tempDir . '/test_export.csv';

        $result = $this->adapter->testValidateAndPrepareSaveTarget($filePath);
        $this->assertEquals($filePath, $result);
    }

    public function testValidateAndPrepareSaveTargetWithInvalidDirectory(): void
    {
        $invalidPath = '/non/existent/directory/test.csv';

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Directory does not exist');

        $this->adapter->testValidateAndPrepareSaveTarget($invalidPath);
    }

    public function testValidateAndPrepareSaveTargetWithPhpOutput(): void
    {
        $result = $this->adapter->testValidateAndPrepareSaveTarget('php://output');
        $this->assertEquals('php://output', $result);
    }

    public function testValidateAndPrepareSaveTargetWithPhpMemory(): void
    {
        $result = $this->adapter->testValidateAndPrepareSaveTarget('php://memory');
        $this->assertEquals('php://memory', $result);
    }

    public function testValidateAndPrepareSaveTargetWithPhpTemp(): void
    {
        $result = $this->adapter->testValidateAndPrepareSaveTarget('php://temp');
        $this->assertEquals('php://temp', $result);
    }

    public function testValidateAndPrepareSaveTargetWithPhpTempWithParams(): void
    {
        $wrapper = 'php://temp/maxmemory:1048576';
        $result = $this->adapter->testValidateAndPrepareSaveTarget($wrapper);
        $this->assertEquals($wrapper, $result);
    }

    public function testValidateAndPrepareSaveTargetWithUnsupportedWrapper(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unsupported stream wrapper');

        $this->adapter->testValidateAndPrepareSaveTarget('unsupported://example.com');
    }

    public function testValidateAndPrepareSaveTargetWithValidFilePointer(): void
    {
        $fp = fopen('php://temp', 'w+');

        $result = $this->adapter->testValidateAndPrepareSaveTarget($fp);
        $this->assertSame($fp, $result);

        fclose($fp);
    }

    public function testValidateAndPrepareSaveTargetWithReadOnlyFilePointer(): void
    {
        $fp = fopen('php://temp', 'r');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('File pointer is not writable');

        try {
            $this->adapter->testValidateAndPrepareSaveTarget($fp);
        } finally {
            fclose($fp);
        }
    }

    public function testValidateAndPrepareSaveTargetWithInvalidType(): void
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('File parameter must be either a string');

        $this->adapter->testValidateAndPrepareSaveTarget(123); // Invalid type
    }

    public function testValidateAndPrepareSaveTargetWithWrongResourceType(): void
    {
        $resource = curl_init();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid resource type');

        try {
            $this->adapter->testValidateAndPrepareSaveTarget($resource);
        } finally {
            curl_close($resource);
        }
    }

    public function testConcreteImplementationMethods(): void
    {
        // Test that our concrete implementation works
        $this->assertEquals(['test'], $this->adapter->getSupportedExtensions());
        $this->assertEquals('application/test', $this->adapter->getMimeType());
        $this->assertEquals('test', $this->adapter->getDefaultExtension());
        $this->assertTrue($this->adapter->supportsFormat('test'));
        $this->assertFalse($this->adapter->supportsFormat('csv'));
        $this->assertEquals('test', $this->adapter->getFormatName());
    }

    public function testExportMethod(): void
    {
        $output = fopen('php://temp', 'w+');
        $exportData = $this->createMock(\Nzoom\Export\Entity\ExportData::class);

        $this->adapter->export($output, 'test', $exportData, ['option' => 'value']);

        rewind($output);
        $content = stream_get_contents($output);
        fclose($output);

        $this->assertEquals('Test export data for type: test', $content);
    }

    public function testPrivateMethodAccessibility(): void
    {
        // Test that we can access private methods via reflection
        $reflection = new \ReflectionClass($this->adapter);

        $validateStringTarget = $reflection->getMethod('validateStringTarget');
        $validateStringTarget->setAccessible(true);

        $result = $validateStringTarget->invoke($this->adapter, 'php://output');
        $this->assertEquals('php://output', $result);
    }
}
