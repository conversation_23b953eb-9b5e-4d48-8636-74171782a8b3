<?php

namespace Tests\Nzoom\Export\Adapter;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Adapter\CsvExportFormatAdapter;
use Nzoom\Export\Entity\ExportData;

/**
 * Test case for CsvExportFormatAdapter
 */
class CsvExportFormatAdapterTest extends ExportTestCase
{
    private CsvExportFormatAdapter $adapter;
    private RegistryMock $registry;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create mock registry
        $this->registry = new RegistryMock();
        
        // Create adapter instance
        $this->adapter = new CsvExportFormatAdapter($this->registry, 'test_module', 'test_controller');
    }

    public function testGetSupportedExtensions(): void
    {
        $extensions = CsvExportFormatAdapter::getSupportedExtensions();
        $this->assertIsArray($extensions);
        $this->assertContains('csv', $extensions);
        $this->assertCount(1, $extensions);
    }

    public function testSupportsFormat(): void
    {
        $this->assertTrue(CsvExportFormatAdapter::supportsFormat('csv'));
        $this->assertTrue(CsvExportFormatAdapter::supportsFormat('CSV'));
        $this->assertFalse(CsvExportFormatAdapter::supportsFormat('xlsx'));
        $this->assertFalse(CsvExportFormatAdapter::supportsFormat('json'));
    }

    public function testGetMimeType(): void
    {
        $mimeType = $this->adapter->getMimeType();
        $this->assertEquals('text/csv', $mimeType);
    }

    public function testGetDefaultExtension(): void
    {
        $extension = $this->adapter->getDefaultExtension();
        $this->assertEquals('csv', $extension);
    }

    public function testGetFormatName(): void
    {
        $formatName = $this->adapter->getFormatName();
        $this->assertEquals('csv', $formatName);
    }

    public function testGetFormatOptions(): void
    {
        $options = $this->adapter->getFormatOptions();
        $this->assertIsArray($options);
        $this->assertArrayHasKey('delimiter', $options);
        $this->assertArrayHasKey('type', $options['delimiter']);
        $this->assertEquals('select', $options['delimiter']['type']);
    }

    public function testExportToFile(): void
    {
        $exportData = $this->createSampleExportData(2);
        $tempFile = $this->createTempFile('', 'csv');

        try {
            $this->adapter->export($tempFile, 'csv', $exportData);
            
            $this->assertFileExistsAndReadable($tempFile);
            
            $content = file_get_contents($tempFile);
            $this->assertNotEmpty($content);
            
            // Check headers
            $this->assertContainsHeaders($content, ['ID', 'Name', 'Email', 'Created At']);
            
            // Check data values
            $this->assertContainsValues($content, ['User 1', '<EMAIL>', 'User 2', '<EMAIL>']);
            
        } finally {
            $this->cleanupTempFile($tempFile);
        }
    }

    public function testExportWithCustomDelimiter(): void
    {
        $exportData = $this->createSampleExportData(1);
        $tempFile = $this->createTempFile('', 'csv');

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, ['delimiter' => 'semicolon']);
            
            $content = file_get_contents($tempFile);
            $this->assertStringContainsString(';', $content);
            $this->assertStringNotContainsString(',', $content);
            
        } finally {
            $this->cleanupTempFile($tempFile);
        }
    }

    public function testExportWithTabDelimiter(): void
    {
        $exportData = $this->createSampleExportData(1);
        $tempFile = $this->createTempFile('', 'csv');

        try {
            $this->adapter->export($tempFile, 'csv', $exportData, ['delimiter' => 'tab']);
            
            $content = file_get_contents($tempFile);
            $this->assertStringContainsString("\t", $content);
            
        } finally {
            $this->cleanupTempFile($tempFile);
        }
    }

    public function testExportToFilePointer(): void
    {
        $exportData = $this->createSampleExportData(1);
        $tempFile = $this->createTempFile('', 'csv');

        try {
            $filePointer = fopen($tempFile, 'w');
            $this->adapter->export($filePointer, 'csv', $exportData);
            fclose($filePointer);
            
            $this->assertFileExistsAndReadable($tempFile);
            $content = file_get_contents($tempFile);
            $this->assertNotEmpty($content);
            
        } finally {
            $this->cleanupTempFile($tempFile);
        }
    }

    public function testExportWithUnsupportedType(): void
    {
        $exportData = $this->createSampleExportData(1);
        $tempFile = $this->createTempFile('', 'txt');

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unsupported export type: xlsx');

        try {
            $this->adapter->export($tempFile, 'xlsx', $exportData);
        } finally {
            $this->cleanupTempFile($tempFile);
        }
    }

    public function testExportWithInvalidFile(): void
    {
        $exportData = $this->createSampleExportData(1);

        $this->expectException(\Exception::class);

        $this->adapter->export(123, 'csv', $exportData);
    }
}
