<?php

namespace Tests\Nzoom\Export\Entity;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;

/**
 * Test case for ExportHeader
 */
class ExportHeaderTest extends ExportTestCase
{
    private ExportHeader $header;

    protected function setUp(): void
    {
        parent::setUp();
        $this->header = new ExportHeader();
    }

    public function testConstructorWithDefaults(): void
    {
        $header = new ExportHeader();
        
        $this->assertEquals('f6f9fc', $header->getBackgroundColor());
        $this->assertEquals([], $header->getStyles());
        $this->assertEquals(0, $header->count());
    }

    public function testConstructorWithParameters(): void
    {
        $styles = ['font-weight' => 'bold', 'color' => 'white'];
        $header = new ExportHeader('ff0000', $styles);
        
        $this->assertEquals('ff0000', $header->getBackgroundColor());
        $this->assertEquals($styles, $header->getStyles());
    }

    public function testAddColumn(): void
    {
        $column = new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER);
        $this->header->addColumn($column);
        
        $this->assertEquals(1, $this->header->count());
        $this->assertTrue($this->header->hasColumn('id'));
        $this->assertSame($column, $this->header->getColumnByVarName('id'));
    }

    public function testAddMultipleColumns(): void
    {
        $column1 = new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER);
        $column2 = new ExportColumn('name', 'Name', ExportValue::TYPE_STRING);
        $column3 = new ExportColumn('email', 'Email', ExportValue::TYPE_STRING);
        
        $this->header->addColumn($column1);
        $this->header->addColumn($column2);
        $this->header->addColumn($column3);
        
        $this->assertEquals(3, $this->header->count());
        $this->assertTrue($this->header->hasColumn('id'));
        $this->assertTrue($this->header->hasColumn('name'));
        $this->assertTrue($this->header->hasColumn('email'));
    }

    public function testAddColumnWithDuplicateVarName(): void
    {
        $column1 = new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER);
        $column2 = new ExportColumn('id', 'Identifier', ExportValue::TYPE_STRING);
        
        $this->header->addColumn($column1);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('A column with variable name "id" already exists');
        
        $this->header->addColumn($column2);
    }

    public function testHasColumn(): void
    {
        $column = new ExportColumn('test_col', 'Test Column');
        $this->header->addColumn($column);
        
        $this->assertTrue($this->header->hasColumn('test_col'));
        $this->assertFalse($this->header->hasColumn('nonexistent'));
    }

    public function testGetColumns(): void
    {
        $column1 = new ExportColumn('id', 'ID');
        $column2 = new ExportColumn('name', 'Name');
        
        $this->header->addColumn($column1);
        $this->header->addColumn($column2);
        
        $columns = $this->header->getColumns();
        $this->assertCount(2, $columns);
        $this->assertSame($column1, $columns[0]);
        $this->assertSame($column2, $columns[1]);
    }

    public function testGetColumnAt(): void
    {
        $column1 = new ExportColumn('id', 'ID');
        $column2 = new ExportColumn('name', 'Name');
        
        $this->header->addColumn($column1);
        $this->header->addColumn($column2);
        
        $this->assertSame($column1, $this->header->getColumnAt(0));
        $this->assertSame($column2, $this->header->getColumnAt(1));
        $this->assertNull($this->header->getColumnAt(2));
        $this->assertNull($this->header->getColumnAt(-1));
    }

    public function testGetColumnByVarName(): void
    {
        $column1 = new ExportColumn('id', 'ID');
        $column2 = new ExportColumn('name', 'Name');
        
        $this->header->addColumn($column1);
        $this->header->addColumn($column2);
        
        $this->assertSame($column1, $this->header->getColumnByVarName('id'));
        $this->assertSame($column2, $this->header->getColumnByVarName('name'));
        $this->assertNull($this->header->getColumnByVarName('nonexistent'));
    }

    public function testSetBackgroundColor(): void
    {
        $this->header->setBackgroundColor('00ff00');
        $this->assertEquals('00ff00', $this->header->getBackgroundColor());
    }

    public function testSetStyles(): void
    {
        $styles = ['border' => '1px solid black', 'padding' => '10px'];
        $this->header->setStyles($styles);
        
        $this->assertEquals($styles, $this->header->getStyles());
    }

    public function testAddStyle(): void
    {
        $this->header->addStyle('font-size', '14px');
        $this->header->addStyle('text-align', 'center');
        
        $expectedStyles = ['font-size' => '14px', 'text-align' => 'center'];
        $this->assertEquals($expectedStyles, $this->header->getStyles());
    }

    public function testAddStyleOverwritesExisting(): void
    {
        $this->header->addStyle('color', 'red');
        $this->header->addStyle('color', 'blue');
        
        $this->assertEquals(['color' => 'blue'], $this->header->getStyles());
    }

    public function testGetLabels(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'User ID'));
        $this->header->addColumn(new ExportColumn('name', 'Full Name'));
        $this->header->addColumn(new ExportColumn('email', 'Email Address'));
        
        $labels = $this->header->getLabels();
        $this->assertEquals(['User ID', 'Full Name', 'Email Address'], $labels);
    }

    public function testGetVarNames(): void
    {
        $this->header->addColumn(new ExportColumn('user_id', 'User ID'));
        $this->header->addColumn(new ExportColumn('full_name', 'Full Name'));
        $this->header->addColumn(new ExportColumn('email_address', 'Email'));
        
        $varNames = $this->header->getVarNames();
        $this->assertEquals(['user_id', 'full_name', 'email_address'], $varNames);
    }

    public function testGetTypes(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $this->header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        $this->header->addColumn(new ExportColumn('price', 'Price', ExportValue::TYPE_FLOAT));
        
        $types = $this->header->getTypes();
        $this->assertEquals([ExportValue::TYPE_INTEGER, ExportValue::TYPE_STRING, ExportValue::TYPE_FLOAT], $types);
    }

    public function testReorderColumns(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID'));
        $this->header->addColumn(new ExportColumn('name', 'Name'));
        $this->header->addColumn(new ExportColumn('email', 'Email'));
        
        // Reorder to: email, id, name
        $this->header->reorderColumns(['email', 'id', 'name']);
        
        $varNames = $this->header->getVarNames();
        $this->assertEquals(['email', 'id', 'name'], $varNames);
        
        // Verify column mapping is updated
        $this->assertSame($this->header->getColumnByVarName('email'), $this->header->getColumnAt(0));
        $this->assertSame($this->header->getColumnByVarName('id'), $this->header->getColumnAt(1));
        $this->assertSame($this->header->getColumnByVarName('name'), $this->header->getColumnAt(2));
    }

    public function testReorderColumnsPartialOrder(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID'));
        $this->header->addColumn(new ExportColumn('name', 'Name'));
        $this->header->addColumn(new ExportColumn('email', 'Email'));
        $this->header->addColumn(new ExportColumn('phone', 'Phone'));
        
        // Only specify order for some columns
        $this->header->reorderColumns(['email', 'name']);
        
        $varNames = $this->header->getVarNames();
        // Should be: email, name, id, phone (specified first, then remaining in original order)
        $this->assertEquals(['email', 'name', 'id', 'phone'], $varNames);
    }

    public function testReorderColumnsWithEmptyArray(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID'));
        $this->header->addColumn(new ExportColumn('name', 'Name'));
        
        $originalVarNames = $this->header->getVarNames();
        
        // Empty array should not change order
        $this->header->reorderColumns([]);
        
        $this->assertEquals($originalVarNames, $this->header->getVarNames());
    }

    public function testReorderColumnsWithInvalidVarName(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID'));
        $this->header->addColumn(new ExportColumn('name', 'Name'));
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Column with variable name "nonexistent" does not exist');
        
        $this->header->reorderColumns(['name', 'nonexistent', 'id']);
    }

    public function testValidateRecordValid(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $this->header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        
        $record = new ExportRecord();
        $record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $record->addValue('name', 'Test User', ExportValue::TYPE_STRING);
        
        $this->assertTrue($this->header->validateRecord($record));
    }

    public function testValidateRecordInvalidCount(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $this->header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        
        $record = new ExportRecord();
        $record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        // Missing second value
        
        $this->assertFalse($this->header->validateRecord($record));
    }

    public function testValidateRecordInvalidType(): void
    {
        $this->header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $this->header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        
        $record = new ExportRecord();
        $record->addValue('id', 'not a number', ExportValue::TYPE_INTEGER);
        $record->addValue('name', 'Test User', ExportValue::TYPE_STRING);
        
        $this->assertFalse($this->header->validateRecord($record));
    }

    public function testIteratorImplementation(): void
    {
        $column1 = new ExportColumn('id', 'ID');
        $column2 = new ExportColumn('name', 'Name');
        $column3 = new ExportColumn('email', 'Email');
        
        $this->header->addColumn($column1);
        $this->header->addColumn($column2);
        $this->header->addColumn($column3);
        
        $iteratedColumns = [];
        $iteratedKeys = [];
        
        foreach ($this->header as $key => $column) {
            $iteratedKeys[] = $key;
            $iteratedColumns[] = $column;
        }
        
        $this->assertEquals([0, 1, 2], $iteratedKeys);
        $this->assertSame($column1, $iteratedColumns[0]);
        $this->assertSame($column2, $iteratedColumns[1]);
        $this->assertSame($column3, $iteratedColumns[2]);
    }

    public function testIteratorWithEmptyHeader(): void
    {
        $iteratedColumns = [];
        
        foreach ($this->header as $column) {
            $iteratedColumns[] = $column;
        }
        
        $this->assertEmpty($iteratedColumns);
    }

    public function testCountableImplementation(): void
    {
        $this->assertEquals(0, count($this->header));
        
        $this->header->addColumn(new ExportColumn('id', 'ID'));
        $this->assertEquals(1, count($this->header));
        
        $this->header->addColumn(new ExportColumn('name', 'Name'));
        $this->assertEquals(2, count($this->header));
    }

    public function testComplexScenario(): void
    {
        // Create a complex header with multiple columns and styling
        $header = new ExportHeader('cccccc', ['font-weight' => 'bold']);
        
        $header->addColumn(new ExportColumn('id', 'User ID', ExportValue::TYPE_INTEGER, '#,##0', 80));
        $header->addColumn(new ExportColumn('first_name', 'First Name', ExportValue::TYPE_STRING, '', 120));
        $header->addColumn(new ExportColumn('last_name', 'Last Name', ExportValue::TYPE_STRING, '', 120));
        $header->addColumn(new ExportColumn('email', 'Email Address', ExportValue::TYPE_STRING, '', 200));
        $header->addColumn(new ExportColumn('created_at', 'Created', ExportValue::TYPE_DATETIME, 'Y-m-d H:i', 150));
        
        // Add additional styling
        $header->addStyle('border', '1px solid black');
        $header->addStyle('text-align', 'center');
        
        // Test all functionality
        $this->assertEquals(5, $header->count());
        $this->assertEquals('cccccc', $header->getBackgroundColor());
        $this->assertEquals(['font-weight' => 'bold', 'border' => '1px solid black', 'text-align' => 'center'], $header->getStyles());
        
        // Test reordering
        $header->reorderColumns(['email', 'first_name', 'last_name']);
        $expectedOrder = ['email', 'first_name', 'last_name', 'id', 'created_at'];
        $this->assertEquals($expectedOrder, $header->getVarNames());
        
        // Test validation with a valid record
        $record = new ExportRecord();
        $record->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);
        $record->addValue('first_name', 'John', ExportValue::TYPE_STRING);
        $record->addValue('last_name', 'Doe', ExportValue::TYPE_STRING);
        $record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $record->addValue('created_at', '2024-01-15 10:30:00', ExportValue::TYPE_DATETIME);
        
        $this->assertTrue($header->validateRecord($record));
    }
}
