<?php

namespace Tests\Nzoom\Export\Entity;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Entity\ExportRecord;
use Nzoom\Export\Entity\ExportValue;
use Nzoom\Export\Entity\ExportHeader;
use Nzoom\Export\Entity\ExportColumn;

/**
 * Test case for ExportRecord
 */
class ExportRecordTest extends ExportTestCase
{
    private ExportRecord $record;

    protected function setUp(): void
    {
        parent::setUp();
        $this->record = new ExportRecord();
    }

    public function testConstructorWithDefaults(): void
    {
        $record = new ExportRecord();
        
        $this->assertEquals([], $record->getMetadata());
        $this->assertEquals(0, $record->count());
        $this->assertEquals([], $record->getValues());
    }

    public function testConstructorWithMetadata(): void
    {
        $metadata = ['source' => 'test', 'version' => '1.0'];
        $record = new ExportRecord($metadata);
        
        $this->assertEquals($metadata, $record->getMetadata());
    }

    public function testAddValue(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        
        $this->assertEquals(1, $this->record->count());
        $this->assertTrue($this->record->hasValue('id'));
        
        $value = $this->record->getValueByColumnName('id');
        $this->assertInstanceOf(ExportValue::class, $value);
        $this->assertEquals(123, $value->getValue());
        $this->assertEquals(ExportValue::TYPE_INTEGER, $value->getType());
    }

    public function testAddValueWithExportValueObject(): void
    {
        $exportValue = new ExportValue('test string', ExportValue::TYPE_STRING);
        $this->record->addValue('name', $exportValue);
        
        $this->assertEquals(1, $this->record->count());
        $this->assertTrue($this->record->hasValue('name'));
        
        $retrievedValue = $this->record->getValueByColumnName('name');
        $this->assertSame($exportValue, $retrievedValue);
    }

    public function testAddMultipleValues(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $this->record->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);
        
        $this->assertEquals(3, $this->record->count());
        $this->assertTrue($this->record->hasValue('id'));
        $this->assertTrue($this->record->hasValue('name'));
        $this->assertTrue($this->record->hasValue('email'));
    }

    public function testSetValueAt(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        // Update the value at index 1
        $this->record->setValueAt(1, 'Jane Doe', ExportValue::TYPE_STRING);
        
        $value = $this->record->getValueAt(1);
        $this->assertEquals('Jane Doe', $value->getValue());
    }

    public function testSetValueAtWithExportValueObject(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        
        $newValue = new ExportValue('Updated Value', ExportValue::TYPE_STRING);
        $this->record->setValueAt(0, $newValue);
        
        $retrievedValue = $this->record->getValueAt(0);
        $this->assertSame($newValue, $retrievedValue);
    }

    public function testSetValueAtOutOfRange(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        
        $this->expectException(\OutOfRangeException::class);
        $this->expectExceptionMessage('Index 5 is out of range (0-0)');
        
        $this->record->setValueAt(5, 'test');
    }

    public function testSetValueAtNegativeIndex(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        
        $this->expectException(\OutOfRangeException::class);
        $this->expectExceptionMessage('Index -1 is out of range (0-0)');
        
        $this->record->setValueAt(-1, 'test');
    }

    public function testSetValueByColumnName(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        $this->record->setValueByColumnName('name', 'Jane Doe', ExportValue::TYPE_STRING);
        
        $value = $this->record->getValueByColumnName('name');
        $this->assertEquals('Jane Doe', $value->getValue());
    }

    public function testSetValueByColumnNameNonExistent(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Column name "nonexistent" does not exist');
        
        $this->record->setValueByColumnName('nonexistent', 'test');
    }

    public function testGetValues(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        $values = $this->record->getValues();
        $this->assertCount(2, $values);
        $this->assertInstanceOf(ExportValue::class, $values[0]);
        $this->assertInstanceOf(ExportValue::class, $values[1]);
        $this->assertEquals(123, $values[0]->getValue());
        $this->assertEquals('John', $values[1]->getValue());
    }

    public function testGetValueAt(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        $value0 = $this->record->getValueAt(0);
        $value1 = $this->record->getValueAt(1);
        $valueNull = $this->record->getValueAt(5);
        
        $this->assertEquals(123, $value0->getValue());
        $this->assertEquals('John', $value1->getValue());
        $this->assertNull($valueNull);
    }

    public function testGetValueByColumnName(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        $idValue = $this->record->getValueByColumnName('id');
        $nameValue = $this->record->getValueByColumnName('name');
        $nullValue = $this->record->getValueByColumnName('nonexistent');
        
        $this->assertEquals(123, $idValue->getValue());
        $this->assertEquals('John', $nameValue->getValue());
        $this->assertNull($nullValue);
    }

    public function testHasValue(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        $this->assertTrue($this->record->hasValue('id'));
        $this->assertTrue($this->record->hasValue('name'));
        $this->assertFalse($this->record->hasValue('nonexistent'));
    }

    public function testGetRawValues(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $this->record->addValue('active', true, ExportValue::TYPE_BOOLEAN);
        
        $rawValues = $this->record->getRawValues();
        $this->assertEquals([123, 'John Doe', true], $rawValues);
    }

    public function testGetFormattedValues(): void
    {
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('date', '2024-01-15', ExportValue::TYPE_DATE, 'd.m.Y');
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        $formattedValues = $this->record->getFormattedValues();
        $this->assertCount(3, $formattedValues);
        $this->assertEquals(123, $formattedValues[0]);
        $this->assertEquals('15.01.2024', $formattedValues[1]);
        $this->assertEquals('John', $formattedValues[2]);
    }

    public function testMetadataOperations(): void
    {
        $this->record->setMetadataValue('source', 'test');
        $this->record->setMetadataValue('version', '1.0');
        
        $this->assertEquals('test', $this->record->getMetadataValue('source'));
        $this->assertEquals('1.0', $this->record->getMetadataValue('version'));
        $this->assertNull($this->record->getMetadataValue('nonexistent'));
        $this->assertEquals('default', $this->record->getMetadataValue('nonexistent', 'default'));
        
        $expectedMetadata = ['source' => 'test', 'version' => '1.0'];
        $this->assertEquals($expectedMetadata, $this->record->getMetadata());
    }

    public function testSetMetadata(): void
    {
        $metadata = ['key1' => 'value1', 'key2' => 'value2'];
        $this->record->setMetadata($metadata);
        
        $this->assertEquals($metadata, $this->record->getMetadata());
    }

    public function testValidateWithValidRecord(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        
        $this->assertTrue($this->record->validate($header));
    }

    public function testValidateWithInvalidRecord(): void
    {
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        
        // Only add one value when header expects two
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        
        $this->assertFalse($this->record->validate($header));
    }

    public function testCountableImplementation(): void
    {
        $this->assertEquals(0, count($this->record));
        
        $this->record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $this->assertEquals(1, count($this->record));
        
        $this->record->addValue('name', 'John', ExportValue::TYPE_STRING);
        $this->assertEquals(2, count($this->record));
    }

    public function testIteratorImplementation(): void
    {
        $value1 = new ExportValue(123, ExportValue::TYPE_INTEGER);
        $value2 = new ExportValue('John', ExportValue::TYPE_STRING);
        $value3 = new ExportValue('<EMAIL>', ExportValue::TYPE_STRING);
        
        $this->record->addValue('id', $value1);
        $this->record->addValue('name', $value2);
        $this->record->addValue('email', $value3);
        
        $iteratedValues = [];
        $iteratedKeys = [];
        
        foreach ($this->record as $key => $value) {
            $iteratedKeys[] = $key;
            $iteratedValues[] = $value;
        }
        
        $this->assertEquals([0, 1, 2], $iteratedKeys);
        $this->assertSame($value1, $iteratedValues[0]);
        $this->assertSame($value2, $iteratedValues[1]);
        $this->assertSame($value3, $iteratedValues[2]);
    }

    public function testIteratorWithEmptyRecord(): void
    {
        $iteratedValues = [];
        
        foreach ($this->record as $value) {
            $iteratedValues[] = $value;
        }
        
        $this->assertEmpty($iteratedValues);
    }

    public function testComplexScenario(): void
    {
        // Create a complex record with various data types and metadata
        $metadata = ['source' => 'database', 'table' => 'users', 'row_id' => 42];
        $record = new ExportRecord($metadata);
        
        // Add values of different types
        $record->addValue('id', 123, ExportValue::TYPE_INTEGER);
        $record->addValue('name', 'John Doe', ExportValue::TYPE_STRING);
        $record->addValue('email', '<EMAIL>', ExportValue::TYPE_STRING);
        $record->addValue('salary', 75000.50, ExportValue::TYPE_FLOAT, '#,##0.00');
        $record->addValue('active', true, ExportValue::TYPE_BOOLEAN);
        $record->addValue('created_at', '2024-01-15 10:30:00', ExportValue::TYPE_DATETIME, 'Y-m-d H:i:s');
        
        // Test all functionality
        $this->assertEquals(6, $record->count());
        $this->assertEquals($metadata, $record->getMetadata());
        
        // Test value access
        $this->assertEquals(123, $record->getValueByColumnName('id')->getValue());
        $this->assertEquals('John Doe', $record->getValueByColumnName('name')->getValue());
        $this->assertEquals(75000.50, $record->getValueByColumnName('salary')->getValue());
        
        // Test raw values
        $rawValues = $record->getRawValues();
        $this->assertEquals([123, 'John Doe', '<EMAIL>', 75000.50, true, '2024-01-15 10:30:00'], $rawValues);
        
        // Test formatted values
        $formattedValues = $record->getFormattedValues();
        $this->assertEquals('75000.50', $formattedValues[3]); // Float with format
        
        // Test updating values
        $record->setValueByColumnName('name', 'Jane Doe', ExportValue::TYPE_STRING);
        $this->assertEquals('Jane Doe', $record->getValueByColumnName('name')->getValue());
        
        // Test metadata operations
        $record->setMetadataValue('updated_at', '2024-01-15 11:00:00');
        $this->assertEquals('2024-01-15 11:00:00', $record->getMetadataValue('updated_at'));
        
        // Test validation against header
        $header = new ExportHeader();
        $header->addColumn(new ExportColumn('id', 'ID', ExportValue::TYPE_INTEGER));
        $header->addColumn(new ExportColumn('name', 'Name', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('email', 'Email', ExportValue::TYPE_STRING));
        $header->addColumn(new ExportColumn('salary', 'Salary', ExportValue::TYPE_FLOAT));
        $header->addColumn(new ExportColumn('active', 'Active', ExportValue::TYPE_BOOLEAN));
        $header->addColumn(new ExportColumn('created_at', 'Created At', ExportValue::TYPE_DATETIME));
        
        $this->assertTrue($record->validate($header));
    }
}
