<?php

namespace Tests\Nzoom\Export\Entity;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Entity\ExportColumn;
use Nzoom\Export\Entity\ExportValue;

/**
 * Test case for ExportColumn
 */
class ExportColumnTest extends ExportTestCase
{
    public function testConstructorWithMinimalParameters(): void
    {
        $column = new ExportColumn('test_var', 'Test Label');
        
        $this->assertEquals('test_var', $column->getVarName());
        $this->assertEquals('Test Label', $column->getLabel());
        $this->assertEquals(ExportValue::TYPE_STRING, $column->getType());
        $this->assertEquals('', $column->getFormat());
        $this->assertNull($column->getWidth());
        $this->assertEquals([], $column->getStyles());
    }

    public function testConstructorWithAllParameters(): void
    {
        $styles = ['color' => 'red', 'bold' => true];
        $column = new ExportColumn(
            'user_id',
            'User ID',
            ExportValue::TYPE_INTEGER,
            '#,##0',
            150,
            $styles
        );
        
        $this->assertEquals('user_id', $column->getVarName());
        $this->assertEquals('User ID', $column->getLabel());
        $this->assertEquals(ExportValue::TYPE_INTEGER, $column->getType());
        $this->assertEquals('#,##0', $column->getFormat());
        $this->assertEquals(150, $column->getWidth());
        $this->assertEquals($styles, $column->getStyles());
    }

    public function testConstructorWithEmptyVarName(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Variable name cannot be empty');
        
        new ExportColumn('', 'Test Label');
    }

    public function testConstructorWithInvalidType(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid type "invalid_type". Valid types are: string, integer, float, boolean, date, datetime, array');
        
        new ExportColumn('test_var', 'Test Label', 'invalid_type');
    }

    public function testSetVarName(): void
    {
        $column = new ExportColumn('original', 'Test');
        $column->setVarName('new_var');
        
        $this->assertEquals('new_var', $column->getVarName());
    }

    public function testSetVarNameWithEmptyString(): void
    {
        $column = new ExportColumn('original', 'Test');
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Variable name cannot be empty');
        
        $column->setVarName('');
    }

    public function testSetLabel(): void
    {
        $column = new ExportColumn('test', 'Original Label');
        $column->setLabel('New Label');
        
        $this->assertEquals('New Label', $column->getLabel());
    }

    public function testSetLabelWithEmptyString(): void
    {
        $column = new ExportColumn('test', 'Original Label');
        $column->setLabel('');
        
        $this->assertEquals('', $column->getLabel());
    }

    public function testSetType(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_STRING);
        $column->setType(ExportValue::TYPE_INTEGER);
        
        $this->assertEquals(ExportValue::TYPE_INTEGER, $column->getType());
    }

    public function testSetTypeWithInvalidType(): void
    {
        $column = new ExportColumn('test', 'Test');
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid type "invalid". Valid types are: string, integer, float, boolean, date, datetime, array');
        
        $column->setType('invalid');
    }

    public function testSetFormat(): void
    {
        $column = new ExportColumn('test', 'Test');
        $column->setFormat('dd/mm/yyyy');
        
        $this->assertEquals('dd/mm/yyyy', $column->getFormat());
    }

    public function testSetWidth(): void
    {
        $column = new ExportColumn('test', 'Test');
        $column->setWidth(200);
        
        $this->assertEquals(200, $column->getWidth());
    }

    public function testSetWidthWithNull(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_STRING, '', 100);
        $column->setWidth(null);
        
        $this->assertNull($column->getWidth());
    }

    public function testSetStyles(): void
    {
        $column = new ExportColumn('test', 'Test');
        $styles = ['background' => 'blue', 'font-size' => '12px'];
        $column->setStyles($styles);
        
        $this->assertEquals($styles, $column->getStyles());
    }

    public function testAddStyle(): void
    {
        $column = new ExportColumn('test', 'Test');
        $column->addStyle('color', 'green');
        $column->addStyle('bold', true);
        
        $expectedStyles = ['color' => 'green', 'bold' => true];
        $this->assertEquals($expectedStyles, $column->getStyles());
    }

    public function testAddStyleOverwritesExisting(): void
    {
        $column = new ExportColumn('test', 'Test');
        $column->addStyle('color', 'red');
        $column->addStyle('color', 'blue');
        
        $this->assertEquals(['color' => 'blue'], $column->getStyles());
    }

    public function testValidateValueWithValidString(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_STRING);
        
        $this->assertTrue($column->validateValue('test string'));
        $this->assertTrue($column->validateValue(123));
        $this->assertTrue($column->validateValue(null));
    }

    public function testValidateValueWithValidInteger(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_INTEGER);
        
        $this->assertTrue($column->validateValue(123));
        $this->assertTrue($column->validateValue('456'));
        $this->assertTrue($column->validateValue(null));
    }

    public function testValidateValueWithInvalidInteger(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_INTEGER);
        
        $this->assertFalse($column->validateValue('not a number'));
        $this->assertFalse($column->validateValue(12.5));
    }

    public function testValidateValueWithValidFloat(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_FLOAT);
        
        $this->assertTrue($column->validateValue(12.5));
        $this->assertTrue($column->validateValue(123));
        $this->assertTrue($column->validateValue('45.67'));
        $this->assertTrue($column->validateValue(null));
    }

    public function testValidateValueWithValidBoolean(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_BOOLEAN);
        
        $this->assertTrue($column->validateValue(true));
        $this->assertTrue($column->validateValue(false));
        $this->assertTrue($column->validateValue(1));
        $this->assertTrue($column->validateValue(0));
        $this->assertTrue($column->validateValue('true'));
        $this->assertTrue($column->validateValue('false'));
        $this->assertTrue($column->validateValue('1'));
        $this->assertTrue($column->validateValue('0'));
        $this->assertTrue($column->validateValue(null));
    }

    public function testValidateValueWithValidDate(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_DATE);

        $this->assertTrue($column->validateValue(new \DateTime()));
        $this->assertTrue($column->validateValue('2024-01-15'));
        $this->assertTrue($column->validateValue('2024/01/15'));
        $this->assertTrue($column->validateValue('January 15, 2024'));
        $this->assertTrue($column->validateValue(null));
    }

    public function testValidateValueWithInvalidDate(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_DATE);
        
        $this->assertFalse($column->validateValue('not a date'));
        $this->assertFalse($column->validateValue(123));
    }

    public function testValidateValueWithValidArray(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_ARRAY);
        
        $this->assertTrue($column->validateValue([]));
        $this->assertTrue($column->validateValue(['a', 'b', 'c']));
        $this->assertTrue($column->validateValue(['key' => 'value']));
        $this->assertTrue($column->validateValue(null));
    }

    public function testValidateValueWithInvalidArray(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_ARRAY);
        
        $this->assertFalse($column->validateValue('string'));
        $this->assertFalse($column->validateValue(123));
    }

    public function testCreateValue(): void
    {
        $column = new ExportColumn('test', 'Test', ExportValue::TYPE_INTEGER, '#,##0');
        $value = $column->createValue(123);
        
        $this->assertInstanceOf(ExportValue::class, $value);
        $this->assertEquals(123, $value->getValue());
        $this->assertEquals(ExportValue::TYPE_INTEGER, $value->getType());
        $this->assertEquals('#,##0', $value->getFormat());
    }

    public function testCreateValueWithDifferentTypes(): void
    {
        // Test string column
        $stringColumn = new ExportColumn('name', 'Name', ExportValue::TYPE_STRING);
        $stringValue = $stringColumn->createValue('Test Name');
        $this->assertEquals('Test Name', $stringValue->getValue());
        $this->assertEquals(ExportValue::TYPE_STRING, $stringValue->getType());
        
        // Test date column
        $dateColumn = new ExportColumn('date', 'Date', ExportValue::TYPE_DATE, 'Y-m-d');
        $dateValue = $dateColumn->createValue('2024-01-15');
        $this->assertEquals('2024-01-15', $dateValue->getValue());
        $this->assertEquals(ExportValue::TYPE_DATE, $dateValue->getType());
        $this->assertEquals('Y-m-d', $dateValue->getFormat());
    }

    public function testAllValidTypes(): void
    {
        $validTypes = [
            ExportValue::TYPE_STRING,
            ExportValue::TYPE_INTEGER,
            ExportValue::TYPE_FLOAT,
            ExportValue::TYPE_BOOLEAN,
            ExportValue::TYPE_DATE,
            ExportValue::TYPE_DATETIME,
            ExportValue::TYPE_ARRAY
        ];
        
        foreach ($validTypes as $type) {
            $column = new ExportColumn('test', 'Test', $type);
            $this->assertEquals($type, $column->getType());
        }
    }

    public function testComplexStyling(): void
    {
        $column = new ExportColumn('test', 'Test');
        
        // Add multiple styles
        $column->addStyle('font-weight', 'bold');
        $column->addStyle('color', 'red');
        $column->addStyle('background-color', 'yellow');
        $column->addStyle('text-align', 'center');
        
        $expectedStyles = [
            'font-weight' => 'bold',
            'color' => 'red',
            'background-color' => 'yellow',
            'text-align' => 'center'
        ];
        
        $this->assertEquals($expectedStyles, $column->getStyles());
        
        // Replace all styles
        $newStyles = ['border' => '1px solid black'];
        $column->setStyles($newStyles);
        $this->assertEquals($newStyles, $column->getStyles());
    }
}
