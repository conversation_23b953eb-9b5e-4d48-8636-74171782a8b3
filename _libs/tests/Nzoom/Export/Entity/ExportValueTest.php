<?php

namespace Tests\Nzoom\Export\Entity;

use Tests\Nzoom\Export\ExportTestCase;
use Nzoom\Export\Entity\ExportValue;

/**
 * Test case for ExportValue
 */
class ExportValueTest extends ExportTestCase
{
    public function testConstructorWithMinimalParameters(): void
    {
        $value = new ExportValue('test');
        
        $this->assertEquals('test', $value->getValue());
        $this->assertNull($value->getType());
        $this->assertNull($value->getFormat());
    }

    public function testConstructorWithAllParameters(): void
    {
        $value = new ExportValue(123, ExportValue::TYPE_INTEGER, '#,##0');
        
        $this->assertEquals(123, $value->getValue());
        $this->assertEquals(ExportValue::TYPE_INTEGER, $value->getType());
        $this->assertEquals('#,##0', $value->getFormat());
    }

    public function testConstructorWithInvalidType(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid type "invalid_type". Valid types are: string, integer, float, boolean, date, datetime, array');
        
        new ExportValue('test', 'invalid_type');
    }

    public function testGetValidTypes(): void
    {
        $validTypes = ExportValue::getValidTypes();
        
        $expectedTypes = [
            ExportValue::TYPE_STRING,
            ExportValue::TYPE_INTEGER,
            ExportValue::TYPE_FLOAT,
            ExportValue::TYPE_BOOLEAN,
            ExportValue::TYPE_DATE,
            ExportValue::TYPE_DATETIME,
            ExportValue::TYPE_ARRAY
        ];
        
        $this->assertEquals($expectedTypes, $validTypes);
    }

    public function testSetValue(): void
    {
        $value = new ExportValue('original');
        $value->setValue('updated');
        
        $this->assertEquals('updated', $value->getValue());
    }

    public function testSetType(): void
    {
        $value = new ExportValue('test', ExportValue::TYPE_STRING);
        $value->setType(ExportValue::TYPE_INTEGER);
        
        $this->assertEquals(ExportValue::TYPE_INTEGER, $value->getType());
    }

    public function testSetTypeWithNull(): void
    {
        $value = new ExportValue('test', ExportValue::TYPE_STRING);
        $value->setType(null);
        
        $this->assertNull($value->getType());
    }

    public function testSetTypeWithInvalidType(): void
    {
        $value = new ExportValue('test');
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid type "invalid". Valid types are: string, integer, float, boolean, date, datetime, array');
        
        $value->setType('invalid');
    }

    public function testSetFormat(): void
    {
        $value = new ExportValue('test');
        $result = $value->setFormat('Y-m-d');
        
        $this->assertEquals('Y-m-d', $value->getFormat());
        $this->assertSame($value, $result); // Test fluent interface
    }

    public function testSetFormatWithNull(): void
    {
        $value = new ExportValue('test', ExportValue::TYPE_STRING, 'original');
        $value->setFormat(null);
        
        $this->assertNull($value->getFormat());
    }

    public function testIsNull(): void
    {
        $nullValue = new ExportValue(null);
        $nonNullValue = new ExportValue('test');
        
        $this->assertTrue($nullValue->isNull());
        $this->assertFalse($nonNullValue->isNull());
    }

    public function testValidateWithNullValue(): void
    {
        $value = new ExportValue(null, ExportValue::TYPE_STRING);
        $this->assertTrue($value->validate());
    }

    public function testValidateWithNullType(): void
    {
        $value = new ExportValue('test', null);
        $this->assertTrue($value->validate());
    }

    public function testValidateStringType(): void
    {
        $stringValue = new ExportValue('test string', ExportValue::TYPE_STRING);
        $numericValue = new ExportValue(123, ExportValue::TYPE_STRING);
        $nullValue = new ExportValue(null, ExportValue::TYPE_STRING);
        
        $this->assertTrue($stringValue->validate());
        $this->assertTrue($numericValue->validate());
        $this->assertTrue($nullValue->validate());
    }

    public function testValidateIntegerType(): void
    {
        $intValue = new ExportValue(123, ExportValue::TYPE_INTEGER);
        $stringIntValue = new ExportValue('456', ExportValue::TYPE_INTEGER);
        $invalidValue = new ExportValue('not a number', ExportValue::TYPE_INTEGER);
        $floatValue = new ExportValue(12.5, ExportValue::TYPE_INTEGER);
        
        $this->assertTrue($intValue->validate());
        $this->assertTrue($stringIntValue->validate());
        $this->assertFalse($invalidValue->validate());
        $this->assertFalse($floatValue->validate());
    }

    public function testValidateFloatType(): void
    {
        $floatValue = new ExportValue(12.5, ExportValue::TYPE_FLOAT);
        $intValue = new ExportValue(123, ExportValue::TYPE_FLOAT);
        $stringFloatValue = new ExportValue('45.67', ExportValue::TYPE_FLOAT);
        $invalidValue = new ExportValue('not a number', ExportValue::TYPE_FLOAT);
        
        $this->assertTrue($floatValue->validate());
        $this->assertTrue($intValue->validate());
        $this->assertTrue($stringFloatValue->validate());
        $this->assertFalse($invalidValue->validate());
    }

    public function testValidateBooleanType(): void
    {
        $boolValue = new ExportValue(true, ExportValue::TYPE_BOOLEAN);
        $intBoolValue = new ExportValue(1, ExportValue::TYPE_BOOLEAN);
        $stringBoolValue = new ExportValue('true', ExportValue::TYPE_BOOLEAN);
        $stringFalseValue = new ExportValue('FALSE', ExportValue::TYPE_BOOLEAN);
        $zeroValue = new ExportValue(0, ExportValue::TYPE_BOOLEAN);
        $stringZeroValue = new ExportValue('0', ExportValue::TYPE_BOOLEAN);
        $invalidValue = new ExportValue('maybe', ExportValue::TYPE_BOOLEAN);
        
        $this->assertTrue($boolValue->validate());
        $this->assertTrue($intBoolValue->validate());
        $this->assertTrue($stringBoolValue->validate());
        $this->assertTrue($stringFalseValue->validate());
        $this->assertTrue($zeroValue->validate());
        $this->assertTrue($stringZeroValue->validate());
        $this->assertFalse($invalidValue->validate());
    }

    public function testValidateDateType(): void
    {
        $dateTimeValue = new ExportValue(new \DateTime(), ExportValue::TYPE_DATE);
        $stringDateValue = new ExportValue('2024-01-15', ExportValue::TYPE_DATE);
        $stringDateValue2 = new ExportValue('January 15, 2024', ExportValue::TYPE_DATE);
        $invalidValue = new ExportValue('not a date', ExportValue::TYPE_DATE);
        $intValue = new ExportValue(123, ExportValue::TYPE_DATE);
        
        $this->assertTrue($dateTimeValue->validate());
        $this->assertTrue($stringDateValue->validate());
        $this->assertTrue($stringDateValue2->validate());
        $this->assertFalse($invalidValue->validate());
        $this->assertFalse($intValue->validate());
    }

    public function testValidateDateTimeType(): void
    {
        $dateTimeValue = new ExportValue(new \DateTime(), ExportValue::TYPE_DATETIME);
        $stringDateTimeValue = new ExportValue('2024-01-15 10:30:00', ExportValue::TYPE_DATETIME);
        $invalidValue = new ExportValue('not a datetime', ExportValue::TYPE_DATETIME);
        
        $this->assertTrue($dateTimeValue->validate());
        $this->assertTrue($stringDateTimeValue->validate());
        $this->assertFalse($invalidValue->validate());
    }

    public function testValidateArrayType(): void
    {
        $arrayValue = new ExportValue([], ExportValue::TYPE_ARRAY);
        $arrayValue2 = new ExportValue(['a', 'b', 'c'], ExportValue::TYPE_ARRAY);
        $assocArrayValue = new ExportValue(['key' => 'value'], ExportValue::TYPE_ARRAY);
        $stringValue = new ExportValue('string', ExportValue::TYPE_ARRAY);
        $intValue = new ExportValue(123, ExportValue::TYPE_ARRAY);
        
        $this->assertTrue($arrayValue->validate());
        $this->assertTrue($arrayValue2->validate());
        $this->assertTrue($assocArrayValue->validate());
        $this->assertFalse($stringValue->validate());
        $this->assertFalse($intValue->validate());
    }

    public function testGetFormattedValueWithNull(): void
    {
        $value = new ExportValue(null);
        $this->assertEquals('', $value->getFormattedValue());
    }

    public function testGetFormattedValueWithNullType(): void
    {
        $value = new ExportValue('test', null);
        $this->assertEquals('test', $value->getFormattedValue());
    }

    public function testGetFormattedValueDateWithDateTime(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, ExportValue::TYPE_DATE);
        
        $this->assertEquals('15.01.2024', $value->getFormattedValue());
    }

    public function testGetFormattedValueDateWithCustomFormat(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, ExportValue::TYPE_DATE, 'Y-m-d');
        
        $this->assertEquals('2024-01-15', $value->getFormattedValue());
    }

    public function testGetFormattedValueDateWithString(): void
    {
        $value = new ExportValue('2024-01-15', ExportValue::TYPE_DATE);
        $this->assertEquals('15.01.2024', $value->getFormattedValue());
    }

    public function testGetFormattedValueDateTimeWithDateTime(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, ExportValue::TYPE_DATETIME);
        
        $this->assertEquals('15.01.2024, 10:30', $value->getFormattedValue());
    }

    public function testGetFormattedValueDateTimeWithCustomFormat(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, ExportValue::TYPE_DATETIME, 'Y-m-d H:i:s');
        
        $this->assertEquals('2024-01-15 10:30:00', $value->getFormattedValue());
    }

    public function testGetFormattedValueDateTimeWithString(): void
    {
        $value = new ExportValue('2024-01-15 10:30:00', ExportValue::TYPE_DATETIME);
        $this->assertEquals('15.01.2024, 10:30', $value->getFormattedValue());
    }

    public function testGetFormattedValueOtherTypes(): void
    {
        $stringValue = new ExportValue('test', ExportValue::TYPE_STRING);
        $intValue = new ExportValue(123, ExportValue::TYPE_INTEGER);
        $floatValue = new ExportValue(12.5, ExportValue::TYPE_FLOAT);
        $boolValue = new ExportValue(true, ExportValue::TYPE_BOOLEAN);
        $arrayValue = new ExportValue(['a', 'b'], ExportValue::TYPE_ARRAY);
        
        $this->assertEquals('test', $stringValue->getFormattedValue());
        $this->assertEquals(123, $intValue->getFormattedValue());
        $this->assertEquals(12.5, $floatValue->getFormattedValue());
        $this->assertEquals(true, $boolValue->getFormattedValue());
        $this->assertEquals(['a', 'b'], $arrayValue->getFormattedValue());
    }

    public function testToStringWithNull(): void
    {
        $value = new ExportValue(null);
        $this->assertEquals('', (string) $value);
    }

    public function testToStringWithArray(): void
    {
        $value = new ExportValue(['key' => 'value', 'number' => 123]);
        $this->assertEquals('{"key":"value","number":123}', (string) $value);
    }

    public function testToStringWithDateTimeAndDateType(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, ExportValue::TYPE_DATE);
        
        $this->assertEquals('2024-01-15', (string) $value);
    }

    public function testToStringWithDateTimeAndDateTimeType(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, ExportValue::TYPE_DATETIME);
        
        $this->assertEquals('2024-01-15 10:30:00', (string) $value);
    }

    public function testToStringWithDateTimeAndCustomFormat(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, ExportValue::TYPE_DATE, 'd/m/Y');
        
        $this->assertEquals('15/01/2024', (string) $value);
    }

    public function testToStringWithDateTimeNoType(): void
    {
        $date = new \DateTime('2024-01-15 10:30:00');
        $value = new ExportValue($date, null, 'Y-m-d H:i');
        
        $this->assertEquals('2024-01-15 10:30', (string) $value);
    }

    public function testToStringWithPrimitiveTypes(): void
    {
        $stringValue = new ExportValue('test string');
        $intValue = new ExportValue(123);
        $floatValue = new ExportValue(12.5);
        $boolValue = new ExportValue(true);
        
        $this->assertEquals('test string', (string) $stringValue);
        $this->assertEquals('123', (string) $intValue);
        $this->assertEquals('12.5', (string) $floatValue);
        $this->assertEquals('1', (string) $boolValue);
    }

    public function testComplexScenario(): void
    {
        // Test a complex scenario with different value types and operations
        
        // String value
        $stringValue = new ExportValue('John Doe', ExportValue::TYPE_STRING);
        $this->assertTrue($stringValue->validate());
        $this->assertEquals('John Doe', $stringValue->getFormattedValue());
        $this->assertEquals('John Doe', (string) $stringValue);
        
        // Integer value with format
        $intValue = new ExportValue(1234567, ExportValue::TYPE_INTEGER, '#,##0');
        $this->assertTrue($intValue->validate());
        $this->assertEquals(1234567, $intValue->getFormattedValue()); // Format not applied in getFormattedValue for integers
        
        // Date value with custom format
        $dateValue = new ExportValue('2024-01-15', ExportValue::TYPE_DATE, 'd/m/Y');
        $this->assertTrue($dateValue->validate());
        $this->assertEquals('15/01/2024', $dateValue->getFormattedValue());
        
        // DateTime value
        $dateTime = new \DateTime('2024-01-15 14:30:00');
        $dateTimeValue = new ExportValue($dateTime, ExportValue::TYPE_DATETIME, 'Y-m-d H:i:s');
        $this->assertTrue($dateTimeValue->validate());
        $this->assertEquals('2024-01-15 14:30:00', $dateTimeValue->getFormattedValue());
        $this->assertEquals('2024-01-15 14:30:00', (string) $dateTimeValue);
        
        // Array value
        $arrayValue = new ExportValue(['name' => 'John', 'age' => 30], ExportValue::TYPE_ARRAY);
        $this->assertTrue($arrayValue->validate());
        $this->assertEquals(['name' => 'John', 'age' => 30], $arrayValue->getFormattedValue());
        $this->assertEquals('{"name":"John","age":30}', (string) $arrayValue);
        
        // Boolean value
        $boolValue = new ExportValue('true', ExportValue::TYPE_BOOLEAN);
        $this->assertTrue($boolValue->validate());
        $this->assertEquals('true', $boolValue->getFormattedValue());
        
        // Test value updates
        $stringValue->setValue('Jane Doe');
        $stringValue->setType(ExportValue::TYPE_STRING);
        $stringValue->setFormat('');
        
        $this->assertEquals('Jane Doe', $stringValue->getValue());
        $this->assertEquals(ExportValue::TYPE_STRING, $stringValue->getType());
        $this->assertEquals('', $stringValue->getFormat());
        
        // Test null handling
        $nullValue = new ExportValue(null, ExportValue::TYPE_STRING);
        $this->assertTrue($nullValue->isNull());
        $this->assertTrue($nullValue->validate());
        $this->assertEquals('', $nullValue->getFormattedValue());
        $this->assertEquals('', (string) $nullValue);
    }
}
