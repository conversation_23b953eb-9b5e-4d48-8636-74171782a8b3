<?php

namespace Tests\Nzoom\Export\Factory;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Factory\ExportFormatFactory;
use Nzoom\Export\Adapter\ExportFormatAdapterInterface;
use Nzoom\Export\Adapter\CsvExportFormatAdapter;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;
use Nzoom\Export\Adapter\JsonExportFormatAdapter;

/**
 * Test case for ExportFormatFactory
 */
class ExportFormatFactoryTest extends ExportTestCase
{
    private ExportFormatFactory $factory;
    private RegistryMock $registry;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create mock registry
        $this->registry = new RegistryMock();
        
        // Create factory instance
        $this->factory = new ExportFormatFactory($this->registry, 'test_module', 'test_controller');
    }

    public function testCreateCsvAdapter(): void
    {
        $adapter = $this->factory->createAdapter('csv');
        
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
        $this->assertEquals('csv', $adapter->getFormatName());
    }

    public function testCreateExcelAdapter(): void
    {
        $adapter = $this->factory->createAdapter('xlsx');
        
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $adapter);
        $this->assertEquals('excel', $adapter->getFormatName());
    }

    public function testCreateJsonAdapter(): void
    {
        $adapter = $this->factory->createAdapter('json');
        
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
        $this->assertInstanceOf(JsonExportFormatAdapter::class, $adapter);
        $this->assertEquals('json', $adapter->getFormatName());
    }

    public function testCreateAdapterWithOptions(): void
    {
        $options = ['delimiter' => 'semicolon'];
        $adapter = $this->factory->createAdapter('csv', $options);
        
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
        
        // Test that options are applied by checking format options
        $formatOptions = $adapter->getFormatOptions();
        $this->assertArrayHasKey('delimiter', $formatOptions);
    }

    public function testCreateAdapterCaching(): void
    {
        $adapter1 = $this->factory->createAdapter('csv');
        $adapter2 = $this->factory->createAdapter('csv');
        
        // Should return the same cached instance
        $this->assertSame($adapter1, $adapter2);
    }

    public function testCreateAdapterCachingWithDifferentOptions(): void
    {
        $adapter1 = $this->factory->createAdapter('csv', ['delimiter' => 'comma']);
        $adapter2 = $this->factory->createAdapter('csv', ['delimiter' => 'semicolon']);
        
        // Should return different instances due to different options
        $this->assertNotSame($adapter1, $adapter2);
    }

    public function testCreateAdapterWithUnsupportedFormat(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No adapter found for format: unsupported');
        
        $this->factory->createAdapter('unsupported');
    }

    public function testGetSupportedFormats(): void
    {
        $formats = $this->factory->getSupportedFormats();
        
        $this->assertIsArray($formats);
        $this->assertContains('csv', $formats);
        $this->assertContains('xlsx', $formats);
        $this->assertContains('xls', $formats);
        $this->assertContains('json', $formats);
    }

    public function testGetSupportedFormatsCaching(): void
    {
        $formats1 = $this->factory->getSupportedFormats();
        $formats2 = $this->factory->getSupportedFormats();
        
        // Should return the same cached result
        $this->assertSame($formats1, $formats2);
    }

    public function testIsFormatSupported(): void
    {
        $this->assertTrue($this->factory->isFormatSupported('csv'));
        $this->assertTrue($this->factory->isFormatSupported('CSV'));
        $this->assertTrue($this->factory->isFormatSupported('xlsx'));
        $this->assertTrue($this->factory->isFormatSupported('json'));
        $this->assertFalse($this->factory->isFormatSupported('unsupported'));
        $this->assertFalse($this->factory->isFormatSupported(''));
    }

    public function testCreateAdapterFromFilename(): void
    {
        $csvAdapter = $this->factory->createAdapterFromFilename('export.csv');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $csvAdapter);
        
        $excelAdapter = $this->factory->createAdapterFromFilename('export.xlsx');
        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $excelAdapter);
        
        $jsonAdapter = $this->factory->createAdapterFromFilename('export.json');
        $this->assertInstanceOf(JsonExportFormatAdapter::class, $jsonAdapter);
    }

    public function testCreateAdapterFromFilenameWithOptions(): void
    {
        $options = ['delimiter' => 'tab'];
        $adapter = $this->factory->createAdapterFromFilename('export.csv', $options);
        
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
    }

    public function testCreateAdapterFromFilenameWithoutExtension(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot determine format from filename: export');
        
        $this->factory->createAdapterFromFilename('export');
    }

    public function testCreateAdapterFromFilenameWithUnsupportedExtension(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No adapter found for format: txt');
        
        $this->factory->createAdapterFromFilename('export.txt');
    }

    public function testCaseInsensitiveFormatHandling(): void
    {
        $adapter1 = $this->factory->createAdapter('CSV');
        $adapter2 = $this->factory->createAdapter('csv');
        $adapter3 = $this->factory->createAdapter('Csv');
        
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter1);
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter2);
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter3);
    }

    public function testFormatTrimming(): void
    {
        $adapter = $this->factory->createAdapter('  csv  ');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
    }

    public function testAdapterDiscovery(): void
    {
        // Test that the factory discovers all available adapters
        $reflection = new \ReflectionClass($this->factory);
        $method = $reflection->getMethod('discoverAdapters');
        $method->setAccessible(true);

        $adapters = $method->invoke($this->factory);

        $this->assertIsArray($adapters);
        $this->assertNotEmpty($adapters);

        // Should contain the known adapter classes
        $this->assertContains('Nzoom\\Export\\Adapter\\CsvExportFormatAdapter', $adapters);
        $this->assertContains('Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter', $adapters);
        $this->assertContains('Nzoom\\Export\\Adapter\\JsonExportFormatAdapter', $adapters);
    }

    public function testAdapterDiscoveryCaching(): void
    {
        // Test that adapter discovery results are cached
        $reflection = new \ReflectionClass($this->factory);
        $method = $reflection->getMethod('discoverAdapters');
        $method->setAccessible(true);

        $adapters1 = $method->invoke($this->factory);
        $adapters2 = $method->invoke($this->factory);

        // Should return the same cached result
        $this->assertSame($adapters1, $adapters2);
    }

    public function testGetClassNameFromFile(): void
    {
        $reflection = new \ReflectionClass($this->factory);
        $method = $reflection->getMethod('getClassNameFromFile');
        $method->setAccessible(true);

        // Test with valid adapter file
        $validFile = __DIR__ . '/../../../../Nzoom/Export/Adapter/CsvExportFormatAdapter.php';
        $className = $method->invoke($this->factory, $validFile);
        $this->assertEquals('Nzoom\\Export\\Adapter\\CsvExportFormatAdapter', $className);

        // Test with abstract class (should return null)
        $abstractFile = __DIR__ . '/../../../../Nzoom/Export/Adapter/AbstractExportFormatAdapter.php';
        $className = $method->invoke($this->factory, $abstractFile);
        $this->assertNull($className);

        // Test with interface file (should return null)
        $interfaceFile = __DIR__ . '/../../../../Nzoom/Export/Adapter/ExportFormatAdapterInterface.php';
        $className = $method->invoke($this->factory, $interfaceFile);
        $this->assertNull($className);

        // Test with non-existent class
        $nonExistentFile = '/path/to/NonExistentAdapter.php';
        $className = $method->invoke($this->factory, $nonExistentFile);
        $this->assertNull($className);
    }

    public function testIsValidAdapter(): void
    {
        $reflection = new \ReflectionClass($this->factory);
        $method = $reflection->getMethod('isValidAdapter');
        $method->setAccessible(true);

        // Test with valid adapter class
        $this->assertTrue($method->invoke($this->factory, 'Nzoom\\Export\\Adapter\\CsvExportFormatAdapter'));
        $this->assertTrue($method->invoke($this->factory, 'Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter'));
        $this->assertTrue($method->invoke($this->factory, 'Nzoom\\Export\\Adapter\\JsonExportFormatAdapter'));

        // Test with non-existent class
        $this->assertFalse($method->invoke($this->factory, 'NonExistentClass'));

        // Test with class that doesn't implement interface
        $this->assertFalse($method->invoke($this->factory, 'stdClass'));
    }

    public function testGetAdapterClass(): void
    {
        $reflection = new \ReflectionClass($this->factory);
        $method = $reflection->getMethod('getAdapterClass');
        $method->setAccessible(true);

        // Test with supported formats
        $csvClass = $method->invoke($this->factory, 'csv');
        $this->assertEquals('Nzoom\\Export\\Adapter\\CsvExportFormatAdapter', $csvClass);

        $xlsxClass = $method->invoke($this->factory, 'xlsx');
        $this->assertEquals('Nzoom\\Export\\Adapter\\ExcelExportFormatAdapter', $xlsxClass);

        $jsonClass = $method->invoke($this->factory, 'json');
        $this->assertEquals('Nzoom\\Export\\Adapter\\JsonExportFormatAdapter', $jsonClass);

        // Test with unsupported format
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No adapter found for format: unsupported');
        $method->invoke($this->factory, 'unsupported');
    }

    public function testCacheKeyGeneration(): void
    {
        // Test that different options generate different cache keys
        $adapter1 = $this->factory->createAdapter('csv', ['delimiter' => 'comma']);
        $adapter2 = $this->factory->createAdapter('csv', ['delimiter' => 'semicolon']);
        $adapter3 = $this->factory->createAdapter('csv', ['delimiter' => 'comma']); // Same as adapter1

        // adapter1 and adapter2 should be different instances
        $this->assertNotSame($adapter1, $adapter2);

        // adapter1 and adapter3 should be the same cached instance
        $this->assertSame($adapter1, $adapter3);
    }

    public function testAdapterConfiguration(): void
    {
        $options = [
            'delimiter' => 'semicolon',
            'quote_char' => '"',
            'escape_char' => '\\'
        ];

        $adapter = $this->factory->createAdapter('csv', $options);

        // Verify that configuration was applied
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);

        // Get the configuration back from the adapter
        $reflection = new \ReflectionClass($adapter);
        if ($reflection->hasMethod('getConfiguration')) {
            $method = $reflection->getMethod('getConfiguration');
            $method->setAccessible(true);
            $config = $method->invoke($adapter);

            $this->assertArrayHasKey('delimiter', $config);
            $this->assertEquals('semicolon', $config['delimiter']);
        }
    }

    public function testEmptyOptionsHandling(): void
    {
        $adapter1 = $this->factory->createAdapter('csv', []);
        $adapter2 = $this->factory->createAdapter('csv');

        // Both should be the same cached instance since empty array and no options are equivalent
        $this->assertSame($adapter1, $adapter2);
    }

    public function testMultipleFormatSupport(): void
    {
        // Test that Excel adapter supports multiple extensions
        $xlsAdapter = $this->factory->createAdapter('xls');
        $xlsxAdapter = $this->factory->createAdapter('xlsx');

        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $xlsAdapter);
        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $xlsxAdapter);

        // They should be different instances due to different formats
        $this->assertNotSame($xlsAdapter, $xlsxAdapter);
    }

    public function testCreateAdapterFromFilenameWithComplexPath(): void
    {
        // Test with complex file paths
        $adapter1 = $this->factory->createAdapterFromFilename('/path/to/export.csv');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter1);

        $adapter2 = $this->factory->createAdapterFromFilename('C:\\Windows\\export.xlsx');
        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $adapter2);

        $adapter3 = $this->factory->createAdapterFromFilename('export.file.with.dots.json');
        $this->assertInstanceOf(JsonExportFormatAdapter::class, $adapter3);
    }

    public function testCreateAdapterFromFilenameWithUppercaseExtension(): void
    {
        $adapter1 = $this->factory->createAdapterFromFilename('export.CSV');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter1);

        $adapter2 = $this->factory->createAdapterFromFilename('export.XLSX');
        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $adapter2);

        $adapter3 = $this->factory->createAdapterFromFilename('export.JSON');
        $this->assertInstanceOf(JsonExportFormatAdapter::class, $adapter3);
    }

    public function testCreateAdapterFromFilenameWithEmptyFilename(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot determine format from filename: ');

        $this->factory->createAdapterFromFilename('');
    }

    public function testCreateAdapterFromFilenameWithOnlyExtension(): void
    {
        $adapter = $this->factory->createAdapterFromFilename('.csv');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
    }

    public function testSupportedFormatsContainsAllExpectedFormats(): void
    {
        $formats = $this->factory->getSupportedFormats();

        // Should contain all formats from all adapters
        $expectedFormats = ['csv', 'xlsx', 'xls', 'json'];

        foreach ($expectedFormats as $format) {
            $this->assertContains($format, $formats, "Format '{$format}' should be supported");
        }
    }

    public function testSupportedFormatsAreUnique(): void
    {
        $formats = $this->factory->getSupportedFormats();

        // Should not contain duplicates
        $uniqueFormats = array_unique($formats);
        $this->assertEquals(count($formats), count($uniqueFormats), 'Supported formats should not contain duplicates');
    }

    public function testIsFormatSupportedWithEmptyString(): void
    {
        $this->assertFalse($this->factory->isFormatSupported(''));
    }

    public function testIsFormatSupportedWithWhitespace(): void
    {
        $this->assertTrue($this->factory->isFormatSupported('  csv  '));
        $this->assertFalse($this->factory->isFormatSupported('  unsupported  '));
    }

    public function testFactoryWithDifferentModuleAndController(): void
    {
        $factory = new ExportFormatFactory($this->registry, 'users', 'profile');

        $adapter = $factory->createAdapter('csv');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);

        // Verify that the adapter was created with the correct module/controller
        $reflection = new \ReflectionClass($adapter);
        $moduleProperty = $reflection->getProperty('module');
        $moduleProperty->setAccessible(true);
        $controllerProperty = $reflection->getProperty('controller');
        $controllerProperty->setAccessible(true);

        $this->assertEquals('users', $moduleProperty->getValue($adapter));
        $this->assertEquals('profile', $controllerProperty->getValue($adapter));
    }

    public function testAdapterInstancesAreProperlyConfigured(): void
    {
        $adapter = $this->factory->createAdapter('csv');

        // Verify adapter has access to registry, module, and controller
        $reflection = new \ReflectionClass($adapter);

        $registryProperty = $reflection->getProperty('registry');
        $registryProperty->setAccessible(true);
        $this->assertSame($this->registry, $registryProperty->getValue($adapter));

        $moduleProperty = $reflection->getProperty('module');
        $moduleProperty->setAccessible(true);
        $this->assertEquals('test_module', $moduleProperty->getValue($adapter));

        $controllerProperty = $reflection->getProperty('controller');
        $controllerProperty->setAccessible(true);
        $this->assertEquals('test_controller', $controllerProperty->getValue($adapter));
    }

    public function testComplexCachingScenario(): void
    {
        // Create multiple adapters with different combinations
        $csv1 = $this->factory->createAdapter('csv');
        $csv2 = $this->factory->createAdapter('CSV'); // Different case, should be same
        $csv3 = $this->factory->createAdapter('csv', []); // Empty options, should be same
        $csv4 = $this->factory->createAdapter('csv', ['delimiter' => 'comma']); // Different options

        // csv1, csv2, csv3 should be the same cached instance
        $this->assertSame($csv1, $csv2);
        $this->assertSame($csv1, $csv3);

        // csv4 should be different due to options
        $this->assertNotSame($csv1, $csv4);

        // Create another with same options as csv4
        $csv5 = $this->factory->createAdapter('csv', ['delimiter' => 'comma']);
        $this->assertSame($csv4, $csv5);
    }
}
