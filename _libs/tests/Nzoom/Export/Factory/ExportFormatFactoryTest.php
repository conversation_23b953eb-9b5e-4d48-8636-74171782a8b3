<?php

namespace Tests\Nzoom\Export\Factory;

use Tests\Nzoom\Export\ExportTestCase;
use Tests\Nzoom\TestHelpers\RegistryMock;
use Nzoom\Export\Factory\ExportFormatFactory;
use Nzoom\Export\Adapter\ExportFormatAdapterInterface;
use Nzoom\Export\Adapter\CsvExportFormatAdapter;
use Nzoom\Export\Adapter\ExcelExportFormatAdapter;
use Nzoom\Export\Adapter\JsonExportFormatAdapter;

/**
 * Test case for ExportFormatFactory
 */
class ExportFormatFactoryTest extends ExportTestCase
{
    private ExportFormatFactory $factory;
    private RegistryMock $registry;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create mock registry
        $this->registry = new RegistryMock();
        
        // Create factory instance
        $this->factory = new ExportFormatFactory($this->registry, 'test_module', 'test_controller');
    }

    public function testCreateCsvAdapter(): void
    {
        $adapter = $this->factory->createAdapter('csv');
        
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
        $this->assertEquals('csv', $adapter->getFormatName());
    }

    public function testCreateExcelAdapter(): void
    {
        $adapter = $this->factory->createAdapter('xlsx');
        
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $adapter);
        $this->assertEquals('excel', $adapter->getFormatName());
    }

    public function testCreateJsonAdapter(): void
    {
        $adapter = $this->factory->createAdapter('json');
        
        $this->assertInstanceOf(ExportFormatAdapterInterface::class, $adapter);
        $this->assertInstanceOf(JsonExportFormatAdapter::class, $adapter);
        $this->assertEquals('json', $adapter->getFormatName());
    }

    public function testCreateAdapterWithOptions(): void
    {
        $options = ['delimiter' => 'semicolon'];
        $adapter = $this->factory->createAdapter('csv', $options);
        
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
        
        // Test that options are applied by checking format options
        $formatOptions = $adapter->getFormatOptions();
        $this->assertArrayHasKey('delimiter', $formatOptions);
    }

    public function testCreateAdapterCaching(): void
    {
        $adapter1 = $this->factory->createAdapter('csv');
        $adapter2 = $this->factory->createAdapter('csv');
        
        // Should return the same cached instance
        $this->assertSame($adapter1, $adapter2);
    }

    public function testCreateAdapterCachingWithDifferentOptions(): void
    {
        $adapter1 = $this->factory->createAdapter('csv', ['delimiter' => 'comma']);
        $adapter2 = $this->factory->createAdapter('csv', ['delimiter' => 'semicolon']);
        
        // Should return different instances due to different options
        $this->assertNotSame($adapter1, $adapter2);
    }

    public function testCreateAdapterWithUnsupportedFormat(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No adapter found for format: unsupported');
        
        $this->factory->createAdapter('unsupported');
    }

    public function testGetSupportedFormats(): void
    {
        $formats = $this->factory->getSupportedFormats();
        
        $this->assertIsArray($formats);
        $this->assertContains('csv', $formats);
        $this->assertContains('xlsx', $formats);
        $this->assertContains('xls', $formats);
        $this->assertContains('json', $formats);
    }

    public function testGetSupportedFormatsCaching(): void
    {
        $formats1 = $this->factory->getSupportedFormats();
        $formats2 = $this->factory->getSupportedFormats();
        
        // Should return the same cached result
        $this->assertSame($formats1, $formats2);
    }

    public function testIsFormatSupported(): void
    {
        $this->assertTrue($this->factory->isFormatSupported('csv'));
        $this->assertTrue($this->factory->isFormatSupported('CSV'));
        $this->assertTrue($this->factory->isFormatSupported('xlsx'));
        $this->assertTrue($this->factory->isFormatSupported('json'));
        $this->assertFalse($this->factory->isFormatSupported('unsupported'));
        $this->assertFalse($this->factory->isFormatSupported(''));
    }

    public function testCreateAdapterFromFilename(): void
    {
        $csvAdapter = $this->factory->createAdapterFromFilename('export.csv');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $csvAdapter);
        
        $excelAdapter = $this->factory->createAdapterFromFilename('export.xlsx');
        $this->assertInstanceOf(ExcelExportFormatAdapter::class, $excelAdapter);
        
        $jsonAdapter = $this->factory->createAdapterFromFilename('export.json');
        $this->assertInstanceOf(JsonExportFormatAdapter::class, $jsonAdapter);
    }

    public function testCreateAdapterFromFilenameWithOptions(): void
    {
        $options = ['delimiter' => 'tab'];
        $adapter = $this->factory->createAdapterFromFilename('export.csv', $options);
        
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
    }

    public function testCreateAdapterFromFilenameWithoutExtension(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Cannot determine format from filename: export');
        
        $this->factory->createAdapterFromFilename('export');
    }

    public function testCreateAdapterFromFilenameWithUnsupportedExtension(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('No adapter found for format: txt');
        
        $this->factory->createAdapterFromFilename('export.txt');
    }

    public function testCaseInsensitiveFormatHandling(): void
    {
        $adapter1 = $this->factory->createAdapter('CSV');
        $adapter2 = $this->factory->createAdapter('csv');
        $adapter3 = $this->factory->createAdapter('Csv');
        
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter1);
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter2);
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter3);
    }

    public function testFormatTrimming(): void
    {
        $adapter = $this->factory->createAdapter('  csv  ');
        $this->assertInstanceOf(CsvExportFormatAdapter::class, $adapter);
    }
}
