<?php

/**
 * Global Mock Outlook class for testing (in global namespace)
 */
class Outlook
{
    private $fields;
    
    public function __construct($fields = [])
    {
        $this->fields = $fields;
    }
    
    public function get($key)
    {
        if ($key === 'current_custom_fields') {
            return $this->fields;
        }
        return null;
    }
    
    public function setFields($fields)
    {
        $this->fields = $fields;
    }
}

/**
 * Global Mock Model class for testing (in global namespace)
 */
class Model
{
    private $data;
    private $exportValues;
    private $exportTypes;
    
    public function __construct($data = [], $exportValues = [], $exportTypes = [])
    {
        $this->data = $data;
        $this->exportValues = $exportValues;
        $this->exportTypes = $exportTypes;
    }
    
    public function get($key)
    {
        return $this->data[$key] ?? null;
    }
    
    public function getExportVarValueWithArgs($varName)
    {
        return $this->exportValues[$varName] ?? null;
    }
    
    public function getExportVarType($varName)
    {
        return $this->exportTypes[$varName] ?? null;
    }
}
