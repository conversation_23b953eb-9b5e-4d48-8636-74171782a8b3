<?php

namespace Nzoom\Renderer;

require_once PH_SMARTY_DIR . 'Smarty.class.php';

/**
 * @method fetch($resource_name, $cache_id = null, $compile_id = null, $display = false)
 * @method assign($tpl_var, $value = null)
 */
class SmartyRenderer extends \Smarty
{
    private static $defaultParams = [
        'caching' => false,
        'compile_check' => true,
        'debugging' => false,
        'force_compile' => false,
        'config_booleanize' => false,
    ];

    private static $dirsList = [
        'template_dir',
        'compile_dir',
        'config_dir',
        'cache_dir',
    ];

    public function __construct($params = array())
    {
        parent::__construct();
        $this->_init($params);
    }

    private function _init($params = array()): void
    {
        // Set params from the list
        foreach (self::$defaultParams as $k=>$v) {
            $this->$k = $params[$k]??$v;
        }

        //set directories
        foreach (self::$dirsList as $dirType) {
            if (array_key_exists($dirType, $params)) {
                $this->$dirType = $params[$dirType];
            }
        }

        self::createDirectory($this->compile_dir);
        if ($this->caching) {
            self::createDirectory($this->cache_dir);
        }

        $this->addPluginsDir(SMARTY_DIR . $this->plugins_dir[0] . '/custom');
        $this->addPluginsDir( __DIR__.'/plugins');
    }

    /**
     * @param $dirPath
     * @return void
     */
    public function addPluginsDir($dirPath)
    {
        //we add the folder with custom made plugins first
        //to be able to override smarty plugins
        //for more info see here: http://www.smarty.net/docsv2/en/variable.plugins.dir.tpl
        array_unshift($this->plugins_dir, $dirPath);
    }

    /**
     * @param $dirPath
     * @return void
     */
    private static function createDirectory($dirPath): void
    {
        if (!file_exists($dirPath)) {
            @mkdir($dirPath, 0777, true);
        }
    }

    public function clearAllCache($expireTime=null)
    {
        $this->clear_all_cache($expireTime);
    }

    public function fetchString(string $string)
    {
        require_once($this->_get_plugin_filepath( 'function', 'eval' ));
        return smarty_function_eval(['var'=>$string], $this);
    }

    public function loadConfigArray(array $data): void
    {
        $this->_config[0]['vars'] = array_merge(
            $this->_config[0]['vars'],
            $data
        );
    }

    public function getTemplateVars(): array
    {
        return array_slice($this->get_template_vars(), 0);
    }

    public function getConfigVars(): array
    {
        return array_slice($this->get_config_vars(), 0);
    }
}
