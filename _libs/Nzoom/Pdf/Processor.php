<?php

namespace Nzoom\Pdf;

use Exception;
use InvalidArgumentException;
use RuntimeException;
use Nzoom\Ghostscript\Processor as GhostscriptProcessor;

/**
 * Enterprise PDF Processing Engine with Advanced Ghostscript Integration
 *
 * This class serves as a high-level, user-friendly interface for sophisticated PDF
 * processing operations, abstracting the complexity of Ghostscript while providing
 * enterprise-grade functionality for document management workflows. It combines
 * the power of Ghostscript's PDF engine with modern PHP practices and comprehensive
 * error handling.
 *
 * Key Capabilities:
 * ================
 *
 * 📄 **Document Operations**:
 * - PDF merging and concatenation with intelligent ordering
 * - Page extraction and manipulation with range support
 * - Document splitting and reorganization
 * - Batch processing with progress tracking
 *
 * 🔄 **Format Conversion**:
 * - Image to PDF conversion with quality control
 * - PDF to image conversion (PNG, JPEG, TIFF)
 * - Multi-format support with automatic detection
 * - Resolution and quality optimization
 *
 * ⚡ **Optimization Engine**:
 * - Web delivery optimization (fast loading, small size)
 * - Print optimization (high quality, color accuracy)
 * - Compression algorithms with quality preservation
 * - Font embedding and subsetting
 *
 * 🛡️ **Validation & Quality Assurance**:
 * - Comprehensive PDF validation with detailed reporting
 * - Corruption detection and integrity checking
 * - Batch validation with summary statistics
 * - Warning and error categorization
 *
 * 🔍 **Monitoring & Debugging**:
 * - Real-time processing metrics and performance data
 * - Detailed execution history and audit trails
 * - Memory usage tracking and optimization analysis
 * - Error diagnostics with actionable insights
 *
 * Architecture & Design:
 * =====================
 *
 * The class follows a **Facade Pattern** design, providing a simplified interface
 * to the complex Ghostscript processor while maintaining full access to advanced
 * features when needed. It implements:
 *
 * - **Fluent Interface**: Method chaining for readable, expressive code
 * - **Dependency Injection**: Configurable Ghostscript processor integration
 * - **Error Aggregation**: Centralized error handling and reporting
 * - **State Management**: Consistent file collection and processing state
 * - **Performance Monitoring**: Built-in metrics and debugging capabilities
 *
 * Usage Patterns:
 * ==============
 *
 * ```php
 * // Basic PDF merging workflow
 * $processor = new Processor();
 * $processor->setFiles(['doc1.pdf', 'doc2.pdf', 'doc3.pdf'])
 *           ->merge('/path/to/merged.pdf');
 *
 * // Advanced optimization with debugging
 * $processor->enableDebug()
 *           ->optimizeForWeb('large.pdf', 'optimized.pdf');
 *
 * // Batch validation with detailed reporting
 * $results = $processor->validatePdfFiles($pdfCollection);
 * foreach ($results['results'] as $result) {
 *     if (!$result['valid']) {
 *         echo "Invalid: {$result['file_path']}\n";
 *         foreach ($result['details']['errors'] as $error) {
 *             echo "  - $error\n";
 *         }
 *     }
 * }
 *
 * // Page extraction with range specification
 * $processor->extractPages('document.pdf', 'pages_1_5.pdf', 1, 5);
 *
 * // Format conversion with quality control
 * $processor->convertToImage('document.pdf', 'preview.png', 'png');
 * ```
 *
 * Integration Benefits:
 * ====================
 *
 * 🎯 **Developer Experience**:
 * - Intuitive API that abstracts Ghostscript complexity
 * - Comprehensive error messages with actionable guidance
 * - Extensive documentation with real-world examples
 * - Type safety with modern PHP 8+ features
 *
 * 🚀 **Performance & Scalability**:
 * - Optimized for batch processing and high-volume operations
 * - Memory-efficient processing with resource management
 * - Configurable quality settings for different use cases
 * - Built-in performance monitoring and optimization
 *
 * 🔒 **Enterprise Readiness**:
 * - Robust error handling with detailed diagnostics
 * - Audit trail capabilities for compliance requirements
 * - Configurable security and validation policies
 * - Production-ready logging and monitoring integration
 *
 * @throws    Exception For high-level operation failures
 * @throws    RuntimeException For underlying Ghostscript execution issues
 * @throws    InvalidArgumentException For invalid parameters or file paths
 * @package   Nzoom\Pdf
 * <AUTHOR> Development Team
 * @version   2.0.0
 * @since     PHP 8.0
 * @license   MIT
 *
 * @see       \Nzoom\Ghostscript\Processor For underlying Ghostscript operations
 * @see       https://www.ghostscript.com/ Official Ghostscript documentation
 *
 * @requires  PHP 8.0+ with modern type system support
 * @requires  Ghostscript 9.50+ installed and accessible
 * @requires  Sufficient disk space for temporary operations
 * @requires  Appropriate file system permissions for I/O operations
 *
 */
class Processor
{
    // =============================================
    // CORE PROCESSOR STATE PROPERTIES
    // =============================================

    /**
     * Collection of PDF file paths queued for processing
     *
     * This array maintains an ordered collection of PDF file paths that will be
     * processed in batch operations. The order is significant for operations like
     * merging, where the sequence determines the final document structure.
     *
     * **File Management Features**:
     * - Maintains processing order for deterministic results
     * - Supports both absolute and relative file paths
     * - Validates file existence during processing operations
     * - Enables batch operations with consistent state management
     *
     * **Usage Patterns**:
     * - PDF merging: Files are concatenated in array order
     * - Batch validation: All files are processed sequentially
     * - State management: Cleared between different operations
     * - Error handling: Invalid files are reported with context
     *
     * @var array<string> Ordered array of PDF file paths for processing
     */
    private array $files = [];

    /**
     * Advanced Ghostscript processor engine instance
     *
     * This property holds the core Ghostscript processor that performs all
     * low-level PDF operations. It provides access to the full power of
     * Ghostscript while maintaining a clean separation of concerns.
     *
     * **Integration Features**:
     * - Configurable during construction with custom options
     * - Maintains processing state and execution history
     * - Provides comprehensive debugging and monitoring capabilities
     * - Handles cross-platform Ghostscript executable detection
     *
     * **Delegation Pattern**:
     * The PDF processor acts as a facade, delegating complex operations
     * to the Ghostscript processor while providing a simplified,
     * PDF-focused interface for common document operations.
     *
     * **Performance Considerations**:
     * - Single instance per PDF processor for state consistency
     * - Reused across multiple operations for efficiency
     * - Maintains execution history for performance analysis
     * - Supports concurrent operations through proper state management
     *
     * @var GhostscriptProcessor Advanced Ghostscript processing engine
     */
    private GhostscriptProcessor $ghostscriptProcessor;

    /**
     * Debug mode activation flag for detailed operation monitoring
     *
     * When enabled, this flag activates comprehensive debugging and monitoring
     * capabilities throughout the PDF processing pipeline. Debug information
     * includes execution timing, memory usage, command details, and detailed
     * error diagnostics.
     *
     * **Debug Information Collected**:
     * - Operation timing and performance metrics
     * - Memory usage patterns and optimization opportunities
     * - Ghostscript command construction and execution details
     * - File validation results and integrity checks
     * - Error categorization and diagnostic information
     *
     * **Performance Impact**:
     * - Minimal overhead when disabled (default state)
     * - Moderate overhead when enabled due to data collection
     * - Debug data stored in memory for immediate access
     * - Automatic cleanup between operations to prevent memory leaks
     *
     * **Use Cases**:
     * - Development and testing environments
     * - Production troubleshooting and diagnostics
     * - Performance optimization and analysis
     * - Compliance and audit trail requirements
     *
     * @var bool Debug mode status (default: false for production efficiency)
     */
    private bool $debugMode = false;

    // =============================================
    // INITIALIZATION AND CONFIGURATION
    // =============================================

    /**
     * Initialize the PDF processor with optional Ghostscript configuration
     *
     * The constructor sets up the PDF processing environment by creating and
     * configuring the underlying Ghostscript processor. It provides a clean
     * initialization path while allowing advanced users to customize the
     * Ghostscript engine behavior through configuration options.
     *
     * **Initialization Process**:
     * 1. **Ghostscript Engine Setup**: Creates a new Ghostscript processor
     *    instance with the provided configuration options
     * 2. **Default Configuration**: Applies sensible defaults for PDF operations
     *    while respecting any custom overrides
     * 3. **State Initialization**: Prepares the processor for immediate use
     *    with clean state and empty file collections
     * 4. **Error Handling Setup**: Configures error aggregation and reporting
     *    mechanisms for comprehensive diagnostics
     *
     * **Configuration Options**:
     * The ghostscriptOptions parameter accepts any valid Ghostscript processor
     * configuration, including:
     * - Quality presets (screen, ebook, printer, prepress)
     * - Resolution settings for different output requirements
     * - Compression and optimization parameters
     * - Security and permission configurations
     * - Custom Ghostscript parameters for advanced use cases
     *
     * **Design Patterns**:
     * - **Dependency Injection**: Accepts configuration at construction time
     * - **Facade Pattern**: Simplifies complex Ghostscript configuration
     * - **Builder Pattern**: Enables fluent configuration through method chaining
     * - **Factory Pattern**: Creates properly configured processor instances
     *
     * @param array $ghostscriptOptions Optional configuration array for the
     *                                  underlying Ghostscript processor. Supports
     *                                  all Ghostscript processor options including:
     *                                  - 'device' => Output format specification
     *                                  - 'resolution' => DPI settings
     *                                  - 'quality' => Compression quality (0.0-1.0)
     *                                  - 'compatibility' => PDF version compatibility
     *                                  - Custom parameters for advanced scenarios
     *
     * @throws RuntimeException When Ghostscript is not available or accessible
     * @throws InvalidArgumentException When invalid configuration options are provided
     *
     * @example
     * ```php
     * // Basic initialization with defaults
     * $processor = new Processor();
     *
     * // High-quality configuration for print workflows
     * $processor = new Processor([
     *     'resolution' => 600,
     *     'quality' => 0.95,
     *     'device' => GhostscriptProcessor::DEVICE_PDF
     * ]);
     *
     * // Web-optimized configuration for online delivery
     * $processor = new Processor([
     *     'resolution' => 150,
     *     'quality' => 0.7,
     *     'compress_pages' => true,
     *     'optimize' => true
     * ]);
     * ```
     */
    public function __construct(array $ghostscriptOptions = [])
    {
        $this->ghostscriptProcessor = new GhostscriptProcessor($ghostscriptOptions);
    }

    // =============================================
    // DEBUG AND MONITORING MANAGEMENT
    // =============================================

    /**
     * Activate comprehensive debug mode for detailed operation monitoring
     *
     * This method enables extensive debugging and monitoring capabilities across
     * both the PDF processor and the underlying Ghostscript engine. When activated,
     * the system collects detailed performance metrics, execution traces, and
     * diagnostic information for all subsequent operations.
     *
     * **Debug Information Collected**:
     * - **Execution Timing**: Precise timing for each operation phase
     * - **Memory Usage**: Memory consumption patterns and peak usage
     * - **Command Construction**: Detailed Ghostscript command building process
     * - **File Operations**: I/O operations and file validation results
     * - **Error Diagnostics**: Comprehensive error categorization and context
     * - **Performance Metrics**: Throughput, efficiency, and optimization data
     *
     * **Synchronization Behavior**:
     * The method ensures both the PDF processor and Ghostscript processor
     * are synchronized in debug mode, providing consistent monitoring across
     * the entire processing pipeline.
     *
     * **Performance Considerations**:
     * - Moderate performance overhead due to data collection
     * - Memory usage increases for debug data storage
     * - Recommended for development, testing, and troubleshooting
     * - Should be disabled in high-performance production environments
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * $processor->enableDebug()
     *           ->optimizeForWeb('input.pdf', 'output.pdf');
     *
     * $debugInfo = $processor->getLastDebugInfo();
     * echo "Processing time: {$debugInfo['total_duration']}s\n";
     * echo "Memory used: " . ($debugInfo['memory_used'] / 1024 / 1024) . "MB\n";
     * ```
     */
    public function enableDebug(): self
    {
        $this->debugMode = true;
        $this->ghostscriptProcessor->enableDebug();
        return $this;
    }

    /**
     * Deactivate debug mode to optimize performance for production use
     *
     * This method disables debugging and monitoring capabilities to minimize
     * performance overhead in production environments. When disabled, the
     * system focuses on execution efficiency rather than data collection.
     *
     * **Performance Benefits**:
     * - Reduced memory usage by eliminating debug data collection
     * - Faster execution due to removed monitoring overhead
     * - Optimized for high-throughput production environments
     * - Maintains essential error handling without verbose diagnostics
     *
     * **Synchronization Behavior**:
     * The method ensures both the PDF processor and Ghostscript processor
     * are synchronized in production mode, eliminating debug overhead
     * throughout the processing pipeline.
     *
     * **State Management**:
     * - Existing debug data is preserved until the next operation
     * - New operations will not collect debug information
     * - Error handling remains fully functional
     * - Performance monitoring is minimized to essential metrics only
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * // Disable debug mode for production processing
     * $processor->disableDebug()
     *           ->setFiles($largeBatch)
     *           ->merge('production_output.pdf');
     * ```
     */
    public function disableDebug(): self
    {
        $this->debugMode = false;
        $this->ghostscriptProcessor->disableDebug();
        return $this;
    }

    /**
     * Check the current debug mode status
     *
     * This method provides a simple way to inspect whether debug mode is
     * currently active, useful for conditional logic and status reporting.
     *
     * **Use Cases**:
     * - Conditional debug output in application logic
     * - Status reporting and system diagnostics
     * - Performance optimization decisions
     * - Testing and validation workflows
     *
     * @return bool True if debug mode is active, false for production mode
     *
     * @example
     * ```php
     * if ($processor->isDebugEnabled()) {
     *     echo "Debug mode active - detailed logging enabled\n";
     *     $stats = $processor->getExecutionStats();
     *     print_r($stats);
     * } else {
     *     echo "Production mode - optimized for performance\n";
     * }
     * ```
     */
    public function isDebugEnabled(): bool
    {
        return $this->debugMode;
    }

    // =============================================
    // ADVANCED INTEGRATION ACCESS
    // =============================================

    /**
     * Get direct access to the underlying Ghostscript processor engine
     *
     * This method provides advanced users with direct access to the powerful
     * Ghostscript processor, enabling custom operations and fine-grained
     * control beyond the standard PDF processor interface.
     *
     * **Advanced Use Cases**:
     * - Custom Ghostscript operations not covered by the PDF processor
     * - Direct access to advanced configuration options
     * - Integration with existing Ghostscript-based workflows
     * - Performance optimization through direct engine access
     * - Custom quality presets and processing pipelines
     *
     * **Integration Patterns**:
     * - **Direct Operation**: Execute custom Ghostscript commands
     * - **Configuration Access**: Modify advanced processor settings
     * - **State Inspection**: Access detailed execution history and metrics
     * - **Custom Workflows**: Build complex processing pipelines
     *
     * **Responsibility Note**:
     * Direct access to the Ghostscript processor bypasses the PDF processor's
     * safety mechanisms and state management. Users should ensure proper
     * error handling and state consistency when using direct access.
     *
     * @return GhostscriptProcessor Direct access to the Ghostscript processing engine
     *
     * @example
     * ```php
     * // Access advanced Ghostscript features
     * $gs = $processor->getGhostscriptProcessor();
     *
     * // Apply custom quality preset
     * $gs->applyQualityPreset(GhostscriptProcessor::QUALITY_PREPRESS)
     *    ->setOption('resolution', 1200)
     *    ->setOption('mono_image_resolution', 2400);
     *
     * // Execute with custom configuration
     * $processor->merge('high_quality_output.pdf');
     *
     * // Access detailed execution metrics
     * $metrics = $gs->getExecutionStats();
     * echo "Average throughput: {$metrics['average_throughput']} files/sec\n";
     * ```
     */
    public function getGhostscriptProcessor(): GhostscriptProcessor
    {
        return $this->ghostscriptProcessor;
    }

    // =============================================
    // FILE COLLECTION AND MANAGEMENT
    // =============================================

    /**
     * Set the complete collection of files for processing operations
     *
     * This method replaces the entire current file collection with a new set
     * of PDF files, providing a clean slate for batch operations. The method
     * is designed for scenarios where you need to completely change the
     * processing context or start a new operation with different files.
     *
     * **File Collection Features**:
     * - **Complete Replacement**: Clears existing files and sets new collection
     * - **Order Preservation**: Maintains the order specified in the input array
     * - **Batch Preparation**: Optimized for large file collections
     * - **State Reset**: Provides clean state for new processing operations
     *
     * **Processing Implications**:
     * - File order determines merge sequence in concatenation operations
     * - All files will be validated during actual processing operations
     * - Empty array is valid and clears the collection for new files
     * - Method supports both absolute and relative file paths
     *
     * **Performance Considerations**:
     * - Lightweight operation that only manages file references
     * - No file validation performed until processing begins
     * - Memory efficient for large file collections
     * - Immediate return for fluent interface chaining
     *
     * @param array $files Array of PDF file paths to set as the processing collection.
     *                     Can be empty to clear the collection. Supports both absolute
     *                     and relative paths. Order is preserved for operations like merging.
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * // Set a new collection of files for merging
     * $processor->setFiles([
     *     '/path/to/document1.pdf',
     *     '/path/to/document2.pdf',
     *     'relative/path/document3.pdf'
     * ])->merge('merged_output.pdf');
     *
     * // Clear the collection and start fresh
     * $processor->setFiles([])
     *           ->addFile('new_document.pdf');
     * ```
     */
    public function setFiles(array $files = []): self
    {
        $this->files = $files;
        return $this;
    }

    /**
     * Add a single PDF file to the current processing collection
     *
     * This method appends a single file to the existing collection, maintaining
     * the current order while adding new files to the end of the processing queue.
     * It's ideal for building file collections incrementally or adding files
     * based on dynamic conditions.
     *
     * **Collection Management**:
     * - **Append Operation**: Adds to the end of the current collection
     * - **Order Preservation**: Maintains existing file order
     * - **Incremental Building**: Supports dynamic file collection building
     * - **Duplicate Handling**: Allows duplicate file paths if needed
     *
     * **File Path Support**:
     * - Accepts both absolute and relative file paths
     * - No immediate validation (performed during processing)
     * - Supports all file systems and path formats
     * - Maintains original path format for processing
     *
     * **Use Cases**:
     * - Building file collections based on user input
     * - Adding files from directory scans or searches
     * - Conditional file inclusion based on business logic
     * - Incremental batch building for large operations
     *
     * @param string $file PDF file path to add to the processing collection.
     *                     Supports both absolute and relative paths. The file
     *                     will be added to the end of the current collection.
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * // Build a collection incrementally
     * $processor->addFile('/path/to/cover.pdf')
     *           ->addFile('/path/to/content.pdf')
     *           ->addFile('/path/to/appendix.pdf')
     *           ->merge('complete_document.pdf');
     *
     * // Conditional file addition
     * if ($includeAppendix) {
     *     $processor->addFile('/path/to/appendix.pdf');
     * }
     * ```
     */
    public function addFile(string $file): self
    {
        $this->files[] = $file;
        return $this;
    }

    /**
     * Get the current collection of files queued for processing
     *
     * This method returns a copy of the current file collection, providing
     * read-only access to inspect the files that will be processed in the
     * next operation. The returned array preserves the processing order
     * and can be used for validation, logging, or user interface display.
     *
     * **Return Value Characteristics**:
     * - **Immutable Copy**: Returns a copy, not a reference to internal state
     * - **Order Preserved**: Files are returned in processing order
     * - **Complete Collection**: Includes all files added via setFiles() or addFile()
     * - **Path Format**: Maintains original path formats (absolute/relative)
     *
     * **Common Use Cases**:
     * - Pre-processing validation and file existence checks
     * - User interface display of pending operations
     * - Logging and audit trail generation
     * - Debug output and troubleshooting
     * - Progress tracking for batch operations
     *
     * @return array<string> Array of PDF file paths in processing order.
     *                       Returns empty array if no files are queued.
     *
     * @example
     * ```php
     * // Inspect the current file collection
     * $files = $processor->getFiles();
     * echo "Files queued for processing: " . count($files) . "\n";
     * foreach ($files as $index => $file) {
     *     echo ($index + 1) . ". " . basename($file) . "\n";
     * }
     *
     * // Validate files before processing
     * foreach ($processor->getFiles() as $file) {
     *     if (!file_exists($file)) {
     *         throw new Exception("File not found: $file");
     *     }
     * }
     * ```
     */
    public function getFiles(): array
    {
        return $this->files;
    }

    /**
     * Clear the entire file collection to prepare for new operations
     *
     * This method removes all files from the current processing collection,
     * effectively resetting the processor to an empty state. It's useful
     * when reusing a processor instance for different operations or when
     * you need to start fresh with a new set of files.
     *
     * **State Management**:
     * - **Complete Reset**: Removes all files from the collection
     * - **Clean State**: Prepares processor for new file additions
     * - **Memory Efficient**: Releases references to file paths
     * - **Immediate Effect**: Changes take effect immediately
     *
     * **Reusability Benefits**:
     * - Enables processor instance reuse for multiple operations
     * - Prevents accidental inclusion of files from previous operations
     * - Provides clean separation between different processing tasks
     * - Supports workflow patterns with multiple processing phases
     *
     * **Performance Considerations**:
     * - Lightweight operation with minimal overhead
     * - No file system operations performed
     * - Immediate return for fluent interface support
     * - Memory usage reduced by releasing file path references
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * // Clear and start a new operation
     * $processor->clearFiles()
     *           ->addFile('new_document.pdf')
     *           ->optimizeForWeb('new_document.pdf', 'optimized.pdf');
     *
     * // Reuse processor for different operations
     * $processor->setFiles($batch1)->merge('output1.pdf');
     * $processor->clearFiles()
     *           ->setFiles($batch2)->merge('output2.pdf');
     * ```
     */
    public function clearFiles(): self
    {
        $this->files = [];
        return $this;
    }

    // =============================================
    // CORE PDF PROCESSING OPERATIONS
    // =============================================

    /**
     * Merge multiple PDF files into a single consolidated document
     *
     * This method performs intelligent PDF merging using the advanced Ghostscript
     * engine, combining multiple PDF files into a single output document while
     * preserving document quality, fonts, and formatting. The operation is
     * optimized for both performance and output quality.
     *
     * **Merge Process Workflow**:
     * 1. **Input Validation**: Validates all files in the current collection
     * 2. **Ghostscript Configuration**: Sets up optimal merging parameters
     * 3. **File Processing**: Processes files in the specified order
     * 4. **Quality Preservation**: Maintains fonts, images, and formatting
     * 5. **Output Generation**: Creates the merged PDF at the specified path
     *
     * **Quality and Compatibility Features**:
     * - **Font Preservation**: Maintains all embedded fonts and text formatting
     * - **Image Quality**: Preserves image resolution and color profiles
     * - **Metadata Handling**: Combines document metadata appropriately
     * - **Bookmark Integration**: Merges bookmarks and navigation structures
     * - **Form Field Support**: Handles interactive form elements correctly
     * - **Security Settings**: Applies appropriate security configurations
     *
     * **Performance Optimizations**:
     * - **Streaming Processing**: Handles large files efficiently
     * - **Memory Management**: Optimized for minimal memory usage
     * - **Batch Processing**: Efficient handling of multiple input files
     * - **Compression**: Applies intelligent compression to reduce output size
     *
     * **Error Handling and Recovery**:
     * - **Input Validation**: Comprehensive file existence and format checking
     * - **Process Monitoring**: Real-time monitoring of merge progress
     * - **Error Reporting**: Detailed error messages with actionable guidance
     * - **Rollback Support**: Clean failure handling without partial outputs
     *
     * **File Order Significance**:
     * The order of files in the collection determines their order in the final
     * merged document. The first file becomes the first pages, the second file
     * follows, and so on. This allows precise control over document structure.
     *
     * @param string $outputFilePath Absolute or relative path where the merged PDF
     *                               will be saved. The directory must exist and be
     *                               writable. If the file exists, it will be overwritten.
     *
     * @return bool True if the merge operation completed successfully, false otherwise.
     *              Check getErrors() for detailed error information on failure.
     *
     * @throws Exception When the merge operation fails due to file access issues,
     *                   invalid input files, or Ghostscript execution problems.
     *                   The exception message contains detailed error information.
     *
     * @example
     * ```php
     * // Basic PDF merging
     * $processor->setFiles([
     *     '/path/to/cover.pdf',
     *     '/path/to/content.pdf',
     *     '/path/to/appendix.pdf'
     * ])->merge('/path/to/merged_document.pdf');
     *
     * // Merge with error handling
     * try {
     *     $success = $processor->merge('output.pdf');
     *     if ($success) {
     *         echo "Merge completed successfully\n";
     *     } else {
     *         echo "Merge failed: " . $processor->getLastError() . "\n";
     *     }
     * } catch (Exception $e) {
     *     echo "Critical error: " . $e->getMessage() . "\n";
     * }
     *
     * // Merge with debug monitoring
     * $processor->enableDebug()
     *           ->merge('output.pdf');
     * $debugInfo = $processor->getLastDebugInfo();
     * echo "Processing time: {$debugInfo['total_duration']}s\n";
     * echo "Files processed: {$debugInfo['input_files_count']}\n";
     * ```
     */
    public function merge(string $outputFilePath): bool
    {
        try {
            $result = $this->ghostscriptProcessor
                ->clearInputFiles()
                ->addInputFiles($this->getFiles())
                ->setOutputFile($outputFilePath)
                ->setupPdfMerging()
                ->execute();

            return $result;
        } catch (RuntimeException $e) {
            throw new Exception('PDF merge failed: ' . $e->getMessage());
        }
    }

    /**
     * Convert image to PDF
     *
     * @param string $imageFile  Input image file path
     * @param string $outputFile Output PDF file path
     *
     * @return bool Success status
     * @throws Exception
     */
    public function convertImageToPdf(string $imageFile, string $outputFile = 'image.pdf'): bool
    {
        try {
            return $this->ghostscriptProcessor
                ->clearInputFiles()
                ->addInputFile($imageFile)
                ->setOutputFile($outputFile)
                ->setupImageConversion()
                ->execute();
        } catch (RuntimeException $e) {
            throw new Exception('Image to PDF conversion failed: ' . $e->getMessage());
        }
    }

    /**
     * Validate if a PDF file is valid
     *
     * @param string $file PDF file path
     *
     * @return bool True if valid, false otherwise
     */
    public function isValid(string $file): bool
    {
        $validation = $this->ghostscriptProcessor->validatePdf($file);

        if ($validation['valid']) {
            // Consider files with warnings as invalid for strict validation
            if ($validation['details']['warning_count'] > 0) {
                return false;
            }
            return true;
        }

        return false;
    }

    /**
     * Get detailed validation information for a PDF file
     *
     * @param string $file PDF file path
     *
     * @return array Detailed validation results
     */
    public function validatePdf(string $file): array
    {
        return $this->ghostscriptProcessor->validatePdf($file);
    }

    /**
     * Validate multiple PDF files
     *
     * @param array $files Array of PDF file paths
     *
     * @return array Batch validation results
     */
    public function validatePdfFiles(array $files): array
    {
        return $this->ghostscriptProcessor->validatePdfFiles($files);
    }

    /**
     * Optimize PDF for web delivery
     *
     * @param string $inputFile  Input PDF file path
     * @param string $outputFile Output PDF file path
     *
     * @return bool Success status
     * @throws Exception
     */
    public function optimizeForWeb(string $inputFile, string $outputFile): bool
    {
        try {
            return $this->ghostscriptProcessor
                ->clearInputFiles()
                ->addInputFile($inputFile)
                ->setOutputFile($outputFile)
                ->setupWebOptimization()
                ->execute();
        } catch (RuntimeException $e) {
            throw new Exception('PDF web optimization failed: ' . $e->getMessage());
        }
    }

    /**
     * Optimize PDF for print
     *
     * @param string $inputFile  Input PDF file path
     * @param string $outputFile Output PDF file path
     *
     * @return bool Success status
     * @throws Exception
     */
    public function optimizeForPrint(string $inputFile, string $outputFile): bool
    {
        try {
            return $this->ghostscriptProcessor
                ->clearInputFiles()
                ->addInputFile($inputFile)
                ->setOutputFile($outputFile)
                ->setupPrintOptimization()
                ->execute();
        } catch (RuntimeException $e) {
            throw new Exception('PDF print optimization failed: ' . $e->getMessage());
        }
    }

    /**
     * Extract specific pages from a PDF
     *
     * @param string   $inputFile  Input PDF file path
     * @param string   $outputFile Output PDF file path
     * @param int      $firstPage  First page to extract (1-based)
     * @param int|null $lastPage   Last page to extract (1-based), null for single page
     *
     * @return bool Success status
     * @throws Exception
     */
    public function extractPages(string $inputFile, string $outputFile, int $firstPage, ?int $lastPage = null): bool
    {
        try {
            return $this->ghostscriptProcessor
                ->clearInputFiles()
                ->addInputFile($inputFile)
                ->setOutputFile($outputFile)
                ->setupPageExtraction($firstPage, $lastPage)
                ->execute();
        } catch (RuntimeException $e) {
            throw new Exception('PDF page extraction failed: ' . $e->getMessage());
        }
    }

    /**
     * Convert PDF to image format
     *
     * @param string $inputFile  Input PDF file path
     * @param string $outputFile Output image file path
     * @param string $format     Image format (png, jpeg, tiff)
     *
     * @return bool Success status
     * @throws Exception
     */
    public function convertToImage(string $inputFile, string $outputFile, string $format = 'png'): bool
    {
        try {
            $deviceMap = [
                'png' => GhostscriptProcessor::DEVICE_PNG,
                'jpeg' => GhostscriptProcessor::DEVICE_JPEG,
                'jpg' => GhostscriptProcessor::DEVICE_JPEG,
                'tiff' => GhostscriptProcessor::DEVICE_TIFF,
                'tif' => GhostscriptProcessor::DEVICE_TIFF,
            ];

            $device = $deviceMap[strtolower($format)] ?? GhostscriptProcessor::DEVICE_PNG;

            return $this->ghostscriptProcessor
                ->clearInputFiles()
                ->addInputFile($inputFile)
                ->setOutputFile($outputFile)
                ->setupImageConversion($device)
                ->execute();
        } catch (RuntimeException $e) {
            throw new Exception('PDF to image conversion failed: ' . $e->getMessage());
        }
    }

    /**
     * Get debug information from the last operation
     *
     * @return array Debug information
     */
    public function getLastDebugInfo(): array
    {
        return $this->ghostscriptProcessor->getLastDebugInfo();
    }

    /**
     * Get execution history
     *
     * @return array Execution history
     */
    public function getExecutionHistory(): array
    {
        return $this->ghostscriptProcessor->getExecutionHistory();
    }

    /**
     * Get execution statistics
     *
     * @return array Execution statistics
     */
    public function getExecutionStats(): array
    {
        return $this->ghostscriptProcessor->getExecutionStats();
    }

    /**
     * Get errors from the last operation
     *
     * @return array Error messages
     */
    public function getErrors(): array
    {
        return $this->ghostscriptProcessor->getErrors();
    }

    /**
     * Check if there are any errors
     *
     * @return bool
     */
    public function hasErrors(): bool
    {
        return $this->ghostscriptProcessor->hasErrors();
    }

    /**
     * Get the last error message
     *
     * @return string|null
     */
    public function getLastError(): ?string
    {
        return $this->ghostscriptProcessor->getLastError();
    }

    /**
     * Clear all errors
     *
     * @return self
     */
    public function clearErrors(): self
    {
        $this->ghostscriptProcessor->clearErrors();
        return $this;
    }
}
