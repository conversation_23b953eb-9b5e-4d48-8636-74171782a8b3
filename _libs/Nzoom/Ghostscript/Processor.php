<?php

namespace Nzoom\Ghostscript;

use InvalidArgumentException;
use RuntimeException;

/**
 * Advanced GhostScript Processor for PDF and Document Operations
 *
 * This class provides a comprehensive, object-oriented interface to GhostScript,
 * enabling sophisticated PDF processing, document conversion, and optimization
 * operations. It abstracts the complexity of GhostScript command-line operations
 * into a fluent, type-safe PHP API with extensive debugging and monitoring capabilities.
 *
 * Key Features:
 * ============
 *
 * 🔧 **Core Operations**:
 * - PDF merging and concatenation with validation
 * - Document format conversion (PDF ↔ PS/EPS ↔ Images)
 * - Page extraction and manipulation
 * - Document optimization for different use cases
 *
 * 📊 **Quality Management**:
 * - Predefined quality presets (screen, ebook, printer, prepress)
 * - Granular control over compression and image quality
 * - Resolution management for different output requirements
 * - Font embedding and preservation options
 *
 * 🛡️ **Security & Validation**:
 * - Comprehensive PDF validation with detailed reporting
 * - Security permission management (printing, copying, etc.)
 * - File integrity checking and corruption detection
 * - Safe execution with input sanitization
 *
 * 🔍 **Debugging & Monitoring**:
 * - Detailed execution logging and performance metrics
 * - Command history and replay capabilities
 * - Memory usage tracking and optimization analysis
 * - Error categorization and diagnostic information
 *
 * 🎯 **Enterprise Features**:
 * - Batch processing with progress tracking
 * - Extensible operation framework
 * - Cross-platform compatibility (Windows/Linux/macOS)
 * - PSR-4 compliant with modern PHP practices
 *
 * Usage Examples:
 * ==============
 *
 * ```php
 * // Basic PDF merging
 * $processor = new Processor();
 * $processor->addInputFiles(['doc1.pdf', 'doc2.pdf'])
 *           ->setOutputFile('merged.pdf')
 *           ->setupPdfMerging()
 *           ->execute();
 *
 * // Quality optimization
 * $processor->applyQualityPreset(Processor::QUALITY_WEB)
 *           ->optimizeForWeb('input.pdf', 'output.pdf');
 *
 * // Advanced validation
 * $validation = $processor->validatePdf('document.pdf');
 * if (!$validation['valid']) {
 *     foreach ($validation['details']['errors'] as $error) {
 *         echo "Error: $error\n";
 *     }
 * }
 *
 * // Debug monitoring
 * $processor->enableDebug();
 * $processor->execute();
 * $stats = $processor->getExecutionStats();
 * echo "Processing rate: {$stats['average_throughput']} files/sec\n";
 * ```
 *
 * Architecture:
 * ============
 *
 * The class is organized into logical sections:
 * - **Initialization**: GhostScript detection and configuration
 * - **File Management**: Input/output file handling with validation
 * - **Options Management**: Configuration and preset management
 * - **Command Building**: Dynamic GhostScript command construction
 * - **Execution Engine**: Process execution with monitoring
 * - **Operations**: High-level document processing operations
 * - **Validation**: Document integrity and quality checking
 * - **Debugging**: Comprehensive logging and analysis tools
 *
 * @throws    RuntimeException When GhostScript is not available or execution fails
 * @throws    InvalidArgumentException When invalid parameters are provided
 * @version   2.0.0
 * @since     PHP 8.0
 * @license   MIT
 *
 * @see       https://www.ghostscript.com/ Official GhostScript documentation
 * @see       https://www.ghostscript.com/doc/current/Use.htm GhostScript usage guide
 *
 * @requires  GhostScript 9.50+ installed and accessible via PATH
 * @requires  PHP 8.0+ with exec() function enabled
 * @requires  Sufficient disk space for temporary file operations
 *
 * @package   Nzoom\Ghostscript
 * <AUTHOR> Development Team
 */
class Processor
{
    // =============================================
    // CORE PROCESSOR STATE PROPERTIES
    // =============================================

    /**
     * Path to the GhostScript executable
     *
     * This property stores the absolute or relative path to the GhostScript
     * executable that will be used for all processing operations. The path
     * is automatically detected during initialization based on the operating
     * system and available GhostScript installations.
     *
     * @var string Detected GhostScript executable path
     */
    protected string $executable;

    /**
     * Collection of input file paths for processing
     *
     * This array contains the absolute paths to all files that will be
     * processed in the current operation. Files are validated for existence
     * and readability when added to ensure processing reliability.
     *
     * @var array<string> Array of validated input file paths
     */
    protected array $inputFiles = [];

    /**
     * Output file path for processed results
     *
     * The destination path where the processed output will be saved.
     * The directory must exist and be writable. If not specified,
     * a temporary file path is automatically generated.
     *
     * @var string Output file path with write permissions
     */
    protected string $outputFile;

    /**
     * Current processing options and configuration
     *
     * This array contains all configuration options that will be applied
     * to the current GhostScript operation. Options are merged from defaults
     * and user-specified values, with user values taking precedence.
     *
     * @var array<string, mixed> Current processing configuration
     */
    protected array $options = [];

    /**
     * Default processing options template
     *
     * This array defines the baseline configuration for all operations,
     * providing sensible defaults for quality, compression, security,
     * and other processing parameters. These defaults can be overridden
     * on a per-operation basis.
     *
     * @var array<string, mixed> Default configuration template
     */
    protected array $defaultOptions = [];

    /**
     * Collection of error messages from operations
     *
     * This array accumulates error messages from failed operations,
     * validation failures, and other issues encountered during processing.
     * Errors are categorized and can be retrieved for debugging purposes.
     *
     * @var array<string> Array of error messages
     */
    protected array $errors = [];

    /**
     * Registry of custom operations and extensions
     *
     * This array stores user-defined operations that extend the processor's
     * capabilities beyond the built-in functionality. Operations are callable
     * functions that can be invoked with custom parameters.
     *
     * @var array<string, callable> Custom operation registry
     */
    protected array $operations = [];

    /**
     * Debug mode activation flag
     *
     * When enabled, the processor collects detailed execution information,
     * performance metrics, and diagnostic data for all operations. This
     * information is stored in debug arrays for analysis and troubleshooting.
     *
     * @var bool Debug mode status (default: false)
     */
    protected bool $debugMode = false;

    /**
     * Debug information from the last operation
     *
     * This array contains comprehensive debugging information from the most
     * recent operation, including timing data, memory usage, command details,
     * and execution results. Only populated when debug mode is enabled.
     *
     * @var array<string, mixed> Last operation debug data
     */
    protected array $debugInfo = [];

    /**
     * Historical record of all operations performed
     *
     * This array maintains a chronological history of all operations
     * executed by this processor instance, including their debug information,
     * performance metrics, and results. Used for analysis and reporting.
     *
     * @var array<array> Array of historical operation records
     */
    protected array $executionHistory = [];

    // =============================================
    // OUTPUT DEVICE CONSTANTS
    // =============================================

    /**
     * PDF output device for document generation and merging
     *
     * Uses GhostScript's 'pdfwrite' device to generate PDF files with
     * comprehensive control over compression, fonts, and compatibility.
     * Ideal for document merging, optimization, and format conversion.
     */
    public const DEVICE_PDF = 'pdfwrite';

    /**
     * High-quality PNG image output device
     *
     * Uses GhostScript's 'png16m' device to generate 24-bit color PNG
     * images with full color depth. Suitable for high-quality image
     * conversion from PDF and PostScript sources.
     */
    public const DEVICE_PNG = 'png16m';

    /**
     * JPEG image output device with compression
     *
     * Uses GhostScript's 'jpeg' device to generate compressed JPEG
     * images. Quality can be controlled through image quality parameters.
     * Ideal for web-optimized image conversion.
     */
    public const DEVICE_JPEG = 'jpeg';

    /**
     * High-quality TIFF image output device
     *
     * Uses GhostScript's 'tiff24nc' device to generate 24-bit color
     * TIFF images without compression. Suitable for archival and
     * print-quality image conversion.
     */
    public const DEVICE_TIFF = 'tiff24nc';

    /**
     * PostScript Level 2 output device
     *
     * Uses GhostScript's 'ps2write' device to generate PostScript
     * Level 2 files. Compatible with most PostScript interpreters
     * and suitable for print workflows.
     */
    public const DEVICE_PS = 'ps2write';

    /**
     * Encapsulated PostScript output device
     *
     * Uses GhostScript's 'eps2write' device to generate EPS files
     * suitable for embedding in other documents. Maintains proper
     * bounding box information for accurate placement.
     */
    public const DEVICE_EPS = 'eps2write';

    // =============================================
    // PDF COMPATIBILITY LEVEL CONSTANTS
    // =============================================

    /**
     * PDF 1.2 compatibility (Acrobat 3.0)
     *
     * Basic PDF features with limited compression and security options.
     * Compatible with very old PDF readers but lacks modern features.
     */
    public const PDF_VERSION_1_2 = '1.2';

    /**
     * PDF 1.3 compatibility (Acrobat 4.0)
     *
     * Adds digital signatures, JavaScript, and improved compression.
     * Good compatibility with older systems while supporting essential features.
     */
    public const PDF_VERSION_1_3 = '1.3';

    /**
     * PDF 1.4 compatibility (Acrobat 5.0) - RECOMMENDED DEFAULT
     *
     * Introduces transparency, tagged PDF, and enhanced security.
     * Excellent balance of features and compatibility for most use cases.
     */
    public const PDF_VERSION_1_4 = '1.4';

    /**
     * PDF 1.5 compatibility (Acrobat 6.0)
     *
     * Adds layers, cross-reference streams, and improved compression.
     * Suitable for complex documents with advanced layout requirements.
     */
    public const PDF_VERSION_1_5 = '1.5';

    /**
     * PDF 1.6 compatibility (Acrobat 7.0)
     *
     * Introduces 3D content, enhanced forms, and digital signatures.
     * Required for documents with advanced interactive features.
     */
    public const PDF_VERSION_1_6 = '1.6';

    /**
     * PDF 1.7 compatibility (Acrobat 8.0)
     *
     * Latest widely-supported PDF version with full feature set.
     * Includes portfolios, rich media, and advanced security options.
     */
    public const PDF_VERSION_1_7 = '1.7';

    // =============================================
    // QUALITY PRESET CONSTANTS
    // =============================================

    /**
     * Screen/Web quality preset (72 DPI)
     *
     * Optimized for on-screen viewing and web delivery with:
     * - Low resolution (72 DPI) for fast loading
     * - High compression for small file sizes
     * - Reduced image quality for bandwidth efficiency
     * - Ideal for: Web pages, email attachments, screen presentations
     */
    public const QUALITY_SCREEN = 'screen';

    /**
     * E-book quality preset (150 DPI)
     *
     * Balanced quality for digital reading devices with:
     * - Medium resolution (150 DPI) for clear text
     * - Moderate compression for reasonable file sizes
     * - Good image quality for illustrations
     * - Ideal for: E-readers, tablets, digital publications
     */
    public const QUALITY_EBOOK = 'ebook';

    /**
     * Printer quality preset (300 DPI)
     *
     * High quality for desktop and office printing with:
     * - Standard print resolution (300 DPI)
     * - Balanced compression preserving quality
     * - High image quality for professional appearance
     * - Ideal for: Office documents, reports, presentations
     */
    public const QUALITY_PRINTER = 'printer';

    /**
     * Prepress quality preset (600+ DPI)
     *
     * Maximum quality for professional printing with:
     * - High resolution (600+ DPI) for crisp output
     * - Minimal compression to preserve all details
     * - Lossless image quality for color accuracy
     * - Ideal for: Commercial printing, marketing materials, publications
     */
    public const QUALITY_PREPRESS = 'prepress';

    // =============================================
    // INITIALIZATION AND SETUP METHODS
    // =============================================

    /**
     * Initialize the GhostScript Processor with configuration options
     *
     * The constructor performs essential initialization tasks to prepare
     * the processor for document operations:
     *
     * 1. **GhostScript Detection**: Automatically locates the GhostScript
     *    executable on the system, trying multiple common installation paths
     *    and handling cross-platform differences.
     *
     * 2. **Default Configuration**: Establishes a comprehensive set of default
     *    processing options optimized for common use cases while maintaining
     *    flexibility for customization.
     *
     * 3. **Option Merging**: Applies user-provided options over the defaults,
     *    allowing immediate customization during instantiation.
     *
     * 4. **Output Setup**: Configures a default output file path in the system
     *    temporary directory with a unique identifier to prevent conflicts.
     *
     * The processor is immediately ready for use after construction, with
     * sensible defaults that work for most PDF processing scenarios.
     *
     * @param array $options Optional configuration overrides for default settings
     *                       Common options include:
     *                       - 'device' => Output format (PDF, PNG, JPEG, etc.)
     *                       - 'resolution' => Output resolution in DPI
     *                       - 'quality' => Compression quality (0.0-1.0)
     *                       - 'compatibility' => PDF version compatibility
     *
     * @throws RuntimeException When GhostScript cannot be found or accessed
     *
     * @example
     * ```php
     * // Basic initialization
     * $processor = new Processor();
     *
     * // With custom options
     * $processor = new Processor([
     *     'resolution' => 600,
     *     'quality' => 0.95,
     *     'device' => Processor::DEVICE_PDF
     * ]);
     * ```
     */
    public function __construct(array $options = [])
    {
        $this->detectGhostScript();
        $this->initializeDefaultOptions();
        $this->setOptions($options);
        $this->outputFile = $this->getDefaultOutputPath();
    }

    /**
     * Detect and validate GhostScript executable on the system
     *
     * This method performs intelligent detection of the GhostScript executable
     * across different operating systems and installation configurations:
     *
     * **Detection Strategy**:
     * 1. **Platform Detection**: Identifies the operating system to determine
     *    the most likely executable name (gswin64c for Windows, gs for Unix-like)
     *
     * 2. **Primary Executable Test**: Attempts to execute the primary candidate
     *    with the version flag to verify functionality
     *
     * 3. **Fallback Testing**: If the primary executable fails, systematically
     *    tests alternative common executable names and installation paths
     *
     * 4. **Validation**: Ensures the detected executable actually responds to
     *    GhostScript commands and returns expected version information
     *
     * **Supported Executables**:
     * - `gs` - Standard Unix/Linux/macOS installation
     * - `gswin64c` - Windows 64-bit console version (preferred)
     * - `gswin32c` - Windows 32-bit console version
     * - `gswin64` - Windows 64-bit GUI version
     * - `gswin32` - Windows 32-bit GUI version
     * - `ghostscript` - Alternative installation name
     *
     * The method prioritizes console versions over GUI versions for better
     * automation compatibility and error handling.
     *
     * @return void
     * @throws RuntimeException When no working GhostScript executable is found
     *
     * @see https://www.ghostscript.com/download/gsdnld.html GhostScript download page
     */
    protected function detectGhostScript(): void
    {
        // Determine the most likely executable based on operating system
        $executable = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN' ? 'gswin64c' : 'gs';

        // Test the primary executable candidate
        exec(escapeshellcmd($executable) . ' -v 2>&1', $output, $code);

        if ($code !== 0) {
            // Primary executable failed, try alternatives
            $alternatives = ['gswin32c', 'gswin64', 'gswin32', 'ghostscript'];

            foreach ($alternatives as $alt) {
                exec(escapeshellcmd($alt) . ' -v 2>&1', $altOutput, $altCode);
                if ($altCode === 0) {
                    $executable = $alt;
                    $code = $altCode; // Update the code to reflect success
                    break;
                }
            }

            // If all alternatives failed, throw an exception
            if ($code !== 0) {
                throw new RuntimeException(
                    'GhostScript is not installed or not found in PATH. ' .
                    'Please install GhostScript and ensure it is accessible via command line. ' .
                    'Tested executables: ' . implode(', ', array_merge([$executable], $alternatives))
                );
            }
        }

        $this->executable = $executable;
    }

    /**
     * Initialize comprehensive default processing options
     *
     * This method establishes a carefully tuned set of default options that
     * provide optimal results for most document processing scenarios. The
     * defaults balance quality, performance, and compatibility considerations.
     *
     * **Option Categories**:
     *
     * 🔧 **Core Execution Options**:
     * - Batch processing mode for automated operations
     * - Quiet mode to suppress unnecessary output
     * - Safety mode to prevent potentially harmful operations
     * - No-pause mode for unattended processing
     *
     * 📄 **PDF-Specific Configuration**:
     * - PDF 1.4 compatibility for broad reader support
     * - Comprehensive compression for optimal file sizes
     * - Font embedding to ensure consistent rendering
     * - Optimization for fast loading and display
     *
     * 🖼️ **Image and Resolution Settings**:
     * - 300 DPI default resolution for print quality
     * - High-quality image compression (90% quality)
     * - Intelligent downsampling for size optimization
     * - Separate settings for color, grayscale, and monochrome
     *
     * 🔒 **Security and Permissions**:
     * - Full permissions enabled by default
     * - Printing, copying, and annotation allowed
     * - Form filling capabilities preserved
     * - Balanced security for general use
     *
     * ⚙️ **Advanced Processing Options**:
     * - Color management preservation
     * - EPS information retention
     * - Selective feature preservation based on use case
     * - Extensible custom parameter support
     *
     * These defaults can be overridden using the options management methods
     * or by applying quality presets for specific use cases.
     *
     * @return void
     *
     * @see applyQualityPreset() For predefined quality configurations
     * @see setOptions() For custom option overrides
     */
    protected function initializeDefaultOptions(): void
    {
        $this->defaultOptions = [
            // ==========================================
            // CORE EXECUTION OPTIONS
            // ==========================================

            /** @var string Output device type - PDF generation by default */
            'device' => self::DEVICE_PDF,

            /** @var bool Suppress informational messages for cleaner output */
            'quiet' => true,

            /** @var bool Disable interactive prompts for automated processing */
            'nopause' => true,

            /** @var bool Enable batch mode for multiple file processing */
            'batch' => true,

            /** @var bool Enable safer mode to prevent potentially harmful operations */
            'safer' => true,

            // ==========================================
            // PDF-SPECIFIC CONFIGURATION
            // ==========================================

            /** @var string PDF version compatibility - 1.4 offers best balance */
            'compatibility' => self::PDF_VERSION_1_4,

            /** @var bool Enable page-level compression for smaller files */
            'compress_pages' => true,

            /** @var bool Enable stream compression for optimal size */
            'compress_streams' => true,

            /** @var bool Use Flate compression algorithm (ZIP-like) */
            'use_flate_compression' => true,

            /** @var bool Enable general optimization for faster loading */
            'optimize' => true,

            /** @var string Disable automatic page rotation to preserve layout */
            'auto_rotate_pages' => '/None',

            /** @var bool Enable duplicate image detection for size reduction */
            'detect_duplication' => true,

            /** @var bool Embed all fonts to ensure consistent rendering */
            'embed_all_fonts' => true,

            // ==========================================
            // RESOLUTION AND QUALITY SETTINGS
            // ==========================================

            /** @var int Default resolution in DPI - 300 for print quality */
            'resolution' => 300,

            /** @var int Color image resolution in DPI */
            'color_image_resolution' => 300,

            /** @var int Grayscale image resolution in DPI */
            'grayscale_image_resolution' => 300,

            /** @var int Monochrome image resolution in DPI - higher for text clarity */
            'mono_image_resolution' => 1200,

            // ==========================================
            // IMAGE QUALITY PARAMETERS
            // ==========================================

            /** @var float Color image quality (0.0-1.0) - 0.9 for high quality */
            'color_image_quality' => 0.9,

            /** @var float Grayscale image quality (0.0-1.0) */
            'grayscale_image_quality' => 0.9,

            // ==========================================
            // COMPRESSION AND DOWNSAMPLING
            // ==========================================

            /** @var bool Enable color image downsampling for size optimization */
            'color_image_downsample' => true,

            /** @var bool Enable grayscale image downsampling */
            'grayscale_image_downsample' => true,

            /** @var bool Disable mono image downsampling to preserve text quality */
            'mono_image_downsample' => false,

            // ==========================================
            // SECURITY AND PERMISSIONS
            // ==========================================

            /** @var bool Allow document printing */
            'allow_printing' => true,

            /** @var bool Allow content copying and extraction */
            'allow_copying' => true,

            /** @var bool Allow adding annotations and comments */
            'allow_annotation' => true,

            /** @var bool Allow form field completion */
            'allow_form_filling' => true,

            // ==========================================
            // ADVANCED PROCESSING OPTIONS
            // ==========================================

            /** @var bool Preserve copy page information */
            'preserve_copy_page' => true,

            /** @var bool Preserve EPS (Encapsulated PostScript) information */
            'preserve_eps_info' => true,

            /** @var bool Preserve halftone information (disabled for smaller files) */
            'preserve_halftone_info' => false,

            /** @var bool Preserve OPI (Open Prepress Interface) comments */
            'preserve_opi_comments' => false,

            /** @var bool Preserve overprint settings for print workflows */
            'preserve_overprint_settings' => false,

            /** @var string Transfer function information handling */
            'transfer_function_info' => '/Preserve',

            /** @var string UCR (Under Color Removal) and BG (Black Generation) info */
            'ucr_and_bg_info' => '/Preserve',

            // ==========================================
            // EXTENSIBILITY AND CUSTOMIZATION
            // ==========================================

            /** @var array Custom GhostScript parameters for advanced use cases */
            'custom_parameters' => [],
        ];

        // Apply the default options as the current configuration
        $this->options = $this->defaultOptions;
    }

    /**
     * Generate a unique default output file path in the system temporary directory
     *
     * This method creates a safe, unique file path for output operations when
     * no specific output path is provided. The generated path includes:
     *
     * - **System temp directory**: Uses the OS-appropriate temporary directory
     * - **Unique identifier**: Includes a unique ID to prevent file conflicts
     * - **PDF extension**: Defaults to PDF format for broad compatibility
     * - **Predictable naming**: Uses 'gs_output_' prefix for easy identification
     *
     * The generated path is guaranteed to be unique within the current process
     * and reduces the risk of file conflicts in multi-user or concurrent environments.
     *
     * @return string Unique temporary file path with PDF extension
     *
     * @example
     * ```php
     * // Example output: /tmp/gs_output_507f1f77bcf86cd799439011.pdf
     * $path = $this->getDefaultOutputPath();
     * ```
     */
    protected function getDefaultOutputPath(): string
    {
        return sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'gs_output_' . uniqid() . '.pdf';
    }

    // =============================================
    // FILE MANAGEMENT AND VALIDATION METHODS
    // =============================================

    /**
     * Add a single input file to the processing queue with validation
     *
     * This method adds a file to the input collection after performing
     * comprehensive validation to ensure the file can be successfully
     * processed. The validation includes:
     *
     * - **Existence check**: Verifies the file exists at the specified path
     * - **Readability check**: Ensures the file can be read by the current process
     * - **Path normalization**: Handles relative and absolute paths correctly
     *
     * Files are added to the processing queue in the order they are specified,
     * which determines their order in operations like PDF merging.
     *
     * @param string $file Absolute or relative path to the input file
     *
     * @return self Fluent interface for method chaining
     * @throws InvalidArgumentException When the file doesn't exist or isn't readable
     *
     * @example
     * ```php
     * $processor->addInputFile('/path/to/document.pdf')
     *           ->addInputFile('relative/path/image.png')
     *           ->execute();
     * ```
     */
    public function addInputFile(string $file): self
    {
        if (!file_exists($file)) {
            throw new InvalidArgumentException("Input file does not exist: $file");
        }

        if (!is_readable($file)) {
            throw new InvalidArgumentException("Input file is not readable: $file");
        }

        $this->inputFiles[] = $file;
        return $this;
    }

    /**
     * Add multiple input files to the processing queue with batch validation
     *
     * This method efficiently adds multiple files to the input collection,
     * validating each file individually and providing detailed error information
     * if any files fail validation. All files must pass validation for the
     * operation to succeed.
     *
     * The method maintains the order of files as specified in the input array,
     * which is important for operations where file order matters (like merging).
     *
     * @param array $files Array of file paths to add to the processing queue
     *
     * @return self Fluent interface for method chaining
     * @throws InvalidArgumentException When any file fails validation
     *
     * @example
     * ```php
     * $files = ['doc1.pdf', 'doc2.pdf', 'doc3.pdf'];
     * $processor->addInputFiles($files)->execute();
     * ```
     */
    public function addInputFiles(array $files): self
    {
        foreach ($files as $file) {
            $this->addInputFile($file);
        }
        return $this;
    }

    /**
     * Replace all input files with a new set (clears existing files first)
     *
     * This method provides a convenient way to completely replace the current
     * input file collection with a new set of files. It's equivalent to calling
     * clearInputFiles() followed by addInputFiles(), but more efficient and atomic.
     *
     * All existing files are removed from the queue before adding the new files,
     * ensuring a clean slate for the new operation.
     *
     * @param array $files Array of file paths to set as the new input collection
     *
     * @return self Fluent interface for method chaining
     * @throws InvalidArgumentException When any file fails validation
     *
     * @example
     * ```php
     * // Replace current files with a new set
     * $processor->setInputFiles(['new1.pdf', 'new2.pdf']);
     * ```
     */
    public function setInputFiles(array $files): self
    {
        $this->inputFiles = [];
        return $this->addInputFiles($files);
    }

    /**
     * Get the current collection of input files
     *
     * Returns a copy of the current input file collection, preserving the
     * order in which files were added. This method is useful for:
     *
     * - Inspecting the current file queue
     * - Debugging file processing issues
     * - Creating backups of file collections
     * - Implementing custom processing logic
     *
     * @return array<string> Array of input file paths in processing order
     *
     * @example
     * ```php
     * $files = $processor->getInputFiles();
     * echo "Processing " . count($files) . " files\n";
     * foreach ($files as $file) {
     *     echo "- " . basename($file) . "\n";
     * }
     * ```
     */
    public function getInputFiles(): array
    {
        return $this->inputFiles;
    }

    /**
     * Remove all input files from the processing queue
     *
     * This method clears the entire input file collection, effectively
     * resetting the processor to accept a new set of files. It's useful
     * when reusing a processor instance for multiple operations.
     *
     * The method provides a fluent interface for easy chaining with
     * subsequent file addition operations.
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * // Clear files and add new ones
     * $processor->clearInputFiles()
     *           ->addInputFiles(['new1.pdf', 'new2.pdf']);
     * ```
     */
    public function clearInputFiles(): self
    {
        $this->inputFiles = [];
        return $this;
    }

    /**
     * Set the output file path with directory validation
     *
     * This method sets the destination path for processed output, performing
     * validation to ensure the operation can succeed:
     *
     * - **Directory existence**: Verifies the parent directory exists
     * - **Write permissions**: Ensures the directory is writable
     * - **Path normalization**: Handles both absolute and relative paths
     *
     * The output file doesn't need to exist (it will be created), but its
     * parent directory must exist and be writable by the current process.
     *
     * @param string $file Absolute or relative path for the output file
     *
     * @return self Fluent interface for method chaining
     * @throws InvalidArgumentException When the output directory doesn't exist or isn't writable
     *
     * @example
     * ```php
     * $processor->setOutputFile('/path/to/output.pdf')
     *           ->execute();
     * ```
     */
    public function setOutputFile(string $file): self
    {
        $dir = dirname($file);
        if (!is_dir($dir)) {
            throw new InvalidArgumentException("Output directory does not exist: $dir");
        }

        if (!is_writable($dir)) {
            throw new InvalidArgumentException("Output directory is not writable: $dir");
        }

        $this->outputFile = $file;
        return $this;
    }

    /**
     * Get the current output file path
     *
     * Returns the currently configured output file path. If no output file
     * has been explicitly set, returns the default temporary file path
     * generated during initialization.
     *
     * @return string Current output file path
     *
     * @example
     * ```php
     * echo "Output will be saved to: " . $processor->getOutputFile();
     * ```
     */
    public function getOutputFile(): string
    {
        return $this->outputFile;
    }

    // =============================================
    // CONFIGURATION AND OPTIONS MANAGEMENT
    // =============================================

    /**
     * Set a single processing option with type validation
     *
     * This method allows fine-grained control over individual processing
     * parameters. Options are validated and applied immediately, affecting
     * all subsequent operations until changed or reset.
     *
     * **Common Options**:
     * - `device`: Output format (PDF, PNG, JPEG, etc.)
     * - `resolution`: Output resolution in DPI
     * - `quality`: Compression quality (0.0-1.0)
     * - `compatibility`: PDF version compatibility
     * - `compress_pages`: Enable page compression
     * - `embed_all_fonts`: Font embedding behavior
     *
     * @param string $key   Option name (case-sensitive)
     * @param mixed  $value Option value (type depends on option)
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * $processor->setOption('resolution', 600)
     *           ->setOption('quality', 0.95)
     *           ->setOption('device', Processor::DEVICE_PNG);
     * ```
     */
    public function setOption(string $key, $value): self
    {
        $this->options[$key] = $value;
        return $this;
    }

    /**
     * Set multiple processing options with batch validation
     *
     * This method efficiently applies multiple configuration changes in a
     * single operation. Options are merged with existing configuration,
     * with new values taking precedence over existing ones.
     *
     * The method preserves existing options that are not specified in the
     * input array, making it safe for partial configuration updates.
     *
     * @param array $options Associative array of option key-value pairs
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * $processor->setOptions([
     *     'resolution' => 300,
     *     'quality' => 0.8,
     *     'compress_pages' => true,
     *     'device' => Processor::DEVICE_PDF
     * ]);
     * ```
     */
    public function setOptions(array $options): self
    {
        $this->options = array_merge($this->options, $options);
        return $this;
    }

    /**
     * Get a specific option value with optional default fallback
     *
     * This method retrieves the current value of a specific option,
     * providing a safe way to access configuration values with fallback
     * support for undefined options.
     *
     * The method is commonly used internally by command builders and
     * externally for configuration inspection and debugging.
     *
     * @param string $key     Option name to retrieve
     * @param mixed  $default Default value if option is not set
     *
     * @return mixed Current option value or default if not set
     *
     * @example
     * ```php
     * $resolution = $processor->getOption('resolution', 300);
     * $quality = $processor->getOption('quality', 0.9);
     * $device = $processor->getOption('device', Processor::DEVICE_PDF);
     * ```
     */
    public function getOption(string $key, $default = null)
    {
        return $this->options[$key] ?? $default;
    }

    /**
     * Get the complete current options configuration
     *
     * Returns a copy of the entire current configuration array, useful for:
     * - Configuration inspection and debugging
     * - Creating configuration backups
     * - Implementing custom processing logic
     * - Generating configuration reports
     *
     * The returned array is a copy, so modifications won't affect the
     * processor's internal state.
     *
     * @return array<string, mixed> Complete current configuration
     *
     * @example
     * ```php
     * $config = $processor->getOptions();
     * echo "Current resolution: " . $config['resolution'] . " DPI\n";
     * echo "Quality setting: " . ($config['quality'] * 100) . "%\n";
     * ```
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * Get the default options template
     *
     * Returns a copy of the default configuration template that was
     * established during initialization. This is useful for:
     * - Understanding baseline configuration
     * - Implementing custom reset logic
     * - Comparing current vs. default settings
     * - Documentation and debugging purposes
     *
     * @return array<string, mixed> Default configuration template
     *
     * @example
     * ```php
     * $defaults = $processor->getDefaultOptions();
     * $current = $processor->getOptions();
     * $changed = array_diff_assoc($current, $defaults);
     * echo "Modified options: " . implode(', ', array_keys($changed));
     * ```
     */
    public function getDefaultOptions(): array
    {
        return $this->defaultOptions;
    }

    /**
     * Reset all options to their default values
     *
     * This method restores the processor to its initial configuration state,
     * discarding all custom options and quality preset applications. It's
     * useful for:
     * - Cleaning up after complex operations
     * - Preparing for different processing tasks
     * - Debugging configuration issues
     * - Implementing configuration rollback
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * // After complex configuration changes
     * $processor->resetOptions()
     *           ->applyQualityPreset(Processor::QUALITY_PRINTER);
     * ```
     */
    public function resetOptions(): self
    {
        $this->options = $this->defaultOptions;
        return $this;
    }

    /**
     * Reset a specific option to its default value
     *
     * This method provides granular control over option management,
     * allowing individual options to be restored to their default
     * values without affecting other customizations.
     *
     * If the specified option doesn't exist in the defaults, the
     * method silently does nothing, making it safe to use with
     * custom or dynamic options.
     *
     * @param string $key Option name to reset to default
     *
     * @return self Fluent interface for method chaining
     *
     * @example
     * ```php
     * // Reset just the resolution while keeping other changes
     * $processor->resetOption('resolution')
     *           ->resetOption('quality');
     * ```
     */
    public function resetOption(string $key): self
    {
        if (isset($this->defaultOptions[$key])) {
            $this->options[$key] = $this->defaultOptions[$key];
        }
        return $this;
    }

    /**
     * Apply a predefined quality preset for common use cases
     *
     * This method provides convenient access to optimized configuration
     * sets for common document processing scenarios. Each preset is
     * carefully tuned for its intended use case:
     *
     * **Available Presets**:
     * - `QUALITY_SCREEN`: Web/screen viewing (72 DPI, high compression)
     * - `QUALITY_EBOOK`: Digital reading (150 DPI, balanced quality)
     * - `QUALITY_PRINTER`: Office printing (300 DPI, good quality)
     * - `QUALITY_PREPRESS`: Commercial printing (600+ DPI, maximum quality)
     *
     * Presets override multiple related options simultaneously, providing
     * a cohesive configuration optimized for the target use case.
     *
     * @param string $preset Quality preset constant (use class constants)
     *
     * @return self Fluent interface for method chaining
     * @throws InvalidArgumentException When an unknown preset is specified
     *
     * @example
     * ```php
     * // Configure for web delivery
     * $processor->applyQualityPreset(Processor::QUALITY_SCREEN);
     *
     * // Configure for high-quality printing
     * $processor->applyQualityPreset(Processor::QUALITY_PREPRESS);
     * ```
     */
    public function applyQualityPreset(string $preset): self
    {
        $presets = [
            self::QUALITY_SCREEN => [
                'resolution' => 72,
                'color_image_resolution' => 72,
                'grayscale_image_resolution' => 72,
                'compress_pages' => true,
                'optimize' => true,
                'color_image_quality' => 0.4,
            ],
            self::QUALITY_EBOOK => [
                'resolution' => 150,
                'color_image_resolution' => 150,
                'grayscale_image_resolution' => 150,
                'compress_pages' => true,
                'optimize' => true,
                'color_image_quality' => 0.6,
            ],
            self::QUALITY_PRINTER => [
                'resolution' => 300,
                'color_image_resolution' => 300,
                'grayscale_image_resolution' => 300,
                'compress_pages' => true,
                'optimize' => true,
                'color_image_quality' => 0.8,
            ],
            self::QUALITY_PREPRESS => [
                'resolution' => 600,
                'color_image_resolution' => 600,
                'grayscale_image_resolution' => 600,
                'mono_image_resolution' => 1200,
                'compress_pages' => false,
                'optimize' => false,
                'color_image_quality' => 1.0,
            ],
        ];

        if (!isset($presets[$preset])) {
            throw new InvalidArgumentException("Unknown quality preset: $preset");
        }

        return $this->setOptions($presets[$preset]);
    }

    // ========== COMMAND BUILDING ==========

    /**
     * Build the complete GhostScript command
     */
    protected function buildCommand(): string
    {
        $cmd = $this->executable;

        // Add core flags
        $cmd .= $this->buildCoreFlags();

        // Add device
        $cmd .= $this->buildDeviceParameters();

        // Add resolution parameters
        $cmd .= $this->buildResolutionParameters();

        // Add compression parameters
        $cmd .= $this->buildCompressionParameters();

        // Add PDF specific parameters
        $cmd .= $this->buildPdfParameters();

        // Add image quality parameters
        $cmd .= $this->buildImageQualityParameters();

        // Add security parameters
        $cmd .= $this->buildSecurityParameters();

        // Add advanced parameters
        $cmd .= $this->buildAdvancedParameters();

        // Add custom parameters
        $cmd .= $this->buildCustomParameters();

        // Add output file
        $cmd .= ' -sOutputFile=' . escapeshellarg($this->outputFile);

        // Add input files
        foreach ($this->inputFiles as $file) {
            $cmd .= ' ' . escapeshellarg($file);
        }

        return $cmd;
    }

    /**
     * Build core flags
     */
    protected function buildCoreFlags(): string
    {
        $flags = '';

        if ($this->getOption('quiet', true)) {
            $flags .= ' -q';
        }

        if ($this->getOption('nopause', true)) {
            $flags .= ' -dNOPAUSE';
        }

        if ($this->getOption('batch', true)) {
            $flags .= ' -dBATCH';
        }

        if ($this->getOption('safer', true)) {
            $flags .= ' -dSAFER';
        }

        return $flags;
    }

    /**
     * Build device parameters
     */
    protected function buildDeviceParameters(): string
    {
        $device = $this->getOption('device', self::DEVICE_PDF);
        return ' -sDEVICE=' . $device;
    }

    /**
     * Build resolution parameters
     */
    protected function buildResolutionParameters(): string
    {
        $params = '';

        if ($resolution = $this->getOption('resolution')) {
            $params .= ' -r' . $resolution;
        }

        if ($colorRes = $this->getOption('color_image_resolution')) {
            $params .= ' -dColorImageResolution=' . $colorRes;
        }

        if ($grayRes = $this->getOption('grayscale_image_resolution')) {
            $params .= ' -dGrayImageResolution=' . $grayRes;
        }

        if ($monoRes = $this->getOption('mono_image_resolution')) {
            $params .= ' -dMonoImageResolution=' . $monoRes;
        }

        return $params;
    }

    /**
     * Build compression parameters
     */
    protected function buildCompressionParameters(): string
    {
        $params = '';

        if ($this->getOption('compress_pages', true)) {
            $params .= ' -dCompressPages=true';
        }

        if ($this->getOption('compress_streams', true)) {
            $params .= ' -dCompressStreams=true';
        }

        if ($this->getOption('use_flate_compression', true)) {
            $params .= ' -dUseFlateCompression=true';
        }

        if ($this->getOption('color_image_downsample', true)) {
            $params .= ' -dDownsampleColorImages=true';
        }

        if ($this->getOption('grayscale_image_downsample', true)) {
            $params .= ' -dDownsampleGrayImages=true';
        }

        if ($this->getOption('mono_image_downsample', false)) {
            $params .= ' -dDownsampleMonoImages=true';
        }

        return $params;
    }

    /**
     * Build PDF specific parameters
     */
    protected function buildPdfParameters(): string
    {
        $params = '';

        if ($compatibility = $this->getOption('compatibility')) {
            $params .= ' -dCompatibilityLevel=' . $compatibility;
        }

        if ($this->getOption('optimize', true)) {
            $params .= ' -dOptimize=true';
        }

        if ($autoRotate = $this->getOption('auto_rotate_pages')) {
            $params .= ' -dAutoRotatePages=' . $autoRotate;
        }

        if ($this->getOption('detect_duplication', true)) {
            $params .= ' -dDetectDuplicateImages=true';
        }

        if ($this->getOption('embed_all_fonts', true)) {
            $params .= ' -dEmbedAllFonts=true';
        }

        return $params;
    }

    /**
     * Build image quality parameters
     */
    protected function buildImageQualityParameters(): string
    {
        $params = '';

        if ($colorQuality = $this->getOption('color_image_quality')) {
            $params .= ' -dColorImageQuality=' . $colorQuality;
        }

        if ($grayQuality = $this->getOption('grayscale_image_quality')) {
            $params .= ' -dGrayImageQuality=' . $grayQuality;
        }

        return $params;
    }

    /**
     * Build security parameters
     */
    protected function buildSecurityParameters(): string
    {
        $params = '';

        if (!$this->getOption('allow_printing', true)) {
            $params .= ' -dAllowPrinting=false';
        }

        if (!$this->getOption('allow_copying', true)) {
            $params .= ' -dAllowCopying=false';
        }

        if (!$this->getOption('allow_annotation', true)) {
            $params .= ' -dAllowAnnotation=false';
        }

        if (!$this->getOption('allow_form_filling', true)) {
            $params .= ' -dAllowFormFilling=false';
        }

        return $params;
    }

    /**
     * Build advanced parameters
     */
    protected function buildAdvancedParameters(): string
    {
        $params = '';

        if ($this->getOption('preserve_copy_page', true)) {
            $params .= ' -dPreserveCopyPage=true';
        }

        if ($this->getOption('preserve_eps_info', true)) {
            $params .= ' -dPreserveEPSInfo=true';
        }

        if ($this->getOption('preserve_halftone_info', false)) {
            $params .= ' -dPreserveHalftoneInfo=true';
        }

        if ($this->getOption('preserve_opi_comments', false)) {
            $params .= ' -dPreserveOPIComments=true';
        }

        if ($this->getOption('preserve_overprint_settings', false)) {
            $params .= ' -dPreserveOverprintSettings=true';
        }

        if ($transferInfo = $this->getOption('transfer_function_info')) {
            $params .= ' -dTransferFunctionInfo=' . $transferInfo;
        }

        if ($ucrBgInfo = $this->getOption('ucr_and_bg_info')) {
            $params .= ' -dUCRandBGInfo=' . $ucrBgInfo;
        }

        return $params;
    }

    /**
     * Build custom parameters
     */
    protected function buildCustomParameters(): string
    {
        $customParams = $this->getOption('custom_parameters', []);

        if (empty($customParams)) {
            return '';
        }

        $params = '';
        foreach ($customParams as $param) {
            $params .= ' ' . $param;
        }

        return $params;
    }

    // ========== EXECUTION ==========

    /**
     * Execute the GhostScript command
     */
    public function execute(): bool
    {
        if (empty($this->inputFiles)) {
            $this->addError('No input files specified');
            return false;
        }

        // Prepare debug information
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $command = $this->buildCommand();

        // Initialize debug entry
        $debugEntry = [
            'timestamp' => date('Y-m-d H:i:s.u'),
            'operation_type' => $this->getOption('device', 'unknown'),
            'command' => $command,
            'command_length' => strlen($command),
            'input_files' => $this->inputFiles,
            'input_files_count' => count($this->inputFiles),
            'output_file' => $this->outputFile,
            'options_used' => $this->options,
            'start_time' => $startTime,
            'start_memory' => $startMemory,
            'process_id' => getmypid(),
            'php_version' => PHP_VERSION,
            'os' => PHP_OS,
        ];

        // Validate input files and collect file information
        $inputFilesInfo = [];
        foreach ($this->inputFiles as $file) {
            $fileInfo = [
                'path' => $file,
                'exists' => file_exists($file),
                'size' => file_exists($file) ? filesize($file) : 0,
                'readable' => is_readable($file),
                'modified' => file_exists($file) ? filemtime($file) : null,
            ];
            $inputFilesInfo[] = $fileInfo;

            if (!$fileInfo['exists']) {
                $this->addError("Input file does not exist: $file");
            }
            if (!$fileInfo['readable']) {
                $this->addError("Input file is not readable: $file");
            }
        }
        $debugEntry['input_files_info'] = $inputFilesInfo;

        // Check output directory
        $outputDir = dirname($this->outputFile);
        $debugEntry['output_directory'] = [
            'path' => $outputDir,
            'exists' => is_dir($outputDir),
            'writable' => is_writable($outputDir),
            'free_space' => disk_free_space($outputDir),
        ];

        // Store debug messages instead of echoing
        $debugMessages = [];
        if ($this->debugMode) {
            $debugMessages[] = "=== GhostScript Execution Debug ===";
            $debugMessages[] = "Timestamp: " . $debugEntry['timestamp'];
            $debugMessages[] = "Operation: " . $debugEntry['operation_type'];
            $debugMessages[] = "Input files: " . count($this->inputFiles);
            $debugMessages[] = "Command: $command";
            $debugMessages[] = "Command length: " . strlen($command) . " characters";
            $debugMessages[] = "Output directory writable: " . ($debugEntry['output_directory']['writable'] ? 'Yes' : 'No');
            $debugMessages[] = "Free disk space: " . number_format(
                $debugEntry['output_directory']['free_space'] / 1024 / 1024,
                2
            ) . " MB";
            $debugMessages[] = "Starting execution...";
        }

        // Execute command
        $execStartTime = microtime(true);
        exec($command . ' 2>&1', $output, $returnCode);
        $execEndTime = microtime(true);
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);

        // Complete debug information
        $debugEntry['exec_start_time'] = $execStartTime;
        $debugEntry['exec_end_time'] = $execEndTime;
        $debugEntry['exec_duration'] = $execEndTime - $execStartTime;
        $debugEntry['end_time'] = $endTime;
        $debugEntry['total_duration'] = $endTime - $startTime;
        $debugEntry['end_memory'] = $endMemory;
        $debugEntry['peak_memory'] = $peakMemory;
        $debugEntry['memory_used'] = $endMemory - $startMemory;
        $debugEntry['return_code'] = $returnCode;
        $debugEntry['output_lines'] = $output;
        $debugEntry['output_line_count'] = count($output);
        $debugEntry['success'] = $returnCode === 0;

        // Check output file
        if (file_exists($this->outputFile)) {
            $outputFileInfo = [
                'exists' => true,
                'size' => filesize($this->outputFile),
                'created_time' => filectime($this->outputFile),
                'modified_time' => filemtime($this->outputFile),
                'readable' => is_readable($this->outputFile),
                'mime_type' => function_exists('mime_content_type') ? mime_content_type($this->outputFile) : 'unknown',
            ];
            $debugEntry['output_file_info'] = $outputFileInfo;
        } else {
            $debugEntry['output_file_info'] = ['exists' => false];
        }

        // Analyze command output for warnings and errors
        $warnings = [];
        $errors = [];
        $info = [];

        foreach ($output as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }

            if (stripos($line, 'error') !== false || stripos($line, 'failed') !== false) {
                $errors[] = $line;
            } elseif (stripos($line, 'warning') !== false || stripos($line, 'warn') !== false) {
                $warnings[] = $line;
            } elseif (stripos($line, 'info') !== false || stripos($line, 'processing') !== false) {
                $info[] = $line;
            }
        }

        $debugEntry['parsed_output'] = [
            'errors' => $errors,
            'warnings' => $warnings,
            'info' => $info,
            'error_count' => count($errors),
            'warning_count' => count($warnings),
            'info_count' => count($info),
        ];

        // Performance metrics
        $debugEntry['performance'] = [
            'files_per_second' => count($this->inputFiles) / max($debugEntry['total_duration'], 0.001),
            'mb_per_second' => array_sum(array_column($inputFilesInfo, 'size')) / 1024 / 1024 / max(
                $debugEntry['total_duration'],
                0.001
            ),
            'memory_efficiency' => $debugEntry['memory_used'] / max(
                array_sum(array_column($inputFilesInfo, 'size')),
                1
            ),
            'command_complexity' => strlen($command) / 100, // Rough complexity metric
        ];

        // Add debug messages to the debug entry
        if ($this->debugMode) {
            $debugMessages[] = "Execution completed!";
            $debugMessages[] = "Return code: $returnCode";
            $debugMessages[] = "Duration: " . number_format($debugEntry['total_duration'], 4) . " seconds";
            $debugMessages[] = "Memory used: " . number_format($debugEntry['memory_used'] / 1024 / 1024, 2) . " MB";
            $debugMessages[] = "Peak memory: " . number_format($peakMemory / 1024 / 1024, 2) . " MB";
            $debugMessages[] = "Output lines: " . count($output);

            if (!empty($warnings)) {
                $debugMessages[] = "Warnings (" . count($warnings) . "):";
                foreach ($warnings as $warning) {
                    $debugMessages[] = "  - $warning";
                }
            }

            if (!empty($errors)) {
                $debugMessages[] = "Errors (" . count($errors) . "):";
                foreach ($errors as $error) {
                    $debugMessages[] = "  - $error";
                }
            }

            if (isset($outputFileInfo)) {
                $debugMessages[] = "Output file: " . $this->outputFile;
                $debugMessages[] = "Output size: " . number_format($outputFileInfo['size'] / 1024, 2) . " KB";
                $debugMessages[] = "Processing rate: " . number_format(
                    $debugEntry['performance']['files_per_second'],
                    2
                ) . " files/sec";
                $debugMessages[] = "Throughput: " . number_format(
                    $debugEntry['performance']['mb_per_second'],
                    2
                ) . " MB/sec";
            }

            $debugMessages[] = "=== End Debug ===";
        }

        // Add debug messages to the debug entry
        $debugEntry['debug_messages'] = $debugMessages;

        // Store debug information
        $this->debugInfo = $debugEntry;
        $this->executionHistory[] = $debugEntry;

        // Handle execution results
        if ($returnCode !== 0) {
            $errorMsg = 'GhostScript execution failed (code: ' . $returnCode . ')';
            if (!empty($errors)) {
                $errorMsg .= ': ' . implode('; ', array_slice($errors, 0, 3));
            } elseif (!empty($output)) {
                $errorMsg .= ': ' . implode('; ', array_slice($output, -3));
            }
            $this->addError($errorMsg);
            return false;
        }

        if (!file_exists($this->outputFile)) {
            $this->addError('Output file was not created: ' . $this->outputFile);
            return false;
        }

        return true;
    }

    /**
     * Get the built command without executing
     */
    public function getCommand(): string
    {
        return $this->buildCommand();
    }

    // ========== OPERATIONS ==========

    /**
     * Set up for PDF merging operation
     */
    public function setupPdfMerging(): self
    {
        $this->setOption('device', self::DEVICE_PDF);
        return $this;
    }

    /**
     * Set up for image conversion
     */
    public function setupImageConversion(string $format = self::DEVICE_PNG): self
    {
        $this->setOption('device', $format);
        return $this;
    }

    /**
     * Set up for PostScript conversion
     */
    public function setupPostScriptConversion(): self
    {
        $this->setOption('device', self::DEVICE_PS);
        return $this;
    }

    /**
     * Set up for EPS conversion
     */
    public function setupEpsConversion(): self
    {
        $this->setOption('device', self::DEVICE_EPS);
        return $this;
    }

    /**
     * Set up for page extraction
     */
    public function setupPageExtraction(int $firstPage, int $lastPage = null): self
    {
        $this->setOption('device', self::DEVICE_PDF);

        $customParams = $this->getOption('custom_parameters', []);
        $customParams[] = '-dFirstPage=' . $firstPage;

        if ($lastPage !== null) {
            $customParams[] = '-dLastPage=' . $lastPage;
        }

        $this->setOption('custom_parameters', $customParams);

        return $this;
    }

    /**
     * Set up for web optimization
     */
    public function setupWebOptimization(): self
    {
        return $this->applyQualityPreset(self::QUALITY_EBOOK)->setupPdfMerging();
    }

    /**
     * Set up for print optimization
     */
    public function setupPrintOptimization(): self
    {
        return $this->applyQualityPreset(self::QUALITY_PRINTER)->setupPdfMerging();
    }

    /**
     * Validate PDF files using GhostScript
     */
    public function validatePdf(string $pdfFile): array
    {
        // Prepare debug information for validation
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        if (!file_exists($pdfFile)) {
            $validation = [
                'valid' => false,
                'error' => 'File does not exist',
                'details' => [],
            ];

            // Store debug info even for file not found
            $this->debugInfo = [
                'timestamp' => date('Y-m-d H:i:s.u'),
                'operation_type' => 'pdf_validation',
                'command' => 'N/A - File not found',
                'input_files' => [$pdfFile],
                'input_files_count' => 1,
                'validation_result' => $validation,
                'start_time' => $startTime,
                'end_time' => microtime(true),
                'total_duration' => microtime(true) - $startTime,
                'start_memory' => $startMemory,
                'end_memory' => memory_get_usage(true),
                'memory_used' => memory_get_usage(true) - $startMemory,
                'success' => false,
                'error' => 'File does not exist: ' . $pdfFile,
            ];

            return $validation;
        }

        // Use GhostScript to validate PDF
        $command = $this->executable . ' -q -dNOPAUSE -dBATCH -dSAFER -sDEVICE=nullpage ' . escapeshellarg(
            $pdfFile
        ) . ' 2>&1';

        $execStartTime = microtime(true);
        exec($command, $output, $returnCode);
        $execEndTime = microtime(true);
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        $validation = [
            'valid' => $returnCode === 0,
            'return_code' => $returnCode,
            'file_size' => filesize($pdfFile),
            'file_path' => $pdfFile,
            'validation_output' => $output,
            'details' => [],
        ];

        // Parse validation output for specific issues
        $errors = [];
        $warnings = [];
        $info = [];

        foreach ($output as $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }

            if (stripos($line, 'error') !== false || stripos($line, 'failed') !== false) {
                $errors[] = $line;
            } elseif (stripos($line, 'warning') !== false || stripos($line, 'warn') !== false) {
                $warnings[] = $line;
            } elseif (stripos($line, 'damaged') !== false || stripos($line, 'corrupt') !== false) {
                $errors[] = $line;
                $validation['valid'] = false;
            } elseif (stripos($line, 'page') !== false || stripos($line, 'processing') !== false) {
                $info[] = $line;
            }
        }

        $validation['details'] = [
            'errors' => $errors,
            'warnings' => $warnings,
            'info' => $info,
            'error_count' => count($errors),
            'warning_count' => count($warnings),
        ];

        // Additional file-based validation
        $validation['file_info'] = [
            'readable' => is_readable($pdfFile),
            'size_mb' => round(filesize($pdfFile) / 1024 / 1024, 2),
            'mime_type' => function_exists('mime_content_type') ? mime_content_type($pdfFile) : 'unknown',
            'modified_time' => filemtime($pdfFile),
        ];

        // Store comprehensive debug information for validation
        $this->debugInfo = [
            'timestamp' => date('Y-m-d H:i:s.u'),
            'operation_type' => 'pdf_validation',
            'command' => $command,
            'command_length' => strlen($command),
            'input_files' => [$pdfFile],
            'input_files_count' => 1,
            'input_files_info' => [
                [
                    'path' => $pdfFile,
                    'exists' => true,
                    'size' => filesize($pdfFile),
                    'readable' => is_readable($pdfFile),
                    'modified' => filemtime($pdfFile),
                ],
            ],
            'validation_result' => $validation,
            'start_time' => $startTime,
            'exec_start_time' => $execStartTime,
            'exec_end_time' => $execEndTime,
            'exec_duration' => $execEndTime - $execStartTime,
            'end_time' => $endTime,
            'total_duration' => $endTime - $startTime,
            'start_memory' => $startMemory,
            'end_memory' => $endMemory,
            'peak_memory' => memory_get_peak_usage(true),
            'memory_used' => $endMemory - $startMemory,
            'return_code' => $returnCode,
            'output_lines' => $output,
            'output_line_count' => count($output),
            'success' => $returnCode === 0,
            'parsed_output' => [
                'errors' => $errors,
                'warnings' => $warnings,
                'info' => $info,
                'error_count' => count($errors),
                'warning_count' => count($warnings),
                'info_count' => count($info),
            ],
            'performance' => [
                'validation_rate' => 1 / max($endTime - $startTime, 0.001),
                'mb_per_second' => (filesize($pdfFile) / 1024 / 1024) / max($endTime - $startTime, 0.001),
                'memory_efficiency' => ($endMemory - $startMemory) / max(filesize($pdfFile), 1),
            ],
            'process_id' => getmypid(),
            'php_version' => PHP_VERSION,
            'os' => PHP_OS,
        ];

        // Store debug messages in the debug info
        $validationDebugMessages = [];
        if ($this->debugMode) {
            $validationDebugMessages[] = "=== PDF Validation Debug ===";
            $validationDebugMessages[] = "File: $pdfFile";
            $validationDebugMessages[] = "Valid: " . ($validation['valid'] ? 'Yes' : 'No');
            $validationDebugMessages[] = "Return code: $returnCode";
            $validationDebugMessages[] = "Duration: " . number_format(
                $this->debugInfo['total_duration'],
                4
            ) . " seconds";
            $validationDebugMessages[] = "File size: " . number_format($validation['file_info']['size_mb'], 2) . " MB";
            $validationDebugMessages[] = "Errors: " . count($errors);
            $validationDebugMessages[] = "Warnings: " . count($warnings);
            $validationDebugMessages[] = "=== End Validation Debug ===";
        }

        // Add debug messages to the debug info
        $this->debugInfo['debug_messages'] = $validationDebugMessages;

        // Add to execution history
        $this->executionHistory[] = $this->debugInfo;

        return $validation;
    }

    /**
     * Validate multiple PDF files
     */
    public function validatePdfFiles(array $pdfFiles): array
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        $results = [];
        $summary = [
            'total_files' => count($pdfFiles),
            'valid_files' => 0,
            'invalid_files' => 0,
            'total_size_mb' => 0,
            'files_with_warnings' => 0,
        ];

        foreach ($pdfFiles as $file) {
            $validation = $this->validatePdf($file);
            $results[] = $validation;

            if ($validation['valid']) {
                $summary['valid_files']++;
            } else {
                $summary['invalid_files']++;
            }

            if ($validation['details']['warning_count'] > 0) {
                $summary['files_with_warnings']++;
            }

            $summary['total_size_mb'] += $validation['file_info']['size_mb'];
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        // Store debug information for batch validation
        $batchDebugInfo = [
            'timestamp' => date('Y-m-d H:i:s.u'),
            'operation_type' => 'batch_pdf_validation',
            'input_files' => $pdfFiles,
            'input_files_count' => count($pdfFiles),
            'start_time' => $startTime,
            'end_time' => $endTime,
            'total_duration' => $endTime - $startTime,
            'start_memory' => $startMemory,
            'end_memory' => $endMemory,
            'memory_used' => $endMemory - $startMemory,
            'peak_memory' => memory_get_peak_usage(true),
            'batch_summary' => $summary,
            'individual_results' => $results,
            'success' => $summary['invalid_files'] === 0,
            'performance' => [
                'files_per_second' => count($pdfFiles) / max($endTime - $startTime, 0.001),
                'mb_per_second' => $summary['total_size_mb'] / max($endTime - $startTime, 0.001),
                'average_file_size_mb' => $summary['total_size_mb'] / max(count($pdfFiles), 1),
            ],
            'process_id' => getmypid(),
            'php_version' => PHP_VERSION,
            'os' => PHP_OS,
        ];

        // Store debug messages for batch validation
        $batchDebugMessages = [];
        if ($this->debugMode) {
            $batchDebugMessages[] = "=== Batch PDF Validation Debug ===";
            $batchDebugMessages[] = "Total files: " . count($pdfFiles);
            $batchDebugMessages[] = "Valid files: " . $summary['valid_files'];
            $batchDebugMessages[] = "Invalid files: " . $summary['invalid_files'];
            $batchDebugMessages[] = "Files with warnings: " . $summary['files_with_warnings'];
            $batchDebugMessages[] = "Total size: " . number_format($summary['total_size_mb'], 2) . " MB";
            $batchDebugMessages[] = "Duration: " . number_format($batchDebugInfo['total_duration'], 4) . " seconds";
            $batchDebugMessages[] = "Processing rate: " . number_format(
                $batchDebugInfo['performance']['files_per_second'],
                2
            ) . " files/sec";
            $batchDebugMessages[] = "=== End Batch Validation Debug ===";
        }

        // Add debug messages to batch debug info
        $batchDebugInfo['debug_messages'] = $batchDebugMessages;

        // Update main debug info with batch information
        $this->debugInfo = $batchDebugInfo;
        $this->executionHistory[] = $batchDebugInfo;

        return [
            'summary' => $summary,
            'results' => $results,
        ];
    }

    // ========== DEBUG INFORMATION ACCESS ==========

    /**
     * Get the last execution debug information
     */
    public function getLastDebugInfo(): array
    {
        return $this->debugInfo;
    }

    /**
     * Get execution history
     */
    public function getExecutionHistory(): array
    {
        return $this->executionHistory;
    }

    /**
     * Get execution statistics
     */
    public function getExecutionStats(): array
    {
        if (empty($this->executionHistory)) {
            return [
                'total_executions' => 0,
                'successful_executions' => 0,
                'failed_executions' => 0,
                'success_rate' => 0,
                'total_duration' => 0,
                'average_duration' => 0,
                'total_memory_used' => 0,
                'average_memory_used' => 0,
                'total_files_processed' => 0,
                'average_throughput' => 0,
            ];
        }

        $successful = array_filter($this->executionHistory, fn($entry) => $entry['success']);
        $totalDuration = array_sum(array_column($this->executionHistory, 'total_duration'));
        $totalMemory = array_sum(array_column($this->executionHistory, 'memory_used'));
        $totalFiles = array_sum(array_column($this->executionHistory, 'input_files_count'));

        return [
            'total_executions' => count($this->executionHistory),
            'successful_executions' => count($successful),
            'failed_executions' => count($this->executionHistory) - count($successful),
            'success_rate' => (count($successful) / count($this->executionHistory)) * 100,
            'total_duration' => $totalDuration,
            'average_duration' => $totalDuration / count($this->executionHistory),
            'total_memory_used' => $totalMemory,
            'average_memory_used' => $totalMemory / count($this->executionHistory),
            'total_files_processed' => $totalFiles,
            'average_throughput' => $totalFiles / max($totalDuration, 0.001),
        ];
    }

    /**
     * Get performance metrics for the last execution
     */
    public function getLastPerformanceMetrics(): array
    {
        if (empty($this->debugInfo)) {
            return [];
        }

        return $this->debugInfo['performance'] ?? [];
    }

    /**
     * Get parsed output from last execution
     */
    public function getLastParsedOutput(): array
    {
        if (empty($this->debugInfo)) {
            return ['errors' => [], 'warnings' => [], 'info' => []];
        }

        return $this->debugInfo['parsed_output'] ?? ['errors' => [], 'warnings' => [], 'info' => []];
    }

    /**
     * Get last execution duration
     */
    public function getLastExecutionDuration(): ?float
    {
        return $this->debugInfo['total_duration'] ?? null;
    }

    /**
     * Get last execution memory usage
     */
    public function getLastMemoryUsage(): ?int
    {
        return $this->debugInfo['memory_used'] ?? null;
    }

    /**
     * Get last command executed
     */
    public function getLastCommand(): ?string
    {
        return $this->debugInfo['command'] ?? null;
    }

    /**
     * Check if last execution was successful
     */
    public function wasLastExecutionSuccessful(): bool
    {
        return $this->debugInfo['success'] ?? false;
    }

    /**
     * Get debug messages from last execution
     */
    public function getLastDebugMessages(): array
    {
        return $this->debugInfo['debug_messages'] ?? [];
    }

    /**
     * Get all debug messages as a formatted string
     */
    public function getLastDebugMessagesAsString(): string
    {
        $messages = $this->getLastDebugMessages();
        return implode("\n", $messages);
    }

    /**
     * Clear execution history
     */
    public function clearExecutionHistory(): self
    {
        $this->executionHistory = [];
        return $this;
    }

    /**
     * Clear current debug info
     */
    public function clearDebugInfo(): self
    {
        $this->debugInfo = [];
        return $this;
    }

    /**
     * Export debug information to array
     */
    public function exportDebugInfo(): array
    {
        return [
            'last_execution' => $this->debugInfo,
            'execution_history' => $this->executionHistory,
            'execution_stats' => $this->getExecutionStats(),
            'exported_at' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION,
            'os' => PHP_OS,
            'ghostscript_version' => $this->getVersion(),
        ];
    }

    // ========== ERROR HANDLING ==========

    /**
     * Add an error message
     */
    protected function addError(string $error): void
    {
        $this->errors[] = $error;
    }

    /**
     * Get all errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get the last error
     */
    public function getLastError(): ?string
    {
        return end($this->errors) ?: null;
    }

    /**
     * Clear all errors
     */
    public function clearErrors(): self
    {
        $this->errors = [];
        return $this;
    }

    /**
     * Check if there are any errors
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    // ========== DEBUG AND UTILITY ==========

    /**
     * Enable debug mode
     */
    public function enableDebug(): self
    {
        $this->debugMode = true;
        return $this;
    }

    /**
     * Disable debug mode
     */
    public function disableDebug(): self
    {
        $this->debugMode = false;
        return $this;
    }

    /**
     * Check if debug mode is enabled
     */
    public function isDebugEnabled(): bool
    {
        return $this->debugMode;
    }

    /**
     * Get GhostScript version
     */
    public function getVersion(): string
    {
        exec(escapeshellcmd($this->executable) . ' --version 2>&1', $output, $code);
        return $code === 0 ? trim(implode(' ', $output)) : 'Unknown';
    }

    /**
     * Get supported devices
     */
    public function getSupportedDevices(): array
    {
        exec(escapeshellcmd($this->executable) . ' -h 2>&1', $output, $code);

        $devices = [];
        $inDeviceSection = false;

        foreach ($output as $line) {
            if (strpos($line, 'Available devices:') !== false) {
                $inDeviceSection = true;
                continue;
            }

            if ($inDeviceSection) {
                if (trim($line) === '' || strpos($line, 'Search path:') !== false) {
                    break;
                }

                preg_match_all('/\b\w+\b/', $line, $matches);
                if (!empty($matches[0])) {
                    $devices = array_merge($devices, $matches[0]);
                }
            }
        }

        return array_unique($devices);
    }

    // ========== EXTENSIBILITY ==========

    /**
     * Register a custom operation
     */
    public function registerOperation(string $name, callable $operation): self
    {
        $this->operations[$name] = $operation;
        return $this;
    }

    /**
     * Execute a custom operation
     */
    public function executeOperation(string $name, ...$args)
    {
        if (!isset($this->operations[$name])) {
            throw new InvalidArgumentException("Unknown operation: $name");
        }

        return call_user_func($this->operations[$name], $this, ...$args);
    }

    /**
     * Get registered operations
     */
    public function getRegisteredOperations(): array
    {
        return array_keys($this->operations);
    }

    /**
     * Add a custom parameter builder
     */
    public function addCustomParameterBuilder(string $name, callable $builder): self
    {
        $this->setOption("custom_builder_$name", $builder);
        return $this;
    }

    /**
     * Execute custom parameter builder
     */
    protected function executeCustomParameterBuilder(string $name): string
    {
        $builder = $this->getOption("custom_builder_$name");

        if (!$builder || !is_callable($builder)) {
            return '';
        }

        return call_user_func($builder, $this);
    }

    // ========== FLUENT INTERFACE HELPERS ==========

    /**
     * Create a new instance with the same configuration
     */
    public function clone(): self
    {
        $clone = new static();
        $clone->options = $this->options;
        $clone->debugMode = $this->debugMode;
        return $clone;
    }

    /**
     * Reset the wrapper to initial state
     */
    public function reset(): self
    {
        $this->inputFiles = [];
        $this->errors = [];
        $this->operations = [];
        $this->debugInfo = [];
        $this->executionHistory = [];
        $this->outputFile = $this->getDefaultOutputPath();
        $this->resetOptions();
        return $this;
    }

    /**
     * Validate current configuration
     */
    public function validate(): bool
    {
        if (empty($this->inputFiles)) {
            $this->addError('No input files specified');
            return false;
        }

        foreach ($this->inputFiles as $file) {
            if (!file_exists($file)) {
                $this->addError("Input file does not exist: $file");
                return false;
            }
        }

        $outputDir = dirname($this->outputFile);
        if (!is_dir($outputDir) || !is_writable($outputDir)) {
            $this->addError("Output directory is not writable: $outputDir");
            return false;
        }

        return true;
    }
}
