<?php

namespace Nzoom\Mvc\ControllerTrait;

use Router;

/**
 * @method i18n($param, $placeholders = array())
 * @method redirect($module, $action = '', $params = array(), $controller = '')
 * @property $module
 * @property $controller
 * @property $action
 * @property \Registry $registry
 * @property $defaultAction
 */
trait CheckAccessTrait
{
    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array();

    public $permissions;

    /**
     * Check access to module and action
     * If no access redirect to the default action of the module
     *
     * @param bool $redirect - defines whether to redirect after check or not
     * @param string $module - optional module
     * @param string $action - optional action
     * @param string $controller - optional controller
     * @return bool - result of the check, true - accessible, false - inaccessible
     */
    public function checkAccessModule($redirect = true, $module = '', $action = '', $controller = '') {
        //get permissions of the currently logged user
        $userPermissions = $this->getUserPermissions();

        if (!$module) {
            $module = $this->module;
        }

        if (!$controller) {
            $controller = $this->controller;
        }

        if (!$action) {
            $action = $this->action;
        }

        //check for secondary controller
        $module_check = $module;
        if ($module != $controller) {
            $module_check .= '_' . $controller;
        }

        //check if the module or action is within the free access list that does not require valid login
        if (in_array($module_check, Router::$freeAccessModules)
                || (isset(Router::$freeAccessActionsModules[$action]) && in_array($module_check, Router::$freeAccessActionsModules[$action]))
                // if this is a programmatically created automations controller and module is something else, not automations, then don't check
                || (get_class($this) == 'Automations_Controller' || is_subclass_of($this, 'Automations_Controller'))
                    && $module_check != 'automations') {
            return true;
        }

        // check if action is among permitted actions of controller (but requires valid login)
        if (in_array($action, $this->permittedActions) && $this->registry['validLogin'] && $this->registry['currentUser']) {
            return true;
        }

        //check permissions for the module and action
        if (isset($userPermissions[$module_check])) {
            if (!$redirect) {
                if (!$this->checkActionPermissions($module_check, '_access_')) {
                    return false;
                } else {
                    return true;
                }
            }

            if (!$this->checkActionPermissions($module_check, '_access_')
                    && !($module_check == 'users' && preg_match('#^(password|profile|mynzoom)$#', $action))) {
                //no access to the module
                //the only exception is the users profile and password module

                //show error message
                $this->registry['messages']->setError($this->i18n('error_no_access_to_module'));
                $this->registry['messages']->insertInSession($this->registry);

                //redirect to the index
                if ($this->action == 'dashlet' || $this->registry['request']->isRequested('dashlet_ajax_action')) {
                    die('<span class="red">' . $this->i18n('error_no_access_to_module') . "</span>");
                }
                $this->redirect('index');
            } elseif ($action && !$this->checkActionPermissions($module_check, $action)) {
                //no access to the module's action

                //show error message
                $this->registry['messages']->setError($this->i18n('error_no_access_to_module_action'));
                $this->registry['messages']->insertInSession($this->registry);

                //try to redirect to the default action of the module (defaultAction - usually list)
                if ($this->checkActionPermissions($module_check, $this->defaultAction)) {
                    //redirect to module's default action
                    $this->redirect($module, $this->defaultAction);
                } else {
                    //redirect to the index
                    if ($this->action == 'dashlet' || $this->registry['request']->isRequested('dashlet_ajax_action')) {
                        die('<span class="red">' . $this->i18n('error_no_access_to_module') . "</span>");
                    }
                    $this->redirect('index');
                }
            }
        }

        // TODO: Fix this: if this is not set "$this->permissions[$module_check]", then you have permissions ...
        return true;
    }

    /**
     * Gets the array of permissions of currently logged user
     *
     * @return array - permissions of current user
     */
    public function getUserPermissions() {
        if (isset($this->permissions)) {
            return $this->permissions;
        }

        //check permissions for the module and action
        $this->permissions = array();
        if ($this->registry['validLogin'] && $this->registry['currentUser']) {
            $this->permissions = $this->registry['currentUser']->getRights();
        }

        return $this->permissions;
    }

    /**
     * Check access of the current user to a certain action module
     *
     * @param string $module_check - module name
     * @param string $action - action name
     * @return bool - result of the check, true - accessible, false - inaccessible
     */
    public function checkActionPermissions($module_check, $action): bool {
        $userPermissions = $this->getUserPermissions();

         if (isset($userPermissions[$module_check])
                    && $action
                    && array_key_exists($action, $userPermissions[$module_check])
                    && ($userPermissions[$module_check][$action] == 'none'
                        || $userPermissions[$module_check][$action] == '')) {
            return false;
        }

        return true;
    }
}
