{"version": 1, "defects": {"Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetSupportedExtensions": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testSupportsFormat": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetMimeType": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetDefaultExtension": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatName": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatOptions": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFile": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithCustomDelimiter": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithTabDelimiter": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFilePointer": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithUnsupportedType": 4, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithInvalidFile": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithHeader": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithoutHeader": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithMetadata": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddRecord": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddMultipleRecords": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testCreateRecord": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordAtInvalidIndex": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testMetadataOperations": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIterator": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testToArray": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoading": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventDirectRecordOperations": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventCreateRecord": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetPageSize": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetHeader": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testValidate": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateCsvAdapter": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateExcelAdapter": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateJsonAdapter": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithOptions": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCaching": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithUnsupportedFormat": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormats": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormatsCaching": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testIsFormatSupported": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilename": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithoutExtension": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUnsupportedExtension": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling": 4, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testFormatTrimming": 4, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIsEmptyWithLazyLoading": 3, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidDate": 3, "Tests\\Nzoom\\Export\\DataFactoryTest::testConstructor": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testSetChunkSize": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithEmptyModels": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithOutlookFields": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithModels": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testFieldTypeMapping": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithLargeModelSet": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreaming": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreaming": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testHeaderCreationWithDefaultValues": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testModelWithoutGetExportVarTypeMethod": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testComplexScenario": 4, "Tests\\Nzoom\\Export\\DataFactoryTest::testCursorStreamingPaginationBehavior": 3, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation": 3, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructor": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testMultipleTypesAndSections": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFilterVisibilityInitialization": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFirstOrZeroMethod": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithDifferentTypeAndSection": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithSessionData": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithPlugins": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithNoPlugins": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDefaultValues": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldTypes": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldRequirements": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testComplexScenario": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldCustomIds": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testTranslationIntegration": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testEmptyTypesAndSections": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructorWithNullModelNames": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testBaseExportOptionsStructure": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDelimiterOptions": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testGroupTablesOptions": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFormatOptions": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithFiltersHide": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleSection": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleType": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsBasic": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCreateExportAction": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testInvokeMethod": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelFactoryName": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelName": 4, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCompleteWorkflow": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testConstructor": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithArray": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testComplexExportScenario": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithAjaxRequest": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithoutLogger": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithLogger": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithInvalidStream": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithValidStream": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateTempStream": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithEmptyString": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithExistingExtension": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithNull": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithString": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelName": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportWithInvalidFormat": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportSuccess": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportAction": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateGeneratorFileStreamer": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportData": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testIsFormatSupported": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetSupportedFormats": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetAdapter": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetFormatFactory": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetFormatFactory": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelFactoryName": 4, "Tests\\Nzoom\\Export\\ExportServiceTest::testLazyInitialization": 4}, "times": {"Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetSupportedExtensions": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testSupportsFormat": 0, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetMimeType": 0, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetDefaultExtension": 0.003, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatName": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testGetFormatOptions": 0, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFile": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithCustomDelimiter": 0.002, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithTabDelimiter": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportToFilePointer": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithUnsupportedType": 0.001, "Tests\\Nzoom\\Export\\Adapter\\CsvExportFormatAdapterTest::testExportWithInvalidFile": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithHeader": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithoutHeader": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testConstructorWithMetadata": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddRecord": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddMultipleRecords": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testCreateRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordAtInvalidIndex": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testMetadataOperations": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIterator": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testToArray": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventDirectRecordOperations": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testLazyLoadingPreventCreateRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetPageSize": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSetHeader": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testValidate": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateCsvAdapter": 0.002, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateExcelAdapter": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateJsonAdapter": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithOptions": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCaching": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterCachingWithDifferentOptions": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterWithUnsupportedFormat": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormats": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testGetSupportedFormatsCaching": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testIsFormatSupported": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilename": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithOptions": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithoutExtension": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCreateAdapterFromFilenameWithUnsupportedExtension": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testCaseInsensitiveFormatHandling": 0.001, "Tests\\Nzoom\\Export\\Factory\\ExportFormatFactoryTest::testFormatTrimming": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordsWithLazyLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testAddRecordWithValidation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testIsEmptyWithLazyLoading": 0, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testGetRecordAtWithLazyLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSortByColumnInvalidColumn": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testToArrayFormatted": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testFilter": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSortByColumnWithNullValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testSortByColumn": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportDataTest::testCountWithEagerLoading": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithMinimalParameters": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testAddStyleOverwritesExisting": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testAllValidTypes": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testCreateValueWithDifferentTypes": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testCreateValue": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithInvalidArray": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidArray": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithInvalidDate": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidDate": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidBoolean": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidFloat": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithInvalidInteger": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidInteger": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testValidateValueWithValidString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testAddStyle": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithAllParameters": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetStyles": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetWidthWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetWidth": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetFormat": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetTypeWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetLabelWithEmptyString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetLabel": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetVarNameWithEmptyString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testSetVarName": 0, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testConstructorWithEmptyVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportColumnTest::testComplexStyling": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testConstructorWithDefaults": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetVarNames": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testCountableImplementation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testIteratorWithEmptyHeader": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testIteratorImplementation": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testValidateRecordInvalidType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testValidateRecordInvalidCount": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testValidateRecordValid": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumnsWithInvalidVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumnsWithEmptyArray": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumnsPartialOrder": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testReorderColumns": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetTypes": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetLabels": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testConstructorWithParameters": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddStyleOverwritesExisting": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddStyle": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testSetStyles": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testSetBackgroundColor": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetColumnByVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetColumnAt": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testGetColumns": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testHasColumn": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddColumnWithDuplicateVarName": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddMultipleColumns": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testAddColumn": 0, "Tests\\Nzoom\\Export\\Entity\\ExportHeaderTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testConstructorWithDefaults": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetValueByColumnName": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testIteratorWithEmptyRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testIteratorImplementation": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testCountableImplementation": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testValidateWithInvalidRecord": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testValidateWithValidRecord": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetMetadata": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testMetadataOperations": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetFormattedValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetRawValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testHasValue": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetValueAt": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testConstructorWithMetadata": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testGetValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueByColumnNameNonExistent": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueByColumnName": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAtNegativeIndex": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAtOutOfRange": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAtWithExportValueObject": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testSetValueAt": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testAddMultipleValues": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testAddValueWithExportValueObject": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testAddValue": 0, "Tests\\Nzoom\\Export\\Entity\\ExportRecordTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testConstructorWithMinimalParameters": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateTimeWithString": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueWithNullType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateWithDateTime": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateWithCustomFormat": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateWithString": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateTimeWithDateTime": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueDateTimeWithCustomFormat": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueOtherTypes": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateArrayType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithArray": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeAndDateType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeAndDateTimeType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeAndCustomFormat": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithDateTimeNoType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testToStringWithPrimitiveTypes": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetFormattedValueWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateDateTimeType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testConstructorWithAllParameters": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetFormat": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testConstructorWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testGetValidTypes": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetValue": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetTypeWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetTypeWithInvalidType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testSetFormatWithNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateDateType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testIsNull": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateWithNullValue": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateWithNullType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateStringType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateIntegerType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateFloatType": 0.001, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testValidateBooleanType": 0, "Tests\\Nzoom\\Export\\Entity\\ExportValueTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testConstructor": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testSetChunkSize": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithEmptyModels": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithOutlookFields": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithModels": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testFieldTypeMapping": 0.002, "Tests\\Nzoom\\Export\\DataFactoryTest::testInvokeWithLargeModelSet": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreaming": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreaming": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testHeaderCreationWithDefaultValues": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testModelWithoutGetExportVarTypeMethod": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testComplexScenario": 0.003, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithDefaultPageSize": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingWithComplexFilters": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithDefaultParameters": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorField": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithEmptyFilters": 0, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsCreateProperLazyConfiguration": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsWithDifferentFieldTypes": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingMethodsPreserveFilterStructure": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateStreamingRecordProviderExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingRecordProviderExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testStreamingPaginationBehavior": 0.002, "Tests\\Nzoom\\Export\\DataFactoryTest::testCursorStreamingPaginationBehavior": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithExistingWhereConditions": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithCustomCursorFieldExecution": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingEmptyResultHandling": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingWithNoInitialWhereConditions": 0.001, "Tests\\Nzoom\\Export\\DataFactoryTest::testCreateCursorStreamingMultiplePageSimulation": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructor": 0, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testMultipleTypesAndSections": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFilterVisibilityInitialization": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFirstOrZeroMethod": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithDifferentTypeAndSection": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithSessionData": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithPlugins": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPluginOptionsWithNoPlugins": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDefaultValues": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldTypes": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldRequirements": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testComplexScenario": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFieldCustomIds": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testTranslationIntegration": 0.004, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testEmptyTypesAndSections": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testConstructorWithNullModelNames": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testBaseExportOptionsStructure": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testDelimiterOptions": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testGroupTablesOptions": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testFormatOptions": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithFiltersHide": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleSection": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsWithSingleType": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testPrepareExportOptionsBasic": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCreateExportAction": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testInvokeMethod": 0.002, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelFactoryName": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testSetModelName": 0.001, "Tests\\Nzoom\\Export\\ExportActionFactoryTest::testCompleteWorkflow": 0.007, "Tests\\Nzoom\\Export\\ExportServiceTest::testConstructor": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithArray": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testComplexExportScenario": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithAjaxRequest": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithoutLogger": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testHandleExportErrorWithLogger": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithInvalidStream": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testStreamToBrowserWithValidStream": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateTempStream": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithEmptyString": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithExistingExtension": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithNull": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetExportFilenameWithString": 0.01, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelName": 0, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportWithInvalidFormat": 0.002, "Tests\\Nzoom\\Export\\ExportServiceTest::testExportSuccess": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportAction": 0.002, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateGeneratorFileStreamer": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testCreateExportData": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testIsFormatSupported": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetSupportedFormats": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetAdapter": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetFormatFactory": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testGetFormatFactory": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testSetModelFactoryName": 0.001, "Tests\\Nzoom\\Export\\ExportServiceTest::testLazyInitialization": 0.002}}