# ChangeLog

All notable changes are documented in this file using the [Keep a CHANGELOG](https://keepachangelog.com/) principles.

## [3.0.4] - 2024-03-14

No functional changes.

## [3.0.3] - 2020-09-28

### Changed

* Changed PHP version constraint in `composer.json` from `^7.3 || ^8.0` to `>=7.3`

## [3.0.2] - 2020-06-26

### Added

* This component is now supported on PHP 8

## [3.0.1] - 2020-06-15

### Changed

* Tests etc. are now ignored for archive exports

## [3.0.0] - 2020-02-07

### Removed

* This component is no longer supported on PHP 7.1 and PHP 7.2

## [2.0.1] - 2018-10-04

### Fixed

* Functions and methods with nullable parameters of type `resource` are now also considered

## [2.0.0] - 2018-09-27

### Changed

* [FunctionSignatureMap.php](https://raw.githubusercontent.com/phan/phan/master/src/Phan/Language/Internal/FunctionSignatureMap.php) from `phan/phan` is now used instead of [arginfo.php](https://raw.githubusercontent.com/rlerdorf/phan/master/includes/arginfo.php) from `rlerdorf/phan`

### Removed

* This component is no longer supported on PHP 5.6 and PHP 7.0

## 1.0.0 - 2015-07-28

* Initial release

[3.0.4]: https://github.com/sebastianbergmann/comparator/resource-operations/3.0.3...3.0.4
[3.0.3]: https://github.com/sebastianbergmann/comparator/resource-operations/3.0.2...3.0.3
[3.0.2]: https://github.com/sebastianbergmann/comparator/resource-operations/3.0.1...3.0.2
[3.0.1]: https://github.com/sebastianbergmann/comparator/resource-operations/3.0.0...3.0.1
[3.0.0]: https://github.com/sebastianbergmann/comparator/resource-operations/2.0.1...3.0.0
[2.0.1]: https://github.com/sebastianbergmann/comparator/resource-operations/2.0.0...2.0.1
[2.0.0]: https://github.com/sebastianbergmann/comparator/resource-operations/1.0.0...2.0.0
