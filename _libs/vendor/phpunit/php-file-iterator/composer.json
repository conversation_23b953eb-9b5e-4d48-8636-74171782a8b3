{"name": "phpunit/php-file-iterator", "description": "FilterIterator implementation that filters files based on a list of suffixes.", "type": "library", "keywords": ["iterator", "filesystem"], "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues"}, "config": {"platform": {"php": "7.3.0"}, "optimize-autoloader": true, "sort-packages": true}, "prefer-stable": true, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "3.0-dev"}}}